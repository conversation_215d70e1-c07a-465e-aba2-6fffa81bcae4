#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: chrns.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_chrns_icon_data[] = {
0x5A,0xC9,0x5A,0xC9,0x5A,0xD1,0x5A,0xC9,0x59,0xC9,0x59,0xC9,0x59,0xC1,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB1,0x79,0xB1,0x79,0xB1,0x79,0xA9,0x99,0xA9,0x99,0xA1,0x99,0xA1,0x98,0x99,0x98,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0xB8,0x89,0xB8,0x89,0xB8,0x81,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0x5A,0xD1,0x5A,0xD1,0x5A,0xC9,0x5A,0xC9,0x59,0xC9,0x59,0xC1,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB9,0x79,0xB1,0x79,0xB1,0x79,0xA9,0x79,0xA9,0x79,0xA1,0x78,0xA1,
0x78,0x99,0x78,0x99,0x98,0x91,0x98,0x91,0xB8,0x91,0xB8,0x89,0xB8,0x89,0xB8,0x89,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0x5A,0xD1,0x5A,0xC9,0x5A,0xC9,0x59,0xC9,0x59,0xC1,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB9,0x79,0xB1,0x79,0xB1,0x99,0xA9,0x39,0xB2,0x3A,0xBB,0xFB,0xC3,0x5C,0xCC,0x5B,0xCC,0xFB,0xBB,0x3A,0xB3,0x59,0x9A,0xB8,0x91,0x98,0x89,0xB8,0x81,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,
0x5A,0xC9,0x5A,0xC9,0x59,0xC9,0x59,0xC1,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB9,0x59,0xB1,0xB9,0xB1,0x7B,0xC3,0x9D,0xE5,0x1F,0xF7,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0x3E,0xF7,0xBD,0xD5,0x9A,0xAB,0xF8,0x89,0xB8,0x79,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0x59,0xC9,0x59,0xC9,0x59,0xC1,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB1,0x79,0xB1,0xFA,0xC2,0x1D,0xE6,0xDF,0xFF,0xFF,0xFF,0x3F,0xF7,0xFD,0xE5,0x1C,0xD5,0x9C,0xC4,
0x9C,0xC4,0x1C,0xCD,0x1D,0xDE,0x3E,0xF7,0xFF,0xFF,0xDF,0xFF,0x1D,0xDE,0x3A,0x9B,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0x59,0xC9,0x59,0xC1,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB1,0x99,0xB1,0x3C,0xD4,0x7F,0xFF,0xFF,0xFF,0x7E,0xEE,0x1B,0xC4,0x59,0xAA,0xB9,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0x98,0x89,0xD8,0x89,0x79,0x92,0x1B,0xB4,0x9E,0xE6,0xFF,0xFF,0x7F,0xF7,0xFA,0xA3,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,
0x59,0xC1,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB1,0x99,0xB1,0xBC,0xDC,0xDF,0xFF,0x9F,0xFF,0x7C,0xCC,0xF9,0xA9,0x78,0x99,0x98,0x99,0x98,0x99,0x98,0x91,0x1B,0xBC,0x1B,0xBC,0x98,0x89,0xB8,0x89,0xB8,0x81,0xB8,0x79,0x38,0x82,0xBB,0xB4,0x3E,0xEF,0xBB,0xB4,0xB7,0x69,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0x59,0xC1,0x79,0xB9,0x79,0xB9,0x79,0xB9,0x79,0xB1,0x3C,0xD4,0xDF,0xFF,0x3F,0xF7,0x7B,0xC3,0x99,0xA1,0x98,0x99,0x98,0x99,0x98,0x99,0xB8,0x91,0x98,0x91,0xFC,0xCC,
0xFC,0xC4,0x98,0x81,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0x98,0x82,0x78,0x7A,0xBB,0xB4,0xFA,0x9B,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x79,0xB9,0x79,0xB9,0x79,0xB9,0x59,0xB1,0xFA,0xC2,0x7F,0xFF,0x9F,0xFF,0x7B,0xC3,0x78,0xA1,0x98,0xA1,0x98,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0x98,0x89,0xFC,0xC4,0xFC,0xC4,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x69,0x98,0x7A,0x3E,0xEF,0x9F,0xF7,0x59,0x83,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,
0x79,0xB9,0x79,0xB9,0x79,0xB1,0xB9,0xB1,0x1D,0xE6,0xFF,0xFF,0x7C,0xCC,0x99,0xA1,0x98,0xA1,0x98,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0xB8,0x89,0x98,0x89,0xFC,0xC4,0xFC,0xC4,0xB8,0x79,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xBB,0xAC,0xFF,0xFF,0x3D,0xCE,0x37,0x62,0xF7,0x51,0x17,0x52,0x17,0x52,0x79,0xB1,0x79,0xB1,0x79,0xB1,0x7B,0xC3,0xDF,0xFF,0x7E,0xEE,0xF9,0xA9,0x98,0x99,0x98,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0xB8,0x89,0xB8,0x89,0x98,0x81,0xFC,0xC4,
0xFC,0xC4,0xB8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0x58,0x6A,0x9D,0xDE,0xDF,0xFF,0xD9,0x8B,0xF7,0x51,0x17,0x52,0x17,0x4A,0x79,0xB1,0x79,0xB1,0x99,0xA9,0x9D,0xE5,0xFF,0xFF,0x1B,0xC4,0x78,0x99,0x98,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0xB8,0x89,0xB8,0x89,0xB8,0x89,0xB8,0x81,0xFC,0xC4,0xFC,0xBC,0xB8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xD7,0x59,0x5A,0x9C,0xFF,0xFF,0xDC,0xC5,0x17,0x52,0x17,0x4A,0x17,0x4A,
0x79,0xB1,0x79,0xA9,0x39,0xB2,0x1F,0xF7,0x3F,0xF7,0x59,0xAA,0x98,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0xB8,0x89,0xB8,0x89,0xB8,0x89,0xB8,0x81,0xB8,0x79,0xFC,0xC4,0xFC,0xBC,0xB7,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xB8,0x6A,0x3E,0xEF,0x3E,0xEF,0xB8,0x62,0x16,0x4A,0x16,0x42,0x79,0xA9,0x79,0xA9,0x3A,0xBB,0xDF,0xFF,0xFD,0xE5,0xB9,0x99,0x98,0x99,0xB8,0x91,0xB8,0x91,0xB8,0x89,0xB8,0x89,0xB8,0x89,0xB8,0x81,0xD8,0x81,0xB8,0x79,0xFC,0xBC,
0xFC,0xBC,0xB7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x1D,0xCE,0xDF,0xFF,0x99,0x73,0x16,0x42,0x16,0x42,0x99,0xA9,0x79,0xA1,0xFB,0xC3,0xFF,0xFF,0x1C,0xD5,0x98,0x99,0x98,0x91,0x98,0x91,0x98,0x89,0x98,0x89,0x98,0x81,0x98,0x81,0xB8,0x79,0xB8,0x79,0xB8,0x79,0x5C,0xC5,0x5C,0xC5,0xD7,0x69,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0xF7,0x49,0x3B,0xAD,0xFF,0xFF,0x5A,0x8C,0x16,0x42,0x36,0x42,
0x99,0xA1,0x78,0xA1,0x5C,0xCC,0xFF,0xFF,0x9C,0xC4,0x98,0x91,0x3A,0xAB,0x3B,0xBC,0x3B,0xBC,0x3B,0xB4,0x3B,0xB4,0x3B,0xB4,0x3B,0xB4,0x3B,0xAC,0xBB,0xB4,0x7F,0xF7,0x5F,0xEF,0xF9,0x82,0xD7,0x61,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,0xF6,0x49,0xDB,0x9C,0xFF,0xFF,0xBA,0x94,0x16,0x3A,0x36,0x3A,0x99,0xA1,0x78,0x99,0x5B,0xCC,0xFF,0xFF,0x9C,0xC4,0x98,0x91,0x5A,0xAB,0x3B,0xBC,0x3B,0xB4,0x3B,0xB4,0x3B,0xB4,0x3B,0xB4,0x3B,0xAC,0x3B,0xAC,0xBB,0xB4,0x5E,0xEF,
0xFF,0xFF,0xFB,0xB4,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,0x17,0x4A,0xF6,0x41,0xDB,0x9C,0xFF,0xFF,0xBA,0x94,0x16,0x3A,0x36,0x3A,0x98,0x99,0x78,0x99,0xFB,0xBB,0xFF,0xFF,0x1C,0xCD,0x98,0x89,0x98,0x89,0x98,0x81,0x98,0x81,0xB8,0x79,0xB8,0x79,0xB7,0x79,0xB7,0x71,0xB7,0x71,0xD7,0x69,0xF9,0x82,0xFB,0xB4,0x7F,0xF7,0x9B,0xAC,0x17,0x62,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,0x17,0x4A,0x17,0x4A,0x16,0x42,0x5B,0xAD,0xFF,0xFF,0x5A,0x84,0x16,0x32,0x36,0x32,
0x98,0x99,0x98,0x91,0x3A,0xB3,0xDF,0xFF,0x1D,0xDE,0xD8,0x89,0xB8,0x89,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xD7,0x61,0xF7,0x61,0x9B,0xAC,0x7F,0xF7,0xBB,0xA4,0x17,0x5A,0xF7,0x51,0x17,0x52,0x17,0x4A,0x17,0x4A,0x16,0x42,0x37,0x42,0x3D,0xC6,0xDF,0xFF,0x98,0x6B,0x16,0x32,0x36,0x32,0x98,0x99,0x98,0x91,0x59,0x9A,0x3E,0xF7,0x3F,0xF7,0x79,0x92,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,
0xF7,0x61,0x17,0x62,0xBB,0xA4,0x7F,0xF7,0xBB,0xA4,0x17,0x52,0x17,0x4A,0x17,0x4A,0x16,0x42,0x16,0x42,0xD7,0x5A,0x5E,0xE7,0x3E,0xE7,0xD7,0x4A,0x36,0x32,0x36,0x2A,0xB8,0x91,0xB8,0x91,0xB8,0x91,0xBD,0xD5,0xFF,0xFF,0x1B,0xB4,0xB8,0x79,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0x17,0x5A,0xBB,0xA4,0x9F,0xF7,0x5A,0x94,0x16,0x4A,0x16,0x42,0x16,0x42,0x16,0x3A,0x5A,0x84,0xFF,0xFF,0xDC,0xB5,0x56,0x32,0x36,0x2A,0x56,0x2A,
0xB8,0x91,0xB8,0x89,0x98,0x89,0x9A,0xAB,0xDF,0xFF,0x9E,0xE6,0x38,0x82,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0xF7,0x51,0x17,0x52,0x5A,0x94,0xF9,0x83,0x16,0x42,0x16,0x42,0x16,0x3A,0x97,0x4A,0x9D,0xD6,0xDF,0xFF,0xF9,0x6B,0x36,0x2A,0x56,0x2A,0x56,0x2A,0xB8,0x89,0xB8,0x89,0xB8,0x81,0xF8,0x89,0x1D,0xDE,0xFF,0xFF,0xBB,0xB4,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,
0xF7,0x59,0x17,0x52,0x17,0x52,0x17,0x4A,0x16,0x4A,0x16,0x42,0x16,0x42,0x36,0x42,0x16,0x3A,0xDA,0x94,0xFF,0xFF,0x5D,0xC6,0x76,0x32,0x36,0x2A,0x56,0x2A,0x56,0x22,0xB8,0x89,0xB8,0x89,0xB8,0x81,0xB8,0x79,0x3A,0x9B,0x7F,0xF7,0x3E,0xEF,0x98,0x82,0xD7,0x69,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,0x17,0x4A,0x17,0x4A,0x16,0x42,0x16,0x42,0x36,0x42,0x16,0x3A,0xF9,0x73,0x9F,0xF7,0x9F,0xEF,0x98,0x5B,0x36,0x2A,0x56,0x2A,0x56,0x22,0x56,0x22,
0xB8,0x81,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xFA,0xA3,0xBB,0xB4,0x78,0x7A,0x98,0x7A,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,0x17,0x4A,0x17,0x4A,0x16,0x42,0x16,0x42,0x16,0x3A,0x16,0x3A,0xF9,0x73,0x5E,0xEF,0xFF,0xFF,0xBA,0x84,0x36,0x2A,0x56,0x2A,0x56,0x22,0x56,0x22,0x56,0x1A,0xB8,0x81,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xB7,0x69,0xBB,0xB4,0x3E,0xEF,0xBB,0xAC,0x58,0x6A,0xD7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,
0x17,0x4A,0x17,0x4A,0x16,0x42,0x16,0x42,0x16,0x3A,0x97,0x4A,0xDA,0x94,0x9F,0xF7,0xFF,0xFF,0x1B,0x95,0x56,0x32,0x56,0x22,0x56,0x22,0x56,0x22,0x56,0x1A,0x55,0x1A,0xD8,0x81,0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xFA,0x9B,0x9F,0xF7,0xFF,0xFF,0x9D,0xDE,0x5A,0x9C,0xB8,0x6A,0x17,0x52,0xF7,0x49,0xF6,0x49,0xF6,0x41,0x16,0x42,0x37,0x42,0xD7,0x5A,0x5A,0x84,0x9D,0xD6,0xFF,0xFF,0x9F,0xEF,0xBA,0x84,0x56,0x32,0x36,0x22,0x56,0x22,0x56,0x22,0x56,0x1A,0x55,0x1A,0x55,0x12,
0xD8,0x79,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0x59,0x83,0x3D,0xCE,0xDF,0xFF,0xFF,0xFF,0x3E,0xEF,0x1D,0xCE,0x3B,0xAD,0xDB,0x9C,0xDB,0x9C,0x5B,0xAD,0x3D,0xC6,0x5E,0xE7,0xFF,0xFF,0xDF,0xFF,0x5D,0xC6,0x98,0x5B,0x36,0x2A,0x56,0x22,0x56,0x22,0x56,0x22,0x56,0x1A,0x55,0x1A,0x55,0x12,0x75,0x12,0xD8,0x79,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0x37,0x62,0xD9,0x8B,0xDC,0xC5,0x3E,0xEF,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0x3E,0xE7,0xDC,0xB5,0xF9,0x6B,0x76,0x32,0x36,0x2A,0x56,0x2A,0x56,0x22,0x56,0x22,0x56,0x1A,0x55,0x1A,0x55,0x12,0x75,0x12,0x75,0x12,0xD8,0x71,0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x51,0xF7,0x51,0x17,0x52,0xB8,0x62,0x99,0x73,0x5A,0x8C,0xBA,0x94,0xBA,0x94,0x5A,0x84,0x98,0x6B,0xD7,0x4A,0x56,0x32,0x36,0x2A,0x36,0x2A,0x56,0x2A,0x56,0x22,0x56,0x22,0x56,0x1A,0x55,0x1A,0x55,0x12,0x75,0x12,0x75,0x12,0x75,0x0A,
0xD7,0x71,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,0x17,0x4A,0x16,0x4A,0x16,0x42,0x16,0x42,0x16,0x3A,0x16,0x3A,0x16,0x32,0x16,0x32,0x36,0x32,0x36,0x2A,0x56,0x2A,0x56,0x2A,0x56,0x22,0x56,0x22,0x56,0x1A,0x55,0x1A,0x55,0x12,0x75,0x12,0x75,0x12,0x75,0x0A,0x75,0x12,0xD7,0x71,0xD7,0x69,0xF7,0x69,0xF7,0x61,0xF7,0x61,0xF7,0x59,0xF7,0x59,0xF7,0x59,0x17,0x52,0x17,0x52,0x17,0x4A,0x17,0x4A,0x16,0x42,0x16,0x42,0x36,0x42,0x36,0x3A,
0x36,0x3A,0x36,0x32,0x36,0x32,0x36,0x2A,0x56,0x2A,0x56,0x2A,0x56,0x22,0x56,0x22,0x56,0x1A,0x55,0x1A,0x55,0x12,0x75,0x12,0x75,0x12,0x75,0x0A,0x75,0x12,0x75,0x12,

0x00,0x00,0x0F,0x6D,0xCA,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0xCA,0x6D,0x0F,0x00,0x00,0x00,0x1C,0xA9,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0xA9,0x1C,0x00,0x0F,0xA8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA8,0x0F,
0x6D,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x6D,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCA,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCA,0x6D,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x6D,0x0F,0xA9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA9,0x0F,
0x00,0x1C,0xA8,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0xA8,0x1C,0x00,0x00,0x00,0x0F,0x6D,0xCA,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0xCA,0x6D,0x0F,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_chrns_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_chrns_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_chrns_icon_data};

