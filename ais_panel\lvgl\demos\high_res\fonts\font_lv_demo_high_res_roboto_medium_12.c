/*******************************************************************************
 * Size: 12 px
 * Bpp: 8
 * Opts: --bpp 8 --size 12 --no-compress --font Roboto-Medium.ttf --range 32-127,176 --format lvgl -o font_lv_demo_high_res_roboto_medium_12.c
 ******************************************************************************/

#include "../../../lvgl.h"

#if LV_USE_DEMO_HIGH_RES

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x1e, 0xff, 0x5c, 0x18, 0xff, 0x57, 0x12, 0xff,
    0x51, 0xd, 0xff, 0x4c, 0x7, 0xff, 0x46, 0x1,
    0xff, 0x41, 0x0, 0x82, 0x1f, 0x3, 0x8b, 0x24,
    0xe, 0xe4, 0x49,

    /* U+0022 "\"" */
    0x68, 0x9b, 0xa4, 0x5f, 0x68, 0x8e, 0xa4, 0x52,
    0x68, 0x79, 0xa4, 0x3d, 0x22, 0x23, 0x35, 0xf,

    /* U+0023 "#" */
    0x0, 0x0, 0xf, 0xeb, 0x1, 0xc2, 0x3a, 0x0,
    0x0, 0x0, 0x47, 0xb4, 0x4, 0xef, 0x7, 0x0,
    0x0, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c,
    0x0, 0x3, 0xa7, 0x58, 0x5c, 0xa0, 0x4, 0x0,
    0x0, 0x0, 0xd2, 0x2a, 0x86, 0x75, 0x0, 0x0,
    0x70, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0x0,
    0x0, 0x28, 0xd4, 0x0, 0xdc, 0x1c, 0x0, 0x0,
    0x0, 0x4e, 0xad, 0x6, 0xf2, 0x1, 0x0, 0x0,
    0x0, 0x75, 0x87, 0x29, 0xd2, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0xe4, 0xc, 0x0, 0x0, 0x0,
    0x6, 0x6c, 0xf4, 0x74, 0x7, 0x0, 0x0, 0xa9,
    0xf3, 0xb0, 0xf4, 0xac, 0x0, 0x6, 0xfd, 0x6e,
    0x0, 0x67, 0xff, 0x13, 0x5, 0xf8, 0x89, 0x0,
    0x15, 0x54, 0xd, 0x0, 0x83, 0xff, 0xb3, 0x3f,
    0x0, 0x0, 0x0, 0x0, 0x52, 0xca, 0xff, 0x82,
    0x0, 0xe, 0x24, 0x0, 0x0, 0x80, 0xfd, 0x17,
    0x52, 0xff, 0x21, 0x0, 0x44, 0xff, 0x2b, 0xf,
    0xec, 0xcf, 0x85, 0xd9, 0xdc, 0x5, 0x0, 0x28,
    0xa0, 0xfa, 0x98, 0x1e, 0x0, 0x0, 0x0, 0x0,
    0xec, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0xc, 0xbd, 0xf1, 0x96, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0xb5, 0xd, 0xe6, 0x25, 0x31, 0x96,
    0x0, 0x0, 0x5b, 0xb7, 0xf, 0xe7, 0x27, 0xc6,
    0x32, 0x0, 0x0, 0x9, 0xb2, 0xe7, 0x8d, 0x6a,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17,
    0xd8, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa5, 0x5f, 0xa2, 0xe7, 0xa0, 0x2, 0x0, 0x0,
    0x42, 0xbf, 0x41, 0xcc, 0xe, 0xd1, 0x3b, 0x0,
    0x0, 0xcc, 0x2a, 0x42, 0xca, 0x6, 0xcb, 0x3f,
    0x0, 0x0, 0x11, 0x0, 0x4, 0xaf, 0xef, 0xaf,
    0x4,

    /* U+0026 "&" */
    0x0, 0x24, 0xc8, 0xf5, 0xb7, 0xf, 0x0, 0x0,
    0x0, 0xaa, 0xd6, 0x4f, 0xe2, 0x7c, 0x0, 0x0,
    0x0, 0xb8, 0xa7, 0x5, 0xd0, 0x73, 0x0, 0x0,
    0x0, 0x55, 0xfc, 0xd4, 0xb6, 0x8, 0x0, 0x0,
    0x0, 0x61, 0xfd, 0xf9, 0x24, 0x12, 0x68, 0x5,
    0x3a, 0xfd, 0x5c, 0xc6, 0xd1, 0x52, 0xf7, 0x0,
    0x75, 0xf8, 0x0, 0x1b, 0xe8, 0xf4, 0xbb, 0x0,
    0x3e, 0xff, 0x6d, 0x28, 0xab, 0xff, 0x85, 0x0,
    0x0, 0x6b, 0xe0, 0xf5, 0xca, 0x9b, 0xfa, 0x39,

    /* U+0027 "'" */
    0x84, 0x8f, 0x84, 0x84, 0x84, 0x74, 0x25, 0x1d,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x1b, 0x0, 0x0, 0x54, 0xcc,
    0x0, 0x17, 0xf0, 0x30, 0x0, 0x8a, 0xad, 0x0,
    0x0, 0xe2, 0x5f, 0x0, 0x18, 0xff, 0x2c, 0x0,
    0x35, 0xff, 0x13, 0x0, 0x3b, 0xff, 0xf, 0x0,
    0x2b, 0xff, 0x1f, 0x0, 0x8, 0xfb, 0x3f, 0x0,
    0x0, 0xc1, 0x7b, 0x0, 0x0, 0x5c, 0xdb, 0x2,
    0x0, 0x2, 0xc6, 0x78, 0x0, 0x0, 0x17, 0x9a,

    /* U+0029 ")" */
    0x1b, 0x0, 0x0, 0x0, 0x9c, 0x82, 0x0, 0x0,
    0x15, 0xea, 0x3e, 0x0, 0x0, 0x84, 0xbe, 0x0,
    0x0, 0x34, 0xfe, 0x18, 0x0, 0x4, 0xfa, 0x50,
    0x0, 0x0, 0xe8, 0x6e, 0x0, 0x0, 0xe0, 0x74,
    0x0, 0x0, 0xf1, 0x63, 0x0, 0x11, 0xfe, 0x38,
    0x0, 0x4d, 0xee, 0x6, 0x0, 0xaf, 0x8d, 0x0,
    0x4a, 0xe3, 0x12, 0x0, 0x82, 0x31, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xd3, 0x32, 0x0, 0x0, 0x47, 0x23,
    0xc8, 0x2e, 0x53, 0x0, 0x81, 0xea, 0xf7, 0xf0,
    0xca, 0x10, 0x0, 0x5b, 0xf0, 0xa5, 0x0, 0x0,
    0x13, 0xeb, 0x31, 0xdd, 0x46, 0x0, 0x0, 0x31,
    0x0, 0x30, 0xa, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x45, 0xc0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0x4, 0x0, 0x0, 0x10, 0x1c,
    0x6d, 0xff, 0x1f, 0x1c, 0x7, 0x98, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x2d, 0x4c, 0x8c, 0xff,
    0x4e, 0x4c, 0x13, 0x0, 0x0, 0x5c, 0xff, 0x4,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x4, 0x0,
    0x0,

    /* U+002C "," */
    0x3c, 0xff, 0xc, 0x49, 0xfa, 0x3, 0x8f, 0xa8,
    0x0, 0x49, 0x15, 0x0,

    /* U+002D "-" */
    0x4, 0x8, 0x8, 0x4, 0x94, 0xff, 0xff, 0x80,
    0x20, 0x38, 0x38, 0x1c,

    /* U+002E "." */
    0x8, 0x94, 0x25, 0x16, 0xe5, 0x49,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0xcc, 0x50, 0x0, 0x0, 0x21,
    0xf1, 0x8, 0x0, 0x0, 0x78, 0xa4, 0x0, 0x0,
    0x0, 0xce, 0x4e, 0x0, 0x0, 0x23, 0xf0, 0x7,
    0x0, 0x0, 0x7a, 0xa2, 0x0, 0x0, 0x0, 0xd0,
    0x4c, 0x0, 0x0, 0x25, 0xef, 0x6, 0x0, 0x0,
    0x7c, 0xa0, 0x0, 0x0, 0x0, 0xd1, 0x4a, 0x0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x3d, 0xd1, 0xf3, 0xc0, 0x23, 0x0, 0x8,
    0xea, 0xae, 0x4b, 0xcc, 0xc3, 0x0, 0x41, 0xff,
    0x26, 0x0, 0x56, 0xff, 0x11, 0x5c, 0xff, 0xa,
    0x0, 0x3b, 0xff, 0x2d, 0x64, 0xff, 0x8, 0x0,
    0x38, 0xff, 0x34, 0x5c, 0xff, 0xb, 0x0, 0x3a,
    0xff, 0x2b, 0x3e, 0xff, 0x28, 0x0, 0x57, 0xff,
    0x10, 0x7, 0xe7, 0xaf, 0x4a, 0xcd, 0xc0, 0x0,
    0x0, 0x3b, 0xd0, 0xf3, 0xc0, 0x22, 0x0,

    /* U+0031 "1" */
    0x0, 0x3, 0x4d, 0xaf, 0x7b, 0x2, 0xe6, 0xf5,
    0xfc, 0x80, 0x2, 0x5d, 0xb, 0xec, 0x80, 0x0,
    0x0, 0x0, 0xec, 0x80, 0x0, 0x0, 0x0, 0xec,
    0x80, 0x0, 0x0, 0x0, 0xec, 0x80, 0x0, 0x0,
    0x0, 0xec, 0x80, 0x0, 0x0, 0x0, 0xec, 0x80,
    0x0, 0x0, 0x0, 0xec, 0x80,

    /* U+0032 "2" */
    0x0, 0x5e, 0xdc, 0xf6, 0xc7, 0x31, 0x0, 0x34,
    0xfe, 0x91, 0x49, 0xd3, 0xd6, 0x0, 0x68, 0xd0,
    0x2, 0x0, 0x6a, 0xfe, 0x3, 0x0, 0x0, 0x0,
    0x0, 0xa2, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x52,
    0xfc, 0x41, 0x0, 0x0, 0x0, 0x39, 0xf6, 0x72,
    0x0, 0x0, 0x0, 0x27, 0xec, 0x8f, 0x0, 0x0,
    0x0, 0x18, 0xdf, 0xd0, 0x3d, 0x3c, 0x3c, 0x16,
    0x5c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,

    /* U+0033 "3" */
    0x0, 0x68, 0xe2, 0xf4, 0xbb, 0x22, 0x0, 0x37,
    0xff, 0x8a, 0x49, 0xd4, 0xbf, 0x0, 0x36, 0x7f,
    0x2, 0x0, 0x76, 0xf4, 0x0, 0x0, 0x0, 0x2,
    0x15, 0xbe, 0xb9, 0x0, 0x0, 0x0, 0xb8, 0xff,
    0xee, 0x1e, 0x0, 0x0, 0x0, 0x25, 0x42, 0xc3,
    0xcf, 0x1, 0x33, 0x60, 0x0, 0x0, 0x58, 0xff,
    0x15, 0x4d, 0xff, 0x79, 0x47, 0xc4, 0xdf, 0x0,
    0x0, 0x75, 0xe2, 0xf4, 0xc0, 0x2d, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x74, 0x0, 0x0,
    0x0, 0x7, 0xdc, 0xff, 0x74, 0x0, 0x0, 0x0,
    0x7a, 0xcb, 0xf8, 0x74, 0x0, 0x0, 0x1b, 0xf3,
    0x3b, 0xf8, 0x74, 0x0, 0x0, 0xa6, 0xa6, 0x0,
    0xf8, 0x74, 0x0, 0x3b, 0xf6, 0x21, 0x4, 0xf8,
    0x76, 0x2, 0xa7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x84, 0x22, 0x34, 0x34, 0x34, 0xf9, 0x90, 0x1a,
    0x0, 0x0, 0x0, 0x0, 0xf8, 0x74, 0x0,

    /* U+0035 "5" */
    0x0, 0x92, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xad, 0xc0, 0x54, 0x54, 0x54, 0x5, 0x0, 0xc8,
    0x88, 0x1, 0x0, 0x0, 0x0, 0x0, 0xe3, 0xe2,
    0xfa, 0xe2, 0x4f, 0x0, 0x0, 0x8c, 0x80, 0x44,
    0xb7, 0xf2, 0x12, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xff, 0x46, 0x1a, 0x90, 0x1e, 0x0, 0x29, 0xff,
    0x44, 0x7, 0xe7, 0xb4, 0x47, 0xb4, 0xed, 0xf,
    0x0, 0x34, 0xc8, 0xf5, 0xcf, 0x3e, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x4d, 0xc8, 0xf6, 0x10, 0x0, 0x0,
    0x54, 0xfd, 0xa4, 0x57, 0x4, 0x0, 0x2, 0xe0,
    0x96, 0x1, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xb4,
    0xf2, 0xe6, 0x51, 0x0, 0x49, 0xff, 0xb5, 0x44,
    0xb6, 0xf2, 0x11, 0x4b, 0xff, 0x21, 0x0, 0x2c,
    0xff, 0x43, 0x2b, 0xff, 0x3e, 0x0, 0x2c, 0xff,
    0x3e, 0x0, 0xcb, 0xc8, 0x4e, 0xb8, 0xe6, 0xb,
    0x0, 0x1f, 0xbb, 0xf3, 0xcb, 0x35, 0x0,

    /* U+0037 "7" */
    0x98, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4e, 0x23,
    0x3c, 0x3c, 0x3c, 0x81, 0xf7, 0x13, 0x0, 0x0,
    0x0, 0x0, 0xbe, 0x9f, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0x35, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xc9, 0x0, 0x0, 0x0, 0x0, 0x16, 0xf8, 0x5e,
    0x0, 0x0, 0x0, 0x0, 0x80, 0xeb, 0x7, 0x0,
    0x0, 0x0, 0x7, 0xe8, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x60, 0xfd, 0x20, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x44, 0xd4, 0xf5, 0xc3, 0x28, 0x0, 0x9,
    0xef, 0xb4, 0x49, 0xd5, 0xc8, 0x0, 0x2a, 0xff,
    0x45, 0x0, 0x72, 0xf9, 0x0, 0xb, 0xec, 0x8e,
    0x14, 0xb6, 0xc5, 0x0, 0x0, 0x54, 0xff, 0xff,
    0xf9, 0x29, 0x0, 0x1a, 0xef, 0x95, 0x43, 0xb9,
    0xd5, 0x4, 0x59, 0xff, 0x13, 0x0, 0x43, 0xff,
    0x29, 0x2e, 0xfe, 0x94, 0x45, 0xb3, 0xf1, 0xa,
    0x0, 0x61, 0xdc, 0xf6, 0xce, 0x41, 0x0,

    /* U+0039 "9" */
    0x0, 0x4c, 0xd9, 0xf0, 0xa5, 0xc, 0x0, 0x1b,
    0xf6, 0xa3, 0x51, 0xe7, 0x96, 0x0, 0x61, 0xfe,
    0xe, 0x0, 0x76, 0xf4, 0x0, 0x67, 0xfc, 0x4,
    0x0, 0x52, 0xff, 0x14, 0x34, 0xff, 0x70, 0x19,
    0xb3, 0xff, 0x15, 0x0, 0x93, 0xff, 0xfe, 0xce,
    0xf7, 0x3, 0x0, 0x0, 0x19, 0x16, 0xa7, 0xba,
    0x0, 0x0, 0xf, 0x58, 0xa1, 0xfa, 0x3b, 0x0,
    0x0, 0x38, 0xf4, 0xc1, 0x3f, 0x0, 0x0,

    /* U+003A ":" */
    0x1b, 0xe4, 0x41, 0xb, 0x96, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x94, 0x21, 0x1c, 0xe6, 0x42,

    /* U+003B ";" */
    0x34, 0xe5, 0x27, 0x1b, 0x97, 0x12, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x50, 0xc, 0x20, 0xff, 0x24, 0x3a, 0xfa, 0xc,
    0x8e, 0x93, 0x0, 0xc, 0x8, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x27, 0x9f, 0x43, 0x0, 0x2f,
    0xa9, 0xfd, 0xdd, 0x2b, 0x7e, 0xfc, 0xb4, 0x49,
    0x1, 0x0, 0x77, 0xfb, 0xbb, 0x52, 0x4, 0x0,
    0x0, 0x25, 0x9e, 0xfa, 0xe5, 0x2e, 0x0, 0x0,
    0x0, 0x1e, 0x94, 0x3f,

    /* U+003D "=" */
    0x1, 0xc, 0xc, 0xc, 0xc, 0xa, 0x28, 0xff,
    0xff, 0xff, 0xff, 0xe8, 0x9, 0x3c, 0x3c, 0x3c,
    0x3c, 0x36, 0x1, 0xc, 0xc, 0xc, 0xc, 0xa,
    0x28, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x9, 0x3c,
    0x3c, 0x3c, 0x3c, 0x36,

    /* U+003E ">" */
    0x3c, 0xa6, 0x2f, 0x0, 0x0, 0x0, 0x24, 0xd0,
    0xff, 0xb7, 0x40, 0x0, 0x0, 0x0, 0x36, 0x9b,
    0xf5, 0xac, 0x0, 0x1, 0x47, 0xaf, 0xfc, 0xa2,
    0x27, 0xdc, 0xfd, 0xac, 0x34, 0x0, 0x39, 0x9b,
    0x25, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x8, 0xa1, 0xf0, 0xe8, 0x7e, 0x0, 0x75, 0xf8,
    0x64, 0x88, 0xff, 0x39, 0x35, 0x47, 0x0, 0x18,
    0xff, 0x53, 0x0, 0x0, 0x0, 0x88, 0xec, 0x13,
    0x0, 0x0, 0x67, 0xf8, 0x45, 0x0, 0x0, 0x0,
    0xde, 0x89, 0x0, 0x0, 0x0, 0x0, 0x44, 0x1c,
    0x0, 0x0, 0x0, 0x0, 0x7e, 0x33, 0x0, 0x0,
    0x0, 0x1, 0xd8, 0x62, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x2, 0x69, 0xd1, 0xee, 0xe3, 0x9c,
    0x1d, 0x0, 0x0, 0x0, 0x1, 0xa9, 0xb4, 0x2a,
    0x0, 0xb, 0x62, 0xda, 0x1e, 0x0, 0x0, 0x68,
    0xba, 0x3, 0x0, 0x0, 0x0, 0x0, 0x59, 0xa5,
    0x0, 0x0, 0xdf, 0x2c, 0x0, 0x77, 0xec, 0xd8,
    0x42, 0x3, 0xdf, 0x6, 0x2d, 0xd9, 0x0, 0x4a,
    0xe0, 0x1e, 0x9f, 0x73, 0x0, 0xc1, 0x24, 0x54,
    0xb1, 0x0, 0xae, 0x7a, 0x0, 0xb1, 0x5b, 0x0,
    0xba, 0x2e, 0x62, 0xa2, 0x0, 0xd7, 0x4f, 0x0,
    0xc8, 0x42, 0x0, 0xd6, 0x10, 0x55, 0xb0, 0x0,
    0xc8, 0x69, 0x25, 0xf8, 0x47, 0x41, 0xc0, 0x0,
    0x25, 0xe5, 0x1, 0x4f, 0xe9, 0xb3, 0x65, 0xe4,
    0xc9, 0x25, 0x0, 0x0, 0xcd, 0x5e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35,
    0xea, 0x6b, 0x11, 0x4, 0x36, 0x1e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xab, 0xe9, 0xf3, 0xcb,
    0x33, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0xd9, 0xd5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x36, 0xff, 0xff, 0x31, 0x0, 0x0,
    0x0, 0x0, 0x91, 0xd4, 0xdc, 0x8d, 0x0, 0x0,
    0x0, 0x3, 0xe8, 0x84, 0x8c, 0xe6, 0x2, 0x0,
    0x0, 0x48, 0xff, 0x34, 0x3a, 0xff, 0x45, 0x0,
    0x0, 0xa4, 0xe6, 0xc, 0xd, 0xe9, 0xa1, 0x0,
    0xa, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xa,
    0x5b, 0xff, 0x61, 0x3c, 0x3c, 0x64, 0xff, 0x59,
    0xb6, 0xd6, 0x0, 0x0, 0x0, 0x0, 0xda, 0xb5,

    /* U+0042 "B" */
    0x20, 0xff, 0xff, 0xff, 0xe9, 0x9c, 0xa, 0x20,
    0xff, 0x89, 0x4c, 0x73, 0xfb, 0x84, 0x20, 0xff,
    0x58, 0x0, 0x0, 0xc3, 0xb5, 0x20, 0xff, 0x58,
    0x0, 0x2b, 0xee, 0x83, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xdf, 0x12, 0x20, 0xff, 0x77, 0x30, 0x41,
    0xd8, 0xad, 0x20, 0xff, 0x58, 0x0, 0x0, 0x8f,
    0xe9, 0x20, 0xff, 0x87, 0x48, 0x60, 0xe8, 0xb8,
    0x20, 0xff, 0xff, 0xff, 0xf0, 0xb3, 0x1f,

    /* U+0043 "C" */
    0x0, 0xa, 0x95, 0xeb, 0xef, 0xb2, 0x21, 0x0,
    0x0, 0xaf, 0xf1, 0x71, 0x5c, 0xd5, 0xd6, 0x6,
    0x26, 0xff, 0x65, 0x0, 0x0, 0x3c, 0xff, 0x3e,
    0x59, 0xff, 0x27, 0x0, 0x0, 0x0, 0x8, 0x2,
    0x67, 0xff, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xff, 0x63, 0x0, 0x0, 0x3b, 0xff, 0x3f,
    0x0, 0xb5, 0xee, 0x6c, 0x5d, 0xd3, 0xd7, 0x6,
    0x0, 0xe, 0x9e, 0xee, 0xee, 0xb0, 0x22, 0x0,

    /* U+0044 "D" */
    0x20, 0xff, 0xff, 0xf9, 0xd0, 0x58, 0x0, 0x0,
    0x20, 0xff, 0x89, 0x55, 0x9d, 0xff, 0x60, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0xa5, 0xe5, 0x2,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x58, 0xff, 0x27,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x42, 0xff, 0x3a,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x59, 0xff, 0x27,
    0x20, 0xff, 0x58, 0x0, 0x0, 0xa7, 0xe7, 0x2,
    0x20, 0xff, 0x87, 0x52, 0x9d, 0xff, 0x64, 0x0,
    0x20, 0xff, 0xff, 0xf9, 0xd0, 0x5a, 0x0, 0x0,

    /* U+0045 "E" */
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0x68, 0x20,
    0xff, 0x89, 0x4c, 0x4c, 0x4c, 0x1e, 0x20, 0xff,
    0x58, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0x5f,
    0xc, 0xc, 0xa, 0x0, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xdc, 0x0, 0x20, 0xff, 0x7f, 0x3c, 0x3c,
    0x33, 0x0, 0x20, 0xff, 0x58, 0x0, 0x0, 0x0,
    0x0, 0x20, 0xff, 0x87, 0x48, 0x48, 0x48, 0x20,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0x74,

    /* U+0046 "F" */
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0x48, 0x20,
    0xff, 0x89, 0x4c, 0x4c, 0x4c, 0x15, 0x20, 0xff,
    0x58, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0x5f,
    0xc, 0xc, 0x9, 0x0, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xc8, 0x0, 0x20, 0xff, 0x7f, 0x3c, 0x3c,
    0x2e, 0x0, 0x20, 0xff, 0x58, 0x0, 0x0, 0x0,
    0x0, 0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0xd, 0x9c, 0xee, 0xf2, 0xc0, 0x35, 0x0,
    0x0, 0xa5, 0xf3, 0x72, 0x55, 0xc2, 0xed, 0x12,
    0x1e, 0xfd, 0x6d, 0x0, 0x0, 0x19, 0xa0, 0x31,
    0x48, 0xff, 0x2e, 0x0, 0x3, 0x4, 0x4, 0x1,
    0x5f, 0xff, 0x1c, 0x0, 0xd4, 0xff, 0xff, 0x68,
    0x50, 0xff, 0x31, 0x0, 0x2b, 0x40, 0xff, 0x68,
    0x1d, 0xfe, 0x79, 0x0, 0x0, 0x10, 0xff, 0x68,
    0x0, 0xa3, 0xf9, 0x7e, 0x53, 0x9a, 0xff, 0x5a,
    0x0, 0x7, 0x8b, 0xe6, 0xf9, 0xd6, 0x71, 0x1,

    /* U+0048 "H" */
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0x5f, 0xc, 0xc, 0xc, 0xd6, 0xa4,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa4,
    0x20, 0xff, 0x7f, 0x3c, 0x3c, 0x3c, 0xde, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0xd4, 0xa4,

    /* U+0049 "I" */
    0xc, 0xff, 0x70, 0xc, 0xff, 0x70, 0xc, 0xff,
    0x70, 0xc, 0xff, 0x70, 0xc, 0xff, 0x70, 0xc,
    0xff, 0x70, 0xc, 0xff, 0x70, 0xc, 0xff, 0x70,
    0xc, 0xff, 0x70,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0xa4, 0xd8, 0x0, 0x0,
    0x0, 0x0, 0xa4, 0xd8, 0x0, 0x0, 0x0, 0x0,
    0xa4, 0xd8, 0x0, 0x0, 0x0, 0x0, 0xa4, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0xa4, 0xd8, 0x0, 0x0,
    0x0, 0x0, 0xa4, 0xd8, 0x7a, 0x8d, 0x0, 0x0,
    0xb1, 0xca, 0x78, 0xfc, 0x6e, 0x69, 0xf9, 0x88,
    0x8, 0x9a, 0xec, 0xec, 0x9b, 0xa,

    /* U+004B "K" */
    0x20, 0xff, 0x58, 0x0, 0x2, 0xb7, 0xed, 0x21,
    0x20, 0xff, 0x58, 0x0, 0x86, 0xfd, 0x46, 0x0,
    0x20, 0xff, 0x58, 0x52, 0xfe, 0x78, 0x0, 0x0,
    0x20, 0xff, 0x7f, 0xf2, 0xad, 0x0, 0x0, 0x0,
    0x20, 0xff, 0xfb, 0xff, 0xa3, 0x0, 0x0, 0x0,
    0x20, 0xff, 0xdc, 0x7d, 0xff, 0x4d, 0x0, 0x0,
    0x20, 0xff, 0x5b, 0x1, 0xbf, 0xe6, 0x11, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x23, 0xf5, 0xa3, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x70, 0xff, 0x4e,

    /* U+004C "L" */
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0, 0x20,
    0xff, 0x58, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff,
    0x58, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0x58,
    0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0x58, 0x0,
    0x0, 0x0, 0x0, 0x20, 0xff, 0x58, 0x0, 0x0,
    0x0, 0x0, 0x20, 0xff, 0x58, 0x0, 0x0, 0x0,
    0x0, 0x20, 0xff, 0x87, 0x48, 0x48, 0x48, 0xf,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0x38,

    /* U+004D "M" */
    0x20, 0xff, 0xf0, 0x8, 0x0, 0x0, 0x0, 0x7a,
    0xff, 0xa0, 0x20, 0xff, 0xff, 0x54, 0x0, 0x0,
    0x0, 0xd5, 0xff, 0xa0, 0x20, 0xff, 0xdc, 0xaf,
    0x0, 0x0, 0x30, 0xff, 0xdc, 0xa0, 0x20, 0xff,
    0x89, 0xf9, 0x11, 0x0, 0x8b, 0xc5, 0xc3, 0xa0,
    0x20, 0xff, 0x4d, 0xe7, 0x65, 0x2, 0xe4, 0x68,
    0xcd, 0xa0, 0x20, 0xff, 0x52, 0x8e, 0xc0, 0x41,
    0xf9, 0x11, 0xd7, 0xa0, 0x20, 0xff, 0x57, 0x31,
    0xfe, 0xb9, 0xad, 0x0, 0xdc, 0xa0, 0x20, 0xff,
    0x58, 0x0, 0xd4, 0xff, 0x4f, 0x0, 0xdc, 0xa0,
    0x20, 0xff, 0x58, 0x0, 0x77, 0xec, 0x5, 0x0,
    0xdc, 0xa0,

    /* U+004E "N" */
    0x20, 0xff, 0xa3, 0x0, 0x0, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0xff, 0x3d, 0x0, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0xf4, 0xd1, 0x3, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0x7e, 0xf9, 0x6e, 0x0, 0xd4, 0xa4,
    0x20, 0xff, 0x58, 0x87, 0xf0, 0x17, 0xd4, 0xa4,
    0x20, 0xff, 0x58, 0xb, 0xe3, 0xa0, 0xd4, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x56, 0xfe, 0xf4, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x0, 0xbc, 0xff, 0xa4,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x29, 0xfa, 0xa4,

    /* U+004F "O" */
    0x0, 0x7, 0x8f, 0xe9, 0xf3, 0xb3, 0x20, 0x0,
    0x0, 0xa4, 0xf7, 0x7e, 0x69, 0xdd, 0xe1, 0xc,
    0x1f, 0xff, 0x72, 0x0, 0x0, 0x2c, 0xff, 0x65,
    0x55, 0xff, 0x29, 0x0, 0x0, 0x0, 0xe5, 0x9a,
    0x66, 0xff, 0x15, 0x0, 0x0, 0x0, 0xd2, 0xaa,
    0x55, 0xff, 0x29, 0x0, 0x0, 0x0, 0xe5, 0x99,
    0x1e, 0xff, 0x71, 0x0, 0x0, 0x29, 0xff, 0x63,
    0x0, 0xa4, 0xf6, 0x7a, 0x64, 0xd9, 0xe1, 0xb,
    0x0, 0x7, 0x8e, 0xe9, 0xf3, 0xb5, 0x21, 0x0,

    /* U+0050 "P" */
    0x20, 0xff, 0xff, 0xff, 0xf1, 0xbb, 0x2d, 0x0,
    0x20, 0xff, 0x89, 0x4c, 0x5a, 0xd2, 0xe5, 0x9,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x4e, 0xff, 0x32,
    0x20, 0xff, 0x5f, 0xc, 0x18, 0xa1, 0xfb, 0x15,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x66, 0x0,
    0x20, 0xff, 0x7f, 0x3c, 0x2f, 0xb, 0x0, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x9, 0x94, 0xea, 0xf1, 0xaf, 0x1d, 0x0,
    0x0, 0xac, 0xf5, 0x7b, 0x6b, 0xe1, 0xdc, 0x9,
    0x26, 0xff, 0x6a, 0x0, 0x0, 0x34, 0xff, 0x5d,
    0x5d, 0xff, 0x21, 0x0, 0x0, 0x0, 0xed, 0x92,
    0x6e, 0xff, 0xd, 0x0, 0x0, 0x0, 0xda, 0xa2,
    0x5f, 0xff, 0x21, 0x0, 0x0, 0x0, 0xed, 0x92,
    0x2a, 0xff, 0x68, 0x0, 0x0, 0x30, 0xff, 0x5a,
    0x0, 0xae, 0xf4, 0x77, 0x66, 0xdd, 0xd8, 0x6,
    0x0, 0x9, 0x93, 0xeb, 0xf7, 0xff, 0x86, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x76, 0xfe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x4,

    /* U+0052 "R" */
    0x20, 0xff, 0xff, 0xff, 0xeb, 0xa9, 0x18, 0x0,
    0x20, 0xff, 0x89, 0x4c, 0x68, 0xee, 0xb1, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x94, 0xe8, 0x0,
    0x20, 0xff, 0x5f, 0xc, 0x2a, 0xd7, 0xbf, 0x0,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x22, 0x0,
    0x20, 0xff, 0x7f, 0x3c, 0xdd, 0xaf, 0x0, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x6c, 0xfd, 0x27, 0x0,
    0x20, 0xff, 0x58, 0x0, 0xa, 0xec, 0x9b, 0x0,
    0x20, 0xff, 0x58, 0x0, 0x0, 0x82, 0xf8, 0x18,

    /* U+0053 "S" */
    0x0, 0x41, 0xc8, 0xf5, 0xe5, 0x82, 0x2, 0x16,
    0xf4, 0xb8, 0x51, 0x78, 0xfc, 0x75, 0x41, 0xff,
    0x3e, 0x0, 0x0, 0x87, 0x89, 0xe, 0xe7, 0xd0,
    0x4b, 0x4, 0x0, 0x0, 0x0, 0x23, 0xac, 0xfb,
    0xea, 0x76, 0x2, 0x0, 0x0, 0x0, 0x14, 0x76,
    0xfb, 0x7f, 0x67, 0xc7, 0x4, 0x0, 0x0, 0xb9,
    0xc6, 0x36, 0xfc, 0xaa, 0x4e, 0x67, 0xf4, 0x94,
    0x0, 0x4a, 0xca, 0xf7, 0xe8, 0x9a, 0xc,

    /* U+0054 "T" */
    0xbc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8,
    0x37, 0x4c, 0x5c, 0xff, 0x8f, 0x4c, 0x4c, 0x2,
    0x0, 0x0, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x60, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x44, 0xff, 0x34, 0x0, 0x0, 0x60, 0xff, 0x1c,
    0x44, 0xff, 0x34, 0x0, 0x0, 0x60, 0xff, 0x1c,
    0x44, 0xff, 0x34, 0x0, 0x0, 0x60, 0xff, 0x1c,
    0x44, 0xff, 0x34, 0x0, 0x0, 0x60, 0xff, 0x1c,
    0x44, 0xff, 0x34, 0x0, 0x0, 0x60, 0xff, 0x1c,
    0x43, 0xff, 0x34, 0x0, 0x0, 0x60, 0xff, 0x1b,
    0x2c, 0xff, 0x4f, 0x0, 0x0, 0x7b, 0xfb, 0x7,
    0x4, 0xd5, 0xd9, 0x5b, 0x65, 0xeb, 0xad, 0x0,
    0x0, 0x23, 0xb3, 0xef, 0xe9, 0x9c, 0x11, 0x0,

    /* U+0056 "V" */
    0xb9, 0xe1, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x7f,
    0x61, 0xff, 0x2d, 0x0, 0x0, 0x67, 0xff, 0x28,
    0x10, 0xf9, 0x79, 0x0, 0x0, 0xb4, 0xd0, 0x0,
    0x0, 0xb3, 0xc4, 0x0, 0xa, 0xf6, 0x79, 0x0,
    0x0, 0x5c, 0xfc, 0x13, 0x4e, 0xff, 0x22, 0x0,
    0x0, 0xd, 0xf7, 0x5c, 0x9b, 0xca, 0x0, 0x0,
    0x0, 0x0, 0xae, 0xa9, 0xe6, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x57, 0xfb, 0xfe, 0x1c, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf4, 0xc3, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x9d, 0xd8, 0x0, 0x0, 0x6b, 0xfe, 0x12, 0x0,
    0x39, 0xff, 0x3b, 0x65, 0xfe, 0xd, 0x0, 0xa8,
    0xff, 0x4c, 0x0, 0x69, 0xfa, 0x8, 0x2d, 0xff,
    0x3d, 0x0, 0xe5, 0xea, 0x87, 0x0, 0x99, 0xcb,
    0x0, 0x2, 0xf2, 0x6f, 0x23, 0xfd, 0x79, 0xc2,
    0x0, 0xc9, 0x93, 0x0, 0x0, 0xbd, 0xa2, 0x61,
    0xcd, 0x29, 0xf7, 0x9, 0xf5, 0x5b, 0x0, 0x0,
    0x85, 0xd4, 0x9e, 0x8d, 0x0, 0xe8, 0x62, 0xff,
    0x23, 0x0, 0x0, 0x4d, 0xfc, 0xe2, 0x4c, 0x0,
    0xaa, 0xcd, 0xeb, 0x0, 0x0, 0x0, 0x15, 0xff,
    0xfc, 0xf, 0x0, 0x6b, 0xff, 0xb3, 0x0, 0x0,
    0x0, 0x0, 0xdc, 0xcc, 0x0, 0x0, 0x2c, 0xff,
    0x7b, 0x0, 0x0,

    /* U+0058 "X" */
    0x6a, 0xff, 0x47, 0x0, 0x0, 0xad, 0xf0, 0x15,
    0x3, 0xd2, 0xce, 0x1, 0x37, 0xff, 0x72, 0x0,
    0x0, 0x41, 0xff, 0x58, 0xc0, 0xd8, 0x5, 0x0,
    0x0, 0x0, 0xac, 0xef, 0xff, 0x4a, 0x0, 0x0,
    0x0, 0x0, 0x41, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb6, 0xf3, 0xff, 0x54, 0x0, 0x0,
    0x0, 0x4d, 0xff, 0x5a, 0xc1, 0xe0, 0x8, 0x0,
    0x7, 0xdb, 0xcb, 0x1, 0x34, 0xfe, 0x7d, 0x0,
    0x79, 0xff, 0x3e, 0x0, 0x0, 0xa4, 0xf5, 0x1c,

    /* U+0059 "Y" */
    0xb4, 0xe3, 0x5, 0x0, 0x0, 0x9e, 0xef, 0xf,
    0x34, 0xff, 0x5c, 0x0, 0x17, 0xf8, 0x7e, 0x0,
    0x0, 0xb2, 0xcf, 0x0, 0x82, 0xee, 0xf, 0x0,
    0x0, 0x32, 0xff, 0x4b, 0xeb, 0x7d, 0x0, 0x0,
    0x0, 0x0, 0xb1, 0xef, 0xee, 0xe, 0x0, 0x0,
    0x0, 0x0, 0x36, 0xff, 0x82, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0x64, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x26,
    0x4c, 0x4c, 0x4c, 0x8c, 0xff, 0x5d, 0x0, 0x0,
    0x0, 0xc, 0xdf, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x96, 0xf0, 0x1b, 0x0, 0x0, 0x0, 0x40, 0xfe,
    0x63, 0x0, 0x0, 0x0, 0xb, 0xdd, 0xb9, 0x0,
    0x0, 0x0, 0x0, 0x92, 0xf2, 0x1f, 0x0, 0x0,
    0x0, 0x3c, 0xfe, 0xa4, 0x48, 0x48, 0x48, 0x3b,
    0x88, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd4,

    /* U+005B "[" */
    0x0, 0x4, 0x4, 0x0, 0x38, 0xff, 0xff, 0x2c,
    0x38, 0xff, 0x5d, 0x8, 0x38, 0xff, 0x34, 0x0,
    0x38, 0xff, 0x34, 0x0, 0x38, 0xff, 0x34, 0x0,
    0x38, 0xff, 0x34, 0x0, 0x38, 0xff, 0x34, 0x0,
    0x38, 0xff, 0x34, 0x0, 0x38, 0xff, 0x34, 0x0,
    0x38, 0xff, 0x34, 0x0, 0x38, 0xff, 0x34, 0x0,
    0x38, 0xff, 0x37, 0x0, 0x38, 0xff, 0xff, 0x2c,
    0xa, 0x30, 0x30, 0x8,

    /* U+005C "\\" */
    0xb3, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x57, 0xfb,
    0x15, 0x0, 0x0, 0x0, 0x9, 0xf2, 0x6b, 0x0,
    0x0, 0x0, 0x0, 0xa1, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x46, 0xff, 0x22, 0x0, 0x0, 0x0, 0x3,
    0xe7, 0x7d, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0x34, 0xff, 0x33, 0x0,
    0x0, 0x0, 0x0, 0xd8, 0x8e, 0x0, 0x0, 0x0,
    0x0, 0x7e, 0xe6, 0x3,

    /* U+005D "]" */
    0x3, 0x4, 0x1, 0xec, 0xff, 0x78, 0x2f, 0xf6,
    0x78, 0x0, 0xf4, 0x78, 0x0, 0xf4, 0x78, 0x0,
    0xf4, 0x78, 0x0, 0xf4, 0x78, 0x0, 0xf4, 0x78,
    0x0, 0xf4, 0x78, 0x0, 0xf4, 0x78, 0x0, 0xf4,
    0x78, 0x0, 0xf4, 0x78, 0x3, 0xf4, 0x78, 0xec,
    0xff, 0x78, 0x2c, 0x30, 0x16,

    /* U+005E "^" */
    0x0, 0x5, 0x7e, 0x14, 0x0, 0x0, 0x54, 0xff,
    0x73, 0x0, 0x0, 0xb7, 0xb2, 0xd6, 0x0, 0x1d,
    0xf7, 0x17, 0xe9, 0x3a, 0x7e, 0xb1, 0x0, 0x92,
    0x9e,

    /* U+005F "_" */
    0x3, 0x4, 0x4, 0x4, 0x4, 0x1, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0x64, 0x33, 0x34, 0x34, 0x34,
    0x34, 0x14,

    /* U+0060 "`" */
    0x53, 0xfc, 0x32, 0x0, 0x0, 0x86, 0xc2, 0x0,

    /* U+0061 "a" */
    0x0, 0x51, 0xdf, 0xf4, 0xb2, 0x12, 0x2a, 0xfd,
    0x7f, 0x45, 0xef, 0x8d, 0x13, 0x3c, 0x6, 0x0,
    0xb4, 0xb9, 0x1, 0x81, 0xdf, 0xf7, 0xfd, 0xbc,
    0x53, 0xfe, 0x3b, 0x1, 0xb0, 0xbc, 0x61, 0xff,
    0x65, 0x61, 0xee, 0xbe, 0x7, 0xa9, 0xf4, 0xc9,
    0xb8, 0xd8,

    /* U+0062 "b" */
    0x44, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x44,
    0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff,
    0x28, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xa0,
    0xed, 0xd6, 0x38, 0x0, 0x44, 0xff, 0xb4, 0x54,
    0xc8, 0xdf, 0x4, 0x44, 0xff, 0x29, 0x0, 0x41,
    0xff, 0x2e, 0x44, 0xff, 0x28, 0x0, 0x24, 0xff,
    0x45, 0x44, 0xff, 0x29, 0x0, 0x41, 0xff, 0x2f,
    0x44, 0xff, 0xb3, 0x4f, 0xc6, 0xdf, 0x3, 0x44,
    0xff, 0x89, 0xec, 0xd7, 0x39, 0x0,

    /* U+0063 "c" */
    0x0, 0x45, 0xd3, 0xf3, 0xb5, 0x18, 0x1a, 0xf6,
    0x9a, 0x4c, 0xd9, 0xaa, 0x68, 0xf9, 0x8, 0x0,
    0x42, 0x7e, 0x82, 0xe7, 0x0, 0x0, 0x0, 0x0,
    0x67, 0xf9, 0x7, 0x0, 0x2b, 0x56, 0x1c, 0xf7,
    0x98, 0x4a, 0xd1, 0xaf, 0x0, 0x4a, 0xd5, 0xf6,
    0xb2, 0xf,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x68, 0xff, 0x4, 0x0,
    0x0, 0x0, 0x0, 0x68, 0xff, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x68, 0xff, 0x4, 0x0, 0x5e, 0xe3,
    0xe1, 0xaf, 0xff, 0x4, 0x20, 0xfc, 0xa8, 0x57,
    0xd7, 0xff, 0x4, 0x6b, 0xfc, 0xf, 0x0, 0x68,
    0xff, 0x4, 0x80, 0xe9, 0x0, 0x0, 0x68, 0xff,
    0x4, 0x68, 0xf8, 0x6, 0x0, 0x68, 0xff, 0x4,
    0x1d, 0xfb, 0x87, 0x33, 0xc5, 0xff, 0x4, 0x0,
    0x5c, 0xe4, 0xe5, 0xa9, 0xff, 0x4,

    /* U+0065 "e" */
    0x0, 0x3f, 0xd2, 0xf3, 0xb2, 0x14, 0x0, 0x16,
    0xf1, 0xab, 0x4b, 0xd9, 0xa2, 0x0, 0x64, 0xfe,
    0x15, 0x0, 0x6b, 0xf1, 0x0, 0x7f, 0xff, 0xf8,
    0xf8, 0xfa, 0xff, 0xb, 0x64, 0xfc, 0x31, 0x28,
    0x28, 0x28, 0x2, 0x16, 0xf0, 0xab, 0x47, 0x7f,
    0xa3, 0x0, 0x0, 0x39, 0xca, 0xf6, 0xd2, 0x41,
    0x0,

    /* U+0066 "f" */
    0x0, 0x1f, 0xc5, 0xf6, 0x37, 0x0, 0x9c, 0xe7,
    0x51, 0x11, 0x0, 0xc1, 0xaa, 0x0, 0x0, 0xbc,
    0xff, 0xff, 0xf0, 0x0, 0x1a, 0xcc, 0xb4, 0x21,
    0x0, 0x0, 0xc4, 0xa8, 0x0, 0x0, 0x0, 0xc4,
    0xa8, 0x0, 0x0, 0x0, 0xc4, 0xa8, 0x0, 0x0,
    0x0, 0xc4, 0xa8, 0x0, 0x0, 0x0, 0xc4, 0xa8,
    0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x59, 0xe1, 0xe5, 0x99, 0xff, 0x14, 0x1d,
    0xfa, 0xab, 0x55, 0xcc, 0xff, 0x14, 0x66, 0xfd,
    0x10, 0x0, 0x5c, 0xff, 0x14, 0x7c, 0xee, 0x0,
    0x0, 0x5c, 0xff, 0x14, 0x64, 0xfc, 0xd, 0x0,
    0x5c, 0xff, 0x14, 0x1a, 0xf9, 0xa7, 0x50, 0xcb,
    0xff, 0x14, 0x0, 0x57, 0xe2, 0xe6, 0xb0, 0xff,
    0x13, 0x0, 0x11, 0x0, 0x0, 0x76, 0xfa, 0x4,
    0xa, 0xde, 0x65, 0x50, 0xe4, 0xb4, 0x0, 0x0,
    0x76, 0xe4, 0xf1, 0xae, 0x18, 0x0,

    /* U+0068 "h" */
    0x4c, 0xff, 0x24, 0x0, 0x0, 0x0, 0x4c, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x4c, 0xff, 0x24, 0x0,
    0x0, 0x0, 0x4c, 0xff, 0x8e, 0xe8, 0xe1, 0x41,
    0x4c, 0xff, 0xb1, 0x51, 0xd2, 0xca, 0x4c, 0xff,
    0x24, 0x0, 0x7c, 0xef, 0x4c, 0xff, 0x24, 0x0,
    0x78, 0xf4, 0x4c, 0xff, 0x24, 0x0, 0x78, 0xf4,
    0x4c, 0xff, 0x24, 0x0, 0x78, 0xf4, 0x4c, 0xff,
    0x24, 0x0, 0x78, 0xf4,

    /* U+0069 "i" */
    0x23, 0xe6, 0x32, 0xe, 0x8a, 0x15, 0x2c, 0xff,
    0x40, 0x2c, 0xff, 0x40, 0x2c, 0xff, 0x40, 0x2c,
    0xff, 0x40, 0x2c, 0xff, 0x40, 0x2c, 0xff, 0x40,
    0x2c, 0xff, 0x40,

    /* U+006A "j" */
    0x0, 0x30, 0xe6, 0x25, 0x0, 0x15, 0x8a, 0xe,
    0x0, 0x34, 0xff, 0x38, 0x0, 0x34, 0xff, 0x38,
    0x0, 0x34, 0xff, 0x38, 0x0, 0x34, 0xff, 0x38,
    0x0, 0x34, 0xff, 0x38, 0x0, 0x34, 0xff, 0x38,
    0x0, 0x34, 0xff, 0x38, 0x0, 0x34, 0xff, 0x36,
    0x23, 0x95, 0xff, 0x1c, 0x67, 0xf3, 0x8b, 0x0,

    /* U+006B "k" */
    0x44, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x44,
    0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff,
    0x28, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0x28,
    0x1f, 0xeb, 0xa7, 0x0, 0x44, 0xff, 0x32, 0xcf,
    0xcf, 0xa, 0x0, 0x44, 0xff, 0xc4, 0xec, 0x1f,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xf3, 0x1e, 0x0,
    0x0, 0x44, 0xff, 0x6d, 0xdd, 0xb5, 0x0, 0x0,
    0x44, 0xff, 0x28, 0x46, 0xff, 0x5a, 0x0, 0x44,
    0xff, 0x28, 0x0, 0xa4, 0xea, 0x13,

    /* U+006C "l" */
    0x2c, 0xff, 0x40, 0x2c, 0xff, 0x40, 0x2c, 0xff,
    0x40, 0x2c, 0xff, 0x40, 0x2c, 0xff, 0x40, 0x2c,
    0xff, 0x40, 0x2c, 0xff, 0x40, 0x2c, 0xff, 0x40,
    0x2c, 0xff, 0x40, 0x2c, 0xff, 0x40,

    /* U+006D "m" */
    0x44, 0xff, 0x8a, 0xe9, 0xe6, 0x50, 0xae, 0xf7,
    0xca, 0x1e, 0x44, 0xff, 0xa7, 0x51, 0xd8, 0xff,
    0x89, 0x59, 0xf2, 0x8d, 0x44, 0xff, 0x28, 0x0,
    0x84, 0xf0, 0x1, 0x0, 0xbc, 0xb0, 0x44, 0xff,
    0x28, 0x0, 0x80, 0xec, 0x0, 0x0, 0xb8, 0xb4,
    0x44, 0xff, 0x28, 0x0, 0x80, 0xed, 0x0, 0x0,
    0xb8, 0xb4, 0x44, 0xff, 0x28, 0x0, 0x80, 0xee,
    0x0, 0x0, 0xb8, 0xb4, 0x44, 0xff, 0x28, 0x0,
    0x80, 0xef, 0x0, 0x0, 0xb8, 0xb4,

    /* U+006E "n" */
    0x4c, 0xff, 0x7c, 0xe6, 0xe3, 0x45, 0x4c, 0xff,
    0xb2, 0x51, 0xd1, 0xcc, 0x4c, 0xff, 0x25, 0x0,
    0x7c, 0xf0, 0x4c, 0xff, 0x24, 0x0, 0x78, 0xf4,
    0x4c, 0xff, 0x24, 0x0, 0x78, 0xf4, 0x4c, 0xff,
    0x24, 0x0, 0x78, 0xf4, 0x4c, 0xff, 0x24, 0x0,
    0x78, 0xf4,

    /* U+006F "o" */
    0x0, 0x46, 0xd4, 0xf4, 0xbe, 0x26, 0x0, 0x1b,
    0xf5, 0xa9, 0x4b, 0xc8, 0xd6, 0x5, 0x6a, 0xfc,
    0xf, 0x0, 0x38, 0xff, 0x38, 0x81, 0xea, 0x0,
    0x0, 0x17, 0xff, 0x58, 0x64, 0xfb, 0xd, 0x0,
    0x38, 0xff, 0x3e, 0x15, 0xf2, 0xa7, 0x4a, 0xc5,
    0xde, 0x5, 0x0, 0x3d, 0xce, 0xf6, 0xc6, 0x2b,
    0x0,

    /* U+0070 "p" */
    0x44, 0xff, 0xa1, 0xef, 0xd5, 0x38, 0x0, 0x44,
    0xff, 0x92, 0x34, 0xbf, 0xde, 0x3, 0x44, 0xff,
    0x28, 0x0, 0x42, 0xff, 0x2b, 0x44, 0xff, 0x28,
    0x0, 0x2a, 0xff, 0x41, 0x44, 0xff, 0x28, 0x0,
    0x4b, 0xff, 0x2b, 0x44, 0xff, 0xa7, 0x4c, 0xce,
    0xdc, 0x2, 0x44, 0xff, 0xa3, 0xed, 0xd6, 0x37,
    0x0, 0x44, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x44,
    0xff, 0x28, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x60, 0xe4, 0xe4, 0x9e, 0xff, 0x4, 0x22,
    0xfc, 0xa3, 0x4d, 0xcc, 0xff, 0x4, 0x6b, 0xfb,
    0xd, 0x0, 0x68, 0xff, 0x4, 0x80, 0xea, 0x0,
    0x0, 0x68, 0xff, 0x4, 0x69, 0xfa, 0xb, 0x0,
    0x68, 0xff, 0x4, 0x1f, 0xfc, 0xa0, 0x4e, 0xcf,
    0xff, 0x4, 0x0, 0x60, 0xe4, 0xe4, 0xb4, 0xff,
    0x4, 0x0, 0x0, 0x0, 0x0, 0x68, 0xff, 0x4,
    0x0, 0x0, 0x0, 0x0, 0x68, 0xff, 0x4, 0x0,
    0x0, 0x0, 0x0, 0x68, 0xff, 0x4,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x2, 0x0, 0x44, 0xff, 0xa6,
    0xf6, 0xe, 0x44, 0xff, 0xc2, 0x66, 0x5, 0x44,
    0xff, 0x2b, 0x0, 0x0, 0x44, 0xff, 0x28, 0x0,
    0x0, 0x44, 0xff, 0x28, 0x0, 0x0, 0x44, 0xff,
    0x28, 0x0, 0x0, 0x44, 0xff, 0x28, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x77, 0xe8, 0xef, 0xa1, 0x9, 0x37, 0xff,
    0x68, 0x46, 0xf3, 0x7b, 0x3e, 0xff, 0x6d, 0xf,
    0x1f, 0x18, 0x0, 0x74, 0xe2, 0xfb, 0xae, 0x11,
    0x25, 0x3a, 0x1, 0x36, 0xe5, 0x90, 0x5f, 0xfa,
    0x54, 0x38, 0xe2, 0x8f, 0x1, 0x87, 0xeb, 0xf3,
    0xaf, 0x12,

    /* U+0074 "t" */
    0x0, 0x74, 0x42, 0x0, 0x0, 0xe8, 0x84, 0x0,
    0xf4, 0xff, 0xff, 0xa4, 0x22, 0xeb, 0x95, 0x17,
    0x0, 0xe8, 0x84, 0x0, 0x0, 0xe8, 0x84, 0x0,
    0x0, 0xe8, 0x84, 0x0, 0x0, 0xd9, 0xc0, 0x35,
    0x0, 0x68, 0xf3, 0x9c,

    /* U+0075 "u" */
    0x4c, 0xff, 0x20, 0x0, 0x78, 0xf4, 0x4c, 0xff,
    0x20, 0x0, 0x78, 0xf4, 0x4c, 0xff, 0x20, 0x0,
    0x78, 0xf4, 0x4c, 0xff, 0x20, 0x0, 0x78, 0xf4,
    0x47, 0xff, 0x24, 0x0, 0x78, 0xf4, 0x22, 0xff,
    0x92, 0x57, 0xdf, 0xf4, 0x0, 0x82, 0xf0, 0xda,
    0xa8, 0xf4,

    /* U+0076 "v" */
    0xb8, 0xbe, 0x0, 0x0, 0xd3, 0x9f, 0x66, 0xf9,
    0xb, 0x19, 0xff, 0x4e, 0x17, 0xfd, 0x4c, 0x5f,
    0xf5, 0x9, 0x0, 0xc4, 0x93, 0xa5, 0xad, 0x0,
    0x0, 0x73, 0xda, 0xe9, 0x5d, 0x0, 0x0, 0x22,
    0xff, 0xfb, 0x11, 0x0, 0x0, 0x0, 0xd1, 0xbc,
    0x0, 0x0,

    /* U+0077 "w" */
    0xb1, 0xaf, 0x0, 0x3b, 0xff, 0x1f, 0x0, 0xcc,
    0x93, 0x71, 0xe6, 0x0, 0x80, 0xff, 0x65, 0x8,
    0xfa, 0x54, 0x32, 0xff, 0x1d, 0xc6, 0xd0, 0xac,
    0x39, 0xff, 0x16, 0x2, 0xef, 0x63, 0xf9, 0x45,
    0xef, 0x73, 0xd5, 0x0, 0x0, 0xb3, 0xd9, 0xca,
    0x1, 0xe7, 0xdc, 0x96, 0x0, 0x0, 0x73, 0xff,
    0x82, 0x0, 0xa1, 0xff, 0x56, 0x0, 0x0, 0x34,
    0xff, 0x3b, 0x0, 0x58, 0xff, 0x18, 0x0,

    /* U+0078 "x" */
    0x79, 0xf8, 0x1d, 0x18, 0xf4, 0x83, 0x8, 0xe0,
    0x9a, 0x97, 0xe7, 0xc, 0x0, 0x59, 0xfb, 0xfb,
    0x64, 0x0, 0x0, 0x4, 0xef, 0xf7, 0x9, 0x0,
    0x0, 0x67, 0xf8, 0xf2, 0x72, 0x0, 0xe, 0xe9,
    0x8f, 0x80, 0xef, 0x13, 0x88, 0xf2, 0x14, 0xd,
    0xea, 0x93,

    /* U+0079 "y" */
    0xc5, 0xc0, 0x0, 0x0, 0xe6, 0x98, 0x75, 0xfb,
    0xf, 0x2e, 0xff, 0x49, 0x24, 0xff, 0x54, 0x74,
    0xf3, 0x7, 0x0, 0xd5, 0x9e, 0xbb, 0xac, 0x0,
    0x0, 0x86, 0xe9, 0xf7, 0x5e, 0x0, 0x0, 0x36,
    0xff, 0xfc, 0x13, 0x0, 0x0, 0x1, 0xe4, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0xd5, 0x72, 0x0, 0x0,
    0x20, 0x80, 0xfc, 0x1e, 0x0, 0x0, 0x63, 0xf2,
    0x6e, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x74, 0xff, 0xff, 0xff, 0xff, 0x7f, 0x1d, 0x40,
    0x40, 0xaa, 0xf9, 0x2d, 0x0, 0x0, 0x2c, 0xf9,
    0x78, 0x0, 0x0, 0x5, 0xce, 0xc6, 0x3, 0x0,
    0x0, 0x82, 0xf6, 0x26, 0x0, 0x0, 0x34, 0xfb,
    0x9f, 0x3c, 0x3c, 0x25, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xa0,

    /* U+007B "{" */
    0x0, 0x0, 0x20, 0x59, 0x0, 0x17, 0xeb, 0x77,
    0x0, 0x6e, 0xe6, 0x0, 0x0, 0x8b, 0xcc, 0x0,
    0x0, 0x8e, 0xc9, 0x0, 0xa, 0xc8, 0xa0, 0x0,
    0xab, 0xf6, 0x21, 0x0, 0x27, 0xd9, 0x8d, 0x0,
    0x0, 0x90, 0xc7, 0x0, 0x0, 0x8b, 0xcc, 0x0,
    0x0, 0x77, 0xde, 0x0, 0x0, 0x24, 0xf8, 0x58,
    0x0, 0x0, 0x3d, 0x79,

    /* U+007C "|" */
    0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc,
    0xfc, 0xfc, 0xf4,

    /* U+007D "}" */
    0x5a, 0x21, 0x0, 0x0, 0x76, 0xe9, 0x16, 0x0,
    0x0, 0xe3, 0x6e, 0x0, 0x0, 0xc8, 0x8a, 0x0,
    0x0, 0xc5, 0x8e, 0x0, 0x0, 0x9c, 0xc7, 0xa,
    0x0, 0x1d, 0xf1, 0xaf, 0x0, 0x88, 0xd9, 0x28,
    0x0, 0xc3, 0x90, 0x0, 0x0, 0xc8, 0x8b, 0x0,
    0x0, 0xdb, 0x76, 0x0, 0x56, 0xf9, 0x25, 0x0,
    0x7a, 0x3e, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x9, 0x3,
    0x0, 0x9a, 0xf7, 0xda, 0x5b, 0x27, 0xf5, 0x35,
    0x33, 0xf3, 0xc, 0x57, 0xf0, 0xff, 0xb9, 0x1,
    0x9, 0x1a, 0x0, 0x0, 0x10, 0x2d, 0x3, 0x0,

    /* U+00B0 "°" */
    0x4, 0xa8, 0xdd, 0x50, 0x32, 0xb8, 0x2a, 0xc2,
    0x5, 0xab, 0xdf, 0x53
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 48, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 51, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 27, .adv_w = 62, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 43, .adv_w = 117, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 115, .adv_w = 109, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 199, .adv_w = 141, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 280, .adv_w = 123, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 352, .adv_w = 32, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 360, .adv_w = 67, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 416, .adv_w = 68, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 472, .adv_w = 85, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 508, .adv_w = 107, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 557, .adv_w = 42, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 569, .adv_w = 63, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 581, .adv_w = 54, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 587, .adv_w = 76, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 637, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 700, .adv_w = 109, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 745, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 808, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 871, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 934, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 997, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1060, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1123, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1186, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1249, .adv_w = 51, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1270, .adv_w = 46, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1300, .adv_w = 98, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1336, .adv_w = 107, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1372, .adv_w = 100, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1408, .adv_w = 93, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1462, .adv_w = 172, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1594, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1666, .adv_w = 121, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1729, .adv_w = 125, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1801, .adv_w = 125, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1873, .adv_w = 109, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1936, .adv_w = 105, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1999, .adv_w = 131, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2071, .adv_w = 136, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2143, .adv_w = 54, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2170, .adv_w = 107, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2224, .adv_w = 121, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2296, .adv_w = 104, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2359, .adv_w = 168, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2449, .adv_w = 136, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2521, .adv_w = 133, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2593, .adv_w = 123, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2665, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2753, .adv_w = 120, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2825, .adv_w = 116, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2888, .adv_w = 117, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2960, .adv_w = 125, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3032, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3104, .adv_w = 169, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3203, .adv_w = 122, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3275, .adv_w = 117, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3347, .adv_w = 116, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3410, .adv_w = 53, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3470, .adv_w = 80, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3530, .adv_w = 53, .box_w = 3, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3575, .adv_w = 82, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 3600, .adv_w = 87, .box_w = 6, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3618, .adv_w = 62, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 3626, .adv_w = 104, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3668, .adv_w = 108, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3738, .adv_w = 101, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3780, .adv_w = 108, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3850, .adv_w = 103, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3899, .adv_w = 68, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3949, .adv_w = 109, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4019, .adv_w = 107, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4079, .adv_w = 49, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4106, .adv_w = 48, .box_w = 4, .box_h = 12, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 4154, .adv_w = 100, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4224, .adv_w = 49, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4254, .adv_w = 167, .box_w = 10, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4324, .adv_w = 107, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4366, .adv_w = 109, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4415, .adv_w = 108, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4485, .adv_w = 109, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4555, .adv_w = 68, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4595, .adv_w = 99, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4637, .adv_w = 64, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4673, .adv_w = 107, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4715, .adv_w = 95, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4757, .adv_w = 143, .box_w = 9, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4820, .adv_w = 97, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4862, .adv_w = 93, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4922, .adv_w = 97, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4964, .adv_w = 64, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5016, .adv_w = 48, .box_w = 1, .box_h = 11, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5027, .adv_w = 64, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5079, .adv_w = 128, .box_w = 8, .box_h = 4, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 5111, .adv_w = 73, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 6}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 1, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    1, 53,
    3, 3,
    3, 8,
    3, 34,
    3, 66,
    3, 68,
    3, 69,
    3, 70,
    3, 72,
    3, 78,
    3, 79,
    3, 80,
    3, 81,
    3, 82,
    3, 84,
    3, 88,
    8, 3,
    8, 8,
    8, 34,
    8, 66,
    8, 68,
    8, 69,
    8, 70,
    8, 72,
    8, 78,
    8, 79,
    8, 80,
    8, 81,
    8, 82,
    8, 84,
    8, 88,
    9, 55,
    9, 56,
    9, 58,
    13, 3,
    13, 8,
    15, 3,
    15, 8,
    16, 16,
    34, 3,
    34, 8,
    34, 32,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 78,
    34, 79,
    34, 80,
    34, 81,
    34, 85,
    34, 86,
    34, 87,
    34, 88,
    34, 90,
    34, 91,
    35, 53,
    35, 55,
    35, 58,
    36, 10,
    36, 53,
    36, 62,
    36, 94,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 57,
    37, 58,
    37, 59,
    38, 53,
    38, 68,
    38, 69,
    38, 70,
    38, 71,
    38, 72,
    38, 80,
    38, 82,
    38, 86,
    38, 87,
    38, 88,
    38, 90,
    39, 13,
    39, 15,
    39, 34,
    39, 43,
    39, 53,
    39, 66,
    39, 68,
    39, 69,
    39, 70,
    39, 72,
    39, 80,
    39, 82,
    39, 83,
    39, 86,
    39, 87,
    39, 90,
    41, 34,
    41, 53,
    41, 57,
    41, 58,
    42, 34,
    42, 53,
    42, 57,
    42, 58,
    43, 34,
    44, 14,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 80,
    44, 82,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 86,
    45, 87,
    45, 88,
    45, 90,
    46, 34,
    46, 53,
    46, 57,
    46, 58,
    47, 34,
    47, 53,
    47, 57,
    47, 58,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 57,
    48, 58,
    48, 59,
    49, 13,
    49, 15,
    49, 34,
    49, 43,
    49, 57,
    49, 59,
    49, 66,
    49, 68,
    49, 69,
    49, 70,
    49, 72,
    49, 80,
    49, 82,
    49, 85,
    49, 87,
    49, 90,
    50, 53,
    50, 55,
    50, 56,
    50, 58,
    51, 53,
    51, 55,
    51, 58,
    53, 1,
    53, 13,
    53, 14,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 43,
    53, 48,
    53, 50,
    53, 52,
    53, 53,
    53, 55,
    53, 56,
    53, 58,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    55, 10,
    55, 13,
    55, 14,
    55, 15,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 62,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 80,
    55, 82,
    55, 83,
    55, 86,
    55, 87,
    55, 90,
    55, 94,
    56, 10,
    56, 13,
    56, 14,
    56, 15,
    56, 34,
    56, 53,
    56, 62,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 83,
    56, 86,
    56, 94,
    57, 14,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 55,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 80,
    57, 82,
    57, 86,
    57, 87,
    57, 90,
    58, 7,
    58, 10,
    58, 11,
    58, 13,
    58, 14,
    58, 15,
    58, 34,
    58, 36,
    58, 40,
    58, 43,
    58, 48,
    58, 50,
    58, 52,
    58, 53,
    58, 54,
    58, 55,
    58, 56,
    58, 57,
    58, 58,
    58, 62,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    58, 94,
    59, 34,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    59, 68,
    59, 69,
    59, 70,
    59, 72,
    59, 80,
    59, 82,
    59, 86,
    59, 87,
    59, 88,
    59, 90,
    60, 43,
    60, 54,
    66, 3,
    66, 8,
    66, 87,
    66, 90,
    67, 3,
    67, 8,
    67, 87,
    67, 89,
    67, 90,
    67, 91,
    68, 3,
    68, 8,
    70, 3,
    70, 8,
    70, 87,
    70, 90,
    71, 3,
    71, 8,
    71, 10,
    71, 62,
    71, 68,
    71, 69,
    71, 70,
    71, 72,
    71, 82,
    71, 94,
    73, 3,
    73, 8,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 82,
    78, 3,
    78, 8,
    79, 3,
    79, 8,
    80, 3,
    80, 8,
    80, 87,
    80, 89,
    80, 90,
    80, 91,
    81, 3,
    81, 8,
    81, 87,
    81, 89,
    81, 90,
    81, 91,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 72,
    83, 80,
    83, 82,
    83, 85,
    83, 87,
    83, 88,
    83, 90,
    85, 80,
    87, 3,
    87, 8,
    87, 13,
    87, 15,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 71,
    87, 72,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 80,
    89, 82,
    90, 3,
    90, 8,
    90, 13,
    90, 15,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 71,
    90, 72,
    90, 80,
    90, 82,
    91, 68,
    91, 69,
    91, 70,
    91, 72,
    91, 80,
    91, 82,
    92, 43,
    92, 54
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -6, -3, -3, -11, -5, -6, -6, -6,
    -6, -2, -2, -9, -2, -6, -9, 1,
    -3, -3, -11, -5, -6, -6, -6, -6,
    -2, -2, -9, -2, -6, -9, 1, 2,
    4, 2, -27, -27, -27, -27, -23, -11,
    -11, -8, -2, -2, -2, -2, -11, -2,
    -7, -4, -14, -4, -4, -1, -4, -2,
    -1, -5, -3, -5, 1, -3, -2, -5,
    -2, -3, -1, -2, -11, -11, -2, -8,
    -2, -2, -4, -2, 2, -2, -2, -2,
    -2, -2, -2, -2, -2, -2, -2, -2,
    -26, -26, -18, -19, 2, -3, -2, -2,
    -2, -2, -2, -2, -2, -2, -2, -2,
    2, -3, 2, -3, 2, -3, 2, -3,
    -2, -15, -3, -3, -3, -3, -2, -2,
    -2, -2, -3, -2, -2, -4, -6, -4,
    -27, -27, 2, -6, -6, -6, -6, -19,
    -2, -19, -9, -26, -1, -12, -5, -12,
    2, -3, 2, -3, 2, -3, 2, -3,
    -11, -11, -2, -8, -2, -2, -4, -2,
    -38, -38, -17, -17, -5, -3, -1, -1,
    -1, -1, -1, -1, -1, 1, 1, 1,
    -3, -3, -2, -3, -5, -2, -4, -6,
    -24, -25, -24, -11, -3, -3, -20, -3,
    -3, -1, 2, 2, 1, 2, -16, -8,
    -8, -8, -8, -8, -8, -19, -8, -8,
    -6, -7, -6, -8, -4, -7, -8, -6,
    -2, 2, -20, -15, -20, -7, -1, -1,
    -1, -1, 2, -4, -4, -4, -4, -4,
    -4, -4, -3, -3, -1, -1, 2, 1,
    -13, -6, -13, -4, 1, 1, -3, -3,
    -3, -3, -3, -3, -3, -2, -2, 1,
    -15, -2, -2, -2, -2, 1, -2, -2,
    -2, -2, -2, -2, -2, -3, -3, -3,
    2, -5, -22, -14, -22, -14, -3, -3,
    -9, -3, -3, -1, 2, -9, 2, 2,
    1, 2, 2, -6, -6, -6, -6, -2,
    -6, -4, -4, -6, -4, -6, -4, -5,
    -2, -4, -2, -2, -2, -3, 2, 1,
    -2, -2, -2, -2, -2, -2, -2, -2,
    -2, -2, -2, -3, -3, -3, -2, -2,
    -2, -2, -1, -1, -3, -3, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1,
    2, 2, 2, 2, -2, -2, -2, -2,
    -2, 2, -7, -7, -2, -2, -2, -2,
    -2, -7, -7, -7, -7, -8, -8, -1,
    -2, -1, -1, -3, -3, -1, -1, -1,
    -1, 2, 2, -16, -16, -3, -2, -2,
    -2, 2, -2, -3, -2, 5, 2, 2,
    2, -3, 1, 1, -16, -16, -1, -1,
    -1, -1, 1, -1, -1, -1, -12, -12,
    -2, -2, -2, -2, -4, -2, 1, 1,
    -16, -16, -1, -1, -1, -1, 1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1,
    -2, -2
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 434,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 8,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_lv_demo_high_res_roboto_medium_12 = {
#else
lv_font_t font_lv_demo_high_res_roboto_medium_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_USE_DEMO_HIGH_RES*/

