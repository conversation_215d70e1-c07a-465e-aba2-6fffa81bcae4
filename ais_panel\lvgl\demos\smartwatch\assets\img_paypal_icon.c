#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: paypal.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_paypal_icon_data[] = {
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x51,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x51,0x01,0x90,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,
0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x51,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x51,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x32,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,
0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x10,0x00,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x30,0x01,0xEE,0x00,0xCD,0x00,
0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCE,0x00,0xEE,0x00,0x0F,0x01,0x50,0x01,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x51,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0x2F,0x01,0xD5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x51,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x30,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0x12,0x02,0xD8,0x03,0xD6,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x0F,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,
0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0x97,0x03,0x39,0x04,0x77,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x0E,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xEE,0x00,0x39,0x04,0x39,0x04,0x19,0x04,0x17,0x02,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x15,0x00,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xEE,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xB4,0x02,0x39,0x04,0x39,0x04,0x39,0x04,0xF5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x51,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,
0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xD1,0x01,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x16,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0x2F,0x01,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0xF4,0x02,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x30,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xB1,0x01,0xF8,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0xF8,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x0F,0x01,0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,
0xCD,0x00,0xCD,0x00,0xCD,0x00,0xCD,0x00,0x0E,0x01,0x56,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x97,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xEE,0x00,0xCD,0x00,0xEE,0x00,0xEE,0x00,0xEE,0x00,0x2E,0x01,0xB0,0x01,0x53,0x02,0x36,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0xF5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x15,0x00,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x12,0x02,0x19,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x56,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x31,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xF8,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,
0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0xD8,0x03,0xB5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x71,0x01,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x77,0x03,0xF5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xF2,0x01,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x19,0x04,0x16,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x74,0x02,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,
0x39,0x04,0x39,0x04,0xF8,0x03,0xB7,0x03,0x77,0x03,0x36,0x03,0xF5,0x02,0xF5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xF5,0x02,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x16,0x03,0x17,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0x10,0x02,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x77,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x97,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x71,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0xD8,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,
0x36,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x71,0x01,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0xF5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0x71,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x13,0x02,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0xD5,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x01,0x51,0x01,0x50,0x01,0x50,0x01,0x50,0x01,0x51,0x01,0x16,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x19,0x04,
0x17,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x56,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0xB8,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x39,0x04,0x77,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x16,0x03,0xF8,0x03,0x39,0x04,0x39,0x04,0x39,0x04,0xF8,0x03,0xF5,0x02,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0xF8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0xB2,0x78,0x0A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD9,0x37,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x30,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x78,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD2,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x02,0xF6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x24,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x35,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x7A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF7,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x04,0x00,0x00,
0x00,0x00,0x00,0x00,0x03,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x59,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x84,0x00,0x00,0x00,0x00,0x00,0x00,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x37,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x6E,0x00,0x00,0x00,
0x00,0x00,0x00,0x03,0xF8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x27,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x12,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x52,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE7,0x2C,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x7D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x9B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0xF3,0xDF,0xAF,0x6B,0x16,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x04,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xED,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x29,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBB,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x86,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x16,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xDE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x11,0x4F,0x50,0x50,0x50,0x3E,0xA2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xDC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x87,0xFE,0xFF,0xFF,0xFF,0xFD,0x64,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_paypal_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_paypal_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_paypal_icon_data};

