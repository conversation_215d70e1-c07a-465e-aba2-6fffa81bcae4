
#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: drive.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_drive_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x13,0x74,0x00,0x00,0x14,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x33,0x64,
0x34,0x6C,0x34,0x6C,0x91,0x53,0xB1,0x5B,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x54,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xB1,0x5B,0x90,0x53,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x0E,0x4B,0xB1,0x5B,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x54,0x74,0xB9,0x9D,0xBC,0xC6,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0x1D,0xCF,0x3A,0xAE,0xD6,0x84,
0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xB1,0x5B,0x0E,0x4B,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x78,0x95,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7B,0xB6,0x74,0x74,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x1A,0xAE,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x1D,0xCF,0x95,0x7C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x33,0x6C,0xED,0x42,0xCD,0x42,0xCD,0x42,0xCD,0x42,0xCD,0x42,0xF2,0x63,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xB9,0xA5,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,
0x7F,0xDF,0x7F,0xDF,0xFD,0xCE,0x54,0x74,0x34,0x6C,0x34,0x6C,0x13,0x64,0xAD,0x3A,0xAD,0x3A,0xAD,0x3A,0xAD,0x3A,0xAD,0x3A,0xD2,0x5B,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xB5,0x7C,0x7E,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0xFA,0xAD,0x34,0x6C,0x34,0x6C,0x13,0x64,0xAD,0x3A,0xAD,0x3A,0xAD,0x3A,0xAD,0x3A,0xAD,0x3A,0xD2,0x5B,0x34,0x6C,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x34,0x6C,0x5B,0xB6,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x75,0x74,0x34,0x6C,0x33,0x6C,0x0E,0x4B,0x0E,0x43,0x0E,0x43,0x0E,0x43,0x0E,0x4B,0xF3,0x63,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x54,0x6C,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x3E,0xCF,0xBC,0xAE,0x9C,0xAE,0x9C,0xAE,0xFD,0xBE,0x7F,0xDF,0x7F,0xDF,
0x7F,0xDF,0x18,0xEF,0xD3,0xF6,0x7E,0xDF,0x98,0x9D,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xD6,0x84,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7E,0xD7,0xBC,0xAE,0x9C,0xAE,0x9C,0xAE,0x9C,0xAE,0x9C,0xAE,0x1D,0xC7,0x7F,0xDF,0x7F,0xDF,0x6D,0xFE,0x4B,0xFE,0x8F,0xFE,0x59,0xB6,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x57,0x8D,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x3E,0xCF,0x9C,0xAE,0x7C,0xA6,0x1B,0x96,0x3B,0x9E,0x9C,0xAE,0xBC,0xB6,0x7F,0xDF,0x7F,0xDF,0x18,0xEF,0x4B,0xFE,0x4B,0xFE,0x4D,0xF6,0x72,0x7C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x57,0x8D,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x3E,0xCF,0x9C,0xAE,0x7C,0xA6,0x1B,0x96,0x3B,0x9E,0x9C,0xAE,0xBC,0xB6,0x7F,0xDF,
0x7F,0xDF,0x7F,0xDF,0x39,0xE7,0x4C,0xFE,0x4B,0xFE,0x0C,0xF6,0x92,0x84,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xF6,0x84,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7E,0xD7,0xBC,0xAE,0x9C,0xAE,0x9C,0xAE,0x9C,0xAE,0x9C,0xAE,0x1D,0xC7,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x3A,0xE7,0x4C,0xFE,0x4B,0xFE,0x2C,0xF6,0xB2,0x8C,0x34,0x6C,0x34,0x6C,0x57,0x95,0x1E,0xCF,0x7B,0xBE,0x54,0x6C,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x54,0x6C,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x5E,0xCF,0xBC,0xB6,0x9C,0xAE,0x9C,0xAE,0xFD,0xBE,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0xB6,0xAD,0x0C,0xF6,0x4B,0xFE,0x2B,0xFE,0xCF,0xA4,0x53,0x74,0xFD,0xCE,0x7F,0xDF,0x7F,0xDF,0x17,0x8D,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x5B,0xB6,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,
0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x75,0x74,0x72,0x7C,0x0C,0xEE,0x69,0xE5,0x68,0xED,0x4A,0xD5,0x5E,0xDF,0x7F,0xDF,0x7F,0xDF,0x57,0x8D,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xD6,0x7C,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x1A,0xAE,0x34,0x6C,0x34,0x6C,0x72,0x7C,0x69,0xE5,0x68,0xED,0x68,0xED,0x5D,0xDF,0x7F,0xDF,0x7F,0xDF,0xF6,0x84,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xD9,0xA5,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0xFD,0xCE,0x54,0x74,0x34,0x6C,0x34,0x6C,0x54,0x74,0x31,0xAD,0xAC,0xE5,0x95,0xE6,0x7F,0xDF,0x7F,0xDF,0x5E,0xD7,0x54,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x3A,0xAE,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,
0x7F,0xDF,0x1D,0xCF,0xB5,0x7C,0x34,0x6C,0x34,0x6C,0x37,0x8D,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0xF9,0xA5,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x98,0x9D,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7B,0xBE,0x75,0x74,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x5B,0xB6,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x9B,0xBE,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x0E,0x4B,0xB1,0x5B,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x74,0x74,0xD9,0xA5,0xDC,0xC6,0x7F,0xDF,0x7F,0xDF,0x7F,0xDF,0x3E,0xCF,0x3A,0xB6,0xF6,0x84,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x17,0x8D,0x5E,0xD7,0x7F,0xDF,0x7F,0xDF,0xFD,0xC6,0x98,0x9D,0x34,0x6C,0xB1,0x5B,0x0E,0x4B,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x91,0x53,0xB1,0x5B,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x74,0x74,0x54,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,
0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x74,0x74,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0xB1,0x5B,0x90,0x53,0x34,0x6C,0x34,0x6C,0x14,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x33,0x64,
0x00,0x00,0x13,0x74,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x34,0x6C,0x53,0x64,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x11,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x10,0x00,0x2B,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF1,0x2A,
0x73,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x72,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,
0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,
0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,
0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,
0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,
0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,
0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x77,0x73,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x72,0x2B,0xF1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF1,0x2A,
0x00,0x10,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_drive_icon = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.w = 32,
    .header.h = 32,
    .data_size = sizeof(img_drive_icon_data),
    .header.cf = LV_COLOR_FORMAT_RGB565A8,
    .data = img_drive_icon_data
};

