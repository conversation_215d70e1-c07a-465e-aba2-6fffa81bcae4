/****************************************************************************
 *
 * ftsmooth.h
 *
 *   Anti-aliasing renderer interface (specification).
 *
 * Copyright (C) 1996-2024 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#ifndef FTSMOOTH_H_
#define FTSMOOTH_H_


#include <freetype/ftrender.h>


FT_BEGIN_HEADER


  FT_DECLARE_RENDERER( ft_smooth_renderer_class )


FT_END_HEADER

#endif /* FTSMOOTH_H_ */


/* END */
