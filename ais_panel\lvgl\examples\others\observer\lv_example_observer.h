/**
 * @file lv_example_observer.h
 *
 */

#ifndef LV_EXAMPLE_OBSERVER_H
#define LV_EXAMPLE_OBSERVER_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_observer_1(void);
void lv_example_observer_2(void);
void lv_example_observer_3(void);
void lv_example_observer_4(void);
void lv_example_observer_5(void);
void lv_example_observer_6(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_OBSERVER_H*/
