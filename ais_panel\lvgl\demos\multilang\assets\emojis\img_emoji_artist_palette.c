#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_ARTIST_PALETTE
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_ARTIST_PALETTE
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_ARTIST_PALETTE uint8_t
img_emoji_artist_palette_map[] = {
    0xfe, 0xfa, 0xff, 0xff, 0xf0, 0xf4, 0xf9, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xf0, 0xfa, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfc, 0xf3, 0xe9, 0xff, 0xef, 0xff, 0xed, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xe4, 0xf7, 0xfc, 0xff, 0xcd, 0xe6, 0xf0, 0xff, 0xcb, 0xe5, 0xf3, 0xff, 0xe6, 0xf6, 0xff, 0xff, 0xfb, 0xfa, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xf0, 0xfe, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xfd, 0xfc, 0xff, 0xff, 0xf0, 0xff, 0xec, 0xff, 0xf3, 0xff, 0xea, 0xff, 0xfa, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xf2, 0xf6, 0xf7, 0xff, 0xb2, 0xd8, 0xea, 0xff, 0x82, 0xca, 0xf4, 0xff, 0x85, 0xaa, 0xee, 0xff, 0x88, 0xb4, 0xf3, 0xff, 0x89, 0xc3, 0xf7, 0xff, 0x82, 0xc7, 0xef, 0xff, 0x78, 0xbe, 0xdc, 0xff, 0x82, 0xbe, 0xd6, 0xff, 0xa5, 0xd4, 0xe9, 0xff, 0xc7, 0xed, 0xff, 0xff, 0xf2, 0xf8, 0xfd, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xf3, 0xf9, 0xff, 0xff, 0xff, 0xeb, 0xff, 0xff, 0xff, 0xee, 0xff, 0xf7, 0xf1, 0xff, 0xff, 0xb7, 0xce, 0xee, 0xff, 0x88, 0xc1, 0xda, 0xff, 0x7a, 0xb1, 0xf4, 0xff, 0x5b, 0x76, 0xfc, 0xff, 0x32, 0x50, 0xe4, 0xff, 0x35, 0x62, 0xe2, 0xff, 0x58, 0x9a, 0xff, 0xff, 0x76, 0xcb, 0xff, 0xff, 0x6d, 0xce, 0xff, 0xff, 0x64, 0xc6, 0xee, 0xff, 0x69, 0xc6, 0xed, 0xff, 0x69, 0xc1, 0xe9, 0xff, 0xd5, 0xe2, 0xf2, 0xff, 0xf6, 0xf9, 0xfe, 0xff,
    0xff, 0xfd, 0xfc, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xe1, 0xdf, 0xfc, 0xff, 0xa5, 0xb6, 0xff, 0xff, 0x68, 0xbc, 0xff, 0xff, 0x4f, 0xd0, 0xff, 0xff, 0x6a, 0xb4, 0xff, 0xff, 0x40, 0x2a, 0xd6, 0xff, 0x3e, 0x3b, 0xeb, 0xff, 0x2a, 0x37, 0xcf, 0xff, 0x46, 0x6d, 0xe1, 0xff, 0x6f, 0xae, 0xff, 0xff, 0x6f, 0xc1, 0xfb, 0xff, 0x70, 0xc8, 0xfe, 0xff, 0x72, 0xca, 0xff, 0xff, 0x60, 0xb7, 0xf9, 0xff, 0x95, 0xc2, 0xed, 0xff, 0xbc, 0xde, 0xfc, 0xff,
    0xf3, 0xff, 0xff, 0xff, 0xb4, 0xf4, 0xf8, 0xff, 0x84, 0xd7, 0xf3, 0xff, 0x80, 0xbb, 0xf3, 0xff, 0x83, 0xcb, 0xed, 0xff, 0x62, 0xd3, 0xdd, 0xff, 0x66, 0xc4, 0xff, 0xff, 0x2c, 0x4d, 0xd9, 0xff, 0x37, 0x3b, 0xc9, 0xff, 0x3e, 0x4d, 0xc7, 0xff, 0x74, 0x97, 0xf1, 0xff, 0x9c, 0xd5, 0xff, 0xff, 0x87, 0xd0, 0xff, 0xff, 0x70, 0xc1, 0xfa, 0xff, 0x65, 0xb9, 0xff, 0xff, 0x51, 0xa5, 0xf7, 0xff, 0x57, 0xae, 0xf8, 0xff, 0x75, 0xc1, 0xfc, 0xff,
    0xfc, 0xf1, 0xff, 0xff, 0x83, 0xd4, 0xe9, 0xff, 0x6c, 0xd1, 0xd3, 0xff, 0xb4, 0xb9, 0x8c, 0xff, 0xf7, 0xaf, 0x61, 0xff, 0xb8, 0x93, 0x59, 0xff, 0x8c, 0xb6, 0xc9, 0xff, 0x5f, 0xb0, 0xff, 0xff, 0x70, 0xa6, 0xfd, 0xff, 0x6f, 0xac, 0xf6, 0xff, 0x81, 0xc6, 0xff, 0xff, 0x80, 0xd2, 0xfc, 0xff, 0x66, 0xbf, 0xeb, 0xff, 0x59, 0xbb, 0xf1, 0xff, 0x63, 0xc7, 0xff, 0xff, 0x62, 0xc8, 0xff, 0xff, 0x40, 0xad, 0xff, 0xff, 0x56, 0xb6, 0xfc, 0xff,
    0xfb, 0xf4, 0xff, 0xff, 0x73, 0xb2, 0xe4, 0xff, 0x6c, 0xa4, 0xb7, 0xff, 0xcb, 0x85, 0x31, 0xff, 0xff, 0x95, 0x0b, 0xff, 0xed, 0x7e, 0x2e, 0xff, 0xa4, 0xa3, 0xad, 0xff, 0x83, 0xc8, 0xff, 0xff, 0x6c, 0xc3, 0xff, 0xff, 0x73, 0xca, 0xff, 0xff, 0x79, 0xd0, 0xff, 0xff, 0x78, 0xd0, 0xfe, 0xff, 0x71, 0xcd, 0xfc, 0xff, 0x67, 0xc8, 0xff, 0xff, 0x57, 0xbc, 0xff, 0xff, 0x45, 0xad, 0xfa, 0xff, 0x52, 0xb2, 0xfe, 0xff, 0x6a, 0xbd, 0xfa, 0xff,
    0x99, 0xde, 0xd3, 0xff, 0x5b, 0xb2, 0xfc, 0xff, 0x8d, 0xb3, 0xf4, 0xff, 0xbe, 0x7f, 0x43, 0xff, 0xf5, 0x8f, 0x0e, 0xff, 0xa5, 0x80, 0x3c, 0xff, 0x7a, 0x9d, 0xaa, 0xff, 0x8c, 0xcb, 0xed, 0xff, 0x69, 0xc0, 0xff, 0xff, 0x7d, 0xcd, 0xff, 0xff, 0x7e, 0xca, 0xff, 0xff, 0x75, 0xbe, 0xf8, 0xff, 0x79, 0xc1, 0xfd, 0xff, 0x78, 0xc4, 0xff, 0xff, 0x6c, 0xbc, 0xff, 0xff, 0x61, 0xb6, 0xff, 0xff, 0x67, 0xb5, 0xf0, 0xff, 0x89, 0xca, 0xf6, 0xff,
    0x60, 0xaf, 0xe8, 0xff, 0x5d, 0xc6, 0xff, 0xff, 0x5a, 0xbd, 0xfb, 0xff, 0x7d, 0xb3, 0xd2, 0xff, 0x7f, 0x98, 0xc2, 0xff, 0x6a, 0x98, 0xe6, 0xff, 0x7a, 0xc5, 0xff, 0xff, 0x70, 0xca, 0xe8, 0xff, 0x8a, 0xd2, 0xf4, 0xff, 0x74, 0xca, 0xff, 0xff, 0x6c, 0xbd, 0xff, 0xff, 0x78, 0xc0, 0xf0, 0xff, 0x72, 0xc7, 0xf7, 0xff, 0x4f, 0xbe, 0xff, 0xff, 0x50, 0xb6, 0xfe, 0xff, 0x75, 0xc1, 0xf1, 0xff, 0x9c, 0xcf, 0xd1, 0xff, 0xce, 0xf4, 0xf4, 0xff,
    0x5d, 0xb5, 0xeb, 0xff, 0x51, 0xb1, 0xf7, 0xff, 0x49, 0xc0, 0xf7, 0xff, 0x59, 0xdf, 0xf1, 0xff, 0x66, 0xd1, 0xed, 0xff, 0x72, 0xb3, 0xfd, 0xff, 0x7f, 0xc0, 0xff, 0xff, 0x72, 0xce, 0xf7, 0xff, 0x60, 0xba, 0xe3, 0xff, 0x5c, 0xbd, 0xff, 0xff, 0x61, 0xbb, 0xff, 0xff, 0x76, 0xbd, 0xff, 0xff, 0x73, 0xc0, 0xfe, 0xff, 0x5b, 0xba, 0xff, 0xff, 0x6a, 0xbd, 0xf0, 0xff, 0x9b, 0xd1, 0xe2, 0xff, 0xde, 0xf1, 0xf8, 0xff, 0xf6, 0xff, 0xff, 0xff,
    0x5e, 0xba, 0xef, 0xff, 0x4d, 0xab, 0xed, 0xff, 0x3e, 0xd2, 0xff, 0xff, 0x26, 0xf8, 0xff, 0xff, 0x33, 0xea, 0xfe, 0xff, 0x60, 0xc1, 0xff, 0xff, 0x6e, 0xad, 0xfd, 0xff, 0x62, 0xbf, 0xfc, 0xff, 0x72, 0xc4, 0xfb, 0xff, 0x60, 0xc1, 0xff, 0xff, 0x57, 0xb9, 0xff, 0xff, 0x62, 0xb8, 0xfe, 0xff, 0x66, 0xbb, 0xf3, 0xff, 0x6b, 0xbe, 0xf6, 0xff, 0xa6, 0xd8, 0xfc, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0x68, 0xb9, 0xf2, 0xff, 0x4c, 0xb3, 0xf2, 0xff, 0x3c, 0xdc, 0xff, 0xff, 0x16, 0xe2, 0xff, 0xff, 0x17, 0xcf, 0xf3, 0xff, 0x4c, 0xc2, 0xfd, 0xff, 0x5a, 0xad, 0xf8, 0xff, 0x5f, 0xb9, 0xff, 0xff, 0x86, 0xc5, 0xff, 0xff, 0x65, 0xb7, 0xff, 0xff, 0x57, 0xb1, 0xf8, 0xff, 0x6f, 0xc0, 0xe3, 0xff, 0x78, 0xc6, 0xdd, 0xff, 0x72, 0xba, 0xe4, 0xff, 0xa9, 0xcc, 0xf7, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0x8f, 0xc4, 0xfd, 0xff, 0x43, 0xb2, 0xea, 0xff, 0x30, 0xc5, 0xff, 0xff, 0x33, 0xbd, 0xff, 0xff, 0x3e, 0xb9, 0xf7, 0xff, 0x4c, 0xcb, 0xf8, 0xff, 0x4d, 0xc0, 0xf3, 0xff, 0x6a, 0xc2, 0xff, 0xff, 0x63, 0xc0, 0xfd, 0xff, 0x60, 0xb4, 0xf6, 0xff, 0x92, 0xc5, 0xed, 0xff, 0xe6, 0xf4, 0xf2, 0xff, 0xe9, 0xfd, 0xf8, 0xff, 0x95, 0xcb, 0xee, 0xff, 0x6c, 0xaf, 0xee, 0xff, 0x8f, 0xc5, 0xff, 0xff, 0xd6, 0xee, 0xf4, 0xff, 0xf3, 0xff, 0xff, 0xff,
    0xcd, 0xe5, 0xff, 0xff, 0x5a, 0xb6, 0xdf, 0xff, 0x2f, 0xa4, 0xe9, 0xff, 0x54, 0xa8, 0xff, 0xff, 0x65, 0xb7, 0xfe, 0xff, 0x4d, 0xc6, 0xd9, 0xff, 0x3f, 0xbe, 0xc6, 0xff, 0x5b, 0xb7, 0xda, 0xff, 0x3c, 0xc6, 0xf5, 0xff, 0x5f, 0xc1, 0xf7, 0xff, 0xc2, 0xd9, 0xf9, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xa2, 0xd2, 0xff, 0xff, 0x34, 0xa3, 0xf3, 0xff, 0x1e, 0xa5, 0xf7, 0xff, 0xb5, 0xde, 0xe1, 0xff, 0xe3, 0xff, 0xff, 0xff,
    0xfa, 0xfb, 0xff, 0xff, 0xa2, 0xd4, 0xe8, 0xff, 0x51, 0xa7, 0xe3, 0xff, 0x4b, 0xa5, 0xff, 0xff, 0x4d, 0xae, 0xe8, 0xff, 0x41, 0xb4, 0xa0, 0xff, 0x42, 0xb8, 0x88, 0xff, 0x3b, 0xa2, 0x8d, 0xff, 0x3d, 0xaf, 0xd8, 0xff, 0x57, 0xb8, 0xf2, 0xff, 0x8f, 0xc4, 0xf7, 0xff, 0xc6, 0xd5, 0xef, 0xff, 0xb8, 0xd5, 0xf4, 0xff, 0x62, 0xb7, 0xfb, 0xff, 0x24, 0x9f, 0xf5, 0xff, 0x23, 0xa1, 0xef, 0xff, 0xc2, 0xdf, 0xe4, 0xff, 0xe6, 0xf7, 0xfa, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xe9, 0xf4, 0xf8, 0xff, 0x7e, 0xbf, 0xf2, 0xff, 0x2e, 0xa7, 0xfd, 0xff, 0x1b, 0x9f, 0xc7, 0xff, 0x3d, 0xaa, 0x7a, 0xff, 0x5c, 0xc1, 0x6c, 0xff, 0x2f, 0xa3, 0x62, 0xff, 0x5f, 0x93, 0xc1, 0xff, 0x5f, 0xaf, 0xf6, 0xff, 0x52, 0xb8, 0xff, 0xff, 0x40, 0xaf, 0xe7, 0xff, 0x30, 0xad, 0xe7, 0xff, 0x26, 0xac, 0xff, 0xff, 0x44, 0xab, 0xff, 0xff, 0x73, 0xae, 0xf3, 0xff, 0xef, 0xf8, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xe5, 0xf7, 0xfe, 0xff, 0xb4, 0xd6, 0xe6, 0xff, 0x6f, 0xab, 0xc3, 0xff, 0x34, 0x8e, 0xac, 0xff, 0x19, 0x8d, 0xac, 0xff, 0x17, 0x98, 0xb9, 0xff, 0x3f, 0xb1, 0xfe, 0xff, 0x38, 0xb2, 0xff, 0xff, 0x30, 0xb2, 0xff, 0xff, 0x29, 0xa8, 0xff, 0xff, 0x30, 0x9d, 0xeb, 0xff, 0x59, 0xa7, 0xdc, 0xff, 0xa4, 0xd2, 0xea, 0xff, 0xe3, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xf6, 0xf1, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xfa, 0xfd, 0xff, 0xff, 0xe9, 0xfa, 0xff, 0xff, 0xbe, 0xe3, 0xf7, 0xff, 0x8b, 0xc9, 0xe1, 0xff, 0x64, 0xb8, 0xd2, 0xff, 0x53, 0xb2, 0xcc, 0xff, 0x5c, 0xb1, 0xef, 0xff, 0x49, 0xa0, 0xe2, 0xff, 0x40, 0x9b, 0xde, 0xff, 0x59, 0xb0, 0xf0, 0xff, 0x88, 0xce, 0xff, 0xff, 0xb9, 0xe6, 0xff, 0xff, 0xe2, 0xf5, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xfb, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xf3, 0xfc, 0xff, 0xff, 0xdf, 0xf8, 0xff, 0xff, 0xc2, 0xea, 0xfc, 0xff, 0xad, 0xdf, 0xf3, 0xff, 0xa7, 0xd9, 0xff, 0xff, 0xa1, 0xd2, 0xfa, 0xff, 0xac, 0xd6, 0xf9, 0xff, 0xcb, 0xec, 0xff, 0xff, 0xe8, 0xfc, 0xff, 0xff, 0xf8, 0xfd, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
};

const lv_image_dsc_t img_emoji_artist_palette = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_artist_palette_map,
    .data_size = sizeof(img_emoji_artist_palette_map),
};

#endif
