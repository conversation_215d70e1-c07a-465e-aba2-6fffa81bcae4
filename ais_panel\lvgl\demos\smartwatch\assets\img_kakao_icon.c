#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: kakao.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_kakao_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x66,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0x67,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xE8,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x48,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xE8,0xFE,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x07,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x27,0xFF,0x66,0xE6,0x06,0xD6,
0x06,0xD6,0x66,0xE6,0x27,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x27,0xFF,0xE6,0xB4,0xC5,0x72,0x64,0x41,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x64,0x41,0xC5,0x72,0xE6,0xB4,0x27,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x48,0xFF,0x00,0x00,
0x00,0x00,0x67,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x46,0xBD,0xC4,0x49,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0xC4,0x51,0x46,0xBD,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x66,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xE5,0x93,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,
0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0xE5,0x93,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x45,0x9C,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x45,0xA4,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x26,0xDE,0x44,0x41,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x44,0x41,0x46,0xDE,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xA5,0x8B,0x24,0x39,0x45,0x7B,0xE6,0xD5,0x26,0xDE,0xE6,0xD5,0x05,0x7B,0x86,0xA4,0x65,0x83,0x24,0x39,
0x65,0x83,0x65,0x83,0x24,0x39,0x85,0x6A,0x05,0x94,0x64,0x41,0x65,0xA4,0xC4,0x51,0x24,0x39,0xA5,0x8B,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x04,0x5A,0x24,0x39,0xE4,0x51,0xA5,0x8B,0x47,0xFF,0x65,0x83,0xC5,0x72,0x27,0xF7,0xC7,0xEE,0x44,0x41,0xC6,0xB4,0xC6,0xB4,0x24,0x39,0xC5,0x8B,0xE6,0xD5,0x66,0xC5,0xC6,0xD5,0x64,0x41,0x24,0x39,0x04,0x5A,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x64,0x41,0x24,0x39,0x24,0x39,0xE4,0x51,0x47,0xFF,0x84,0x49,0x86,0xA4,0xC6,0xB4,0x26,0xDE,0x25,0x7B,0xE6,0xB4,0xC6,0xB4,0x24,0x39,0xC5,0x93,0x47,0xFF,0x07,0xF7,0xA4,0x49,0x24,0x39,0x24,0x39,0x64,0x41,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xC4,0x49,0x24,0x39,0x24,0x39,0xE4,0x51,0x47,0xFF,0xA4,0x49,0xC7,0xEE,0x46,0xDE,0xA7,0xEE,0x86,0xC5,
0xE6,0xB4,0xC6,0xB4,0x24,0x39,0xC5,0x93,0x46,0xDE,0x46,0xDE,0xA6,0xAC,0x24,0x39,0x24,0x39,0xE4,0x51,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x25,0x7B,0x24,0x39,0x24,0x39,0xC4,0x51,0x47,0xFF,0xE5,0x72,0xC7,0xEE,0x45,0x7B,0xA5,0x8B,0x27,0xFF,0xE6,0xB4,0xC7,0xEE,0x46,0xDE,0xE6,0xB4,0xA6,0xCD,0x44,0x5A,0x07,0xF7,0x65,0x62,0x24,0x39,0x45,0x83,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xC6,0xCD,0x24,0x39,0x24,0x39,0x24,0x39,0x85,0x6A,0x84,0x49,0x44,0x5A,0x24,0x39,0x24,0x39,0x85,0x62,0xA4,0x49,0x85,0x62,0xA5,0x6A,0xA4,0x49,0x04,0x5A,0x24,0x39,0x04,0x5A,0x64,0x41,0x44,0x39,0xE6,0xD5,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x85,0x8B,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,
0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0xA5,0x8B,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x45,0x7B,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x05,0x7B,0x27,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x48,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x86,0xAC,0x64,0x41,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x64,0x41,0x66,0xA4,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x48,0xFF,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xA6,0xAC,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,0x24,0x39,
0x24,0x39,0x24,0x39,0x24,0x39,0xE4,0x51,0x25,0x9C,0xA7,0xEE,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x05,0x73,0x24,0x39,0x24,0x39,0x85,0x6A,0x66,0xC5,0x06,0xB5,0x06,0xBD,0x86,0xCD,0x87,0xEE,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,
0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x07,0xF7,0x64,0x41,0x64,0x41,0x66,0xA4,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE8,0xFE,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xC6,0xCD,0x85,0x62,0x26,0xDE,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xC9,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x48,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x67,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x27,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x48,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC9,0xFE,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0xC9,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x48,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x48,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,
0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x47,0xFF,0x48,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x1B,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_kakao_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_kakao_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_kakao_icon_data};

