#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_BOOKS
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_BOOKS
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_BOOKS uint8_t img_emoji_books_map[]
= {
    0xff, 0xf6, 0xfc, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xfa, 0xf7, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xe2, 0xff, 0xfd, 0xff, 0xff, 0xf7, 0xf8, 0xff, 0xf7, 0xf0, 0xf3, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xec, 0xf3, 0xf6, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xf9, 0xfe, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0xe4, 0xff, 0xf7, 0xff, 0xfb, 0xff, 0xf9, 0xff, 0xef, 0xff, 0xff, 0xff, 0xdd, 0xd0, 0xd8, 0xff, 0x65, 0x5f, 0x60, 0xff, 0x37, 0x5e, 0x44, 0xff, 0xd5, 0x92, 0x77, 0xff, 0xe4, 0xa3, 0x8e, 0xff, 0xe6, 0xb2, 0x9b, 0xff, 0xe2, 0xbf, 0xa5, 0xff, 0xe2, 0xc7, 0xad, 0xff, 0xf1, 0xdc, 0xc6, 0xff, 0xec, 0xe5, 0xd1, 0xff, 0xdc, 0xea, 0xd8, 0xff, 0xea, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xfe, 0xfa, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xf5, 0xff, 0xf1, 0xf3, 0xdd, 0xff, 0xe7, 0xf9, 0xec, 0xff, 0xba, 0xb0, 0xb6, 0xff, 0x50, 0x47, 0x44, 0xff, 0x4e, 0x56, 0x2e, 0xff, 0xe1, 0x87, 0x4e, 0xff, 0xf4, 0x7b, 0x49, 0xff, 0xe3, 0x70, 0x3d, 0xff, 0xeb, 0x7b, 0x47, 0xff, 0xef, 0x79, 0x48, 0xff, 0xf5, 0x7e, 0x51, 0xff, 0xe4, 0x78, 0x4f, 0xff, 0xd1, 0x7b, 0x57, 0xff, 0xff, 0xbd, 0x9a, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xec, 0xeb, 0xef, 0xff, 0xff, 0xfe, 0xf9, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xef, 0xf7, 0xf7, 0xff, 0xa7, 0x94, 0xa5, 0xff, 0x5d, 0x3d, 0x42, 0xff, 0x8a, 0x5e, 0x39, 0xff, 0xfa, 0x7e, 0x3e, 0xff, 0xef, 0x8b, 0x4b, 0xff, 0xde, 0x7a, 0x39, 0xff, 0xea, 0x83, 0x3e, 0xff, 0xe9, 0x78, 0x36, 0xff, 0xf0, 0x78, 0x3c, 0xff, 0xe2, 0x75, 0x3f, 0xff, 0xce, 0x79, 0x47, 0xff, 0xff, 0xc4, 0x94, 0xff, 0xed, 0xf8, 0xe8, 0xff, 0xfb, 0xff, 0xfe, 0xff,
    0xf4, 0xfe, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xe2, 0xfa, 0xff, 0xff, 0xc4, 0xf1, 0xff, 0xff, 0x89, 0x8c, 0xab, 0xff, 0x4e, 0x3b, 0x44, 0xff, 0x8d, 0x58, 0x3d, 0xff, 0xdc, 0x76, 0x42, 0xff, 0xe8, 0x67, 0x3a, 0xff, 0xe6, 0x70, 0x3d, 0xff, 0xe9, 0x7e, 0x45, 0xff, 0xd9, 0x78, 0x39, 0xff, 0xd6, 0x80, 0x44, 0xff, 0xc5, 0x88, 0x50, 0xff, 0xa9, 0x8c, 0x59, 0xff, 0xc1, 0xbd, 0x8c, 0xff, 0xbd, 0xd2, 0xb9, 0xff, 0xf8, 0xff, 0xf8, 0xff,
    0x9e, 0xc3, 0xb9, 0xff, 0x76, 0x78, 0x97, 0xff, 0x31, 0x8b, 0xc1, 0xff, 0x2d, 0x9d, 0xd2, 0xff, 0x28, 0x5b, 0x7b, 0xff, 0x32, 0x43, 0x40, 0xff, 0x73, 0x56, 0x41, 0xff, 0x7e, 0x58, 0x3a, 0xff, 0x6a, 0x78, 0x48, 0xff, 0x86, 0xa3, 0x6b, 0xff, 0x84, 0xb0, 0x6b, 0xff, 0x76, 0xaa, 0x5d, 0xff, 0x79, 0xaf, 0x62, 0xff, 0x6c, 0xac, 0x64, 0xff, 0x57, 0xa9, 0x68, 0xff, 0x4f, 0xb2, 0x74, 0xff, 0x90, 0xaf, 0x88, 0xff, 0xec, 0xff, 0xea, 0xff,
    0x5f, 0x7a, 0x59, 0xff, 0x3c, 0x38, 0x62, 0xff, 0x14, 0x8f, 0xe5, 0xff, 0x10, 0xa0, 0xef, 0xff, 0x0c, 0x4c, 0x6a, 0xff, 0x3f, 0x4f, 0x3d, 0xff, 0x77, 0x59, 0x46, 0xff, 0x47, 0x44, 0x3f, 0xff, 0x29, 0x66, 0x34, 0xff, 0x5e, 0xab, 0x6e, 0xff, 0x55, 0xb0, 0x64, 0xff, 0x5a, 0xb6, 0x5d, 0xff, 0x6a, 0xba, 0x61, 0xff, 0x69, 0xad, 0x5a, 0xff, 0x74, 0xb3, 0x69, 0xff, 0x66, 0xa6, 0x64, 0xff, 0x79, 0x9f, 0x6f, 0xff, 0xe6, 0xff, 0xe2, 0xff,
    0xac, 0x9f, 0x73, 0xff, 0x48, 0x23, 0x55, 0xff, 0x04, 0x76, 0xe1, 0xff, 0x07, 0x8d, 0xeb, 0xff, 0x19, 0x42, 0x5b, 0xff, 0x60, 0x55, 0x37, 0xff, 0x8e, 0x53, 0x43, 0xff, 0x42, 0x3a, 0x45, 0xff, 0x47, 0x5b, 0x2c, 0xff, 0x7c, 0xab, 0x6d, 0xff, 0x59, 0xa9, 0x56, 0xff, 0x54, 0xb5, 0x54, 0xff, 0x55, 0xb8, 0x50, 0xff, 0x46, 0xa3, 0x40, 0xff, 0x5f, 0xbd, 0x5f, 0xff, 0x4e, 0xac, 0x57, 0xff, 0x70, 0x9a, 0x63, 0xff, 0xe4, 0xff, 0xdc, 0xff,
    0xce, 0xbc, 0xbb, 0xff, 0x32, 0x36, 0x4e, 0xff, 0x1e, 0x6f, 0x77, 0xff, 0x00, 0x8b, 0xff, 0xff, 0x31, 0x3c, 0x57, 0xff, 0x4b, 0x46, 0x3d, 0xff, 0xd4, 0x6a, 0x1d, 0xff, 0x31, 0x3e, 0x3c, 0xff, 0x3b, 0x4e, 0x45, 0xff, 0x66, 0x9f, 0x5a, 0xff, 0x57, 0xb0, 0x48, 0xff, 0x4a, 0xa3, 0x4d, 0xff, 0x5a, 0xa8, 0x61, 0xff, 0x57, 0xa4, 0x50, 0xff, 0x56, 0xa4, 0x4c, 0xff, 0x57, 0xa3, 0x5b, 0xff, 0x5b, 0x98, 0x58, 0xff, 0xd3, 0xf9, 0xcf, 0xff,
    0xdb, 0xcb, 0xc5, 0xff, 0x3a, 0x3a, 0x4a, 0xff, 0x21, 0x61, 0x61, 0xff, 0x0e, 0x86, 0xfe, 0xff, 0x31, 0x38, 0x4c, 0xff, 0x3f, 0x39, 0x34, 0xff, 0xc4, 0x67, 0x2a, 0xff, 0x3e, 0x4a, 0x54, 0xff, 0x38, 0x4a, 0x43, 0xff, 0x63, 0x99, 0x56, 0xff, 0x55, 0xab, 0x45, 0xff, 0x48, 0xa0, 0x4c, 0xff, 0x56, 0xa4, 0x5d, 0xff, 0x54, 0xa1, 0x4d, 0xff, 0x53, 0xa1, 0x49, 0xff, 0x52, 0x9f, 0x55, 0xff, 0x56, 0x94, 0x52, 0xff, 0xc6, 0xee, 0xc4, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0x54, 0x55, 0x59, 0xff, 0x1d, 0x44, 0x35, 0xff, 0x1c, 0x74, 0xc8, 0xff, 0x47, 0x46, 0x50, 0xff, 0x53, 0x46, 0x44, 0xff, 0xb4, 0x64, 0x35, 0xff, 0x33, 0x35, 0x40, 0xff, 0x37, 0x46, 0x42, 0xff, 0x5c, 0x90, 0x50, 0xff, 0x51, 0xa5, 0x3f, 0xff, 0x48, 0x9e, 0x4a, 0xff, 0x50, 0x9d, 0x59, 0xff, 0x4e, 0x9b, 0x47, 0xff, 0x4f, 0x9d, 0x45, 0xff, 0x4b, 0x98, 0x4e, 0xff, 0x50, 0x90, 0x4e, 0xff, 0xb8, 0xe1, 0xb4, 0xff,
    0xfa, 0xf6, 0xfb, 0xff, 0x6a, 0x6f, 0x6e, 0xff, 0x3a, 0x47, 0x31, 0xff, 0x3a, 0x6e, 0x9c, 0xff, 0x4b, 0x42, 0x3f, 0xff, 0x59, 0x3f, 0x38, 0xff, 0xb0, 0x6c, 0x41, 0xff, 0x63, 0x4a, 0x48, 0xff, 0x39, 0x43, 0x43, 0xff, 0x55, 0x86, 0x48, 0xff, 0x4c, 0x9d, 0x3c, 0xff, 0x46, 0x9a, 0x48, 0xff, 0x4a, 0x97, 0x53, 0xff, 0x48, 0x95, 0x41, 0xff, 0x4a, 0x9a, 0x3f, 0xff, 0x42, 0x91, 0x47, 0xff, 0x4f, 0x8f, 0x4d, 0xff, 0xa9, 0xd5, 0xa6, 0xff,
    0xf0, 0xf8, 0xff, 0xff, 0x8b, 0x98, 0x96, 0xff, 0x48, 0x42, 0x2f, 0xff, 0x43, 0x5e, 0x72, 0xff, 0x55, 0x53, 0x52, 0xff, 0x70, 0x58, 0x58, 0xff, 0xa0, 0x75, 0x54, 0xff, 0x6a, 0x40, 0x2d, 0xff, 0x3d, 0x42, 0x45, 0xff, 0x52, 0x7d, 0x44, 0xff, 0x47, 0x95, 0x37, 0xff, 0x43, 0x96, 0x47, 0xff, 0x45, 0x90, 0x4c, 0xff, 0x41, 0x8e, 0x3a, 0xff, 0x47, 0x97, 0x3c, 0xff, 0x3d, 0x8d, 0x40, 0xff, 0x4b, 0x8e, 0x4b, 0xff, 0x97, 0xc6, 0x95, 0xff,
    0xf4, 0xfc, 0xff, 0xff, 0xab, 0xbc, 0xb9, 0xff, 0x46, 0x34, 0x2d, 0xff, 0x45, 0x5a, 0x69, 0xff, 0x7c, 0x99, 0xb4, 0xff, 0xb2, 0xb8, 0xdb, 0xff, 0xb7, 0xbf, 0xc6, 0xff, 0x93, 0x75, 0x70, 0xff, 0x3f, 0x41, 0x49, 0xff, 0x4c, 0x74, 0x3d, 0xff, 0x42, 0x8d, 0x31, 0xff, 0x40, 0x90, 0x43, 0xff, 0x3d, 0x87, 0x45, 0xff, 0x3b, 0x88, 0x34, 0xff, 0x44, 0x94, 0x39, 0xff, 0x38, 0x88, 0x3b, 0xff, 0x43, 0x88, 0x45, 0xff, 0x85, 0xb6, 0x82, 0xff,
    0xfb, 0xfa, 0xfc, 0xff, 0xda, 0xeb, 0xe0, 0xff, 0x5b, 0x45, 0x4a, 0xff, 0x26, 0x45, 0x5e, 0xff, 0x20, 0x6b, 0xb5, 0xff, 0x2d, 0x66, 0xc3, 0xff, 0x1e, 0x6b, 0xb0, 0xff, 0x36, 0x42, 0x6a, 0xff, 0x40, 0x3f, 0x49, 0xff, 0x46, 0x6c, 0x38, 0xff, 0x3d, 0x85, 0x2b, 0xff, 0x3d, 0x8b, 0x3e, 0xff, 0x34, 0x7e, 0x3c, 0xff, 0x34, 0x81, 0x2d, 0xff, 0x41, 0x91, 0x36, 0xff, 0x31, 0x83, 0x36, 0xff, 0x3d, 0x84, 0x41, 0xff, 0x75, 0xa9, 0x73, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xf2, 0xff, 0xef, 0xff, 0x5c, 0x45, 0x53, 0xff, 0x1a, 0x44, 0x69, 0xff, 0x0b, 0x78, 0xe4, 0xff, 0x08, 0x6a, 0xf4, 0xff, 0x00, 0x6d, 0xe4, 0xff, 0x27, 0x58, 0xa8, 0xff, 0x42, 0x3e, 0x4a, 0xff, 0x44, 0x67, 0x35, 0xff, 0x3b, 0x81, 0x27, 0xff, 0x39, 0x86, 0x3c, 0xff, 0x31, 0x79, 0x37, 0xff, 0x2f, 0x7c, 0x28, 0xff, 0x3f, 0x8f, 0x34, 0xff, 0x2e, 0x81, 0x32, 0xff, 0x3d, 0x84, 0x41, 0xff, 0x6e, 0xa2, 0x6c, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xfd, 0xfb, 0xfb, 0xff, 0x6c, 0x71, 0x7a, 0xff, 0x65, 0x72, 0x82, 0xff, 0x84, 0x94, 0xa5, 0xff, 0xa6, 0xb3, 0xc1, 0xff, 0xbd, 0xc6, 0xcf, 0xff, 0x95, 0x9c, 0x9f, 0xff, 0x73, 0x7b, 0x70, 0xff, 0x80, 0x88, 0x7d, 0xff, 0x93, 0x9b, 0x90, 0xff, 0xa6, 0xae, 0xa3, 0xff, 0xb6, 0xbe, 0xb3, 0xff, 0xc7, 0xcf, 0xc4, 0xff, 0xd8, 0xe0, 0xd5, 0xff, 0xe3, 0xeb, 0xe0, 0xff, 0xd5, 0xd5, 0xd5, 0xff, 0xbc, 0xbc, 0xbc, 0xff,
    0xff, 0xf7, 0xf0, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xa3, 0xa6, 0xae, 0xff, 0x94, 0x9d, 0xaa, 0xff, 0x95, 0xa2, 0xb0, 0xff, 0xa2, 0xac, 0xb6, 0xff, 0xad, 0xb6, 0xba, 0xff, 0x99, 0x9d, 0x9e, 0xff, 0x91, 0x95, 0x8f, 0xff, 0x99, 0x9d, 0x97, 0xff, 0xa2, 0xa6, 0xa0, 0xff, 0xa9, 0xad, 0xa7, 0xff, 0xad, 0xb1, 0xab, 0xff, 0xb1, 0xb5, 0xaf, 0xff, 0xb8, 0xbc, 0xb6, 0xff, 0xbe, 0xc2, 0xbc, 0xff, 0xdc, 0xdc, 0xdc, 0xff, 0xd9, 0xd9, 0xd9, 0xff,
    0xff, 0xf9, 0xf2, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xf4, 0xf5, 0xf9, 0xff, 0xe9, 0xee, 0xf7, 0xff, 0xeb, 0xf4, 0xfd, 0xff, 0xf8, 0xfe, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xf6, 0xf9, 0xf7, 0xff, 0xfa, 0xfd, 0xfb, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf9, 0xf9, 0xf9, 0xff,
};

const lv_image_dsc_t img_emoji_books = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_books_map,
    .data_size = sizeof(img_emoji_books_map),
};

#endif
