#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: penguin.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_penguin_icon_data[] = {
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x76,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x76,0x6B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x38,0x73,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,
0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x73,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x63,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x17,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x6B,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x18,0x6B,0x17,0x6B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,
0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x17,0x6B,0x50,0x4A,0x0C,0x42,0xEA,0x39,0xEA,0x39,0x0C,0x42,0x50,0x4A,0x17,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x17,0x6B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0x17,0x63,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xB4,0x5A,0xE9,0x39,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE9,0x39,0xB3,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x18,0x6B,0x17,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x72,0x52,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,
0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0x72,0x52,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x38,0x73,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xF7,0x6A,0xE9,0x39,0xE8,0x31,0xE8,0x31,0x08,0x3A,0xCC,0x52,0xE8,0x31,0xE8,0x31,0xCC,0x52,0x08,0x3A,0xE8,0x31,0xE8,0x31,0xC9,0x39,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x73,0xFF,0xFF,
0xFF,0xFF,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x0D,0x42,0xE8,0x31,0xE8,0x31,0xE8,0x31,0x38,0xC6,0x7D,0xEF,0x2D,0x63,0x2D,0x63,0x9E,0xF7,0x38,0xC6,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xCC,0x39,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xFF,0xFF,0x76,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xE9,0x39,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xDB,0xDE,0xD3,0x94,0xB3,0x94,
0xB3,0x94,0x14,0x9D,0xF7,0xBD,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xC9,0x39,0xB1,0x41,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x76,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x17,0x6B,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0x7A,0xCE,0x3C,0xE7,0x6E,0x6B,0x6E,0x6B,0xBE,0xF7,0x39,0xC6,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xB1,0x41,0xB1,0x41,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,
0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xD5,0x62,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0x69,0x4A,0xCD,0x7B,0xC6,0x62,0xC6,0x62,0xCD,0x7B,0x69,0x4A,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xD0,0x41,0xB1,0x41,0xB1,0x41,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xF6,0x6A,0xE8,0x39,0xE8,0x31,0x87,0x52,0xC4,0xE5,0x00,0xFE,0x00,0xFE,0x00,0xFE,
0x00,0xFE,0x00,0xFE,0x00,0xFE,0xC4,0xE5,0x87,0x52,0xE8,0x31,0xE8,0x39,0xB0,0x49,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x50,0x9A,0xE7,0xE1,0xE8,0x61,0x30,0x84,0x34,0xF7,0x23,0xFE,0x00,0xFE,0x00,0xFE,0x00,0xFE,0x00,0xFE,0x23,0xFE,0x34,0xF7,0x30,0x84,0xE8,0x51,0xE7,0xD9,0xCD,0x91,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x18,0x6B,
0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xEA,0xB9,0xE7,0xF9,0xE7,0xF9,0xB2,0xF4,0x9E,0xF7,0x59,0xF7,0x67,0xFE,0x21,0xFE,0x21,0xFE,0x67,0xFE,0x59,0xF7,0x9E,0xF7,0x14,0xF5,0xE7,0xF9,0xE7,0xF9,0xC9,0xB9,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x95,0x5A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xD4,0x5A,0xE8,0x39,0xE7,0xA1,0xE7,0xF9,0xE7,0xF9,0x69,0xFA,0x8E,0xFB,0xD3,0xFC,0xF6,0xF5,
0x17,0xF6,0xF3,0xFC,0xCF,0xFB,0x8A,0xFA,0xE7,0xF9,0xE7,0xF9,0xE7,0xB9,0xE8,0x41,0xD0,0x39,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x95,0x5A,0x38,0x6B,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xEB,0x39,0xE8,0x31,0xE8,0x31,0xF4,0xC4,0xE7,0xF9,0xE7,0xF9,0xE7,0xF9,0xE7,0xF9,0xE7,0xF9,0xE7,0xF9,0xE7,0xF9,0xE7,0xF9,0xE7,0xF9,0x4D,0xFB,0x92,0xCC,0xE8,0x31,0xE8,0x31,0xCA,0x39,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x95,0x5A,
0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xD6,0x62,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xFB,0xDE,0x08,0xFA,0xE7,0xF9,0x8A,0xFA,0x71,0xFC,0xEF,0xFB,0xAE,0xFB,0x71,0xFC,0x34,0xFD,0xBA,0xF6,0x9E,0xF7,0xFB,0xDE,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xD0,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x12,0x4A,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x92,0x52,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xDB,0xDE,0x08,0xFA,0xE7,0xF9,0x4D,0xFB,0x9E,0xF7,0x9E,0xF7,
0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0xDB,0xDE,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xCE,0x39,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x33,0x52,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x92,0x52,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xD7,0xB5,0x49,0xFA,0xE7,0xF9,0x4D,0xFB,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0xD7,0xB5,0xE8,0x31,0xE8,0x31,0xE8,0x31,0xCF,0x39,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x34,0x52,
0x76,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x92,0x52,0xE8,0x31,0xCF,0x39,0xE8,0x39,0x92,0x8C,0x9E,0xF7,0x9A,0xF6,0xF7,0xF5,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x92,0x8C,0xE8,0x39,0xCF,0x39,0xE8,0x31,0xCF,0x39,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xD2,0x41,0x52,0x4A,0xFF,0xFF,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xF6,0x62,0xEF,0x41,0xB1,0x41,0xCD,0x39,0x29,0x3A,0xFB,0xDE,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,
0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0xFB,0xDE,0x29,0x3A,0xCD,0x39,0xB1,0x41,0xCF,0x39,0xB0,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x33,0x52,0xFF,0xFF,0xFF,0xFF,0x38,0x73,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xF7,0x6A,0xF2,0x41,0xD1,0x41,0x49,0x4A,0xCF,0x73,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0xCF,0x73,0x49,0x4A,0xD1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xD1,0x41,0x74,0x52,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xF7,0x62,0xE4,0xD4,0x00,0xFE,0xE4,0x93,0x8E,0x73,0x3C,0xE7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x9E,0xF7,0x3C,0xE7,0x8E,0x73,0xE4,0x93,0x00,0xFE,0xE4,0xD4,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0x34,0x52,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x63,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x16,0x73,0x81,0xFD,0x00,0xFE,0x00,0xFE,0xA1,0xED,0x26,0x9C,0x8E,0x9C,0x75,0xAD,
0x75,0xAD,0x8E,0x9C,0x26,0x9C,0xA1,0xED,0x00,0xFE,0x00,0xFE,0x81,0xFD,0xF0,0x49,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xF2,0x49,0xB5,0x52,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0xF0,0x7A,0x44,0xD4,0xA3,0xE4,0x02,0xED,0x64,0xD4,0x09,0x9B,0xEF,0x51,0xEF,0x51,0x09,0x9B,0x64,0xD4,0x02,0xED,0xA3,0xE4,0x44,0xD4,0x4E,0x6A,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xF2,0x41,0x34,0x52,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x75,0x5A,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xD2,0x41,0x74,0x52,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x6B,0x18,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x75,0x5A,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,
0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xF2,0x49,0x53,0x52,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x63,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x75,0x5A,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xD1,0x41,0x33,0x4A,0x76,0x4A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x38,0x73,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x75,0x5A,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xB1,0x41,0xD1,0x41,0x33,0x4A,0x54,0x52,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x76,0x6B,0x38,0x6B,0x38,0x6B,0x38,0x6B,0x74,0x52,0xB1,0x41,
0xD1,0x41,0xF2,0x41,0xF2,0x49,0x33,0x4A,0x54,0x52,0x54,0x5A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x49,0x98,0xC1,0xDA,0xF3,0xF3,0xDA,0xC1,0x98,0x49,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x19,0x97,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0x97,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x84,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x84,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x18,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC8,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x2D,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC8,0x08,0x00,0x00,0x00,0x00,0x84,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x84,0x00,0x00,0x00,0x19,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x19,0x00,
0x00,0x97,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x97,0x00,0x07,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0x07,0x49,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x49,
0x98,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC1,0xDA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDA,
0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0xDA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE8,
0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF0,0x98,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD0,0x49,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x84,
0x07,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x11,0x00,0x97,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD0,0x00,0x00,0x19,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x36,0x00,
0x00,0x00,0x84,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBE,0x00,0x00,0x00,0x00,0x08,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x00,0x00,0x00,0x00,0x18,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x33,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2D,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x84,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC5,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x19,0x97,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xD1,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x49,0x98,0xC1,0xEB,0xFF,0xFE,0xFA,0xF1,0xD3,0x7C,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_penguin_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_penguin_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_penguin_icon_data};

