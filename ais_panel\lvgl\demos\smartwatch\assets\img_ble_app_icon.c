#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: ble_app.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_ble_app_icon_data[] = {
0x00,0x00,0x00,0x00,0x8C,0x29,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x6C,0x21,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x6A,0x10,0xCB,0x18,0x0C,0x21,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4D,0x29,0x8D,0x21,0x8D,0x21,0xCE,0x29,0x6F,0x32,0xEE,0x29,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x6C,0x21,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x8A,0x10,
0x8A,0x10,0x6A,0x10,0x4A,0x10,0xAB,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x29,0x8D,0x21,0xB3,0x53,0x9F,0x9F,0xBF,0x9F,0x79,0x75,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x6C,0x21,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x6A,0x10,0xBD,0x46,0x5F,0x47,0xDB,0x3D,0x8A,0x10,0x4A,0x10,0xCB,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x1B,0x7E,0xBF,0x9F,0xBF,0x9F,0xFD,0x96,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x6C,0x21,0x4A,0x10,0x4A,0x10,0x4A,0x10,0xCB,0x10,0x5F,0x47,0x5F,0x47,0x5F,0x47,0xCE,0x19,0x4A,0x10,0xCB,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x4F,0x32,0x8D,0x21,0x8D,0x21,0x6C,0x21,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x0F,0x22,0x5F,0x47,
0x5F,0x47,0x5F,0x47,0xEF,0x21,0x4A,0x10,0xCB,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9C,0x86,0x1A,0x7E,0x1A,0x7E,0xBA,0x75,0x56,0x34,0x56,0x34,0x56,0x34,0xFB,0x3D,0x5F,0x47,0x5F,0x47,0x5F,0x47,0xEF,0x21,0x4A,0x10,0xCB,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x47,0x5F,0x47,0x5F,0x47,0x5F,0x47,0x3F,0x47,0xBF,0x3E,0x3F,0x36,0xF9,0x23,0x7B,0x33,0x7D,0x3B,0xFF,0x3B,0x1F,0x3C,0x1F,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x47,0x5F,0x47,0x1F,0x47,0x3F,0x36,0x5F,0x25,
0x3F,0x25,0x3F,0x25,0x3F,0x25,0xFF,0x3B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x7F,0x43,0xFF,0x3B,0x1F,0x34,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x47,0x9F,0x3E,0x5F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0xFF,0x3B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x7F,0x43,0x1F,0x34,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x36,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0xFF,0x3B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x5F,0x4B,0x1F,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xDF,0x6E,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,
0x3F,0x25,0x3F,0x25,0x5F,0x8E,0x7F,0x8D,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x5F,0x4B,0x3F,0x34,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x5F,0x8F,0x5F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x9F,0x9E,0x7E,0xEF,0x1E,0xDF,0x3F,0x6C,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x7F,0x43,0x1F,0x2C,0x00,0x00,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x1F,0x4E,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x9F,0x9E,0x7E,0xEF,0x5E,0xE7,0x5E,0xE7,0x3E,0xBE,0x5F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xFF,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x5F,0x97,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x1E,0xC7,0x5F,0x8E,
0x3F,0x25,0x3F,0x25,0x9F,0x9E,0x7E,0xEF,0x3E,0xE7,0x5E,0xE7,0x5E,0xE7,0x3E,0xE7,0x9F,0x84,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x7F,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x6E,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0xBF,0x4D,0x9E,0xF7,0xBE,0xFF,0xFE,0xC6,0x7F,0x35,0x9F,0x9E,0x7E,0xEF,0x7F,0xA5,0xDF,0xB5,0x5E,0xE7,0x5E,0xE7,0x5E,0xE7,0xBF,0x5B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x1F,0x34,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xFF,0x4D,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x9F,0x45,0x3E,0xDF,0xBE,0xFF,0x7E,0xEF,0x1E,0xCF,0x7E,0xEF,0x1E,0xDF,0x7E,0xE7,0x5E,0xE7,0xFE,0xDE,0xBF,0x63,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xFF,0x3B,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x3D,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x5F,0x2D,
0x9F,0xA6,0xBE,0xFF,0xBE,0xFF,0x7E,0xEF,0x5E,0xE7,0x7E,0xE7,0x1E,0xBE,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xDF,0x3B,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x35,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x9F,0x9E,0xBE,0xFF,0x7E,0xEF,0x5E,0xE7,0x5F,0x9D,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xBF,0x3B,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x3D,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x86,0x9E,0xFF,0xBE,0xFF,0x7E,0xEF,0x5E,0xE7,0x5E,0xE7,0x7F,0x9D,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xDF,0x3B,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xFF,0x4D,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x7F,0x35,0xFE,0xC6,
0xBE,0xFF,0x9E,0xF7,0x3E,0xD7,0x7E,0xEF,0x3E,0xE7,0x5E,0xE7,0x5E,0xE7,0x9E,0xCE,0x7F,0x53,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xFF,0x3B,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x66,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x9F,0x4D,0x7E,0xEF,0xBE,0xFF,0x3E,0xD7,0x9F,0x45,0x9F,0x9E,0x7E,0xEF,0x7F,0xA5,0xFF,0xB5,0x5E,0xE7,0x5E,0xE7,0x3E,0xE7,0xBF,0x63,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xFF,0x3B,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x5F,0x8F,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x5F,0x2D,0x3E,0xDF,0xBF,0xA6,0x5F,0x2D,0x3F,0x25,0x9F,0x9E,0x7E,0xEF,0xFE,0xDE,0x5E,0xE7,0x5E,0xE7,0x5E,0xE7,0x5F,0x9D,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x5F,0x43,0x1F,0x04,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xFF,0x4D,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x5F,0x2D,0x3F,0x25,
0x3F,0x25,0x3F,0x25,0x9F,0x9E,0x7E,0xEF,0x5E,0xE7,0x5E,0xE7,0x9E,0xCE,0x9F,0x5B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xDF,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x3F,0x8F,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x9F,0x9E,0x7E,0xEF,0x5E,0xE7,0xDF,0x8C,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x7F,0x43,0x9F,0x34,0x00,0x00,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x66,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x7F,0x96,0xFF,0xA5,0x5F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x1F,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x1F,0x36,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,
0x3F,0x25,0x3F,0x25,0x3F,0x25,0xFF,0x3B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xFF,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x47,0x7F,0x36,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0xFF,0x3B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x5F,0x4B,0x1F,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x8D,0x21,0x8D,0x21,0x3B,0x86,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x47,0x5F,0x47,0x1F,0x47,0x1F,0x36,0x3F,0x25,0x3F,0x25,0x3F,0x25,0x3F,0x25,0xFF,0x3B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0x3F,0x4B,0xDF,0x3B,0x3F,0x34,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x1B,0x7E,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x47,0x5F,0x47,0x5F,0x47,0x5F,0x47,0x1F,0x47,
0x7F,0x36,0xFF,0x2D,0x5B,0x24,0xBD,0x33,0x7F,0x43,0xDF,0x3B,0x1F,0x3C,0x5F,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8D,0x21,0x8D,0x21,0x93,0x4B,0x9F,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0xBF,0x9F,0x9F,0x97,0x5F,0x47,0x5F,0x47,0x5F,0x47,0x5F,0x47,0x5F,0x47,0x5F,0x47,0xDB,0x3D,0x8A,0x10,0x4A,0x10,0x0B,0x21,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x6E,0x29,0x8D,0x21,0x8D,0x21,0xCE,0x29,0x6F,0x32,0x6F,0x32,0x6F,0x32,0x6F,0x32,0x6F,0x32,0x0E,0x32,0xAA,0x10,0xAA,0x10,0xAA,0x10,0xAA,0x10,0xAA,0x10,0x8A,0x10,0x6A,0x10,0x4A,0x10,0xAA,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8C,0x29,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x8D,0x21,0x6C,0x21,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x4A,0x10,0x4A,0x10,
0x4A,0x10,0x6A,0x10,0xCB,0x18,0x0C,0x21,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x19,0xA6,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0xB9,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0xDE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD3,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x31,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x88,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x88,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEB,0xB9,0x5E,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0xA7,0x0A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x47,0x00,0x00,0x00,0x00,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x6E,0x00,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x45,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF7,0x06,0x00,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9C,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x4A,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA9,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD9,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC8,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xAE,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x02,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xAE,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0x0E,0x00,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x60,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x8D,0x00,0x00,0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x68,0x00,0x00,0x00,0x00,0x00,
0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0xD4,0x8A,0x16,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x0B,0xDD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x19,0xA6,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0xB8,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_ble_app_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_ble_app_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_ble_app_icon_data};

