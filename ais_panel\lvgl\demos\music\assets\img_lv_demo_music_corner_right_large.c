#include "../lv_demo_music.h"
#if LV_USE_DEMO_MUSIC && LV_DEMO_MUSIC_LARGE



#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_LV_DEMO_MUSIC_CORNER_2
    #define LV_ATTRIBUTE_IMAGE_IMG_LV_DEMO_MUSIC_CORNER_2
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_LV_DEMO_MUSIC_CORNER_2 uint8_t
img_lv_demo_music_corner_right_map[] = {
    /*Pixel format:  Blue: 8 bit, Green: 8 bit, Red: 8 bit, Alpha: 8 bit*/
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x04,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x0b,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x0b, 0x47, 0x32, 0x34, 0x13,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x14, 0x47, 0x32, 0x34, 0x20,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x04, 0x47, 0x32, 0x34, 0x1f, 0x47, 0x32, 0x34, 0x2f,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x2c, 0x47, 0x32, 0x34, 0x44,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x08, 0x47, 0x32, 0x34, 0x3c, 0x47, 0x32, 0x34, 0x5c,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x0c, 0x47, 0x32, 0x34, 0x50, 0x47, 0x32, 0x34, 0x7b,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x0f, 0x47, 0x32, 0x34, 0x67, 0x47, 0x32, 0x34, 0x9c,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x13, 0x47, 0x32, 0x34, 0x7f, 0x47, 0x32, 0x34, 0xc3,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x20, 0x47, 0x32, 0x34, 0x98, 0x47, 0x32, 0x34, 0xe0,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x10, 0x47, 0x32, 0x34, 0x33, 0x47, 0x32, 0x34, 0xb0, 0x47, 0x32, 0x34, 0xfc,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x2b, 0x47, 0x32, 0x34, 0x63, 0x47, 0x32, 0x34, 0xc4, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x48, 0x47, 0x32, 0x34, 0x93, 0x47, 0x32, 0x34, 0xd7, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x6b, 0x47, 0x32, 0x34, 0xc8, 0x47, 0x32, 0x34, 0xeb, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x10, 0x47, 0x32, 0x34, 0x27, 0x47, 0x32, 0x34, 0x90, 0x47, 0x32, 0x34, 0xef, 0x47, 0x32, 0x34, 0xf8, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x30, 0x47, 0x32, 0x34, 0x74, 0x47, 0x32, 0x34, 0xbf, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xfc, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x14, 0x47, 0x32, 0x34, 0x5c, 0x47, 0x32, 0x34, 0xb7, 0x47, 0x32, 0x34, 0xe0, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x13, 0x47, 0x32, 0x34, 0x38, 0x47, 0x32, 0x34, 0x8c, 0x47, 0x32, 0x34, 0xef, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x0b, 0x47, 0x32, 0x34, 0x33, 0x47, 0x32, 0x34, 0x7c, 0x47, 0x32, 0x34, 0xbf, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x1b, 0x47, 0x32, 0x34, 0x5b, 0x47, 0x32, 0x34, 0xc4, 0x47, 0x32, 0x34, 0xef, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x13, 0x47, 0x32, 0x34, 0x30, 0x47, 0x32, 0x34, 0x74, 0x47, 0x32, 0x34, 0xb0, 0x47, 0x32, 0x34, 0xe4, 0x47, 0x32, 0x34, 0xf8, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x04, 0x47, 0x32, 0x34, 0x0b, 0x47, 0x32, 0x34, 0x33, 0x47, 0x32, 0x34, 0x68, 0x47, 0x32, 0x34, 0xc0, 0x47, 0x32, 0x34, 0xf3, 0x47, 0x32, 0x34, 0xfc, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x10, 0x47, 0x32, 0x34, 0x28, 0x47, 0x32, 0x34, 0x7b, 0x47, 0x32, 0x34, 0xc3, 0x47, 0x32, 0x34, 0xe7, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x0c, 0x47, 0x32, 0x34, 0x1f, 0x47, 0x32, 0x34, 0x43, 0x47, 0x32, 0x34, 0x6f, 0x47, 0x32, 0x34, 0xbb, 0x47, 0x32, 0x34, 0xf4, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x23, 0x47, 0x32, 0x34, 0x58, 0x47, 0x32, 0x34, 0x98, 0x47, 0x32, 0x34, 0xdb, 0x47, 0x32, 0x34, 0xef, 0x47, 0x32, 0x34, 0xfc, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x0c, 0x47, 0x32, 0x34, 0x23, 0x47, 0x32, 0x34, 0x47, 0x47, 0x32, 0x34, 0x74, 0x47, 0x32, 0x34, 0xa4, 0x47, 0x32, 0x34, 0xd0, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xfc, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x04, 0x47, 0x32, 0x34, 0x1f, 0x47, 0x32, 0x34, 0x4f, 0x47, 0x32, 0x34, 0xa7, 0x47, 0x32, 0x34, 0xdb, 0x47, 0x32, 0x34, 0xf7, 0x47, 0x32, 0x34, 0xfc, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x04, 0x47, 0x32, 0x34, 0x14, 0x47, 0x32, 0x34, 0x2b, 0x47, 0x32, 0x34, 0x47, 0x47, 0x32, 0x34, 0x63, 0x47, 0x32, 0x34, 0x87, 0x47, 0x32, 0x34, 0xac, 0x47, 0x32, 0x34, 0xd8, 0x47, 0x32, 0x34, 0xf0, 0x47, 0x32, 0x34, 0xfc, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x00, 0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x04, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x08, 0x47, 0x32, 0x34, 0x0b, 0x47, 0x32, 0x34, 0x0f, 0x47, 0x32, 0x34, 0x14, 0x47, 0x32, 0x34, 0x1c, 0x47, 0x32, 0x34, 0x3b, 0x47, 0x32, 0x34, 0x60, 0x47, 0x32, 0x34, 0x8f, 0x47, 0x32, 0x34, 0xbc, 0x47, 0x32, 0x34, 0xe3, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x03, 0x47, 0x32, 0x34, 0x07, 0x47, 0x32, 0x34, 0x0c, 0x47, 0x32, 0x34, 0x1b, 0x47, 0x32, 0x34, 0x28, 0x47, 0x32, 0x34, 0x3b, 0x47, 0x32, 0x34, 0x4c, 0x47, 0x32, 0x34, 0x67, 0x47, 0x32, 0x34, 0x80, 0x47, 0x32, 0x34, 0x9f, 0x47, 0x32, 0x34, 0xb0, 0x47, 0x32, 0x34, 0xc3, 0x47, 0x32, 0x34, 0xd4, 0x47, 0x32, 0x34, 0xe7, 0x47, 0x32, 0x34, 0xf4, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
    0x47, 0x32, 0x34, 0x04, 0x47, 0x32, 0x34, 0x0b, 0x47, 0x32, 0x34, 0x14, 0x47, 0x32, 0x34, 0x28, 0x47, 0x32, 0x34, 0x3c, 0x47, 0x32, 0x34, 0x58, 0x47, 0x32, 0x34, 0x74, 0x47, 0x32, 0x34, 0x9b, 0x47, 0x32, 0x34, 0xc3, 0x47, 0x32, 0x34, 0xec, 0x47, 0x32, 0x34, 0xfb, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff, 0x47, 0x32, 0x34, 0xff,
};

const lv_image_dsc_t img_lv_demo_music_corner_right = {
    .header.w = 32,
    .header.h = 32,
    .header.stride = 128,
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .data = img_lv_demo_music_corner_right_map,
    .data_size = sizeof(img_lv_demo_music_corner_right_map),
};

#endif

