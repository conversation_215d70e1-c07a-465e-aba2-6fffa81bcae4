#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: instagram.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_instagram_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4A,0xFF,0xC6,0xFE,0x85,0xFE,0x46,0xFE,0x26,0xFE,0xE6,0xFD,0xC7,0xFD,0x68,0xFD,0x49,0xFD,0x0A,0xFD,0xCB,0xFC,0xAD,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x46,0xFF,0xE5,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,
0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0xAD,0xFB,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0xFF,0x04,0xFF,0xC4,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0D,0xFB,0x70,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x49,0xFF,0x04,0xFF,0xC4,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0E,0xFB,0xAE,0xFA,0x6E,0xFA,0x50,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0xFF,0x03,0xFF,0xC4,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,
0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0E,0xFB,0xAE,0xFA,0x6E,0xFA,0x2F,0xFA,0xEF,0xF9,0x70,0xF9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x48,0xFF,0x03,0xFF,0xC4,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0E,0xFB,0xAE,0xFA,0x6E,0xFA,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0x90,0xF9,0xCF,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x6B,0xFF,0x04,0xFF,0xC4,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0E,0xFB,0xAE,0xFA,0x6E,0xFA,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0x90,0xF9,0x30,0xF1,0xF0,0xE8,0xD0,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xFF,0xC4,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0xAA,0xFD,0x30,0xFE,0x32,0xFE,0x13,0xFE,0xD3,0xFD,0xB4,0xFD,
0x94,0xFD,0x54,0xFD,0x34,0xFD,0x15,0xFD,0x94,0xFC,0xF0,0xFA,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0x90,0xF9,0x70,0xF1,0xF0,0xE8,0xF0,0xE0,0xD1,0xD8,0x00,0x00,0x00,0x00,0x00,0x00,0x46,0xFF,0xC4,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xC7,0xFD,0xD4,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0x18,0xFD,0xB0,0xF9,0x90,0xF9,0x91,0xF1,0x10,0xE9,0xF0,0xE0,0xF1,0xD8,0xF1,0xD8,0xF2,0xC8,0x00,0x00,
0x00,0x00,0xE5,0xFE,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0xD4,0xFE,0xFF,0xFF,0xBD,0xFF,0xD1,0xFD,0xCD,0xFC,0x6D,0xFC,0x2D,0xFC,0xEE,0xFB,0xAE,0xFB,0x4F,0xFB,0x0F,0xFB,0xF0,0xFA,0x34,0xFC,0x5E,0xFF,0xFF,0xFF,0xD8,0xFC,0x91,0xF1,0x50,0xE9,0xF0,0xE0,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0x00,0x00,0x2A,0xFF,0xA4,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0xAA,0xFD,0xFF,0xFF,0xBD,0xFF,0xEC,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,
0x0E,0xFB,0xAE,0xFA,0x6E,0xFA,0xB0,0xFA,0xF7,0xFC,0xD3,0xFA,0x5E,0xFF,0xDF,0xFF,0x12,0xF2,0xF0,0xE0,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xB3,0xC0,0xC6,0xFE,0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x30,0xFE,0xFF,0xFF,0xD1,0xFD,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0xAF,0xFB,0x73,0xFC,0x53,0xFC,0xF0,0xFA,0x2F,0xFA,0x56,0xFC,0xFF,0xFF,0xB8,0xFC,0xD6,0xF3,0xFF,0xFF,0x75,0xEB,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,
0x85,0xFE,0x45,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0x33,0xFE,0xFF,0xFF,0xCD,0xFC,0x2C,0xFC,0xEC,0xFB,0xAD,0xFB,0xB6,0xFD,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xF7,0xFC,0xF1,0xF9,0x57,0xFC,0x12,0xF2,0x32,0xF2,0xFF,0xFF,0xD7,0xEB,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0x66,0xFE,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0x13,0xFE,0xFF,0xFF,0x4D,0xFC,0xEC,0xFB,0x8D,0xFB,0x96,0xFD,0xFF,0xFF,0xD8,0xFD,0x92,0xFB,
0x72,0xFB,0xDA,0xFD,0xFF,0xFF,0x77,0xFC,0x91,0xF1,0x91,0xE9,0x92,0xE1,0xFF,0xFF,0xF7,0xEB,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0x26,0xFE,0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0xF4,0xFD,0xFF,0xFF,0x0D,0xFC,0x8D,0xFB,0x8E,0xFB,0xDF,0xFF,0xB8,0xFD,0x6E,0xFA,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0xBA,0xFD,0xBF,0xFF,0xD2,0xE9,0x71,0xE1,0x51,0xE1,0xFF,0xFF,0xF8,0xE3,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,
0xE6,0xFD,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0xD4,0xFD,0xFF,0xFF,0xCE,0xFB,0x4D,0xFB,0x32,0xFC,0xFF,0xFF,0x71,0xFB,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0x90,0xF9,0x55,0xF3,0xFF,0xFF,0xF4,0xEA,0xF1,0xD8,0x52,0xD9,0xFF,0xFF,0xF8,0xE3,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xA7,0xFD,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0x94,0xFD,0xFF,0xFF,0x8E,0xFB,0x0E,0xFB,0xF2,0xFB,0xFF,0xFF,0x32,0xFB,0xCF,0xF9,0x90,0xF9,
0x90,0xF9,0x91,0xF1,0x75,0xF3,0xFF,0xFF,0x74,0xE2,0xF1,0xD8,0x52,0xD1,0xFF,0xFF,0xF8,0xDB,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0x68,0xFD,0x29,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x75,0xFD,0xFF,0xFF,0x4E,0xFB,0xAE,0xFA,0xAF,0xFA,0xBF,0xFF,0x58,0xFD,0x90,0xF9,0x90,0xF9,0x91,0xF1,0x91,0xE9,0xDB,0xF5,0x9F,0xFF,0x12,0xD9,0xF2,0xD0,0x53,0xC9,0xFF,0xFF,0xF8,0xDB,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,
0x49,0xFD,0xE9,0xFC,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x55,0xFD,0xFF,0xFF,0x0F,0xFB,0x6E,0xFA,0x2F,0xFA,0xB7,0xFC,0xFF,0xFF,0x59,0xFD,0xF4,0xF2,0x35,0xF3,0xDB,0xF5,0xFF,0xFF,0xD7,0xEB,0xF2,0xD0,0xF2,0xC8,0x53,0xC9,0xFF,0xFF,0xF9,0xD3,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0x0A,0xFD,0xAA,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x15,0xFD,0xFF,0xFF,0xF0,0xFA,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0xB8,0xFC,0xDF,0xFF,0xFF,0xFF,
0xFF,0xFF,0x9F,0xFF,0x17,0xEC,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0x94,0xC1,0xFF,0xFF,0xD9,0xD3,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xCB,0xFC,0x6B,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0E,0xFB,0x94,0xFC,0xFF,0xFF,0x34,0xFC,0xCF,0xF9,0x90,0xF9,0x90,0xF9,0x91,0xF1,0x12,0xEA,0x55,0xEB,0xB5,0xE2,0x52,0xD9,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0x58,0xD3,0xFF,0xFF,0x58,0xC3,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xD8,0x78,
0xAD,0xFC,0x2C,0xFC,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0E,0xFB,0xAE,0xFA,0xF0,0xFA,0xDF,0xFF,0x5E,0xFF,0x11,0xFA,0x90,0xF9,0x91,0xF1,0x91,0xE9,0x71,0xE1,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0x75,0xB9,0x3E,0xF7,0xDF,0xFF,0x96,0xA9,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xBA,0x80,0x00,0x00,0xEC,0xFB,0x8D,0xFB,0x4D,0xFB,0x0E,0xFB,0xAE,0xFA,0x6E,0xFA,0x2F,0xFA,0x18,0xFD,0xFF,0xFF,0x5E,0xFF,0xD6,0xF3,0x32,0xF2,0xB2,0xE9,0x52,0xE1,0x52,0xD9,
0x53,0xD1,0x53,0xC9,0x54,0xC9,0x94,0xC1,0x58,0xD3,0x3E,0xF7,0xFF,0xFF,0x7A,0xD4,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0x00,0x00,0x00,0x00,0xAD,0xFB,0x4D,0xFB,0x0E,0xFB,0xAE,0xFA,0x6E,0xFA,0x2F,0xFA,0xCF,0xF9,0xB0,0xF9,0xD8,0xFC,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0x7A,0xD4,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xF9,0x70,0x00,0x00,
0x00,0x00,0x00,0x00,0x0D,0xFB,0xAE,0xFA,0x6E,0xFA,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0x90,0xF9,0x91,0xF1,0x12,0xEA,0x75,0xEB,0xB7,0xEB,0xB7,0xEB,0xB7,0xE3,0xB7,0xE3,0xB8,0xDB,0xB8,0xDB,0xB8,0xD3,0xB9,0xD3,0x58,0xCB,0x96,0xA9,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xD9,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xAE,0xFA,0x8E,0xFA,0x2F,0xFA,0xCF,0xF9,0x90,0xF9,0x90,0xF9,0x91,0xF1,0x50,0xE9,0xF0,0xE0,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,
0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xF9,0x70,0xD9,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4F,0xFA,0xEF,0xF9,0x90,0xF9,0x90,0xF9,0x70,0xF1,0x10,0xE9,0xF0,0xE0,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xF9,0x70,0xFA,0x68,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0xF9,0x90,0xF9,0x30,0xF1,0xF0,0xE8,0xF0,0xE0,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xF9,0x70,0xDA,0x68,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCF,0xF0,0xF0,0xE8,0xF0,0xE0,0xF1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,
0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xF9,0x70,0xFA,0x68,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD0,0xE0,0xD1,0xD8,0xF1,0xD8,0xF2,0xD0,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xD9,0x70,0xF8,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF2,0xC8,0xF2,0xC8,0xF3,0xC0,0xF3,0xB8,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xF8,0x78,0xF9,0x70,0xF9,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xB3,0xC0,0xF4,0xB8,0xF4,0xB0,0xF5,0xA8,0xF5,0xA0,0xF6,0x98,
0xF6,0x90,0xF7,0x90,0xF7,0x88,0xF8,0x80,0xD8,0x78,0xBA,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x16,0x65,0xA4,0xCF,0xEC,0xFA,0xFA,0xEC,0xCF,0xA2,0x62,0x11,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xA7,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF7,0xA5,0x2B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x99,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0x95,0x0A,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x23,0xCF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD3,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0xE8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE7,0x36,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xE8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEC,0x26,0x00,0x00,0x00,
0x00,0x00,0x0E,0xCE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD4,0x0A,0x00,0x00,0x00,0x00,0x99,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x84,0x00,0x00,0x00,0x31,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x1A,0x00,
0x00,0xA8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0x00,0x15,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x0D,0x65,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0xA4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC8,0xED,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xED,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xA2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0x62,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x56,
0x11,0xF6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x0C,0x00,0xA5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x97,0x00,0x00,0x2B,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x19,0x00,
0x00,0x00,0x95,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x82,0x00,0x00,0x00,0x00,0x09,0xCD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD3,0x0A,0x00,0x00,0x00,0x00,0x00,0x1F,0xE7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0x24,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x36,0xED,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0x32,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD2,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0A,0x84,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x83,0x09,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1A,0x98,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x97,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0D,0x57,0x99,0xC8,0xEA,0xF9,0xF9,0xE9,0xC8,0x98,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_instagram_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_instagram_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_instagram_icon_data};

