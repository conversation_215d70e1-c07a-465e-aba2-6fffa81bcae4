#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_RED_HEART
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_RED_HEART
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_RED_HEART uint8_t
img_emoji_red_heart_map[] = {
    0xff, 0xee, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xfb, 0xff, 0xee, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xf1, 0xfb, 0xff, 0xff, 0xfb, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xea, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfa, 0xf3, 0xfa, 0xff, 0xfb, 0xff, 0xf9, 0xff, 0xf8, 0xff, 0xf4, 0xff, 0xf6, 0xff, 0xee, 0xff, 0xfd, 0xfe, 0xf5, 0xff, 0xff, 0xf7, 0xfd, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xf8, 0xfe, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xf8, 0xfd, 0xff, 0xff, 0xd1, 0xd4, 0xff, 0xff, 0xa0, 0x9d, 0xff, 0xff, 0x97, 0x93, 0xfc, 0xff, 0x9e, 0xa2, 0xe3, 0xff, 0xca, 0xd0, 0xed, 0xff, 0xf8, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xe2, 0xff, 0xfd, 0xff, 0xf7, 0xff, 0xe9, 0xeb, 0xff, 0xff, 0xce, 0xce, 0xff, 0xff, 0xa8, 0xa9, 0xff, 0xff, 0x94, 0x98, 0xe6, 0xff, 0xb2, 0xb9, 0xf2, 0xff, 0xde, 0xe7, 0xff, 0xff, 0xed, 0xef, 0xf9, 0xff, 0xfd, 0xff, 0xff, 0xff,
    0xe3, 0xf6, 0xfe, 0xff, 0xa2, 0xab, 0xeb, 0xff, 0x66, 0x65, 0xe3, 0xff, 0x50, 0x4f, 0xed, 0xff, 0x47, 0x47, 0xed, 0xff, 0x3b, 0x3c, 0xd4, 0xff, 0x50, 0x5a, 0xc5, 0xff, 0x86, 0x9a, 0xd4, 0xff, 0xea, 0xfc, 0xd3, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xab, 0xae, 0xf8, 0xff, 0x62, 0x61, 0xe7, 0xff, 0x42, 0x44, 0xe9, 0xff, 0x38, 0x42, 0xe3, 0xff, 0x40, 0x54, 0xdb, 0xff, 0x2c, 0x4a, 0xbb, 0xff, 0xbb, 0xb6, 0xdd, 0xff, 0xfa, 0xf9, 0xff, 0xff,
    0xc9, 0xd3, 0xff, 0xff, 0x5b, 0x5c, 0xdc, 0xff, 0x58, 0x5c, 0xf0, 0xff, 0x7c, 0x8f, 0xff, 0xff, 0x59, 0x6d, 0xff, 0xff, 0x3b, 0x40, 0xf9, 0xff, 0x38, 0x3a, 0xf2, 0xff, 0x31, 0x3e, 0xd0, 0xff, 0xb7, 0xbd, 0xff, 0xff, 0xa8, 0xaf, 0xff, 0xff, 0x5f, 0x6a, 0xd2, 0xff, 0x67, 0x76, 0xfa, 0xff, 0x72, 0x83, 0xff, 0xff, 0x3b, 0x4a, 0xfe, 0xff, 0x2c, 0x3b, 0xfb, 0xff, 0x29, 0x36, 0xfc, 0xff, 0x5e, 0x55, 0xb6, 0xff, 0xba, 0xb7, 0xff, 0xff,
    0x8b, 0x8c, 0xff, 0xff, 0x4f, 0x4d, 0xe3, 0xff, 0x70, 0x78, 0xff, 0xff, 0x7c, 0x97, 0xff, 0xff, 0x44, 0x60, 0xdd, 0xff, 0x33, 0x3b, 0xf3, 0xff, 0x35, 0x32, 0xff, 0xff, 0x2e, 0x2f, 0xef, 0xff, 0x3e, 0x3e, 0xe6, 0xff, 0x55, 0x5a, 0xf5, 0xff, 0x3b, 0x4a, 0xd6, 0xff, 0x5f, 0x74, 0xf8, 0xff, 0x6e, 0x82, 0xff, 0xff, 0x3a, 0x46, 0xea, 0xff, 0x3b, 0x3c, 0xfc, 0xff, 0x45, 0x3f, 0xff, 0xff, 0x33, 0x29, 0xc9, 0xff, 0x79, 0x74, 0xf7, 0xff,
    0x62, 0x6a, 0xe1, 0xff, 0x73, 0x79, 0xff, 0xff, 0x7e, 0x86, 0xff, 0xff, 0x4c, 0x5b, 0xed, 0xff, 0x37, 0x46, 0xe3, 0xff, 0x44, 0x4b, 0xff, 0xff, 0x36, 0x3a, 0xf7, 0xff, 0x47, 0x4c, 0xff, 0xff, 0x19, 0x21, 0xe0, 0xff, 0x54, 0x5e, 0xff, 0xff, 0x4f, 0x59, 0xff, 0xff, 0x41, 0x4c, 0xf4, 0xff, 0x43, 0x4c, 0xf0, 0xff, 0x42, 0x49, 0xec, 0xff, 0x49, 0x4d, 0xf2, 0xff, 0x36, 0x37, 0xdf, 0xff, 0x3e, 0x37, 0xec, 0xff, 0x5e, 0x5b, 0xf7, 0xff,
    0x52, 0x63, 0xe8, 0xff, 0x58, 0x6c, 0xef, 0xff, 0x59, 0x67, 0xff, 0xff, 0x2c, 0x31, 0xea, 0xff, 0x32, 0x37, 0xf8, 0xff, 0x45, 0x4f, 0xff, 0xff, 0x27, 0x34, 0xdc, 0xff, 0x3a, 0x42, 0xf3, 0xff, 0x31, 0x41, 0xee, 0xff, 0x2a, 0x38, 0xea, 0xff, 0x2a, 0x34, 0xec, 0xff, 0x3a, 0x41, 0xfc, 0xff, 0x3b, 0x43, 0xfb, 0xff, 0x2e, 0x38, 0xe9, 0xff, 0x2e, 0x3e, 0xe4, 0xff, 0x3a, 0x4d, 0xec, 0xff, 0x3f, 0x3c, 0xd8, 0xff, 0x57, 0x58, 0xe4, 0xff,
    0x63, 0x71, 0xff, 0xff, 0x2f, 0x4a, 0xd7, 0xff, 0x43, 0x5b, 0xf1, 0xff, 0x3e, 0x48, 0xff, 0xff, 0x30, 0x38, 0xff, 0xff, 0x38, 0x4a, 0xf1, 0xff, 0x29, 0x3b, 0xe2, 0xff, 0x3f, 0x43, 0xff, 0xff, 0x3f, 0x4c, 0xf4, 0xff, 0x32, 0x3f, 0xe9, 0xff, 0x3c, 0x4a, 0xf6, 0xff, 0x3a, 0x49, 0xf9, 0xff, 0x2f, 0x40, 0xf4, 0xff, 0x2b, 0x40, 0xf9, 0xff, 0x24, 0x3f, 0xfb, 0xff, 0x28, 0x45, 0xff, 0xff, 0x3f, 0x41, 0xbe, 0xff, 0x6e, 0x70, 0xe0, 0xff,
    0x9e, 0xaf, 0xff, 0xff, 0x43, 0x47, 0xd5, 0xff, 0x38, 0x33, 0xfa, 0xff, 0x49, 0x4c, 0xff, 0xff, 0x28, 0x36, 0xed, 0xff, 0x2f, 0x3d, 0xe9, 0xff, 0x4d, 0x54, 0xff, 0xff, 0x3a, 0x38, 0xef, 0xff, 0x2f, 0x43, 0xe8, 0xff, 0x3e, 0x4d, 0xfd, 0xff, 0x2c, 0x34, 0xec, 0xff, 0x2b, 0x2e, 0xe7, 0xff, 0x49, 0x4e, 0xff, 0xff, 0x41, 0x4a, 0xf8, 0xff, 0x2b, 0x38, 0xe8, 0xff, 0x3b, 0x49, 0xfd, 0xff, 0x3b, 0x31, 0xb3, 0xff, 0xb5, 0xb2, 0xff, 0xff,
    0xd5, 0xee, 0xfe, 0xff, 0x74, 0x73, 0xf3, 0xff, 0x38, 0x2d, 0xf9, 0xff, 0x38, 0x44, 0xfc, 0xff, 0x36, 0x50, 0xf0, 0xff, 0x2e, 0x40, 0xf1, 0xff, 0x37, 0x3d, 0xfa, 0xff, 0x3f, 0x45, 0xf2, 0xff, 0x3b, 0x44, 0xff, 0xff, 0x2d, 0x38, 0xed, 0xff, 0x37, 0x48, 0xf3, 0xff, 0x44, 0x55, 0xff, 0xff, 0x34, 0x42, 0xef, 0xff, 0x2b, 0x35, 0xe7, 0xff, 0x37, 0x3d, 0xee, 0xff, 0x3b, 0x40, 0xef, 0xff, 0x6c, 0x67, 0xca, 0xff, 0xd8, 0xd9, 0xff, 0xff,
    0xf8, 0xff, 0xf4, 0xff, 0xb8, 0xb8, 0xfe, 0xff, 0x50, 0x4d, 0xe9, 0xff, 0x25, 0x30, 0xe6, 0xff, 0x34, 0x4e, 0xf9, 0xff, 0x30, 0x4a, 0xf4, 0xff, 0x28, 0x38, 0xe9, 0xff, 0x3d, 0x45, 0xfd, 0xff, 0x3c, 0x38, 0xff, 0xff, 0x30, 0x3b, 0xf1, 0xff, 0x32, 0x4c, 0xec, 0xff, 0x32, 0x4f, 0xf0, 0xff, 0x2a, 0x3e, 0xf1, 0xff, 0x38, 0x3d, 0xf6, 0xff, 0x45, 0x42, 0xeb, 0xff, 0x3a, 0x37, 0xc9, 0xff, 0xb4, 0xb6, 0xec, 0xff, 0xf2, 0xf8, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xef, 0xfc, 0xf4, 0xff, 0x8f, 0xa1, 0xdc, 0xff, 0x35, 0x39, 0xef, 0xff, 0x29, 0x32, 0xff, 0xff, 0x2f, 0x4e, 0xf1, 0xff, 0x2c, 0x48, 0xe3, 0xff, 0x38, 0x3c, 0xff, 0xff, 0x36, 0x32, 0xf5, 0xff, 0x41, 0x4c, 0xfa, 0xff, 0x27, 0x42, 0xe6, 0xff, 0x1e, 0x3a, 0xec, 0xff, 0x39, 0x46, 0xff, 0xff, 0x3d, 0x3d, 0xf5, 0xff, 0x4e, 0x49, 0xce, 0xff, 0x81, 0x7e, 0xd3, 0xff, 0xf2, 0xf7, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xec, 0xff, 0xff, 0xff, 0xff, 0xeb, 0xff, 0xd8, 0xf1, 0xed, 0xff, 0x7d, 0x83, 0xff, 0xff, 0x3f, 0x3a, 0xff, 0xff, 0x2e, 0x3c, 0xf3, 0xff, 0x32, 0x48, 0xed, 0xff, 0x33, 0x40, 0xff, 0xff, 0x3b, 0x42, 0xe5, 0xff, 0x3e, 0x4d, 0xf0, 0xff, 0x2d, 0x41, 0xf2, 0xff, 0x2e, 0x3e, 0xff, 0xff, 0x3b, 0x3f, 0xff, 0xff, 0x3a, 0x36, 0xd5, 0xff, 0x7a, 0x79, 0xc9, 0xff, 0xf1, 0xf5, 0xff, 0xff, 0xfb, 0xfe, 0xff, 0xff, 0xfb, 0xff, 0xfa, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xd0, 0xdc, 0xff, 0xff, 0x7f, 0x7e, 0xf6, 0xff, 0x42, 0x3d, 0xf7, 0xff, 0x30, 0x36, 0xf3, 0xff, 0x2e, 0x47, 0xe3, 0xff, 0x3c, 0x4e, 0xe1, 0xff, 0x36, 0x44, 0xea, 0xff, 0x38, 0x41, 0xfd, 0xff, 0x35, 0x37, 0xf4, 0xff, 0x39, 0x39, 0xd9, 0xff, 0x7a, 0x7a, 0xde, 0xff, 0xd7, 0xda, 0xf9, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xf8, 0xf8, 0xf8, 0xff,
    0xf8, 0xff, 0xf5, 0xff, 0xfc, 0xf9, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xd6, 0xdd, 0xf1, 0xff, 0x87, 0x87, 0xf9, 0xff, 0x47, 0x4c, 0xf5, 0xff, 0x2e, 0x46, 0xe6, 0xff, 0x32, 0x47, 0xe9, 0xff, 0x35, 0x40, 0xf6, 0xff, 0x3d, 0x3c, 0xf6, 0xff, 0x47, 0x47, 0xdb, 0xff, 0x7b, 0x82, 0xd2, 0xff, 0xda, 0xe5, 0xfb, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf8, 0xf3, 0xf5, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xf1, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xea, 0xff, 0xd7, 0xe7, 0xfe, 0xff, 0x6e, 0x7d, 0xf7, 0xff, 0x39, 0x42, 0xff, 0xff, 0x2a, 0x3e, 0xf7, 0xff, 0x37, 0x3c, 0xff, 0xff, 0x3e, 0x39, 0xee, 0xff, 0x7c, 0x7e, 0xea, 0xff, 0xe0, 0xef, 0xff, 0xff, 0xf8, 0xff, 0xeb, 0xff, 0xf8, 0xfc, 0xf1, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xfd, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xfa, 0xf9, 0xfd, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xf9, 0xfa, 0xf6, 0xff, 0xfd, 0xfc, 0xfe, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xea, 0xe1, 0xfc, 0xff, 0xc7, 0xbb, 0xdf, 0xff, 0x4c, 0x47, 0xe0, 0xff, 0x58, 0x53, 0xcc, 0xff, 0xa6, 0xa3, 0xeb, 0xff, 0xf6, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xfa, 0xf9, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xf9, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xfa, 0xfb, 0xf7, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xf5, 0xec, 0xff, 0xff, 0xb5, 0xba, 0xff, 0xff, 0xb2, 0xb6, 0xfd, 0xff, 0xde, 0xe0, 0xff, 0xff, 0xfb, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xf9, 0xff, 0xfb, 0xfc, 0xf8, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xec, 0xfc, 0xff, 0xff, 0xed, 0xf9, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xfd, 0xf9, 0xf4, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
};

const lv_image_dsc_t img_emoji_red_heart = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_red_heart_map,
    .data_size = sizeof(img_emoji_red_heart_map),
};

#endif
