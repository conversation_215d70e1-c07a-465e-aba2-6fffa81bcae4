#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: battery_state.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_battery_state_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0xE6,0x21,0xF6,0x82,0xDD,0x04,0xC5,0xC4,0xB4,0x04,0xC5,0x62,0xD5,0x21,0xF6,0xE2,0xED,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xFF,0xC2,0xE5,0x65,0xA4,0xA8,0x83,0x8A,0x7B,0x8B,0x7B,0xCD,0x7B,0x8B,0x7B,0x8A,0x7B,0xA9,0x83,0x46,0x9C,0xA2,0xDD,0xE0,0xFD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x83,0xDD,0xA8,0x83,0x2D,0x63,0xEE,0x5A,0x93,0x8C,0x9E,0xF7,0xFF,0xFF,0xBE,0xF7,0xD4,0x9C,0xEE,0x5A,0x0D,0x63,0xA9,0x83,0x63,0xCD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE2,0xED,0x67,0xA4,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x19,0xC6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7A,0xCE,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x28,0x94,
0x01,0xF6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE5,0xBC,0x29,0x94,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xF4,0x9C,0x18,0xC6,0x59,0xC6,0x18,0xC6,0x55,0xA5,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCB,0x83,0x63,0xD5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xE9,0x8B,0x06,0xBD,0x2D,0x6B,0xEE,0x5A,0xEE,0x5A,0x2E,0x63,0x73,0x8C,0xF4,0x9C,0x93,0x8C,0x2F,0x63,0xEE,0x5A,0xEE,0x5A,0x0D,0x63,0x05,0xC5,0xA5,0xB4,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x8C,0x63,0xD5,0x05,0xBD,0xEA,0x8B,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xAB,0x7B,0x25,0xC5,0x43,0xCD,
0xA5,0xAC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x94,0x83,0xD5,0x83,0xD5,0x44,0xC5,0x06,0xBD,0xA7,0xAC,0x87,0xA4,0x68,0xA4,0x88,0xA4,0xA7,0xAC,0xE6,0xBC,0x24,0xC5,0x63,0xCD,0x23,0xC5,0xA5,0xAC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x08,0x94,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x63,0xCD,0x23,0xC5,0xA5,0xAC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x28,0x94,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0xA4,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x63,0xCD,0x23,0xC5,
0xA5,0xAC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x28,0x94,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0xC6,0xDD,0xE8,0xDD,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x63,0xCD,0x23,0xC5,0xA5,0xAC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xC9,0x83,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0xD3,0xE6,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x63,0xCD,0x23,0xC5,0xC7,0x83,0x07,0x94,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xED,0x5A,0xE5,0xBC,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x2B,0xDE,0xB3,0xE6,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x43,0xCD,0x23,0xC5,
0x6B,0x4A,0x6B,0x4A,0x68,0x7B,0x26,0x9C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x52,0x2D,0x63,0xE6,0xB4,0x83,0xD5,0x83,0xD5,0xA4,0xD5,0x5A,0xEF,0x0A,0xDE,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x83,0xD5,0x04,0xBD,0x2A,0x6B,0x6B,0x4A,0x4B,0x42,0x4B,0x42,0x8A,0x4A,0xA8,0x83,0x04,0xA4,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xCD,0x52,0xEE,0x5A,0xEE,0x5A,0xAB,0x7B,0x87,0xAC,0x72,0xDE,0x7D,0xEF,0x4C,0xDE,0x2C,0xDE,0xED,0xD5,0xA7,0xAC,0xAA,0x83,0xCD,0x52,0xAC,0x52,0x6B,0x4A,0x6B,0x4A,0xE7,0x8B,0x83,0xD5,0x01,0xF6,0xE2,0xED,0x01,0xF6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x52,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x0E,0x5B,0xF5,0x9C,0x15,0x9D,0x76,0xAD,0x9E,0xEF,0x56,0xAD,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,
0x8B,0x52,0x24,0xC5,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x22,0xF6,0x01,0xF6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x15,0xA5,0x7A,0xCE,0x0E,0x5B,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,0x66,0xA4,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x01,0xF6,0x42,0xEE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xCD,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x0E,0x5B,0xBB,0xD6,0x6F,0x6B,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,0x24,0xCD,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0xC2,0xDD,0x63,0xD5,0x23,0xCD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x52,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xB4,0x94,0x32,0x84,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,
0x24,0xCD,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0xE2,0xE5,0x47,0x9C,0x4C,0x6B,0xEE,0x5A,0x6B,0x73,0xA4,0xB4,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x73,0x8C,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,0x24,0xCD,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0xA2,0xDD,0xC9,0x83,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xE7,0x8B,0xC3,0xCC,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xCD,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x2E,0x63,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,0x44,0xCD,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x02,0xEE,0x08,0x8C,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xA8,0x83,0x23,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x52,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,
0x44,0xCD,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x01,0xF6,0x86,0xAC,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xA8,0x83,0x43,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x52,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,0x44,0xCD,0x21,0xF6,0x21,0xF6,0x21,0xF6,0xC2,0xE5,0x6C,0x73,0xEE,0x5A,0xB0,0x73,0x9A,0xCE,0xB3,0x94,0xEE,0x5A,0xEE,0x5A,0xC7,0x8B,0xE4,0xBC,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xCD,0x52,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,0x44,0xCD,0x21,0xF6,0x21,0xF6,0x21,0xF6,0x86,0xAC,0xEE,0x5A,0xEE,0x5A,0xFC,0xDE,0xB7,0xB5,0x38,0xC6,0xB0,0x73,0x4C,0x6B,0x45,0x9C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCD,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xED,0x5A,0xAC,0x52,0xAC,0x52,
0x44,0xCD,0x21,0xF6,0x21,0xF6,0x02,0xEE,0xC9,0x83,0xEE,0x5A,0x2F,0x63,0xFB,0xDE,0x38,0xC6,0x18,0xBE,0x2E,0x63,0x69,0x7B,0x23,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xEE,0x52,0xED,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xCD,0x52,0xAC,0x52,0xAC,0x52,0xCC,0x52,0xC2,0xE5,0x21,0xF6,0x21,0xF6,0xC2,0xE5,0x6B,0x73,0xEE,0x5A,0xEE,0x5A,0xD4,0x9C,0xB7,0xB5,0xF1,0x7B,0x4C,0x6B,0x25,0x9C,0xE0,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xAC,0x52,0xAD,0x52,0xCD,0x52,0xCD,0x52,0xCD,0x5A,0xCD,0x52,0xCD,0x52,0xCD,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0x6A,0x73,0x21,0xF6,0x21,0xF6,0x21,0xF6,0xC2,0xDD,0x4C,0x6B,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x0D,0x63,0xA7,0x83,0x23,0xCD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0x00,0x00,
0xE0,0xFF,0x22,0xF6,0x21,0xF6,0xE2,0xE5,0x8B,0x7B,0xEE,0x5A,0xEE,0x5A,0xEE,0x5A,0x0D,0x63,0x68,0x7B,0xE3,0xBC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x8C,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0xAC,0x52,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0xEE,0x21,0xF6,0x86,0xAC,0x0D,0x63,0x0D,0x63,0x6A,0x73,0xC7,0x83,0x03,0xBD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0xCD,0xA4,0xAC,0x25,0x9C,0xA3,0xB4,0x23,0xCD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0A,0x54,0x93,0xB7,0xCF,0xBB,0x99,0x5B,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x6F,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x84,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x8D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xAC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x47,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x6D,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEC,0xA1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0xFF,0xA2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x73,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x77,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x72,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x64,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x56,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD7,0x2A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x57,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xC0,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x57,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD9,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xB3,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x89,0x0E,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x5A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0xC9,0xB4,0x5A,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD2,0xEA,0xFF,0xFF,0xFF,0x8E,0x00,0x00,0x00,0x00,0x00,0x5D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCB,0xFC,0xFF,0xFF,0xFF,0xFF,0xFA,0x0A,0x00,0x00,
0x00,0x00,0x5D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0xF8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x00,0x00,0x00,0x00,0x5F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x29,0x00,0x00,0x00,0x00,0x60,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x08,0x00,0x00,
0x00,0x00,0x61,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x00,0x00,0x00,0x00,0x00,0x5D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x47,0x00,0x00,0x00,0x00,0x00,0x1B,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDD,0x01,0x00,0x00,0x00,
0x00,0x00,0x00,0x5F,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x8F,0xCA,0xFF,0xFF,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x2A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCF,0x49,0x00,0x02,0x60,0xE7,0xD7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x61,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0x5B,0x77,0x8E,0x7A,0x5D,0x29,0x00,0x00,0x00,0x00,0x00,0x0E,0x88,0xE1,0xFF,0xFF,0xFF,0xF8,0x5C,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xA5,0xCA,0x8C,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_battery_state_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_battery_state_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_battery_state_icon_data};

