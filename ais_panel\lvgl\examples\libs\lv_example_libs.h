/**
 * @file lv_example_libs.h
 *
 */

#ifndef LV_EXAMPLE_LIBS_H
#define LV_EXAMPLE_LIBS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "barcode/lv_example_barcode.h"
#include "bmp/lv_example_bmp.h"
#include "ffmpeg/lv_example_ffmpeg.h"
#include "freetype/lv_example_freetype.h"
#include "gif/lv_example_gif.h"
#include "lodepng/lv_example_lodepng.h"
#include "libpng/lv_example_libpng.h"
#include "qrcode/lv_example_qrcode.h"
#include "rlottie/lv_example_rlottie.h"
#include "tjpgd/lv_example_tjpgd.h"
#include "libjpeg_turbo/lv_example_libjpeg_turbo.h"
#include "tiny_ttf/lv_example_tiny_ttf.h"
#include "svg/lv_example_svg.h"

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_LIBS_H*/
