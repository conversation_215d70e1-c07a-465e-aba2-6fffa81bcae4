#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: chat.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_chat_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x66,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x3F,0x67,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x5F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xFF,0x87,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0xFF,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x87,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x7E,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3F,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3F,0x3E,0x3E,0x36,0xFF,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3F,0x3E,0x3E,0x36,0x1E,0x36,0x35,0x4E,0x74,0x76,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x66,0x5F,0xC7,0x9F,0xD7,0xFF,0x6E,0xDF,0x5E,0xFF,0xA6,0x9F,0xDF,0xDF,0x7E,0x9F,0x4E,0xBE,0x7E,0x9F,0xDF,0xDE,0x8E,0x3F,0x3E,0x3E,0x36,0x1E,0x36,0xFE,0x2D,0x15,0x3E,0x74,0x5E,0xD6,0x76,0x00,0x00,0x00,0x00,
0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x8E,0xFF,0xFF,0xFF,0xFF,0x1F,0xA7,0x9E,0x5E,0xFF,0xFF,0xFF,0xFF,0x7F,0xCF,0x3E,0x3E,0xBF,0xE7,0xFF,0xFF,0xFF,0xFF,0xDE,0x2D,0x1E,0x36,0xFE,0x2D,0xFE,0x2D,0x56,0x4E,0xB4,0x66,0xF5,0x76,0x36,0x87,0x00,0x00,0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x6E,0xDF,0xEF,0xFF,0xFF,0x9E,0x6E,0x3E,0x46,0x7F,0xCF,0xFF,0xFF,0xDF,0x8E,0xFE,0x2D,0xFF,0xA6,0xFF,0xFF,0x3F,0xBF,0x9E,0x25,0xDE,0x2D,0xFE,0x2D,0xDE,0x25,0x96,0x5E,0xF5,0x76,0x16,0x7F,0x37,0x87,0xF0,0x87,
0x1F,0x67,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xBF,0x56,0x3E,0x46,0x3E,0x3E,0x5E,0x3E,0x5F,0x46,0x1E,0x3E,0xFE,0x2D,0x1E,0x36,0x1F,0x36,0xDE,0x35,0x9E,0x25,0xBE,0x25,0xBE,0x25,0xDE,0x25,0xDE,0x25,0xF5,0x76,0x16,0x7F,0x36,0x87,0x57,0x8F,0x57,0x8F,0x1F,0x67,0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0x9F,0x4E,0x5E,0x46,0x7F,0x46,0x7F,0x46,0x5E,0x3E,0x1E,0x36,0x1E,0x36,0x1E,0x36,0x1E,0x36,0xDE,0x25,0xBE,0x25,0xDE,0x25,0xBE,0x25,0x1B,0x3E,0x16,0x7F,0x36,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,
0x1F,0x67,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3E,0x3E,0x1E,0x36,0x1E,0x36,0xFE,0x2D,0xFE,0x2D,0xDE,0x25,0xBE,0x25,0xBE,0x1D,0xD8,0x6E,0x36,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0xFF,0x5E,0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3F,0x3E,0x3E,0x36,0x1E,0x36,0xFE,0x2D,0xFE,0x2D,0xDE,0x25,0xBE,0x25,0xBE,0x1D,0x3B,0x3E,0x36,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,
0xDF,0x5E,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3F,0x3E,0x3E,0x36,0x1E,0x36,0xFE,0x2D,0xFE,0x2D,0xDE,0x25,0xBE,0x25,0xBE,0x1D,0xDD,0x2D,0x37,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,0x36,0x87,0xDF,0x56,0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3F,0x3E,0x3E,0x36,0x1E,0x36,0xFE,0x2D,0xFE,0x2D,0xDE,0x25,0xBE,0x25,0xBE,0x1D,0xFC,0x35,0x17,0x7F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,0x36,0x87,0x36,0x87,
0xBF,0x56,0x9F,0x4E,0x9F,0x4E,0x7F,0x46,0x7F,0x46,0x5F,0x3E,0x3F,0x3E,0x3E,0x36,0x1E,0x36,0xFE,0x2D,0xFE,0x2D,0xDE,0x25,0xBE,0x25,0xDD,0x25,0x79,0x56,0x37,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,0x36,0x87,0x36,0x87,0x35,0x7F,0x00,0x00,0x7E,0x46,0x5F,0x46,0x5F,0x46,0x3F,0x46,0x3E,0x3E,0x3E,0x36,0x1E,0x36,0x1E,0x2E,0x1B,0x36,0x1B,0x36,0x3A,0x3E,0x98,0x5E,0x16,0x7F,0x36,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,0x36,0x87,0x36,0x87,0x35,0x7F,0x35,0x7F,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x76,0xB4,0x66,0xF5,0x76,0x16,0x7F,0x36,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,0x36,0x87,0x36,0x87,0x35,0x7F,0x35,0x7F,0x15,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x36,0x7F,0x16,0x7F,0x36,0x87,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,0x36,0x87,0x36,0x87,0x35,0x7F,0x35,0x7F,0x15,0x7F,0x15,0x77,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x16,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x57,0x8F,0x56,0x87,0x36,0x87,0x36,0x87,0x35,0x7F,0x35,0x7F,0x15,0x7F,0x15,0x77,0x14,0x77,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x36,0x97,0x56,0x8F,0x56,0x8F,0x36,0x87,0x36,0x87,0x36,0x87,0x35,0x7F,0xF5,0x57,0x00,0x00,0x00,0x00,0x00,0x00,


0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x47,0x6E,0x82,0x74,0x55,0x14,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x19,0x93,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0xB5,0x32,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4D,0xF0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x84,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA0,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x37,0xF8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x75,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF7,0x1E,0x00,0x00,0x00,0x00,0x00,0x49,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x91,0x00,0x00,0x00,0x00,0x00,0x9E,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x01,0x00,0x00,0x00,0x00,
0xD5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x19,0x00,0x00,0x00,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF1,0x47,0x00,0x00,0xF1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF6,0x38,0x00,0xD7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD7,0x02,
0xB8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x58,0x8D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xAF,0x6D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x5D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,
0x56,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x56,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x42,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9C,0x00,0x19,0x22,0x22,0x24,0x37,0x5A,0x85,0xA2,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x82,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x76,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x55,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x75,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xAE,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x5D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0x59,0x7C,0x7B,0x60,0x3A,0x15,0x03,0x00,0x00,0x00,


}; // LVGL_9 compatible
const lv_img_dsc_t img_chat_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 24,
   .header.h = 24,
   .data_size = sizeof(img_chat_icon_data),
   .header.cf = LV_COLOR_FORMAT_NATIVE_WITH_ALPHA,
   .data = img_chat_icon_data};

