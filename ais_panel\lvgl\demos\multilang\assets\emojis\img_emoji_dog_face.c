#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_DOG_FACE
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_DOG_FACE
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_DOG_FACE uint8_t
img_emoji_dog_face_map[] = {
    0xfb, 0xfd, 0xff, 0xff, 0xee, 0xf1, 0xf6, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xf0, 0xf2, 0xec, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xfe, 0xff, 0xfa, 0xff, 0xf8, 0xfc, 0xf6, 0xff, 0xfa, 0xff, 0xfc, 0xff, 0xee, 0xfa, 0xf4, 0xff, 0xf0, 0xfe, 0xf8, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xf5, 0xf7, 0xf7, 0xff, 0xf7, 0xf9, 0xf9, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xf6, 0xfa, 0xf5, 0xff, 0xf8, 0xfc, 0xf7, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xf6, 0xfa, 0xf5, 0xff, 0xf7, 0xfa, 0xf8, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfc, 0xfd, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xfd, 0xff, 0xfb, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xfb, 0xff, 0xfc, 0xff, 0xec, 0xf3, 0xf0, 0xff, 0xf7, 0xff, 0xfd, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xf0, 0xfd, 0xfb, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xee, 0xf7, 0xfb, 0xff, 0xf4, 0xfa, 0xff, 0xff, 0xf5, 0xf8, 0xff, 0xff, 0xfb, 0xfd, 0xff, 0xff, 0xfb, 0xfa, 0xff, 0xff, 0xf1, 0xed, 0xf9, 0xff, 0xfe, 0xf7, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xf8, 0xf9, 0xf0, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xfd, 0xfe, 0xff, 0xff, 0xe5, 0xe8, 0xed, 0xff, 0xc6, 0xcb, 0xd4, 0xff, 0xa7, 0xb0, 0xba, 0xff, 0xaa, 0xb6, 0xc2, 0xff, 0xdb, 0xeb, 0xf7, 0xff, 0xd6, 0xea, 0xf5, 0xff, 0xdf, 0xf6, 0xfe, 0xff, 0xdc, 0xf2, 0xfd, 0xff, 0xe1, 0xf7, 0xff, 0xff, 0xd1, 0xe7, 0xf3, 0xff, 0xdf, 0xef, 0xff, 0xff, 0xc5, 0xd1, 0xe3, 0xff, 0x8c, 0x91, 0xa6, 0xff, 0x91, 0x8e, 0xa7, 0xff, 0x9c, 0x97, 0xb2, 0xff, 0xd0, 0xd4, 0xd5, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xec, 0xed, 0xff, 0xff, 0x92, 0x93, 0xad, 0xff, 0x6b, 0x6f, 0x8c, 0xff, 0x57, 0x60, 0x81, 0xff, 0x4e, 0x5a, 0x7c, 0xff, 0xa8, 0xbc, 0xdb, 0xff, 0xc4, 0xdd, 0xf7, 0xff, 0xd0, 0xed, 0xff, 0xff, 0xc6, 0xed, 0xfc, 0xff, 0xc8, 0xef, 0xfe, 0xff, 0xb8, 0xe0, 0xf2, 0xff, 0xcb, 0xf0, 0xff, 0xff, 0xa9, 0xc7, 0xe0, 0xff, 0x51, 0x66, 0x82, 0xff, 0x44, 0x4f, 0x6f, 0xff, 0x5a, 0x5e, 0x81, 0xff, 0x7f, 0x84, 0x99, 0xff, 0xc2, 0xc7, 0xdc, 0xff,
    0xbc, 0xbf, 0xe5, 0xff, 0x58, 0x5c, 0x85, 0xff, 0x49, 0x4f, 0x7e, 0xff, 0x54, 0x5e, 0x8e, 0xff, 0x4b, 0x59, 0x89, 0xff, 0xb6, 0xcc, 0xf5, 0xff, 0xce, 0xea, 0xff, 0xff, 0xcf, 0xed, 0xff, 0xff, 0xbb, 0xe7, 0xf8, 0xff, 0xbb, 0xe9, 0xfb, 0xff, 0xb7, 0xe6, 0xfb, 0xff, 0xcc, 0xfa, 0xff, 0xff, 0xbb, 0xe3, 0xff, 0xff, 0x6d, 0x8b, 0xae, 0xff, 0x4b, 0x5e, 0x84, 0xff, 0x60, 0x6c, 0x96, 0xff, 0x4b, 0x51, 0x7a, 0xff, 0x91, 0x97, 0xc0, 0xff,
    0x86, 0x92, 0xbc, 0xff, 0x45, 0x52, 0x80, 0xff, 0x46, 0x54, 0x85, 0xff, 0x58, 0x66, 0x9a, 0xff, 0x5b, 0x6f, 0x9e, 0xff, 0xcc, 0xe6, 0xff, 0xff, 0xc8, 0xe7, 0xff, 0xff, 0xc5, 0xe9, 0xfb, 0xff, 0xcc, 0xed, 0xff, 0xff, 0xc8, 0xed, 0xff, 0xff, 0xc0, 0xe9, 0xff, 0xff, 0xbc, 0xe7, 0xff, 0xff, 0xba, 0xdf, 0xff, 0xff, 0x88, 0xa2, 0xca, 0xff, 0x49, 0x59, 0x84, 0xff, 0x4a, 0x52, 0x81, 0xff, 0x4e, 0x5b, 0x89, 0xff, 0x8f, 0x9c, 0xca, 0xff,
    0x7f, 0x92, 0xb8, 0xff, 0x58, 0x69, 0x94, 0xff, 0x49, 0x5b, 0x8a, 0xff, 0x43, 0x57, 0x87, 0xff, 0x52, 0x6a, 0x94, 0xff, 0xcd, 0xeb, 0xff, 0xff, 0xc0, 0xe4, 0xf4, 0xff, 0xce, 0xf5, 0xfd, 0xff, 0xd3, 0xe8, 0xfe, 0xff, 0xcf, 0xe9, 0xff, 0xff, 0xc7, 0xe5, 0xff, 0xff, 0xae, 0xd0, 0xee, 0xff, 0xbd, 0xdb, 0xfe, 0xff, 0xab, 0xbf, 0xe8, 0xff, 0x5e, 0x66, 0x95, 0xff, 0x4c, 0x4d, 0x7f, 0xff, 0x47, 0x59, 0x88, 0xff, 0x84, 0x96, 0xc5, 0xff,
    0x83, 0x98, 0xcb, 0xff, 0x4d, 0x5e, 0x96, 0xff, 0x50, 0x61, 0x9a, 0xff, 0x3c, 0x4a, 0x84, 0xff, 0x74, 0x88, 0xb8, 0xff, 0xd0, 0xeb, 0xff, 0xff, 0xc0, 0xe1, 0xf4, 0xff, 0xc5, 0xec, 0xf5, 0xff, 0xc6, 0xf1, 0xff, 0xff, 0xc3, 0xef, 0xff, 0xff, 0x8c, 0xb5, 0xdc, 0xff, 0x60, 0x84, 0xba, 0xff, 0x63, 0x81, 0xc2, 0xff, 0x83, 0x99, 0xe0, 0xff, 0x61, 0x70, 0xb8, 0xff, 0x46, 0x52, 0x98, 0xff, 0x45, 0x53, 0x83, 0xff, 0x9d, 0xab, 0xdb, 0xff,
    0xaa, 0xb8, 0xe2, 0xff, 0x4b, 0x5a, 0x88, 0xff, 0x35, 0x49, 0x79, 0xff, 0x51, 0x67, 0x97, 0xff, 0x98, 0xb1, 0xdb, 0xff, 0x71, 0x8e, 0xad, 0xff, 0x36, 0x53, 0x68, 0xff, 0xd2, 0xef, 0xfd, 0xff, 0xbf, 0xe0, 0xfa, 0xff, 0xb7, 0xd7, 0xfa, 0xff, 0x79, 0x9a, 0xcc, 0xff, 0x2a, 0x49, 0x88, 0xff, 0x22, 0x3d, 0x80, 0xff, 0x8c, 0xa5, 0xe5, 0xff, 0x68, 0x7c, 0xb5, 0xff, 0x46, 0x59, 0x8c, 0xff, 0x57, 0x60, 0x86, 0xff, 0xb4, 0xbd, 0xe3, 0xff,
    0xda, 0xdd, 0xf9, 0xff, 0x78, 0x83, 0xa1, 0xff, 0x4e, 0x65, 0x85, 0xff, 0x68, 0x88, 0xab, 0xff, 0xb4, 0xd7, 0xf8, 0xff, 0x7e, 0x9c, 0xb7, 0xff, 0x37, 0x4f, 0x65, 0xff, 0xd2, 0xe6, 0xf7, 0xff, 0xd3, 0xe9, 0xff, 0xff, 0xcf, 0xe5, 0xff, 0xff, 0x7d, 0x95, 0xc9, 0xff, 0x2f, 0x4a, 0x8a, 0xff, 0x28, 0x43, 0x83, 0xff, 0x7f, 0x99, 0xcf, 0xff, 0xae, 0xc8, 0xec, 0xff, 0x78, 0x92, 0xaa, 0xff, 0x97, 0x9b, 0xb3, 0xff, 0xe0, 0xe4, 0xfc, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xd2, 0xda, 0xe7, 0xff, 0xb3, 0xca, 0xda, 0xff, 0xae, 0xd1, 0xe5, 0xff, 0xc6, 0xed, 0xff, 0xff, 0xd2, 0xf2, 0xff, 0xff, 0xc6, 0xdb, 0xf1, 0xff, 0xd5, 0xe2, 0xf8, 0xff, 0xb1, 0xc0, 0xc2, 0xff, 0x7b, 0x8e, 0x9d, 0xff, 0x9e, 0xb4, 0xd7, 0xff, 0x6e, 0x87, 0xb9, 0xff, 0x6f, 0x8a, 0xbd, 0xff, 0xb1, 0xcb, 0xf3, 0xff, 0xcf, 0xe9, 0xff, 0xff, 0xe0, 0xf9, 0xff, 0xff, 0xe8, 0xe9, 0xf3, 0xff, 0xfb, 0xfd, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xfb, 0xfe, 0xff, 0xff, 0xed, 0xff, 0xff, 0xff, 0xe1, 0xff, 0xff, 0xff, 0xc4, 0xe7, 0xf5, 0xff, 0xc8, 0xe6, 0xf9, 0xff, 0xe6, 0xfb, 0xff, 0xff, 0xb0, 0xbe, 0xd4, 0xff, 0x21, 0x2c, 0x1c, 0xff, 0x30, 0x3f, 0x3b, 0xff, 0x57, 0x6f, 0x7b, 0xff, 0xd6, 0xf3, 0xff, 0xff, 0xbf, 0xdc, 0xfb, 0xff, 0xd4, 0xec, 0xff, 0xff, 0xe6, 0xfa, 0xff, 0xff, 0xeb, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xed, 0xea, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xec, 0xf6, 0xf6, 0xff, 0xeb, 0xfe, 0xff, 0xff, 0xd5, 0xeb, 0xf6, 0xff, 0xbb, 0xd5, 0xe3, 0xff, 0xc3, 0xdd, 0xee, 0xff, 0x7c, 0x94, 0xa8, 0xff, 0x98, 0x97, 0x9b, 0xff, 0x46, 0x4e, 0x55, 0xff, 0xa7, 0xbb, 0xc6, 0xff, 0x99, 0xb6, 0xc5, 0xff, 0xd4, 0xf1, 0xff, 0xff, 0xc6, 0xdb, 0xea, 0xff, 0xf2, 0xfb, 0xff, 0xff, 0xfa, 0xfc, 0xff, 0xff, 0xfd, 0xfb, 0xfb, 0xff, 0xfd, 0xfb, 0xfa, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xf8, 0xfb, 0xff, 0xff, 0xf4, 0xfd, 0xff, 0xff, 0xe8, 0xfb, 0xff, 0xff, 0xcb, 0xea, 0xf9, 0xff, 0x92, 0xb6, 0xc8, 0xff, 0x4e, 0x36, 0x77, 0xff, 0x50, 0x46, 0x7b, 0xff, 0x68, 0x71, 0x96, 0xff, 0xc3, 0xdc, 0xf0, 0xff, 0xc2, 0xde, 0xe5, 0xff, 0xed, 0xff, 0xff, 0xff, 0xfb, 0xfe, 0xff, 0xff, 0xff, 0xf7, 0xfe, 0xff, 0xf7, 0xf9, 0xfa, 0xff, 0xf6, 0xf8, 0xf9, 0xff,
    0xfb, 0xff, 0xfe, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xf4, 0xf3, 0xfc, 0xff, 0xea, 0xfa, 0xff, 0xff, 0xd5, 0xf6, 0xff, 0xff, 0xc5, 0xf2, 0xff, 0xff, 0xaa, 0x80, 0xf7, 0xff, 0x8d, 0x74, 0xd6, 0xff, 0xcd, 0xcd, 0xff, 0xff, 0xcd, 0xe1, 0xff, 0xff, 0xe6, 0xff, 0xff, 0xff, 0xe8, 0xf6, 0xf4, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfb, 0xfe, 0xff, 0xff, 0xf7, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd6, 0xcb, 0xeb, 0xff, 0xda, 0xd1, 0xeb, 0xff, 0xe9, 0xe3, 0xf4, 0xff, 0xfc, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xef, 0xff, 0xff, 0xf6, 0xf0, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfd, 0xfc, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfe, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfd, 0xfe, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
};

const lv_image_dsc_t img_emoji_dog_face = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_dog_face_map,
    .data_size = sizeof(img_emoji_dog_face_map),
};

#endif
