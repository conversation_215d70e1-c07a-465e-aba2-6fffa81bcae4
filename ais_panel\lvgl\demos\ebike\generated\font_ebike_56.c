/*******************************************************************************
 * Size: 56 px
 * Bpp: 4
 * Opts:
 ******************************************************************************/

#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "../../../lvgl.h"
#endif

#ifndef FONT_EBIKE_56
    #define FONT_EBIKE_56 1
#endif

#if FONT_EBIKE_56

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+002E "." */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xe9, 0x0,
    0x2e, 0xff, 0xff, 0xb0, 0x8f, 0xff, 0xff, 0xf2,
    0xbf, 0xff, 0xff, 0xf5, 0x8f, 0xff, 0xff, 0xf2,
    0x2e, 0xff, 0xff, 0xa0, 0x2, 0xbf, 0xe9, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x0, 0x3, 0x67, 0x76, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0xff,
    0xf9, 0x10, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x30, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xe0, 0xd, 0xff, 0xff, 0xfc, 0x20, 0x9, 0xff,
    0xff, 0xff, 0x30, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xc, 0xff, 0xff, 0xf5, 0x2f, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0x72, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf7, 0x3f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x83, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf8, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x83, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf8,
    0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x83, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xf8, 0x3f, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x83,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf8, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x83, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x83, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf8, 0x3f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x83, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf8, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x83, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf8,
    0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x83, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0x82, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf7, 0x1f, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x60,
    0xff, 0xff, 0xff, 0x10, 0x0, 0xc, 0xff, 0xff,
    0xf5, 0xc, 0xff, 0xff, 0xfc, 0x20, 0x19, 0xff,
    0xff, 0xff, 0x20, 0x8f, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xd0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0xff, 0xc5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x67, 0x75,
    0x20, 0x0, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x3, 0x9f, 0xff, 0xff, 0x80, 0x1,
    0x6d, 0xff, 0xff, 0xff, 0xf8, 0x3a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x89, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x89, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x9f,
    0xff, 0xfb, 0x7f, 0xff, 0xff, 0x89, 0xfa, 0x50,
    0x1, 0xff, 0xff, 0xf8, 0x10, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x80,

    /* U+0032 "2" */
    0x0, 0x0, 0x0, 0x25, 0x67, 0x65, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xdf, 0xff, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0x80, 0x3f, 0xff, 0xff, 0xf8, 0x0, 0x3d, 0xff,
    0xff, 0xfc, 0x7, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0x40,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0x9, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf1, 0x9f,
    0xff, 0xff, 0x10, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0x19, 0xff, 0xff, 0xf1, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xf1, 0x9f, 0xff, 0xff, 0x10, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0x19, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff,
    0x10, 0x0, 0x0, 0xbf, 0xff, 0xff, 0x12, 0x44,
    0x44, 0x40, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x96, 0x66, 0x66, 0x66,
    0x66, 0x60, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0,

    /* U+0033 "3" */
    0x0, 0x0, 0x0, 0x15, 0x67, 0x65, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff,
    0xd5, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xb0, 0x1f, 0xff, 0xff, 0xfa, 0x10, 0x1b, 0xff,
    0xff, 0xff, 0x4, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf2, 0x6f, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0x36, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x7, 0xff, 0xff, 0xf4, 0x7f,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x47, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x47, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xf4, 0x6f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x17,
    0x78, 0xcf, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xf4, 0x6e, 0xee, 0xee,
    0x40, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x47, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x7, 0xff, 0xff, 0xf4,
    0x7f, 0xff, 0xff, 0x40, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x47, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x46, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf4, 0x5f, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x34,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf1, 0x1f, 0xff, 0xff, 0xfa, 0x10, 0x1b, 0xff,
    0xff, 0xfe, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xa0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x67, 0x65,
    0x10, 0x0, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xfd, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xaf,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xca, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf8, 0xaf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0x4a, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf1, 0xaf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xfd, 0xa, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x90, 0xaf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xf5,
    0xa, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x20, 0xaf, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xe0, 0xa, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfa, 0x0,
    0xaf, 0xff, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x70, 0xa, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xf3, 0x0, 0xaf, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x0, 0xa,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xb0, 0x0, 0xaf, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xf8, 0x0, 0xa, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0, 0xaf,
    0xff, 0xff, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf0,
    0x0, 0xa, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xdf,
    0xff, 0xfc, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0x90, 0x0, 0xa, 0xff,
    0xff, 0xf0, 0x0, 0x5, 0xff, 0xff, 0xf9, 0x66,
    0x66, 0xcf, 0xff, 0xff, 0x66, 0x61, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x56, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x56, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xf0, 0x0, 0x0,

    /* U+0035 "5" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x3,
    0xff, 0xff, 0xfb, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x0, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf8, 0x1, 0x7c, 0xef, 0xea, 0x40, 0x0,
    0x3f, 0xff, 0xff, 0x83, 0xef, 0xff, 0xff, 0xff,
    0x90, 0x3, 0xff, 0xff, 0xf9, 0xef, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x3f, 0xff,
    0xff, 0xff, 0xa6, 0x7e, 0xff, 0xff, 0xff, 0x63,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x1e, 0xff, 0xff,
    0xf8, 0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0x80, 0x33, 0x33, 0x32, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0x81, 0xaa,
    0xaa, 0xa5, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf8,
    0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x82, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xf8, 0x2f, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0x81, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xf7, 0xf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x50,
    0xcf, 0xff, 0xff, 0xd2, 0x1, 0x9f, 0xff, 0xff,
    0xf2, 0x7, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x0,
    0x0, 0x4c, 0xff, 0xff, 0xff, 0xff, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x56, 0x77, 0x63, 0x0,
    0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x15, 0x67, 0x75, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xdf, 0xff, 0xff, 0xff,
    0xe8, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x20, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xe0, 0xd, 0xff, 0xff, 0xfc, 0x20, 0x9, 0xff,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xc, 0xff, 0xff, 0xf5, 0x1f, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0x62, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf7, 0x3f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x83, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf8, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x18, 0x88, 0x88, 0x43, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x80, 0x0, 0x24, 0x43, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xf8, 0x4, 0xdf, 0xff,
    0xff, 0x91, 0x0, 0x3f, 0xff, 0xff, 0x87, 0xff,
    0xff, 0xff, 0xff, 0xd1, 0x3, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x13,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x3f, 0xff, 0xff, 0xfe, 0x40, 0x29, 0xff,
    0xff, 0xff, 0x73, 0xff, 0xff, 0xff, 0x30, 0x0,
    0xb, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0x83, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf8, 0x3f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x83, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf8, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0x83, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x2f, 0xff, 0xff, 0x83, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf8,
    0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x83, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xf8, 0x2f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0x82, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xf8, 0x1f, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x70,
    0xff, 0xff, 0xff, 0x20, 0x0, 0xc, 0xff, 0xff,
    0xf5, 0xc, 0xff, 0xff, 0xfd, 0x20, 0x19, 0xff,
    0xff, 0xff, 0x20, 0x7f, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xd0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xf8,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x15, 0x67, 0x76,
    0x30, 0x0, 0x0, 0x0,

    /* U+0037 "7" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x8f, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x0, 0x14, 0x67, 0x75, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff,
    0xd6, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xe0, 0xf, 0xff, 0xff, 0xfb, 0x10, 0x1a, 0xff,
    0xff, 0xff, 0x23, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xf4, 0x4f, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0x55, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x5, 0xff, 0xff, 0xf6, 0x5f,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0x65, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xf6, 0x5f, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0x64, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf5, 0x3f, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x41, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x9, 0xff, 0xff, 0xf1,
    0xb, 0xff, 0xff, 0xf3, 0x0, 0x3, 0xff, 0xff,
    0xfc, 0x0, 0x3f, 0xff, 0xff, 0xfa, 0x79, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x9f, 0xff, 0xff, 0xc3, 0x3,
    0xcf, 0xff, 0xff, 0x80, 0xe, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0xdf, 0xff, 0xfd, 0x2, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf2, 0x4f,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0x45, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xf5, 0x5f, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0x65, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf6, 0x5f, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x65, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x5, 0xff, 0xff, 0xf6,
    0x5f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x65, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xf6, 0x5f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x65, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xf6, 0x4f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x52,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xf3, 0xf, 0xff, 0xff, 0xfb, 0x10, 0x1b, 0xff,
    0xff, 0xff, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xb0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x67, 0x65,
    0x10, 0x0, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x0, 0x25, 0x67, 0x65, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xef, 0xff, 0xff, 0xff,
    0xd6, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xb0, 0xf, 0xff, 0xff, 0xfb, 0x10, 0x1b, 0xff,
    0xff, 0xff, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0x45, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x6, 0xff, 0xff, 0xf5, 0x6f,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0x66, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xf6, 0x6f, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x66, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xf6, 0x6f, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x66, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x6, 0xff, 0xff, 0xf6,
    0x6f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0x66, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xf6, 0x6f, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0x66, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf6, 0x6f, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x65,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xf6, 0x4f, 0xff, 0xff, 0xfb, 0x20, 0x3c, 0xff,
    0xff, 0xff, 0x62, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xf6, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xf9, 0x5f, 0xff, 0xff,
    0x60, 0x0, 0x8f, 0xff, 0xff, 0xe6, 0x5, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x3, 0x54, 0x30, 0x0,
    0x5f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xf6,
    0x28, 0x88, 0x88, 0x30, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x65, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xf6, 0x5f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x65, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xf5, 0x4f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x52,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xf3, 0xf, 0xff, 0xff, 0xfb, 0x10, 0x1b, 0xff,
    0xff, 0xff, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xc0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x67, 0x65,
    0x20, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 154, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 32, .adv_w = 340, .box_w = 19, .box_h = 45, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 460, .adv_w = 243, .box_w = 13, .box_h = 43, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 740, .adv_w = 322, .box_w = 19, .box_h = 44, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1158, .adv_w = 335, .box_w = 19, .box_h = 45, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1586, .adv_w = 335, .box_w = 21, .box_h = 43, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2038, .adv_w = 334, .box_w = 19, .box_h = 44, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2456, .adv_w = 339, .box_w = 19, .box_h = 45, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2884, .adv_w = 294, .box_w = 19, .box_h = 43, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3293, .adv_w = 337, .box_w = 19, .box_h = 45, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3721, .adv_w = 339, .box_w = 19, .box_h = 45, .ofs_x = 1, .ofs_y = -1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint8_t glyph_id_ofs_list_0[] = {
    0, 0, 1, 2, 3, 4, 5, 6,
    7, 8, 9, 10
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 46, .range_length = 12, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = glyph_id_ofs_list_0, .list_length = 12, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_FULL
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] = {
    1, 3,
    6, 3,
    9, 1,
    9, 3,
    9, 6
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] = {
    -30, -9, -42, 6, -23
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs = {
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 5,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
    /*Store all the custom data of the font*/
    static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_ebike_56 = {
#else
lv_font_t font_ebike_56 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 45,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -7,
    .underline_thickness = 3,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
    .fallback = NULL,
    .user_data = NULL
};



#endif /*#if FONT_EBIKE_56*/

