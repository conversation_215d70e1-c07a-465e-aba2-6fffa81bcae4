#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: alipay.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_alipay_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,0x00,0x00,0x00,0x00,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x1C,0x05,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,
0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,
0x00,0x00,0x00,0x00,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0xDC,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x29,0x56,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x56,0x28,0x03,0x00,0x00,0x00,0x00,0x00,0x0D,0x7F,0xE4,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xE4,0x7E,0x0C,0x00,0x00,
0x00,0x02,0x7F,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0x7C,0x01,0x00,0x00,0x29,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x26,0x00,0x00,0x56,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xAA,0x7D,0x7D,0xB6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0x52,0x00,
0x00,0x62,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x56,0x00,0x00,0x6D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x56,0x00,0x00,0x6D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFE,0xC9,0xAF,0xB0,0xB0,0xB0,0xB0,0xAF,0x3C,0x00,0x00,0x4B,0xB1,0xB0,0xB0,0xB0,0xB0,0xAF,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,
0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFC,0x6E,0x26,0x2A,0x2A,0x2A,0x2A,0x2A,0x0E,0x00,0x00,0x12,0x2A,0x2A,0x2A,0x2A,0x2A,0x26,0x81,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xEE,0xE7,0xE8,0xE8,0xE8,0xE8,0xE6,0x4E,0x00,0x00,0x63,0xEA,0xE8,0xE8,0xE8,0xE8,0xE6,0xF0,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,0xD9,0xDA,0xDA,0xDA,0xD8,0x4A,0x00,0x00,0x5D,0xDC,0xDB,0xDC,0xDC,0xDD,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,
0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x88,0x31,0x34,0x34,0x34,0x34,0x23,0x1B,0x1B,0x26,0x35,0x2E,0x1A,0x17,0x2F,0xD9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0xDA,0xDB,0xDA,0xDA,0xDA,0xDB,0xDC,0xDC,0xDB,0xDE,0x87,0x01,0x00,0x51,0xF8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x53,0x00,0x05,0xA5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,
0x00,0x63,0xFF,0xFF,0xFF,0xF6,0xC4,0x89,0x64,0x55,0x56,0x66,0x84,0xAA,0xD2,0xF1,0xFE,0xFF,0xC3,0x11,0x00,0x31,0xE8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xFF,0xDF,0x5C,0x0B,0x02,0x0D,0x13,0x0D,0x02,0x00,0x02,0x14,0x38,0x6E,0xAC,0x58,0x00,0x01,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0xF3,0x53,0x0C,0x61,0xAD,0xCE,0xD8,0xCE,0xAD,0x75,0x37,0x0B,0x00,0x00,0x05,0x04,0x00,0x17,0xC4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,
0x00,0x63,0xFF,0xBB,0x17,0x9C,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0xBD,0x6F,0x20,0x00,0x00,0x00,0x03,0x26,0x65,0xB1,0xE9,0xFE,0xFF,0xFF,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0x92,0x43,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0x55,0x00,0x00,0x01,0x00,0x00,0x00,0x07,0x2E,0x75,0xC0,0xF1,0xFF,0xFF,0x5E,0x00,0x00,0x63,0xFF,0x94,0x54,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x7E,0x06,0x00,0x1C,0x85,0x4B,0x0A,0x00,0x00,0x00,0x00,0x0C,0x3C,0x85,0xCB,0x57,0x00,
0x00,0x63,0xFF,0xBD,0x3A,0xE6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF0,0x78,0x08,0x00,0x12,0xA2,0xFF,0xF4,0xB3,0x4E,0x0B,0x00,0x00,0x00,0x00,0x00,0x13,0x10,0x00,0x00,0x63,0xFF,0xF1,0x4A,0x75,0xF3,0xFF,0xFF,0xFF,0xFF,0xFA,0xC6,0x4E,0x04,0x00,0x1B,0xA3,0xFD,0xFF,0xFF,0xFF,0xF5,0xB4,0x4C,0x0A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0xFF,0xFF,0xC4,0x29,0x42,0x8E,0xAE,0xB1,0x94,0x55,0x13,0x00,0x05,0x44,0xC3,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0xAE,0x44,0x07,0x00,0x00,0x00,0x00,
0x00,0x63,0xFF,0xFF,0xFF,0xCA,0x4F,0x11,0x05,0x02,0x00,0x01,0x14,0x47,0xA0,0xEE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF0,0xA2,0x36,0x03,0x00,0x00,0x00,0x62,0xFF,0xFF,0xFF,0xFF,0xF6,0xCE,0xA5,0x91,0x95,0xAD,0xD3,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,0x8E,0x1C,0x00,0x00,0x56,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0x52,0x00,
0x00,0x29,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x28,0x00,0x00,0x02,0x7F,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0x7E,0x02,0x00,0x00,0x00,0x0D,0x7F,0xE4,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xE4,0x7E,0x0D,0x00,0x00,
0x00,0x00,0x00,0x03,0x28,0x56,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x56,0x28,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_alipay_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_alipay_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_alipay_icon_data};

