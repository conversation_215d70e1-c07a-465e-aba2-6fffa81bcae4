/*******************************************************************************
 * Size: 24 px
 * Bpp: 8
 * Opts: --bpp 8 --size 24 --no-compress --font Roboto-Medium.ttf --range 32-127,176 --format lvgl -o font_lv_demo_high_res_roboto_medium_24.c
 ******************************************************************************/

#include "../../../lvgl.h"

#if LV_USE_DEMO_HIGH_RES

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x3a, 0xff, 0xff, 0xc0, 0x34, 0xff, 0xff, 0xbb,
    0x2e, 0xff, 0xff, 0xb5, 0x29, 0xff, 0xff, 0xaf,
    0x23, 0xff, 0xff, 0xa9, 0x1d, 0xff, 0xff, 0xa3,
    0x18, 0xff, 0xff, 0x9d, 0x12, 0xff, 0xff, 0x97,
    0xd, 0xff, 0xff, 0x91, 0x7, 0xff, 0xff, 0x8b,
    0x1, 0xff, 0xff, 0x85, 0x0, 0xfc, 0xff, 0x7f,
    0x0, 0x1b, 0x1c, 0xd, 0x0, 0x0, 0x8, 0x0,
    0x11, 0xd9, 0xfe, 0x80, 0x48, 0xff, 0xff, 0xdc,
    0xe, 0xc8, 0xf3, 0x72,

    /* U+0022 "\"" */
    0xd0, 0xff, 0x38, 0x48, 0xff, 0xc0, 0xd0, 0xff,
    0x36, 0x48, 0xff, 0xbe, 0xd0, 0xff, 0x23, 0x48,
    0xff, 0xab, 0xd0, 0xff, 0xc, 0x48, 0xff, 0x94,
    0xd0, 0xf5, 0x0, 0x48, 0xff, 0x7d, 0xd0, 0xde,
    0x0, 0x48, 0xff, 0x66, 0x23, 0x24, 0x0, 0xc,
    0x2c, 0xf,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xf5, 0xfa, 0x5,
    0x0, 0x60, 0xff, 0x97, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x26, 0xff, 0xd0, 0x0, 0x0, 0x8e,
    0xff, 0x68, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x54, 0xff, 0xa1, 0x0, 0x0, 0xbc, 0xff, 0x39,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x83, 0xff,
    0x72, 0x0, 0x0, 0xea, 0xfe, 0xc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb1, 0xff, 0x43, 0x0,
    0x19, 0xff, 0xdb, 0x0, 0x0, 0x0, 0x0, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x34, 0x0, 0xdc, 0xe0, 0xe4,
    0xff, 0xfa, 0xe0, 0xe0, 0xf1, 0xff, 0xed, 0xe0,
    0xe0, 0x2d, 0x0, 0x0, 0x0, 0x45, 0xff, 0xb2,
    0x0, 0x0, 0xad, 0xff, 0x4a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7a, 0xff, 0x7d, 0x0, 0x0,
    0xe2, 0xff, 0x14, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x48, 0x0, 0x17, 0xff, 0xdd,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0xe0, 0xe0, 0xf9,
    0xff, 0xe5, 0xe0, 0xe6, 0xff, 0xf7, 0xe0, 0xe0,
    0x49, 0x0, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x54, 0x0,
    0x0, 0x0, 0x43, 0xff, 0xb4, 0x0, 0x0, 0xab,
    0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x71, 0xff, 0x87, 0x0, 0x0, 0xd9, 0xff, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xff,
    0x59, 0x0, 0x9, 0xfd, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcc, 0xff, 0x2c, 0x0,
    0x34, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xf6, 0xf9, 0x5, 0x0, 0x61, 0xff,
    0x96, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x18,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0x18, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x3f, 0xde, 0xff, 0x5e,
    0xd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x52, 0xeb,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x65, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xf7, 0xf6, 0xff,
    0xff, 0xff, 0x5a, 0x0, 0x0, 0xe1, 0xff, 0xff,
    0x6b, 0x2, 0x4, 0x71, 0xff, 0xff, 0xe4, 0x3,
    0x1d, 0xff, 0xff, 0xcb, 0x0, 0x0, 0x0, 0x0,
    0xc1, 0xff, 0xff, 0x33, 0x20, 0xff, 0xff, 0xba,
    0x0, 0x0, 0x0, 0x0, 0x86, 0xfc, 0xfc, 0x52,
    0x4, 0xf0, 0xff, 0xf5, 0x25, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x82, 0xff, 0xff,
    0xef, 0x74, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xa8, 0xff, 0xff, 0xff, 0xf6, 0xa2,
    0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58,
    0xd3, 0xff, 0xff, 0xff, 0xfe, 0x98, 0x6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0x9f, 0xf9,
    0xff, 0xff, 0x9e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0xef, 0xff, 0xff, 0x28,
    0x7d, 0x9c, 0x9c, 0x9, 0x0, 0x0, 0x0, 0x0,
    0x86, 0xff, 0xff, 0x64, 0xb9, 0xff, 0xff, 0x33,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff, 0x6b,
    0x78, 0xff, 0xff, 0xb8, 0x9, 0x0, 0x0, 0x13,
    0xdc, 0xff, 0xff, 0x41, 0x12, 0xe7, 0xff, 0xff,
    0xe2, 0xa8, 0xad, 0xee, 0xff, 0xff, 0xce, 0x2,
    0x0, 0x30, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x1d, 0x0, 0x0, 0x0, 0x9, 0x5f,
    0xa2, 0xff, 0xf4, 0x8d, 0x4c, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xdc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xdc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x50, 0xca, 0xf3, 0xe4, 0x9f, 0x19, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xfd, 0xc5, 0xe1, 0xff, 0xd1, 0x7,
    0x0, 0x0, 0x0, 0xa, 0x0, 0x0, 0x0, 0x0,
    0xb9, 0xff, 0x61, 0x0, 0x5, 0xcd, 0xff, 0x42,
    0x0, 0x0, 0x33, 0xf3, 0x65, 0x0, 0x0, 0x0,
    0xd7, 0xff, 0x29, 0x0, 0x0, 0x99, 0xff, 0x63,
    0x0, 0x5, 0xd1, 0xf4, 0x1f, 0x0, 0x0, 0x0,
    0xc2, 0xff, 0x5d, 0x0, 0x3, 0xcb, 0xff, 0x45,
    0x0, 0x7d, 0xff, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0x5b, 0xff, 0xfc, 0xc1, 0xdc, 0xff, 0xd4, 0x9,
    0x2a, 0xf9, 0xc7, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x51, 0xc9, 0xf3, 0xe6, 0xa1, 0x1b, 0x2,
    0xc7, 0xfa, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x71,
    0xff, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0xf5,
    0xd6, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0xfd,
    0x3a, 0x22, 0xad, 0xea, 0xe9, 0xaa, 0x1e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x65, 0xff, 0x93,
    0x19, 0xec, 0xff, 0xd5, 0xd8, 0xff, 0xe7, 0x13,
    0x0, 0x0, 0x0, 0x0, 0x1a, 0xf0, 0xe2, 0xd,
    0x7d, 0xff, 0xac, 0x1, 0x2, 0xb3, 0xff, 0x72,
    0x0, 0x0, 0x0, 0x0, 0xb2, 0xff, 0x4b, 0x0,
    0xa0, 0xff, 0x62, 0x0, 0x0, 0x6a, 0xff, 0x97,
    0x0, 0x0, 0x0, 0x59, 0xff, 0xa6, 0x0, 0x0,
    0x9c, 0xff, 0x61, 0x0, 0x0, 0x69, 0xff, 0x95,
    0x0, 0x0, 0x1, 0xd3, 0xec, 0x15, 0x0, 0x0,
    0x73, 0xff, 0xa9, 0x0, 0x0, 0xaa, 0xff, 0x6c,
    0x0, 0x0, 0x0, 0xf, 0x37, 0x0, 0x0, 0x0,
    0x12, 0xe8, 0xff, 0xd3, 0xd0, 0xff, 0xe5, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x23, 0xaf, 0xea, 0xeb, 0xb0, 0x23, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x3, 0x71, 0xd2, 0xf8, 0xf3, 0xbe,
    0x45, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xab, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x5a, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x43,
    0xff, 0xff, 0xdd, 0x61, 0x6d, 0xec, 0xff, 0xea,
    0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x84, 0xff,
    0xff, 0x3f, 0x0, 0x0, 0x69, 0xff, 0xff, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0xff, 0xff,
    0x29, 0x0, 0x0, 0x7a, 0xff, 0xfb, 0xb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0x91,
    0x0, 0x47, 0xf4, 0xff, 0x9c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xc7, 0xff, 0xfe, 0xb3,
    0xff, 0xff, 0xbc, 0xa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x23, 0xf5, 0xff, 0xff, 0xfd,
    0x85, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0x99, 0xff, 0xff, 0xff, 0xeb, 0x1f,
    0x0, 0x0, 0xe, 0x30, 0x30, 0x7, 0x0, 0x7,
    0xbc, 0xff, 0xff, 0xc9, 0xff, 0xff, 0xd3, 0xe,
    0x0, 0x5f, 0xff, 0xff, 0x21, 0x0, 0x7f, 0xff,
    0xff, 0x93, 0x0, 0x95, 0xff, 0xff, 0xb8, 0x3,
    0x83, 0xff, 0xfd, 0xb, 0x0, 0xdd, 0xff, 0xf6,
    0xb, 0x0, 0x4, 0xb9, 0xff, 0xff, 0x95, 0xd8,
    0xff, 0xd5, 0x0, 0x0, 0xf4, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0xf, 0xd5, 0xff, 0xff, 0xff, 0xff,
    0x7e, 0x0, 0x0, 0xd0, 0xff, 0xff, 0x2b, 0x0,
    0x0, 0x0, 0x22, 0xeb, 0xff, 0xff, 0xec, 0x12,
    0x0, 0x0, 0x69, 0xff, 0xff, 0xd9, 0x49, 0x17,
    0x31, 0x89, 0xf8, 0xff, 0xff, 0xef, 0x28, 0x0,
    0x0, 0x1, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xdc, 0x13, 0x0,
    0x0, 0x0, 0x4f, 0xb5, 0xea, 0xfb, 0xf1, 0xcc,
    0x85, 0x1d, 0x87, 0xff, 0xff, 0xc1, 0x6,

    /* U+0027 "'" */
    0x8, 0xff, 0xff, 0x20, 0x8, 0xff, 0xff, 0x1d,
    0x8, 0xff, 0xff, 0xe, 0x8, 0xff, 0xfd, 0x1,
    0x8, 0xff, 0xed, 0x0, 0x8, 0xff, 0xdd, 0x0,
    0x0, 0x14, 0x10, 0x0,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xb2, 0x96, 0x0, 0x0,
    0x0, 0xa, 0xc9, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0xa0, 0xff, 0xce, 0xa, 0x0, 0x0, 0x41, 0xff,
    0xf9, 0x27, 0x0, 0x0, 0x1, 0xd4, 0xff, 0x95,
    0x0, 0x0, 0x0, 0x41, 0xff, 0xff, 0x2a, 0x0,
    0x0, 0x0, 0xa4, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xea, 0xff, 0x97, 0x0, 0x0, 0x0, 0x22,
    0xff, 0xff, 0x63, 0x0, 0x0, 0x0, 0x51, 0xff,
    0xff, 0x49, 0x0, 0x0, 0x0, 0x66, 0xff, 0xff,
    0x2e, 0x0, 0x0, 0x0, 0x77, 0xff, 0xff, 0x1a,
    0x0, 0x0, 0x0, 0x78, 0xff, 0xff, 0x1d, 0x0,
    0x0, 0x0, 0x68, 0xff, 0xff, 0x2f, 0x0, 0x0,
    0x0, 0x56, 0xff, 0xff, 0x42, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0x58, 0x0, 0x0, 0x0, 0x3,
    0xf4, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xb8,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x59, 0xff,
    0xfe, 0x15, 0x0, 0x0, 0x0, 0x7, 0xeb, 0xff,
    0x77, 0x0, 0x0, 0x0, 0x0, 0x67, 0xff, 0xe9,
    0xe, 0x0, 0x0, 0x0, 0x2, 0xca, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x21, 0xea, 0xff, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x2b, 0xdf, 0xad, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x28,

    /* U+0029 ")" */
    0x0, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a,
    0xe5, 0x3b, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xfe,
    0xf5, 0x3b, 0x0, 0x0, 0x0, 0x0, 0x7b, 0xff,
    0xea, 0x15, 0x0, 0x0, 0x0, 0x2, 0xc4, 0xff,
    0xa4, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xff,
    0x3b, 0x0, 0x0, 0x0, 0x0, 0xd1, 0xff, 0xa8,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfa, 0x14,
    0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0x55, 0x0,
    0x0, 0x0, 0xb, 0xfc, 0xff, 0x95, 0x0, 0x0,
    0x0, 0x0, 0xe6, 0xff, 0xbe, 0x0, 0x0, 0x0,
    0x0, 0xd4, 0xff, 0xd9, 0x0, 0x0, 0x0, 0x0,
    0xc1, 0xff, 0xef, 0x0, 0x0, 0x0, 0x0, 0xba,
    0xff, 0xed, 0x0, 0x0, 0x0, 0x0, 0xc4, 0xff,
    0xdc, 0x0, 0x0, 0x0, 0x0, 0xd7, 0xff, 0xc9,
    0x0, 0x0, 0x0, 0x1, 0xf4, 0xff, 0x9d, 0x0,
    0x0, 0x0, 0x26, 0xff, 0xff, 0x66, 0x0, 0x0,
    0x0, 0x62, 0xff, 0xff, 0x23, 0x0, 0x0, 0x0,
    0xb2, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x1c, 0xfb,
    0xff, 0x59, 0x0, 0x0, 0x0, 0x96, 0xff, 0xc7,
    0x1, 0x0, 0x0, 0x40, 0xfc, 0xfb, 0x31, 0x0,
    0x0, 0x33, 0xed, 0xff, 0x69, 0x0, 0x0, 0x0,
    0x4e, 0xfc, 0x6d, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x0, 0x0, 0xa7, 0xff, 0x66, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0x5b, 0x0, 0x0, 0x0, 0x0, 0x6, 0x1d,
    0x0, 0x0, 0x91, 0xff, 0x50, 0x0, 0x0, 0x11,
    0x0, 0x4d, 0xfd, 0xb7, 0x53, 0x8c, 0xff, 0x4f,
    0x5b, 0xbd, 0xee, 0x5, 0x75, 0xf9, 0xff, 0xff,
    0xfe, 0xff, 0xfa, 0xff, 0xff, 0xfb, 0x34, 0x0,
    0xc, 0x50, 0xaa, 0xff, 0xff, 0xf5, 0xa0, 0x56,
    0xf, 0x0, 0x0, 0x0, 0x1, 0xb2, 0xff, 0xfb,
    0xfc, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x75,
    0xff, 0xd1, 0x57, 0xff, 0xe3, 0x13, 0x0, 0x0,
    0x0, 0x39, 0xfb, 0xfd, 0x36, 0x0, 0xb3, 0xff,
    0xb5, 0x1, 0x0, 0x0, 0x10, 0xa7, 0x91, 0x0,
    0x0, 0x1f, 0xec, 0x69, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0xd, 0x0, 0x0,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0x80, 0x80,
    0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb8,
    0xff, 0xff, 0xc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb8, 0xff, 0xff, 0xc, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x9c, 0x9c, 0x9c, 0x9c, 0xe3, 0xff,
    0xff, 0xa0, 0x9c, 0x9c, 0x9c, 0x4e, 0x34, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x34, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff,
    0xc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb8,
    0xff, 0xff, 0xc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb8, 0xff, 0xff, 0xc, 0x0, 0x0, 0x0,
    0x0,

    /* U+002C "," */
    0x0, 0x78, 0xff, 0xff, 0x16, 0x0, 0x78, 0xff,
    0xff, 0x15, 0x0, 0x84, 0xff, 0xfd, 0x7, 0x0,
    0xae, 0xff, 0xd5, 0x0, 0xf, 0xf3, 0xff, 0x73,
    0x0, 0x75, 0xff, 0xd5, 0x6, 0x0, 0x2, 0x65,
    0x1c, 0x0, 0x0,

    /* U+002D "-" */
    0xd, 0x50, 0x50, 0x50, 0x50, 0x50, 0x4e, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x2c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc,

    /* U+002E "." */
    0x0, 0x7, 0x16, 0x0, 0x22, 0xeb, 0xff, 0x89,
    0x60, 0xff, 0xff, 0xdc, 0x18, 0xd0, 0xf3, 0x6f,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x73, 0xff,
    0xcd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c,
    0xff, 0xfc, 0x17, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x89, 0xff, 0xb6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xe3, 0xff, 0x59, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x42, 0xff, 0xf2, 0x9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x9f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf2, 0xff, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x59, 0xff, 0xe3, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb5, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xfb, 0xff, 0x2b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xce,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xff,
    0xfc, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0, 0x85,
    0xff, 0xb7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xe0, 0xff, 0x5a, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf3, 0xa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9b, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x70, 0x80, 0x2d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x1b, 0x94, 0xdc, 0xf8, 0xf0, 0xc7,
    0x69, 0x3, 0x0, 0x0, 0x0, 0x2c, 0xec, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb3, 0x3, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xa7, 0x55, 0x67, 0xda,
    0xff, 0xff, 0x6f, 0x0, 0x3e, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x1c, 0xf7, 0xff, 0xde, 0x1,
    0x82, 0xff, 0xff, 0x4d, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0xff, 0xff, 0x23, 0xab, 0xff, 0xff, 0x21,
    0x0, 0x0, 0x0, 0x0, 0x86, 0xff, 0xff, 0x4d,
    0xbc, 0xff, 0xff, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x76, 0xff, 0xff, 0x60, 0xc3, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x74, 0xff, 0xff, 0x68,
    0xc4, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x74, 0xff, 0xff, 0x68, 0xc4, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x74, 0xff, 0xff, 0x67,
    0xbd, 0xff, 0xff, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x76, 0xff, 0xff, 0x5e, 0xaa, 0xff, 0xff, 0x23,
    0x0, 0x0, 0x0, 0x0, 0x86, 0xff, 0xff, 0x4c,
    0x80, 0xff, 0xff, 0x51, 0x0, 0x0, 0x0, 0x0,
    0xb1, 0xff, 0xff, 0x22, 0x3a, 0xff, 0xff, 0xb6,
    0x0, 0x0, 0x0, 0x1a, 0xf6, 0xff, 0xdb, 0x0,
    0x0, 0xca, 0xff, 0xff, 0xa9, 0x52, 0x61, 0xd7,
    0xff, 0xff, 0x6b, 0x0, 0x0, 0x28, 0xe8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x3, 0x0,
    0x0, 0x0, 0x18, 0x90, 0xdc, 0xf8, 0xf2, 0xc7,
    0x68, 0x3, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x96, 0xe9,
    0x0, 0x0, 0x20, 0x7d, 0xda, 0xff, 0xff, 0xfc,
    0x4, 0xc1, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x8, 0xff, 0xff, 0xee, 0x9d, 0xe8, 0xff, 0xfc,
    0x6, 0xa2, 0x4b, 0x5, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xfc,

    /* U+0032 "2" */
    0x0, 0x0, 0x0, 0x2f, 0x9b, 0xde, 0xf8, 0xf2,
    0xca, 0x74, 0x7, 0x0, 0x0, 0x0, 0x0, 0x69,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xce,
    0x11, 0x0, 0x0, 0x3d, 0xfd, 0xff, 0xfa, 0x8f,
    0x58, 0x74, 0xea, 0xff, 0xff, 0x98, 0x0, 0x0,
    0xb3, 0xff, 0xff, 0x59, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0xff, 0xf0, 0x0, 0x0, 0xf1, 0xff, 0xed,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd7, 0xff, 0xff,
    0xc, 0x2, 0x60, 0x60, 0x4f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd9, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff,
    0xff, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xca, 0xff, 0xfc, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9d,
    0xff, 0xff, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x84, 0xff, 0xff, 0xb5, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x75,
    0xff, 0xff, 0xcb, 0xc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x66, 0xff, 0xff, 0xd2, 0x12,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58,
    0xfd, 0xff, 0xd9, 0x17, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4c, 0xfa, 0xff, 0xe0, 0x1d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0xf7, 0xff, 0xfc, 0x6b, 0x48, 0x48, 0x48, 0x48,
    0x48, 0x48, 0x36, 0x0, 0xb7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+0033 "3" */
    0x0, 0x0, 0x0, 0x44, 0xac, 0xe5, 0xfa, 0xee,
    0xc1, 0x66, 0x3, 0x0, 0x0, 0x0, 0x0, 0x90,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe,
    0x9, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xef, 0x81,
    0x55, 0x71, 0xe1, 0xff, 0xff, 0x84, 0x0, 0x0,
    0xc1, 0xff, 0xff, 0x3f, 0x0, 0x0, 0x0, 0x24,
    0xfd, 0xff, 0xdd, 0x0, 0x0, 0x68, 0x78, 0x75,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe3, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0xfa, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0x33, 0xbf, 0xff,
    0xfe, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0xff, 0xff, 0xff, 0xff, 0xee, 0x5e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xdb, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0x44, 0x4d, 0x75, 0xe3, 0xff, 0xff,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1a, 0xef, 0xff, 0xeb, 0x7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0x2d, 0x8, 0xc4, 0xc4, 0xa6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xff, 0xff,
    0x34, 0x0, 0xe5, 0xff, 0xfc, 0x2e, 0x0, 0x0,
    0x0, 0x15, 0xec, 0xff, 0xf9, 0xd, 0x0, 0x7d,
    0xff, 0xff, 0xeb, 0x79, 0x51, 0x6e, 0xdc, 0xff,
    0xff, 0x9a, 0x0, 0x0, 0x5, 0xab, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xbe, 0xb, 0x0,
    0x0, 0x0, 0x0, 0x55, 0xb8, 0xec, 0xfb, 0xee,
    0xbe, 0x61, 0x2, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0xe9, 0xff, 0xff,
    0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x43,
    0xff, 0xff, 0xfe, 0xff, 0xe8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xdb, 0xff, 0xb0,
    0xf0, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xf2, 0x1c, 0xf0, 0xff,
    0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfb, 0xff, 0x6e, 0x0, 0xf0, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xcb, 0xff, 0xca,
    0x2, 0x0, 0xf0, 0xff, 0xe8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x71, 0xff, 0xfb, 0x2f, 0x0, 0x0,
    0xf0, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf4, 0xff, 0x89, 0x0, 0x0, 0x0, 0xf0, 0xff,
    0xe8, 0x0, 0x0, 0x0, 0x0, 0xb6, 0xff, 0xde,
    0xa, 0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0x49, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8, 0x19, 0x50,
    0x50, 0x50, 0x50, 0x50, 0x50, 0x50, 0xf5, 0xff,
    0xef, 0x50, 0x50, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0xff, 0xe8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xff, 0xe8, 0x0, 0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0x16, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x32, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x4e, 0xff, 0xff, 0xa3, 0x7c, 0x7c, 0x7c,
    0x7c, 0x7c, 0x7c, 0xf, 0x0, 0x6a, 0xff, 0xff,
    0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x86, 0xff, 0xff, 0x15, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa2, 0xff, 0xf6,
    0x3, 0x2f, 0x48, 0x37, 0x6, 0x0, 0x0, 0x0,
    0x0, 0xbe, 0xff, 0xf5, 0xe0, 0xff, 0xff, 0xff,
    0xf1, 0x73, 0x0, 0x0, 0x0, 0xda, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x81, 0x0,
    0x0, 0x84, 0xca, 0xc9, 0x3e, 0xd, 0x20, 0x88,
    0xfe, 0xff, 0xfc, 0x1f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa1, 0xff, 0xff, 0x73,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xff, 0x9f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0xff, 0xff, 0xaa,
    0x57, 0xcc, 0xcc, 0x4d, 0x0, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0x95, 0x3c, 0xff, 0xff, 0xbe,
    0x1, 0x0, 0x0, 0x2, 0xc2, 0xff, 0xff, 0x59,
    0x1, 0xca, 0xff, 0xff, 0xba, 0x5d, 0x5e, 0xbc,
    0xff, 0xff, 0xdf, 0x6, 0x0, 0x20, 0xdc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x32, 0x0,
    0x0, 0x0, 0xd, 0x7c, 0xd0, 0xf6, 0xf7, 0xd8,
    0x8e, 0x19, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x0, 0xf, 0x71, 0xb9, 0xe3,
    0xf8, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58,
    0xec, 0xff, 0xff, 0xff, 0xff, 0x24, 0x0, 0x0,
    0x0, 0x0, 0x64, 0xfe, 0xff, 0xff, 0xc6, 0x83,
    0x69, 0xe, 0x0, 0x0, 0x0, 0x2b, 0xf7, 0xff,
    0xeb, 0x44, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xab, 0xff, 0xfd, 0x3b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0xfb, 0xff, 0xaf,
    0x0, 0x23, 0x48, 0x3d, 0xb, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0x86, 0xc3, 0xff, 0xff, 0xff,
    0xf5, 0x79, 0x0, 0x0, 0x7e, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7c, 0x0,
    0x91, 0xff, 0xff, 0xfa, 0x76, 0x16, 0x17, 0x7c,
    0xfd, 0xff, 0xfa, 0x19, 0x9e, 0xff, 0xff, 0x71,
    0x0, 0x0, 0x0, 0x0, 0xa1, 0xff, 0xff, 0x6a,
    0x9b, 0xff, 0xff, 0x39, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xff, 0x95, 0x89, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x35, 0xff, 0xff, 0x9d,
    0x5a, 0xff, 0xff, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0x85, 0x11, 0xf5, 0xff, 0xeb,
    0x13, 0x0, 0x0, 0x4, 0xc6, 0xff, 0xff, 0x43,
    0x0, 0x80, 0xff, 0xff, 0xd8, 0x67, 0x65, 0xc3,
    0xff, 0xff, 0xc8, 0x0, 0x0, 0x4, 0xa9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0x1f, 0x0,
    0x0, 0x0, 0x1, 0x5d, 0xc3, 0xf2, 0xf7, 0xd6,
    0x83, 0xf, 0x0, 0x0,

    /* U+0037 "7" */
    0x30, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa4, 0x30, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x9a, 0xf, 0x50, 0x50, 0x50, 0x50, 0x50,
    0x50, 0x50, 0x50, 0xab, 0xff, 0xff, 0x3c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xe3, 0xff, 0xcb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x60, 0xff, 0xff, 0x59,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xd5, 0xff, 0xe3, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff,
    0x77, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc4, 0xff, 0xf4, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xff,
    0xff, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb2, 0xff, 0xfd, 0x24, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0xfe, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x3f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1b, 0xfa, 0xff, 0xce, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8d, 0xff, 0xff,
    0x5c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0xf2, 0xff, 0xe5, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xff,
    0xff, 0x79, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xe8, 0xff, 0xf5, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x2a, 0x9e, 0xe0, 0xf9, 0xf4, 0xce,
    0x7a, 0xa, 0x0, 0x0, 0x0, 0x4c, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x14, 0x0,
    0xc, 0xed, 0xff, 0xff, 0xa6, 0x59, 0x6a, 0xdc,
    0xff, 0xff, 0x9c, 0x0, 0x4b, 0xff, 0xff, 0xb5,
    0x0, 0x0, 0x0, 0x1d, 0xf9, 0xff, 0xee, 0x0,
    0x5a, 0xff, 0xff, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0xdb, 0xff, 0xfb, 0x2, 0x2b, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x1b, 0xf9, 0xff, 0xcc, 0x0,
    0x0, 0xa0, 0xff, 0xff, 0xac, 0x5d, 0x6c, 0xdb,
    0xff, 0xfb, 0x44, 0x0, 0x0, 0x4, 0x8a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xea, 0x43, 0x0, 0x0,
    0x0, 0x31, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x9f, 0x5, 0x0, 0x16, 0xe9, 0xff, 0xef,
    0x5a, 0xf, 0x20, 0x9c, 0xff, 0xff, 0x96, 0x0,
    0x7f, 0xff, 0xff, 0x65, 0x0, 0x0, 0x0, 0x1,
    0xc9, 0xff, 0xfc, 0x1f, 0xb6, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x84, 0xff, 0xff, 0x55,
    0xbd, 0xff, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x5c, 0x91, 0xff, 0xff, 0x85,
    0x0, 0x0, 0x0, 0x7, 0xdc, 0xff, 0xff, 0x30,
    0x2c, 0xfc, 0xff, 0xfe, 0x9b, 0x56, 0x64, 0xcd,
    0xff, 0xff, 0xc7, 0x0, 0x0, 0x62, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdd, 0x1f, 0x0,
    0x0, 0x0, 0x2f, 0xa0, 0xe0, 0xf9, 0xf4, 0xce,
    0x7d, 0xe, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x24, 0x96, 0xde, 0xf8, 0xe8, 0xa9,
    0x2e, 0x0, 0x0, 0x0, 0x0, 0x45, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x53, 0x0, 0x0,
    0x18, 0xec, 0xff, 0xff, 0xa0, 0x5c, 0x85, 0xf8,
    0xff, 0xf5, 0x22, 0x0, 0x7d, 0xff, 0xff, 0x8b,
    0x0, 0x0, 0x0, 0x63, 0xff, 0xff, 0x98, 0x0,
    0xc4, 0xff, 0xff, 0x16, 0x0, 0x0, 0x0, 0x3,
    0xe4, 0xff, 0xeb, 0x0, 0xe0, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb7, 0xff, 0xff, 0x1c,
    0xda, 0xff, 0xfd, 0x7, 0x0, 0x0, 0x0, 0x0,
    0xa0, 0xff, 0xff, 0x30, 0xb0, 0xff, 0xff, 0x55,
    0x0, 0x0, 0x0, 0x2, 0xcb, 0xff, 0xff, 0x37,
    0x58, 0xff, 0xff, 0xe9, 0x4c, 0xb, 0x2a, 0xb0,
    0xff, 0xff, 0xff, 0x2b, 0x1, 0xb9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0x1a,
    0x0, 0x9, 0x97, 0xf9, 0xff, 0xff, 0xef, 0x6e,
    0xbf, 0xff, 0xf1, 0x1, 0x0, 0x0, 0x0, 0xc,
    0x35, 0x33, 0x7, 0x7, 0xf4, 0xff, 0xbb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0x5e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x54, 0xf8, 0xff, 0xdb, 0x8, 0x0,
    0x0, 0x0, 0x2a, 0x68, 0x85, 0xcc, 0xff, 0xff,
    0xf6, 0x38, 0x0, 0x0, 0x0, 0x0, 0x70, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf5, 0xe0, 0xae, 0x63, 0x6,
    0x0, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x1e, 0xd5, 0xf0, 0x5e, 0x6c, 0xff, 0xff, 0xcc,
    0x2e, 0xf1, 0xff, 0x7f, 0x0, 0xb, 0x19, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x14, 0x0,
    0x2c, 0xf0, 0xff, 0x79, 0x6d, 0xff, 0xff, 0xcc,
    0x1f, 0xd5, 0xf0, 0x62,

    /* U+003B ";" */
    0x0, 0x48, 0xea, 0xe0, 0x30, 0x0, 0xac, 0xff,
    0xff, 0x8c, 0x0, 0x62, 0xfd, 0xf9, 0x46, 0x0,
    0x0, 0x14, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x39, 0xd8, 0xd8, 0x40, 0x0,
    0x44, 0xff, 0xff, 0x4c, 0x0, 0x4c, 0xff, 0xff,
    0x41, 0x0, 0x73, 0xff, 0xfe, 0x18, 0x0, 0xbd,
    0xff, 0xba, 0x0, 0x39, 0xff, 0xfb, 0x30, 0x0,
    0x4, 0x71, 0x59, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x18, 0x84, 0x78, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xa0, 0xf9, 0xff, 0x8c, 0x0, 0x0,
    0x0, 0x1, 0x49, 0xbb, 0xff, 0xff, 0xff, 0xff,
    0x86, 0x0, 0x8, 0x65, 0xd6, 0xff, 0xff, 0xff,
    0xfa, 0xac, 0x49, 0x2, 0x2d, 0xea, 0xff, 0xff,
    0xff, 0xd6, 0x73, 0x15, 0x0, 0x0, 0x0, 0x44,
    0xff, 0xff, 0xf3, 0x58, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x31, 0xf3, 0xff, 0xff, 0xfd, 0xb9,
    0x58, 0x7, 0x0, 0x0, 0x0, 0x0, 0x10, 0x76,
    0xe4, 0xff, 0xff, 0xff, 0xee, 0x94, 0x32, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x5c, 0xce, 0xff, 0xff,
    0xff, 0xff, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x41, 0xb3, 0xfe, 0xff, 0x8c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0x98,
    0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1,

    /* U+003D "=" */
    0x1c, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60,
    0x60, 0x60, 0x4c, 0x4c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xcc, 0x4c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c,
    0x5c, 0x49, 0x4c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcc, 0x4c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcc,

    /* U+003E ">" */
    0x70, 0x8d, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x80, 0xff, 0xfd, 0xaf, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x75, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x63, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x33, 0x91, 0xea, 0xff, 0xff, 0xff,
    0xec, 0x86, 0x1c, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x4e, 0xac, 0xf8, 0xff, 0xff, 0xfb, 0x77, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0xca, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x2, 0x45, 0xa6,
    0xf7, 0xff, 0xff, 0xff, 0x81, 0x0, 0x24, 0x84,
    0xe3, 0xff, 0xff, 0xff, 0xf5, 0x98, 0x2a, 0x0,
    0x6e, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x76, 0x11,
    0x0, 0x0, 0x0, 0x80, 0xff, 0xff, 0xc3, 0x54,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0xa1,
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x1f, 0x97, 0xdd, 0xf9, 0xf3, 0xca,
    0x6f, 0x4, 0x0, 0x0, 0x36, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbb, 0x4, 0x1, 0xd8,
    0xff, 0xff, 0xc1, 0x71, 0x84, 0xef, 0xff, 0xff,
    0x65, 0x2e, 0xff, 0xff, 0xcc, 0x2, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xaf, 0x1d, 0x6c, 0x6c, 0x3f,
    0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x47, 0xff,
    0xff, 0x96, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x14, 0xd7, 0xff, 0xfc, 0x2e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x15, 0xd4, 0xff, 0xff, 0x77, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xcd, 0xff, 0xff,
    0x86, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x75,
    0xff, 0xff, 0x8f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc2, 0xff, 0xf6, 0xc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc7, 0xe8, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb1, 0xff,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0x14, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa1, 0xf6, 0xa4, 0x0,
    0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x97,
    0xd2, 0xf2, 0xfb, 0xec, 0xc6, 0x85, 0x25, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0xbd, 0xff, 0xff, 0xf3, 0xcd, 0xc6, 0xda,
    0xf9, 0xff, 0xfc, 0x93, 0x9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0xf1, 0xff, 0xbf, 0x44,
    0x2, 0x0, 0x0, 0x0, 0xd, 0x56, 0xd6, 0xff,
    0xc4, 0xc, 0x0, 0x0, 0x0, 0x0, 0x2a, 0xf3,
    0xff, 0x75, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xa9, 0xff, 0xa5, 0x0, 0x0,
    0x0, 0x3, 0xd1, 0xff, 0x7b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xce, 0xff, 0x3d, 0x0, 0x0, 0x65, 0xff, 0xc9,
    0x1, 0x0, 0x0, 0x0, 0x5b, 0xce, 0xf5, 0xdb,
    0xa1, 0x22, 0x0, 0x0, 0x3f, 0xff, 0xad, 0x0,
    0x0, 0xd6, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x8e,
    0xff, 0xff, 0xdf, 0xf6, 0xff, 0xf3, 0x3, 0x0,
    0x1, 0xde, 0xf8, 0x7, 0x2e, 0xff, 0xed, 0x5,
    0x0, 0x0, 0x54, 0xff, 0xef, 0x39, 0x0, 0x2b,
    0xff, 0xef, 0x0, 0x0, 0x0, 0xa3, 0xff, 0x37,
    0x70, 0xff, 0xa0, 0x0, 0x0, 0x1, 0xde, 0xff,
    0x64, 0x0, 0x0, 0x41, 0xff, 0xd7, 0x0, 0x0,
    0x0, 0x7c, 0xff, 0x57, 0xa0, 0xff, 0x76, 0x0,
    0x0, 0x3d, 0xff, 0xf5, 0x9, 0x0, 0x0, 0x57,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x71, 0xff, 0x66,
    0xbd, 0xff, 0x55, 0x0, 0x0, 0x7f, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xa9, 0x0, 0x0,
    0x0, 0x6e, 0xff, 0x65, 0xcb, 0xff, 0x43, 0x0,
    0x0, 0xab, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x84,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x88, 0xff, 0x51,
    0xca, 0xff, 0x4c, 0x0, 0x0, 0xbd, 0xff, 0x92,
    0x0, 0x0, 0x0, 0x9b, 0xff, 0x7a, 0x0, 0x0,
    0x0, 0xb1, 0xff, 0x27, 0xb8, 0xff, 0x57, 0x0,
    0x0, 0xb1, 0xff, 0xaa, 0x0, 0x0, 0x6, 0xe3,
    0xff, 0x70, 0x0, 0x0, 0x14, 0xf9, 0xe0, 0x0,
    0x96, 0xff, 0x87, 0x0, 0x0, 0x7e, 0xff, 0xf4,
    0x38, 0xa, 0xa1, 0xff, 0xff, 0x9d, 0x0, 0x1,
    0xa0, 0xff, 0x6f, 0x0, 0x5e, 0xff, 0xc8, 0x0,
    0x0, 0x1b, 0xf2, 0xff, 0xff, 0xfd, 0xff, 0x8d,
    0xff, 0xff, 0xbe, 0xd6, 0xff, 0xbb, 0x3, 0x0,
    0x12, 0xf9, 0xfd, 0x32, 0x0, 0x0, 0x3d, 0xcf,
    0xf8, 0xd2, 0x50, 0x0, 0x5f, 0xdc, 0xf9, 0xdc,
    0x7f, 0x7, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xc2,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x19, 0xeb, 0xff, 0xa9, 0x7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xf3,
    0xff, 0xcf, 0x55, 0xa, 0x0, 0x0, 0x0, 0x8,
    0x48, 0x48, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xcd, 0xff, 0xff, 0xfc,
    0xdc, 0xd0, 0xe6, 0xfd, 0xff, 0xb7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x48, 0x9d, 0xd4, 0xf2, 0xfc, 0xef, 0xca,
    0x8a, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xe5, 0xff,
    0xff, 0xe0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xff,
    0xff, 0xff, 0x43, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xab, 0xff, 0xf6,
    0xf9, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0xf9, 0xff, 0xa8,
    0xb1, 0xff, 0xf6, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xff, 0x4e,
    0x57, 0xff, 0xff, 0x67, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xce, 0xff, 0xee, 0x6,
    0x9, 0xf3, 0xff, 0xc8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0x9a, 0x0,
    0x0, 0xa3, 0xff, 0xff, 0x2a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x90, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x49, 0xff, 0xff, 0x8b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xeb, 0xff, 0xe4, 0x2, 0x0,
    0x0, 0x4, 0xea, 0xff, 0xe8, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x52, 0xff, 0xff, 0xc6, 0x68, 0x68,
    0x68, 0x68, 0xcb, 0xff, 0xff, 0x4e, 0x0, 0x0,
    0x0, 0x0, 0xb3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0x0, 0x0,
    0x0, 0x18, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x16, 0x0,
    0x0, 0x75, 0xff, 0xff, 0x7f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0x72, 0x0,
    0x0, 0xd5, 0xff, 0xff, 0x27, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x32, 0xff, 0xff, 0xd3, 0x0,
    0x37, 0xff, 0xff, 0xcf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd7, 0xff, 0xff, 0x35,
    0x98, 0xff, 0xff, 0x77, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xff, 0x97,

    /* U+0042 "B" */
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xf3,
    0xd3, 0x95, 0x29, 0x0, 0x0, 0x44, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x5b, 0x0, 0x44, 0xff, 0xff, 0xd2, 0x68, 0x68,
    0x6a, 0x81, 0xce, 0xff, 0xff, 0xf5, 0x18, 0x44,
    0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xc6, 0xff, 0xff, 0x61, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x81, 0xff, 0xff,
    0x78, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa5, 0xff, 0xff, 0x54, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x2, 0x1c, 0x7c, 0xfe,
    0xff, 0xd6, 0x5, 0x44, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xba, 0x1b, 0x0,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc8, 0x3a, 0x0, 0x44, 0xff, 0xff,
    0xc5, 0x3c, 0x3c, 0x3c, 0x47, 0x7e, 0xf3, 0xff,
    0xf5, 0x2d, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x63, 0xff, 0xff, 0xa5, 0x44,
    0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xd7, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0xff, 0xff,
    0xdb, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0xff, 0xff, 0xb1, 0x44, 0xff,
    0xff, 0xd0, 0x60, 0x60, 0x60, 0x72, 0xb5, 0xff,
    0xff, 0xff, 0x4c, 0x44, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x86, 0x0,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xe0, 0xa6, 0x3e, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x11, 0x73, 0xc8, 0xe9, 0xf9,
    0xe4, 0xad, 0x4a, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa9, 0x6, 0x0, 0x0, 0x2b, 0xf7, 0xff,
    0xff, 0xca, 0x7f, 0x77, 0x9e, 0xf6, 0xff, 0xff,
    0x9a, 0x0, 0x0, 0xc9, 0xff, 0xff, 0x85, 0x0,
    0x0, 0x0, 0x0, 0x24, 0xf1, 0xff, 0xfe, 0x2b,
    0x35, 0xff, 0xff, 0xd1, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x83, 0xff, 0xff, 0x82, 0x85, 0xff,
    0xff, 0x75, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x37, 0xbc, 0xbc, 0x80, 0xab, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc3, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcc, 0xff, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc6, 0xff,
    0xff, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xad, 0xff, 0xff, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0xff, 0xff, 0x6f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x30, 0xa8, 0xa8, 0x74,
    0x3c, 0xff, 0xff, 0xc7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x83, 0xff, 0xff, 0x88, 0x1, 0xd4,
    0xff, 0xff, 0x71, 0x0, 0x0, 0x0, 0x0, 0x23,
    0xf0, 0xff, 0xff, 0x31, 0x0, 0x36, 0xfc, 0xff,
    0xff, 0xbc, 0x76, 0x75, 0x9d, 0xf6, 0xff, 0xff,
    0x9f, 0x0, 0x0, 0x0, 0x49, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa9, 0x6, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0x81, 0xcf, 0xef, 0xf8,
    0xe2, 0xaa, 0x48, 0x0, 0x0, 0x0,

    /* U+0044 "D" */
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xd1,
    0x8c, 0x22, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x6d, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xd2,
    0x68, 0x68, 0x78, 0xb0, 0xfc, 0xff, 0xff, 0x85,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xea, 0xff, 0xfd, 0x3f, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x51, 0xff, 0xff, 0xbc, 0x0, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xe0, 0xff, 0xfd, 0x1b, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0xff,
    0xff, 0x4b, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8d, 0xff, 0xff, 0x67,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x84, 0xff, 0xff, 0x77, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x68, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0xff,
    0xff, 0x4d, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xe2, 0xff, 0xfe, 0x1e,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x55, 0xff, 0xff, 0xc0, 0x0, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x38, 0xec,
    0xff, 0xfe, 0x45, 0x0, 0x44, 0xff, 0xff, 0xd0,
    0x60, 0x61, 0x73, 0xb0, 0xfd, 0xff, 0xff, 0x89,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x70, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xe7, 0xc9,
    0x7e, 0x1d, 0x0, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x44, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x44, 0xff, 0xff, 0xd2, 0x68, 0x68, 0x68, 0x68,
    0x68, 0x68, 0x68, 0x54, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb4, 0x0, 0x44, 0xff, 0xff, 0xcf,
    0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x40, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xd0, 0x60, 0x60, 0x60, 0x60,
    0x60, 0x60, 0x60, 0x55, 0x44, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe4,

    /* U+0046 "F" */
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x94, 0x44, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x94,
    0x44, 0xff, 0xff, 0xd2, 0x68, 0x68, 0x68, 0x68,
    0x68, 0x68, 0x68, 0x3c, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xd0,
    0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x36, 0x0,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x44, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x14, 0x82, 0xcc, 0xef, 0xfa,
    0xe9, 0xb9, 0x63, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x47, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0x19, 0x0, 0x0, 0x32, 0xfa, 0xff,
    0xff, 0xcc, 0x7b, 0x6c, 0x8e, 0xeb, 0xff, 0xff,
    0xcb, 0x4, 0x0, 0xcd, 0xff, 0xff, 0x8d, 0x1,
    0x0, 0x0, 0x0, 0x11, 0xd8, 0xff, 0xff, 0x56,
    0x38, 0xff, 0xff, 0xdb, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x58, 0xff, 0xff, 0xa6, 0x7c, 0xff,
    0xff, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x34, 0x34, 0x28, 0xa7, 0xff, 0xff, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0xff, 0x38, 0x0, 0x0, 0x0, 0xa8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xb8, 0xff,
    0xff, 0x3e, 0x0, 0x0, 0x0, 0xa8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x9d, 0xff, 0xff, 0x55,
    0x0, 0x0, 0x0, 0x2c, 0x44, 0x44, 0x5e, 0xff,
    0xff, 0xd0, 0x77, 0xff, 0xff, 0x8e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x24, 0xff, 0xff, 0xd0,
    0x24, 0xff, 0xff, 0xe9, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x24, 0xff, 0xff, 0xd0, 0x0, 0xb7,
    0xff, 0xff, 0xb0, 0x8, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xd0, 0x0, 0x1b, 0xed, 0xff,
    0xff, 0xdf, 0x85, 0x65, 0x6e, 0xa2, 0xf7, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x29, 0xd7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb6, 0xe,
    0x0, 0x0, 0x0, 0x9, 0x5f, 0xbb, 0xe2, 0xf9,
    0xed, 0xd3, 0x96, 0x3f, 0x0, 0x0,

    /* U+0048 "H" */
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xac, 0xff, 0xff, 0x48, 0x44,
    0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xac, 0xff, 0xff, 0x48, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xac, 0xff, 0xff, 0x48, 0x44, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xac, 0xff, 0xff, 0x48, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xac,
    0xff, 0xff, 0x48, 0x44, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xff,
    0xff, 0x48, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xff, 0xff,
    0x48, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x48,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x48, 0x44,
    0xff, 0xff, 0xd1, 0x64, 0x64, 0x64, 0x64, 0x64,
    0x64, 0x64, 0xcc, 0xff, 0xff, 0x48, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xac, 0xff, 0xff, 0x48, 0x44, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xac, 0xff, 0xff, 0x48, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xac,
    0xff, 0xff, 0x48, 0x44, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xff,
    0xff, 0x48, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xff, 0xff,
    0x48, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xac, 0xff, 0xff, 0x48,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xac, 0xff, 0xff, 0x48,

    /* U+0049 "I" */
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc, 0x18, 0xff, 0xff, 0xdc,
    0x18, 0xff, 0xff, 0xdc,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0xff, 0xac, 0x5, 0xc, 0xc, 0x5,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xff, 0xff, 0xab,
    0x6d, 0xff, 0xff, 0x8f, 0x0, 0x0, 0x0, 0x0,
    0x66, 0xff, 0xff, 0x9b, 0x43, 0xff, 0xff, 0xdb,
    0x7, 0x0, 0x0, 0x3, 0xc9, 0xff, 0xff, 0x67,
    0x5, 0xdd, 0xff, 0xff, 0xd1, 0x77, 0x72, 0xc9,
    0xff, 0xff, 0xe9, 0xe, 0x0, 0x35, 0xee, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0x3b, 0x0,
    0x0, 0x0, 0x1b, 0x8e, 0xd6, 0xf6, 0xf8, 0xdb,
    0x91, 0x1d, 0x0, 0x0,

    /* U+004B "K" */
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xef, 0xff, 0xff, 0x7f, 0x0, 0x44,
    0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x12,
    0xdb, 0xff, 0xff, 0xa7, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x5, 0xbe, 0xff,
    0xff, 0xc8, 0x8, 0x0, 0x0, 0x44, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x9a, 0xff, 0xff, 0xe1,
    0x17, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x71, 0xff, 0xff, 0xf3, 0x2e, 0x0,
    0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0,
    0x49, 0xfc, 0xff, 0xfd, 0x4c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x28, 0xf1,
    0xff, 0xff, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x44, 0xff, 0xff, 0xc4, 0xda, 0xff, 0xff,
    0xc6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x44, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x44,
    0xff, 0xff, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xe5,
    0x12, 0x0, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xff, 0xc8, 0xe, 0xc1, 0xff, 0xff, 0xad,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff,
    0xdd, 0x12, 0x0, 0x1f, 0xf1, 0xff, 0xff, 0x62,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf3, 0x24,
    0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0xac, 0xff, 0xff, 0xc8, 0x4,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x13, 0xe6, 0xff, 0xff, 0x82, 0x0,
    0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x48, 0xff, 0xff, 0xfc, 0x3b, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x95, 0xff, 0xff, 0xde, 0xe,

    /* U+004C "L" */
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xd0, 0x60, 0x60, 0x60, 0x60,
    0x60, 0x60, 0x60, 0x2b, 0x44, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x74,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x74,

    /* U+004D "M" */
    0x44, 0xff, 0xff, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc9, 0xff,
    0xff, 0xff, 0x40, 0x44, 0xff, 0xff, 0xff, 0xff,
    0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xff, 0xff, 0xff, 0xff, 0x40, 0x44, 0xff,
    0xff, 0xff, 0xff, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x89, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x44, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xe6, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x44, 0xff, 0xff, 0xcb,
    0xff, 0xff, 0x46, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4a, 0xff, 0xff, 0xcb, 0xff, 0xff, 0x40, 0x44,
    0xff, 0xff, 0x80, 0xf3, 0xff, 0xa7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaa, 0xff, 0xef, 0x84, 0xff,
    0xff, 0x40, 0x44, 0xff, 0xff, 0x7e, 0x9d, 0xff,
    0xf7, 0x10, 0x0, 0x0, 0x0, 0x11, 0xf8, 0xff,
    0x94, 0x85, 0xff, 0xff, 0x40, 0x44, 0xff, 0xff,
    0x88, 0x3b, 0xff, 0xff, 0x68, 0x0, 0x0, 0x0,
    0x6b, 0xff, 0xff, 0x32, 0x8f, 0xff, 0xff, 0x40,
    0x44, 0xff, 0xff, 0x92, 0x0, 0xd7, 0xff, 0xc9,
    0x0, 0x0, 0x0, 0xcc, 0xff, 0xcf, 0x0, 0x98,
    0xff, 0xff, 0x40, 0x44, 0xff, 0xff, 0x9c, 0x0,
    0x76, 0xff, 0xff, 0x2a, 0x0, 0x2b, 0xff, 0xff,
    0x6d, 0x0, 0xa2, 0xff, 0xff, 0x40, 0x44, 0xff,
    0xff, 0xa6, 0x0, 0x18, 0xfb, 0xff, 0x8a, 0x0,
    0x8c, 0xff, 0xf8, 0x12, 0x0, 0xac, 0xff, 0xff,
    0x40, 0x44, 0xff, 0xff, 0xaf, 0x0, 0x0, 0xb1,
    0xff, 0xe7, 0x8, 0xe8, 0xff, 0xa8, 0x0, 0x0,
    0xb3, 0xff, 0xff, 0x40, 0x44, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0x99, 0xff, 0xff,
    0x46, 0x0, 0x0, 0xb4, 0xff, 0xff, 0x40, 0x44,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x4, 0xe8, 0xff,
    0xff, 0xff, 0xe1, 0x2, 0x0, 0x0, 0xb4, 0xff,
    0xff, 0x40, 0x44, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x8a, 0xff, 0xff, 0xff, 0x81, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0xff, 0x40, 0x44, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x28, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xff, 0x40,
    0x44, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xc5, 0xff, 0xbd, 0x0, 0x0, 0x0, 0x0, 0xb4,
    0xff, 0xff, 0x40,

    /* U+004E "N" */
    0x44, 0xff, 0xff, 0xed, 0x16, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xac, 0xff, 0xff, 0x44, 0x44,
    0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xac, 0xff, 0xff, 0x44, 0x44, 0xff,
    0xff, 0xff, 0xff, 0x46, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xac, 0xff, 0xff, 0x44, 0x44, 0xff, 0xff,
    0xff, 0xff, 0xdd, 0x9, 0x0, 0x0, 0x0, 0x0,
    0xac, 0xff, 0xff, 0x44, 0x44, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0xac,
    0xff, 0xff, 0x44, 0x44, 0xff, 0xff, 0xc1, 0xe3,
    0xff, 0xfb, 0x2e, 0x0, 0x0, 0x0, 0xac, 0xff,
    0xff, 0x44, 0x44, 0xff, 0xff, 0xb4, 0x50, 0xff,
    0xff, 0xc9, 0x2, 0x0, 0x0, 0xac, 0xff, 0xff,
    0x44, 0x44, 0xff, 0xff, 0xb4, 0x0, 0xae, 0xff,
    0xff, 0x6c, 0x0, 0x0, 0xac, 0xff, 0xff, 0x44,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x1b, 0xf1, 0xff,
    0xf2, 0x1b, 0x0, 0xac, 0xff, 0xff, 0x44, 0x44,
    0xff, 0xff, 0xb4, 0x0, 0x0, 0x6b, 0xff, 0xff,
    0xae, 0x0, 0xac, 0xff, 0xff, 0x44, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x2, 0xc7, 0xff, 0xff,
    0x50, 0xac, 0xff, 0xff, 0x44, 0x44, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0xff, 0xe3,
    0xb9, 0xff, 0xff, 0x44, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x87, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x44, 0x44, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xdc, 0xff, 0xff, 0xff,
    0xff, 0x44, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xff, 0xff,
    0x44, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa2, 0xff, 0xff, 0xff, 0x44,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xec, 0xff, 0xff, 0x44,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0xb, 0x68, 0xc3, 0xe7, 0xf5,
    0xd8, 0xa3, 0x3a, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2a, 0xde, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x96, 0x4, 0x0, 0x0, 0x0, 0x1c,
    0xef, 0xff, 0xff, 0xe1, 0x94, 0x83, 0xad, 0xfa,
    0xff, 0xff, 0x9d, 0x0, 0x0, 0x0, 0xb4, 0xff,
    0xff, 0xa9, 0x8, 0x0, 0x0, 0x0, 0x30, 0xec,
    0xff, 0xff, 0x45, 0x0, 0x25, 0xff, 0xff, 0xe3,
    0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xff,
    0xff, 0xb3, 0x0, 0x78, 0xff, 0xff, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xee, 0xff,
    0xf9, 0xe, 0xa5, 0xff, 0xff, 0x49, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff,
    0x33, 0xbe, 0xff, 0xff, 0x2f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa5, 0xff, 0xff, 0x4e,
    0xcb, 0xff, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9c, 0xff, 0xff, 0x5b, 0xc0,
    0xff, 0xff, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa4, 0xff, 0xff, 0x4d, 0xa7, 0xff,
    0xff, 0x48, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xba, 0xff, 0xff, 0x35, 0x7c, 0xff, 0xff,
    0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xeb, 0xff, 0xfa, 0x10, 0x28, 0xff, 0xff, 0xe1,
    0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x52, 0xff,
    0xff, 0xb8, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xa4,
    0x6, 0x0, 0x0, 0x0, 0x26, 0xe5, 0xff, 0xff,
    0x4b, 0x0, 0x0, 0x1e, 0xf0, 0xff, 0xff, 0xdd,
    0x8c, 0x7a, 0xa2, 0xf6, 0xff, 0xff, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x9d, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x69, 0xc3, 0xe7, 0xf6,
    0xda, 0xa6, 0x3e, 0x0, 0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xe6, 0xb4, 0x58, 0x1, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xbd, 0xd, 0x0, 0x44, 0xff, 0xff, 0xd4,
    0x6c, 0x6c, 0x6c, 0x74, 0x9d, 0xf8, 0xff, 0xff,
    0xaf, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xf6, 0xff, 0xff, 0x2d,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa0, 0xff, 0xff, 0x68, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8a, 0xff, 0xff, 0x74, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc9, 0xff,
    0xff, 0x54, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x8, 0x2d, 0xa6, 0xff, 0xff, 0xf0, 0xf,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x59, 0x0, 0x44, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcd, 0x43, 0x0, 0x0, 0x44, 0xff, 0xff, 0xd1,
    0x64, 0x64, 0x64, 0x5f, 0x4e, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0xf, 0x70, 0xc7, 0xe9, 0xf4,
    0xd6, 0x9d, 0x33, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x35, 0xe7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8a, 0x2, 0x0, 0x0, 0x0, 0x29,
    0xf6, 0xff, 0xff, 0xdb, 0x90, 0x85, 0xb3, 0xfd,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x1, 0xc6, 0xff,
    0xff, 0x98, 0x4, 0x0, 0x0, 0x0, 0x3c, 0xf2,
    0xff, 0xfe, 0x3a, 0x0, 0x39, 0xff, 0xff, 0xd5,
    0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0xa7, 0x0, 0x8c, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf8, 0xff,
    0xf3, 0x7, 0xb9, 0xff, 0xff, 0x37, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0x25, 0xd2, 0xff, 0xff, 0x1e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0x3f,
    0xdf, 0xff, 0xff, 0x18, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0xff, 0xff, 0x4b, 0xd3,
    0xff, 0xff, 0x1f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0x3e, 0xb9, 0xff,
    0xff, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xce, 0xff, 0xff, 0x28, 0x8e, 0xff, 0xff,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf8, 0xff, 0xf4, 0x7, 0x3b, 0xff, 0xff, 0xd6,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x66, 0xff,
    0xff, 0xad, 0x0, 0x1, 0xca, 0xff, 0xff, 0x95,
    0x3, 0x0, 0x0, 0x0, 0x31, 0xee, 0xff, 0xfe,
    0x39, 0x0, 0x0, 0x2a, 0xf6, 0xff, 0xff, 0xd6,
    0x88, 0x7b, 0xa8, 0xfa, 0xff, 0xff, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x36, 0xe6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xae, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x70, 0xc7, 0xea, 0xf7,
    0xe5, 0xff, 0xff, 0xfa, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x71, 0xfb, 0xff, 0xff, 0xa5, 0xa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x40, 0xea, 0xff, 0xb4, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0x81, 0x4, 0x0,

    /* U+0052 "R" */
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xd7, 0x9a, 0x30, 0x0, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x75, 0x0, 0x0, 0x44, 0xff, 0xff, 0xd4,
    0x6c, 0x6c, 0x6c, 0x83, 0xc9, 0xff, 0xff, 0xff,
    0x43, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xa0, 0xff, 0xff, 0xac, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xda, 0x0, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xda, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x65, 0xff, 0xff,
    0xb3, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x1, 0x19, 0x68, 0xf2, 0xff, 0xff, 0x4b, 0x0,
    0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8a, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x4b, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff, 0xd1,
    0x64, 0x64, 0x64, 0xe7, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x6d, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x6,
    0xe1, 0xff, 0xff, 0x3d, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x64, 0xff,
    0xff, 0xc3, 0x0, 0x0, 0x44, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xdb, 0xff, 0xff,
    0x4a, 0x0, 0x44, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5b, 0xff, 0xff, 0xcf, 0x1,
    0x44, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xd4, 0xff, 0xff, 0x58,

    /* U+0053 "S" */
    0x0, 0x0, 0x0, 0x10, 0x78, 0xc7, 0xef, 0xfa,
    0xe5, 0xb5, 0x5b, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x36, 0xe7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc7, 0x13, 0x0, 0x0, 0x15, 0xeb, 0xff,
    0xff, 0xd5, 0x80, 0x6c, 0x8a, 0xe7, 0xff, 0xff,
    0xc3, 0x0, 0x0, 0x71, 0xff, 0xff, 0xbf, 0x3,
    0x0, 0x0, 0x0, 0xe, 0xda, 0xff, 0xff, 0x49,
    0x0, 0x94, 0xff, 0xff, 0x6b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x75, 0xff, 0xff, 0x84, 0x0, 0x77,
    0xff, 0xff, 0xaa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x1c, 0x1c, 0x10, 0x0, 0x1a, 0xf3, 0xff,
    0xff, 0xad, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x47, 0xf1, 0xff, 0xff,
    0xff, 0xd6, 0x83, 0x2d, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0xa0, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xbe, 0x35, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x14, 0x67, 0xba, 0xfb, 0xff,
    0xff, 0xfa, 0x5f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x18, 0x8e, 0xff, 0xff,
    0xfa, 0x24, 0x7, 0x44, 0x44, 0x3a, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xff, 0x7c,
    0x6, 0xfc, 0xff, 0xf6, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x66, 0xff, 0xff, 0x98, 0x0, 0xc1,
    0xff, 0xff, 0x7c, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xb5, 0xff, 0xff, 0x7d, 0x0, 0x39, 0xf8, 0xff,
    0xff, 0xc3, 0x7b, 0x63, 0x77, 0xca, 0xff, 0xff,
    0xf7, 0x23, 0x0, 0x0, 0x49, 0xe9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x54, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x73, 0xbf, 0xe8, 0xfb,
    0xf2, 0xce, 0x89, 0x1e, 0x0, 0x0,

    /* U+0054 "T" */
    0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x78,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x30, 0x68,
    0x68, 0x68, 0x68, 0x86, 0xff, 0xff, 0xda, 0x68,
    0x68, 0x68, 0x68, 0x68, 0x6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x34, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x34, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x34, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x34, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x88, 0xff, 0xff, 0x6c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0x38, 0x88, 0xff,
    0xff, 0x6c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0x38, 0x88, 0xff, 0xff, 0x6c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xff,
    0xff, 0x38, 0x88, 0xff, 0xff, 0x6c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0x38,
    0x88, 0xff, 0xff, 0x6c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0x38, 0x88, 0xff,
    0xff, 0x6c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0x38, 0x88, 0xff, 0xff, 0x6c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xff,
    0xff, 0x38, 0x88, 0xff, 0xff, 0x6c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0x38,
    0x88, 0xff, 0xff, 0x6c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0x38, 0x88, 0xff,
    0xff, 0x6c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0x38, 0x88, 0xff, 0xff, 0x6c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xff,
    0xff, 0x38, 0x81, 0xff, 0xff, 0x78, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcb, 0xff, 0xff, 0x30,
    0x66, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf0, 0xff, 0xff, 0x15, 0x22, 0xfd,
    0xff, 0xfa, 0x32, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xff, 0xff, 0xcb, 0x0, 0x0, 0x9c, 0xff, 0xff,
    0xf9, 0x9b, 0x6d, 0x78, 0xb8, 0xff, 0xff, 0xfd,
    0x45, 0x0, 0x0, 0x7, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xae, 0xe4, 0xf9, 0xf5,
    0xd5, 0x91, 0x24, 0x0, 0x0, 0x0,

    /* U+0056 "V" */
    0x9b, 0xff, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0xfd, 0xff, 0xff, 0x29,
    0x3f, 0xff, 0xff, 0xeb, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x68, 0xff, 0xff, 0xcd, 0x0,
    0x1, 0xe1, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xba, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x87, 0xff, 0xff, 0x92, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0xfb, 0xff, 0xfc, 0x17, 0x0,
    0x0, 0x2a, 0xff, 0xff, 0xe3, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xb7, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0x36, 0x0, 0x0,
    0x0, 0x0, 0xb2, 0xff, 0xff, 0x5b, 0x0, 0x0,
    0x0, 0x0, 0x73, 0xff, 0xff, 0x88, 0x0, 0x0,
    0x0, 0xc, 0xf7, 0xff, 0xf3, 0xb, 0x0, 0x0,
    0x0, 0x0, 0x19, 0xfd, 0xff, 0xd9, 0x0, 0x0,
    0x0, 0x57, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbb, 0xff, 0xff, 0x2b, 0x0,
    0x0, 0xa9, 0xff, 0xff, 0x45, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x7d, 0x0,
    0x8, 0xf3, 0xff, 0xe6, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf5, 0xff, 0xcf, 0x0,
    0x4e, 0xff, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa7, 0xff, 0xff, 0x21,
    0xa1, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0x77,
    0xee, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xea, 0xff, 0xf0,
    0xff, 0xff, 0x77, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x93, 0xff, 0xff,
    0xff, 0xfd, 0x1d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0xff, 0xff,
    0xff, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd9, 0xff,
    0xff, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x53, 0xff, 0xff, 0x9a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb9, 0xff, 0xf9, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x91, 0x17, 0xff, 0xff,
    0xcf, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf4, 0xff,
    0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0x56, 0x0, 0xdc, 0xff, 0xfb, 0x9, 0x0,
    0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0x84, 0x0,
    0x0, 0x0, 0x0, 0xc3, 0xff, 0xff, 0x1b, 0x0,
    0xa1, 0xff, 0xff, 0x3a, 0x0, 0x0, 0x0, 0x7c,
    0xff, 0xff, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x2,
    0xf3, 0xff, 0xe0, 0x0, 0x0, 0x65, 0xff, 0xff,
    0x6f, 0x0, 0x0, 0x0, 0xbd, 0xff, 0xf4, 0xff,
    0xf9, 0xa, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xa4,
    0x0, 0x0, 0x2a, 0xff, 0xff, 0xa4, 0x0, 0x0,
    0x7, 0xf7, 0xff, 0x85, 0xff, 0xff, 0x44, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x69, 0x0, 0x0, 0x1,
    0xed, 0xff, 0xd9, 0x0, 0x0, 0x40, 0xff, 0xff,
    0x28, 0xd5, 0xff, 0x84, 0x0, 0x0, 0x93, 0xff,
    0xff, 0x2e, 0x0, 0x0, 0x0, 0xb3, 0xff, 0xfe,
    0x10, 0x0, 0x81, 0xff, 0xe4, 0x0, 0x92, 0xff,
    0xc4, 0x0, 0x0, 0xc7, 0xff, 0xf0, 0x2, 0x0,
    0x0, 0x0, 0x78, 0xff, 0xff, 0x44, 0x0, 0xc2,
    0xff, 0x9f, 0x0, 0x4f, 0xff, 0xf9, 0xa, 0x4,
    0xf6, 0xff, 0xb7, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0xff, 0xff, 0x79, 0x9, 0xf9, 0xff, 0x5a, 0x0,
    0xf, 0xfc, 0xff, 0x44, 0x2f, 0xff, 0xff, 0x7c,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf9, 0xff, 0xae,
    0x44, 0xff, 0xfe, 0x17, 0x0, 0x0, 0xca, 0xff,
    0x84, 0x63, 0xff, 0xff, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc6, 0xff, 0xe4, 0x86, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x87, 0xff, 0xc4, 0x97, 0xff,
    0xfb, 0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8b,
    0xff, 0xff, 0xdd, 0xff, 0x8c, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xfa, 0xd4, 0xff, 0xca, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x50, 0xff, 0xff, 0xff,
    0xff, 0x48, 0x0, 0x0, 0x0, 0x9, 0xf8, 0xff,
    0xff, 0xff, 0x8f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xff, 0xff, 0xff, 0xf9, 0xa, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x53,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd9,
    0xff, 0xff, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7c, 0xff, 0xff, 0xff, 0x18, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9e, 0xff, 0xff, 0x7a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xff, 0xff,
    0xdd, 0x0, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0x22, 0xf7, 0xff, 0xff, 0x4d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x21, 0xf8, 0xff, 0xff, 0x4d, 0x0,
    0x7d, 0xff, 0xff, 0xdb, 0x6, 0x0, 0x0, 0x0,
    0x0, 0xae, 0xff, 0xff, 0xb1, 0x0, 0x0, 0x6,
    0xd9, 0xff, 0xff, 0x76, 0x0, 0x0, 0x0, 0x41,
    0xff, 0xff, 0xf5, 0x1f, 0x0, 0x0, 0x0, 0x43,
    0xff, 0xff, 0xf2, 0x18, 0x0, 0x3, 0xd2, 0xff,
    0xff, 0x78, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6,
    0xff, 0xff, 0x9f, 0x0, 0x69, 0xff, 0xff, 0xd6,
    0x5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xf0,
    0xff, 0xfe, 0x45, 0xeb, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcc, 0xff,
    0xff, 0xff, 0xef, 0x16, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x64, 0xff, 0xff,
    0xff, 0x9b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xdb, 0xff, 0xff, 0xff,
    0xf7, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x82, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xf8, 0xff, 0xfc, 0x3d, 0xe7, 0xff, 0xff,
    0x57, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbd,
    0xff, 0xff, 0x95, 0x0, 0x5f, 0xff, 0xff, 0xe6,
    0xe, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff,
    0xec, 0x11, 0x0, 0x1, 0xc6, 0xff, 0xff, 0x93,
    0x0, 0x0, 0x0, 0x10, 0xe8, 0xff, 0xff, 0x66,
    0x0, 0x0, 0x0, 0x33, 0xfd, 0xff, 0xfd, 0x33,
    0x0, 0x0, 0x98, 0xff, 0xff, 0xcc, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x9a, 0xff, 0xff, 0xcc, 0x2,
    0x37, 0xfd, 0xff, 0xfe, 0x39, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xee, 0xff, 0xff, 0x6d,

    /* U+0059 "Y" */
    0xa8, 0xff, 0xff, 0x9d, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0xf1, 0xff, 0xff, 0x3f, 0x24,
    0xfa, 0xff, 0xfa, 0x1e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7e, 0xff, 0xff, 0xb7, 0x0, 0x0, 0x96,
    0xff, 0xff, 0x93, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xed, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x18, 0xf4,
    0xff, 0xf7, 0x18, 0x0, 0x0, 0x0, 0x76, 0xff,
    0xff, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x85, 0xff,
    0xff, 0x8a, 0x0, 0x0, 0x9, 0xe8, 0xff, 0xfa,
    0x23, 0x0, 0x0, 0x0, 0x0, 0xf, 0xed, 0xff,
    0xf3, 0x12, 0x0, 0x6d, 0xff, 0xff, 0x95, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x73, 0xff, 0xff,
    0x81, 0x6, 0xe2, 0xff, 0xf5, 0x18, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xe2, 0xff, 0xee,
    0x72, 0xff, 0xff, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x62, 0xff, 0xff, 0xfd,
    0xff, 0xed, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd5, 0xff, 0xff, 0xff,
    0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x55, 0xff, 0xff, 0xe8, 0x8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x30, 0xff, 0xff, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x30, 0xff, 0xff, 0xcc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x30, 0xff, 0xff, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0xff, 0xff, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff,
    0xff, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xff,
    0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x5a, 0x0, 0x68, 0x68, 0x68,
    0x68, 0x68, 0x68, 0x68, 0x68, 0xab, 0xff, 0xff,
    0xe1, 0xe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x15, 0xe9, 0xff, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb1, 0xff, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x64, 0xff, 0xff,
    0xd3, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x22, 0xf3, 0xff, 0xf9, 0x2f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xc5, 0xff, 0xff, 0x78, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7b, 0xff, 0xff,
    0xc4, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x32, 0xfa, 0xff, 0xf3, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xd6, 0xff, 0xff, 0x63, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x92, 0xff, 0xff,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x45, 0xfe, 0xff, 0xea, 0x16, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0xe4, 0xff, 0xff, 0x4f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0xff, 0xff,
    0xd7, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60,
    0x60, 0x3d, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa4,
    0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa4,

    /* U+005B "[" */
    0x1b, 0x3c, 0x3c, 0x3c, 0x3c, 0x13, 0x74, 0xff,
    0xff, 0xff, 0xff, 0x54, 0x74, 0xff, 0xff, 0xff,
    0xff, 0x54, 0x74, 0xff, 0xff, 0x64, 0x0, 0x0,
    0x74, 0xff, 0xff, 0x64, 0x0, 0x0, 0x74, 0xff,
    0xff, 0x64, 0x0, 0x0, 0x74, 0xff, 0xff, 0x64,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x64, 0x0, 0x0,
    0x74, 0xff, 0xff, 0x64, 0x0, 0x0, 0x74, 0xff,
    0xff, 0x64, 0x0, 0x0, 0x74, 0xff, 0xff, 0x64,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x64, 0x0, 0x0,
    0x74, 0xff, 0xff, 0x64, 0x0, 0x0, 0x74, 0xff,
    0xff, 0x64, 0x0, 0x0, 0x74, 0xff, 0xff, 0x64,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x64, 0x0, 0x0,
    0x74, 0xff, 0xff, 0x64, 0x0, 0x0, 0x74, 0xff,
    0xff, 0x64, 0x0, 0x0, 0x74, 0xff, 0xff, 0x64,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x64, 0x0, 0x0,
    0x74, 0xff, 0xff, 0x64, 0x0, 0x0, 0x74, 0xff,
    0xff, 0x86, 0x38, 0x12, 0x74, 0xff, 0xff, 0xff,
    0xff, 0x54, 0x74, 0xff, 0xff, 0xff, 0xff, 0x54,

    /* U+005C "\\" */
    0x93, 0xff, 0xff, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0xff, 0xff, 0x9f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0xff, 0xf4, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6b, 0xff, 0xff, 0x64, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0xf7, 0xff,
    0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa6, 0xff, 0xff, 0x2a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x43, 0xff, 0xff, 0x8c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0xe9, 0x5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7e, 0xff, 0xff, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfd,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb9, 0xff, 0xfc, 0x1a, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x56, 0xff, 0xff,
    0x79, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xec, 0xff, 0xdb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x91, 0xff, 0xff, 0x3e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcc, 0xff, 0xf5, 0xe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x69, 0xff,
    0xff, 0x66, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf6, 0xff, 0xc9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0x80, 0x7f,
    0x9,

    /* U+005D "]" */
    0x33, 0x3c, 0x3c, 0x3c, 0x39, 0xdc, 0xff, 0xff,
    0xff, 0xf4, 0xdc, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xe8, 0xff, 0xf4, 0x0, 0x0, 0xe8, 0xff,
    0xf4, 0x0, 0x0, 0xe8, 0xff, 0xf4, 0x0, 0x0,
    0xe8, 0xff, 0xf4, 0x0, 0x0, 0xe8, 0xff, 0xf4,
    0x0, 0x0, 0xe8, 0xff, 0xf4, 0x0, 0x0, 0xe8,
    0xff, 0xf4, 0x0, 0x0, 0xe8, 0xff, 0xf4, 0x0,
    0x0, 0xe8, 0xff, 0xf4, 0x0, 0x0, 0xe8, 0xff,
    0xf4, 0x0, 0x0, 0xe8, 0xff, 0xf4, 0x0, 0x0,
    0xe8, 0xff, 0xf4, 0x0, 0x0, 0xe8, 0xff, 0xf4,
    0x0, 0x0, 0xe8, 0xff, 0xf4, 0x0, 0x0, 0xe8,
    0xff, 0xf4, 0x0, 0x0, 0xe8, 0xff, 0xf4, 0x0,
    0x0, 0xe8, 0xff, 0xf4, 0x0, 0x0, 0xe8, 0xff,
    0xf4, 0x30, 0x38, 0xed, 0xff, 0xf4, 0xdc, 0xff,
    0xff, 0xff, 0xf4, 0xdc, 0xff, 0xff, 0xff, 0xf4,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0x2, 0x7b, 0x80, 0x1d, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xff,
    0x89, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb3,
    0xff, 0xff, 0xeb, 0x7, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfd, 0xff, 0xed, 0xff, 0x5b, 0x0, 0x0,
    0x0, 0x0, 0x86, 0xff, 0xc3, 0x84, 0xff, 0xc5,
    0x0, 0x0, 0x0, 0x6, 0xe9, 0xff, 0x60, 0x22,
    0xfe, 0xff, 0x2e, 0x0, 0x0, 0x59, 0xff, 0xf2,
    0xb, 0x0, 0xbf, 0xff, 0x98, 0x0, 0x0, 0xc2,
    0xff, 0x9b, 0x0, 0x0, 0x5c, 0xff, 0xf3, 0xd,
    0x2b, 0xff, 0xff, 0x39, 0x0, 0x0, 0x9, 0xf0,
    0xff, 0x6b,

    /* U+005F "_" */
    0x3e, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40,
    0x40, 0x40, 0x32, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc8, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc8,

    /* U+0060 "`" */
    0x19, 0x7f, 0x80, 0x7a, 0x4, 0x0, 0x0, 0x0,
    0x85, 0xff, 0xff, 0x7a, 0x0, 0x0, 0x0, 0x0,
    0x9d, 0xff, 0xf8, 0x28, 0x0, 0x0, 0x0, 0x4,
    0xb3, 0xff, 0xc5, 0x2,

    /* U+0061 "a" */
    0x0, 0x0, 0x47, 0xae, 0xe7, 0xfa, 0xeb, 0xb4,
    0x41, 0x0, 0x0, 0x0, 0x93, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x6a, 0x0, 0x51, 0xff,
    0xff, 0xdb, 0x56, 0x35, 0x6c, 0xf5, 0xff, 0xf7,
    0x17, 0x7b, 0xcc, 0xcc, 0x38, 0x0, 0x0, 0x0,
    0x8a, 0xff, 0xff, 0x5c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x65, 0xff, 0xff, 0x76, 0x0,
    0x5, 0x66, 0xbb, 0xe8, 0xfa, 0xff, 0xff, 0xff,
    0xff, 0x78, 0x10, 0xcb, 0xff, 0xff, 0xfa, 0xdf,
    0xd0, 0xe2, 0xff, 0xff, 0x78, 0x91, 0xff, 0xff,
    0x9b, 0x11, 0x0, 0x0, 0x64, 0xff, 0xff, 0x78,
    0xdc, 0xff, 0xf9, 0x7, 0x0, 0x0, 0x0, 0x64,
    0xff, 0xff, 0x78, 0xe7, 0xff, 0xfb, 0x10, 0x0,
    0x0, 0x1, 0xa3, 0xff, 0xff, 0x78, 0xaf, 0xff,
    0xff, 0xc7, 0x5c, 0x61, 0xbf, 0xff, 0xff, 0xff,
    0x7d, 0x29, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xff, 0xff, 0x91, 0x0, 0x24, 0xa6, 0xea,
    0xfa, 0xdc, 0x85, 0x2b, 0xff, 0xff, 0xca,

    /* U+0062 "b" */
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0x52,
    0x7b, 0xd9, 0xf9, 0xe7, 0x9e, 0x1d, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe8, 0x24, 0x0, 0x8c, 0xff, 0xff, 0xff,
    0xb0, 0x64, 0x6d, 0xd4, 0xff, 0xff, 0xbe, 0x0,
    0x8c, 0xff, 0xff, 0x95, 0x0, 0x0, 0x0, 0xf,
    0xe2, 0xff, 0xff, 0x2a, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x80, 0xff, 0xff, 0x68,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x52, 0xff, 0xff, 0x88, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x45, 0xff, 0xff, 0x95,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xff, 0xff, 0x8a, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x83, 0xff, 0xff, 0x68,
    0x8c, 0xff, 0xff, 0x99, 0x0, 0x0, 0x0, 0x10,
    0xe7, 0xff, 0xff, 0x29, 0x8c, 0xff, 0xff, 0xff,
    0xb0, 0x61, 0x72, 0xd8, 0xff, 0xff, 0xbb, 0x0,
    0x8c, 0xff, 0xff, 0xce, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe8, 0x22, 0x0, 0x8c, 0xff, 0xff, 0x19,
    0x7b, 0xda, 0xf9, 0xe9, 0xa0, 0x1d, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x0, 0x26, 0x9d, 0xe0, 0xf9, 0xee,
    0xb9, 0x4a, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x84, 0x0,
    0x0, 0x21, 0xf3, 0xff, 0xfe, 0x97, 0x5a, 0x78,
    0xee, 0xff, 0xff, 0x46, 0x0, 0x95, 0xff, 0xff,
    0x76, 0x0, 0x0, 0x0, 0x38, 0xff, 0xff, 0xab,
    0x0, 0xe1, 0xff, 0xf8, 0xb, 0x0, 0x0, 0x0,
    0x0, 0xa7, 0xbc, 0x9b, 0x6, 0xff, 0xff, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0xff, 0xff, 0xc9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xde, 0xff, 0xf6, 0x7, 0x0, 0x0, 0x0,
    0x0, 0x56, 0x64, 0x53, 0x0, 0x93, 0xff, 0xff,
    0x6a, 0x0, 0x0, 0x0, 0x24, 0xfc, 0xff, 0xb2,
    0x0, 0x21, 0xf3, 0xff, 0xfd, 0x92, 0x55, 0x6f,
    0xe4, 0xff, 0xff, 0x45, 0x0, 0x0, 0x4d, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x79, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x9c, 0xe1, 0xf9, 0xe9,
    0xae, 0x3d, 0x0, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xff,
    0xff, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xff, 0xff,
    0x8, 0x0, 0x0, 0x0, 0x51, 0xc4, 0xf4, 0xf1,
    0xb7, 0x35, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x77, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xf0,
    0xff, 0xff, 0x8, 0x0, 0x31, 0xfc, 0xff, 0xff,
    0xa1, 0x5f, 0x7c, 0xe7, 0xff, 0xff, 0xff, 0x8,
    0x0, 0x9f, 0xff, 0xff, 0x8a, 0x0, 0x0, 0x0,
    0x22, 0xf3, 0xff, 0xff, 0x8, 0x0, 0xe3, 0xff,
    0xfd, 0x15, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xff,
    0xff, 0x8, 0x6, 0xff, 0xff, 0xde, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x10,
    0xff, 0xff, 0xcb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0xff, 0xff, 0x8, 0x3, 0xff, 0xff, 0xdb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xff, 0xff,
    0x8, 0x0, 0xdd, 0xff, 0xf7, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x98,
    0xff, 0xff, 0x64, 0x0, 0x0, 0x0, 0xd, 0xe9,
    0xff, 0xff, 0x8, 0x0, 0x2b, 0xfb, 0xff, 0xf0,
    0x5e, 0x1f, 0x3e, 0xc4, 0xff, 0xff, 0xff, 0x8,
    0x0, 0x0, 0x70, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0,
    0x4d, 0xc4, 0xf5, 0xf4, 0xc2, 0x4e, 0x9a, 0xff,
    0xff, 0x8,

    /* U+0065 "e" */
    0x0, 0x0, 0x0, 0x11, 0x7e, 0xd4, 0xf6, 0xef,
    0xbf, 0x5a, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0xde, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9b,
    0x0, 0x0, 0x0, 0x8, 0xd8, 0xff, 0xfd, 0x99,
    0x58, 0x6e, 0xdf, 0xff, 0xff, 0x56, 0x0, 0x0,
    0x6d, 0xff, 0xff, 0x7a, 0x0, 0x0, 0x0, 0x20,
    0xf7, 0xff, 0xc2, 0x0, 0x0, 0xc4, 0xff, 0xfa,
    0xe, 0x0, 0x0, 0x0, 0x0, 0xc2, 0xff, 0xf9,
    0x4, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x17, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xfa, 0xff, 0xf1, 0x18,
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x3,
    0x0, 0xd5, 0xff, 0xff, 0x2e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x86, 0xff,
    0xff, 0xbc, 0x5, 0x0, 0x0, 0x0, 0x23, 0xb1,
    0xf, 0x0, 0x0, 0x14, 0xe7, 0xff, 0xff, 0xcd,
    0x69, 0x51, 0x86, 0xf0, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x34, 0xe7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe6, 0x30, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x84, 0xd2, 0xf5, 0xf4, 0xd3, 0x82, 0x11, 0x0,
    0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x54, 0x47,
    0x17, 0x0, 0x0, 0x0, 0x2a, 0xd1, 0xff, 0xff,
    0xff, 0x7d, 0x0, 0x0, 0xa, 0xe2, 0xff, 0xff,
    0xff, 0xff, 0x74, 0x0, 0x0, 0x59, 0xff, 0xff,
    0xd1, 0x29, 0x7, 0x8, 0x0, 0x0, 0x81, 0xff,
    0xff, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x78, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x78,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0xd, 0x1c, 0x95, 0xff, 0xff, 0x63, 0x1c, 0x18,
    0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x0, 0x45, 0xbe, 0xf2, 0xf4, 0xc4,
    0x4c, 0x77, 0xff, 0xff, 0x24, 0x0, 0x0, 0x67,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0xff,
    0xff, 0x24, 0x0, 0x26, 0xf9, 0xff, 0xff, 0xaa,
    0x61, 0x73, 0xd9, 0xff, 0xff, 0xff, 0x24, 0x0,
    0x92, 0xff, 0xff, 0x92, 0x0, 0x0, 0x0, 0xd,
    0xde, 0xff, 0xff, 0x24, 0x0, 0xd6, 0xff, 0xfe,
    0x18, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xff,
    0x24, 0x0, 0xf9, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0xff, 0x24, 0x4, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb4,
    0xff, 0xff, 0x24, 0x0, 0xf7, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xff, 0x24,
    0x0, 0xd1, 0xff, 0xfd, 0x18, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0xff, 0x24, 0x0, 0x8c, 0xff,
    0xff, 0x8f, 0x0, 0x0, 0x0, 0xf, 0xe0, 0xff,
    0xff, 0x24, 0x0, 0x22, 0xf7, 0xff, 0xff, 0xa2,
    0x5c, 0x70, 0xda, 0xff, 0xff, 0xff, 0x24, 0x0,
    0x0, 0x60, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xed, 0xff, 0xff, 0x24, 0x0, 0x0, 0x0, 0x41,
    0xbd, 0xf3, 0xf5, 0xc2, 0x48, 0xb8, 0xff, 0xff,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd9, 0xff, 0xff, 0xe, 0x0, 0x0,
    0x54, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x1b, 0xee, 0xff, 0xb1,
    0x58, 0x4c, 0x82, 0xf4, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x8, 0xb4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x53, 0xb3, 0xe8, 0xfb, 0xe9, 0xb1, 0x48, 0x0,
    0x0, 0x0,

    /* U+0068 "h" */
    0x94, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x94, 0xff, 0xff, 0x44, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0xff,
    0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x94, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x94, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94,
    0xff, 0xff, 0x44, 0x61, 0xcd, 0xf7, 0xef, 0xb3,
    0x2d, 0x0, 0x94, 0xff, 0xff, 0xcb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x1f, 0x94, 0xff, 0xff,
    0xff, 0xad, 0x63, 0x73, 0xdf, 0xff, 0xff, 0x8f,
    0x94, 0xff, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x30,
    0xff, 0xff, 0xcc, 0x94, 0xff, 0xff, 0x44, 0x0,
    0x0, 0x0, 0x1, 0xf8, 0xff, 0xe3, 0x94, 0xff,
    0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0xf0, 0xff,
    0xe8, 0x94, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0xff, 0xe8, 0x94, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8, 0x94,
    0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0xff, 0xe8, 0x94, 0xff, 0xff, 0x44, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0xff, 0xe8, 0x94, 0xff, 0xff,
    0x44, 0x0, 0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8,
    0x94, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xff, 0xe8, 0x94, 0xff, 0xff, 0x44, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8,

    /* U+0069 "i" */
    0x2b, 0xdf, 0xea, 0x45, 0x7d, 0xff, 0xff, 0xa4,
    0x33, 0xeb, 0xf4, 0x4f, 0x0, 0x1, 0x2, 0x0,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c,

    /* U+006A "j" */
    0x0, 0x0, 0x41, 0xe9, 0xe0, 0x2f, 0x0, 0x0,
    0x9d, 0xff, 0xff, 0x84, 0x0, 0x0, 0x4b, 0xf4,
    0xec, 0x37, 0x0, 0x0, 0x0, 0x2, 0x1, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0x70, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x6c, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0x70, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x6c, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0x70, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x6c, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0x6f, 0x0, 0x0, 0x80, 0xff,
    0xff, 0x5c, 0x5e, 0x6f, 0xeb, 0xff, 0xff, 0x33,
    0xe0, 0xff, 0xff, 0xff, 0xbc, 0x0, 0xc0, 0xf8,
    0xea, 0x9f, 0x13, 0x0,

    /* U+006B "k" */
    0x88, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x3, 0xb6, 0xff, 0xff, 0xae, 0x2,
    0x88, 0xff, 0xff, 0x50, 0x0, 0x0, 0x97, 0xff,
    0xff, 0xc9, 0x9, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x76, 0xff, 0xff, 0xde, 0x16, 0x0, 0x0,
    0x88, 0xff, 0xff, 0x50, 0x54, 0xfe, 0xff, 0xee,
    0x28, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x80,
    0xf6, 0xff, 0xf9, 0x3f, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xfc, 0xff, 0xff, 0x9c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x2f, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xff, 0xc0, 0xff, 0xff, 0xd2,
    0x7, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x98,
    0x0, 0xb6, 0xff, 0xff, 0x8a, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0x50, 0x0, 0x1a, 0xee, 0xff,
    0xfd, 0x3d, 0x0, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x5b, 0xff, 0xff, 0xde, 0xc, 0x0,
    0x88, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0xac,
    0xff, 0xff, 0x9b, 0x0, 0x88, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x15, 0xe9, 0xff, 0xff, 0x4c,

    /* U+006C "l" */
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,
    0x5c, 0xff, 0xff, 0x7c, 0x5c, 0xff, 0xff, 0x7c,

    /* U+006D "m" */
    0x8c, 0xff, 0xff, 0x2d, 0x6d, 0xd0, 0xf7, 0xee,
    0xb4, 0x2c, 0x0, 0x2e, 0xad, 0xeb, 0xf9, 0xd9,
    0x79, 0x3, 0x0, 0x8c, 0xff, 0xff, 0xd2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x5f, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x93, 0x0, 0x8c, 0xff,
    0xff, 0xfe, 0x9e, 0x61, 0x79, 0xe8, 0xff, 0xff,
    0xff, 0xee, 0x7f, 0x62, 0x9b, 0xfe, 0xff, 0xfc,
    0x18, 0x8c, 0xff, 0xff, 0x7e, 0x0, 0x0, 0x0,
    0x41, 0xff, 0xff, 0xff, 0x39, 0x0, 0x0, 0x0,
    0xa8, 0xff, 0xff, 0x51, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x5, 0xfe, 0xff, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0x73, 0xff, 0xff, 0x68, 0x8c,
    0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xfc,
    0xff, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0x6c, 0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0,
    0x0, 0x0, 0xfc, 0xff, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x6c, 0xff, 0xff, 0x6c, 0x8c, 0xff, 0xff,
    0x4c, 0x0, 0x0, 0x0, 0x0, 0xfc, 0xff, 0xd9,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0x6c,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0xfc, 0xff, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0xff, 0xff, 0x6c, 0x8c, 0xff, 0xff, 0x4c, 0x0,
    0x0, 0x0, 0x0, 0xfc, 0xff, 0xda, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0x6c, 0x8c, 0xff,
    0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xfc, 0xff,
    0xda, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff,
    0x6c, 0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0xff, 0xdb, 0x0, 0x0, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0x6c, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0xfc, 0xff, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0x6c,

    /* U+006E "n" */
    0x94, 0xff, 0xff, 0x21, 0x60, 0xca, 0xf5, 0xf2,
    0xb9, 0x33, 0x0, 0x94, 0xff, 0xff, 0xbc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x25, 0x94, 0xff,
    0xff, 0xff, 0xaf, 0x63, 0x72, 0xde, 0xff, 0xff,
    0x94, 0x94, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xcf, 0x94, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0xf7, 0xff, 0xe4, 0x94,
    0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0xff, 0xe8, 0x94, 0xff, 0xff, 0x44, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0xff, 0xe8, 0x94, 0xff, 0xff,
    0x44, 0x0, 0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8,
    0x94, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xff, 0xe8, 0x94, 0xff, 0xff, 0x44, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8, 0x94, 0xff,
    0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0xf0, 0xff,
    0xe8, 0x94, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0xff, 0xe8, 0x94, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0xff, 0xe8,

    /* U+006F "o" */
    0x0, 0x0, 0x0, 0x18, 0x85, 0xd5, 0xf5, 0xf0,
    0xc4, 0x66, 0x4, 0x0, 0x0, 0x0, 0x0, 0x36,
    0xe7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe,
    0xb, 0x0, 0x0, 0x11, 0xe4, 0xff, 0xff, 0xa3,
    0x56, 0x66, 0xd4, 0xff, 0xff, 0xa3, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x8b, 0x0, 0x0, 0x0, 0xe,
    0xe2, 0xff, 0xfe, 0x2c, 0x0, 0xd5, 0xff, 0xfd,
    0x14, 0x0, 0x0, 0x0, 0x0, 0x71, 0xff, 0xff,
    0x7b, 0x1, 0xfa, 0xff, 0xdb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xff, 0xff, 0xa4, 0x10, 0xff,
    0xff, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25,
    0xff, 0xff, 0xb4, 0x3, 0xfe, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xa1,
    0x0, 0xd7, 0xff, 0xfc, 0x16, 0x0, 0x0, 0x0,
    0x0, 0x69, 0xff, 0xff, 0x7b, 0x0, 0x86, 0xff,
    0xff, 0x8b, 0x0, 0x0, 0x0, 0x9, 0xd6, 0xff,
    0xff, 0x27, 0x0, 0x16, 0xe9, 0xff, 0xff, 0x9c,
    0x51, 0x5f, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x3a, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xbb, 0xe, 0x0, 0x0, 0x0, 0x0, 0x19,
    0x8f, 0xda, 0xf8, 0xec, 0xc3, 0x62, 0x3, 0x0,
    0x0,

    /* U+0070 "p" */
    0x8c, 0xff, 0xff, 0x33, 0x92, 0xe0, 0xf9, 0xe6,
    0x9c, 0x1a, 0x0, 0x0, 0x8c, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x1f, 0x0,
    0x8c, 0xff, 0xff, 0xf3, 0x6a, 0x26, 0x38, 0xb9,
    0xff, 0xff, 0xb5, 0x0, 0x8c, 0xff, 0xff, 0x68,
    0x0, 0x0, 0x0, 0x8, 0xe5, 0xff, 0xff, 0x22,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x82, 0xff, 0xff, 0x60, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xff, 0x81,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0xff, 0x8d, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xff, 0x82,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8d, 0xff, 0xff, 0x60, 0x8c, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x14, 0xec, 0xff, 0xfe, 0x20,
    0x8c, 0xff, 0xff, 0xfe, 0x99, 0x52, 0x62, 0xd7,
    0xff, 0xff, 0xae, 0x0, 0x8c, 0xff, 0xff, 0xeb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x1b, 0x0,
    0x8c, 0xff, 0xff, 0x54, 0x81, 0xdb, 0xf9, 0xe7,
    0x9a, 0x17, 0x0, 0x0, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x0, 0x54, 0xc4, 0xf3, 0xf3, 0xbc,
    0x3d, 0x8f, 0xff, 0xff, 0x8, 0x0, 0x0, 0x7d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xdf, 0xff,
    0xff, 0x8, 0x0, 0x35, 0xfd, 0xff, 0xfd, 0x96,
    0x53, 0x6c, 0xdb, 0xff, 0xff, 0xff, 0x8, 0x0,
    0xa3, 0xff, 0xff, 0x7d, 0x0, 0x0, 0x0, 0x13,
    0xec, 0xff, 0xff, 0x8, 0x0, 0xe5, 0xff, 0xfb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xd4, 0xff, 0xff,
    0x8, 0x7, 0xff, 0xff, 0xda, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd4, 0xff, 0xff, 0x8, 0x10, 0xff,
    0xff, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd4,
    0xff, 0xff, 0x8, 0x3, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd4, 0xff, 0xff, 0x8,
    0x0, 0xdd, 0xff, 0xfb, 0x13, 0x0, 0x0, 0x0,
    0x0, 0xd4, 0xff, 0xff, 0x8, 0x0, 0x99, 0xff,
    0xff, 0x83, 0x0, 0x0, 0x0, 0x14, 0xed, 0xff,
    0xff, 0x8, 0x0, 0x2d, 0xfb, 0xff, 0xfd, 0x91,
    0x4f, 0x69, 0xdb, 0xff, 0xff, 0xff, 0x8, 0x0,
    0x0, 0x73, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xf5, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0, 0x4f,
    0xc4, 0xf4, 0xf3, 0xbc, 0x3d, 0xd4, 0xff, 0xff,
    0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd4, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd4,
    0xff, 0xff, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd4, 0xff, 0xff, 0x8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd4, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd4, 0xff,
    0xff, 0x8,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x1,
    0x8c, 0xff, 0xff, 0x43, 0x9d, 0xed, 0xff, 0x1a,
    0x8c, 0xff, 0xff, 0xea, 0xff, 0xff, 0xff, 0x19,
    0x8c, 0xff, 0xff, 0xff, 0xd4, 0x90, 0x90, 0xe,
    0x8c, 0xff, 0xff, 0xad, 0x3, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x0, 0x3, 0x67, 0xc7, 0xf2, 0xf9, 0xe0,
    0x9d, 0x28, 0x0, 0x0, 0x0, 0x5, 0xb9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x49, 0x0,
    0x0, 0x6c, 0xff, 0xff, 0xc1, 0x40, 0x31, 0x83,
    0xff, 0xff, 0xea, 0x7, 0x0, 0xaa, 0xff, 0xff,
    0x25, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xff, 0x39,
    0x0, 0x94, 0xff, 0xff, 0x7a, 0x3, 0x0, 0x0,
    0x2, 0x4, 0x4, 0x1, 0x0, 0x27, 0xf1, 0xff,
    0xff, 0xe9, 0xa5, 0x64, 0x1b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0xb9, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xa5, 0x15, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x68, 0xab, 0xf1, 0xff, 0xff, 0xd2, 0x4,
    0x5, 0x34, 0x34, 0x22, 0x0, 0x0, 0x0, 0xd,
    0xc4, 0xff, 0xff, 0x44, 0x7, 0xfc, 0xff, 0xd6,
    0x2, 0x0, 0x0, 0x0, 0x81, 0xff, 0xff, 0x56,
    0x0, 0xa7, 0xff, 0xff, 0xac, 0x3e, 0x2c, 0x5d,
    0xec, 0xff, 0xfb, 0x1e, 0x0, 0x12, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x6, 0x70, 0xc6, 0xef, 0xfb, 0xe5,
    0xaa, 0x3c, 0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0xe8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x44,
    0xe8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x44,
    0x19, 0x1c, 0xd5, 0xff, 0xff, 0x23, 0x1c, 0x7,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xc9, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xab, 0xff, 0xff, 0xa4, 0x68, 0x29,
    0x0, 0x0, 0x50, 0xff, 0xff, 0xff, 0xff, 0x58,
    0x0, 0x0, 0x0, 0x6b, 0xd9, 0xf9, 0xe6, 0x43,

    /* U+0075 "u" */
    0x9c, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xf4, 0xff, 0xe4, 0x9c, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xf4, 0xff, 0xe4, 0x9c, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf4, 0xff,
    0xe4, 0x9c, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xf4, 0xff, 0xe4, 0x9c, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xf4, 0xff, 0xe4, 0x9c,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf4,
    0xff, 0xe4, 0x9c, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0xff, 0xe4, 0x9c, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xf4, 0xff, 0xe4,
    0x98, 0xff, 0xff, 0x48, 0x0, 0x0, 0x0, 0x0,
    0xf4, 0xff, 0xe4, 0x81, 0xff, 0xff, 0x7d, 0x0,
    0x0, 0x0, 0x34, 0xfd, 0xff, 0xe4, 0x41, 0xff,
    0xff, 0xf5, 0x7f, 0x5c, 0x88, 0xf2, 0xff, 0xff,
    0xe4, 0x1, 0xbc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xef, 0xff, 0xe4, 0x0, 0xb, 0x8d, 0xe2,
    0xfb, 0xea, 0xa6, 0x25, 0xcf, 0xff, 0xe4,

    /* U+0076 "v" */
    0x91, 0xff, 0xff, 0x5d, 0x0, 0x0, 0x0, 0x0,
    0x8a, 0xff, 0xff, 0x64, 0x39, 0xff, 0xff, 0xaa,
    0x0, 0x0, 0x0, 0x0, 0xd4, 0xff, 0xfa, 0x12,
    0x1, 0xe1, 0xff, 0xf1, 0x5, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xb6, 0x0, 0x0, 0x8b, 0xff, 0xff,
    0x42, 0x0, 0x0, 0x6a, 0xff, 0xff, 0x5f, 0x0,
    0x0, 0x34, 0xff, 0xff, 0x8f, 0x0, 0x0, 0xb5,
    0xff, 0xf9, 0xf, 0x0, 0x0, 0x0, 0xdc, 0xff,
    0xdb, 0x0, 0x9, 0xf6, 0xff, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x86, 0xff, 0xff, 0x27, 0x4b, 0xff,
    0xff, 0x5b, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x73, 0x96, 0xff, 0xf7, 0xd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd8, 0xff, 0xc0, 0xe0, 0xff,
    0xae, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x81,
    0xff, 0xfe, 0xff, 0xff, 0x57, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff, 0xf5,
    0xb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd3, 0xff, 0xff, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0xff, 0x53,
    0x0, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x7a, 0xff, 0xff, 0x49, 0x0, 0x0, 0x0, 0x56,
    0xff, 0xff, 0x1e, 0x0, 0x0, 0x0, 0x82, 0xff,
    0xff, 0x41, 0x36, 0xff, 0xff, 0x84, 0x0, 0x0,
    0x0, 0xa0, 0xff, 0xff, 0x6a, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xf6, 0x7, 0x2, 0xee, 0xff, 0xbf,
    0x0, 0x0, 0x1, 0xe9, 0xff, 0xff, 0xb6, 0x0,
    0x0, 0x3, 0xf3, 0xff, 0xb9, 0x0, 0x0, 0xad,
    0xff, 0xf5, 0x4, 0x0, 0x36, 0xff, 0xff, 0xff,
    0xf8, 0xb, 0x0, 0x31, 0xff, 0xff, 0x75, 0x0,
    0x0, 0x69, 0xff, 0xff, 0x35, 0x0, 0x81, 0xff,
    0xc8, 0xf6, 0xff, 0x4f, 0x0, 0x6c, 0xff, 0xff,
    0x31, 0x0, 0x0, 0x25, 0xff, 0xff, 0x70, 0x0,
    0xcc, 0xff, 0x72, 0xb2, 0xff, 0x9b, 0x0, 0xa7,
    0xff, 0xeb, 0x1, 0x0, 0x0, 0x0, 0xe0, 0xff,
    0xab, 0x17, 0xfe, 0xff, 0x26, 0x64, 0xff, 0xe6,
    0x1, 0xe1, 0xff, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0xe6, 0x61, 0xff, 0xd9, 0x0, 0x17,
    0xfe, 0xff, 0x50, 0xff, 0xff, 0x65, 0x0, 0x0,
    0x0, 0x0, 0x58, 0xff, 0xff, 0xcc, 0xff, 0x8c,
    0x0, 0x0, 0xc8, 0xff, 0xd5, 0xff, 0xff, 0x21,
    0x0, 0x0, 0x0, 0x0, 0x15, 0xfe, 0xff, 0xff,
    0xff, 0x3f, 0x0, 0x0, 0x7a, 0xff, 0xff, 0xff,
    0xdd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xef, 0x4, 0x0, 0x0, 0x2c, 0xff,
    0xff, 0xff, 0x99, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8b, 0xff, 0xff, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0xdd, 0xff, 0xff, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x46, 0xff, 0xff, 0x5a, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xfe, 0x13, 0x0,
    0x0, 0x0,

    /* U+0078 "x" */
    0x35, 0xfd, 0xff, 0xe2, 0x9, 0x0, 0x0, 0x5,
    0xd8, 0xff, 0xff, 0x4a, 0x0, 0x98, 0xff, 0xff,
    0x7d, 0x0, 0x0, 0x71, 0xff, 0xff, 0xaf, 0x0,
    0x0, 0x12, 0xeb, 0xff, 0xf4, 0x1a, 0x14, 0xef,
    0xff, 0xf5, 0x1f, 0x0, 0x0, 0x0, 0x62, 0xff,
    0xff, 0x9f, 0x97, 0xff, 0xff, 0x79, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xc6, 0xff, 0xfe, 0xfc, 0xff,
    0xd8, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0xfc, 0xff, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc8, 0xff, 0xff, 0xde,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xdd, 0xff, 0xf6, 0xec, 0xff,
    0xea, 0x11, 0x0, 0x0, 0x0, 0x0, 0x83, 0xff,
    0xff, 0x81, 0x6a, 0xff, 0xff, 0x98, 0x0, 0x0,
    0x0, 0x25, 0xf8, 0xff, 0xe6, 0xb, 0x3, 0xd4,
    0xff, 0xfd, 0x35, 0x0, 0x0, 0xba, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x46, 0xff, 0xff, 0xcc, 0x2,
    0x56, 0xff, 0xff, 0xce, 0x2, 0x0, 0x0, 0x0,
    0xb5, 0xff, 0xff, 0x6a,

    /* U+0079 "y" */
    0xb0, 0xff, 0xff, 0x5f, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0x57, 0x56, 0xff, 0xff, 0xae,
    0x0, 0x0, 0x0, 0x7, 0xf3, 0xff, 0xf5, 0xb,
    0x9, 0xf3, 0xff, 0xf4, 0x8, 0x0, 0x0, 0x47,
    0xff, 0xff, 0xa8, 0x0, 0x0, 0xa4, 0xff, 0xff,
    0x4c, 0x0, 0x0, 0x94, 0xff, 0xff, 0x51, 0x0,
    0x0, 0x4a, 0xff, 0xff, 0x9b, 0x0, 0x0, 0xdf,
    0xff, 0xf1, 0x8, 0x0, 0x0, 0x4, 0xec, 0xff,
    0xe8, 0x2, 0x2c, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x98, 0xff, 0xff, 0x3a, 0x79, 0xff,
    0xff, 0x4a, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0x89, 0xc5, 0xff, 0xed, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xe3, 0xff, 0xe2, 0xfd, 0xff,
    0x9b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x32, 0xff, 0xff, 0xff, 0xe9,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd9, 0xff, 0xff, 0x95, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x85, 0xff, 0xff, 0x3d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xae, 0xff, 0xe4, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0xfb, 0xff, 0x8d, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0x81, 0xe4,
    0xff, 0xfb, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd8, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb7, 0xf6, 0xd8,
    0x6b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x0, 0xec, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xec, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x46, 0x4c, 0x4c, 0x4c, 0x4c, 0x4e, 0xe5,
    0xff, 0xff, 0x7c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7c, 0xff, 0xff, 0xbf, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xfb, 0xff,
    0xec, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0xe0, 0xff, 0xff, 0x4e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaa, 0xff, 0xff, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x65,
    0xff, 0xff, 0xd0, 0x7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x29, 0xf5, 0xff, 0xf5, 0x28, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xd1, 0xff,
    0xff, 0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x94, 0xff, 0xff, 0xd4, 0x48, 0x48, 0x48,
    0x48, 0x48, 0x48, 0x12, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x51, 0x23,
    0x0, 0x0, 0x0, 0x0, 0x2a, 0xdb, 0xff, 0x82,
    0x0, 0x0, 0x0, 0x25, 0xf0, 0xff, 0xcf, 0x2c,
    0x0, 0x0, 0x0, 0xa7, 0xff, 0xfc, 0x1e, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0x93, 0x0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xff, 0x6e, 0x0, 0x0,
    0xa, 0x44, 0xdb, 0xff, 0xf0, 0x18, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xe9, 0x40, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xf5, 0x57, 0x0, 0x0, 0x0,
    0x2, 0x2d, 0xcf, 0xff, 0xf8, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x45, 0xff, 0xff, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x1a, 0xff, 0xff, 0x95, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xa5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe9, 0xff, 0xc9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x98, 0xff, 0xfe, 0x2d, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xe7, 0xff, 0xdf, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x26, 0xca, 0xff, 0x78,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x42, 0x1a,

    /* U+007C "|" */
    0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff,
    0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff,
    0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff,
    0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff,
    0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff, 0xf4, 0xff,
    0x57, 0x5c,

    /* U+007D "}" */
    0x21, 0x5f, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xe3, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xcd, 0xff, 0xf4, 0x28, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xfa, 0xff, 0xaa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xf1, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xff, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x94, 0xff, 0xff, 0x18, 0x0, 0x0,
    0x0, 0x0, 0x94, 0xff, 0xff, 0x18, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x65, 0xff, 0xff, 0x4f, 0x0, 0x0,
    0x0, 0x0, 0x12, 0xeb, 0xff, 0xdc, 0x46, 0xb,
    0x0, 0x0, 0x0, 0x37, 0xdf, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x48, 0xeb, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x19, 0xf3, 0xff, 0xd1, 0x2f, 0x3,
    0x0, 0x0, 0x6d, 0xff, 0xff, 0x48, 0x0, 0x0,
    0x0, 0x0, 0x8d, 0xff, 0xff, 0x1e, 0x0, 0x0,
    0x0, 0x0, 0x94, 0xff, 0xff, 0x18, 0x0, 0x0,
    0x0, 0x0, 0x94, 0xff, 0xff, 0x17, 0x0, 0x0,
    0x0, 0x0, 0x9e, 0xff, 0xff, 0xf, 0x0, 0x0,
    0x0, 0x0, 0xc3, 0xff, 0xec, 0x0, 0x0, 0x0,
    0x0, 0x29, 0xfd, 0xff, 0x9d, 0x0, 0x0, 0x0,
    0x3f, 0xde, 0xff, 0xea, 0x1d, 0x0, 0x0, 0x0,
    0x75, 0xff, 0xcc, 0x29, 0x0, 0x0, 0x0, 0x0,
    0x18, 0x43, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x9, 0x8f, 0xe8, 0xf7, 0xc7, 0x4d, 0x0,
    0x0, 0x0, 0x0, 0x70, 0xa8, 0x5c, 0x0, 0xb2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8d, 0x2, 0x0,
    0x5, 0xe0, 0xff, 0x75, 0x40, 0xff, 0xff, 0xac,
    0x71, 0xcb, 0xff, 0xff, 0xc4, 0x73, 0xba, 0xff,
    0xff, 0x2c, 0x86, 0xff, 0xd8, 0x1, 0x0, 0x5,
    0x98, 0xff, 0xff, 0xff, 0xff, 0xff, 0x98, 0x0,
    0x55, 0x8c, 0x5e, 0x0, 0x0, 0x0, 0x0, 0x56,
    0xcf, 0xf9, 0xe3, 0x7e, 0x3, 0x0,

    /* U+00B0 "°" */
    0x0, 0x2d, 0xbf, 0xf2, 0xcd, 0x3f, 0x0, 0x17,
    0xeb, 0xf5, 0xb3, 0xeb, 0xf7, 0x29, 0x69, 0xff,
    0x4c, 0x0, 0x27, 0xff, 0x84, 0x6c, 0xff, 0x47,
    0x0, 0x21, 0xff, 0x88, 0x1d, 0xf0, 0xf3, 0xb2,
    0xe8, 0xfa, 0x30, 0x0, 0x36, 0xc4, 0xf4, 0xd1,
    0x49, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 96, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 103, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 68, .adv_w = 125, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 110, .adv_w = 234, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 348, .adv_w = 218, .box_w = 12, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 612, .adv_w = 282, .box_w = 16, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 884, .adv_w = 245, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1139, .adv_w = 65, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 11},
    {.bitmap_index = 1167, .adv_w = 134, .box_w = 7, .box_h = 26, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 1349, .adv_w = 135, .box_w = 7, .box_h = 26, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 1531, .adv_w = 170, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 1652, .adv_w = 214, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1821, .adv_w = 84, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1856, .adv_w = 126, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 1877, .adv_w = 107, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1893, .adv_w = 152, .box_w = 9, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2064, .adv_w = 218, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2268, .adv_w = 218, .box_w = 8, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2404, .adv_w = 218, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2625, .adv_w = 218, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2846, .adv_w = 218, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3084, .adv_w = 218, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3288, .adv_w = 218, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3492, .adv_w = 218, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3713, .adv_w = 218, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3917, .adv_w = 218, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4121, .adv_w = 102, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4173, .adv_w = 91, .box_w = 5, .box_h = 17, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4258, .adv_w = 195, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4390, .adv_w = 215, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 4478, .adv_w = 200, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 4610, .adv_w = 187, .box_w = 11, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4797, .adv_w = 344, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 5237, .adv_w = 256, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5509, .adv_w = 242, .box_w = 13, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5730, .adv_w = 251, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5968, .adv_w = 251, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6206, .adv_w = 217, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6410, .adv_w = 211, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6614, .adv_w = 261, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6852, .adv_w = 273, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7107, .adv_w = 108, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7175, .adv_w = 213, .box_w = 12, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7379, .adv_w = 242, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7634, .adv_w = 208, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7838, .adv_w = 336, .box_w = 19, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8161, .adv_w = 273, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8416, .adv_w = 265, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8671, .adv_w = 245, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8909, .adv_w = 265, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9209, .adv_w = 240, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9447, .adv_w = 232, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9685, .adv_w = 233, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9940, .adv_w = 250, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10178, .adv_w = 248, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10450, .adv_w = 338, .box_w = 21, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10807, .adv_w = 243, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11062, .adv_w = 234, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11317, .adv_w = 231, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11555, .adv_w = 105, .box_w = 6, .box_h = 24, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 11699, .adv_w = 161, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11908, .adv_w = 105, .box_w = 5, .box_h = 24, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12028, .adv_w = 164, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 12118, .adv_w = 173, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12151, .adv_w = 124, .box_w = 7, .box_h = 4, .ofs_x = 0, .ofs_y = 15},
    {.bitmap_index = 12179, .adv_w = 208, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12322, .adv_w = 216, .box_w = 12, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12538, .adv_w = 201, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12694, .adv_w = 217, .box_w = 13, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12928, .adv_w = 206, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13097, .adv_w = 136, .box_w = 9, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13268, .adv_w = 218, .box_w = 13, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 13502, .adv_w = 213, .box_w = 11, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13700, .adv_w = 98, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13768, .adv_w = 96, .box_w = 6, .box_h = 22, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 13900, .adv_w = 200, .box_w = 12, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14116, .adv_w = 98, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14188, .adv_w = 334, .box_w = 19, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14435, .adv_w = 214, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14578, .adv_w = 219, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14747, .adv_w = 216, .box_w = 12, .box_h = 18, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 14963, .adv_w = 218, .box_w = 13, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 15197, .adv_w = 135, .box_w = 8, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15309, .adv_w = 198, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15465, .adv_w = 128, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15593, .adv_w = 213, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15736, .adv_w = 190, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15892, .adv_w = 285, .box_w = 18, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16126, .adv_w = 193, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16282, .adv_w = 187, .box_w = 12, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 16498, .adv_w = 193, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16654, .adv_w = 129, .box_w = 8, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 16846, .adv_w = 96, .box_w = 2, .box_h = 21, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 16888, .adv_w = 129, .box_w = 8, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 17080, .adv_w = 255, .box_w = 14, .box_h = 5, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 17150, .adv_w = 146, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 11}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 1, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    1, 53,
    3, 3,
    3, 8,
    3, 34,
    3, 66,
    3, 68,
    3, 69,
    3, 70,
    3, 72,
    3, 78,
    3, 79,
    3, 80,
    3, 81,
    3, 82,
    3, 84,
    3, 88,
    8, 3,
    8, 8,
    8, 34,
    8, 66,
    8, 68,
    8, 69,
    8, 70,
    8, 72,
    8, 78,
    8, 79,
    8, 80,
    8, 81,
    8, 82,
    8, 84,
    8, 88,
    9, 55,
    9, 56,
    9, 58,
    13, 3,
    13, 8,
    15, 3,
    15, 8,
    16, 16,
    34, 3,
    34, 8,
    34, 32,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 78,
    34, 79,
    34, 80,
    34, 81,
    34, 85,
    34, 86,
    34, 87,
    34, 88,
    34, 90,
    34, 91,
    35, 53,
    35, 55,
    35, 58,
    36, 10,
    36, 53,
    36, 62,
    36, 94,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 57,
    37, 58,
    37, 59,
    38, 53,
    38, 68,
    38, 69,
    38, 70,
    38, 71,
    38, 72,
    38, 80,
    38, 82,
    38, 86,
    38, 87,
    38, 88,
    38, 90,
    39, 13,
    39, 15,
    39, 34,
    39, 43,
    39, 53,
    39, 66,
    39, 68,
    39, 69,
    39, 70,
    39, 72,
    39, 80,
    39, 82,
    39, 83,
    39, 86,
    39, 87,
    39, 90,
    41, 34,
    41, 53,
    41, 57,
    41, 58,
    42, 34,
    42, 53,
    42, 57,
    42, 58,
    43, 34,
    44, 14,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 80,
    44, 82,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 86,
    45, 87,
    45, 88,
    45, 90,
    46, 34,
    46, 53,
    46, 57,
    46, 58,
    47, 34,
    47, 53,
    47, 57,
    47, 58,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 57,
    48, 58,
    48, 59,
    49, 13,
    49, 15,
    49, 34,
    49, 43,
    49, 57,
    49, 59,
    49, 66,
    49, 68,
    49, 69,
    49, 70,
    49, 72,
    49, 80,
    49, 82,
    49, 85,
    49, 87,
    49, 90,
    50, 53,
    50, 55,
    50, 56,
    50, 58,
    51, 53,
    51, 55,
    51, 58,
    53, 1,
    53, 13,
    53, 14,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 43,
    53, 48,
    53, 50,
    53, 52,
    53, 53,
    53, 55,
    53, 56,
    53, 58,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    55, 10,
    55, 13,
    55, 14,
    55, 15,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 62,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 80,
    55, 82,
    55, 83,
    55, 86,
    55, 87,
    55, 90,
    55, 94,
    56, 10,
    56, 13,
    56, 14,
    56, 15,
    56, 34,
    56, 53,
    56, 62,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 83,
    56, 86,
    56, 94,
    57, 14,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 55,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 80,
    57, 82,
    57, 86,
    57, 87,
    57, 90,
    58, 7,
    58, 10,
    58, 11,
    58, 13,
    58, 14,
    58, 15,
    58, 34,
    58, 36,
    58, 40,
    58, 43,
    58, 48,
    58, 50,
    58, 52,
    58, 53,
    58, 54,
    58, 55,
    58, 56,
    58, 57,
    58, 58,
    58, 62,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    58, 94,
    59, 34,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    59, 68,
    59, 69,
    59, 70,
    59, 72,
    59, 80,
    59, 82,
    59, 86,
    59, 87,
    59, 88,
    59, 90,
    60, 43,
    60, 54,
    66, 3,
    66, 8,
    66, 87,
    66, 90,
    67, 3,
    67, 8,
    67, 87,
    67, 89,
    67, 90,
    67, 91,
    68, 3,
    68, 8,
    70, 3,
    70, 8,
    70, 87,
    70, 90,
    71, 3,
    71, 8,
    71, 10,
    71, 62,
    71, 68,
    71, 69,
    71, 70,
    71, 72,
    71, 82,
    71, 94,
    73, 3,
    73, 8,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 82,
    78, 3,
    78, 8,
    79, 3,
    79, 8,
    80, 3,
    80, 8,
    80, 87,
    80, 89,
    80, 90,
    80, 91,
    81, 3,
    81, 8,
    81, 87,
    81, 89,
    81, 90,
    81, 91,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 72,
    83, 80,
    83, 82,
    83, 85,
    83, 87,
    83, 88,
    83, 90,
    85, 80,
    87, 3,
    87, 8,
    87, 13,
    87, 15,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 71,
    87, 72,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 80,
    89, 82,
    90, 3,
    90, 8,
    90, 13,
    90, 15,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 71,
    90, 72,
    90, 80,
    90, 82,
    91, 68,
    91, 69,
    91, 70,
    91, 72,
    91, 80,
    91, 82,
    92, 43,
    92, 54
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -11, -7, -7, -22, -9, -11, -11, -11,
    -11, -4, -4, -17, -4, -11, -17, 2,
    -7, -7, -22, -9, -11, -11, -11, -11,
    -4, -4, -17, -4, -11, -17, 2, 4,
    7, 4, -53, -53, -53, -53, -46, -22,
    -22, -15, -4, -4, -4, -4, -22, -3,
    -14, -7, -28, -9, -9, -2, -9, -3,
    -2, -9, -6, -9, 2, -5, -4, -10,
    -5, -5, -2, -3, -23, -23, -4, -16,
    -4, -4, -8, -4, 4, -4, -4, -4,
    -3, -4, -4, -4, -3, -5, -4, -5,
    -51, -51, -36, -39, 4, -6, -4, -4,
    -4, -4, -4, -4, -5, -4, -4, -4,
    3, -5, 3, -5, 3, -5, 3, -5,
    -4, -31, -6, -6, -6, -6, -5, -5,
    -5, -5, -5, -5, -4, -7, -12, -7,
    -54, -54, 4, -12, -12, -12, -12, -38,
    -4, -39, -17, -52, -3, -23, -10, -23,
    3, -5, 3, -5, 3, -5, 3, -5,
    -23, -23, -4, -16, -4, -4, -8, -4,
    -76, -76, -33, -34, -10, -7, -2, -2,
    -2, -2, -2, -2, -2, 3, 3, 3,
    -6, -5, -4, -7, -9, -4, -9, -11,
    -48, -51, -48, -22, -5, -5, -40, -5,
    -5, -3, 3, 3, 3, 3, -31, -17,
    -17, -17, -17, -17, -17, -39, -17, -17,
    -12, -14, -12, -15, -9, -14, -15, -11,
    -4, 4, -40, -29, -40, -14, -2, -2,
    -2, -2, 3, -9, -8, -8, -8, -8,
    -9, -8, -6, -5, -2, -2, 4, 3,
    -27, -11, -27, -8, 3, 2, -6, -6,
    -6, -6, -6, -6, -6, -4, -4, 3,
    -29, -5, -5, -5, -5, 3, -5, -5,
    -5, -5, -4, -5, -4, -6, -6, -6,
    4, -9, -43, -28, -43, -28, -5, -5,
    -18, -5, -5, -3, 3, -18, 3, 3,
    2, 3, 3, -12, -12, -12, -12, -4,
    -12, -7, -7, -12, -7, -12, -7, -11,
    -4, -7, -4, -4, -4, -6, 4, 2,
    -5, -5, -5, -5, -4, -4, -4, -4,
    -4, -4, -4, -5, -5, -5, -3, -3,
    -3, -3, -3, -3, -5, -5, -2, -3,
    -2, -3, -2, -2, -3, -3, -2, -2,
    3, 3, 4, 3, -4, -4, -4, -4,
    -4, 4, -15, -15, -4, -4, -4, -4,
    -4, -15, -15, -15, -15, -16, -16, -3,
    -4, -3, -3, -5, -5, -2, -3, -2,
    -3, 3, 3, -32, -32, -6, -4, -4,
    -4, 4, -4, -7, -4, 9, 3, 3,
    3, -6, 3, 3, -31, -31, -3, -2,
    -2, -2, 2, -2, -3, -2, -23, -23,
    -4, -4, -4, -4, -7, -4, 3, 3,
    -31, -31, -3, -2, -2, -2, 2, -2,
    -3, -2, -3, -3, -3, -3, -3, -3,
    -4, -4
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 434,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 8,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_lv_demo_high_res_roboto_medium_24 = {
#else
lv_font_t font_lv_demo_high_res_roboto_medium_24 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 26,          /*The maximum line height required by the font*/
    .base_line = 6,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_USE_DEMO_HIGH_RES*/

