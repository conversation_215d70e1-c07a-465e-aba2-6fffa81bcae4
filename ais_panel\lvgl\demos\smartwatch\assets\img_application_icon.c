#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: application.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_application_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x1F,0x00,0x3E,0x0C,0x5E,0x0C,0x7E,0x0C,0x7E,0x0C,0x7E,0x0C,0x7E,0x0C,0x5E,0x0C,0x3D,0x0C,0x00,0x00,0x00,0x00,0x1D,0x0C,0x5E,0x0C,0x7E,0x0C,0x7E,0x0C,0x7E,0x0C,0x7E,0x0C,0x7E,0x0C,0x3E,0x0C,0x1F,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x13,0x9F,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x9F,0x0C,0x1E,0x0C,0x7B,0x23,0xFD,0x0B,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x3E,0x0C,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1E,0x0C,0x7B,0x13,0x1D,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x5E,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0x7B,0x13,0x1D,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x7F,0x0C,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0x7B,0x13,0x1D,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x7F,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0x7B,0x13,0x1D,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x7F,0x0C,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0x9F,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0x7B,0x13,0xFD,0x0B,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x7F,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x13,0x1E,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1E,0x0C,0x7B,0x13,0x9C,0x13,0x9F,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x5E,0x0C,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0x7B,0x13,0xDD,0x13,0x1D,0x0C,0x1D,0x0C,0x1D,0x0C,0x1D,0x0C,0x1D,0x0C,0xFD,0x0B,0x00,0x00,0x00,0x00,0x7B,0x13,0x9C,0x13,0xFD,0x0B,0x1D,0x0C,0x1D,0x0C,0x1D,0x0C,0x1D,0x0C,0x1D,0x0C,0x1D,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBD,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x5B,0x13,0x1A,0x04,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x3D,0x0C,0x1E,0x0C,0x3E,0x0C,0x3E,0x0C,0x3E,0x0C,0x3E,0x0C,0x3D,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1D,0xDF,0x1D,0xD7,0x1D,0xD7,0x1D,0xD7,0x1D,0xD7,0x1D,0xDF,0xFE,0xDE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0x13,0x7F,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x5E,0x0C,0x00,0x00,0x00,0x00,0x1D,0xD7,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3E,0xDF,0x1D,0xD7,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1E,0x0C,0xBC,0xCE,0x1D,0xD7,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3D,0xDF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0xBC,0xCE,0x1D,0xD7,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3E,0xDF,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0xBC,0xCE,0x1D,0xD7,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3E,0xDF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0xBC,0xCE,0x1D,0xD7,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3E,0xDF,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1D,0x0C,0xBC,0xCE,0x1D,0xD7,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3E,0xDF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x13,0x7F,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x1E,0x0C,0xBC,0xCE,0xFD,0xD6,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3E,0xDF,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7B,0x13,0xBC,0x13,0x7F,0x0C,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0xBF,0x04,0x7F,0x0C,0x7D,0x0C,0xBD,0xD6,0xDC,0xCE,0x1D,0xD7,0x3E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x5E,0xDF,0x3E,0xDF,0x1D,0xDF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBF,0x02,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x7B,0x13,0x5B,0x13,0x7B,0x13,0x00,0x00,0x00,0x00,0xBD,0xCE,0xDC,0xCE,0xBC,0xCE,0xBC,0xCE,0xBC,0xCE,0xBC,0xCE,0xDC,0xCE,0xBC,0xCE,0xFF,0xFF,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,


0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xAF,0xEC,0xED,0xED,0xED,0xED,0xDF,0x50,0x00,0x00,0x43,0xE1,0xED,0xED,0xED,0xED,0xEB,0xB3,0x06,0x00,0x00,0x00,0x00,0x6F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x16,0x07,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xB2,0x00,0x00,
0x00,0x00,0xB7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x65,0x38,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEC,0x00,0x00,0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,
0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,0x00,0x00,0xA5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x4A,0x25,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x00,0x00,0x00,0x00,0x2C,0xE8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xB5,0x00,0x00,0x94,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0x43,0x00,0x00,0x00,0x00,0x00,0x0B,0x3B,0x44,0x44,0x44,0x44,0x1E,0x00,0x00,0x00,0x00,0x24,0x44,0x44,0x44,0x44,0x39,0x06,0x00,0x00,0x00,
0x00,0x00,0x00,0x1B,0x6D,0x76,0x76,0x76,0x76,0x45,0x00,0x00,0x00,0x00,0x4B,0x76,0x76,0x76,0x76,0x68,0x16,0x00,0x00,0x00,0x00,0x00,0x29,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD2,0x00,0x00,0xB6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x50,0x00,0x00,0x00,0x00,0x9E,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x43,0x1F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0x00,0x00,0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,
0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x70,0x40,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x00,0x00,0x00,0x00,0xB9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x68,0x39,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xED,0x00,0x00,
0x00,0x00,0x74,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0x19,0x0C,0xE8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xAE,0x00,0x00,0x00,0x00,0x03,0x73,0xB7,0xBB,0xBB,0xBB,0xBB,0x9D,0x28,0x00,0x00,0x2C,0xA4,0xBB,0xBB,0xBB,0xBB,0xB5,0x6D,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,


}; // LVGL_9 compatible
const lv_img_dsc_t img_application_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 24,
   .header.h = 24,
   .data_size = sizeof(img_application_icon_data),
   .header.cf = LV_COLOR_FORMAT_NATIVE_WITH_ALPHA,
   .data = img_application_icon_data};

