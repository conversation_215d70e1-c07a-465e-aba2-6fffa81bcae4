/**
 * @file lv_example_tiny_ttf.h
 *
 */

#ifndef LV_EXAMPLE_TINY_TTF_H
#define LV_EXAMPLE_TINY_TTF_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_tiny_ttf_1(void);
void lv_example_tiny_ttf_2(void);
void lv_example_tiny_ttf_3(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_TINY_TTF_H*/
