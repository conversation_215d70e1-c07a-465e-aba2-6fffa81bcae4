/*******************************************************************************
 * Size: 20 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 20 --font Inter-SemiBold.ttf -r 0x20-0x7F --font ../../../scripts/built_in_font/DejaVuSans.ttf --range 0xFE99,0xFE97,0xFE8D,0xFEBF,0xFE83,0xFE95,0xFE8E,0xFEF3,0xFE8B,0xFEBC,0xFE87,0xFEB3,0xFE98,0xFECB,0xFEDF,0xFED3,0xFEEF,0xFED8,0xFEB1,0xFEAD,0x627,0xFEA9,0xFEF9,0xFED0,0x0644,0xFECF,0xFE94,0x0639,0xFECC,0x0646,0x0648,0x0645,0xFEE3,0xFEE4,0xFEE2,0x0631,0x0633,0x0628,0xFE91,0x0637,0x064A,0x062D,0x0641,0x0636,0x0642,0x0649,0x0638,0x0625,0x0623,0x0621,0xFEE0,0xFEDE,0xFECA,0xFEE8,0xFEE6,0xFEAE,0xFEB4,0xFEB2,0xFE92,0xFE90,0xFEC4,0xFEC2,0xFEF4,0xFEF2,0xFEAA,0xFEDB,0xFEDC,0xFEA0,0xFEA3,0xFEA4,0xFEA2,0xFEF7,0xFEC0,0xFEF0,0xFEE1,0xFEEE,0xFEC9 --font ../../../scripts/built_in_font/SimSun.woff --symbols 语語言標題月日电池今日距离天的速度时间设置蓝牙灯亮度音量最大限度光照强统计三平均高时速简体中文。 --format lvgl -o font_ebike_inter_20.c --force-fast-kern-format
 ******************************************************************************/

#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "../../../lvgl.h"
#endif

#ifndef FONT_EBIKE_INTER_20
    #define FONT_EBIKE_INTER_20 1
#endif

#if FONT_EBIKE_INTER_20

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x4f, 0xf7, 0x3f, 0xf6, 0x3f, 0xf6, 0x3f, 0xf6,
    0x2f, 0xf5, 0x2f, 0xf5, 0x2f, 0xf5, 0x1f, 0xf4,
    0x1f, 0xf4, 0x1f, 0xf4, 0x4, 0x40, 0x2e, 0xf4,
    0x7f, 0xf9, 0x1d, 0xe3,

    /* U+0022 "\"" */
    0xdf, 0x13, 0xfc, 0xdf, 0x13, 0xfc, 0xdf, 0x13,
    0xfc, 0xdf, 0x13, 0xfc, 0xdf, 0x13, 0xfc, 0x45,
    0x1, 0x53,

    /* U+0023 "#" */
    0x0, 0x0, 0xff, 0x0, 0xe, 0xf1, 0x0, 0x0,
    0x2f, 0xc0, 0x1, 0xfe, 0x0, 0x0, 0x5, 0xfa,
    0x0, 0x3f, 0xb0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0xdf, 0x20, 0xc, 0xf3, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0xef, 0x10, 0x0, 0x3, 0xfc,
    0x0, 0x1f, 0xe0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0xbf, 0x40, 0x9, 0xf6, 0x0, 0x0,
    0xe, 0xf1, 0x0, 0xcf, 0x30, 0x0, 0x0, 0xfe,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x3f, 0xc0, 0x1,
    0xfe, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x81, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xef, 0xfc,
    0x60, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x5f, 0xfd, 0x6f, 0x6b, 0xff, 0x80, 0xa,
    0xff, 0x10, 0xf2, 0xd, 0xfd, 0x0, 0xaf, 0xf2,
    0xf, 0x20, 0x0, 0x0, 0x4, 0xff, 0xe7, 0xf2,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xea, 0x40,
    0x0, 0x0, 0x0, 0x6a, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0xf, 0x58, 0xff, 0xb0, 0x4, 0x42,
    0x0, 0xf2, 0xa, 0xff, 0x0, 0xff, 0xc0, 0xf,
    0x20, 0xbf, 0xf0, 0xa, 0xff, 0xb5, 0xf7, 0xbf,
    0xfb, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x7, 0xcf, 0xff, 0xc7, 0x10, 0x0, 0x0,
    0x0, 0xf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x81, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x4, 0xcf, 0xe9, 0x0, 0x0, 0xd, 0xd0, 0x1,
    0xff, 0xac, 0xf9, 0x0, 0x9, 0xf3, 0x0, 0x5f,
    0x90, 0x1f, 0xd0, 0x4, 0xf7, 0x0, 0x4, 0xfb,
    0x3, 0xfc, 0x1, 0xec, 0x0, 0x0, 0xd, 0xff,
    0xff, 0x50, 0xbe, 0x10, 0x0, 0x0, 0x6, 0x98,
    0x30, 0x7f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xd1, 0x8e, 0xfb, 0x20, 0x0, 0x0, 0x9, 0xf3,
    0x8f, 0xcb, 0xfc, 0x0, 0x0, 0x4, 0xf7, 0xd,
    0xf1, 0xc, 0xf2, 0x0, 0x1, 0xec, 0x0, 0xfe,
    0x0, 0xaf, 0x30, 0x0, 0xbe, 0x10, 0xd, 0xf1,
    0xc, 0xf2, 0x0, 0x7f, 0x50, 0x0, 0x8f, 0xdb,
    0xfc, 0x0, 0x3f, 0x90, 0x0, 0x0, 0x9e, 0xfb,
    0x10,

    /* U+0026 "&" */
    0x0, 0x4, 0xcf, 0xfb, 0x30, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xcf,
    0xd3, 0x3e, 0xf8, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x9, 0xf9, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x3f,
    0xf3, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf4, 0x0, 0xff, 0x30,
    0x9, 0xff, 0x5b, 0xff, 0x33, 0xff, 0x20, 0xf,
    0xf8, 0x0, 0xcf, 0xeb, 0xfe, 0x0, 0x1f, 0xf8,
    0x0, 0x1d, 0xff, 0xf7, 0x0, 0xd, 0xff, 0x62,
    0x3a, 0xff, 0xf1, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x2a, 0xef, 0xeb, 0x53,
    0xff, 0xa0,

    /* U+0027 "'" */
    0xdf, 0x1d, 0xf1, 0xdf, 0x1d, 0xf1, 0xdf, 0x14,
    0x50,

    /* U+0028 "(" */
    0x0, 0x9, 0xfd, 0x0, 0x3, 0xff, 0x40, 0x0,
    0xbf, 0xd0, 0x0, 0x1f, 0xf8, 0x0, 0x7, 0xff,
    0x30, 0x0, 0xbf, 0xf0, 0x0, 0xd, 0xfd, 0x0,
    0x0, 0xff, 0xb0, 0x0, 0xf, 0xfa, 0x0, 0x0,
    0xff, 0xa0, 0x0, 0xf, 0xfb, 0x0, 0x0, 0xdf,
    0xd0, 0x0, 0xb, 0xff, 0x0, 0x0, 0x6f, 0xf3,
    0x0, 0x1, 0xff, 0x80, 0x0, 0xa, 0xfd, 0x0,
    0x0, 0x2f, 0xf5, 0x0, 0x0, 0x8f, 0xc0,

    /* U+0029 ")" */
    0x1e, 0xf6, 0x0, 0x6, 0xff, 0x10, 0x0, 0xff,
    0x90, 0x0, 0xaf, 0xf0, 0x0, 0x5f, 0xf4, 0x0,
    0x2f, 0xf8, 0x0, 0xf, 0xfb, 0x0, 0xd, 0xfd,
    0x0, 0xc, 0xfe, 0x0, 0xc, 0xfe, 0x0, 0xd,
    0xfd, 0x0, 0xf, 0xfb, 0x0, 0x2f, 0xf8, 0x0,
    0x5f, 0xf4, 0x0, 0xaf, 0xf0, 0x0, 0xff, 0x80,
    0x7, 0xff, 0x10, 0x1e, 0xf6, 0x0,

    /* U+002A "*" */
    0x0, 0x6, 0xf4, 0x0, 0x0, 0x70, 0x5f, 0x31,
    0x70, 0x5f, 0xe9, 0xf8, 0xff, 0x30, 0x5c, 0xff,
    0xfc, 0x40, 0x2, 0xaf, 0xff, 0x91, 0x4, 0xff,
    0xaf, 0xaf, 0xf3, 0x9, 0x25, 0xf3, 0x39, 0x0,
    0x0, 0x6f, 0x40, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0x0, 0x6,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd0, 0x0,
    0x0, 0x33, 0x38, 0xfe, 0x33, 0x32, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd0,
    0x0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0,

    /* U+002C "," */
    0x7, 0x82, 0xf, 0xf3, 0x1f, 0xf0, 0x4f, 0xc0,
    0x7f, 0x70, 0xbf, 0x10,

    /* U+002D "-" */
    0x11, 0x11, 0x11, 0x19, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xff, 0xf0,

    /* U+002E "." */
    0x0, 0x0, 0x4f, 0xe2, 0xaf, 0xf6, 0x3e, 0xd1,

    /* U+002F "/" */
    0x0, 0x0, 0x8, 0x81, 0x0, 0x0, 0x4f, 0xf0,
    0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0, 0xcf, 0x70,
    0x0, 0x1, 0xff, 0x30, 0x0, 0x5, 0xfe, 0x0,
    0x0, 0x9, 0xfa, 0x0, 0x0, 0xe, 0xf6, 0x0,
    0x0, 0x2f, 0xf1, 0x0, 0x0, 0x6f, 0xd0, 0x0,
    0x0, 0xaf, 0x90, 0x0, 0x0, 0xef, 0x50, 0x0,
    0x3, 0xff, 0x10, 0x0, 0x7, 0xfc, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x4f, 0xf0, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x19, 0xef, 0xeb, 0x40, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xf7, 0x0, 0xd, 0xff, 0x73, 0x5e,
    0xff, 0x30, 0x5f, 0xf7, 0x0, 0x2, 0xff, 0xb0,
    0xaf, 0xf1, 0x0, 0x0, 0xbf, 0xf0, 0xdf, 0xd0,
    0x0, 0x0, 0x7f, 0xf2, 0xef, 0xb0, 0x0, 0x0,
    0x6f, 0xf3, 0xef, 0xc0, 0x0, 0x0, 0x6f, 0xf3,
    0xdf, 0xd0, 0x0, 0x0, 0x8f, 0xf2, 0xaf, 0xf1,
    0x0, 0x0, 0xbf, 0xf0, 0x5f, 0xf8, 0x0, 0x2,
    0xff, 0xb0, 0xd, 0xff, 0x83, 0x6e, 0xff, 0x30,
    0x2, 0xef, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x19,
    0xef, 0xeb, 0x40, 0x0,

    /* U+0031 "1" */
    0x0, 0x4d, 0xff, 0x31, 0xaf, 0xff, 0xf3, 0xdf,
    0xfc, 0xff, 0x3e, 0xb1, 0x6f, 0xf3, 0x40, 0x6,
    0xff, 0x30, 0x0, 0x6f, 0xf3, 0x0, 0x6, 0xff,
    0x30, 0x0, 0x6f, 0xf3, 0x0, 0x6, 0xff, 0x30,
    0x0, 0x6f, 0xf3, 0x0, 0x6, 0xff, 0x30, 0x0,
    0x6f, 0xf3, 0x0, 0x6, 0xff, 0x30, 0x0, 0x6f,
    0xf3,

    /* U+0032 "2" */
    0x0, 0x6c, 0xff, 0xd8, 0x10, 0x0, 0xaf, 0xff,
    0xff, 0xfe, 0x20, 0x6f, 0xfa, 0x32, 0x8f, 0xfb,
    0xb, 0xfe, 0x0, 0x0, 0xaf, 0xf0, 0x46, 0x40,
    0x0, 0x8, 0xff, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xb0, 0x0, 0x0, 0x0, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0xbf, 0xf5,
    0x0, 0x0, 0x1, 0xcf, 0xf5, 0x0, 0x0, 0x1,
    0xdf, 0xf4, 0x0, 0x0, 0x2, 0xef, 0xf7, 0x33,
    0x33, 0x31, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x4b,
    0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+0033 "3" */
    0x0, 0x4b, 0xef, 0xfb, 0x50, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0x90, 0x4f, 0xfb, 0x42, 0x4c, 0xff,
    0x33, 0x88, 0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0,
    0x0, 0x2b, 0xfe, 0x10, 0x0, 0xf, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0xff, 0xff, 0xd5, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xf4, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xb4, 0x54, 0x0, 0x0, 0xd, 0xfd, 0xbf,
    0xf1, 0x0, 0x1, 0xff, 0xc5, 0xff, 0xd5, 0x35,
    0xdf, 0xf5, 0x9, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x4, 0xbe, 0xfe, 0xb4, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x2, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf7, 0x0, 0x0, 0x3, 0xff, 0x8f, 0xf7, 0x0,
    0x0, 0xd, 0xfd, 0xf, 0xf7, 0x0, 0x0, 0x8f,
    0xf2, 0xf, 0xf7, 0x0, 0x3, 0xff, 0x70, 0xf,
    0xf7, 0x0, 0xd, 0xfc, 0x0, 0xf, 0xf7, 0x0,
    0x8f, 0xf2, 0x0, 0xf, 0xf7, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x33, 0x33, 0x33, 0x4f, 0xf9, 0x31,
    0x0, 0x0, 0x0, 0xf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0x0,

    /* U+0035 "5" */
    0xb, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xd0, 0xe, 0xf9, 0x33, 0x33, 0x32,
    0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x2f, 0xf6,
    0xbf, 0xfb, 0x30, 0x4, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x3b, 0xe9, 0x20, 0x5e, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x82, 0x33, 0x0, 0x0, 0xf, 0xf8, 0x9f,
    0xf1, 0x0, 0x5, 0xff, 0x53, 0xff, 0xd4, 0x26,
    0xff, 0xd0, 0x7, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x4, 0xbe, 0xfe, 0x91, 0x0,

    /* U+0036 "6" */
    0x0, 0x8, 0xdf, 0xfc, 0x60, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xb0, 0xc, 0xff, 0x84, 0x5b, 0xff,
    0x64, 0xff, 0x40, 0x0, 0x5, 0x64, 0x9f, 0xd1,
    0x9e, 0xfd, 0x70, 0xc, 0xfb, 0xdf, 0xff, 0xff,
    0xa0, 0xef, 0xfe, 0x40, 0x2c, 0xff, 0x5e, 0xff,
    0x40, 0x0, 0x1f, 0xfb, 0xef, 0xf0, 0x0, 0x0,
    0xbf, 0xdb, 0xff, 0x0, 0x0, 0xc, 0xfd, 0x7f,
    0xf5, 0x0, 0x2, 0xff, 0xa1, 0xef, 0xf6, 0x24,
    0xdf, 0xf3, 0x4, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x2, 0xae, 0xfe, 0xb3, 0x0,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xb2, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x4, 0x44, 0x44, 0x46, 0xff,
    0x90, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0x0, 0x2f, 0xf8, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x10, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x4f, 0xf7,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x60, 0x0, 0x0, 0x0, 0xdf, 0xd0,
    0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0,
    0xe, 0xfd, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x5b, 0xff, 0xeb, 0x50, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0x90, 0x4f, 0xfb, 0x31, 0x3c, 0xff,
    0x35, 0xff, 0x30, 0x0, 0x4f, 0xf4, 0x1e, 0xfb,
    0x20, 0x2b, 0xfd, 0x0, 0x2b, 0xff, 0xff, 0xfa,
    0x10, 0x5, 0xdf, 0xff, 0xff, 0xd3, 0x4, 0xff,
    0xb2, 0x3, 0xcf, 0xf2, 0xcf, 0xf0, 0x0, 0x1,
    0xff, 0xae, 0xfc, 0x0, 0x0, 0xd, 0xfd, 0xdf,
    0xe0, 0x0, 0x1, 0xff, 0xc8, 0xff, 0xb2, 0x3,
    0xcf, 0xf6, 0xb, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x5, 0xbe, 0xfe, 0xb5, 0x0,

    /* U+0039 "9" */
    0x0, 0x5b, 0xff, 0xe9, 0x20, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0x40, 0x5f, 0xfc, 0x42, 0x5e, 0xfe,
    0xc, 0xfe, 0x0, 0x0, 0x3f, 0xf6, 0xef, 0xa0,
    0x0, 0x0, 0xff, 0xbd, 0xfd, 0x0, 0x0, 0x3f,
    0xfd, 0x7f, 0xfa, 0x20, 0x4d, 0xff, 0xe0, 0xcf,
    0xff, 0xff, 0xdb, 0xfd, 0x0, 0x8d, 0xfe, 0x91,
    0xcf, 0xc0, 0x0, 0x0, 0x0, 0xf, 0xf8, 0xaf,
    0xf1, 0x0, 0x8, 0xff, 0x34, 0xff, 0xd5, 0x59,
    0xff, 0xb0, 0x8, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x5, 0xcf, 0xfd, 0x80, 0x0,

    /* U+003A ":" */
    0x3e, 0xd1, 0xaf, 0xf6, 0x4f, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe2, 0xaf, 0xf6, 0x3e, 0xd1,

    /* U+003B ";" */
    0x1d, 0xe4, 0x6f, 0xfa, 0x1e, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x82, 0xf, 0xf3, 0x1f, 0xf0, 0x4f, 0xc0,
    0x7f, 0x70, 0xbf, 0x10,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xb9, 0x0, 0x0, 0x0, 0x5d, 0xff,
    0x90, 0x0, 0x6, 0xef, 0xff, 0xb3, 0x1, 0x8e,
    0xff, 0xf9, 0x20, 0x1, 0xff, 0xfd, 0x70, 0x0,
    0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xe8, 0x10, 0x0, 0x0, 0x17, 0xef, 0xff,
    0xa3, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x35,

    /* U+003D "=" */
    0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x33, 0x33, 0x33, 0x33, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x33, 0x33, 0x33, 0x33,
    0x31, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xff, 0xff, 0xf7,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xe7, 0x10,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x8e, 0xff, 0xfa, 0x30, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x3a, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x2b, 0xff, 0x90, 0x0,
    0x5, 0xbf, 0xff, 0xc3, 0x1, 0x7e, 0xff, 0xfa,
    0x30, 0x1, 0xff, 0xff, 0x92, 0x0, 0x0, 0x2f,
    0xe8, 0x10, 0x0, 0x0, 0x1, 0x70, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x3b, 0xef, 0xea, 0x20, 0x5, 0xff, 0xff,
    0xff, 0xf3, 0xe, 0xfe, 0x41, 0x7f, 0xfb, 0x2e,
    0xe5, 0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xe,
    0xfc, 0x0, 0x0, 0x1, 0xbf, 0xf5, 0x0, 0x0,
    0x4e, 0xff, 0x50, 0x0, 0x0, 0xef, 0xd1, 0x0,
    0x0, 0x2, 0xff, 0x40, 0x0, 0x0, 0x2, 0xbb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x30, 0x0, 0x0, 0x8, 0xff, 0x80,
    0x0, 0x0, 0x2, 0xee, 0x20, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xfd, 0x93, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x6f, 0xfd, 0x72, 0x0,
    0x3, 0x8f, 0xfe, 0x10, 0x0, 0x4f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xfc, 0x0, 0xd, 0xfa,
    0x0, 0x3a, 0xdd, 0x86, 0xa4, 0x1e, 0xf5, 0x4,
    0xff, 0x10, 0x5f, 0xff, 0xff, 0xff, 0x70, 0x7f,
    0xb0, 0x9f, 0xb0, 0x1f, 0xfa, 0x33, 0x9f, 0xf7,
    0x2, 0xff, 0xc, 0xf7, 0x7, 0xfc, 0x0, 0x0,
    0xdf, 0x70, 0xf, 0xf1, 0xdf, 0x50, 0x9f, 0x80,
    0x0, 0x9, 0xf7, 0x0, 0xff, 0x1d, 0xf5, 0x9,
    0xf8, 0x0, 0x0, 0x9f, 0x70, 0xf, 0xf0, 0xcf,
    0x70, 0x7f, 0xc0, 0x0, 0xc, 0xf7, 0x3, 0xfe,
    0xa, 0xfa, 0x3, 0xff, 0x60, 0x6, 0xff, 0xa0,
    0x9f, 0x90, 0x5f, 0xf1, 0x9, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xf2, 0x0, 0xef, 0x90, 0x7, 0xef,
    0xd8, 0x9, 0xff, 0xc3, 0x0, 0x6, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xd7, 0x30, 0x0, 0x36, 0x60, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6a, 0xdf, 0xff, 0xda,
    0x50, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x9, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfe, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xe7, 0xff, 0x20, 0x0, 0x0, 0x1, 0xff, 0x91,
    0xff, 0x80, 0x0, 0x0, 0x6, 0xff, 0x30, 0xbf,
    0xd0, 0x0, 0x0, 0xc, 0xfe, 0x0, 0x6f, 0xf3,
    0x0, 0x0, 0x2f, 0xf8, 0x0, 0x1f, 0xf9, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x50, 0x3, 0xff,
    0x81, 0x11, 0x12, 0xff, 0xb0, 0x9, 0xff, 0x20,
    0x0, 0x0, 0xaf, 0xf1, 0xf, 0xfd, 0x0, 0x0,
    0x0, 0x5f, 0xf7, 0x5f, 0xf7, 0x0, 0x0, 0x0,
    0xf, 0xfc,

    /* U+0042 "B" */
    0x9f, 0xff, 0xff, 0xfd, 0x81, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x9f, 0xf3, 0x22, 0x4b,
    0xff, 0x80, 0x9f, 0xf1, 0x0, 0x0, 0xff, 0xb0,
    0x9f, 0xf1, 0x0, 0x0, 0xff, 0x90, 0x9f, 0xf1,
    0x0, 0x2a, 0xff, 0x20, 0x9f, 0xff, 0xff, 0xff,
    0xb2, 0x0, 0x9f, 0xfe, 0xee, 0xef, 0xfc, 0x20,
    0x9f, 0xf1, 0x0, 0x4, 0xef, 0xd0, 0x9f, 0xf1,
    0x0, 0x0, 0x7f, 0xf3, 0x9f, 0xf1, 0x0, 0x0,
    0x8f, 0xf4, 0x9f, 0xf3, 0x33, 0x37, 0xff, 0xf1,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x70, 0x9f, 0xff,
    0xff, 0xfe, 0xb4, 0x0,

    /* U+0043 "C" */
    0x0, 0x3, 0xae, 0xff, 0xc7, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xfe, 0x30, 0x9, 0xff, 0xe8,
    0x66, 0xbf, 0xfe, 0x13, 0xff, 0xd1, 0x0, 0x0,
    0x7f, 0xf8, 0x9f, 0xf3, 0x0, 0x0, 0x0, 0xcd,
    0xbd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0, 0xa,
    0xc9, 0x3f, 0xfd, 0x10, 0x0, 0x7, 0xff, 0x80,
    0x9f, 0xfe, 0x86, 0x6b, 0xff, 0xe1, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x4a, 0xef,
    0xfc, 0x70, 0x0,

    /* U+0044 "D" */
    0x9f, 0xff, 0xff, 0xec, 0x70, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x9f, 0xf5, 0x44,
    0x6a, 0xff, 0xf2, 0x9, 0xff, 0x10, 0x0, 0x5,
    0xff, 0xb0, 0x9f, 0xf1, 0x0, 0x0, 0xa, 0xff,
    0x19, 0xff, 0x10, 0x0, 0x0, 0x5f, 0xf4, 0x9f,
    0xf1, 0x0, 0x0, 0x3, 0xff, 0x69, 0xff, 0x10,
    0x0, 0x0, 0x3f, 0xf6, 0x9f, 0xf1, 0x0, 0x0,
    0x5, 0xff, 0x49, 0xff, 0x10, 0x0, 0x0, 0xaf,
    0xf1, 0x9f, 0xf1, 0x0, 0x0, 0x5f, 0xfb, 0x9,
    0xff, 0x54, 0x46, 0xbf, 0xff, 0x20, 0x9f, 0xff,
    0xff, 0xff, 0xfe, 0x30, 0x9, 0xff, 0xff, 0xfe,
    0xc7, 0x0, 0x0,

    /* U+0045 "E" */
    0x9f, 0xff, 0xff, 0xff, 0xfe, 0x9f, 0xff, 0xff,
    0xff, 0xfe, 0x9f, 0xf4, 0x33, 0x33, 0x32, 0x9f,
    0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x9f, 0xf4, 0x33, 0x33, 0x31, 0x9f, 0xff,
    0xff, 0xff, 0xf6, 0x9f, 0xff, 0xff, 0xff, 0xf6,
    0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f,
    0xf4, 0x33, 0x33, 0x33, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xff, 0xff,

    /* U+0046 "F" */
    0x9f, 0xff, 0xff, 0xff, 0xfc, 0x9f, 0xff, 0xff,
    0xff, 0xfc, 0x9f, 0xf4, 0x33, 0x33, 0x32, 0x9f,
    0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x9f, 0xf4, 0x33, 0x33, 0x30, 0x9f, 0xff,
    0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xff, 0xf1,
    0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f,
    0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3, 0xae, 0xff, 0xc7, 0x10, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xfe, 0x40, 0x8, 0xff, 0xe8,
    0x66, 0xaf, 0xff, 0x23, 0xff, 0xd1, 0x0, 0x0,
    0x5f, 0xfa, 0x9f, 0xf3, 0x0, 0x0, 0x0, 0x34,
    0x3c, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xb0, 0x0, 0xe, 0xff, 0xff, 0xfe, 0xfb, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xdf, 0xe0, 0x0, 0x0,
    0x0, 0xaf, 0xf9, 0xff, 0x30, 0x0, 0x0, 0xd,
    0xfc, 0x3f, 0xfd, 0x10, 0x0, 0x7, 0xff, 0x70,
    0x9f, 0xfe, 0x86, 0x6b, 0xff, 0xd0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x3a, 0xef,
    0xfc, 0x70, 0x0,

    /* U+0048 "H" */
    0x9f, 0xf1, 0x0, 0x0, 0x3, 0xff, 0x79, 0xff,
    0x10, 0x0, 0x0, 0x3f, 0xf7, 0x9f, 0xf1, 0x0,
    0x0, 0x3, 0xff, 0x79, 0xff, 0x10, 0x0, 0x0,
    0x3f, 0xf7, 0x9f, 0xf1, 0x0, 0x0, 0x3, 0xff,
    0x79, 0xff, 0x43, 0x33, 0x33, 0x5f, 0xf7, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x79, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x9f, 0xf1, 0x0, 0x0,
    0x3, 0xff, 0x79, 0xff, 0x10, 0x0, 0x0, 0x3f,
    0xf7, 0x9f, 0xf1, 0x0, 0x0, 0x3, 0xff, 0x79,
    0xff, 0x10, 0x0, 0x0, 0x3f, 0xf7, 0x9f, 0xf1,
    0x0, 0x0, 0x3, 0xff, 0x79, 0xff, 0x10, 0x0,
    0x0, 0x3f, 0xf7,

    /* U+0049 "I" */
    0x9f, 0xf1, 0x9f, 0xf1, 0x9f, 0xf1, 0x9f, 0xf1,
    0x9f, 0xf1, 0x9f, 0xf1, 0x9f, 0xf1, 0x9f, 0xf1,
    0x9f, 0xf1, 0x9f, 0xf1, 0x9f, 0xf1, 0x9f, 0xf1,
    0x9f, 0xf1, 0x9f, 0xf1,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0x0, 0x0,
    0xd, 0xfc, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0,
    0x0, 0x0, 0xd, 0xfc, 0x0, 0x0, 0x0, 0xd,
    0xfc, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0x0,
    0x0, 0xd, 0xfc, 0x0, 0x0, 0x0, 0xd, 0xfc,
    0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0x0, 0x0,
    0xd, 0xfc, 0x5f, 0xf4, 0x0, 0xf, 0xfb, 0x2f,
    0xfd, 0x43, 0xaf, 0xf6, 0x9, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x6c, 0xff, 0xd7, 0x0,

    /* U+004B "K" */
    0x9f, 0xf1, 0x0, 0x0, 0x8f, 0xfa, 0x9, 0xff,
    0x10, 0x0, 0x6f, 0xfc, 0x0, 0x9f, 0xf1, 0x0,
    0x4f, 0xfd, 0x10, 0x9, 0xff, 0x10, 0x3f, 0xfe,
    0x10, 0x0, 0x9f, 0xf1, 0x2e, 0xff, 0x20, 0x0,
    0x9, 0xff, 0x2d, 0xff, 0x30, 0x0, 0x0, 0x9f,
    0xfd, 0xff, 0xb0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x9f, 0xff, 0x5e, 0xff,
    0x20, 0x0, 0x9, 0xff, 0x50, 0x3f, 0xfd, 0x0,
    0x0, 0x9f, 0xf1, 0x0, 0x8f, 0xf9, 0x0, 0x9,
    0xff, 0x10, 0x0, 0xcf, 0xf5, 0x0, 0x9f, 0xf1,
    0x0, 0x2, 0xff, 0xf2, 0x9, 0xff, 0x10, 0x0,
    0x5, 0xff, 0xc0,

    /* U+004C "L" */
    0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f,
    0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1,
    0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0,
    0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x9f,
    0xf4, 0x33, 0x33, 0x31, 0x9f, 0xff, 0xff, 0xff,
    0xf7, 0x9f, 0xff, 0xff, 0xff, 0xf7,

    /* U+004D "M" */
    0x9f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x9f, 0xff, 0x40, 0x0, 0x0, 0x2, 0xff, 0xfb,
    0x9f, 0xff, 0xb0, 0x0, 0x0, 0x9, 0xff, 0xfb,
    0x9f, 0xff, 0xf2, 0x0, 0x0, 0x1f, 0xff, 0xfb,
    0x9f, 0xfd, 0xf9, 0x0, 0x0, 0x7f, 0xfe, 0xfb,
    0x9f, 0xf6, 0xff, 0x0, 0x0, 0xdf, 0x8d, 0xfb,
    0x9f, 0xf0, 0xff, 0x60, 0x4, 0xff, 0x2d, 0xfb,
    0x9f, 0xf0, 0x9f, 0xd0, 0xb, 0xfb, 0xd, 0xfb,
    0x9f, 0xf0, 0x2f, 0xf4, 0x2f, 0xf4, 0xd, 0xfb,
    0x9f, 0xf0, 0xc, 0xfb, 0x9f, 0xd0, 0xd, 0xfb,
    0x9f, 0xf0, 0x5, 0xff, 0xff, 0x70, 0xd, 0xfb,
    0x9f, 0xf0, 0x0, 0xef, 0xff, 0x10, 0xd, 0xfb,
    0x9f, 0xf0, 0x0, 0x8f, 0xfa, 0x0, 0xd, 0xfb,
    0x9f, 0xf0, 0x0, 0x1f, 0xf3, 0x0, 0xd, 0xfb,

    /* U+004E "N" */
    0x9f, 0xf2, 0x0, 0x0, 0x3, 0xff, 0x69, 0xff,
    0xd0, 0x0, 0x0, 0x3f, 0xf6, 0x9f, 0xff, 0x90,
    0x0, 0x3, 0xff, 0x69, 0xff, 0xff, 0x40, 0x0,
    0x3f, 0xf6, 0x9f, 0xfd, 0xfe, 0x10, 0x3, 0xff,
    0x69, 0xff, 0x3f, 0xfb, 0x0, 0x3f, 0xf6, 0x9f,
    0xf1, 0x5f, 0xf7, 0x3, 0xff, 0x69, 0xff, 0x10,
    0xaf, 0xf3, 0x3f, 0xf6, 0x9f, 0xf1, 0x0, 0xdf,
    0xd4, 0xff, 0x69, 0xff, 0x10, 0x3, 0xff, 0xdf,
    0xf6, 0x9f, 0xf1, 0x0, 0x7, 0xff, 0xff, 0x69,
    0xff, 0x10, 0x0, 0xb, 0xff, 0xf6, 0x9f, 0xf1,
    0x0, 0x0, 0x1e, 0xff, 0x69, 0xff, 0x10, 0x0,
    0x0, 0x5f, 0xf6,

    /* U+004F "O" */
    0x0, 0x3, 0xae, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x8, 0xff,
    0xe8, 0x66, 0xbf, 0xfe, 0x10, 0x3f, 0xfd, 0x10,
    0x0, 0x6, 0xff, 0xa0, 0x9f, 0xf3, 0x0, 0x0,
    0x0, 0xbf, 0xf1, 0xdf, 0xe0, 0x0, 0x0, 0x0,
    0x6f, 0xf4, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x3f,
    0xf6, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xf6,
    0xdf, 0xe0, 0x0, 0x0, 0x0, 0x6f, 0xf4, 0x9f,
    0xf3, 0x0, 0x0, 0x0, 0xbf, 0xf1, 0x3f, 0xfd,
    0x10, 0x0, 0x6, 0xff, 0xa0, 0x9, 0xff, 0xe8,
    0x66, 0xbf, 0xfe, 0x20, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xe3, 0x0, 0x0, 0x3, 0xae, 0xff, 0xc7,
    0x0, 0x0,

    /* U+0050 "P" */
    0x9f, 0xff, 0xff, 0xfd, 0x70, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x9f, 0xf3, 0x33, 0x4b,
    0xff, 0x80, 0x9f, 0xf1, 0x0, 0x0, 0xef, 0xe0,
    0x9f, 0xf1, 0x0, 0x0, 0xbf, 0xf0, 0x9f, 0xf1,
    0x0, 0x0, 0xdf, 0xe0, 0x9f, 0xf1, 0x0, 0x19,
    0xff, 0xa0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x9f, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x9f, 0xf3,
    0x22, 0x20, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf1,
    0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x3, 0xae, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x8, 0xff,
    0xe8, 0x66, 0xbf, 0xfe, 0x10, 0x3f, 0xfd, 0x10,
    0x0, 0x6, 0xff, 0xa0, 0x9f, 0xf3, 0x0, 0x0,
    0x0, 0xbf, 0xf1, 0xdf, 0xe0, 0x0, 0x0, 0x0,
    0x6f, 0xf4, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x3f,
    0xf6, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xf6,
    0xdf, 0xe0, 0x0, 0x0, 0x0, 0x6f, 0xf4, 0x9f,
    0xf3, 0x0, 0xad, 0x70, 0xbf, 0xf1, 0x3f, 0xfd,
    0x10, 0x2f, 0xfa, 0xff, 0xa0, 0x9, 0xff, 0xe8,
    0x6a, 0xff, 0xfe, 0x20, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x3, 0xae, 0xff, 0xcc,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xa0,

    /* U+0052 "R" */
    0x9f, 0xff, 0xff, 0xfd, 0x80, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x9f, 0xf3, 0x33, 0x4b,
    0xff, 0x90, 0x9f, 0xf1, 0x0, 0x0, 0xdf, 0xe0,
    0x9f, 0xf1, 0x0, 0x0, 0xbf, 0xf0, 0x9f, 0xf1,
    0x0, 0x0, 0xdf, 0xe0, 0x9f, 0xf4, 0x33, 0x4b,
    0xff, 0x90, 0x9f, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x9f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x9f, 0xf1,
    0x0, 0xef, 0xe0, 0x0, 0x9f, 0xf1, 0x0, 0x5f,
    0xf8, 0x0, 0x9f, 0xf1, 0x0, 0xc, 0xff, 0x20,
    0x9f, 0xf1, 0x0, 0x3, 0xff, 0xa0, 0x9f, 0xf1,
    0x0, 0x0, 0xaf, 0xf4,

    /* U+0053 "S" */
    0x0, 0x4, 0xbe, 0xff, 0xc6, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x5, 0xff, 0xd5,
    0x35, 0xbf, 0xf8, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0xdf, 0xd0, 0xa, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xfe, 0xa4, 0x0, 0x0, 0x0, 0x6,
    0xae, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x2,
    0x8f, 0xfb, 0x0, 0x44, 0x20, 0x0, 0x0, 0xaf,
    0xf0, 0xf, 0xfc, 0x0, 0x0, 0xb, 0xff, 0x0,
    0xaf, 0xfb, 0x54, 0x5b, 0xff, 0xb0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x7c, 0xef,
    0xfc, 0x71, 0x0,

    /* U+0054 "T" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x63, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x3, 0x33, 0x3c,
    0xfe, 0x33, 0x33, 0x10, 0x0, 0x0, 0xbf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xe0, 0x0, 0x0,

    /* U+0055 "U" */
    0x9f, 0xf1, 0x0, 0x0, 0x6, 0xff, 0x39, 0xff,
    0x10, 0x0, 0x0, 0x6f, 0xf3, 0x9f, 0xf1, 0x0,
    0x0, 0x6, 0xff, 0x39, 0xff, 0x10, 0x0, 0x0,
    0x6f, 0xf3, 0x9f, 0xf1, 0x0, 0x0, 0x6, 0xff,
    0x39, 0xff, 0x10, 0x0, 0x0, 0x6f, 0xf3, 0x9f,
    0xf1, 0x0, 0x0, 0x6, 0xff, 0x39, 0xff, 0x10,
    0x0, 0x0, 0x6f, 0xf3, 0x9f, 0xf1, 0x0, 0x0,
    0x6, 0xff, 0x38, 0xff, 0x20, 0x0, 0x0, 0x8f,
    0xf3, 0x5f, 0xfa, 0x0, 0x0, 0x1e, 0xff, 0x0,
    0xdf, 0xfc, 0x75, 0x8e, 0xff, 0x80, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x7c, 0xff,
    0xeb, 0x50, 0x0,

    /* U+0056 "V" */
    0x5f, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0xf,
    0xfe, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x9, 0xff,
    0x30, 0x0, 0x0, 0xcf, 0xf1, 0x3, 0xff, 0x90,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0xdf, 0xe0, 0x0,
    0x7, 0xff, 0x50, 0x0, 0x8f, 0xf4, 0x0, 0xc,
    0xff, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0xc, 0xfe, 0x0, 0x7f, 0xf3, 0x0,
    0x0, 0x6, 0xff, 0x40, 0xdf, 0xd0, 0x0, 0x0,
    0x1, 0xff, 0xa2, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xaf, 0xf8, 0xff, 0x20, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf1,
    0x0, 0x0,

    /* U+0057 "W" */
    0x7f, 0xf5, 0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0,
    0x3f, 0xf9, 0x2f, 0xf9, 0x0, 0x0, 0xaf, 0xfc,
    0x0, 0x0, 0x7f, 0xf5, 0xe, 0xfd, 0x0, 0x0,
    0xef, 0xff, 0x0, 0x0, 0xbf, 0xf0, 0x9, 0xff,
    0x10, 0x2, 0xff, 0xff, 0x40, 0x0, 0xff, 0xb0,
    0x4, 0xff, 0x50, 0x7, 0xfe, 0xcf, 0x90, 0x3,
    0xff, 0x60, 0x0, 0xff, 0xa0, 0xb, 0xf9, 0x7f,
    0xd0, 0x7, 0xff, 0x20, 0x0, 0xbf, 0xe0, 0xf,
    0xf5, 0x2f, 0xf2, 0xb, 0xfd, 0x0, 0x0, 0x6f,
    0xf2, 0x4f, 0xf0, 0xe, 0xf6, 0xf, 0xf8, 0x0,
    0x0, 0x1f, 0xf6, 0x8f, 0xb0, 0x9, 0xfa, 0x4f,
    0xf4, 0x0, 0x0, 0xd, 0xfa, 0xcf, 0x60, 0x4,
    0xfe, 0x8f, 0xf0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x20, 0x0, 0xff, 0xef, 0xa0, 0x0, 0x0, 0x3,
    0xff, 0xfd, 0x0, 0x0, 0xbf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xef, 0xf8, 0x0, 0x0, 0x6f, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0,
    0x1f, 0xfc, 0x0, 0x0,

    /* U+0058 "X" */
    0x1f, 0xfe, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x6,
    0xff, 0x90, 0x0, 0xc, 0xff, 0x30, 0x0, 0xcf,
    0xf3, 0x0, 0x6f, 0xf9, 0x0, 0x0, 0x2f, 0xfd,
    0x1, 0xff, 0xe0, 0x0, 0x0, 0x7, 0xff, 0x7a,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x7a, 0xff, 0x50, 0x0, 0x0, 0x2f,
    0xfd, 0x1, 0xff, 0xe1, 0x0, 0x0, 0xcf, 0xf3,
    0x0, 0x6f, 0xf9, 0x0, 0x7, 0xff, 0x80, 0x0,
    0xc, 0xff, 0x40, 0x2f, 0xfd, 0x0, 0x0, 0x2,
    0xff, 0xe0,

    /* U+0059 "Y" */
    0x4f, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xf5, 0xa,
    0xff, 0x40, 0x0, 0x3, 0xff, 0xb0, 0x1, 0xff,
    0xd0, 0x0, 0xc, 0xff, 0x20, 0x0, 0x7f, 0xf6,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0xd, 0xfe, 0x10,
    0xef, 0xe0, 0x0, 0x0, 0x4, 0xff, 0x97, 0xff,
    0x50, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x0,

    /* U+005A "Z" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xee, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x23, 0x33, 0x33, 0x3b, 0xff,
    0x40, 0x0, 0x0, 0x5, 0xff, 0x80, 0x0, 0x0,
    0x2, 0xef, 0xc0, 0x0, 0x0, 0x0, 0xcf, 0xe2,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x1e, 0xfd, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x7,
    0xff, 0x60, 0x0, 0x0, 0x3, 0xff, 0xc3, 0x33,
    0x33, 0x33, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+005B "[" */
    0xcf, 0xff, 0xf5, 0xcf, 0xff, 0xf5, 0xcf, 0xa0,
    0x0, 0xcf, 0xa0, 0x0, 0xcf, 0xa0, 0x0, 0xcf,
    0xa0, 0x0, 0xcf, 0xa0, 0x0, 0xcf, 0xa0, 0x0,
    0xcf, 0xa0, 0x0, 0xcf, 0xa0, 0x0, 0xcf, 0xa0,
    0x0, 0xcf, 0xa0, 0x0, 0xcf, 0xa0, 0x0, 0xcf,
    0xa0, 0x0, 0xcf, 0xa0, 0x0, 0xcf, 0xa0, 0x0,
    0xcf, 0xff, 0xf5, 0xcf, 0xff, 0xf5,

    /* U+005C "\\" */
    0x58, 0x50, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0,
    0x9, 0xfa, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x1, 0xff, 0x20, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x0, 0x8f, 0xb0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0xf, 0xf4, 0x0, 0x0, 0xb, 0xf8, 0x0,
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x3, 0xff, 0x10,
    0x0, 0x0, 0xef, 0x50, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0x2f, 0xf2,

    /* U+005D "]" */
    0x7f, 0xff, 0xfa, 0x7f, 0xff, 0xfa, 0x0, 0xc,
    0xfa, 0x0, 0xc, 0xfa, 0x0, 0xc, 0xfa, 0x0,
    0xc, 0xfa, 0x0, 0xc, 0xfa, 0x0, 0xc, 0xfa,
    0x0, 0xc, 0xfa, 0x0, 0xc, 0xfa, 0x0, 0xc,
    0xfa, 0x0, 0xc, 0xfa, 0x0, 0xc, 0xfa, 0x0,
    0xc, 0xfa, 0x0, 0xc, 0xfa, 0x0, 0xc, 0xfa,
    0x7f, 0xff, 0xfa, 0x7f, 0xff, 0xfa,

    /* U+005E "^" */
    0x0, 0x8, 0xff, 0x30, 0x0, 0x1, 0xff, 0xfa,
    0x0, 0x0, 0x7f, 0x8d, 0xf2, 0x0, 0xe, 0xf1,
    0x6f, 0x90, 0x6, 0xfa, 0x0, 0xff, 0x10, 0xef,
    0x30, 0x8, 0xf8, 0x16, 0x50, 0x0, 0x16, 0x50,

    /* U+005F "_" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0xf, 0xff, 0xff, 0xff, 0xff,
    0x60,

    /* U+0060 "`" */
    0x3, 0x31, 0x0, 0x9, 0xfc, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0x3f, 0xc0,

    /* U+0061 "a" */
    0x0, 0x19, 0xdf, 0xfc, 0x50, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0x80, 0xa, 0xff, 0x40, 0x1b, 0xff,
    0x10, 0x2, 0x20, 0x0, 0x6f, 0xf3, 0x0, 0x2,
    0x57, 0x9e, 0xff, 0x30, 0x3d, 0xff, 0xff, 0xef,
    0xf3, 0xe, 0xfe, 0x63, 0x5, 0xff, 0x32, 0xff,
    0x50, 0x0, 0x7f, 0xf3, 0x1f, 0xf9, 0x0, 0x3e,
    0xff, 0x30, 0xbf, 0xff, 0xef, 0xcf, 0xf3, 0x0,
    0x9e, 0xfd, 0x74, 0xff, 0x30,

    /* U+0062 "b" */
    0x9f, 0xf0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x9, 0xef, 0xc5, 0x0, 0x9f, 0xf9,
    0xff, 0xff, 0xf7, 0x9, 0xff, 0xe5, 0x14, 0xef,
    0xf2, 0x9f, 0xf5, 0x0, 0x4, 0xff, 0x79, 0xff,
    0x0, 0x0, 0xf, 0xfa, 0x9f, 0xf0, 0x0, 0x0,
    0xef, 0xb9, 0xff, 0x0, 0x0, 0xf, 0xfa, 0x9f,
    0xf6, 0x0, 0x5, 0xff, 0x79, 0xff, 0xe5, 0x15,
    0xef, 0xf2, 0x9f, 0xfa, 0xff, 0xff, 0xf7, 0x9,
    0xff, 0x9, 0xef, 0xc5, 0x0,

    /* U+0063 "c" */
    0x0, 0x6, 0xcf, 0xfd, 0x70, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xc0, 0x6, 0xff, 0xa2, 0x18, 0xff,
    0x60, 0xdf, 0xd0, 0x0, 0xb, 0xd9, 0x1f, 0xf8,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xd0, 0x0, 0xb, 0xe9, 0x6, 0xff, 0xb2, 0x29,
    0xff, 0x60, 0xa, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x6, 0xcf, 0xfd, 0x70, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x9, 0xef, 0xd5, 0x6f, 0xf2,
    0x0, 0xdf, 0xff, 0xff, 0xaf, 0xf2, 0x8, 0xff,
    0xa2, 0x29, 0xff, 0xf2, 0xe, 0xfe, 0x0, 0x0,
    0xbf, 0xf2, 0xf, 0xf9, 0x0, 0x0, 0x7f, 0xf2,
    0x1f, 0xf8, 0x0, 0x0, 0x5f, 0xf2, 0xf, 0xf9,
    0x0, 0x0, 0x7f, 0xf2, 0xe, 0xfe, 0x0, 0x0,
    0xcf, 0xf2, 0x8, 0xff, 0xb2, 0x29, 0xff, 0xf2,
    0x0, 0xdf, 0xff, 0xff, 0x9f, 0xf2, 0x0, 0x19,
    0xef, 0xd4, 0x5f, 0xf2,

    /* U+0065 "e" */
    0x0, 0x6, 0xcf, 0xfc, 0x70, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xb0, 0x6, 0xff, 0x80, 0x6, 0xff,
    0x60, 0xdf, 0xa0, 0x0, 0xa, 0xfc, 0xf, 0xfd,
    0xbb, 0xbb, 0xdf, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x1f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xb0, 0x0, 0x4, 0x52, 0x7, 0xff, 0x81, 0x5,
    0xff, 0xa0, 0xb, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x6, 0xcf, 0xfd, 0x81, 0x0,

    /* U+0066 "f" */
    0x0, 0x1, 0x67, 0x61, 0x0, 0x3f, 0xff, 0xf2,
    0x0, 0xcf, 0xfa, 0xa0, 0x0, 0xff, 0xa0, 0x0,
    0x9f, 0xff, 0xff, 0xd0, 0x9f, 0xff, 0xff, 0xd0,
    0x0, 0xff, 0x90, 0x0, 0x0, 0xff, 0x90, 0x0,
    0x0, 0xff, 0x90, 0x0, 0x0, 0xff, 0x90, 0x0,
    0x0, 0xff, 0x90, 0x0, 0x0, 0xff, 0x90, 0x0,
    0x0, 0xff, 0x90, 0x0, 0x0, 0xff, 0x90, 0x0,
    0x0, 0xff, 0x90, 0x0,

    /* U+0067 "g" */
    0x0, 0x8, 0xef, 0xd5, 0x5f, 0xf3, 0x0, 0xcf,
    0xff, 0xff, 0x9f, 0xf3, 0x7, 0xff, 0xb2, 0x29,
    0xff, 0xf3, 0xd, 0xfe, 0x0, 0x0, 0xcf, 0xf3,
    0xf, 0xf9, 0x0, 0x0, 0x7f, 0xf3, 0x1f, 0xf8,
    0x0, 0x0, 0x5f, 0xf3, 0x1f, 0xf9, 0x0, 0x0,
    0x6f, 0xf3, 0xe, 0xfd, 0x0, 0x0, 0xbf, 0xf3,
    0x8, 0xff, 0x91, 0x7, 0xff, 0xf3, 0x1, 0xdf,
    0xff, 0xff, 0xaf, 0xf3, 0x0, 0x19, 0xef, 0xd5,
    0x5f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2,
    0x6, 0xde, 0x40, 0x3, 0xdf, 0xd0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x19, 0xdf, 0xfd,
    0x92, 0x0,

    /* U+0068 "h" */
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0,
    0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0xaf,
    0xd1, 0xaf, 0xfc, 0x40, 0xaf, 0xdc, 0xff, 0xff,
    0xf4, 0xaf, 0xfd, 0x43, 0x8f, 0xfb, 0xaf, 0xf2,
    0x0, 0xc, 0xfe, 0xaf, 0xe0, 0x0, 0x9, 0xff,
    0xaf, 0xe0, 0x0, 0x9, 0xff, 0xaf, 0xe0, 0x0,
    0x9, 0xff, 0xaf, 0xe0, 0x0, 0x9, 0xff, 0xaf,
    0xe0, 0x0, 0x9, 0xff, 0xaf, 0xe0, 0x0, 0x9,
    0xff, 0xaf, 0xe0, 0x0, 0x9, 0xff,

    /* U+0069 "i" */
    0x17, 0x30, 0xcf, 0xf0, 0xaf, 0xe0, 0x4, 0x10,
    0xaf, 0xe0, 0xaf, 0xe0, 0xaf, 0xe0, 0xaf, 0xe0,
    0xaf, 0xe0, 0xaf, 0xe0, 0xaf, 0xe0, 0xaf, 0xe0,
    0xaf, 0xe0, 0xaf, 0xe0, 0xaf, 0xe0,

    /* U+006A "j" */
    0x0, 0x17, 0x30, 0x0, 0xcf, 0xf0, 0x0, 0xbf,
    0xd0, 0x0, 0x4, 0x10, 0x0, 0xaf, 0xe0, 0x0,
    0xaf, 0xe0, 0x0, 0xaf, 0xe0, 0x0, 0xaf, 0xe0,
    0x0, 0xaf, 0xe0, 0x0, 0xaf, 0xe0, 0x0, 0xaf,
    0xe0, 0x0, 0xaf, 0xe0, 0x0, 0xaf, 0xe0, 0x0,
    0xaf, 0xe0, 0x0, 0xaf, 0xe0, 0x0, 0xaf, 0xe0,
    0x3, 0xef, 0xc0, 0x6f, 0xff, 0x60, 0x6f, 0xd6,
    0x0,

    /* U+006B "k" */
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0xa, 0xfe, 0x0, 0x7, 0xff, 0x90, 0xaf, 0xe0,
    0x5, 0xff, 0xa0, 0xa, 0xfe, 0x3, 0xff, 0xc0,
    0x0, 0xaf, 0xe2, 0xef, 0xd1, 0x0, 0xa, 0xff,
    0xdf, 0xe2, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x40,
    0x0, 0xa, 0xff, 0xbf, 0xfe, 0x10, 0x0, 0xaf,
    0xf0, 0x5f, 0xfb, 0x0, 0xa, 0xfe, 0x0, 0xaf,
    0xf6, 0x0, 0xaf, 0xe0, 0x0, 0xdf, 0xf3, 0xa,
    0xfe, 0x0, 0x3, 0xff, 0xd0,

    /* U+006C "l" */
    0xaf, 0xea, 0xfe, 0xaf, 0xea, 0xfe, 0xaf, 0xea,
    0xfe, 0xaf, 0xea, 0xfe, 0xaf, 0xea, 0xfe, 0xaf,
    0xea, 0xfe, 0xaf, 0xea, 0xfe,

    /* U+006D "m" */
    0xaf, 0xc2, 0xbf, 0xfa, 0x11, 0xae, 0xfc, 0x30,
    0xaf, 0xde, 0xff, 0xff, 0xbc, 0xff, 0xff, 0xf2,
    0xaf, 0xfa, 0x12, 0xcf, 0xfd, 0x21, 0x8f, 0xf8,
    0xaf, 0xf0, 0x0, 0x5f, 0xf5, 0x0, 0xf, 0xfa,
    0xaf, 0xe0, 0x0, 0x4f, 0xf4, 0x0, 0xe, 0xfa,
    0xaf, 0xe0, 0x0, 0x4f, 0xf4, 0x0, 0xe, 0xfa,
    0xaf, 0xe0, 0x0, 0x4f, 0xf4, 0x0, 0xe, 0xfa,
    0xaf, 0xe0, 0x0, 0x4f, 0xf4, 0x0, 0xe, 0xfa,
    0xaf, 0xe0, 0x0, 0x4f, 0xf4, 0x0, 0xe, 0xfa,
    0xaf, 0xe0, 0x0, 0x4f, 0xf4, 0x0, 0xe, 0xfa,
    0xaf, 0xe0, 0x0, 0x4f, 0xf4, 0x0, 0xe, 0xfa,

    /* U+006E "n" */
    0xaf, 0xc1, 0xaf, 0xfc, 0x40, 0xaf, 0xdd, 0xff,
    0xff, 0xf3, 0xaf, 0xfb, 0x20, 0x7f, 0xfa, 0xaf,
    0xf1, 0x0, 0xc, 0xfd, 0xaf, 0xe0, 0x0, 0xa,
    0xfe, 0xaf, 0xe0, 0x0, 0xa, 0xfe, 0xaf, 0xe0,
    0x0, 0xa, 0xfe, 0xaf, 0xe0, 0x0, 0xa, 0xfe,
    0xaf, 0xe0, 0x0, 0xa, 0xfe, 0xaf, 0xe0, 0x0,
    0xa, 0xfe, 0xaf, 0xe0, 0x0, 0xa, 0xfe,

    /* U+006F "o" */
    0x0, 0x6, 0xcf, 0xfd, 0x70, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xfc, 0x0, 0x6, 0xff, 0xa1, 0x18,
    0xff, 0x90, 0xd, 0xfd, 0x0, 0x0, 0xbf, 0xf0,
    0x1f, 0xf8, 0x0, 0x0, 0x6f, 0xf3, 0x2f, 0xf7,
    0x0, 0x0, 0x4f, 0xf4, 0x1f, 0xf8, 0x0, 0x0,
    0x6f, 0xf3, 0xd, 0xfd, 0x0, 0x0, 0xbf, 0xf0,
    0x6, 0xff, 0xa2, 0x18, 0xff, 0x90, 0x0, 0xaf,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x6, 0xcf, 0xfd,
    0x70, 0x0,

    /* U+0070 "p" */
    0xaf, 0xd1, 0xaf, 0xfc, 0x40, 0xa, 0xfe, 0xcf,
    0xff, 0xff, 0x50, 0xaf, 0xfd, 0x20, 0x3e, 0xff,
    0x1a, 0xff, 0x30, 0x0, 0x5f, 0xf6, 0xaf, 0xe0,
    0x0, 0x1, 0xff, 0x8a, 0xfd, 0x0, 0x0, 0xf,
    0xf9, 0xaf, 0xf0, 0x0, 0x1, 0xff, 0x8a, 0xff,
    0x40, 0x0, 0x7f, 0xf6, 0xaf, 0xfe, 0x41, 0x5f,
    0xff, 0x1a, 0xfe, 0xbf, 0xff, 0xff, 0x50, 0xaf,
    0xe1, 0xaf, 0xfc, 0x40, 0xa, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0, 0xa,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x9, 0xef, 0xd5, 0x5f, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0x9f, 0xf2, 0x8, 0xff, 0xa2, 0x29,
    0xff, 0xf2, 0xe, 0xfe, 0x0, 0x0, 0xbf, 0xf2,
    0xf, 0xf9, 0x0, 0x0, 0x7f, 0xf2, 0x1f, 0xf8,
    0x0, 0x0, 0x5f, 0xf2, 0xf, 0xf9, 0x0, 0x0,
    0x7f, 0xf2, 0xe, 0xfe, 0x0, 0x0, 0xcf, 0xf2,
    0x8, 0xff, 0xb2, 0x29, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xaf, 0xf2, 0x0, 0x19, 0xef, 0xd4,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf2,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0xa, 0xfd, 0x3c, 0xf8, 0xaf,
    0xde, 0xff, 0x8a, 0xff, 0xd4, 0x21, 0xaf, 0xf2,
    0x0, 0xa, 0xfe, 0x0, 0x0, 0xaf, 0xe0, 0x0,
    0xa, 0xfe, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0xa,
    0xfe, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0xa, 0xfe,
    0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x3a, 0xef, 0xeb, 0x30, 0x0, 0x4f, 0xff,
    0xef, 0xff, 0x40, 0xc, 0xfe, 0x20, 0x1d, 0xfc,
    0x0, 0xdf, 0xd0, 0x0, 0x12, 0x10, 0x8, 0xff,
    0xe9, 0x62, 0x0, 0x0, 0x9, 0xff, 0xff, 0xfc,
    0x30, 0x0, 0x0, 0x47, 0xbf, 0xfe, 0x0, 0x34,
    0x20, 0x0, 0x9f, 0xf2, 0xf, 0xfc, 0x10, 0x1c,
    0xff, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x4b, 0xef, 0xeb, 0x40, 0x0,

    /* U+0074 "t" */
    0x0, 0x78, 0x40, 0x0, 0xf, 0xf9, 0x0, 0x0,
    0xff, 0x90, 0x8, 0xff, 0xff, 0xfc, 0x7f, 0xff,
    0xff, 0xc0, 0xf, 0xf9, 0x0, 0x0, 0xff, 0x90,
    0x0, 0xf, 0xf9, 0x0, 0x0, 0xff, 0x90, 0x0,
    0xf, 0xf9, 0x0, 0x0, 0xff, 0x90, 0x0, 0xe,
    0xfd, 0x32, 0x0, 0x9f, 0xff, 0xb0, 0x0, 0x8e,
    0xfc,

    /* U+0075 "u" */
    0xaf, 0xe0, 0x0, 0xb, 0xfd, 0xaf, 0xe0, 0x0,
    0xb, 0xfd, 0xaf, 0xe0, 0x0, 0xb, 0xfd, 0xaf,
    0xe0, 0x0, 0xb, 0xfd, 0xaf, 0xe0, 0x0, 0xb,
    0xfd, 0xaf, 0xe0, 0x0, 0xb, 0xfd, 0xaf, 0xe0,
    0x0, 0xb, 0xfd, 0xaf, 0xf0, 0x0, 0xe, 0xfd,
    0x7f, 0xfb, 0x23, 0xbf, 0xfd, 0x1e, 0xff, 0xff,
    0xeb, 0xfd, 0x2, 0xbf, 0xfb, 0x2a, 0xfd,

    /* U+0076 "v" */
    0x5f, 0xf5, 0x0, 0x0, 0xdf, 0xe0, 0xf, 0xfa,
    0x0, 0x2, 0xff, 0x80, 0xa, 0xff, 0x0, 0x7,
    0xff, 0x20, 0x4, 0xff, 0x40, 0xc, 0xfd, 0x0,
    0x0, 0xef, 0x90, 0x1f, 0xf7, 0x0, 0x0, 0x9f,
    0xe0, 0x5f, 0xf2, 0x0, 0x0, 0x3f, 0xf3, 0xaf,
    0xc0, 0x0, 0x0, 0xe, 0xf8, 0xff, 0x60, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x10, 0x0, 0x0, 0x3,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf5,
    0x0, 0x0,

    /* U+0077 "w" */
    0x6f, 0xf3, 0x0, 0xf, 0xfd, 0x0, 0x7, 0xff,
    0x21, 0xff, 0x70, 0x4, 0xff, 0xf1, 0x0, 0xbf,
    0xd0, 0xd, 0xfb, 0x0, 0x8f, 0xff, 0x50, 0xf,
    0xf9, 0x0, 0x8f, 0xf0, 0xc, 0xfe, 0xf9, 0x3,
    0xff, 0x40, 0x4, 0xff, 0x30, 0xff, 0x5f, 0xd0,
    0x7f, 0xf0, 0x0, 0xf, 0xf7, 0x5f, 0xc0, 0xff,
    0x1b, 0xfb, 0x0, 0x0, 0xbf, 0xb9, 0xf7, 0xb,
    0xf5, 0xff, 0x70, 0x0, 0x6, 0xff, 0xdf, 0x30,
    0x7f, 0xcf, 0xf2, 0x0, 0x0, 0x2f, 0xff, 0xe0,
    0x3, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xdf, 0xfa,
    0x0, 0xe, 0xff, 0x90, 0x0, 0x0, 0x9, 0xff,
    0x60, 0x0, 0xaf, 0xf5, 0x0, 0x0,

    /* U+0078 "x" */
    0x1e, 0xfb, 0x0, 0x6, 0xff, 0x40, 0x6f, 0xf3,
    0x0, 0xef, 0xa0, 0x0, 0xdf, 0xc0, 0x8f, 0xf2,
    0x0, 0x3, 0xff, 0x6f, 0xf7, 0x0, 0x0, 0xa,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x70,
    0x0, 0x0, 0xa, 0xff, 0xfd, 0x0, 0x0, 0x4,
    0xff, 0x8f, 0xf8, 0x0, 0x0, 0xdf, 0xc0, 0x8f,
    0xf2, 0x0, 0x7f, 0xf3, 0x0, 0xef, 0xb0, 0x2f,
    0xfa, 0x0, 0x5, 0xff, 0x50,

    /* U+0079 "y" */
    0x5f, 0xf5, 0x0, 0x0, 0xdf, 0xe0, 0xf, 0xfa,
    0x0, 0x2, 0xff, 0x80, 0xa, 0xfe, 0x0, 0x6,
    0xff, 0x20, 0x4, 0xff, 0x40, 0xb, 0xfd, 0x0,
    0x0, 0xef, 0x80, 0x1f, 0xf7, 0x0, 0x0, 0x9f,
    0xd0, 0x5f, 0xf1, 0x0, 0x0, 0x3f, 0xf2, 0xaf,
    0xb0, 0x0, 0x0, 0xe, 0xf8, 0xff, 0x60, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0,
    0x0, 0x25, 0xff, 0x90, 0x0, 0x0, 0x6, 0xff,
    0xfe, 0x10, 0x0, 0x0, 0x8, 0xff, 0xb2, 0x0,
    0x0, 0x0,

    /* U+007A "z" */
    0xaf, 0xff, 0xff, 0xff, 0xf0, 0xaf, 0xff, 0xff,
    0xff, 0xe0, 0x12, 0x22, 0x29, 0xff, 0x60, 0x0,
    0x0, 0x4f, 0xfa, 0x0, 0x0, 0x1, 0xef, 0xd0,
    0x0, 0x0, 0xc, 0xff, 0x20, 0x0, 0x0, 0x9f,
    0xf5, 0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x0,
    0x2f, 0xfe, 0x32, 0x22, 0x20, 0xcf, 0xff, 0xff,
    0xff, 0xf2, 0xdf, 0xff, 0xff, 0xff, 0xf2,

    /* U+007B "{" */
    0x0, 0x0, 0x8d, 0xf5, 0x0, 0xa, 0xff, 0xf5,
    0x0, 0xf, 0xfb, 0x10, 0x0, 0x2f, 0xf4, 0x0,
    0x0, 0x2f, 0xf4, 0x0, 0x0, 0x2f, 0xf4, 0x0,
    0x0, 0x5f, 0xf3, 0x0, 0x38, 0xef, 0xb0, 0x0,
    0x7f, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0x90, 0x0,
    0x1, 0xbf, 0xf2, 0x0, 0x0, 0x3f, 0xf4, 0x0,
    0x0, 0x2f, 0xf4, 0x0, 0x0, 0x2f, 0xf4, 0x0,
    0x0, 0x2f, 0xf5, 0x0, 0x0, 0xf, 0xfc, 0x10,
    0x0, 0x9, 0xff, 0xf5, 0x0, 0x0, 0x7c, 0xe5,

    /* U+007C "|" */
    0x9f, 0xc9, 0xfc, 0x9f, 0xc9, 0xfc, 0x9f, 0xc9,
    0xfc, 0x9f, 0xc9, 0xfc, 0x9f, 0xc9, 0xfc, 0x9f,
    0xc9, 0xfc, 0x9f, 0xc9, 0xfc, 0x9f, 0xc9, 0xfc,
    0x9f, 0xc9, 0xfc, 0x9f, 0xc9, 0xfc, 0x9f, 0xc9,
    0xfc, 0x9f, 0xc9, 0xfc,

    /* U+007D "}" */
    0x7f, 0xc7, 0x0, 0x0, 0x7f, 0xff, 0x80, 0x0,
    0x1, 0xcf, 0xe0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x5f, 0xf3, 0x0, 0x0, 0xd, 0xfd, 0x81,
    0x0, 0x0, 0x8f, 0xf4, 0x0, 0xb, 0xff, 0xf4,
    0x0, 0x4f, 0xf9, 0x10, 0x0, 0x6f, 0xf1, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x2, 0xdf, 0xe0, 0x0,
    0x7f, 0xff, 0x70, 0x0, 0x7e, 0xc6, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x80, 0x2, 0xdb, 0x1f, 0xff, 0xff, 0xb3, 0xaf,
    0xc5, 0xfd, 0x13, 0xef, 0xff, 0xf6, 0x4a, 0x60,
    0x1, 0xaf, 0xe8, 0x0,

    /* U+0621 "ء" */
    0x0, 0x4a, 0xa9, 0x0, 0x8f, 0xff, 0xf0, 0x1f,
    0xe4, 0x2, 0x4, 0xf8, 0x0, 0x0, 0x4f, 0xb0,
    0x0, 0x0, 0xdf, 0xe9, 0x9b, 0x0, 0x9f, 0xff,
    0xb4, 0xdf, 0xfd, 0x60, 0x6e, 0x93, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0623 "أ" */
    0x5e, 0xe2, 0xd2, 0x10, 0xc7, 0x32, 0xbf, 0xe4,
    0x52, 0x0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,

    /* U+0625 "إ" */
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x0, 0x0,
    0x5e, 0xe2, 0xd2, 0x0, 0xb8, 0x42, 0xcf, 0xc3,
    0x30, 0x0,

    /* U+0627 "ا" */
    0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1,
    0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f,
    0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb0,

    /* U+0628 "ب" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48,
    0x15, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf3, 0xaf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x3a, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x6f, 0xd4, 0x0, 0x0, 0x0, 0x36,
    0xbf, 0xf4, 0x0, 0xaf, 0xff, 0xcb, 0xce, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x39, 0xdf, 0xff, 0xdb,
    0x85, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+062D "ح" */
    0x3, 0x68, 0x99, 0x98, 0x52, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x57, 0x32, 0x9f, 0xfc,
    0x86, 0x0, 0x0, 0x1c, 0xfb, 0x20, 0x0, 0x0,
    0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x5, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x60, 0x0, 0x0, 0x28,
    0x0, 0x4f, 0xff, 0xba, 0xad, 0xfe, 0x0, 0x1,
    0x7b, 0xef, 0xfd, 0x93,

    /* U+0631 "ر" */
    0x0, 0x0, 0x0, 0x6, 0x70, 0x0, 0x0, 0x0,
    0x8, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xf6, 0x0,
    0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x7,
    0xf5, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x0, 0x5f, 0xc0, 0x0, 0x0, 0x5, 0xff, 0x40,
    0x0, 0x5, 0xbf, 0xf6, 0x0, 0xae, 0xff, 0xfc,
    0x30, 0x0, 0xce, 0xc8, 0x30, 0x0, 0x0,

    /* U+0633 "س" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x2f, 0xb0, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x3f, 0xb0, 0x1, 0xfb, 0x6, 0x70, 0x0, 0x0,
    0x5, 0xf8, 0x0, 0x6f, 0xe0, 0x2, 0xfa, 0x2f,
    0xa0, 0x0, 0x0, 0x3, 0xff, 0x41, 0xdf, 0xf9,
    0x19, 0xf7, 0x7f, 0x50, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xe1, 0xaf, 0x20, 0x0,
    0x0, 0x6, 0xfb, 0xef, 0xb0, 0x3c, 0xfc, 0x20,
    0xbf, 0x10, 0x0, 0x0, 0xc, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x7f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xc4,
    0x10, 0x39, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xcc, 0xb6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0636 "ض" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xcf, 0xfb, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xfc, 0xcf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x20, 0x2e, 0xf9, 0x10,
    0x5, 0xf9, 0x4, 0x50, 0x0, 0x0, 0xc, 0xe2,
    0xef, 0x50, 0x0, 0x5, 0xfa, 0x2f, 0xb0, 0x0,
    0x0, 0xa, 0xff, 0xf5, 0x0, 0x2, 0x9f, 0xf4,
    0x7f, 0x50, 0x0, 0x0, 0x9, 0xff, 0xfc, 0xcd,
    0xff, 0xff, 0x60, 0xaf, 0x20, 0x0, 0x0, 0xa,
    0xfa, 0xff, 0xff, 0xeb, 0x71, 0x0, 0xbf, 0x10,
    0x0, 0x0, 0xd, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x30, 0x0, 0x0, 0x5f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xc4, 0x10, 0x28,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5a, 0xcc, 0xb7, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0637 "ط" */
    0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x3, 0xae, 0xfd, 0x80, 0x0, 0x0, 0xf, 0xc0,
    0x8, 0xff, 0xdb, 0xef, 0xb0, 0x0, 0x0, 0xfc,
    0xb, 0xfe, 0x40, 0x0, 0xdf, 0x10, 0x0, 0xf,
    0xca, 0xfb, 0x10, 0x0, 0xd, 0xf1, 0x0, 0x0,
    0xff, 0xfc, 0x0, 0x0, 0x6d, 0xfc, 0x7, 0xcc,
    0xcf, 0xff, 0xdc, 0xde, 0xff, 0xfb, 0x10, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x94, 0x0, 0x0,

    /* U+0638 "ظ" */
    0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x7, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x25,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x3, 0xae, 0xfd, 0x80, 0x0, 0x0, 0xf, 0xc0,
    0x8, 0xff, 0xdb, 0xef, 0xb0, 0x0, 0x0, 0xfc,
    0xb, 0xfe, 0x40, 0x0, 0xdf, 0x10, 0x0, 0xf,
    0xca, 0xfb, 0x10, 0x0, 0xd, 0xf1, 0x0, 0x0,
    0xff, 0xfc, 0x0, 0x0, 0x6d, 0xfc, 0x7, 0xcc,
    0xcf, 0xff, 0xdc, 0xde, 0xff, 0xfb, 0x10, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x94, 0x0, 0x0,

    /* U+0639 "ع" */
    0x0, 0x7, 0xce, 0xb0, 0x0, 0x0, 0x1d, 0xff,
    0xc7, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xed, 0x0, 0x0, 0x1, 0x0, 0xd, 0xe3,
    0x26, 0xbe, 0xe0, 0x0, 0x5e, 0xff, 0xff, 0xd9,
    0x0, 0x1, 0xcf, 0xe7, 0x10, 0x0, 0x0, 0xdf,
    0x90, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xd3, 0x0, 0x0, 0x2, 0x60,
    0x8f, 0xfe, 0xba, 0xad, 0xfb, 0x0, 0x29, 0xce,
    0xfe, 0xd8, 0x20,

    /* U+0641 "ف" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8d, 0xc4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd1, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf8,
    0x0, 0xef, 0x0, 0x53, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xd1, 0x2f, 0xf0, 0x7f, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xa, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x58, 0x6d, 0xc0, 0xaf,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xf6,
    0x5, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x4, 0x9f,
    0xfb, 0x0, 0x8, 0xff, 0xfd, 0xcc, 0xcd, 0xef,
    0xff, 0xe7, 0x0, 0x0, 0x2, 0x9c, 0xef, 0xff,
    0xed, 0xa7, 0x40, 0x0, 0x0,

    /* U+0642 "ق" */
    0x0, 0x0, 0x0, 0x1, 0x10, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xc4, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x41, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xad, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x6f, 0xb1, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0x6f, 0x70, 0xf, 0xd0,
    0x0, 0x0, 0x0, 0x2f, 0xfa, 0xcf, 0xf0, 0x3c,
    0x60, 0x0, 0x5, 0xdf, 0xcd, 0xf0, 0x8f, 0x30,
    0x0, 0x0, 0x0, 0xd, 0xe0, 0xcf, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xb0, 0xed, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x60, 0xed, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x6f, 0xf3,
    0x0, 0x7f, 0xb0, 0x0, 0x3a, 0xff, 0x40, 0x0,
    0xc, 0xff, 0xef, 0xff, 0xd3, 0x0, 0x0, 0x0,
    0x9e, 0xfe, 0xb5, 0x0, 0x0, 0x0,

    /* U+0644 "ل" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb,
    0x3f, 0x90, 0x0, 0x0, 0x3, 0xfa, 0x8f, 0x40,
    0x0, 0x0, 0x8, 0xf7, 0x8f, 0x30, 0x0, 0x0,
    0x2e, 0xf2, 0x5f, 0xc1, 0x0, 0x6, 0xef, 0x90,
    0xc, 0xff, 0xcc, 0xff, 0xf9, 0x0, 0x0, 0x7c,
    0xff, 0xc8, 0x20, 0x0,

    /* U+0645 "م" */
    0x0, 0x0, 0x4d, 0xfb, 0x30, 0x0, 0x4, 0xff,
    0xff, 0xf4, 0x0, 0xd, 0xf6, 0x6, 0xfb, 0x0,
    0xf, 0xe0, 0x0, 0xfe, 0x6, 0xef, 0xf4, 0x5,
    0xfc, 0x3f, 0xfe, 0xff, 0xff, 0xf5, 0x8f, 0x90,
    0x38, 0xa9, 0x20, 0x9f, 0x50, 0x0, 0x0, 0x0,
    0xaf, 0x50, 0x0, 0x0, 0x0, 0xaf, 0x50, 0x0,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x0, 0xaf,
    0x50, 0x0, 0x0, 0x0,

    /* U+0646 "ن" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25,
    0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x2, 0xb6, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x7f,
    0x60, 0x0, 0x0, 0x0, 0xaf, 0x28, 0xf4, 0x0,
    0x0, 0x0, 0xb, 0xf1, 0x8f, 0x40, 0x0, 0x0,
    0x0, 0xff, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x8f,
    0x80, 0x1f, 0xf5, 0x0, 0x1, 0x8f, 0xe1, 0x0,
    0x5f, 0xff, 0xde, 0xff, 0xe3, 0x0, 0x0, 0x29,
    0xdf, 0xec, 0x71, 0x0, 0x0,

    /* U+0648 "و" */
    0x0, 0x1, 0xae, 0xe7, 0x0, 0x0, 0xd, 0xff,
    0xff, 0x60, 0x0, 0x3f, 0xc0, 0x2f, 0xe0, 0x0,
    0x4f, 0xc0, 0xb, 0xf0, 0x0, 0xd, 0xff, 0xce,
    0xf1, 0x0, 0x1, 0x9d, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0xcf, 0x70,
    0x0, 0x1, 0x5d, 0xfc, 0x0, 0xad, 0xef, 0xff,
    0xa0, 0x0, 0xdf, 0xec, 0x83, 0x0, 0x0,

    /* U+0649 "ى" */
    0x0, 0x0, 0x0, 0x1, 0x68, 0x73, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0xef, 0x50, 0x1c, 0xf2, 0x0, 0x0, 0x0,
    0xee, 0x20, 0x1, 0x20, 0x0, 0x0, 0x0, 0x7f,
    0xfb, 0x50, 0x0, 0x2b, 0x60, 0x0, 0x4, 0xbf,
    0xfc, 0x10, 0x8f, 0x30, 0x0, 0x0, 0x1, 0x9f,
    0x80, 0xbf, 0x10, 0x0, 0x0, 0x0, 0x5f, 0xb0,
    0x9f, 0x50, 0x0, 0x0, 0x5, 0xef, 0x60, 0x3f,
    0xfa, 0x66, 0x9b, 0xff, 0xf9, 0x0, 0x4, 0xef,
    0xff, 0xff, 0xfb, 0x40, 0x0, 0x0, 0x5, 0x77,
    0x63, 0x0, 0x0, 0x0,

    /* U+064A "ي" */
    0x0, 0x0, 0x0, 0x7, 0xdf, 0xea, 0x20, 0x0,
    0x0, 0x0, 0xaf, 0xe9, 0xaf, 0xe0, 0x0, 0x0,
    0x0, 0xfe, 0x0, 0x6, 0xb3, 0x0, 0x0, 0x0,
    0xdf, 0x93, 0x0, 0x0, 0x2, 0x10, 0x0, 0x2d,
    0xff, 0xc4, 0x0, 0x5f, 0x60, 0x0, 0x0, 0x4a,
    0xff, 0x40, 0xaf, 0x20, 0x0, 0x0, 0x0, 0x4f,
    0xa0, 0xaf, 0x10, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0x7f, 0xb1, 0x0, 0x2, 0x6c, 0xff, 0x20, 0xc,
    0xff, 0xdd, 0xff, 0xff, 0xc3, 0x0, 0x0, 0x7e,
    0xff, 0xec, 0x83, 0x0, 0x0, 0x0, 0x0, 0x21,
    0x11, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf5, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x51, 0x34, 0x0,
    0x0, 0x0,

    /* U+3002 "。" */
    0x0, 0x12, 0x0, 0x6, 0x97, 0xa0, 0xb, 0x0,
    0x73, 0xb, 0x0, 0x82, 0x4, 0xba, 0x80, 0x0,
    0x0, 0x0,

    /* U+4E09 "三" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x60, 0x0, 0x0, 0x37, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x7b, 0xf8, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc8, 0x0, 0x0,
    0x0, 0x1, 0xa8, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x0, 0x7, 0x87, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0xbe, 0xa0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4E2D "中" */
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x11, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x61,
    0x3e, 0x77, 0x77, 0x7e, 0xa7, 0x77, 0x77, 0xf8,
    0x3e, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0xe2,
    0x2e, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0xe2,
    0x2e, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0xe2,
    0x2e, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0xe2,
    0x2e, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0xe2,
    0x3f, 0x77, 0x77, 0x7e, 0xa7, 0x77, 0x77, 0xf3,
    0x39, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0xa1,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4EAE "亮" */
    0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0x80, 0x0, 0x0, 0x32, 0x0, 0x2, 0x88,
    0x77, 0x77, 0x79, 0x87, 0x77, 0x77, 0xed, 0x20,
    0x0, 0x20, 0x2, 0x0, 0x0, 0x0, 0x1, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x97, 0x77, 0x77,
    0x7b, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xd, 0x40,
    0x0, 0x0, 0x7, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x40, 0x0, 0x0, 0x7, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x97, 0x77, 0x77, 0x7b, 0x80,
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x0, 0x0, 0xc7, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x9f, 0x60, 0x7, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0,
    0xa, 0x50, 0x0, 0xc8, 0x77, 0x77, 0xe3, 0x1,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xd4, 0x0, 0x2,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf3,
    0x0, 0x2, 0xe0, 0x0, 0x0, 0x50, 0x0, 0x0,
    0x2, 0xf0, 0x0, 0x2, 0xe0, 0x0, 0x1, 0x80,
    0x0, 0x0, 0xa, 0x80, 0x0, 0x2, 0xf0, 0x0,
    0x5, 0xc0, 0x0, 0x2, 0xa8, 0x0, 0x0, 0x0,
    0xce, 0xdd, 0xdf, 0xb0, 0x5, 0x65, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4ECA "今" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf3, 0x54, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x70, 0xa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xab, 0x0, 0x1,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xd1,
    0x10, 0x0, 0x3e, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0x10, 0x78, 0x0, 0x3, 0xec, 0x30, 0x0,
    0x0, 0x7, 0xb0, 0x0, 0xb, 0xc0, 0x0, 0x1c,
    0xfd, 0x80, 0x0, 0x86, 0x0, 0x0, 0x1, 0xf1,
    0x0, 0x0, 0x6d, 0x30, 0x7, 0x10, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x77, 0x77, 0x77, 0x77, 0x77, 0xd8, 0x0, 0x0,
    0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x5, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+4F53 "体" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x80, 0x0, 0x3,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x30,
    0x0, 0x2, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8b, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe3, 0x0, 0x0, 0x2, 0xe0, 0x0,
    0x6, 0x0, 0x0, 0x5, 0xc0, 0x2a, 0x87, 0x8b,
    0xf8, 0x77, 0x9d, 0x60, 0x0, 0xc, 0xd3, 0x0,
    0x0, 0x7e, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x4a,
    0xf2, 0x0, 0x0, 0xe8, 0xe4, 0x40, 0x0, 0x0,
    0x0, 0xb1, 0xf1, 0x0, 0x5, 0xd2, 0xe0, 0xa0,
    0x0, 0x0, 0x7, 0x30, 0xf1, 0x0, 0xd, 0x52,
    0xe0, 0x83, 0x0, 0x0, 0x4, 0x0, 0xf1, 0x0,
    0x6a, 0x2, 0xe0, 0x1d, 0x0, 0x0, 0x0, 0x0,
    0xf1, 0x1, 0xd1, 0x2, 0xe0, 0xa, 0x90, 0x0,
    0x0, 0x0, 0xf1, 0xa, 0x30, 0x2, 0xe0, 0x1,
    0xf9, 0x0, 0x0, 0x0, 0xf1, 0x65, 0x0, 0x2,
    0xe0, 0x2a, 0x7f, 0xc1, 0x0, 0x0, 0xf5, 0x30,
    0x79, 0x78, 0xf7, 0x77, 0x46, 0x10, 0x0, 0x0,
    0xf1, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf1, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf1, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa0, 0x0,
    0x0, 0x3, 0x80, 0x0, 0x0, 0x0,

    /* U+5149 "光" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0xc, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x74, 0x0, 0xc, 0x50, 0x0, 0xc4, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x60, 0xc, 0x50, 0x4, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xf3, 0xc, 0x50,
    0xb, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd6,
    0xc, 0x50, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x41, 0xc, 0x50, 0xa1, 0x0, 0x13, 0x0,
    0x4, 0x77, 0x77, 0x77, 0x7e, 0xa8, 0xa7, 0x77,
    0xdf, 0x30, 0x0, 0x20, 0x0, 0xf, 0x20, 0xf,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x0, 0xf, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0x0, 0xf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6b, 0x0, 0xf, 0x10, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0xb6, 0x0, 0xf,
    0x10, 0x0, 0x7, 0x0, 0x0, 0x0, 0x2, 0xe1,
    0x0, 0xf, 0x10, 0x0, 0x8, 0x0, 0x0, 0x0,
    0xc, 0x60, 0x0, 0xf, 0x10, 0x0, 0xa, 0x20,
    0x0, 0x1, 0xb6, 0x0, 0x0, 0xe, 0x50, 0x0,
    0xd, 0xb0, 0x0, 0x59, 0x20, 0x0, 0x0, 0x6,
    0xef, 0xff, 0xfe, 0x60, 0x6, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5747 "均" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x70, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf3, 0x0, 0x0,
    0xa9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x20,
    0x0, 0xf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe2, 0x0, 0x5, 0xe7, 0x77, 0x77, 0x8e, 0x30,
    0x0, 0xe, 0x21, 0x0, 0xb4, 0x0, 0x0, 0x3,
    0xe0, 0x6, 0x77, 0xf8, 0xda, 0x3a, 0x0, 0x0,
    0x0, 0x3e, 0x0, 0x10, 0xe, 0x20, 0xa, 0x11,
    0x80, 0x0, 0x4, 0xd0, 0x0, 0x0, 0xe2, 0x4,
    0x40, 0x7, 0xd1, 0x0, 0x4d, 0x0, 0x0, 0xe,
    0x20, 0x0, 0x0, 0xe, 0xa0, 0x5, 0xc0, 0x0,
    0x0, 0xe2, 0x0, 0x0, 0x0, 0x87, 0x0, 0x5c,
    0x0, 0x0, 0xe, 0x20, 0x0, 0x0, 0x0, 0x6,
    0x26, 0xb0, 0x0, 0x0, 0xe2, 0x17, 0x40, 0x0,
    0x69, 0x10, 0x7b, 0x0, 0x0, 0x1f, 0xc9, 0x20,
    0x7, 0xc5, 0x0, 0x8, 0xa0, 0x7, 0xcf, 0x91,
    0x0, 0x9f, 0xb1, 0x0, 0x0, 0xa8, 0x0, 0xbb,
    0x20, 0x0, 0x9, 0x80, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x52, 0x15,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xaf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x75, 0x0, 0x0,

    /* U+5927 "大" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x4d,
    0x10, 0x48, 0x77, 0x77, 0x77, 0xea, 0x77, 0x77,
    0x79, 0x97, 0x0, 0x0, 0x0, 0x0, 0xf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4e, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xb1, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc6, 0xa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x10, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x80,
    0x0, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xd0, 0x0, 0x0, 0xc7, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xd2, 0x0, 0x0, 0x2, 0xea, 0x10, 0x0,
    0x0, 0x7, 0xc1, 0x0, 0x0, 0x0, 0x2, 0xef,
    0x82, 0x0, 0x29, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xce, 0x70, 0x66, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+5929 "天" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x60,
    0x0, 0x0, 0x5, 0x87, 0x77, 0x78, 0x77, 0x77,
    0xcf, 0x70, 0x0, 0x0, 0x1, 0x0, 0x0, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0x1b, 0x30, 0x4, 0xa8, 0x77, 0x77, 0x9f, 0x87,
    0x77, 0x7a, 0xcb, 0x10, 0x0, 0x0, 0x0, 0x7,
    0xa4, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb6, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0x10, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xb0, 0x3, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xe2, 0x0, 0xb,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb7, 0x0,
    0x0, 0x2e, 0x40, 0x0, 0x0, 0x0, 0x0, 0x99,
    0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0, 0x0,
    0x98, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd6, 0x10,
    0x1, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xf8, 0x4, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0x0,

    /* U+5E73 "平" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x60, 0x0, 0x5, 0xa8, 0x77, 0x77, 0xb9, 0x77,
    0x77, 0xbb, 0x20, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x50, 0x0, 0x30, 0x0, 0x0, 0x0, 0x9, 0x10,
    0x0, 0xd5, 0x0, 0xc, 0xc0, 0x0, 0x0, 0x0,
    0x3e, 0x30, 0xd, 0x50, 0x3, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0xbe, 0x0, 0xd5, 0x0, 0xb3, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xf0, 0xd, 0x50, 0x54,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0xd5,
    0x4, 0x0, 0x3, 0x90, 0x2a, 0x87, 0x77, 0x77,
    0x7e, 0xa7, 0x77, 0x77, 0xbb, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x61, 0x0,
    0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0xc6, 0x0, 0x0, 0x65, 0x0, 0x0, 0xe9, 0x77,
    0x77, 0x79, 0x77, 0x77, 0x7b, 0xb2, 0x0, 0xd,
    0x40, 0x0, 0xb2, 0x0, 0x9, 0x40, 0x0, 0x0,
    0x0, 0xd4, 0x0, 0xf, 0x10, 0x0, 0xd4, 0x3,
    0x0, 0x0, 0xd, 0x77, 0x77, 0xf8, 0x77, 0x7e,
    0x98, 0xfa, 0x0, 0x0, 0xd4, 0x10, 0xf, 0x10,
    0x0, 0xc3, 0x0, 0x0, 0x0, 0xe, 0x30, 0x0,
    0xf1, 0x0, 0xc, 0x30, 0x0, 0x0, 0x0, 0xf2,
    0x0, 0xf, 0x87, 0x77, 0xe4, 0x0, 0x0, 0x0,
    0xf, 0x10, 0x0, 0x70, 0x0, 0x4, 0x10, 0x0,
    0x0, 0x2, 0xe0, 0x6, 0x77, 0x77, 0x77, 0x7e,
    0x50, 0x0, 0x0, 0x3b, 0x0, 0x0, 0x60, 0x0,
    0x9, 0xd1, 0x0, 0x0, 0x6, 0x80, 0x0, 0x0,
    0x90, 0x5, 0xe2, 0x0, 0x0, 0x0, 0x93, 0x0,
    0x0, 0x3, 0xa4, 0xe3, 0x0, 0x0, 0x0, 0xb,
    0x0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x0,
    0x3, 0x60, 0x0, 0x0, 0x2a, 0xb7, 0xe9, 0x40,
    0x0, 0x0, 0x70, 0x0, 0x4, 0xa9, 0x30, 0x1,
    0x9f, 0xfc, 0xa4, 0x13, 0x3, 0x67, 0x40, 0x0,
    0x0, 0x0, 0x5, 0x94, 0x0,

    /* U+5F3A "强" */
    0x0, 0x0, 0x3, 0x0, 0x33, 0x11, 0x11, 0x19,
    0x0, 0x4, 0x87, 0x77, 0xf6, 0x5, 0xd6, 0x66,
    0x67, 0xf2, 0x0, 0x0, 0x0, 0xe, 0x10, 0x5b,
    0x0, 0x0, 0x1e, 0x0, 0x0, 0x0, 0x0, 0xe1,
    0x5, 0xb0, 0x0, 0x1, 0xe0, 0x0, 0x0, 0x0,
    0xe, 0x10, 0x5d, 0x77, 0x87, 0x7f, 0x0, 0x0,
    0x98, 0x77, 0xf2, 0x3, 0x50, 0x1f, 0x20, 0x40,
    0x0, 0xb, 0x50, 0xb, 0x10, 0x0, 0x1, 0xe0,
    0x0, 0x0, 0x0, 0xc4, 0x0, 0x0, 0x15, 0x0,
    0x1e, 0x0, 0x17, 0x0, 0xe, 0x20, 0x0, 0x1,
    0xf7, 0x78, 0xf7, 0x79, 0xe1, 0x3, 0xf8, 0x77,
    0xe3, 0x1e, 0x0, 0x1e, 0x0, 0x3c, 0x0, 0x6,
    0x0, 0x2f, 0x1, 0xe0, 0x1, 0xe0, 0x3, 0xc0,
    0x0, 0x0, 0x3, 0xe0, 0x1f, 0x77, 0x8f, 0x77,
    0x9d, 0x0, 0x0, 0x0, 0x5c, 0x2, 0xc0, 0x1,
    0xe0, 0x2, 0x40, 0x0, 0x0, 0x8, 0x90, 0x0,
    0x0, 0x1e, 0x0, 0x70, 0x0, 0x0, 0x0, 0xb6,
    0x0, 0x0, 0x1, 0xe0, 0x4, 0xc1, 0x0, 0x41,
    0x2f, 0x20, 0x0, 0x13, 0x5f, 0x78, 0x9d, 0xb0,
    0x1, 0xcf, 0x90, 0x8f, 0xfd, 0xa8, 0x63, 0x10,
    0x3e, 0x0, 0x3, 0x60, 0x1, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x30,

    /* U+6587 "文" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x8d, 0x10,
    0x2a, 0x87, 0x7b, 0x77, 0x77, 0x79, 0xf7, 0x77,
    0x75, 0x0, 0x0, 0x0, 0x80, 0x0, 0x0, 0x6c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x20, 0x0,
    0xa, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x4e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x60, 0xb, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x3, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x79, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xed, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7d, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9a, 0x2, 0xdb, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xc5, 0x0, 0x0, 0x9f, 0xd8,
    0x31, 0x0, 0x0, 0x49, 0x70, 0x0, 0x0, 0x0,
    0x2a, 0xff, 0xe6, 0x4, 0x63, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x62, 0x0,

    /* U+65E5 "日" */
    0x40, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe, 0x97,
    0x77, 0x77, 0x77, 0x79, 0xf2, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xe, 0x40, 0x0, 0x0, 0x0,
    0x3, 0xd0, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0xd, 0x40, 0x0, 0x0, 0x0, 0x3, 0xd0, 0xd4,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xd, 0x97, 0x77,
    0x77, 0x77, 0x79, 0xd0, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0xd, 0x40, 0x0, 0x0, 0x0, 0x3,
    0xd0, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xd,
    0x40, 0x0, 0x0, 0x0, 0x3, 0xd0, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0xd, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd0, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0xe, 0x97, 0x77, 0x77, 0x77, 0x79, 0xe0,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x5, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x20,

    /* U+65F6 "时" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x70, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0xe, 0x30, 0x0, 0x3b, 0x77, 0x7e, 0x60, 0x0,
    0x0, 0xe, 0x30, 0x0, 0x3e, 0x0, 0xe, 0x30,
    0x0, 0x0, 0xe, 0x30, 0x20, 0x3e, 0x0, 0xe,
    0x57, 0x77, 0x77, 0x7f, 0x9a, 0xf5, 0x2e, 0x0,
    0xe, 0x22, 0x10, 0x0, 0xe, 0x30, 0x0, 0x2e,
    0x0, 0xe, 0x20, 0x0, 0x0, 0xe, 0x30, 0x0,
    0x2f, 0x77, 0x7f, 0x21, 0x70, 0x0, 0xe, 0x30,
    0x0, 0x2e, 0x0, 0xe, 0x20, 0x8a, 0x0, 0xe,
    0x30, 0x0, 0x2e, 0x0, 0xe, 0x20, 0x1f, 0x80,
    0xe, 0x30, 0x0, 0x2e, 0x0, 0xe, 0x20, 0xa,
    0x80, 0xe, 0x30, 0x0, 0x2e, 0x0, 0xe, 0x20,
    0x1, 0x0, 0xe, 0x30, 0x0, 0x3e, 0x0, 0xe,
    0x20, 0x0, 0x0, 0xe, 0x30, 0x0, 0x3f, 0x77,
    0x7f, 0x30, 0x0, 0x0, 0xe, 0x30, 0x0, 0x3e,
    0x0, 0xd, 0x20, 0x0, 0x0, 0xe, 0x30, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x31, 0x2f, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x73, 0x0, 0x0,

    /* U+6700 "最" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0x77, 0x77, 0x77, 0x77,
    0xda, 0x0, 0x0, 0x0, 0x1, 0xf1, 0x0, 0x0,
    0x0, 0xc, 0x50, 0x0, 0x0, 0x0, 0x1f, 0x87,
    0x77, 0x77, 0x77, 0xe5, 0x0, 0x0, 0x0, 0x1,
    0xf1, 0x0, 0x0, 0x0, 0xc, 0x50, 0x0, 0x0,
    0x0, 0x1f, 0x10, 0x0, 0x0, 0x0, 0xc5, 0x0,
    0x0, 0x0, 0x1, 0xf8, 0x77, 0x77, 0x77, 0x7e,
    0x50, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x8d, 0x20, 0x58, 0x8f, 0x77, 0x79,
    0xe7, 0x77, 0x77, 0x77, 0x76, 0x0, 0x2, 0xe0,
    0x0, 0x3d, 0x11, 0x11, 0x12, 0xa2, 0x0, 0x0,
    0x2f, 0x77, 0x79, 0xd4, 0x96, 0x55, 0x8f, 0x50,
    0x0, 0x2, 0xe0, 0x0, 0x3d, 0x2, 0x50, 0xa,
    0x90, 0x0, 0x0, 0x2f, 0x77, 0x79, 0xd0, 0x9,
    0x2, 0xf1, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x3d,
    0x0, 0x73, 0xb7, 0x0, 0x0, 0x0, 0x2e, 0x0,
    0x5, 0xe7, 0x50, 0xdc, 0x0, 0x0, 0x0, 0x37,
    0xfb, 0xda, 0x8d, 0x0, 0x1d, 0xd2, 0x0, 0x0,
    0x6f, 0xc7, 0x30, 0x3, 0xd0, 0x1b, 0x26, 0xf7,
    0x10, 0x0, 0x30, 0x0, 0x0, 0x3d, 0x19, 0x10,
    0x4, 0xef, 0x91, 0x0, 0x0, 0x0, 0x2, 0x95,
    0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6708 "月" */
    0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x8, 0x0,
    0x0, 0xd, 0xa7, 0x77, 0x77, 0x78, 0xf5, 0x0,
    0x0, 0xc5, 0x0, 0x0, 0x0, 0x1f, 0x0, 0x0,
    0xc, 0x50, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0xc5, 0x0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0xc,
    0xa7, 0x77, 0x77, 0x78, 0xf0, 0x0, 0x0, 0xc5,
    0x0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0xc, 0x40,
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0xd4, 0x0,
    0x0, 0x0, 0x1f, 0x0, 0x0, 0xe, 0x97, 0x77,
    0x77, 0x78, 0xf0, 0x0, 0x0, 0xf3, 0x0, 0x0,
    0x0, 0x1f, 0x0, 0x0, 0x2f, 0x0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0x0,
    0x1f, 0x0, 0x0, 0xb6, 0x0, 0x0, 0x0, 0x1,
    0xf0, 0x0, 0x3d, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x0, 0xb, 0x20, 0x0, 0x0, 0x45, 0x47, 0xf0,
    0x9, 0x20, 0x0, 0x0, 0x0, 0x3b, 0xfa, 0x5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x0,

    /* U+6A19 "標" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0x0, 0x0, 0x0, 0xf0, 0x6,
    0x97, 0x7e, 0x77, 0xe7, 0x77, 0x20, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0xd, 0x0, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x61, 0x84, 0x4e, 0x44, 0xe4,
    0x95, 0x0, 0x8, 0x78, 0xf7, 0x96, 0xe4, 0x3e,
    0x33, 0xe3, 0xc6, 0x0, 0x0, 0x5, 0xf0, 0x0,
    0xd1, 0xd, 0x0, 0xd0, 0xb3, 0x0, 0x0, 0x9,
    0xf7, 0x0, 0xd1, 0xd, 0x0, 0xd0, 0xb3, 0x0,
    0x0, 0xe, 0xf6, 0xc0, 0xe8, 0x78, 0x77, 0x87,
    0xd3, 0x0, 0x0, 0x3b, 0xf0, 0xe3, 0x60, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0xa2, 0xf0, 0x40,
    0x78, 0x77, 0x77, 0x79, 0xa0, 0x0, 0x1, 0x90,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0,
    0x7, 0x10, 0xf0, 0x8, 0x97, 0x77, 0x88, 0x77,
    0x7d, 0x80, 0x5, 0x0, 0xf0, 0x0, 0x4, 0x0,
    0x89, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x2f, 0x70, 0x89, 0x29, 0x30, 0x0, 0x0, 0x0,
    0xf0, 0x1, 0xc5, 0x0, 0x89, 0x1, 0xd8, 0x0,
    0x0, 0x0, 0xf0, 0x1b, 0x30, 0x0, 0x89, 0x0,
    0x2f, 0x40, 0x0, 0x0, 0xf2, 0x71, 0x0, 0x69,
    0xe7, 0x0, 0x6, 0x20, 0x0, 0x0, 0xb1, 0x0,
    0x0, 0x5, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6C60 "池" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x2,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0, 0x0,
    0x0, 0x2, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf2, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x40, 0xd4, 0x1, 0xe0, 0x0,
    0x0, 0x0, 0x13, 0x0, 0x1, 0x50, 0xf1, 0x1,
    0xe0, 0x1, 0xb1, 0x0, 0x9, 0x90, 0x6, 0x10,
    0xe1, 0x1, 0xe6, 0x67, 0xf1, 0x0, 0x0, 0xe7,
    0x8, 0x0, 0xe7, 0x67, 0xe0, 0x2, 0xe0, 0x0,
    0x0, 0x77, 0x17, 0x76, 0xf1, 0x1, 0xe0, 0x2,
    0xd0, 0x0, 0x0, 0x0, 0x72, 0x0, 0xe1, 0x1,
    0xe0, 0x2, 0xd0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0xe1, 0x1, 0xe0, 0x3, 0xc0, 0x0, 0x0, 0x3,
    0x80, 0x0, 0xe1, 0x1, 0xe0, 0x4, 0xc0, 0x0,
    0x0, 0xb, 0x40, 0x0, 0xe1, 0x2, 0xe5, 0xbd,
    0x90, 0x0, 0x17, 0xdf, 0x0, 0x0, 0xe1, 0x2,
    0xf0, 0x2a, 0x14, 0x0, 0x0, 0x6c, 0x0, 0x0,
    0xe1, 0x2, 0xd0, 0x0, 0x7, 0x0, 0x0, 0x6b,
    0x0, 0x0, 0xe1, 0x0, 0x0, 0x0, 0x9, 0x30,
    0x0, 0x9c, 0x0, 0x0, 0xe4, 0x11, 0x11, 0x11,
    0x2d, 0xc0, 0x0, 0x8c, 0x0, 0x0, 0x6d, 0xee,
    0xee, 0xee, 0xdc, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+706F "灯" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0x0, 0x0, 0xc, 0x50,
    0x9, 0x97, 0x78, 0xc7, 0x79, 0xa3, 0x0, 0x0,
    0xc5, 0x8, 0x0, 0x0, 0x4e, 0x0, 0x0, 0x0,
    0x1, 0xc, 0x57, 0xe3, 0x0, 0x4, 0xe0, 0x0,
    0x0, 0x0, 0x80, 0xc8, 0xb1, 0x0, 0x0, 0x4e,
    0x0, 0x0, 0x0, 0x58, 0xb, 0x90, 0x0, 0x0,
    0x4, 0xe0, 0x0, 0x0, 0x1e, 0x50, 0xc4, 0x0,
    0x0, 0x0, 0x4e, 0x0, 0x0, 0x1, 0x60, 0xc,
    0x40, 0x0, 0x0, 0x4, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xd3, 0x0, 0x0, 0x0, 0x4e, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x10, 0x0, 0x0, 0x4, 0xe0,
    0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0,
    0x4e, 0x0, 0x0, 0x0, 0x0, 0x4a, 0x3d, 0x20,
    0x0, 0x4, 0xe0, 0x0, 0x0, 0x0, 0x9, 0x40,
    0x7d, 0x0, 0x0, 0x4e, 0x0, 0x0, 0x0, 0x1,
    0xb0, 0x0, 0xd0, 0x0, 0x4, 0xe0, 0x0, 0x0,
    0x0, 0x92, 0x0, 0x0, 0x2, 0x10, 0x6e, 0x0,
    0x0, 0x0, 0x54, 0x0, 0x0, 0x0, 0x28, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xb2, 0x0, 0x0, 0x0,

    /* U+7167 "照" */
    0x0, 0x30, 0x0, 0x61, 0x13, 0x33, 0x33, 0x33,
    0xa3, 0x0, 0xd, 0x97, 0x7f, 0x60, 0x65, 0xc9,
    0x33, 0x3f, 0x50, 0x0, 0xd3, 0x0, 0xe1, 0x0,
    0xe, 0x30, 0x1, 0xf0, 0x0, 0xd, 0x30, 0xe,
    0x10, 0x5, 0xc0, 0x0, 0x5c, 0x0, 0x0, 0xc3,
    0x0, 0xe1, 0x0, 0xd3, 0x7, 0x7d, 0x80, 0x0,
    0xc, 0x63, 0x3f, 0x10, 0xa6, 0x0, 0xa, 0xb0,
    0x0, 0x0, 0xc6, 0x33, 0xf1, 0x86, 0x30, 0x0,
    0x0, 0x80, 0x0, 0xc, 0x30, 0xe, 0x31, 0x1f,
    0x77, 0x77, 0x8f, 0x20, 0x0, 0xc3, 0x0, 0xe1,
    0x1, 0xf0, 0x0, 0x2, 0xe0, 0x0, 0xd, 0x30,
    0xe, 0x10, 0x1f, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0xd9, 0x77, 0xf2, 0x1, 0xf7, 0x77, 0x78, 0xf0,
    0x0, 0xd, 0x30, 0xd, 0x10, 0x1a, 0x0, 0x0,
    0x17, 0x0, 0x0, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x80, 0x5, 0x40,
    0x1, 0xa0, 0x0, 0x75, 0x0, 0x0, 0x5a, 0x0,
    0xe, 0x20, 0x8, 0xb0, 0x0, 0xe8, 0x0, 0x1d,
    0x80, 0x0, 0xba, 0x0, 0x2f, 0x40, 0x6, 0xf3,
    0xb, 0xf2, 0x0, 0x8, 0x70, 0x0, 0xb1, 0x0,
    0xf, 0x20, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x20,

    /* U+7259 "牙" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0x10, 0x0, 0x5a, 0x87, 0x77, 0x77,
    0x7b, 0x87, 0x79, 0xb8, 0x0, 0x0, 0x2, 0x30,
    0x0, 0x0, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7e, 0x10, 0x0, 0xe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x0, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0xe, 0x20,
    0x0, 0x34, 0x0, 0x1, 0xec, 0x77, 0x77, 0x77,
    0xf8, 0x77, 0x7f, 0xf4, 0x0, 0x5, 0x20, 0x0,
    0x5, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xe7, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xab, 0xe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7e, 0x10, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0x20, 0xe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0x20,
    0x0, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0x10, 0x0, 0xe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x88, 0x0, 0x0, 0x0, 0xe2, 0x0, 0x0, 0x0,
    0x2, 0x93, 0x0, 0x1, 0x32, 0x3f, 0x20, 0x0,
    0x0, 0x1, 0x40, 0x0, 0x0, 0x5, 0xcf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa2, 0x0, 0x0, 0x0, 0x0,

    /* U+7535 "电" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x60, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x81, 0x0,
    0xe9, 0x77, 0x78, 0xf7, 0x77, 0x78, 0xf5, 0x0,
    0xd4, 0x0, 0x1, 0xf0, 0x0, 0x2, 0xf0, 0x0,
    0xd4, 0x0, 0x1, 0xf0, 0x0, 0x2, 0xf0, 0x0,
    0xd9, 0x77, 0x78, 0xf7, 0x77, 0x78, 0xf0, 0x0,
    0xd4, 0x0, 0x1, 0xf0, 0x0, 0x2, 0xf0, 0x0,
    0xd4, 0x0, 0x1, 0xf0, 0x0, 0x2, 0xf0, 0x0,
    0xe4, 0x0, 0x1, 0xf0, 0x0, 0x2, 0xf0, 0x0,
    0xe9, 0x77, 0x78, 0xf7, 0x77, 0x78, 0xf0, 0x0,
    0xe3, 0x0, 0x1, 0xf0, 0x0, 0x1, 0x60, 0x50,
    0x10, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x70,
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0xa0,
    0x0, 0x0, 0x0, 0xf2, 0x0, 0x0, 0x1, 0xf6,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xe4,

    /* U+7684 "的" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xe3, 0x0, 0x0, 0x1e, 0x50, 0x0,
    0x0, 0x0, 0x5c, 0x0, 0x0, 0x5, 0xf3, 0x0,
    0x0, 0x0, 0x8, 0x20, 0x0, 0x0, 0xb9, 0x0,
    0x0, 0x0, 0x84, 0xa3, 0x3b, 0x40, 0x1f, 0x10,
    0x0, 0x56, 0xc, 0x73, 0x33, 0xe7, 0x8, 0xb7,
    0x77, 0x7d, 0xd0, 0xc4, 0x0, 0xd, 0x21, 0xd1,
    0x0, 0x0, 0xb7, 0xc, 0x40, 0x0, 0xd2, 0x93,
    0x0, 0x0, 0xb, 0x70, 0xc4, 0x0, 0xd, 0x55,
    0x3, 0x0, 0x0, 0xc6, 0xb, 0x40, 0x0, 0xd3,
    0x0, 0x95, 0x0, 0xc, 0x60, 0xba, 0x77, 0x7e,
    0x20, 0x2, 0xf2, 0x0, 0xc6, 0xb, 0x40, 0x0,
    0xd2, 0x0, 0xd, 0x70, 0xd, 0x50, 0xb4, 0x0,
    0xd, 0x20, 0x0, 0x62, 0x0, 0xd5, 0xb, 0x40,
    0x0, 0xd2, 0x0, 0x0, 0x0, 0xe, 0x40, 0xc4,
    0x0, 0xd, 0x20, 0x0, 0x0, 0x0, 0xf3, 0xc,
    0x40, 0x0, 0xd3, 0x0, 0x0, 0x0, 0xf, 0x20,
    0xca, 0x77, 0x7e, 0x30, 0x0, 0x64, 0x28, 0xe0,
    0xc, 0x40, 0x0, 0xc2, 0x0, 0x0, 0x7f, 0xf7,
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x65,
    0x0, 0x0,

    /* U+79BB "离" */
    0x0, 0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xd9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0x0,
    0x0, 0x5, 0xa0, 0x5a, 0x87, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x73, 0x0, 0x6, 0x71, 0x51,
    0x0, 0xa7, 0x8, 0x30, 0x0, 0x0, 0x8, 0x90,
    0x7, 0x9b, 0x91, 0xc, 0x70, 0x0, 0x0, 0x7,
    0x80, 0x1, 0xbc, 0xb2, 0xc, 0x40, 0x0, 0x0,
    0x7, 0x80, 0x68, 0x10, 0x8f, 0x1c, 0x40, 0x0,
    0x0, 0x7, 0x84, 0x10, 0x0, 0x7, 0xc, 0x50,
    0x0, 0x0, 0x8, 0xb7, 0x77, 0xba, 0x77, 0x7d,
    0x50, 0x0, 0x0, 0x10, 0x0, 0x0, 0xca, 0x0,
    0x2, 0x3, 0x0, 0x2, 0xe7, 0x77, 0x7a, 0xd7,
    0x77, 0x77, 0x7f, 0x50, 0x1, 0xe0, 0x0, 0x1c,
    0x10, 0x20, 0x0, 0xf, 0x10, 0x1, 0xe0, 0x1,
    0xa2, 0x0, 0x6a, 0x10, 0xf, 0x10, 0x1, 0xe0,
    0x3e, 0x97, 0x88, 0x7c, 0xe0, 0xf, 0x10, 0x1,
    0xe0, 0xb, 0x63, 0x10, 0x0, 0xc1, 0xf, 0x10,
    0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x31, 0x1f,
    0x0, 0x2, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x4a,
    0xfd, 0x0, 0x1, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x61, 0x0,

    /* U+7B80 "简" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0x90, 0x0, 0x0, 0x9a, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0x20, 0x7, 0x31, 0xe2,
    0x0, 0x2, 0x80, 0x0, 0xc8, 0xaa, 0x77, 0x6a,
    0x97, 0xb7, 0x77, 0x72, 0x7, 0x50, 0xd, 0x40,
    0x57, 0x0, 0x4c, 0x0, 0x0, 0x45, 0x5, 0x26,
    0x40, 0x50, 0x0, 0x8, 0x1, 0x0, 0x10, 0x1,
    0xd5, 0x7, 0x87, 0x77, 0x77, 0x7e, 0x40, 0x0,
    0xc3, 0x7b, 0x1, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x0, 0xf2, 0x12, 0x30, 0x0, 0x32, 0x0, 0x1f,
    0x0, 0x0, 0xf1, 0x0, 0xd8, 0x77, 0xdd, 0x0,
    0x1f, 0x0, 0x0, 0xf1, 0x0, 0xd2, 0x0, 0xb4,
    0x0, 0x1f, 0x0, 0x0, 0xf1, 0x0, 0xd2, 0x0,
    0xb4, 0x0, 0x1f, 0x0, 0x0, 0xf1, 0x0, 0xd8,
    0x77, 0xd4, 0x0, 0x1f, 0x0, 0x0, 0xf1, 0x0,
    0xd2, 0x0, 0xb5, 0x0, 0x1f, 0x0, 0x0, 0xf1,
    0x0, 0xd2, 0x0, 0xb6, 0x0, 0x1f, 0x0, 0x0,
    0xf1, 0x0, 0xe8, 0x77, 0xd7, 0x0, 0x1f, 0x0,
    0x0, 0xf1, 0x0, 0x70, 0x0, 0x10, 0x0, 0x1f,
    0x0, 0x0, 0xf1, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xdd, 0x0, 0x0, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xe3, 0x0, 0x0, 0x3b, 0x20,
    0x0, 0x0, 0x0, 0x8, 0xd1, 0x0, 0x0, 0x9,
    0xc0, 0x0, 0x0, 0x0, 0x1e, 0x20, 0x0, 0x0,
    0x3, 0x80, 0x5, 0xb0, 0x0, 0xa5, 0x0, 0x37,
    0x97, 0x7e, 0x87, 0x77, 0x72, 0x5, 0x90, 0x5,
    0xf3, 0x0, 0x6e, 0x40, 0x0, 0x0, 0x5d, 0x56,
    0x6e, 0x50, 0x2, 0xb1, 0x4, 0x10, 0x0, 0x3a,
    0x52, 0xc5, 0x0, 0x9, 0x0, 0x1, 0xb8, 0x0,
    0x0, 0x9, 0x60, 0x3, 0xc6, 0x66, 0x7a, 0x8e,
    0xc0, 0x0, 0x87, 0x0, 0x4, 0xea, 0xf5, 0x2f,
    0x2, 0xc0, 0x1a, 0xa4, 0x67, 0x61, 0x1, 0xf0,
    0xf, 0x0, 0x0, 0x2f, 0xe9, 0x40, 0x0, 0x2,
    0xf0, 0xf, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0,
    0x3, 0xd0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x32, 0x6, 0xa0, 0xf, 0x0, 0x0, 0x2, 0x58,
    0xa8, 0x30, 0xc, 0x60, 0xf, 0x0, 0x60, 0x7f,
    0xb4, 0x0, 0x0, 0x5e, 0x0, 0xf, 0x0, 0x71,
    0x5, 0x0, 0x0, 0x4, 0xd2, 0x0, 0xf, 0x20,
    0xa7, 0x0, 0x0, 0x1, 0x88, 0x0, 0x0, 0x9,
    0xee, 0xe6, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+7F6E "置" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x8a, 0xe0, 0x0, 0x0, 0xd, 0x40, 0xe,
    0x20, 0xc, 0x30, 0x6, 0xc0, 0x0, 0x0, 0xd,
    0x40, 0xe, 0x20, 0xc, 0x30, 0x6, 0xc0, 0x0,
    0x0, 0xe, 0x40, 0xe, 0x30, 0xc, 0x40, 0x6,
    0xd0, 0x0, 0x0, 0xe, 0x98, 0x88, 0x9c, 0x88,
    0x88, 0x8a, 0xb0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x4f, 0x20, 0x0, 0x5, 0x70, 0x0, 0x0, 0x7b,
    0x98, 0x88, 0xbd, 0x88, 0x88, 0x88, 0x82, 0x0,
    0x0, 0x0, 0x21, 0x0, 0x86, 0x0, 0x1, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0x88, 0x88, 0x88,
    0x89, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x6b, 0x0,
    0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0x88, 0x88, 0x88, 0x89, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x6b, 0x0, 0x0, 0x0, 0x2, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0x88, 0x88, 0x88,
    0x89, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0x0,
    0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0x88, 0x88, 0x88, 0x89, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x6b, 0x0, 0x0, 0x0, 0x2, 0xe0,
    0x3, 0x0, 0x28, 0x88, 0xbd, 0x88, 0x88, 0x88,
    0x89, 0xf8, 0xaf, 0xa0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x70, 0x0, 0xf,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd2, 0x0,
    0x0, 0xe0, 0x0, 0x97, 0x0, 0x59, 0x77, 0x7e,
    0x87, 0x77, 0x7f, 0x77, 0x8a, 0xa2, 0x0, 0x0,
    0x0, 0xd2, 0x0, 0x0, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x0, 0x6, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x30, 0xba, 0x0, 0xac, 0x0,
    0xb, 0x60, 0x0, 0x0, 0xc6, 0xb, 0x60, 0xf,
    0x97, 0x78, 0x88, 0x10, 0x0, 0xc, 0x40, 0xb6,
    0x5, 0xb0, 0x73, 0x0, 0x0, 0x0, 0x0, 0xc4,
    0xb, 0x60, 0xb1, 0x1, 0xcc, 0x20, 0x0, 0x0,
    0xc, 0x40, 0xb7, 0x53, 0x0, 0x0, 0xbb, 0x0,
    0x0, 0x0, 0x50, 0x9, 0x31, 0x0, 0x0, 0x2,
    0x50, 0x0, 0x0, 0x0, 0xc7, 0x77, 0x77, 0x77,
    0x77, 0xe6, 0x0, 0x0, 0x0, 0xf, 0x20, 0xa7,
    0x2, 0xe0, 0xe, 0x30, 0x0, 0x0, 0x0, 0xe2,
    0xa, 0x70, 0x2e, 0x0, 0xe2, 0x0, 0x0, 0x0,
    0xe, 0x20, 0xa7, 0x2, 0xe0, 0xe, 0x20, 0x0,
    0x0, 0x0, 0xe2, 0xa, 0x70, 0x2e, 0x0, 0xe2,
    0x17, 0x0, 0x89, 0x7d, 0x87, 0xba, 0x78, 0xd7,
    0x7d, 0x8b, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28,
    0x0, 0x0, 0x7, 0x50, 0x19, 0x87, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x7a, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0x50, 0x0, 0x0,
    0x3, 0xa7, 0x77, 0x77, 0x77, 0x77, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0x60, 0x0, 0x0, 0x5, 0x97, 0x77, 0x77, 0x77,
    0x77, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xc7, 0x77,
    0x77, 0x77, 0x7d, 0x80, 0x0, 0x0, 0x1, 0xf1,
    0x0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0, 0x1,
    0xf1, 0x0, 0x0, 0x0, 0xf, 0x30, 0x0, 0x0,
    0x1, 0xf1, 0x0, 0x0, 0x0, 0xf, 0x30, 0x0,
    0x0, 0x1, 0xf8, 0x77, 0x77, 0x77, 0x7f, 0x40,
    0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x0, 0x1, 0x70, 0x0, 0x0, 0x0,
    0x6, 0x0, 0x0,

    /* U+8A9E "語" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x41, 0x0, 0x0, 0x1, 0xf3, 0x0,
    0x3a, 0x88, 0xa7, 0x77, 0xc8, 0x0, 0x0, 0x0,
    0xc3, 0x28, 0x0, 0x6, 0xb0, 0x0, 0x0, 0x0,
    0x7, 0x87, 0x77, 0x77, 0x20, 0x7, 0x90, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x8c,
    0xb6, 0x6d, 0x90, 0x0, 0x0, 0x77, 0x78, 0xd1,
    0x0, 0xb, 0x50, 0xd, 0x40, 0x0, 0x0, 0x21,
    0x0, 0x0, 0x0, 0xd, 0x30, 0xe, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x20, 0xf,
    0x33, 0x40, 0x0, 0x77, 0x79, 0xd1, 0x99, 0x7c,
    0x87, 0x7c, 0x8b, 0xb0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x2, 0x61, 0x11, 0x11, 0xb4, 0x0,
    0x2, 0xd7, 0x77, 0xf5, 0x3, 0xf6, 0x66, 0x66,
    0xe6, 0x0, 0x1, 0xe0, 0x0, 0xe1, 0x3, 0xe0,
    0x0, 0x0, 0xd4, 0x0, 0x1, 0xe0, 0x0, 0xe1,
    0x2, 0xe0, 0x0, 0x0, 0xd4, 0x0, 0x1, 0xe0,
    0x0, 0xe1, 0x3, 0xe0, 0x0, 0x0, 0xd4, 0x0,
    0x1, 0xf7, 0x77, 0xf1, 0x3, 0xf7, 0x77, 0x77,
    0xe4, 0x0, 0x2, 0xe0, 0x0, 0xe2, 0x3, 0xe0,
    0x0, 0x0, 0xd5, 0x0, 0x1, 0x40, 0x0, 0x10,
    0x2, 0x40, 0x0, 0x0, 0x20, 0x0,

    /* U+8BA1 "计" */
    0x0, 0x5, 0x0, 0x0, 0x0, 0x1, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0, 0x1,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0x0, 0x0, 0x0, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf2, 0x0, 0x0, 0x0, 0x1, 0x11, 0x94, 0x0,
    0x0, 0x0, 0xf2, 0x0, 0xb, 0x30, 0x7, 0x66,
    0xf6, 0x9, 0x87, 0x77, 0xf8, 0x77, 0x77, 0x60,
    0x0, 0x0, 0xe1, 0x0, 0x0, 0x0, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe1, 0x0, 0x0, 0x0,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe1, 0x0,
    0x0, 0x0, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe1, 0x0, 0x0, 0x0, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe1, 0x2, 0x10, 0x0, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe1, 0x48, 0x0, 0x0,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf7, 0xc0,
    0x0, 0x0, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x20, 0x0, 0x0, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb6, 0x0, 0x0, 0x1, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BBE "设" */
    0x0, 0x7, 0x30, 0x0, 0x5, 0x0, 0x1, 0x80,
    0x0, 0x0, 0x0, 0x1f, 0x40, 0x0, 0xea, 0x77,
    0x9f, 0x30, 0x0, 0x0, 0x0, 0xb9, 0x0, 0xd,
    0x40, 0x3, 0xf0, 0x0, 0x0, 0x0, 0x3, 0x10,
    0x0, 0xe3, 0x0, 0x3f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0x0, 0x3, 0x0, 0x7, 0x90, 0x0, 0x3f, 0x88,
    0x93, 0x8, 0x87, 0xf9, 0x2, 0xd1, 0x0, 0x0,
    0x57, 0x77, 0x30, 0x0, 0xe, 0x22, 0xa1, 0x0,
    0x0, 0x0, 0x90, 0x0, 0x0, 0x0, 0xe3, 0x50,
    0x8b, 0x77, 0x77, 0xaf, 0x50, 0x0, 0x0, 0xe,
    0x20, 0x0, 0x43, 0x0, 0x9, 0xb0, 0x0, 0x0,
    0x0, 0xe2, 0x0, 0x0, 0x90, 0x0, 0xf4, 0x0,
    0x0, 0x0, 0xe, 0x20, 0x12, 0x9, 0x20, 0x7c,
    0x0, 0x0, 0x0, 0x0, 0xe2, 0x28, 0x0, 0x2b,
    0x2f, 0x30, 0x0, 0x0, 0x0, 0xe, 0x6b, 0x0,
    0x0, 0x9e, 0x80, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x20, 0x0, 0x1d, 0xea, 0x10, 0x0, 0x0, 0x0,
    0x1d, 0x60, 0x0, 0x5d, 0x60, 0x9f, 0x93, 0x0,
    0x0, 0x0, 0x10, 0x4, 0xb8, 0x10, 0x0, 0x4d,
    0xfe, 0x50, 0x0, 0x1, 0x58, 0x50, 0x0, 0x0,
    0x0, 0x5, 0x30,

    /* U+8BED "语" */
    0x2, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x0, 0x0, 0xc, 0x50, 0x5, 0x97, 0x7b, 0x77,
    0x78, 0xc8, 0x0, 0x0, 0x5f, 0x10, 0x0, 0x2,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc0, 0x0,
    0x0, 0x4c, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x7b, 0xc7, 0x78, 0xf3, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x97, 0x0, 0x2e, 0x0,
    0x0, 0x57, 0x8f, 0x30, 0x0, 0xb, 0x50, 0x2,
    0xe0, 0x0, 0x1, 0x11, 0xe0, 0x0, 0x0, 0xe2,
    0x0, 0x2e, 0x4, 0x60, 0x0, 0x1e, 0x4, 0xa7,
    0x7a, 0x77, 0x78, 0xa7, 0x9a, 0x10, 0x1, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0x0, 0x2, 0x40, 0x0, 0x0, 0x8, 0x0,
    0x0, 0x1, 0xe0, 0x0, 0x3f, 0x77, 0x77, 0x78,
    0xf3, 0x0, 0x0, 0x1e, 0x1, 0x43, 0xe0, 0x0,
    0x0, 0x2f, 0x0, 0x0, 0x1, 0xe4, 0xa0, 0x2e,
    0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x4f, 0xc0,
    0x2, 0xe0, 0x0, 0x0, 0x2f, 0x0, 0x0, 0x2,
    0xc1, 0x0, 0x3f, 0x77, 0x77, 0x78, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0, 0x2f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+8DDD "距" */
    0x0, 0x10, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0xa9, 0x77, 0x7e, 0x62, 0xd7,
    0x77, 0x77, 0xac, 0x10, 0x0, 0x96, 0x0, 0xe,
    0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x96,
    0x0, 0xe, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x96, 0x0, 0xe, 0x1, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x96, 0x0, 0xe, 0x11, 0xf5,
    0x55, 0x55, 0xb3, 0x0, 0x0, 0xaa, 0x8d, 0x7d,
    0x11, 0xf2, 0x22, 0x22, 0xf3, 0x0, 0x0, 0x41,
    0x1d, 0x0, 0x1, 0xf0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x50, 0x1d, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0xf4, 0x1e, 0x7e, 0x41, 0xf0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0xe0, 0x1d, 0x0,
    0x1, 0xf7, 0x77, 0x77, 0xf0, 0x0, 0x0, 0xe0,
    0x1d, 0x0, 0x1, 0xf0, 0x0, 0x0, 0xe1, 0x0,
    0x0, 0xe0, 0x1d, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe0, 0x1d, 0x3, 0x61, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe1, 0x5f, 0x94,
    0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x17, 0xfe,
    0x81, 0x0, 0x2, 0xf0, 0x0, 0x0, 0xa, 0x30,
    0xc, 0x70, 0x0, 0x0, 0x5, 0xe7, 0x77, 0x77,
    0x77, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+901F "速" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x85, 0x0, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x1, 0x0, 0x0, 0xe,
    0x80, 0x67, 0x77, 0x79, 0xe7, 0x77, 0xde, 0x30,
    0x0, 0x8, 0xb0, 0x12, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x3,
    0xc0, 0x3, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xe7, 0x79, 0xe7, 0x7e, 0xb0, 0x0, 0x0, 0x0,
    0x50, 0x1, 0xe0, 0x3, 0xc0, 0xc, 0x40, 0x0,
    0x9, 0x97, 0xf5, 0x1, 0xe0, 0x3, 0xc0, 0xc,
    0x40, 0x0, 0x0, 0x0, 0xf0, 0x1, 0xf7, 0x79,
    0xe7, 0x7d, 0x40, 0x0, 0x0, 0x0, 0xf0, 0x1,
    0xb0, 0x2f, 0xc0, 0x7, 0x20, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0xc9, 0xe7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0xb, 0x53, 0xc1, 0xac,
    0x50, 0x0, 0x0, 0x0, 0xf0, 0x1, 0xa3, 0x3,
    0xc0, 0x5, 0xf8, 0x0, 0x0, 0x4, 0xf1, 0x37,
    0x0, 0x4, 0xd0, 0x0, 0x5a, 0x0, 0x1, 0xb9,
    0x19, 0x60, 0x0, 0x5, 0xd0, 0x0, 0x0, 0x0,
    0xc, 0x90, 0x0, 0x7b, 0x74, 0x24, 0x42, 0x33,
    0x45, 0x72, 0x1, 0x0, 0x0, 0x1, 0x7b, 0xdf,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0x77, 0x77, 0x77,
    0x77, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x96, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x9a, 0x77, 0x77, 0x77, 0x77, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x96, 0x0, 0x0, 0x0, 0x0, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0xaa, 0x77, 0x77, 0x77,
    0x77, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x7, 0x10, 0x19, 0x87,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x8b, 0x80,
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x41,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0x77, 0x7e, 0x87,
    0x77, 0xd8, 0x0, 0x0, 0x0, 0x0, 0xd2, 0x0,
    0xe, 0x10, 0x0, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0x77, 0x7f, 0x87, 0x77, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0xd2, 0x0, 0xe, 0x10, 0x0, 0xb5,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0x77, 0x7f, 0x87,
    0x77, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x40, 0x0,
    0xe, 0x10, 0x0, 0x15, 0x0, 0x0, 0x0, 0x9,
    0x87, 0x77, 0x7f, 0x87, 0x77, 0x8d, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x10, 0x0, 0x0,
    0x3, 0x0, 0x6, 0x77, 0x77, 0x77, 0x7f, 0x87,
    0x77, 0x77, 0xaf, 0x60, 0x1, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+95F4 "间" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x5f, 0x22, 0x77, 0x77, 0x77, 0x77, 0xe6,
    0x93, 0xe, 0x40, 0x21, 0x0, 0x0, 0x0, 0xe3,
    0xd8, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe2,
    0xd4, 0x0, 0x11, 0x0, 0x0, 0x40, 0x0, 0xe2,
    0xd4, 0x0, 0x4e, 0x77, 0x78, 0xf1, 0x0, 0xe2,
    0xc4, 0x0, 0x4d, 0x0, 0x2, 0xe0, 0x0, 0xe2,
    0xc4, 0x0, 0x3d, 0x0, 0x2, 0xe0, 0x0, 0xe2,
    0xc4, 0x0, 0x3e, 0x77, 0x78, 0xe0, 0x0, 0xe2,
    0xc4, 0x0, 0x3d, 0x0, 0x2, 0xe0, 0x0, 0xe2,
    0xc4, 0x0, 0x3d, 0x0, 0x2, 0xe0, 0x0, 0xe2,
    0xc4, 0x0, 0x3d, 0x0, 0x2, 0xe0, 0x0, 0xe2,
    0xc4, 0x0, 0x4e, 0x77, 0x78, 0xf0, 0x0, 0xe2,
    0xc4, 0x0, 0x4c, 0x0, 0x2, 0xc0, 0x0, 0xe2,
    0xd4, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0xe2,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x4, 0x22, 0xf2,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xe0,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x20,

    /* U+9650 "限" */
    0x40, 0x0, 0x50, 0x12, 0x0, 0x0, 0x6, 0x10,
    0xe, 0x97, 0x7f, 0x62, 0xf7, 0x77, 0x77, 0xf9,
    0x0, 0xe2, 0x3, 0xc0, 0x2e, 0x0, 0x0, 0xe,
    0x20, 0xd, 0x20, 0x75, 0x2, 0xe0, 0x0, 0x0,
    0xe2, 0x0, 0xd2, 0xa, 0x0, 0x1f, 0x77, 0x77,
    0x7f, 0x20, 0xd, 0x20, 0x70, 0x1, 0xe0, 0x0,
    0x0, 0xe2, 0x0, 0xd2, 0x15, 0x0, 0x1e, 0x0,
    0x0, 0xe, 0x30, 0xd, 0x20, 0x72, 0x1, 0xe0,
    0x0, 0x0, 0xe3, 0x0, 0xd2, 0x1, 0xb0, 0x1f,
    0x7b, 0x77, 0x7e, 0x20, 0xd, 0x20, 0xa, 0x41,
    0xe0, 0x62, 0x0, 0x1a, 0x10, 0xd2, 0x0, 0x87,
    0x1e, 0x2, 0x70, 0xb, 0xe6, 0xd, 0x20, 0xa,
    0x81, 0xe0, 0xb, 0x1b, 0x60, 0x0, 0xd5, 0xae,
    0xf3, 0x1e, 0x0, 0x7b, 0x10, 0x0, 0xd, 0x20,
    0xa5, 0x1, 0xe0, 0x0, 0xd4, 0x0, 0x0, 0xd2,
    0x0, 0x0, 0x1e, 0x3, 0x53, 0xf7, 0x0, 0xe,
    0x30, 0x0, 0x2, 0xfa, 0x70, 0x4, 0xfe, 0x83,
    0xe3, 0x0, 0x0, 0x3f, 0x70, 0x0, 0x3, 0xec,
    0x4b, 0x20, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x0,

    /* U+97F3 "音" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xb0, 0x0, 0x6, 0x10, 0x0, 0x0, 0x79, 0x77,
    0x77, 0x9a, 0x77, 0x79, 0xea, 0x0, 0x0, 0x0,
    0x0, 0x61, 0x0, 0x0, 0x5a, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xd1, 0x0, 0xa, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x80, 0x0, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x84, 0x0,
    0x65, 0x0, 0x4, 0xa0, 0x7, 0x97, 0x77, 0x77,
    0x77, 0x79, 0x77, 0x77, 0xbb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x77, 0x77, 0x77, 0x77, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0xd4, 0x0, 0x0, 0x0, 0xf,
    0x20, 0x0, 0x0, 0x0, 0xd, 0x40, 0x0, 0x0,
    0x0, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd9, 0x77,
    0x77, 0x77, 0x7f, 0x20, 0x0, 0x0, 0x0, 0xd,
    0x40, 0x0, 0x0, 0x0, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xd4, 0x0, 0x0, 0x0, 0xf, 0x20, 0x0,
    0x0, 0x0, 0xd, 0x97, 0x77, 0x77, 0x77, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x0,
    0xf, 0x30, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0,
    0x0, 0x0, 0x50, 0x0, 0x0,

    /* U+984C "題" */
    0x0, 0x33, 0x0, 0x6, 0x30, 0x0, 0x0, 0x0,
    0x8, 0x10, 0x0, 0x4c, 0x77, 0x7d, 0x73, 0xa8,
    0x7a, 0x77, 0x8a, 0x60, 0x0, 0x4a, 0x0, 0xb,
    0x30, 0x0, 0xb, 0x30, 0x0, 0x0, 0x0, 0x4c,
    0x66, 0x6d, 0x30, 0x4, 0x8, 0x0, 0x71, 0x0,
    0x0, 0x4a, 0x11, 0x1b, 0x30, 0xf, 0x77, 0x77,
    0xf5, 0x0, 0x0, 0x4a, 0x11, 0x1b, 0x40, 0xe,
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x5c, 0x66, 0x6c,
    0x20, 0xf, 0x77, 0x77, 0xf0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x20, 0xe, 0x0, 0x0, 0xe0, 0x0,
    0x4, 0x98, 0x79, 0x77, 0xdb, 0x1f, 0x77, 0x77,
    0xf0, 0x0, 0x0, 0x10, 0xd, 0x10, 0x0, 0xe,
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x98, 0xd, 0x10,
    0x0, 0xf, 0x77, 0x77, 0xf1, 0x0, 0x0, 0xa5,
    0xd, 0x88, 0xf5, 0x5, 0x40, 0x0, 0x30, 0x0,
    0x0, 0xb4, 0xd, 0x10, 0x0, 0x2, 0xf5, 0x9,
    0x30, 0x0, 0x0, 0xd8, 0x1d, 0x10, 0x0, 0xb,
    0x50, 0x1, 0xe5, 0x0, 0x2, 0xb0, 0x8e, 0x10,
    0x0, 0x83, 0x0, 0x0, 0x69, 0x0, 0x6, 0x40,
    0x9, 0xc7, 0x34, 0x10, 0x11, 0x12, 0x46, 0x70,
    0x9, 0x0, 0x0, 0x38, 0xdf, 0xff, 0xff, 0xff,
    0xfe, 0x10, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+9AD8 "高" */
    0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb4,
    0x0, 0x0, 0x0, 0x50, 0x2, 0xa8, 0x77, 0x77,
    0x78, 0x77, 0x77, 0x77, 0x7c, 0x80, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x97, 0x77, 0x77, 0x7e, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xc5, 0x0, 0x0, 0x0, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0xc, 0x50, 0x0, 0x0,
    0xd, 0x30, 0x0, 0x0, 0x0, 0x0, 0xca, 0x77,
    0x77, 0x77, 0xe3, 0x0, 0x0, 0x0, 0x11, 0x3,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x31, 0x0, 0x2,
    0xe7, 0x77, 0x77, 0x77, 0x77, 0x77, 0x7e, 0x90,
    0x0, 0x2e, 0x0, 0x30, 0x0, 0x0, 0x50, 0x0,
    0xe3, 0x0, 0x2, 0xe0, 0xb, 0xa7, 0x77, 0x7f,
    0x50, 0xe, 0x30, 0x0, 0x1e, 0x0, 0xa6, 0x0,
    0x0, 0xe1, 0x0, 0xe3, 0x0, 0x1, 0xe0, 0xa,
    0x60, 0x0, 0xe, 0x10, 0xe, 0x30, 0x0, 0x1e,
    0x0, 0xaa, 0x77, 0x77, 0xf2, 0x0, 0xe3, 0x0,
    0x2, 0xe0, 0x7, 0x20, 0x0, 0x7, 0x0, 0xe,
    0x30, 0x0, 0x2e, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x9a, 0xf2, 0x0, 0x2, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FE83 "ﺃ" */
    0x4e, 0xe2, 0xd2, 0x10, 0xd5, 0x1, 0xaf, 0xf6,
    0x96, 0x20, 0x0, 0x0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0,

    /* U+FE87 "ﺇ" */
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0,
    0x1f, 0xb0, 0x1f, 0xb0, 0x1f, 0xb0, 0x0, 0x0,
    0x5e, 0xe2, 0xd2, 0x0, 0xb8, 0x42, 0xcf, 0xc3,
    0x30, 0x0,

    /* U+FE8B "ﺋ" */
    0x0, 0x4d, 0xe4, 0x0, 0xb4, 0x1, 0x0, 0x9a,
    0x53, 0x0, 0xae, 0xb4, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x90, 0x0, 0xf, 0xd0,
    0x0, 0xf, 0xc0, 0x0, 0x5f, 0xa0, 0x2d, 0xff,
    0x40, 0x3f, 0xe7, 0x0,

    /* U+FE8D "ﺍ" */
    0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1,
    0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f,
    0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb0,

    /* U+FE8E "ﺎ" */
    0x1f, 0xb0, 0x0, 0x1f, 0xb0, 0x0, 0x1f, 0xb0,
    0x0, 0x1f, 0xb0, 0x0, 0x1f, 0xb0, 0x0, 0x1f,
    0xb0, 0x0, 0x1f, 0xb0, 0x0, 0x1f, 0xb0, 0x0,
    0x1f, 0xb0, 0x0, 0x1f, 0xb0, 0x0, 0x1f, 0xb0,
    0x0, 0x1f, 0xb0, 0x0, 0xf, 0xe1, 0x0, 0x9,
    0xff, 0xd3, 0x0, 0x9e, 0xf4,

    /* U+FE90 "ﺐ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48,
    0x10, 0x5, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf4, 0x0, 0xaf, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x50, 0xa, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf7, 0x0, 0x6f, 0xb1,
    0x0, 0x0, 0x0, 0x5, 0xbf, 0xff, 0xd1, 0x0,
    0x9f, 0xfc, 0x98, 0x9a, 0xcf, 0xff, 0xb2, 0x9f,
    0xfa, 0x0, 0x39, 0xdf, 0xff, 0xec, 0x96, 0x10,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+FE91 "ﺑ" */
    0x0, 0xf, 0xd0, 0x0, 0xfd, 0x0, 0xf, 0xc0,
    0x5, 0xfa, 0x2d, 0xff, 0x43, 0xfd, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x0, 0xd, 0xa0, 0x0,
    0x43,

    /* U+FE92 "ﺒ" */
    0x0, 0xf, 0xd0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0x5f, 0xf3, 0x0,
    0x2d, 0xff, 0xff, 0xd2, 0x3f, 0xd7, 0x8e, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0, 0xd, 0xa0, 0x0, 0x0, 0x4, 0x30, 0x0,

    /* U+FE94 "ﺔ" */
    0x0, 0x11, 0x2, 0x0, 0x0, 0x0, 0x9e, 0x2f,
    0x50, 0x0, 0x0, 0x24, 0x5, 0x10, 0x0, 0x0,
    0x0, 0x3, 0x20, 0x0, 0x0, 0x1, 0x6f, 0xb0,
    0x0, 0x1, 0xaf, 0xff, 0xc0, 0x0, 0xd, 0xf7,
    0x1d, 0xf0, 0x0, 0x6f, 0x50, 0xa, 0xf2, 0x0,
    0x8f, 0x62, 0x5c, 0xfa, 0x0, 0x2d, 0xff, 0xfe,
    0xdf, 0xeb, 0x0, 0x34, 0x30, 0x1b, 0xfe,

    /* U+FE95 "ﺕ" */
    0x0, 0x0, 0x0, 0x20, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x4b, 0xc0, 0x0, 0x0,
    0x0, 0x4, 0x20, 0x0, 0x51, 0x33, 0x0, 0x0,
    0x7d, 0x27, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf4, 0xaf, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x2a, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xd0, 0x5f, 0xd4, 0x0, 0x0, 0x0,
    0x37, 0xcf, 0xf3, 0x0, 0x9f, 0xff, 0xcc, 0xce,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0x39, 0xdf, 0xff,
    0xdb, 0x85, 0x10, 0x0, 0x0,

    /* U+FE97 "ﺗ" */
    0x0, 0x20, 0x11, 0x0, 0xf7, 0x9e, 0x0, 0x52,
    0x34, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0xf, 0xd0, 0x0, 0xf, 0xd0, 0x0, 0xf, 0xc0,
    0x0, 0x5f, 0x90, 0x2d, 0xff, 0x30, 0x3f, 0xd6,
    0x0,

    /* U+FE98 "ﺘ" */
    0x0, 0x20, 0x11, 0x0, 0x0, 0xf7, 0x9e, 0x0,
    0x0, 0x52, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x10, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0x5f, 0xf3, 0x0, 0x2d, 0xff, 0xff, 0xd2,
    0x3f, 0xd7, 0x8e, 0xf3,

    /* U+FE99 "ﺙ" */
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x4b, 0xc0, 0x0,
    0x0, 0x0, 0x4, 0x20, 0x0, 0x51, 0x33, 0x0,
    0x0, 0x7d, 0x27, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf4, 0xaf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x2a, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xd0, 0x5f, 0xd4, 0x0, 0x0,
    0x0, 0x37, 0xcf, 0xf3, 0x0, 0x9f, 0xff, 0xcc,
    0xce, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x39, 0xdf,
    0xff, 0xdb, 0x85, 0x10, 0x0, 0x0,

    /* U+FEA0 "ﺠ" */
    0x0, 0x7f, 0xfe, 0xc9, 0x51, 0x0, 0x0, 0x0,
    0x6, 0xde, 0xff, 0xff, 0xfb, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x8f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xfe, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xe4, 0x9f, 0xc1, 0x0, 0x2c, 0xdf, 0xff,
    0xc2, 0x0, 0xcf, 0xfc, 0x13, 0xff, 0xd9, 0x40,
    0x0, 0x1, 0x9e, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x30, 0x0,
    0x0, 0x0,

    /* U+FEA2 "ﺢ" */
    0x3, 0x68, 0x99, 0x98, 0x52, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x57, 0x32, 0x9f,
    0xff, 0xc6, 0x0, 0x0, 0x1, 0xcf, 0xb3, 0xcd,
    0x0, 0x0, 0x0, 0xbf, 0x80, 0x6, 0xf3, 0x0,
    0x0, 0x5f, 0xa0, 0x0, 0xe, 0xb0, 0x0, 0xb,
    0xf2, 0x0, 0x0, 0x7f, 0x70, 0x0, 0xfd, 0x0,
    0x0, 0x0, 0xcf, 0xd1, 0xf, 0xc0, 0x0, 0x0,
    0x1, 0xaf, 0x10, 0xee, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf6, 0x0, 0x0, 0x2, 0x80, 0x0, 0x4f,
    0xff, 0xba, 0xad, 0xfe, 0x0, 0x0, 0x17, 0xbe,
    0xff, 0xd9, 0x30,

    /* U+FEA3 "ﺣ" */
    0x0, 0x7f, 0xfe, 0xc9, 0x51, 0x0, 0x0, 0x6d,
    0xef, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x14,
    0x8f, 0xfe, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xe7,
    0x0, 0x0, 0x0, 0x5f, 0xf9, 0x0, 0x0, 0x0,
    0x3a, 0xfe, 0x40, 0x0, 0x2c, 0xdf, 0xff, 0xc2,
    0x0, 0x0, 0x3f, 0xfd, 0x94, 0x0, 0x0, 0x0,

    /* U+FEA4 "ﺤ" */
    0x0, 0x7f, 0xfe, 0xc9, 0x51, 0x0, 0x0, 0x0,
    0x6, 0xde, 0xff, 0xff, 0xfb, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x8f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xfe, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xe4, 0x9f, 0xc1, 0x0, 0x2c, 0xdf, 0xff,
    0xc2, 0x0, 0xcf, 0xfc, 0x13, 0xff, 0xd9, 0x40,
    0x0, 0x1, 0x9e, 0xf1,

    /* U+FEA9 "ﺩ" */
    0x0, 0x58, 0x30, 0x0, 0x1, 0xdf, 0x30, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0x8, 0xf6, 0x0, 0x0,
    0x2f, 0xa0, 0x0, 0x2, 0xfb, 0x0, 0x1, 0xcf,
    0x7c, 0xdd, 0xff, 0xd0, 0x9e, 0xfd, 0x81, 0x0,

    /* U+FEAA "ﺪ" */
    0x0, 0x58, 0x30, 0x0, 0x0, 0x0, 0x1d, 0xf3,
    0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0x2f, 0xa0,
    0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x1,
    0xcf, 0xf6, 0x0, 0x9c, 0xcf, 0xfd, 0xdf, 0xd8,
    0xcf, 0xfd, 0x81, 0x2c, 0xfb,

    /* U+FEAD "ﺭ" */
    0x0, 0x0, 0x0, 0x6, 0x70, 0x0, 0x0, 0x0,
    0x8, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xf6, 0x0,
    0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x7,
    0xf5, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x0, 0x5f, 0xc0, 0x0, 0x0, 0x5, 0xff, 0x40,
    0x0, 0x5, 0xbf, 0xf6, 0x0, 0xae, 0xff, 0xfc,
    0x30, 0x0, 0xce, 0xc8, 0x30, 0x0, 0x0,

    /* U+FEAE "ﺮ" */
    0x0, 0x0, 0x0, 0x6, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0xcf, 0xae, 0xf3, 0x0,
    0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf3, 0x0, 0x0, 0x1, 0x38, 0xef, 0xf5,
    0x0, 0x0, 0xd, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0xab, 0x95, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEB1 "ﺱ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x2f, 0xb0, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x3f, 0xb0, 0x1, 0xfb, 0x6, 0x70, 0x0, 0x0,
    0x5, 0xf8, 0x0, 0x6f, 0xe0, 0x2, 0xfa, 0x2f,
    0xa0, 0x0, 0x0, 0x3, 0xff, 0x41, 0xdf, 0xf9,
    0x19, 0xf7, 0x7f, 0x50, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xe1, 0xaf, 0x20, 0x0,
    0x0, 0x6, 0xfb, 0xef, 0xb0, 0x3c, 0xfc, 0x20,
    0xbf, 0x10, 0x0, 0x0, 0xc, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x7f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xc4,
    0x10, 0x39, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xcc, 0xb6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEB2 "ﺲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x1f, 0xb0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x20, 0x2, 0xfb, 0x0, 0xf,
    0xc0, 0x0, 0x6, 0x70, 0x0, 0x0, 0x5, 0xf7,
    0x0, 0x5f, 0xe0, 0x2, 0xfd, 0x0, 0x2, 0xfa,
    0x0, 0x0, 0x0, 0x3f, 0xe1, 0xb, 0xff, 0x60,
    0x6f, 0xf6, 0x0, 0x7f, 0x50, 0x0, 0x0, 0x3,
    0xff, 0xed, 0xfb, 0xff, 0xdf, 0xff, 0xfd, 0x8a,
    0xf2, 0x0, 0x0, 0x0, 0x6f, 0xde, 0xfb, 0x15,
    0xdf, 0xd5, 0x6d, 0xfb, 0xbf, 0x10, 0x0, 0x0,
    0xc, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf3, 0x0, 0x0, 0x7, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xc4, 0x10,
    0x39, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfe, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0xcc, 0xb7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+FEB3 "ﺳ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf1, 0x0, 0x4, 0xf8, 0x0, 0xc, 0xf0, 0x0,
    0xbf, 0x10, 0x0, 0x4f, 0x80, 0x0, 0xdf, 0x10,
    0xb, 0xf1, 0x0, 0x6, 0xfb, 0x0, 0xf, 0xf3,
    0x0, 0xcf, 0x0, 0x1, 0xdf, 0xf3, 0x6, 0xff,
    0xb0, 0x2f, 0xd0, 0x2c, 0xff, 0xef, 0xfd, 0xfe,
    0xcf, 0xef, 0xf5, 0x3, 0xff, 0xa1, 0x8d, 0xfd,
    0x42, 0xbf, 0xd6, 0x0,

    /* U+FEB4 "ﺴ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x10, 0x0, 0x0, 0x4, 0xf8, 0x0,
    0xc, 0xf0, 0x0, 0xbf, 0x10, 0x0, 0x0, 0x4,
    0xf8, 0x0, 0xd, 0xf1, 0x0, 0xbf, 0x10, 0x0,
    0x0, 0x6, 0xfb, 0x0, 0xf, 0xf3, 0x0, 0xcf,
    0x30, 0x0, 0x0, 0x1d, 0xff, 0x30, 0x6f, 0xfb,
    0x2, 0xff, 0xb0, 0x0, 0x2c, 0xff, 0xef, 0xfd,
    0xfe, 0xcf, 0xef, 0xff, 0xfe, 0xc0, 0x3f, 0xfa,
    0x15, 0xdf, 0xd4, 0x2b, 0xfe, 0x84, 0xbf, 0xf0,

    /* U+FEBC "ﺼ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7d, 0xfe, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xeb,
    0xcf, 0xf1, 0x0, 0x0, 0x4, 0xe7, 0x5, 0xff,
    0x70, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x5f, 0xa4,
    0xfe, 0x30, 0x0, 0x8, 0xf7, 0x0, 0x0, 0xc,
    0xff, 0xff, 0x30, 0x0, 0x3b, 0xff, 0x50, 0x2,
    0xcf, 0xfe, 0xff, 0xec, 0xcd, 0xff, 0xff, 0xff,
    0xd6, 0x3f, 0xfa, 0x16, 0xdf, 0xff, 0xfe, 0xb6,
    0x6, 0xdf, 0x80,

    /* U+FEBF "ﺿ" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xdf,
    0xea, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xfe,
    0xbc, 0xfe, 0x10, 0x0, 0x4e, 0x70, 0x5f, 0xf7,
    0x0, 0x8, 0xf6, 0x0, 0x5, 0xfa, 0x4f, 0xe3,
    0x0, 0x0, 0x8f, 0x70, 0x0, 0xcf, 0xff, 0xf3,
    0x0, 0x3, 0xbf, 0xf2, 0x2c, 0xff, 0xef, 0xfe,
    0xcc, 0xdf, 0xff, 0xe4, 0x3, 0xff, 0xa1, 0x6d,
    0xff, 0xff, 0xeb, 0x60, 0x0,

    /* U+FEC0 "ﻀ" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xdf, 0xea, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xfe, 0xbc, 0xff, 0x10, 0x0,
    0x0, 0x4e, 0x70, 0x5f, 0xf7, 0x0, 0x8, 0xf7,
    0x0, 0x0, 0x5, 0xfa, 0x4f, 0xe3, 0x0, 0x0,
    0x8f, 0x70, 0x0, 0x0, 0xcf, 0xff, 0xf3, 0x0,
    0x3, 0xbf, 0xf5, 0x0, 0x2c, 0xff, 0xef, 0xfe,
    0xcc, 0xdf, 0xff, 0xff, 0xfd, 0x63, 0xff, 0xa1,
    0x6d, 0xff, 0xff, 0xeb, 0x60, 0x6d, 0xf8,

    /* U+FEC2 "ﻂ" */
    0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x3, 0xae, 0xfd, 0x80, 0x0, 0x0, 0x0, 0xf,
    0xc0, 0x8, 0xff, 0xdb, 0xef, 0xb0, 0x0, 0x0,
    0x0, 0xfc, 0xb, 0xfe, 0x40, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0xf, 0xca, 0xfb, 0x10, 0x0, 0xd,
    0xf1, 0x0, 0x0, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x6d, 0xfe, 0x0, 0x7, 0xcc, 0xcf, 0xff, 0xdc,
    0xde, 0xff, 0xff, 0xff, 0xc2, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x94, 0x1a, 0xef, 0x30,

    /* U+FEC4 "ﻄ" */
    0x0, 0x9, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf3, 0x0, 0x7, 0xdf, 0xeb, 0x30,
    0x0, 0x0, 0x9, 0xf3, 0x4, 0xef, 0xfb, 0xcf,
    0xf2, 0x0, 0x0, 0x9, 0xf3, 0x5f, 0xf8, 0x0,
    0x6, 0xf8, 0x0, 0x0, 0x9, 0xf6, 0xff, 0x40,
    0x0, 0x7, 0xf8, 0x0, 0x0, 0x9, 0xff, 0xf4,
    0x0, 0x3, 0xaf, 0xf6, 0x0, 0x2c, 0xce, 0xff,
    0xec, 0xcd, 0xff, 0xff, 0xff, 0xd7, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xeb, 0x60, 0x6d, 0xf9,

    /* U+FEC9 "ﻉ" */
    0x0, 0x7, 0xce, 0xb0, 0x0, 0x0, 0x1d, 0xff,
    0xc7, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xed, 0x0, 0x0, 0x1, 0x0, 0xd, 0xe3,
    0x26, 0xbe, 0xe0, 0x0, 0x5e, 0xff, 0xff, 0xd9,
    0x0, 0x1, 0xcf, 0xe7, 0x10, 0x0, 0x0, 0xdf,
    0x90, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xd3, 0x0, 0x0, 0x2, 0x60,
    0x8f, 0xfe, 0xba, 0xad, 0xfb, 0x0, 0x29, 0xce,
    0xfe, 0xd8, 0x20,

    /* U+FECA "ﻊ" */
    0x1, 0x8c, 0xc8, 0x20, 0x0, 0x1, 0xef, 0xff,
    0xfe, 0x10, 0x0, 0x4f, 0xf3, 0x2f, 0xf5, 0x0,
    0x0, 0xdf, 0xce, 0xfd, 0x10, 0x0, 0x4, 0xff,
    0xf9, 0x10, 0x0, 0x1, 0xef, 0xef, 0xc3, 0x0,
    0x0, 0x9f, 0xa0, 0x9f, 0xfe, 0xca, 0xc, 0xf1,
    0x0, 0x3a, 0xef, 0xd0, 0xcf, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xd3, 0x0, 0x0, 0x3, 0x70, 0x7f, 0xfe, 0xba,
    0xae, 0xfb, 0x0, 0x29, 0xce, 0xff, 0xd9, 0x20,

    /* U+FECB "ﻋ" */
    0x0, 0x0, 0x1, 0x69, 0x70, 0x0, 0x0, 0x6,
    0xff, 0xfb, 0x0, 0x0, 0x4, 0xfd, 0x41, 0x0,
    0x0, 0x0, 0xbf, 0x10, 0x0, 0x0, 0x0, 0xe,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0,
    0x3, 0x0, 0xa, 0xf7, 0x0, 0x5c, 0xe0, 0x0,
    0x1e, 0xfe, 0xef, 0xf9, 0x0, 0x2, 0x8f, 0xff,
    0x91, 0x2, 0xce, 0xff, 0xe8, 0x10, 0x0, 0x3f,
    0xea, 0x50, 0x0, 0x0, 0x0,

    /* U+FECC "ﻌ" */
    0x0, 0x2, 0x9c, 0xb7, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x7f, 0xe0, 0x5f, 0xf1,
    0x0, 0x1, 0xef, 0x9d, 0xfa, 0x0, 0x0, 0x3,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xb1,
    0x0, 0x2c, 0xef, 0xf7, 0xbf, 0xfd, 0xa3, 0xfe,
    0xb3, 0x0, 0x6c, 0xfd,

    /* U+FECF "ﻏ" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x15, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x97, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xb0, 0x0, 0x0, 0x4f, 0xd4, 0x10,
    0x0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xec, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x0, 0x30, 0x0, 0xaf, 0x70, 0x5, 0xce, 0x0,
    0x1, 0xef, 0xee, 0xff, 0x90, 0x0, 0x28, 0xff,
    0xf9, 0x10, 0x2c, 0xef, 0xfe, 0x81, 0x0, 0x3,
    0xfe, 0xa5, 0x0, 0x0, 0x0,

    /* U+FED0 "ﻐ" */
    0x0, 0x0, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xe9, 0x0, 0x0, 0x0, 0x0, 0x4, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x9c, 0xb7, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x7f, 0xe0, 0x5f, 0xf1, 0x0, 0x1,
    0xef, 0x9d, 0xfa, 0x0, 0x0, 0x3, 0xef, 0xfb,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xb1, 0x0, 0x2c,
    0xef, 0xf7, 0xbf, 0xfd, 0xa3, 0xfe, 0xb3, 0x0,
    0x6c, 0xfd,

    /* U+FED3 "ﻓ" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0xd,
    0x90, 0x0, 0x0, 0x0, 0x4, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d, 0xd5,
    0x0, 0x0, 0xb, 0xff, 0xff, 0x50, 0x0, 0x2f,
    0xe2, 0x4f, 0xe0, 0x0, 0x4f, 0x90, 0xd, 0xf1,
    0x0, 0x1f, 0xd1, 0x2f, 0xf1, 0x0, 0x9, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x48, 0x6f, 0xc0, 0x0,
    0x0, 0x1, 0xaf, 0x70, 0x2c, 0xcd, 0xef, 0xfc,
    0x0, 0x3f, 0xff, 0xfd, 0x80, 0x0,

    /* U+FED8 "ﻘ" */
    0x0, 0x0, 0x21, 0x12, 0x0, 0x0, 0x0, 0x0,
    0xf8, 0x8f, 0x0, 0x0, 0x0, 0x0, 0x52, 0x25,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4b, 0xc5, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0x70, 0x0, 0x0, 0xd, 0xf5, 0x3f,
    0xf0, 0x0, 0x0, 0xf, 0xe0, 0xc, 0xf1, 0x0,
    0x0, 0xd, 0xf2, 0x1f, 0xf0, 0x0, 0x0, 0x7,
    0xfe, 0xdf, 0x80, 0x0, 0x2c, 0xce, 0xff, 0xff,
    0xec, 0xc3, 0x3f, 0xff, 0xd9, 0x8c, 0xef, 0xf5,

    /* U+FEDB "ﻛ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xc8, 0x0, 0x0, 0x1, 0x8e, 0xff,
    0x60, 0x0, 0x4b, 0xff, 0xe7, 0x10, 0x0, 0x8f,
    0xfb, 0x50, 0x0, 0x0, 0x1f, 0xe3, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x80, 0x0, 0x0, 0x0, 0x2, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x80, 0x0, 0x2c, 0xcc, 0xef,
    0xf2, 0x0, 0x3, 0xff, 0xff, 0xc3, 0x0, 0x0,

    /* U+FEDC "ﻜ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xc8, 0x0, 0x0, 0x0, 0x1,
    0x8e, 0xff, 0x60, 0x0, 0x0, 0x4b, 0xff, 0xe7,
    0x10, 0x0, 0x0, 0x8f, 0xfb, 0x50, 0x0, 0x0,
    0x0, 0x1f, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xa0, 0x0, 0x2c, 0xcc, 0xef, 0xf3, 0xdf,
    0xec, 0x23, 0xff, 0xff, 0xb3, 0x1, 0xae, 0xf3,

    /* U+FEDE "ﻞ" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xc0, 0x0, 0x3f, 0x90, 0x0, 0x0, 0x3, 0xff,
    0x20, 0x8, 0xf4, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xd4, 0x8f, 0x30, 0x0, 0x0, 0x2e, 0xfa, 0xef,
    0x55, 0xfc, 0x10, 0x0, 0x6e, 0xf9, 0x0, 0x0,
    0xc, 0xff, 0xcc, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x7, 0xdf, 0xfc, 0x82, 0x0, 0x0, 0x0,

    /* U+FEDF "ﻟ" */
    0x0, 0x9, 0xf3, 0x0, 0x9, 0xf3, 0x0, 0x9,
    0xf3, 0x0, 0x9, 0xf3, 0x0, 0x9, 0xf3, 0x0,
    0x9, 0xf3, 0x0, 0x9, 0xf3, 0x0, 0x9, 0xf3,
    0x0, 0x9, 0xf3, 0x0, 0x9, 0xf3, 0x0, 0x9,
    0xf3, 0x0, 0xa, 0xf2, 0x0, 0x1e, 0xf0, 0x2c,
    0xff, 0xb0, 0x3f, 0xea, 0x10,

    /* U+FEE0 "ﻠ" */
    0x0, 0x9, 0xf3, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x9, 0xf3, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x9, 0xf3, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x9, 0xf3, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x9, 0xf3, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x9, 0xf3, 0x0, 0x0, 0xa, 0xf3, 0x0,
    0x0, 0x1e, 0xf8, 0x0, 0x2c, 0xff, 0xff, 0xea,
    0x3f, 0xea, 0x5d, 0xfd,

    /* U+FEE1 "ﻡ" */
    0x0, 0x0, 0x4d, 0xfb, 0x30, 0x0, 0x4, 0xff,
    0xff, 0xf4, 0x0, 0xd, 0xf6, 0x6, 0xfb, 0x0,
    0xf, 0xe0, 0x0, 0xfe, 0x6, 0xef, 0xf4, 0x5,
    0xfc, 0x3f, 0xfe, 0xff, 0xff, 0xf5, 0x8f, 0x90,
    0x38, 0xa9, 0x20, 0x9f, 0x50, 0x0, 0x0, 0x0,
    0xaf, 0x50, 0x0, 0x0, 0x0, 0xaf, 0x50, 0x0,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x0, 0xaf,
    0x50, 0x0, 0x0, 0x0,

    /* U+FEE2 "ﻢ" */
    0x0, 0x0, 0x2c, 0xfb, 0x20, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x9, 0xf6,
    0x7, 0xf9, 0x0, 0x0, 0x0, 0xee, 0x0, 0xf,
    0xe0, 0x0, 0x0, 0xf, 0xe0, 0x2, 0xff, 0x70,
    0x0, 0x5d, 0xff, 0xfc, 0xef, 0xff, 0xf7, 0x3f,
    0xc3, 0x8d, 0xff, 0xdb, 0xff, 0x88, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEE3 "ﻣ" */
    0x0, 0x0, 0x19, 0xec, 0x70, 0x0, 0x0, 0xd,
    0xff, 0xff, 0x80, 0x0, 0x5, 0xfa, 0x3, 0xff,
    0x0, 0x0, 0x9f, 0x20, 0xc, 0xf1, 0x0, 0x1e,
    0xf3, 0x0, 0xef, 0x2, 0xce, 0xff, 0xfd, 0xdf,
    0xb0, 0x3f, 0xfa, 0x8d, 0xff, 0xa1, 0x0,

    /* U+FEE4 "ﻤ" */
    0x0, 0x0, 0x19, 0xdc, 0x40, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0x50, 0x0, 0x0, 0x5, 0xfa,
    0x4, 0xfd, 0x0, 0x0, 0x0, 0x9f, 0x30, 0xc,
    0xf1, 0x0, 0x0, 0x1e, 0xf3, 0x0, 0xdf, 0x80,
    0x2, 0xce, 0xff, 0xfd, 0xdf, 0xff, 0xe9, 0x3f,
    0xfa, 0x9d, 0xff, 0xda, 0xef, 0xc0,

    /* U+FEE6 "ﻦ" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x25, 0x0, 0x6, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xa0, 0x0, 0x3, 0x10,
    0x0, 0x0, 0x0, 0xcf, 0x10, 0x4, 0xf8, 0x0,
    0x0, 0x0, 0xa, 0xf9, 0x0, 0x7f, 0x50, 0x0,
    0x0, 0x0, 0xaf, 0xfd, 0x58, 0xf3, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0xf6, 0x7f, 0x40, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x0,
    0xaf, 0x70, 0x0, 0xd, 0xf8, 0x30, 0x13, 0xbf,
    0xd0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x5, 0xac, 0xc9, 0x40, 0x0,
    0x0, 0x0,

    /* U+FEE8 "ﻨ" */
    0x0, 0x1, 0x10, 0x0, 0x0, 0xd, 0xa0, 0x0,
    0x0, 0x4, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x10, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0x5f, 0xf3, 0x0, 0x2d, 0xff, 0xff, 0xd2,
    0x3f, 0xd7, 0x8e, 0xf3,

    /* U+FEEE "ﻮ" */
    0x0, 0x0, 0x26, 0x60, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1f, 0xfb, 0xdf,
    0xa0, 0x0, 0x0, 0x5f, 0xa0, 0xe, 0xf0, 0x0,
    0x0, 0x3f, 0xd0, 0xb, 0xf0, 0x0, 0x0, 0xc,
    0xff, 0xde, 0xfd, 0xd6, 0x0, 0x1, 0x9e, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0x50, 0x0, 0x0, 0x14,
    0x8f, 0xf9, 0x0, 0x0, 0xdf, 0xff, 0xff, 0x70,
    0x0, 0x0, 0xac, 0xa8, 0x50, 0x0, 0x0, 0x0,

    /* U+FEEF "ﻯ" */
    0x0, 0x0, 0x0, 0x1, 0x68, 0x73, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0xef, 0x50, 0x1c, 0xf2, 0x0, 0x0, 0x0,
    0xee, 0x20, 0x1, 0x20, 0x0, 0x0, 0x0, 0x7f,
    0xfb, 0x50, 0x0, 0x2b, 0x60, 0x0, 0x4, 0xbf,
    0xfc, 0x10, 0x8f, 0x30, 0x0, 0x0, 0x1, 0x9f,
    0x80, 0xbf, 0x10, 0x0, 0x0, 0x0, 0x5f, 0xb0,
    0x9f, 0x50, 0x0, 0x0, 0x5, 0xef, 0x60, 0x3f,
    0xfa, 0x66, 0x9b, 0xff, 0xf9, 0x0, 0x4, 0xef,
    0xff, 0xff, 0xfb, 0x40, 0x0, 0x0, 0x5, 0x77,
    0x63, 0x0, 0x0, 0x0,

    /* U+FEF0 "ﻰ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe4, 0x0,
    0x2a, 0x50, 0x0, 0x0, 0x8, 0xfd, 0xff, 0x20,
    0x9f, 0x30, 0x0, 0x0, 0xc, 0xf3, 0x4f, 0xc0,
    0xbf, 0x10, 0x0, 0x0, 0x6, 0xff, 0x28, 0xfc,
    0x9f, 0x40, 0x0, 0x0, 0x0, 0x8f, 0x60, 0x9d,
    0x4f, 0xf6, 0x10, 0x1, 0x37, 0xff, 0x20, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x17, 0xab, 0xcb, 0x84, 0x0, 0x0, 0x0,

    /* U+FEF2 "ﻲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe4, 0x0,
    0x2a, 0x50, 0x0, 0x0, 0x8, 0xfd, 0xff, 0x20,
    0x9f, 0x30, 0x0, 0x0, 0xc, 0xf3, 0x4f, 0xc0,
    0xbf, 0x10, 0x0, 0x0, 0x6, 0xff, 0x28, 0xfc,
    0x9f, 0x40, 0x0, 0x0, 0x0, 0x8f, 0x60, 0x9d,
    0x4f, 0xf6, 0x10, 0x1, 0x37, 0xff, 0x20, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x17, 0xab, 0xcb, 0x84, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x20, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf5, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x51, 0x34, 0x0, 0x0, 0x0, 0x0,

    /* U+FEF3 "ﻳ" */
    0x0, 0xf, 0xd0, 0x0, 0xf, 0xd0, 0x0, 0xf,
    0xc0, 0x0, 0x5f, 0xa0, 0x2d, 0xff, 0x40, 0x3f,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x11,
    0x0, 0xf7, 0x9e, 0x0, 0x52, 0x34,

    /* U+FEF4 "ﻴ" */
    0x0, 0xf, 0xd0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0x5f, 0xf3, 0x0,
    0x2d, 0xff, 0xff, 0xd2, 0x3f, 0xd7, 0x8e, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x11, 0x0,
    0x0, 0xf7, 0x9e, 0x0, 0x0, 0x52, 0x34, 0x0,

    /* U+FEF7 "ﻷ" */
    0x9, 0xeb, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x10,
    0x0, 0x0, 0x0, 0x1e, 0x44, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x13, 0x0,
    0x0, 0x0, 0x7f, 0x50, 0x9, 0x60, 0x0, 0x7,
    0xf5, 0x0, 0xaf, 0x10, 0x0, 0x7f, 0x50, 0x3,
    0xf8, 0x0, 0x7, 0xf5, 0x0, 0xb, 0xe0, 0x0,
    0x7f, 0x50, 0x0, 0x4f, 0x70, 0x7, 0xf5, 0x0,
    0x0, 0xde, 0x0, 0x7f, 0x50, 0x0, 0x5, 0xf5,
    0x7, 0xf5, 0x0, 0x0, 0xe, 0xd0, 0x9f, 0x30,
    0x0, 0x0, 0x6f, 0x4d, 0xf0, 0x0, 0x0, 0x0,
    0xee, 0xfa, 0x0, 0x0, 0x0, 0x8, 0xff, 0x20,
    0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x9, 0xee,
    0xff, 0x60, 0x0, 0x0, 0x7f, 0xfb, 0x30, 0x0,
    0x0,

    /* U+FEF9 "ﻹ" */
    0x0, 0x0, 0x0, 0x7, 0xf5, 0x9, 0x60, 0x0,
    0x7, 0xf5, 0xa, 0xf1, 0x0, 0x7, 0xf5, 0x3,
    0xf8, 0x0, 0x7, 0xf5, 0x0, 0xbe, 0x0, 0x7,
    0xf5, 0x0, 0x4f, 0x70, 0x7, 0xf5, 0x0, 0xd,
    0xe0, 0x7, 0xf5, 0x0, 0x5, 0xf5, 0x7, 0xf5,
    0x0, 0x0, 0xed, 0x9, 0xf3, 0x0, 0x0, 0x6f,
    0x4d, 0xf0, 0x0, 0x0, 0xe, 0xef, 0xa0, 0x0,
    0x0, 0x8, 0xff, 0x20, 0x0, 0x0, 0x4f, 0xf6,
    0x0, 0x9, 0xee, 0xff, 0x60, 0x0, 0x7, 0xff,
    0xb3, 0x0, 0x0, 0x3d, 0xe4, 0x0, 0x0, 0x0,
    0xb4, 0x1, 0x0, 0x0, 0x0, 0x9a, 0x43, 0x0,
    0x0, 0x0, 0x9f, 0xc5, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 79, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 99, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 28, .adv_w = 127, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 46, .adv_w = 206, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 137, .adv_w = 208, .box_w = 13, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 261, .adv_w = 270, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 366, .adv_w = 212, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 464, .adv_w = 68, .box_w = 3, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 473, .adv_w = 126, .box_w = 7, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 536, .adv_w = 126, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 590, .adv_w = 174, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 631, .adv_w = 215, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 686, .adv_w = 94, .box_w = 4, .box_h = 6, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 698, .adv_w = 149, .box_w = 7, .box_h = 3, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 709, .adv_w = 93, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 717, .adv_w = 121, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 789, .adv_w = 213, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 873, .adv_w = 154, .box_w = 7, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 922, .adv_w = 199, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 999, .adv_w = 209, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1076, .adv_w = 213, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1160, .adv_w = 202, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1237, .adv_w = 207, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1314, .adv_w = 188, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1391, .adv_w = 207, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1468, .adv_w = 207, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1545, .adv_w = 93, .box_w = 4, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1567, .adv_w = 94, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1595, .adv_w = 215, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1661, .adv_w = 215, .box_w = 10, .box_h = 7, .ofs_x = 2, .ofs_y = 2},
    {.bitmap_index = 1696, .adv_w = 215, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1762, .adv_w = 174, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1832, .adv_w = 320, .box_w = 19, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2003, .adv_w = 232, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2101, .adv_w = 210, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2185, .adv_w = 238, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2276, .adv_w = 232, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2367, .adv_w = 194, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2437, .adv_w = 187, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2507, .adv_w = 242, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2598, .adv_w = 238, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2689, .adv_w = 88, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2717, .adv_w = 179, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2787, .adv_w = 217, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2878, .adv_w = 181, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2948, .adv_w = 290, .box_w = 16, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3060, .adv_w = 237, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3151, .adv_w = 248, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3249, .adv_w = 206, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3333, .adv_w = 248, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3438, .adv_w = 208, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3522, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3613, .adv_w = 211, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3704, .adv_w = 235, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3795, .adv_w = 232, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3893, .adv_w = 322, .box_w = 20, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4033, .adv_w = 221, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4131, .adv_w = 225, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4229, .adv_w = 209, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4306, .adv_w = 126, .box_w = 6, .box_h = 18, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4360, .adv_w = 121, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4432, .adv_w = 126, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4486, .adv_w = 154, .box_w = 9, .box_h = 7, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 4518, .adv_w = 150, .box_w = 11, .box_h = 3, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4535, .adv_w = 159, .box_w = 6, .box_h = 4, .ofs_x = 2, .ofs_y = 12},
    {.bitmap_index = 4547, .adv_w = 184, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4608, .adv_w = 202, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4685, .adv_w = 185, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4746, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4830, .adv_w = 190, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4891, .adv_w = 121, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4951, .adv_w = 200, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5041, .adv_w = 196, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5111, .adv_w = 83, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5141, .adv_w = 83, .box_w = 6, .box_h = 19, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 5198, .adv_w = 182, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5275, .adv_w = 83, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5296, .adv_w = 287, .box_w = 16, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5384, .adv_w = 195, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5439, .adv_w = 195, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5505, .adv_w = 200, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5588, .adv_w = 200, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5678, .adv_w = 127, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5720, .adv_w = 176, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5781, .adv_w = 122, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5830, .adv_w = 195, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5885, .adv_w = 184, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5951, .adv_w = 268, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6045, .adv_w = 180, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6106, .adv_w = 184, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6196, .adv_w = 180, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6251, .adv_w = 126, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6323, .adv_w = 115, .box_w = 3, .box_h = 24, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 6359, .adv_w = 126, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6431, .adv_w = 215, .box_w = 11, .box_h = 5, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 6459, .adv_w = 150, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6494, .adv_w = 89, .box_w = 4, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6534, .adv_w = 89, .box_w = 4, .box_h = 21, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 6576, .adv_w = 89, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6599, .adv_w = 301, .box_w = 17, .box_h = 11, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 6693, .adv_w = 207, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6777, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 6832, .adv_w = 391, .box_w = 22, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6975, .adv_w = 387, .box_w = 22, .box_h = 15, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7140, .adv_w = 296, .box_w = 17, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7268, .adv_w = 296, .box_w = 17, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7396, .adv_w = 191, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7479, .adv_w = 332, .box_w = 19, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7612, .adv_w = 248, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 7738, .adv_w = 233, .box_w = 12, .box_h = 18, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7846, .adv_w = 198, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7906, .adv_w = 235, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7991, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 8046, .adv_w = 250, .box_w = 14, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8130, .adv_w = 250, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 8228, .adv_w = 320, .box_w = 6, .box_h = 6, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 8246, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8406, .adv_w = 320, .box_w = 16, .box_h = 20, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 8566, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8756, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8946, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9136, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9326, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9507, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9688, .adv_w = 320, .box_w = 19, .box_h = 18, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9859, .adv_w = 320, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10030, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10211, .adv_w = 320, .box_w = 19, .box_h = 18, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10382, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10563, .adv_w = 320, .box_w = 13, .box_h = 18, .ofs_x = 4, .ofs_y = -2},
    {.bitmap_index = 10680, .adv_w = 320, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10851, .adv_w = 320, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11041, .adv_w = 320, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11176, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11376, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11566, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11747, .adv_w = 320, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11918, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12099, .adv_w = 320, .box_w = 16, .box_h = 18, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 12243, .adv_w = 320, .box_w = 17, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 12405, .adv_w = 320, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12576, .adv_w = 320, .box_w = 18, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 12756, .adv_w = 320, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12927, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13117, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13298, .adv_w = 320, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13469, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13659, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13849, .adv_w = 320, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14020, .adv_w = 320, .box_w = 19, .box_h = 18, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 14191, .adv_w = 320, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14371, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14561, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14751, .adv_w = 320, .box_w = 16, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 14903, .adv_w = 320, .box_w = 17, .box_h = 18, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 15056, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15237, .adv_w = 320, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15417, .adv_w = 320, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15607, .adv_w = 89, .box_w = 4, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15649, .adv_w = 89, .box_w = 4, .box_h = 21, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 15691, .adv_w = 89, .box_w = 6, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 15727, .adv_w = 89, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15750, .adv_w = 98, .box_w = 6, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15795, .adv_w = 314, .box_w = 19, .box_h = 11, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 15900, .adv_w = 89, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 15925, .adv_w = 97, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 15965, .adv_w = 172, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16020, .adv_w = 301, .box_w = 17, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16097, .adv_w = 89, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16130, .adv_w = 97, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16174, .adv_w = 301, .box_w = 17, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16268, .adv_w = 207, .box_w = 15, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 16358, .adv_w = 207, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 16449, .adv_w = 198, .box_w = 12, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16497, .adv_w = 207, .box_w = 15, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16557, .adv_w = 143, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16589, .adv_w = 168, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16634, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 16689, .adv_w = 177, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 16761, .adv_w = 391, .box_w = 22, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 16904, .adv_w = 408, .box_w = 25, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 17067, .adv_w = 268, .box_w = 17, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17135, .adv_w = 285, .box_w = 20, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17215, .adv_w = 278, .box_w = 19, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17282, .adv_w = 272, .box_w = 17, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17367, .adv_w = 278, .box_w = 19, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17462, .adv_w = 304, .box_w = 19, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17605, .adv_w = 263, .box_w = 18, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17740, .adv_w = 191, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 17823, .adv_w = 170, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 17895, .adv_w = 191, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17956, .adv_w = 154, .box_w = 11, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18000, .adv_w = 167, .box_w = 11, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18077, .adv_w = 154, .box_w = 11, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18143, .adv_w = 153, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18213, .adv_w = 162, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18285, .adv_w = 152, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18373, .adv_w = 177, .box_w = 13, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18477, .adv_w = 242, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 18612, .adv_w = 98, .box_w = 6, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18657, .adv_w = 106, .box_w = 8, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18717, .adv_w = 198, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 18777, .adv_w = 213, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 18855, .adv_w = 171, .box_w = 11, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18894, .adv_w = 185, .box_w = 13, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18940, .adv_w = 244, .box_w = 15, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 19038, .adv_w = 97, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 19082, .adv_w = 165, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 19154, .adv_w = 250, .box_w = 14, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 19238, .adv_w = 267, .box_w = 16, .box_h = 8, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 19302, .adv_w = 267, .box_w = 16, .box_h = 11, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 19390, .adv_w = 89, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 19420, .adv_w = 97, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 19460, .adv_w = 183, .box_w = 11, .box_h = 19, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 19565, .adv_w = 183, .box_w = 10, .box_h = 20, .ofs_x = 0, .ofs_y = -5}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x2, 0x4, 0x6, 0x7, 0xc, 0x10, 0x12,
    0x15, 0x16, 0x17, 0x18, 0x20, 0x21, 0x23, 0x24,
    0x25, 0x27, 0x28, 0x29, 0x29e1, 0x47e8, 0x480c, 0x488d,
    0x48a9, 0x4932, 0x4b28, 0x5126, 0x5306, 0x5308, 0x5852, 0x5885,
    0x5919, 0x5f66, 0x5fc4, 0x5fd5, 0x60df, 0x60e7, 0x63f8, 0x663f,
    0x6a4e, 0x6b46, 0x6c38, 0x6f14, 0x7063, 0x739a, 0x755f, 0x78be,
    0x794d, 0x7ebc, 0x83df, 0x847d, 0x8580, 0x859d, 0x85cc, 0x87bc,
    0x89fe, 0x8bae, 0x8fd3, 0x902f, 0x91d2, 0x922b, 0x94b7, 0xf862,
    0xf866, 0xf86a, 0xf86c, 0xf86d, 0xf86f, 0xf870, 0xf871, 0xf873,
    0xf874, 0xf876, 0xf877, 0xf878, 0xf87f, 0xf881, 0xf882, 0xf883,
    0xf888, 0xf889, 0xf88c, 0xf88d, 0xf890, 0xf891, 0xf892, 0xf893,
    0xf89b, 0xf89e, 0xf89f, 0xf8a1, 0xf8a3, 0xf8a8, 0xf8a9, 0xf8aa,
    0xf8ab, 0xf8ae, 0xf8af, 0xf8b2, 0xf8b7, 0xf8ba, 0xf8bb, 0xf8bd,
    0xf8be, 0xf8bf, 0xf8c0, 0xf8c1, 0xf8c2, 0xf8c3, 0xf8c5, 0xf8c7,
    0xf8cd, 0xf8ce, 0xf8cf, 0xf8d1, 0xf8d2, 0xf8d3, 0xf8d6, 0xf8d8
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1569, .range_length = 63705, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 120, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
    /*Store all the custom data of the font*/
    static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_ebike_inter_20 = {
#else
lv_font_t font_ebike_inter_20 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 27,          /*The maximum line height required by the font*/
    .base_line = 6,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_EBIKE_INTER_20*/

