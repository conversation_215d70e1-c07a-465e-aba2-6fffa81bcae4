#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: note.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_note_icon_data[] = {
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0x5F,0xAD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x4D,0x1F,0x4D,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x45,0x1F,0x4D,0xFF,0x4C,0xDF,0x4C,0xBF,0x4C,0xDF,0x4C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0x5F,0xAD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x1F,0x4D,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x1F,0x4D,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x1F,0x4D,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xBF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xBF,0xB4,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x1F,0x4D,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xBF,0xB4,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x1F,0x4D,0xBF,0x4C,0xBF,0x4C,0xFF,0x4C,0xFF,0x4C,0xFF,0x4C,
0xFF,0xFF,0xFF,0xFF,0xDF,0xB4,0xFF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xDF,0xBC,0xBF,0xB4,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x3F,0x4D,0x1F,0x4D,0x3F,0x4D,0xFF,0xFF,0xDF,0x4C,0xBF,0x4C,0xFF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xBC,0xDF,0xBC,0xBF,0xBC,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xFF,0x45,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0x4C,0xBF,0x4C,
0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5F,0xAD,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0x7F,0x55,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0x4C,0xBF,0x4C,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0x7F,0x55,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4C,0xBF,0x4C,0xDF,0x4C,0xBF,0x4C,
0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0x7F,0x55,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5F,0x55,0xDF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xDF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0x7F,0x55,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,
0xFF,0xFF,0xFF,0xFF,0xDF,0xBC,0xBF,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x45,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x7F,0x55,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x1F,0x84,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x7F,0x55,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xBF,0x4C,0xDF,0x4C,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0x7F,0x55,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0x4C,0xBF,0x4C,0xBF,0x4C,0xDF,0x4C,0xDF,0x4C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xDF,0x64,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0x64,0x7F,0x54,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xBF,0x4D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,


0x00,0x00,0x00,0x00,0xC7,0xA3,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF3,0xF3,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF3,0xFF,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF3,0xFF,0xD5,0x29,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xF3,0xFF,0xFF,0xF3,0x56,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF3,0xFF,0xFF,0xFF,0xFF,0x8A,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x43,0x59,0x00,0x00,0x00,0x00,0xEE,0xFF,0xFF,0xFF,0xFF,0xFF,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x7A,0xD0,0xFA,0xFF,0xFF,0x00,0x00,0x00,0x00,0xDE,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x03,0x00,0x00,0x00,0x27,0x6E,0xB6,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,
0x00,0x00,0x00,0x00,0xC8,0xDA,0xFF,0xFF,0xFF,0xFF,0xFF,0x25,0x07,0x95,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0xC8,0xA0,0x54,0xED,0xFF,0xFF,0xFF,0x3F,0x54,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0xC8,0xA0,0x00,0x1B,0xBD,0xFF,0xFF,0x40,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0xC8,0xA0,0x00,0x00,0x01,0xBC,0xFF,0x40,0x87,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF7,0xE0,0xFF,
0x00,0x00,0x2A,0x10,0xC8,0xA0,0x00,0x00,0x00,0x4F,0xFF,0x40,0x92,0xFF,0xFF,0xFF,0xFF,0xEF,0xCE,0x9E,0x33,0x00,0x68,0xFF,0x24,0xC2,0xFF,0xF5,0xE7,0xA0,0x00,0x00,0x00,0x16,0xD9,0x22,0x92,0xFF,0xD3,0x86,0x3D,0x04,0x00,0x00,0x00,0x00,0x68,0xFF,0x96,0xFF,0xFF,0xFF,0xFF,0xB0,0x00,0x00,0x00,0x00,0x03,0x00,0x68,0xFF,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x68,0xFF,0xE5,0xFF,0xFF,0xFF,0xFF,0x90,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0xFF,0x10,0x00,0x00,0x00,0x00,0x00,0x2E,0x47,0x69,0xFF,
0x98,0xFF,0xFF,0xFF,0xFF,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0xFF,0x10,0x00,0x00,0x00,0x03,0xBE,0xFF,0xFF,0xED,0xFF,0x26,0xC7,0xFF,0xF6,0x97,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0xFF,0x10,0x00,0x00,0x00,0x51,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x2B,0x11,0x00,0x00,0x00,0x00,0x00,0x04,0x52,0x7F,0x6D,0xFF,0x10,0x00,0x00,0x00,0x93,0xFF,0xFF,0xFF,0xFF,0xF9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0xD5,0xFF,0xFF,0xFE,0xFF,0x10,0x00,0x00,0x00,0x36,0xFF,0xFF,0xFF,0xFF,0x86,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x48,0xFF,0xFF,0xFF,0xFF,0xFF,0x10,0x00,0x00,0x00,0x00,0x7E,0xE8,0xF7,0xA3,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6B,0xFF,0xFF,0xFF,0xFF,0xF6,0x05,0x00,0x00,0x00,0x00,0x00,0x05,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x11,0xF6,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x43,0xB2,0xDF,0x7E,0x11,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,


}; // LVGL_9 compatible
const lv_img_dsc_t img_note_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 24,
   .header.h = 24,
   .data_size = sizeof(img_note_icon_data),
   .header.cf = LV_COLOR_FORMAT_NATIVE_WITH_ALPHA,
   .data = img_note_icon_data};

