/**
 * @file    Sentence.h
 * @brief   Sentence class declaration
 * <AUTHOR>
 * @version 0.1
 * @date    2024-12-21
 * 
 * @copyright Copyright (c) 2024
 */

#include <string.h>
#include "type.h"
#include "const.h"
#include <cstdint>

#ifndef __SENTENCE_H__
#define __SENTENCE_H__

#define NMEA_NULL_INTEGER    -999999
#define NMEA_NULL_DOUBLE     -999999.
#define NMEA_NULL_CHAR       '\0'

#define MMSI_BROADCAST       0

class CSentence {    
protected:
    char m_szSentence[MAX_NMEA_LEN];    //!< Variable to store NMEA sentence
    uint8_t m_szTalkerID[4];            //! 2 + 1(NULL)
    int32_t m_nLen;                     //!< Length of the input NMEA sentence
    int32_t m_nFormat;                  //!< Type of NMEA sentence (ABK, ABM, ACA, etc.)

public:
    /**
     * @enum tagNMEAFormatEnum
     * @brief NMEA sentence type
     */
    enum tagNMEAFormatEnum {
        NMEA_UNKNOWN = -1,  //!< Undefined format
        NMEA_ABM = 0,       //!< ABM sentence
        NMEA_ABK,           //!< ABK sentence
        NMEA_ACA,           //!< ACA sentence
        NMEA_ACK,           //!< ACK sentence
        NMEA_ACS,           //!< ACS sentence
        NMEA_AIR,           //!< AIR sentence
        NMEA_ALF,           //!< Alert Sentence(BAM)
        NMEA_ALR,           //!< ALR sentence
        NMEA_BBM,           //!< BBM sentence
        NMEA_GSA,           //!< GSA sentence
        NMEA_GSV,           //!< GSV sentence
        NMEA_LR1,           //!< LR1 sentence
        NMEA_LR2,           //!< LR2 sentence
        NMEA_LR3,           //!< LR3 sentence
        NMEA_LRF,           //!< LRF sentence
        NMEA_LRI,           //!< LRI sentence
        NMEA_SPW,           //!< VSD sentence
        NMEA_SSD,           //!< SSD sentence
        NMEA_TXT,           //!< TXT sentence
        NMEA_VDM,           //!< VDM sentence
        NMEA_VDO,           //!< VDO sentence
        NMEA_VSD,           //!< VSD sentence
        //NMEA_ACN,         //!< Alert command(BAM)
        //NMEA_ALC,         //!< Cyclic alert list(BAM)
        //NMEA_HBT,         //!< Heartbeat supervision sentence (BAM)
        NMEA_COUNT
    };

public:
    /**
     * @fn CSentence::CSentence()
     * @brief Default constructor.
     */
    CSentence() {
        m_szSentence[0] = '\0';
        m_szTalkerID[0] = '\0';
        m_nLen          = 0;
        m_nFormat       = NMEA_UNKNOWN;
    }
    /**
     * @fn CSentence::CSentence(char *pszSentence)
     * @brief Constructor.
     * @param pszSentence NMEA sentence to parse.
     */
    CSentence(const char *pszSentence) {
        SetSentence(pszSentence);
    }
    /**
     * @fn CSentence::~CSentence()
     * @brief Default destructor.
     */
    ~CSentence() {}

    /**
     * @fn void CSentence::SetTalkerID(const uint8_t *pszTalkerID)
     * @brief Set the talker ID.
     * @param pszTalkerID Talker ID to set.
     */
    void   SetTalkerID(const uint8_t *pszTalkerID) { strcpy((char *)m_szTalkerID, (char *)pszTalkerID); }

    /**
     * @fn void CSentence::GetTalkerID(char *pszTalker)
     * @brief Get the talker ID.
     * @param pszTalker Talker ID to get.
     */
    void   GetTalkerID(char *pszTalker/*[3]*/);

    /**
     * @fn void CSentence::GetFormat(char *pszFormat)
     * @brief Get the format.
     * @param pszFormat Format to get.
     */
    void   GetFormat(char *pszFormat/*[4]*/);
    
    int32_t  GetFieldInteger(int nField);
    int32_t  GetFieldHexa(int nField);
    uint32_t GetFieldMMSI(int nField);
    double   GetFieldDouble(int nField);
    int32_t  GetFieldLat(int nField);
    int32_t  GetFieldLon(int nField);
    char     GetFieldChar(int nField);
    int32_t  GetFieldString(int nField, char *pszField, int nBuffLen = 0);

    const char *GetSentence() { return m_szSentence; }

    virtual void SetSentence(const char *pszSentence);
    virtual void Parse();
    virtual int  GetFormat() { return m_nFormat; }
    virtual int  MakeSentence(uint8_t *pszSentence) { return 0; }

    bool          IsValidChecksum();
    static  void  SendMakeNmeaCsData(uint8_t *pData);
    static  int   ComputeChecksum(char *pszSentence);
    static  char *ConvertNMEAString(const char *pszText);
    static  char *ConvertNormalString(const char *pszText);
    static  bool  IsNMEAReservedCharater(char ch);
    /*    *
     * @fn static char xtod(char c)
     * @brief Convert hexadecimal character to decimal.
     * @param c Hexadecimal character to convert.
     * @return Decimal value.
     */
    static  char  xtod(char c) {
        return (c>='0' && c<='9') ? c-'0' : ((c>='A' && c<='F') ? c-'A'+10 : ((c>='a' && c<='f') ? c-'a'+10 : 0));
    }
};

#endif
