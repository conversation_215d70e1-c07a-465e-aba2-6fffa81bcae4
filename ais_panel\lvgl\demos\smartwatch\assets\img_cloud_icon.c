#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: cloud.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_cloud_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA7,0xFE,0x66,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA7,0xFE,0x66,0xFE,0x00,0x00,0x00,0x00,0xC7,0xFE,0xA7,0xFE,0x00,0x00,0x00,0x00,0xC7,0xFE,0x87,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC7,0xFE,0xC7,0xFE,0x00,0x00,0x00,0x00,0xA7,0xFE,0xE8,0xFE,0x00,0x00,0xC7,0xFE,0xC7,0xFE,0x66,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC7,0xFE,0xC7,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC7,0xFE,0xC7,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC7,0xFE,0xC7,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xF5,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x00,0x00,0x00,0x00,0x00,0x00,0x66,0xFE,0xC7,0xFE,0xF6,0xC6,0xFE,0xA6,0xDE,0xA6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0xC7,0xFE,0xC7,0xFE,0xC7,0xFE,0x00,0x00,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0xE0,0xFD,0xE8,0xFE,0xC7,0xFE,0xCE,0xE6,0xFE,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xBF,0xAE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x88,0xFE,0x66,0xFE,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0xE9,0xE5,0xFF,0xA6,0xFC,0xAE,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x82,0xF5,0xFE,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0x7F,0x9E,0x00,0x00,0x00,0x00,0xC7,0xFE,0xC7,0xFE,0xC7,0xFE,0x00,0x00,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0xB9,0xB6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0x1F,0xA7,0x00,0x00,0x00,0x00,
0xA7,0xFE,0xC7,0xFE,0xA7,0xFE,0x00,0x00,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0xB9,0xB6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xDF,0xB6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0xC9,0xD5,0xBE,0x96,0xBE,0x96,0xDF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0x7F,0x9E,
0x00,0x00,0x00,0x00,0xC7,0xFE,0xA5,0xFE,0x80,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x60,0xFD,0x12,0xB6,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0xBF,0x96,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0x00,0x00,0xC7,0xFE,0xC7,0xFE,0xC7,0xFE,0x00,0x00,0xE8,0xE5,0x82,0xF5,0x84,0xE5,0x14,0xA6,0x38,0x96,0x14,0xAE,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0xDF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,
0x00,0x00,0xA7,0xFE,0xA7,0xFE,0x00,0x00,0x00,0x00,0xFF,0xFF,0xBD,0xA6,0x7D,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0xBE,0x96,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0x00,0x00,0x00,0x00,0x00,0x00,0x5E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0xBE,0x96,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xA6,0xFF,0xFF,
0x00,0x00,0x00,0x00,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0xBE,0x96,0xFF,0xA6,0xFF,0xA6,0xFE,0xA6,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x7F,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x9E,0x86,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x9E,0x7F,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x7E,0x86,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,


0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x74,0x0A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x67,0x05,0x00,0x00,0xD0,0x1F,0x00,0x00,0x5C,0x11,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA2,0x72,0x00,0x00,0x9A,0x10,0x00,0x29,0xDF,0x0A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x1A,0x78,0x00,0x00,0x00,0x00,0x00,0x49,0x4A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x22,0x00,0x00,0x00,0x1C,0x7D,0x9C,0x84,0x33,0x00,0x00,0x00,0x0A,0x71,0x2C,0x3C,0x14,0x00,0x00,0x00,0x00,0x00,0x00,0x5E,0xE3,0x3D,0x00,0x56,0xF2,0xFF,0xFF,0xFF,0xFF,0x9D,0x04,0x08,0xDE,0xD7,0xFE,0xFF,0xFD,0x97,0x06,0x00,0x00,0x00,0x00,0x00,0x0B,0x05,0x31,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC3,0x77,0xA4,0xFF,0xFF,0xFF,0xFF,0xFF,0x90,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xA6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF1,0x05,0x00,0x00,0x69,0x77,0x2F,0x00,0xE5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x11,0x00,0x00,0x66,0x77,0x2C,0x00,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x55,0x07,0x00,0x00,0x00,0x00,0x00,0xB5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC1,0x05,
0x00,0x00,0x0D,0x06,0x37,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x56,0x00,0x64,0xE4,0x3B,0x00,0x6F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7B,0x00,0x67,0x1E,0x00,0x00,0x01,0xDA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x4C,0x00,0x00,0x00,0x36,0xC6,0xFC,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xAD,0x01,
0x00,0x00,0x2D,0xF6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0xA7,0x77,0x72,0x43,0x01,0x00,0x00,0x00,0xA5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x65,0x00,0x00,0x00,0x00,0x00,0x00,0xD2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD1,0x00,0x00,0x00,0x00,0x00,0x00,0xB5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDE,0x00,0x00,0x00,0x00,
0x00,0x00,0x46,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5D,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xB7,0x11,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x2A,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x14,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,


}; // LVGL_9 compatible
const lv_img_dsc_t img_cloud_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 24,
   .header.h = 24,
   .data_size = sizeof(img_cloud_icon_data),
   .header.cf = LV_COLOR_FORMAT_NATIVE_WITH_ALPHA,
   .data = img_cloud_icon_data};

