/*******************************************************************************
 * Size: 40 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 40 --font TrumpGothicPro.ttf -r 0x20-0x7F --font ../../../scripts/built_in_font/DejaVuSans.ttf --range 0xFEA9,0xFE8D,0xFEAA,0xFECB,0xFEF9,0xFE95,0xFE8E,0xFEF3,0xFE8B,0xFEBC,0xFEA3,0xFE87,0xFEF4 --font ../../../scripts/built_in_font/SimSun.woff --symbols 语語言標題月日电池今日距离天的速度时间设置蓝牙灯亮度音量最大限度光照强统计三平均高时速简体中文。 --format lvgl -o font_ebike_trump_40.c --force-fast-kern-format
 ******************************************************************************/

#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "../../../lvgl.h"
#endif

#ifndef FONT_EBIKE_TRUMP_40
    #define FONT_EBIKE_TRUMP_40 1
#endif

#if FONT_EBIKE_TRUMP_40

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x6f, 0xf5, 0x5f, 0xf5, 0x5f, 0xf4, 0x5f, 0xf4,
    0x4f, 0xf4, 0x4f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf2,
    0x3f, 0xf2, 0x2f, 0xf2, 0x2f, 0xf1, 0x2f, 0xf1,
    0x1f, 0xf0, 0x1f, 0xf0, 0x1f, 0xf0, 0xf, 0xf0,
    0xf, 0xf0, 0xf, 0xf0, 0xf, 0xe0, 0xf, 0xe0,
    0xe, 0xd0, 0xe, 0xd0, 0xe, 0xd0, 0xd, 0xc0,
    0x8, 0x80, 0x0, 0x0, 0x0, 0x0, 0x3, 0x30,
    0x6f, 0xf5, 0xaf, 0xf9, 0x3e, 0xd2,

    /* U+0022 "\"" */
    0xf, 0xf5, 0x4f, 0xf0, 0xf, 0xf3, 0x3f, 0xf0,
    0xd, 0xf2, 0x2f, 0xe0, 0xc, 0xf1, 0x1f, 0xd0,
    0xb, 0xf0, 0xf, 0xc0, 0xa, 0xf0, 0xe, 0xb0,
    0x9, 0xe0, 0xd, 0x90, 0x8, 0xd0, 0xc, 0x80,
    0x2, 0x40, 0x4, 0x30,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x0, 0x58, 0x50, 0x0, 0x48,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0xcf, 0x80, 0x0, 0x0, 0x0, 0x1, 0xff, 0x30,
    0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x0, 0x4, 0xff, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfb, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf7, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x8f, 0xb0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x33, 0x38, 0xfe, 0x33,
    0x38, 0xfe, 0x33, 0x31, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf6, 0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf2, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x2,
    0x33, 0x8f, 0xe3, 0x33, 0x7f, 0xf3, 0x33, 0x20,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x2, 0xff, 0x10, 0x2, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf9, 0x0,
    0xa, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf5,
    0x0, 0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0x0, 0x2f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xd0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x90, 0x0, 0xaf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x50, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0x1, 0x88, 0x10, 0x0, 0x88, 0x10,
    0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0, 0x9b,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xb5, 0x0, 0x7,
    0xff, 0xff, 0xff, 0x80, 0x2f, 0xfd, 0xcd, 0xdf,
    0xf2, 0x7f, 0xf1, 0x9b, 0x1f, 0xf7, 0x9f, 0xd0,
    0x9b, 0xd, 0xfa, 0xaf, 0xc0, 0x9b, 0xc, 0xfb,
    0xbf, 0xc0, 0x9b, 0xc, 0xfb, 0xbf, 0xc0, 0x9b,
    0xc, 0xfb, 0xbf, 0xc0, 0x9b, 0xc, 0xfb, 0xbf,
    0xc0, 0x9b, 0xc, 0xfb, 0xaf, 0xe0, 0x9b, 0xc,
    0xfb, 0x7f, 0xf3, 0x9b, 0x0, 0x0, 0x2f, 0xfb,
    0x9b, 0x0, 0x0, 0xa, 0xff, 0xeb, 0x0, 0x0,
    0x1, 0xef, 0xfb, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x30, 0x0, 0x0, 0x9f, 0xff,
    0xd0, 0x0, 0x0, 0x9b, 0xaf, 0xf5, 0x12, 0x10,
    0x9b, 0x2f, 0xf9, 0xbf, 0xc0, 0x9b, 0xd, 0xfb,
    0xbf, 0xc0, 0x9b, 0xc, 0xfb, 0xbf, 0xc0, 0x9b,
    0xc, 0xfb, 0xbf, 0xc0, 0x9b, 0xc, 0xfb, 0xaf,
    0xc0, 0x9b, 0xc, 0xfb, 0xaf, 0xc0, 0x9b, 0xc,
    0xfa, 0x8f, 0xe0, 0x9b, 0xe, 0xf8, 0x4f, 0xf6,
    0x9b, 0x6f, 0xf4, 0xe, 0xff, 0xff, 0xff, 0xe0,
    0x4, 0xff, 0xff, 0xff, 0x40, 0x0, 0x16, 0xcd,
    0x61, 0x0, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0,
    0x0, 0x45, 0x0, 0x0,

    /* U+0025 "%" */
    0x6, 0xdf, 0xea, 0x10, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0x4f, 0xfd, 0xff, 0xa0, 0x0,
    0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x3f, 0xf0, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0,
    0x0, 0xdf, 0x50, 0xf, 0xf2, 0x0, 0x0, 0xe,
    0xf1, 0x0, 0x0, 0x0, 0xef, 0x40, 0xe, 0xf4,
    0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0, 0x0, 0xff,
    0x40, 0xe, 0xf5, 0x0, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x0, 0xff, 0x40, 0xe, 0xf5, 0x0, 0x0,
    0xbf, 0x40, 0x0, 0x0, 0x0, 0xff, 0x40, 0xe,
    0xf5, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x40, 0xe, 0xf5, 0x0, 0x4, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x40, 0xe, 0xf5, 0x0,
    0x8, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xff, 0x40,
    0xe, 0xf5, 0x0, 0xc, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x40, 0xe, 0xf5, 0x0, 0x1f, 0xf0,
    0x3, 0xbf, 0xfc, 0x30, 0xff, 0x40, 0xe, 0xf5,
    0x0, 0x5f, 0xb0, 0xe, 0xff, 0xff, 0xe0, 0xff,
    0x40, 0xe, 0xf5, 0x0, 0x9f, 0x70, 0x4f, 0xe2,
    0x2e, 0xf4, 0xff, 0x40, 0xe, 0xf4, 0x0, 0xdf,
    0x20, 0x7f, 0xa0, 0xa, 0xf7, 0xef, 0x40, 0xe,
    0xf4, 0x1, 0xfe, 0x0, 0x9f, 0x90, 0x9, 0xf9,
    0xdf, 0x50, 0xf, 0xf2, 0x6, 0xfa, 0x0, 0xaf,
    0x90, 0x8, 0xf9, 0xaf, 0xb1, 0x6f, 0xf0, 0xa,
    0xf6, 0x0, 0xaf, 0x90, 0x8, 0xfa, 0x4f, 0xff,
    0xff, 0xa0, 0xe, 0xf2, 0x0, 0xaf, 0x90, 0x8,
    0xfa, 0x6, 0xdf, 0xe9, 0x10, 0x2f, 0xd0, 0x0,
    0xaf, 0x90, 0x8, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0xaf, 0x90, 0x8, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0xaf, 0x90,
    0x8, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0xaf, 0x90, 0x8, 0xfa, 0x0, 0x0, 0x0,
    0x3, 0xfc, 0x0, 0x0, 0xaf, 0x90, 0x8, 0xfa,
    0x0, 0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0xaf,
    0x90, 0x8, 0xfa, 0x0, 0x0, 0x0, 0xc, 0xf4,
    0x0, 0x0, 0xaf, 0x90, 0x8, 0xf9, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x9f, 0x90, 0x9,
    0xf9, 0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0,
    0x7f, 0xa0, 0xa, 0xf7, 0x0, 0x0, 0x0, 0x9f,
    0x70, 0x0, 0x0, 0x4f, 0xe2, 0x2e, 0xf4, 0x0,
    0x0, 0x0, 0xdf, 0x30, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xfc, 0x30,

    /* U+0026 "&" */
    0x0, 0x2, 0x67, 0x51, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x50, 0x0, 0x7, 0xff, 0xfd, 0xff,
    0xf2, 0x0, 0xe, 0xfd, 0x10, 0x3f, 0xf9, 0x0,
    0x2f, 0xf5, 0x0, 0x9, 0xfd, 0x0, 0x4f, 0xf2,
    0x0, 0x7, 0xff, 0x0, 0x5f, 0xf2, 0x0, 0x6,
    0xff, 0x10, 0x5f, 0xf2, 0x0, 0x5, 0xee, 0x10,
    0x5f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x3, 0x88, 0x0, 0xf, 0xf7,
    0x0, 0x6, 0xff, 0x10, 0x8, 0xff, 0x72, 0x6,
    0xff, 0x10, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x2a, 0xff, 0xff, 0xff, 0xf5, 0x4, 0xff,
    0xe9, 0x7a, 0xff, 0x72, 0xc, 0xfa, 0x0, 0x6,
    0xff, 0x10, 0x2f, 0xf3, 0x0, 0x6, 0xff, 0x10,
    0x4f, 0xf2, 0x0, 0x6, 0xff, 0x10, 0x5f, 0xf2,
    0x0, 0x6, 0xff, 0x10, 0x5f, 0xf2, 0x0, 0x6,
    0xff, 0x10, 0x5f, 0xf2, 0x0, 0x6, 0xff, 0x10,
    0x5f, 0xf2, 0x0, 0x6, 0xff, 0x10, 0x5f, 0xf2,
    0x0, 0x6, 0xff, 0x10, 0x5f, 0xf2, 0x0, 0x6,
    0xff, 0x10, 0x5f, 0xf2, 0x0, 0x7, 0xff, 0x0,
    0x3f, 0xf5, 0x0, 0x9, 0xfe, 0x0, 0xe, 0xfc,
    0x10, 0x3f, 0xfa, 0x0, 0x8, 0xff, 0xfd, 0xff,
    0xf3, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x2, 0x67, 0x51, 0x0, 0x0,

    /* U+0027 "'" */
    0xf, 0xf5, 0xf, 0xf3, 0xd, 0xf2, 0xc, 0xf1,
    0xb, 0xf0, 0xa, 0xf0, 0x9, 0xe0, 0x8, 0xd0,
    0x2, 0x40,

    /* U+0028 "(" */
    0x0, 0x2, 0xff, 0x40, 0x0, 0x7f, 0xe0, 0x0,
    0xc, 0xf9, 0x0, 0x1, 0xff, 0x40, 0x0, 0x5f,
    0xf0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0xcf, 0x80,
    0x0, 0xf, 0xf5, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x6f, 0xf0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0xaf,
    0xc0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0xef, 0x80,
    0x0, 0xf, 0xf7, 0x0, 0x0, 0xff, 0x60, 0x0,
    0x1f, 0xf5, 0x0, 0x1, 0xff, 0x50, 0x0, 0x2f,
    0xf4, 0x0, 0x2, 0xff, 0x40, 0x0, 0x1f, 0xf5,
    0x0, 0x0, 0xff, 0x60, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0xff, 0x70, 0x0, 0xd, 0xf9, 0x0, 0x0,
    0xbf, 0xb0, 0x0, 0x9, 0xfd, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x1f, 0xf4,
    0x0, 0x0, 0xef, 0x70, 0x0, 0xa, 0xfa, 0x0,
    0x0, 0x7f, 0xe0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x4,
    0xff, 0x10, 0x0, 0x8, 0x82,

    /* U+0029 ")" */
    0x9f, 0xd0, 0x0, 0x3f, 0xf1, 0x0, 0xe, 0xf6,
    0x0, 0x9, 0xfb, 0x0, 0x5, 0xff, 0x0, 0x1,
    0xff, 0x30, 0x0, 0xdf, 0x70, 0x0, 0xaf, 0xa0,
    0x0, 0x7f, 0xe0, 0x0, 0x5f, 0xf1, 0x0, 0x3f,
    0xf3, 0x0, 0x1f, 0xf5, 0x0, 0xf, 0xf7, 0x0,
    0xd, 0xf9, 0x0, 0xc, 0xfa, 0x0, 0xb, 0xfb,
    0x0, 0xb, 0xfb, 0x0, 0xa, 0xfc, 0x0, 0x9,
    0xfd, 0x0, 0xa, 0xfc, 0x0, 0xa, 0xfc, 0x0,
    0xb, 0xfb, 0x0, 0xc, 0xfa, 0x0, 0xc, 0xfa,
    0x0, 0xe, 0xf8, 0x0, 0xf, 0xf6, 0x0, 0x2f,
    0xf4, 0x0, 0x4f, 0xf2, 0x0, 0x6f, 0xf0, 0x0,
    0x9f, 0xc0, 0x0, 0xcf, 0x90, 0x0, 0xff, 0x50,
    0x3, 0xff, 0x20, 0x7, 0xfd, 0x0, 0xc, 0xf9,
    0x0, 0x1f, 0xf4, 0x0, 0x6f, 0xf0, 0x0, 0x58,
    0x50, 0x0,

    /* U+002A "*" */
    0x0, 0x17, 0x0, 0x17, 0x0, 0x0, 0xc, 0xf4,
    0x9, 0xf8, 0x0, 0x0, 0x2f, 0xd1, 0xfd, 0x0,
    0x0, 0x0, 0x7f, 0xcf, 0x30, 0x0, 0x3c, 0xcc,
    0xff, 0xfc, 0xcc, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x3, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0xdf, 0x5f, 0xb0, 0x0, 0x0, 0x9f, 0x80, 0xbf,
    0x60, 0x0, 0x4, 0xc1, 0x3, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x7b, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x46, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x12, 0x22, 0x22, 0xbf,
    0x92, 0x22, 0x22, 0x0, 0x0, 0x0, 0xa, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0,
    0x0,

    /* U+002C "," */
    0x0, 0x7, 0x87, 0x0, 0x3, 0xff, 0x70, 0x0,
    0x9f, 0xe0, 0x0, 0xe, 0xf6, 0x0, 0x4, 0xfd,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0xf, 0xd0, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,

    /* U+002D "-" */
    0x23, 0x33, 0x31, 0xbf, 0xff, 0xf7, 0xbf, 0xff,
    0xf7,

    /* U+002E "." */
    0x3, 0x30, 0x6f, 0xf5, 0xaf, 0xf9, 0x3e, 0xd2,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0x0, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x80, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xc0, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4f,
    0xf0, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0xf,
    0xf3, 0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0, 0x0, 0xc,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0, 0x0, 0x8,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x5f, 0xf0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x2, 0x67, 0x61, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xf7, 0x0, 0x7, 0xff, 0xfd, 0xff, 0xf4,
    0x0, 0xef, 0xd1, 0x2, 0xff, 0xa0, 0x1f, 0xf5,
    0x0, 0x8, 0xfe, 0x3, 0xff, 0x30, 0x0, 0x6f,
    0xf0, 0x4f, 0xf2, 0x0, 0x5, 0xff, 0x15, 0xff,
    0x20, 0x0, 0x5f, 0xf1, 0x5f, 0xf2, 0x0, 0x5,
    0xff, 0x15, 0xff, 0x20, 0x0, 0x5f, 0xf1, 0x5f,
    0xf2, 0x0, 0x5, 0xff, 0x15, 0xff, 0x20, 0x0,
    0x5f, 0xf1, 0x5f, 0xf2, 0x0, 0x5, 0xff, 0x15,
    0xff, 0x20, 0x0, 0x5f, 0xf1, 0x5f, 0xf2, 0x0,
    0x5, 0xff, 0x15, 0xff, 0x20, 0x0, 0x5f, 0xf1,
    0x5f, 0xf2, 0x0, 0x5, 0xff, 0x15, 0xff, 0x20,
    0x0, 0x5f, 0xf1, 0x5f, 0xf2, 0x0, 0x5, 0xff,
    0x15, 0xff, 0x20, 0x0, 0x5f, 0xf1, 0x5f, 0xf2,
    0x0, 0x5, 0xff, 0x15, 0xff, 0x20, 0x0, 0x5f,
    0xf1, 0x5f, 0xf2, 0x0, 0x5, 0xff, 0x15, 0xff,
    0x20, 0x0, 0x5f, 0xf1, 0x5f, 0xf2, 0x0, 0x5,
    0xff, 0x15, 0xff, 0x20, 0x0, 0x5f, 0xf1, 0x4f,
    0xf2, 0x0, 0x5, 0xff, 0x13, 0xff, 0x30, 0x0,
    0x6f, 0xf0, 0x1f, 0xf5, 0x0, 0x9, 0xfe, 0x0,
    0xef, 0xd1, 0x2, 0xff, 0xa0, 0x7, 0xff, 0xfd,
    0xff, 0xf3, 0x0, 0x9, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x2, 0x67, 0x61, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x19, 0xff, 0x62, 0x9f, 0xff, 0xf6, 0xcf,
    0xff, 0xff, 0x6c, 0xe8, 0x3f, 0xf6, 0x30, 0x1,
    0xff, 0x60, 0x0, 0x1f, 0xf6, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x1f, 0xf6, 0x0, 0x1, 0xff, 0x60,
    0x0, 0x1f, 0xf6, 0x0, 0x1, 0xff, 0x60, 0x0,
    0x1f, 0xf6, 0x0, 0x1, 0xff, 0x60, 0x0, 0x1f,
    0xf6, 0x0, 0x1, 0xff, 0x60, 0x0, 0x1f, 0xf6,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x1f, 0xf6, 0x0,
    0x1, 0xff, 0x60, 0x0, 0x1f, 0xf6, 0x0, 0x1,
    0xff, 0x60, 0x0, 0x1f, 0xf6, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x1f, 0xf6, 0x0, 0x1, 0xff, 0x60,
    0x0, 0x1f, 0xf6, 0x0, 0x1, 0xff, 0x60, 0x0,
    0x1f, 0xf6, 0x0, 0x1, 0xff, 0x60, 0x0, 0x1f,
    0xf6, 0x0, 0x1, 0xff, 0x60,

    /* U+0032 "2" */
    0x0, 0x5, 0x77, 0x40, 0x0, 0x3, 0xef, 0xff,
    0xfc, 0x10, 0xe, 0xff, 0xde, 0xff, 0xc0, 0x6f,
    0xf7, 0x0, 0x9f, 0xf3, 0xaf, 0xd0, 0x0, 0xf,
    0xf7, 0xcf, 0xb0, 0x0, 0xd, 0xf9, 0xcf, 0xa0,
    0x0, 0xd, 0xfa, 0xdf, 0xa0, 0x0, 0xd, 0xfa,
    0xdf, 0xa0, 0x0, 0xd, 0xfa, 0xdf, 0xa0, 0x0,
    0xd, 0xfa, 0xdf, 0xa0, 0x0, 0xe, 0xfa, 0x23,
    0x20, 0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x6, 0xff, 0x50,
    0x0, 0x0, 0xe, 0xfe, 0x0, 0x0, 0x0, 0x6f,
    0xf7, 0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0,
    0x8, 0xff, 0x70, 0x0, 0x0, 0x1f, 0xfe, 0x0,
    0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0, 0x1, 0xff,
    0xe0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0, 0x0,
    0xe, 0xff, 0x10, 0x0, 0x0, 0x4f, 0xfa, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0xdf, 0xe7, 0x77, 0x77, 0x70, 0xdf, 0xff,
    0xff, 0xff, 0xe0, 0xdf, 0xff, 0xff, 0xff, 0xe0,

    /* U+0033 "3" */
    0x0, 0x4, 0x77, 0x40, 0x0, 0x2, 0xdf, 0xff,
    0xfd, 0x10, 0xd, 0xff, 0xde, 0xff, 0xc0, 0x5f,
    0xf7, 0x0, 0x9f, 0xf3, 0x9f, 0xe0, 0x0, 0xf,
    0xf7, 0xbf, 0xc0, 0x0, 0xd, 0xfa, 0xcf, 0xb0,
    0x0, 0xc, 0xfa, 0xcf, 0xb0, 0x0, 0xc, 0xfb,
    0xcf, 0xb0, 0x0, 0xc, 0xfb, 0xcf, 0xb0, 0x0,
    0xc, 0xfb, 0xcf, 0xb0, 0x0, 0xc, 0xfa, 0x12,
    0x10, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0xd,
    0xf8, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0x0, 0x0,
    0x15, 0xdf, 0xa0, 0x0, 0xf, 0xff, 0xd6, 0x0,
    0x0, 0xf, 0xff, 0xf9, 0x0, 0x0, 0x2, 0x38,
    0xef, 0xb0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0,
    0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0, 0xc,
    0xf9, 0x0, 0x0, 0x0, 0xc, 0xfa, 0xad, 0x90,
    0x0, 0xc, 0xfb, 0xcf, 0xb0, 0x0, 0xc, 0xfb,
    0xcf, 0xb0, 0x0, 0xc, 0xfb, 0xcf, 0xb0, 0x0,
    0xc, 0xfb, 0xcf, 0xb0, 0x0, 0xc, 0xfa, 0xbf,
    0xc0, 0x0, 0xd, 0xfa, 0x9f, 0xe0, 0x0, 0xf,
    0xf8, 0x5f, 0xf7, 0x0, 0x8f, 0xf3, 0xd, 0xff,
    0xee, 0xff, 0xc0, 0x2, 0xdf, 0xff, 0xfd, 0x10,
    0x0, 0x4, 0x77, 0x40, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x8, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0xf0, 0x0,
    0x0, 0x0, 0x9f, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0xdf, 0x7f, 0xf0, 0x0, 0x0, 0x1, 0xfb, 0x7f,
    0xf0, 0x0, 0x0, 0x5, 0xf8, 0x7f, 0xf0, 0x0,
    0x0, 0x9, 0xf5, 0x7f, 0xf0, 0x0, 0x0, 0xe,
    0xf1, 0x7f, 0xf0, 0x0, 0x0, 0x2f, 0xe0, 0x7f,
    0xf0, 0x0, 0x0, 0x6f, 0xb0, 0x7f, 0xf0, 0x0,
    0x0, 0xaf, 0x70, 0x7f, 0xf0, 0x0, 0x0, 0xef,
    0x40, 0x7f, 0xf0, 0x0, 0x3, 0xff, 0x10, 0x7f,
    0xf0, 0x0, 0x7, 0xfd, 0x0, 0x7f, 0xf0, 0x0,
    0xb, 0xfa, 0x0, 0x7f, 0xf0, 0x0, 0xf, 0xf7,
    0x0, 0x7f, 0xf0, 0x0, 0x3f, 0xf3, 0x0, 0x7f,
    0xf0, 0x0, 0x8f, 0xf0, 0x0, 0x7f, 0xf0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x47, 0x77, 0x77, 0xbf,
    0xf7, 0x73, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0,

    /* U+0035 "5" */
    0x7f, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xff,
    0xff, 0xf0, 0x7f, 0xf8, 0x77, 0x77, 0x70, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x6d, 0xfc, 0x20, 0x7f, 0xf8, 0xff, 0xff,
    0xe1, 0x7f, 0xff, 0x74, 0xaf, 0xf9, 0x7f, 0xf7,
    0x0, 0xc, 0xfd, 0x7f, 0xf2, 0x0, 0x8, 0xff,
    0x49, 0x90, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0x7, 0xff, 0x13, 0x30,
    0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff,
    0x7f, 0xf0, 0x0, 0x7, 0xff, 0x6f, 0xf0, 0x0,
    0x7, 0xff, 0x6f, 0xf1, 0x0, 0x8, 0xff, 0x4f,
    0xf4, 0x0, 0xb, 0xfd, 0xf, 0xfc, 0x0, 0x4f,
    0xf9, 0x8, 0xff, 0xfd, 0xff, 0xf2, 0x0, 0xaf,
    0xff, 0xff, 0x50, 0x0, 0x3, 0x67, 0x51, 0x0,

    /* U+0036 "6" */
    0x0, 0x2, 0x67, 0x61, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xf6, 0x0, 0x6, 0xff, 0xfd, 0xff, 0xf4,
    0x0, 0xdf, 0xd1, 0x3, 0xff, 0xb0, 0x1f, 0xf6,
    0x0, 0x9, 0xfe, 0x3, 0xff, 0x30, 0x0, 0x6f,
    0xf0, 0x4f, 0xf2, 0x0, 0x5, 0xff, 0x14, 0xff,
    0x20, 0x0, 0x5f, 0xf1, 0x4f, 0xf2, 0x0, 0x5,
    0xff, 0x14, 0xff, 0x20, 0x0, 0x39, 0x90, 0x4f,
    0xf2, 0x0, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x21, 0x0, 0x4,
    0xff, 0x28, 0xff, 0xf7, 0x0, 0x4f, 0xfb, 0xff,
    0xff, 0xf5, 0x4, 0xff, 0xf4, 0x4, 0xff, 0xd0,
    0x4f, 0xf8, 0x0, 0x9, 0xff, 0x4, 0xff, 0x40,
    0x0, 0x6f, 0xf2, 0x4f, 0xf3, 0x0, 0x5, 0xff,
    0x24, 0xff, 0x30, 0x0, 0x5f, 0xf2, 0x4f, 0xf3,
    0x0, 0x5, 0xff, 0x24, 0xff, 0x30, 0x0, 0x5f,
    0xf2, 0x4f, 0xf3, 0x0, 0x5, 0xff, 0x24, 0xff,
    0x30, 0x0, 0x5f, 0xf2, 0x4f, 0xf3, 0x0, 0x5,
    0xff, 0x24, 0xff, 0x30, 0x0, 0x5f, 0xf2, 0x4f,
    0xf3, 0x0, 0x5, 0xff, 0x23, 0xff, 0x30, 0x0,
    0x5f, 0xf1, 0x1f, 0xf6, 0x0, 0x8, 0xff, 0x0,
    0xdf, 0xd1, 0x2, 0xef, 0xc0, 0x6, 0xff, 0xfd,
    0xff, 0xf5, 0x0, 0x8, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x2, 0x67, 0x62, 0x0, 0x0,

    /* U+0037 "7" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x17, 0x77, 0x77, 0x77,
    0xbf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0x0, 0x9,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xe, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x2, 0x67, 0x51, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0x40, 0x8, 0xff, 0xfd, 0xff, 0xf2, 0xf,
    0xfc, 0x0, 0x4f, 0xf8, 0x3f, 0xf4, 0x0, 0xb,
    0xfc, 0x6f, 0xf1, 0x0, 0x8, 0xff, 0x6f, 0xf0,
    0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff,
    0x7f, 0xf0, 0x0, 0x7, 0xff, 0x6f, 0xf0, 0x0,
    0x7, 0xff, 0x5f, 0xf0, 0x0, 0x7, 0xfe, 0x4f,
    0xf1, 0x0, 0x8, 0xfd, 0xe, 0xf3, 0x0, 0xa,
    0xf8, 0x7, 0xfc, 0x20, 0x5f, 0xe1, 0x0, 0x7f,
    0xff, 0xfd, 0x30, 0x0, 0x9f, 0xff, 0xfe, 0x30,
    0x7, 0xfd, 0x53, 0x8f, 0xe0, 0xf, 0xf4, 0x0,
    0xb, 0xf7, 0x4f, 0xf1, 0x0, 0x8, 0xfc, 0x6f,
    0xf0, 0x0, 0x7, 0xfe, 0x7f, 0xf0, 0x0, 0x7,
    0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0,
    0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff,
    0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0,
    0x7, 0xff, 0x6f, 0xf0, 0x0, 0x7, 0xff, 0x6f,
    0xf1, 0x0, 0x8, 0xff, 0x4f, 0xf4, 0x0, 0xa,
    0xfd, 0xf, 0xfc, 0x0, 0x4f, 0xf9, 0x8, 0xff,
    0xfd, 0xff, 0xf2, 0x0, 0xaf, 0xff, 0xff, 0x50,
    0x0, 0x2, 0x67, 0x51, 0x0,

    /* U+0039 "9" */
    0x0, 0x3, 0x77, 0x50, 0x0, 0x1, 0xcf, 0xff,
    0xfe, 0x40, 0xa, 0xff, 0xed, 0xff, 0xf1, 0x1f,
    0xfb, 0x0, 0x5f, 0xf7, 0x5f, 0xf3, 0x0, 0xc,
    0xfb, 0x7f, 0xf0, 0x0, 0x9, 0xfd, 0x8f, 0xf0,
    0x0, 0x9, 0xfe, 0x8f, 0xf0, 0x0, 0x9, 0xfe,
    0x8f, 0xf0, 0x0, 0x9, 0xfe, 0x8f, 0xf0, 0x0,
    0x9, 0xfe, 0x8f, 0xf0, 0x0, 0x9, 0xfe, 0x8f,
    0xf0, 0x0, 0x9, 0xfe, 0x8f, 0xf0, 0x0, 0x9,
    0xfe, 0x8f, 0xf0, 0x0, 0x9, 0xfe, 0x8f, 0xf0,
    0x0, 0x9, 0xfe, 0x8f, 0xf0, 0x0, 0x9, 0xfe,
    0x7f, 0xf1, 0x0, 0xb, 0xfe, 0x5f, 0xf5, 0x0,
    0xf, 0xfe, 0x1f, 0xfe, 0x65, 0xcf, 0xfe, 0x8,
    0xff, 0xff, 0xe9, 0xfe, 0x0, 0x7e, 0xfb, 0x28,
    0xfe, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x0, 0x0,
    0x0, 0x8, 0xfe, 0x1, 0x10, 0x0, 0x8, 0xfe,
    0x7f, 0xf0, 0x0, 0x8, 0xfe, 0x7f, 0xf0, 0x0,
    0x8, 0xfe, 0x7f, 0xf0, 0x0, 0x8, 0xfe, 0x6f,
    0xf0, 0x0, 0x8, 0xfd, 0x5f, 0xf3, 0x0, 0xb,
    0xfc, 0x1f, 0xfb, 0x0, 0x4f, 0xf8, 0xb, 0xff,
    0xed, 0xff, 0xf2, 0x1, 0xcf, 0xff, 0xfe, 0x40,
    0x0, 0x3, 0x77, 0x50, 0x0,

    /* U+003A ":" */
    0x3, 0x30, 0x6f, 0xf5, 0xaf, 0xf9, 0x3e, 0xd2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x30, 0x6f, 0xf5,
    0xaf, 0xf9, 0x3e, 0xd2,

    /* U+003B ";" */
    0x0, 0x0, 0x42, 0x0, 0x0, 0xaf, 0xf3, 0x0,
    0xd, 0xff, 0x60, 0x0, 0x5e, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xa9, 0x0, 0x1, 0xff, 0x90,
    0x0, 0x7f, 0xf1, 0x0, 0xc, 0xf8, 0x0, 0x2,
    0xff, 0x10, 0x0, 0x8f, 0x70, 0x0, 0xd, 0xe0,
    0x0, 0x3, 0xf6, 0x0, 0x0, 0x8e, 0x0, 0x0,
    0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+003D "=" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40,

    /* U+003E ">" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x9, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x90, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x1, 0x67, 0x62, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xf7, 0x0, 0x4, 0xff, 0xfd, 0xff, 0xf5,
    0x0, 0xcf, 0xe2, 0x2, 0xef, 0xc0, 0xf, 0xf7,
    0x0, 0x7, 0xff, 0x2, 0xff, 0x40, 0x0, 0x5f,
    0xf2, 0x3f, 0xf4, 0x0, 0x4, 0xff, 0x33, 0xff,
    0x40, 0x0, 0x4f, 0xf3, 0x3f, 0xf4, 0x0, 0x4,
    0xff, 0x33, 0xff, 0x40, 0x0, 0x4f, 0xf3, 0x3f,
    0xf4, 0x0, 0x4, 0xff, 0x30, 0x22, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x9f, 0xe0, 0x0, 0x0, 0x2,
    0x8f, 0xf9, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0x10,
    0x0, 0x6, 0xff, 0xea, 0x10, 0x0, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x4,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0,
    0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xb0, 0x0, 0x0, 0x0, 0x2, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x42, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x4e, 0xd1, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x32,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4a, 0xff, 0xff, 0xfd, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xfb, 0x51, 0x1, 0x37,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfd, 0x30, 0x0, 0x0, 0x0, 0x9, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x1e, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfe, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x60,
    0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0xb, 0xfd,
    0x0, 0x0, 0x0, 0x9e, 0xd4, 0x4a, 0xa0, 0x0,
    0x4f, 0xf1, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xbf, 0xc0, 0x0, 0xf, 0xf5, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0xe, 0xfe, 0x69, 0xff,
    0x90, 0x0, 0xd, 0xf8, 0x0, 0xdf, 0x90, 0x0,
    0x0, 0x3f, 0xf4, 0x2, 0xff, 0x50, 0x0, 0xa,
    0xfa, 0x2, 0xff, 0x30, 0x0, 0x0, 0x7f, 0xe0,
    0x4, 0xff, 0x20, 0x0, 0x9, 0xfb, 0x6, 0xff,
    0x0, 0x0, 0x0, 0xbf, 0xb0, 0x6, 0xff, 0x0,
    0x0, 0x9, 0xfc, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0xef, 0x70, 0xa, 0xfb, 0x0, 0x0, 0x9, 0xfb,
    0xc, 0xf8, 0x0, 0x0, 0x2, 0xff, 0x40, 0xd,
    0xf8, 0x0, 0x0, 0xa, 0xfa, 0xe, 0xf5, 0x0,
    0x0, 0x5, 0xff, 0x0, 0xf, 0xf5, 0x0, 0x0,
    0xd, 0xf8, 0xf, 0xf3, 0x0, 0x0, 0x8, 0xfd,
    0x0, 0x3f, 0xf1, 0x0, 0x0, 0xf, 0xf6, 0x1f,
    0xf2, 0x0, 0x0, 0xc, 0xfa, 0x0, 0x7f, 0xe0,
    0x0, 0x0, 0x2f, 0xf3, 0x2f, 0xf1, 0x0, 0x0,
    0xf, 0xf6, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x6f,
    0xf0, 0x3f, 0xf0, 0x0, 0x0, 0x2f, 0xf3, 0x0,
    0xef, 0x70, 0x0, 0x0, 0xbf, 0xa0, 0x2f, 0xf1,
    0x0, 0x0, 0x5f, 0xf0, 0x1, 0xff, 0x40, 0x0,
    0x1, 0xff, 0x40, 0x1f, 0xf2, 0x0, 0x0, 0x8f,
    0xd0, 0x5, 0xff, 0x10, 0x0, 0x8, 0xfd, 0x0,
    0xf, 0xf3, 0x0, 0x0, 0xaf, 0xb0, 0xb, 0xfe,
    0x0, 0x0, 0x2f, 0xf4, 0x0, 0xe, 0xf5, 0x0,
    0x0, 0xbf, 0xe4, 0x8e, 0xfe, 0x0, 0x0, 0xdf,
    0xa0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0xaf, 0xff,
    0xf4, 0xbf, 0x72, 0x5d, 0xfc, 0x0, 0x0, 0x8,
    0xfc, 0x0, 0x0, 0x3d, 0xea, 0x20, 0x2e, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x67, 0x51, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfd, 0x30, 0x0, 0x0, 0x0, 0x18, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfd, 0x85,
    0x44, 0x7a, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xce, 0xff, 0xda, 0x50, 0x0, 0x0, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x6,
    0xfc, 0xfe, 0x0, 0x0, 0x0, 0x8, 0xf9, 0xff,
    0x0, 0x0, 0x0, 0xa, 0xf7, 0xef, 0x20, 0x0,
    0x0, 0xc, 0xf5, 0xcf, 0x40, 0x0, 0x0, 0xe,
    0xf3, 0xaf, 0x70, 0x0, 0x0, 0xf, 0xf1, 0x9f,
    0x90, 0x0, 0x0, 0x2f, 0xf0, 0x7f, 0xb0, 0x0,
    0x0, 0x4f, 0xe0, 0x5f, 0xd0, 0x0, 0x0, 0x7f,
    0xc0, 0x3f, 0xf0, 0x0, 0x0, 0x9f, 0xa0, 0x1f,
    0xf1, 0x0, 0x0, 0xbf, 0x80, 0xf, 0xf3, 0x0,
    0x0, 0xdf, 0x60, 0xe, 0xf6, 0x0, 0x0, 0xff,
    0x50, 0xc, 0xf8, 0x0, 0x1, 0xff, 0x30, 0xa,
    0xfa, 0x0, 0x3, 0xff, 0x10, 0x8, 0xfc, 0x0,
    0x5, 0xff, 0x0, 0x6, 0xfe, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0x30, 0xc, 0xfa, 0x44, 0x44, 0xff, 0x50,
    0xe, 0xf7, 0x0, 0x0, 0xef, 0x70, 0xf, 0xf5,
    0x0, 0x0, 0xcf, 0x90, 0x2f, 0xf3, 0x0, 0x0,
    0xaf, 0xb0, 0x4f, 0xf1, 0x0, 0x0, 0x8f, 0xd0,
    0x6f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x9f, 0xe0,
    0x0, 0x0, 0x5f, 0xf2, 0xbf, 0xc0, 0x0, 0x0,
    0x3f, 0xf4,

    /* U+0042 "B" */
    0x7f, 0xff, 0xff, 0xc7, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x7f, 0xf8, 0x78, 0xef, 0xf4,
    0x7, 0xff, 0x0, 0x1, 0xff, 0x80, 0x7f, 0xf0,
    0x0, 0xb, 0xfb, 0x7, 0xff, 0x0, 0x0, 0xaf,
    0xc0, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7, 0xff,
    0x0, 0x0, 0xaf, 0xc0, 0x7f, 0xf0, 0x0, 0xa,
    0xfc, 0x7, 0xff, 0x0, 0x0, 0xaf, 0xc0, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x7, 0xff, 0x0, 0x0,
    0xbf, 0xa0, 0x7f, 0xf0, 0x0, 0xd, 0xf7, 0x7,
    0xff, 0x0, 0x18, 0xff, 0x10, 0x7f, 0xff, 0xff,
    0xfd, 0x30, 0x7, 0xff, 0xff, 0xff, 0xa2, 0x0,
    0x7f, 0xf8, 0x78, 0xdf, 0xe2, 0x7, 0xff, 0x0,
    0x0, 0xcf, 0x90, 0x7f, 0xf0, 0x0, 0x6, 0xff,
    0x7, 0xff, 0x0, 0x0, 0x4f, 0xf1, 0x7f, 0xf0,
    0x0, 0x4, 0xff, 0x27, 0xff, 0x0, 0x0, 0x4f,
    0xf3, 0x7f, 0xf0, 0x0, 0x4, 0xff, 0x37, 0xff,
    0x0, 0x0, 0x4f, 0xf3, 0x7f, 0xf0, 0x0, 0x4,
    0xff, 0x27, 0xff, 0x0, 0x0, 0x4f, 0xf2, 0x7f,
    0xf0, 0x0, 0x6, 0xff, 0x17, 0xff, 0x0, 0x0,
    0xbf, 0xe0, 0x7f, 0xf8, 0x78, 0xbf, 0xfa, 0x7,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x7f, 0xff, 0xff,
    0xe9, 0x20, 0x0,

    /* U+0043 "C" */
    0x0, 0x4, 0x77, 0x50, 0x0, 0x1, 0xcf, 0xff,
    0xfe, 0x40, 0xb, 0xff, 0xed, 0xff, 0xe1, 0x1f,
    0xfa, 0x0, 0x5f, 0xf6, 0x5f, 0xf1, 0x0, 0xc,
    0xfa, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x8f, 0xe0,
    0x0, 0x9, 0xfd, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f,
    0xe0, 0x0, 0x7, 0xca, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x8f,
    0xe0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x8f, 0xe0, 0x0, 0x3, 0x65, 0x8f, 0xe0,
    0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfd, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x5f, 0xf2, 0x0, 0xc,
    0xfa, 0x1f, 0xfa, 0x0, 0x5f, 0xf6, 0xa, 0xff,
    0xed, 0xff, 0xe1, 0x1, 0xcf, 0xff, 0xfe, 0x40,
    0x0, 0x4, 0x77, 0x50, 0x0,

    /* U+0044 "D" */
    0x7f, 0xff, 0xff, 0xc7, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xb0, 0x7f, 0xf8, 0x78, 0xef, 0xf4, 0x7f,
    0xf0, 0x0, 0x1f, 0xf8, 0x7f, 0xf0, 0x0, 0xc,
    0xfb, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0,
    0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc,
    0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0,
    0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa,
    0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0,
    0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc,
    0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0,
    0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa,
    0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0,
    0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc,
    0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0,
    0xb, 0xfc, 0x7f, 0xf0, 0x0, 0xc, 0xfb, 0x7f,
    0xf0, 0x0, 0x1f, 0xf8, 0x7f, 0xf8, 0x78, 0xef,
    0xf4, 0x7f, 0xff, 0xff, 0xff, 0xb0, 0x7f, 0xff,
    0xff, 0xd7, 0x0,

    /* U+0045 "E" */
    0x7f, 0xff, 0xff, 0xff, 0xb7, 0xff, 0xff, 0xff,
    0xfb, 0x7f, 0xf8, 0x77, 0x77, 0x57, 0xff, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7f,
    0xf8, 0x77, 0x77, 0x37, 0xff, 0xff, 0xff, 0xf7,
    0x7f, 0xff, 0xff, 0xff, 0x77, 0xff, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x77, 0x77, 0x57, 0xff, 0xff, 0xff, 0xfb, 0x7f,
    0xff, 0xff, 0xff, 0xb0,

    /* U+0046 "F" */
    0x7f, 0xff, 0xff, 0xff, 0xb7, 0xff, 0xff, 0xff,
    0xfb, 0x7f, 0xf8, 0x77, 0x77, 0x57, 0xff, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7f,
    0xf8, 0x77, 0x77, 0x37, 0xff, 0xff, 0xff, 0xf7,
    0x7f, 0xff, 0xff, 0xff, 0x77, 0xff, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3, 0x67, 0x62, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xf8, 0x0, 0xa, 0xff, 0xed, 0xff, 0xf6,
    0x1, 0xff, 0xa0, 0x1, 0xdf, 0xd0, 0x5f, 0xf2,
    0x0, 0x6, 0xff, 0x17, 0xff, 0x0, 0x0, 0x3f,
    0xf3, 0x8f, 0xe0, 0x0, 0x3, 0xff, 0x48, 0xfe,
    0x0, 0x0, 0x3f, 0xf4, 0x8f, 0xe0, 0x0, 0x3,
    0xff, 0x48, 0xfe, 0x0, 0x0, 0x3f, 0xf4, 0x8f,
    0xe0, 0x0, 0x3, 0xff, 0x48, 0xfe, 0x0, 0x0,
    0x2f, 0xf4, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x0,
    0xef, 0xff, 0xf4, 0x8f, 0xe0, 0xe, 0xff, 0xff,
    0x48, 0xfe, 0x0, 0x56, 0x8f, 0xf4, 0x8f, 0xe0,
    0x0, 0x3, 0xff, 0x48, 0xfe, 0x0, 0x0, 0x3f,
    0xf4, 0x8f, 0xe0, 0x0, 0x3, 0xff, 0x48, 0xfe,
    0x0, 0x0, 0x3f, 0xf4, 0x8f, 0xe0, 0x0, 0x3,
    0xff, 0x48, 0xfe, 0x0, 0x0, 0x3f, 0xf4, 0x8f,
    0xe0, 0x0, 0x3, 0xff, 0x47, 0xff, 0x0, 0x0,
    0x3f, 0xf3, 0x5f, 0xf2, 0x0, 0x6, 0xff, 0x11,
    0xff, 0xb0, 0x1, 0xdf, 0xd0, 0xa, 0xff, 0xfd,
    0xff, 0xf6, 0x0, 0x1b, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x3, 0x67, 0x62, 0x0, 0x0,

    /* U+0048 "H" */
    0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0,
    0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f,
    0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7,
    0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0,
    0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff,
    0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0,
    0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f,
    0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7,
    0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xf7, 0x77, 0x7b, 0xff, 0x7f, 0xf0, 0x0,
    0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f,
    0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7,
    0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0,
    0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff,
    0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0,
    0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f,
    0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0x7,
    0xff, 0x7f, 0xf0, 0x0, 0x7, 0xff, 0x7f, 0xf0,
    0x0, 0x7, 0xff,

    /* U+0049 "I" */
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,
    0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0,

    /* U+004A "J" */
    0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f,
    0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0,
    0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0,
    0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f,
    0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0,
    0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0,
    0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f,
    0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0,
    0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0,
    0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f,
    0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0,
    0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0,
    0x0, 0x7f, 0xf0, 0x0, 0x7f, 0xf0, 0x0, 0x8f,
    0xe0, 0x0, 0xdf, 0xb0, 0x4b, 0xff, 0x60, 0xdf,
    0xfc, 0x0, 0xde, 0x90, 0x0,

    /* U+004B "K" */
    0x7f, 0xf0, 0x0, 0x3, 0xff, 0x40, 0x7f, 0xf0,
    0x0, 0x8, 0xff, 0x0, 0x7f, 0xf0, 0x0, 0xd,
    0xfb, 0x0, 0x7f, 0xf0, 0x0, 0x2f, 0xf6, 0x0,
    0x7f, 0xf0, 0x0, 0x7f, 0xf1, 0x0, 0x7f, 0xf0,
    0x0, 0xcf, 0xc0, 0x0, 0x7f, 0xf0, 0x1, 0xff,
    0x70, 0x0, 0x7f, 0xf0, 0x6, 0xff, 0x20, 0x0,
    0x7f, 0xf0, 0xb, 0xfe, 0x0, 0x0, 0x7f, 0xf0,
    0xf, 0xf9, 0x0, 0x0, 0x7f, 0xf0, 0x5f, 0xf4,
    0x0, 0x0, 0x7f, 0xf0, 0xaf, 0xf0, 0x0, 0x0,
    0x7f, 0xf1, 0xff, 0xa0, 0x0, 0x0, 0x7f, 0xf5,
    0xff, 0x50, 0x0, 0x0, 0x7f, 0xfa, 0xff, 0x10,
    0x0, 0x0, 0x7f, 0xfe, 0xfe, 0x0, 0x0, 0x0,
    0x7f, 0xf9, 0xff, 0x20, 0x0, 0x0, 0x7f, 0xf3,
    0xff, 0x80, 0x0, 0x0, 0x7f, 0xf0, 0xdf, 0xd0,
    0x0, 0x0, 0x7f, 0xf0, 0x7f, 0xf3, 0x0, 0x0,
    0x7f, 0xf0, 0x1f, 0xf9, 0x0, 0x0, 0x7f, 0xf0,
    0xb, 0xfe, 0x0, 0x0, 0x7f, 0xf0, 0x5, 0xff,
    0x40, 0x0, 0x7f, 0xf0, 0x0, 0xff, 0x90, 0x0,
    0x7f, 0xf0, 0x0, 0xaf, 0xe0, 0x0, 0x7f, 0xf0,
    0x0, 0x4f, 0xf4, 0x0, 0x7f, 0xf0, 0x0, 0xe,
    0xfa, 0x0, 0x7f, 0xf0, 0x0, 0x8, 0xff, 0x0,
    0x7f, 0xf0, 0x0, 0x2, 0xff, 0x50, 0x7f, 0xf0,
    0x0, 0x0, 0xdf, 0xa0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0,

    /* U+004C "L" */
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x7f, 0xf8, 0x77, 0x70, 0x7f, 0xff, 0xff, 0xf2,
    0x7f, 0xff, 0xff, 0xf2,

    /* U+004D "M" */
    0x7f, 0xff, 0x60, 0x0, 0x0, 0xe, 0xff, 0xf7,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xff, 0xff, 0x7f,
    0xff, 0xb0, 0x0, 0x0, 0x3f, 0xff, 0xf7, 0xff,
    0xfe, 0x0, 0x0, 0x5, 0xff, 0xff, 0x7f, 0xff,
    0xf0, 0x0, 0x0, 0x8f, 0xff, 0xf7, 0xff, 0xff,
    0x30, 0x0, 0xa, 0xff, 0xff, 0x7f, 0xef, 0xf5,
    0x0, 0x0, 0xdf, 0xdf, 0xf7, 0xfe, 0xdf, 0x80,
    0x0, 0xf, 0xfb, 0xff, 0x7f, 0xea, 0xfa, 0x0,
    0x2, 0xff, 0x8f, 0xf7, 0xfe, 0x8f, 0xc0, 0x0,
    0x4f, 0xf6, 0xff, 0x7f, 0xe5, 0xff, 0x0, 0x6,
    0xfd, 0x6f, 0xf7, 0xfe, 0x3f, 0xf1, 0x0, 0x9f,
    0xa6, 0xff, 0x7f, 0xe1, 0xff, 0x40, 0xb, 0xf7,
    0x6f, 0xf7, 0xff, 0xe, 0xf6, 0x0, 0xef, 0x46,
    0xff, 0x7f, 0xf0, 0xcf, 0x90, 0xf, 0xf2, 0x6f,
    0xf7, 0xff, 0x9, 0xfb, 0x3, 0xff, 0x6, 0xff,
    0x7f, 0xf0, 0x7f, 0xe0, 0x5f, 0xc0, 0x6f, 0xf7,
    0xff, 0x5, 0xff, 0x8, 0xf9, 0x6, 0xff, 0x7f,
    0xf0, 0x2f, 0xf3, 0xaf, 0x70, 0x7f, 0xf7, 0xff,
    0x0, 0xff, 0x5d, 0xf4, 0x7, 0xff, 0x7f, 0xf0,
    0xd, 0xf7, 0xff, 0x10, 0x7f, 0xf7, 0xff, 0x0,
    0xbf, 0xcf, 0xe0, 0x7, 0xff, 0x7f, 0xf0, 0x9,
    0xff, 0xfc, 0x0, 0x7f, 0xf7, 0xff, 0x0, 0x6f,
    0xff, 0x90, 0x7, 0xff, 0x7f, 0xf0, 0x4, 0xff,
    0xf6, 0x0, 0x7f, 0xf7, 0xff, 0x0, 0x1f, 0xff,
    0x40, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0xff, 0xf1,
    0x0, 0x7f, 0xf7, 0xff, 0x0, 0xc, 0xfe, 0x0,
    0x7, 0xff, 0x7f, 0xf0, 0x0, 0xaf, 0xb0, 0x0,
    0x8f, 0xf7, 0xff, 0x0, 0x8, 0xf9, 0x0, 0x8,
    0xff, 0x7f, 0xf0, 0x0, 0x5f, 0x60, 0x0, 0x8f,
    0xf0,

    /* U+004E "N" */
    0x7f, 0xf0, 0x0, 0x0, 0x9f, 0xe7, 0xff, 0x40,
    0x0, 0x9, 0xfe, 0x7f, 0xf8, 0x0, 0x0, 0x9f,
    0xe7, 0xff, 0xc0, 0x0, 0x9, 0xfe, 0x7f, 0xff,
    0x10, 0x0, 0x9f, 0xe7, 0xff, 0xf4, 0x0, 0x9,
    0xfe, 0x7f, 0xff, 0x80, 0x0, 0x9f, 0xe7, 0xff,
    0xfc, 0x0, 0x9, 0xfe, 0x7f, 0xff, 0xf1, 0x0,
    0x9f, 0xe7, 0xff, 0xff, 0x50, 0x9, 0xfe, 0x7f,
    0xfb, 0xf9, 0x0, 0x9f, 0xe7, 0xff, 0x7f, 0xd0,
    0x9, 0xfe, 0x7f, 0xf3, 0xff, 0x10, 0x9f, 0xe7,
    0xff, 0xf, 0xf5, 0x9, 0xfe, 0x7f, 0xf0, 0xcf,
    0x90, 0x8f, 0xe7, 0xff, 0x8, 0xfd, 0x8, 0xfe,
    0x7f, 0xf0, 0x4f, 0xf1, 0x8f, 0xe7, 0xff, 0x0,
    0xff, 0x58, 0xfe, 0x7f, 0xf0, 0xc, 0xf9, 0x8f,
    0xe7, 0xff, 0x0, 0x8f, 0xd8, 0xfe, 0x7f, 0xf0,
    0x4, 0xff, 0xaf, 0xe7, 0xff, 0x0, 0xf, 0xfd,
    0xfe, 0x7f, 0xf0, 0x0, 0xcf, 0xff, 0xe7, 0xff,
    0x0, 0x8, 0xff, 0xfe, 0x7f, 0xf0, 0x0, 0x4f,
    0xff, 0xe7, 0xff, 0x0, 0x0, 0xff, 0xfe, 0x7f,
    0xf0, 0x0, 0xc, 0xff, 0xe7, 0xff, 0x0, 0x0,
    0x8f, 0xfe, 0x7f, 0xf0, 0x0, 0x4, 0xff, 0xe7,
    0xff, 0x0, 0x0, 0x1f, 0xfe, 0x7f, 0xf0, 0x0,
    0x0, 0xdf, 0xe0,

    /* U+004F "O" */
    0x0, 0x4, 0x77, 0x50, 0x0, 0x1, 0xcf, 0xff,
    0xfe, 0x40, 0xb, 0xff, 0xed, 0xff, 0xe1, 0x1f,
    0xfa, 0x0, 0x5f, 0xf6, 0x5f, 0xf1, 0x0, 0xc,
    0xfa, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x8f, 0xe0,
    0x0, 0x9, 0xfd, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f,
    0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9,
    0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0,
    0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f,
    0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9,
    0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0,
    0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfd, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x5f, 0xf2, 0x0, 0xc,
    0xfa, 0x1f, 0xfa, 0x0, 0x5f, 0xf6, 0xa, 0xff,
    0xed, 0xff, 0xe1, 0x1, 0xcf, 0xff, 0xfe, 0x40,
    0x0, 0x4, 0x77, 0x50, 0x0,

    /* U+0050 "P" */
    0x7f, 0xff, 0xff, 0xc7, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xb0, 0x7f, 0xf8, 0x78, 0xef, 0xf4, 0x7f,
    0xf0, 0x0, 0x1f, 0xf8, 0x7f, 0xf0, 0x0, 0xc,
    0xfa, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0,
    0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc,
    0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0,
    0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x7f, 0xf0, 0x0, 0xb,
    0xfc, 0x7f, 0xf0, 0x0, 0xc, 0xfa, 0x7f, 0xf0,
    0x0, 0x1f, 0xf8, 0x7f, 0xf8, 0x78, 0xef, 0xf3,
    0x7f, 0xff, 0xff, 0xff, 0xb0, 0x7f, 0xff, 0xff,
    0xc7, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x4, 0x77, 0x50, 0x0, 0x1, 0xcf, 0xff,
    0xfe, 0x40, 0xb, 0xff, 0xed, 0xff, 0xe1, 0x1f,
    0xfa, 0x0, 0x5f, 0xf6, 0x5f, 0xf1, 0x0, 0xc,
    0xfa, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x8f, 0xe0,
    0x0, 0x9, 0xfd, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f,
    0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9,
    0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0,
    0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f,
    0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9,
    0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0,
    0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfe,
    0x8f, 0xe0, 0x0, 0x9, 0xfe, 0x8f, 0xe0, 0x0,
    0x9, 0xfe, 0x8f, 0xe0, 0x0, 0x9, 0xfd, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x5f, 0xf1, 0x0, 0xc,
    0xfa, 0x1f, 0xfa, 0x0, 0x5f, 0xf6, 0xa, 0xff,
    0xed, 0xff, 0xe0, 0x1, 0xcf, 0xff, 0xff, 0x40,
    0x0, 0x4, 0x78, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x5,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x10,

    /* U+0052 "R" */
    0x7f, 0xff, 0xff, 0xd8, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x7f, 0xf8, 0x78, 0xef, 0xf4,
    0x7, 0xff, 0x0, 0x1, 0xff, 0x80, 0x7f, 0xf0,
    0x0, 0xc, 0xfb, 0x7, 0xff, 0x0, 0x0, 0xaf,
    0xc0, 0x7f, 0xf0, 0x0, 0xa, 0xfc, 0x7, 0xff,
    0x0, 0x0, 0xaf, 0xc0, 0x7f, 0xf0, 0x0, 0xa,
    0xfc, 0x7, 0xff, 0x0, 0x0, 0xaf, 0xc0, 0x7f,
    0xf0, 0x0, 0xa, 0xfc, 0x7, 0xff, 0x0, 0x0,
    0xaf, 0xc0, 0x7f, 0xf0, 0x0, 0xb, 0xfb, 0x7,
    0xff, 0x0, 0x0, 0xef, 0x90, 0x7f, 0xf0, 0x1,
    0x7f, 0xf6, 0x7, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x7f, 0xff, 0xff, 0xfd, 0x30, 0x7, 0xff, 0x8b,
    0xff, 0x0, 0x0, 0x7f, 0xf0, 0x3f, 0xf2, 0x0,
    0x7, 0xff, 0x0, 0xef, 0x70, 0x0, 0x7f, 0xf0,
    0xa, 0xfb, 0x0, 0x7, 0xff, 0x0, 0x6f, 0xf0,
    0x0, 0x7f, 0xf0, 0x2, 0xff, 0x40, 0x7, 0xff,
    0x0, 0xd, 0xf8, 0x0, 0x7f, 0xf0, 0x0, 0x9f,
    0xd0, 0x7, 0xff, 0x0, 0x5, 0xff, 0x10, 0x7f,
    0xf0, 0x0, 0x1f, 0xf6, 0x7, 0xff, 0x0, 0x0,
    0xcf, 0xa0, 0x7f, 0xf0, 0x0, 0x8, 0xfe, 0x7,
    0xff, 0x0, 0x0, 0x4f, 0xf3, 0x7f, 0xf0, 0x0,
    0x0, 0xff, 0x70,

    /* U+0053 "S" */
    0x0, 0x5, 0x77, 0x30, 0x0, 0x3, 0xef, 0xff,
    0xfc, 0x10, 0x1e, 0xff, 0xde, 0xff, 0xb0, 0x6f,
    0xf6, 0x0, 0xaf, 0xf2, 0xaf, 0xd0, 0x0, 0x1f,
    0xf6, 0xcf, 0xa0, 0x0, 0xe, 0xf8, 0xdf, 0xa0,
    0x0, 0xe, 0xf9, 0xdf, 0xa0, 0x0, 0xe, 0xf9,
    0xdf, 0xa0, 0x0, 0xe, 0xf9, 0xdf, 0xa0, 0x0,
    0xe, 0xf9, 0xdf, 0xb0, 0x0, 0xe, 0xf9, 0xbf,
    0xd0, 0x0, 0xa, 0xc7, 0x8f, 0xf3, 0x0, 0x0,
    0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xef, 0xf6, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x70, 0x0, 0x0, 0x3, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x30, 0x0,
    0x0, 0x6, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xbf,
    0xf3, 0x56, 0x30, 0x0, 0x3f, 0xf7, 0xdf, 0xa0,
    0x0, 0xf, 0xf9, 0xdf, 0xa0, 0x0, 0xe, 0xf9,
    0xdf, 0xa0, 0x0, 0xe, 0xf9, 0xdf, 0xa0, 0x0,
    0xe, 0xf9, 0xdf, 0xa0, 0x0, 0xe, 0xf9, 0xbf,
    0xa0, 0x0, 0xe, 0xf8, 0x9f, 0xd0, 0x0, 0x1f,
    0xf5, 0x5f, 0xf6, 0x0, 0xaf, 0xf2, 0xe, 0xff,
    0xde, 0xff, 0xb0, 0x3, 0xef, 0xff, 0xfd, 0x10,
    0x0, 0x5, 0x77, 0x40, 0x0,

    /* U+0054 "T" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x6b, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x57, 0x77, 0xef, 0xc7, 0x77,
    0x30, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x90, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0, 0x0,
    0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90,
    0x0, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x90, 0x0, 0x0, 0x0, 0xe, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x90, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x90, 0x0, 0x0, 0x0, 0xe,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0,
    0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x90, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0, 0x0,
    0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90,
    0x0, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x90, 0x0, 0x0, 0x0, 0xe, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x90, 0x0, 0x0,

    /* U+0055 "U" */
    0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0,
    0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f,
    0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8,
    0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0,
    0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff,
    0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0,
    0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f,
    0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8,
    0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0,
    0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff,
    0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0,
    0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f,
    0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8,
    0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0,
    0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0, 0x8, 0xff,
    0x7f, 0xf0, 0x0, 0x8, 0xff, 0x7f, 0xf0, 0x0,
    0x8, 0xfe, 0x6f, 0xf0, 0x0, 0x8, 0xfe, 0x4f,
    0xf3, 0x0, 0xb, 0xfb, 0xf, 0xfb, 0x0, 0x4f,
    0xf8, 0x9, 0xff, 0xed, 0xff, 0xf1, 0x0, 0xbf,
    0xff, 0xff, 0x40, 0x0, 0x3, 0x77, 0x51, 0x0,

    /* U+0056 "V" */
    0x9f, 0xe0, 0x0, 0x0, 0xef, 0x97, 0xff, 0x0,
    0x0, 0xf, 0xf7, 0x5f, 0xf1, 0x0, 0x1, 0xff,
    0x53, 0xff, 0x20, 0x0, 0x2f, 0xf3, 0x1f, 0xf4,
    0x0, 0x4, 0xff, 0x10, 0xff, 0x50, 0x0, 0x6f,
    0xf0, 0xd, 0xf7, 0x0, 0x7, 0xfd, 0x0, 0xbf,
    0x90, 0x0, 0x9f, 0xb0, 0x9, 0xfa, 0x0, 0xa,
    0xf9, 0x0, 0x7f, 0xc0, 0x0, 0xcf, 0x70, 0x5,
    0xfd, 0x0, 0xd, 0xf6, 0x0, 0x3f, 0xf0, 0x0,
    0xff, 0x40, 0x1, 0xff, 0x0, 0x1f, 0xf2, 0x0,
    0xf, 0xf2, 0x2, 0xff, 0x0, 0x0, 0xef, 0x40,
    0x4f, 0xe0, 0x0, 0xc, 0xf5, 0x5, 0xfc, 0x0,
    0x0, 0xaf, 0x70, 0x7f, 0xa0, 0x0, 0x8, 0xf8,
    0x9, 0xf8, 0x0, 0x0, 0x6f, 0xa0, 0xaf, 0x60,
    0x0, 0x4, 0xfb, 0xc, 0xf4, 0x0, 0x0, 0x2f,
    0xd0, 0xdf, 0x20, 0x0, 0x0, 0xff, 0xf, 0xf0,
    0x0, 0x0, 0xe, 0xf1, 0xff, 0x0, 0x0, 0x0,
    0xcf, 0x4f, 0xd0, 0x0, 0x0, 0xa, 0xf7, 0xfb,
    0x0, 0x0, 0x0, 0x8f, 0xbf, 0x90, 0x0, 0x0,
    0x6, 0xfe, 0xf7, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x50, 0x0, 0x0, 0x2, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0,

    /* U+0057 "W" */
    0x7f, 0xf0, 0x0, 0x3, 0xff, 0x70, 0x0, 0xb,
    0xfb, 0x6f, 0xf1, 0x0, 0x5, 0xff, 0x80, 0x0,
    0xd, 0xfa, 0x4f, 0xf2, 0x0, 0x6, 0xff, 0xa0,
    0x0, 0xe, 0xf8, 0x2f, 0xf3, 0x0, 0x8, 0xff,
    0xc0, 0x0, 0xf, 0xf6, 0x1f, 0xf5, 0x0, 0xa,
    0xff, 0xd0, 0x0, 0x1f, 0xf5, 0xf, 0xf6, 0x0,
    0xb, 0xff, 0xf0, 0x0, 0x2f, 0xf3, 0xd, 0xf8,
    0x0, 0xd, 0xff, 0xf1, 0x0, 0x4f, 0xf1, 0xb,
    0xf9, 0x0, 0xf, 0xff, 0xf2, 0x0, 0x5f, 0xf0,
    0xa, 0xfa, 0x0, 0xf, 0xfd, 0xf4, 0x0, 0x6f,
    0xe0, 0x8, 0xfc, 0x0, 0x2f, 0xea, 0xf6, 0x0,
    0x8f, 0xc0, 0x6, 0xfd, 0x0, 0x3f, 0xd9, 0xf7,
    0x0, 0x9f, 0xa0, 0x4, 0xff, 0x0, 0x5f, 0xb7,
    0xf9, 0x0, 0xaf, 0x80, 0x3, 0xff, 0x0, 0x7f,
    0x95, 0xfb, 0x0, 0xcf, 0x70, 0x1, 0xff, 0x20,
    0x8f, 0x73, 0xfc, 0x0, 0xdf, 0x50, 0x0, 0xff,
    0x30, 0xaf, 0x62, 0xfe, 0x0, 0xff, 0x30, 0x0,
    0xdf, 0x40, 0xcf, 0x40, 0xff, 0x0, 0xff, 0x20,
    0x0, 0xcf, 0x60, 0xdf, 0x20, 0xef, 0x21, 0xff,
    0x0, 0x0, 0xaf, 0x70, 0xff, 0x0, 0xcf, 0x33,
    0xfe, 0x0, 0x0, 0x8f, 0x91, 0xff, 0x0, 0xaf,
    0x54, 0xfc, 0x0, 0x0, 0x6f, 0xa2, 0xfd, 0x0,
    0x9f, 0x76, 0xfb, 0x0, 0x0, 0x5f, 0xb4, 0xfb,
    0x0, 0x7f, 0x87, 0xf9, 0x0, 0x0, 0x3f, 0xd5,
    0xfa, 0x0, 0x5f, 0xa8, 0xf7, 0x0, 0x0, 0x1f,
    0xe7, 0xf8, 0x0, 0x3f, 0xca, 0xf6, 0x0, 0x0,
    0xf, 0xf9, 0xf6, 0x0, 0x2f, 0xdb, 0xf4, 0x0,
    0x0, 0xe, 0xfc, 0xf4, 0x0, 0xf, 0xfc, 0xf2,
    0x0, 0x0, 0xc, 0xff, 0xf3, 0x0, 0xe, 0xff,
    0xf0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x0, 0xc,
    0xff, 0xf0, 0x0, 0x0, 0x9, 0xff, 0xf0, 0x0,
    0xb, 0xff, 0xd0, 0x0, 0x0, 0x7, 0xff, 0xe0,
    0x0, 0x9, 0xff, 0xb0, 0x0, 0x0, 0x5, 0xff,
    0xc0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0x5, 0xff, 0x80, 0x0,

    /* U+0058 "X" */
    0xc, 0xfb, 0x0, 0x0, 0x0, 0xdf, 0xb0, 0x6f,
    0xf1, 0x0, 0x0, 0x1f, 0xf6, 0x1, 0xff, 0x50,
    0x0, 0x6, 0xff, 0x10, 0xc, 0xfa, 0x0, 0x0,
    0xaf, 0xd0, 0x0, 0x7f, 0xf0, 0x0, 0xf, 0xf8,
    0x0, 0x2, 0xff, 0x40, 0x4, 0xff, 0x40, 0x0,
    0xc, 0xf9, 0x0, 0x8f, 0xf0, 0x0, 0x0, 0x7f,
    0xe0, 0xd, 0xfa, 0x0, 0x0, 0x2, 0xff, 0x31,
    0xff, 0x60, 0x0, 0x0, 0xd, 0xf8, 0x6f, 0xf1,
    0x0, 0x0, 0x0, 0x7f, 0xca, 0xfc, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x7, 0xfc, 0xaf,
    0xb0, 0x0, 0x0, 0x0, 0xcf, 0x76, 0xff, 0x0,
    0x0, 0x0, 0x1f, 0xf3, 0x2f, 0xf4, 0x0, 0x0,
    0x6, 0xfe, 0x0, 0xdf, 0x80, 0x0, 0x0, 0xbf,
    0x90, 0x9, 0xfd, 0x0, 0x0, 0x1f, 0xf4, 0x0,
    0x5f, 0xf1, 0x0, 0x6, 0xff, 0x0, 0x1, 0xff,
    0x60, 0x0, 0xbf, 0xa0, 0x0, 0xc, 0xfa, 0x0,
    0xf, 0xf6, 0x0, 0x0, 0x8f, 0xe0, 0x5, 0xff,
    0x10, 0x0, 0x3, 0xff, 0x30, 0xaf, 0xc0, 0x0,
    0x0, 0xf, 0xf8, 0xf, 0xf7, 0x0, 0x0, 0x0,
    0xbf, 0xc0,

    /* U+0059 "Y" */
    0x8f, 0xf0, 0x0, 0x0, 0x2f, 0xf5, 0x4f, 0xf2,
    0x0, 0x0, 0x5f, 0xf2, 0x1f, 0xf6, 0x0, 0x0,
    0x8f, 0xe0, 0xd, 0xf9, 0x0, 0x0, 0xbf, 0xb0,
    0xa, 0xfc, 0x0, 0x0, 0xff, 0x70, 0x6, 0xff,
    0x0, 0x2, 0xff, 0x30, 0x2, 0xff, 0x30, 0x5,
    0xff, 0x0, 0x0, 0xff, 0x60, 0x8, 0xfc, 0x0,
    0x0, 0xbf, 0x90, 0xc, 0xf9, 0x0, 0x0, 0x8f,
    0xc0, 0xf, 0xf5, 0x0, 0x0, 0x4f, 0xf0, 0x2f,
    0xf2, 0x0, 0x0, 0xf, 0xf3, 0x5f, 0xe0, 0x0,
    0x0, 0xd, 0xf6, 0x9f, 0xa0, 0x0, 0x0, 0x9,
    0xf9, 0xcf, 0x70, 0x0, 0x0, 0x6, 0xfd, 0xff,
    0x30, 0x0, 0x0, 0x2, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x0, 0x0,

    /* U+005A "Z" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xff,
    0xff, 0xfc, 0x7, 0x77, 0x77, 0x8f, 0xf8, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x8f,
    0xf0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0,
    0x0, 0xff, 0x80, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x0, 0xc,
    0xfc, 0x0, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x8f, 0xf1,
    0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0,
    0xff, 0x80, 0x0, 0x0, 0x4, 0xff, 0x40, 0x0,
    0x0, 0x8, 0xff, 0x10, 0x0, 0x0, 0xc, 0xfd,
    0x0, 0x0, 0x0, 0xf, 0xf9, 0x0, 0x0, 0x0,
    0x3f, 0xf5, 0x0, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x0, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0xff,
    0x90, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0,
    0x7, 0xff, 0x10, 0x0, 0x0, 0xb, 0xfd, 0x0,
    0x0, 0x0, 0xf, 0xf9, 0x0, 0x0, 0x0, 0x3f,
    0xf5, 0x0, 0x0, 0x0, 0x6f, 0xf9, 0x77, 0x77,
    0x77, 0x7f, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff,

    /* U+005B "[" */
    0x3f, 0xff, 0xff, 0x43, 0xff, 0xff, 0xf4, 0x3f,
    0xf3, 0x11, 0x3, 0xff, 0x20, 0x0, 0x3f, 0xf2,
    0x0, 0x3, 0xff, 0x20, 0x0, 0x3f, 0xf2, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x3f, 0xf2, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x3f, 0xf2, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x3f, 0xf2, 0x0, 0x3, 0xff, 0x20,
    0x0, 0x3f, 0xf2, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x3f, 0xf2, 0x0, 0x3, 0xff, 0x20, 0x0, 0x3f,
    0xf2, 0x0, 0x3, 0xff, 0x20, 0x0, 0x3f, 0xf2,
    0x0, 0x3, 0xff, 0x20, 0x0, 0x3f, 0xf2, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x3f, 0xf2, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x3f, 0xf2, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x3f, 0xf2, 0x0, 0x3, 0xff, 0x20,
    0x0, 0x3f, 0xf2, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x3f, 0xf2, 0x0, 0x3, 0xff, 0x20, 0x0, 0x3f,
    0xf2, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x3f, 0xff,
    0xff, 0x40, 0x33, 0x33, 0x30,

    /* U+005C "\\" */
    0x5f, 0xf0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xb0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x8,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x80, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x10,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x80,

    /* U+005D "]" */
    0x7f, 0xff, 0xff, 0x7f, 0xff, 0xff, 0x1, 0x17,
    0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0,
    0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7,
    0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0,
    0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7,
    0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0,
    0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7,
    0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0,
    0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x7, 0xff, 0x0, 0x7, 0xff, 0x0, 0x7,
    0xff, 0x7f, 0xff, 0xff, 0x7f, 0xff, 0xff, 0x13,
    0x33, 0x33,

    /* U+005E "^" */
    0x0, 0x0, 0x7, 0x86, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0xe, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x7f, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xc0, 0xff, 0x70, 0x0, 0x0,
    0xe, 0xf7, 0xa, 0xfc, 0x0, 0x0, 0x4, 0xff,
    0x20, 0x5f, 0xf1, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0xff, 0x70, 0x0, 0xe, 0xf7, 0x0, 0xb, 0xfc,
    0x0, 0x4, 0xff, 0x20, 0x0, 0x5f, 0xf2, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0xff, 0x70, 0xf, 0xf7,
    0x0, 0x0, 0xb, 0xfc, 0x5, 0xff, 0x20, 0x0,
    0x0, 0x5f, 0xf2,

    /* U+005F "_" */
    0x9d, 0xdd, 0xdd, 0xdd, 0xd2, 0xbf, 0xff, 0xff,
    0xff, 0xf3,

    /* U+0060 "`" */
    0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0x6, 0xff,
    0x20, 0x0, 0xcf, 0x90, 0x0, 0x3f, 0xf1,

    /* U+0061 "a" */
    0x5, 0xcf, 0xfc, 0x30, 0x3f, 0xff, 0xff, 0xf1,
    0x9f, 0xf5, 0x6f, 0xf7, 0xcf, 0x90, 0xc, 0xfa,
    0xef, 0x80, 0xa, 0xfb, 0xef, 0x70, 0xa, 0xfc,
    0xef, 0x70, 0xa, 0xfc, 0x78, 0x30, 0xa, 0xfc,
    0x0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0xa, 0xfc,
    0x0, 0x43, 0xa, 0xfc, 0x1d, 0xff, 0x9a, 0xfc,
    0x7f, 0xff, 0xfe, 0xfc, 0xbf, 0xe1, 0x4f, 0xfc,
    0xdf, 0x90, 0xc, 0xfc, 0xef, 0x70, 0xa, 0xfc,
    0xef, 0x70, 0xa, 0xfc, 0xef, 0x70, 0xa, 0xfc,
    0xef, 0x80, 0xa, 0xfc, 0xdf, 0x90, 0xc, 0xfc,
    0xaf, 0xd0, 0x3f, 0xfc, 0x6f, 0xfe, 0xfb, 0xfc,
    0xa, 0xfd, 0x63, 0xfc,

    /* U+0062 "b" */
    0xaf, 0xc0, 0x0, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0x0, 0xaf, 0xc0, 0x0, 0x0, 0xa, 0xfc, 0x0,
    0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0xa, 0xfc,
    0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0xa,
    0xfc, 0x0, 0x0, 0x0, 0xaf, 0xc2, 0xcf, 0xb0,
    0xa, 0xfc, 0xdf, 0xff, 0x80, 0xaf, 0xf9, 0x3e,
    0xfc, 0xa, 0xff, 0x0, 0x8f, 0xe0, 0xaf, 0xd0,
    0x6, 0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf,
    0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0,
    0xaf, 0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f,
    0xf0, 0xaf, 0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0,
    0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff, 0xa, 0xfc,
    0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff, 0xa,
    0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff,
    0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x6,
    0xff, 0xa, 0xfe, 0x0, 0x7f, 0xe0, 0xaf, 0xf4,
    0xb, 0xfc, 0xa, 0xfb, 0xfe, 0xff, 0x80, 0xaf,
    0x54, 0xdf, 0xb0, 0x0,

    /* U+0063 "c" */
    0x3, 0xbf, 0xfc, 0x40, 0x2f, 0xff, 0xff, 0xf3,
    0x8f, 0xf6, 0x5e, 0xfa, 0xbf, 0xc0, 0x9, 0xfd,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x4, 0x88, 0xcf, 0xa0, 0x0, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0xcf, 0xa0, 0x0, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0xcf, 0xa0, 0x0, 0x11,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xbf, 0xc0, 0x9, 0xfd,
    0x8f, 0xf6, 0x4e, 0xfa, 0x2f, 0xff, 0xff, 0xf3,
    0x2, 0xbe, 0xec, 0x30,

    /* U+0064 "d" */
    0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x8, 0xfe,
    0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x8, 0xfe,
    0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x8, 0xfe,
    0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x8, 0xfe,
    0x8, 0xfd, 0x58, 0xfe, 0x4f, 0xff, 0xfa, 0xfe,
    0x8f, 0xf5, 0x6f, 0xfe, 0xbf, 0xb0, 0xb, 0xfe,
    0xcf, 0xa0, 0x9, 0xfe, 0xcf, 0x90, 0x8, 0xfe,
    0xdf, 0x90, 0x8, 0xfe, 0xdf, 0x90, 0x8, 0xfe,
    0xdf, 0x90, 0x8, 0xfe, 0xdf, 0x90, 0x8, 0xfe,
    0xdf, 0x90, 0x8, 0xfe, 0xdf, 0x90, 0x8, 0xfe,
    0xdf, 0x90, 0x8, 0xfe, 0xdf, 0x90, 0x8, 0xfe,
    0xdf, 0x90, 0x8, 0xfe, 0xdf, 0x90, 0x8, 0xfe,
    0xdf, 0x90, 0x8, 0xfe, 0xcf, 0x90, 0x8, 0xfe,
    0xcf, 0xa0, 0x9, 0xfe, 0xbf, 0xb0, 0xb, 0xfe,
    0x8f, 0xf4, 0x6f, 0xfe, 0x4f, 0xff, 0xf8, 0xfe,
    0x8, 0xfe, 0x61, 0xfe,

    /* U+0065 "e" */
    0x3, 0xbf, 0xec, 0x40, 0x2f, 0xff, 0xff, 0xf4,
    0x8f, 0xf6, 0x5e, 0xfa, 0xbf, 0xb0, 0x9, 0xfd,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xff, 0xff, 0xfe, 0xcf, 0xff, 0xff, 0xfe,
    0xcf, 0xb2, 0x22, 0x22, 0xcf, 0xa0, 0x0, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0xcf, 0xa0, 0x6, 0xdc,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xbf, 0xc0, 0x9, 0xfd,
    0x8f, 0xf6, 0x4e, 0xfa, 0x2f, 0xff, 0xff, 0xf3,
    0x2, 0xbe, 0xfc, 0x40,

    /* U+0066 "f" */
    0x0, 0x4, 0xcf, 0xe4, 0x0, 0x1f, 0xff, 0xf5,
    0x0, 0x6f, 0xf6, 0x31, 0x0, 0x9f, 0xc0, 0x0,
    0x0, 0xaf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0xef, 0xff, 0xff, 0xf4, 0xef, 0xff, 0xff, 0xf4,
    0x23, 0xcf, 0xb3, 0x30, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0xbf, 0xb0, 0x0,

    /* U+0067 "g" */
    0x0, 0x4c, 0xff, 0xff, 0xff, 0x2, 0xff, 0xff,
    0xff, 0xff, 0x8, 0xff, 0x22, 0xef, 0x40, 0xb,
    0xfb, 0x0, 0x9f, 0xa0, 0xc, 0xf9, 0x0, 0x8f,
    0xc0, 0xc, 0xf9, 0x0, 0x8f, 0xe0, 0xd, 0xf9,
    0x0, 0x8f, 0xe0, 0xd, 0xf9, 0x0, 0x8f, 0xe0,
    0xd, 0xf9, 0x0, 0x8f, 0xe0, 0xc, 0xf9, 0x0,
    0x8f, 0xd0, 0xb, 0xfb, 0x0, 0xaf, 0xc0, 0x8,
    0xff, 0x22, 0xef, 0xa0, 0x1, 0xef, 0xff, 0xff,
    0x30, 0x0, 0x7f, 0xff, 0xe6, 0x0, 0x1, 0xfb,
    0x11, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0x10, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xd5, 0x0, 0x2, 0xff, 0xff, 0xff, 0x30, 0xb,
    0xfd, 0x45, 0xff, 0x90, 0xf, 0xf5, 0x0, 0xaf,
    0xc0, 0x2f, 0xf3, 0x0, 0x8f, 0xd0, 0x3f, 0xf3,
    0x0, 0x8f, 0xe0, 0x3f, 0xf3, 0x0, 0x8f, 0xe0,
    0x3f, 0xf3, 0x0, 0x8f, 0xe0, 0x2f, 0xf3, 0x0,
    0x8f, 0xe0, 0x2f, 0xf4, 0x0, 0x9f, 0xd0, 0xf,
    0xf6, 0x0, 0xbf, 0xb0, 0xc, 0xfd, 0x46, 0xff,
    0x70, 0x5, 0xff, 0xff, 0xff, 0x10, 0x0, 0x5d,
    0xff, 0xb2, 0x0,

    /* U+0068 "h" */
    0xaf, 0xb0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0xaf, 0xb0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0xaf, 0xb0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0xaf, 0xb0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0xaf, 0xb2, 0xcf, 0xa0, 0xaf, 0xbd, 0xff, 0xf7,
    0xaf, 0xf8, 0x3e, 0xfb, 0xaf, 0xe0, 0x8, 0xfe,
    0xaf, 0xc0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff, 0xaf, 0xb0, 0x6, 0xff,
    0xaf, 0xb0, 0x6, 0xff,

    /* U+0069 "i" */
    0x9, 0xfa, 0x3, 0xff, 0xf3, 0x1f, 0xff, 0x20,
    0x38, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfb, 0x0, 0xaf, 0xb0, 0xa, 0xfb, 0x0, 0xaf,
    0xb0, 0xa, 0xfb, 0x0, 0xaf, 0xb0, 0xa, 0xfb,
    0x0, 0xaf, 0xb0, 0xa, 0xfb, 0x0, 0xaf, 0xb0,
    0xa, 0xfb, 0x0, 0xaf, 0xb0, 0xa, 0xfb, 0x0,
    0xaf, 0xb0, 0xa, 0xfb, 0x0, 0xaf, 0xb0, 0xa,
    0xfb, 0x0, 0xaf, 0xb0, 0xa, 0xfb, 0x0, 0xaf,
    0xb0, 0xa, 0xfb, 0x0, 0xaf, 0xb0, 0xa, 0xfb,
    0x0,

    /* U+006A "j" */
    0x0, 0x3, 0x83, 0x0, 0x1, 0xff, 0xf2, 0x0,
    0x3f, 0xff, 0x30, 0x0, 0x9f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xb0, 0x0, 0xa, 0xfb, 0x0,
    0x0, 0xaf, 0xb0, 0x0, 0xa, 0xfb, 0x0, 0x0,
    0xaf, 0xb0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0xaf,
    0xb0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0xaf, 0xb0,
    0x0, 0xa, 0xfb, 0x0, 0x0, 0xaf, 0xb0, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0xa,
    0xfb, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0xaf, 0xb0, 0x0, 0xa, 0xfb, 0x0,
    0x0, 0xaf, 0xb0, 0x0, 0xa, 0xfb, 0x0, 0x0,
    0xaf, 0xb0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0xaf,
    0xb0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0xaf, 0xb0,
    0x0, 0xa, 0xfb, 0x0, 0x0, 0xaf, 0xa0, 0x0,
    0xc, 0xf9, 0x0, 0x37, 0xff, 0x60, 0x5f, 0xff,
    0xf1, 0x4, 0xef, 0xd4, 0x0,

    /* U+006B "k" */
    0xaf, 0xb0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0, 0xaf,
    0xb0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0, 0xaf, 0xb0,
    0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0xaf, 0xb0, 0x0, 0x5f, 0xf2, 0xaf, 0xb0, 0x0,
    0xbf, 0xc0, 0xaf, 0xb0, 0x1, 0xff, 0x60, 0xaf,
    0xb0, 0x7, 0xff, 0x10, 0xaf, 0xb0, 0xc, 0xfb,
    0x0, 0xaf, 0xb0, 0x2f, 0xf6, 0x0, 0xaf, 0xb0,
    0x8f, 0xf0, 0x0, 0xaf, 0xb0, 0xef, 0xa0, 0x0,
    0xaf, 0xb3, 0xff, 0x50, 0x0, 0xaf, 0xb9, 0xff,
    0x0, 0x0, 0xaf, 0xbf, 0xf9, 0x0, 0x0, 0xaf,
    0xdf, 0xf8, 0x0, 0x0, 0xaf, 0xbc, 0xfd, 0x0,
    0x0, 0xaf, 0xb7, 0xff, 0x20, 0x0, 0xaf, 0xb1,
    0xff, 0x80, 0x0, 0xaf, 0xb0, 0xcf, 0xd0, 0x0,
    0xaf, 0xb0, 0x6f, 0xf2, 0x0, 0xaf, 0xb0, 0x1f,
    0xf7, 0x0, 0xaf, 0xb0, 0xb, 0xfd, 0x0, 0xaf,
    0xb0, 0x6, 0xff, 0x20, 0xaf, 0xb0, 0x1, 0xff,
    0x70, 0xaf, 0xb0, 0x0, 0xbf, 0xc0, 0xaf, 0xb0,
    0x0, 0x5f, 0xf1,

    /* U+006C "l" */
    0xaf, 0xca, 0xfc, 0xaf, 0xca, 0xfc, 0xaf, 0xca,
    0xfc, 0xaf, 0xca, 0xfc, 0xaf, 0xca, 0xfc, 0xaf,
    0xca, 0xfc, 0xaf, 0xca, 0xfc, 0xaf, 0xca, 0xfc,
    0xaf, 0xca, 0xfc, 0xaf, 0xca, 0xfc, 0xaf, 0xca,
    0xfc, 0xaf, 0xca, 0xfc, 0xaf, 0xca, 0xfc, 0xaf,
    0xca, 0xfc, 0xaf, 0xca, 0xfc, 0xaf, 0xc0,

    /* U+006D "m" */
    0xaf, 0x53, 0xcf, 0xa0, 0x1b, 0xfd, 0x30, 0xaf,
    0x9e, 0xff, 0xf7, 0xaf, 0xff, 0xc0, 0xaf, 0xf9,
    0x3e, 0xfe, 0xc3, 0xaf, 0xf0, 0xaf, 0xf0, 0x8,
    0xff, 0x40, 0x3f, 0xf3, 0xaf, 0xd0, 0x6, 0xff,
    0x10, 0x1f, 0xf4, 0xaf, 0xc0, 0x5, 0xff, 0x0,
    0x1f, 0xf4, 0xaf, 0xc0, 0x5, 0xff, 0x0, 0x1f,
    0xf5, 0xaf, 0xc0, 0x5, 0xff, 0x0, 0x1f, 0xf5,
    0xaf, 0xc0, 0x5, 0xff, 0x0, 0x1f, 0xf5, 0xaf,
    0xc0, 0x5, 0xff, 0x0, 0x1f, 0xf5, 0xaf, 0xc0,
    0x5, 0xff, 0x0, 0x1f, 0xf5, 0xaf, 0xc0, 0x5,
    0xff, 0x0, 0x1f, 0xf5, 0xaf, 0xc0, 0x5, 0xff,
    0x0, 0x1f, 0xf5, 0xaf, 0xc0, 0x5, 0xff, 0x0,
    0x1f, 0xf5, 0xaf, 0xc0, 0x5, 0xff, 0x0, 0x1f,
    0xf5, 0xaf, 0xc0, 0x5, 0xff, 0x0, 0x1f, 0xf5,
    0xaf, 0xc0, 0x5, 0xff, 0x0, 0x1f, 0xf5, 0xaf,
    0xc0, 0x5, 0xff, 0x0, 0x1f, 0xf5, 0xaf, 0xc0,
    0x5, 0xff, 0x0, 0x1f, 0xf5, 0xaf, 0xc0, 0x5,
    0xff, 0x0, 0x1f, 0xf5, 0xaf, 0xc0, 0x5, 0xff,
    0x0, 0x1f, 0xf5, 0xaf, 0xc0, 0x5, 0xff, 0x0,
    0x1f, 0xf5, 0xaf, 0xc0, 0x5, 0xff, 0x0, 0x1f,
    0xf5,

    /* U+006E "n" */
    0xaf, 0x53, 0xcf, 0xb0, 0xa, 0xf9, 0xef, 0xff,
    0x70, 0xaf, 0xf9, 0x3e, 0xfc, 0xa, 0xff, 0x0,
    0x8f, 0xe0, 0xaf, 0xd0, 0x6, 0xff, 0xa, 0xfc,
    0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff, 0xa,
    0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff,
    0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5,
    0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0,
    0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf,
    0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0,
    0xaf, 0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f,
    0xf0, 0xaf, 0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0,
    0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff, 0xa, 0xfc,
    0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff, 0x0,

    /* U+006F "o" */
    0x3, 0xbf, 0xfc, 0x40, 0x2f, 0xff, 0xff, 0xf3,
    0x8f, 0xf6, 0x5e, 0xfa, 0xbf, 0xc0, 0x9, 0xfd,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xbf, 0xc0, 0x9, 0xfd,
    0x8f, 0xf6, 0x4e, 0xfa, 0x2f, 0xff, 0xff, 0xf3,
    0x2, 0xbe, 0xec, 0x30,

    /* U+0070 "p" */
    0xaf, 0x53, 0xcf, 0xb0, 0xa, 0xf9, 0xef, 0xff,
    0x80, 0xaf, 0xf9, 0x3d, 0xfc, 0xa, 0xff, 0x0,
    0x8f, 0xe0, 0xaf, 0xc0, 0x6, 0xff, 0xa, 0xfc,
    0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff, 0xa,
    0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5, 0xff,
    0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0, 0x5,
    0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf, 0xc0,
    0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0, 0xaf,
    0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f, 0xf0,
    0xaf, 0xc0, 0x5, 0xff, 0xa, 0xfc, 0x0, 0x5f,
    0xf0, 0xaf, 0xc0, 0x6, 0xff, 0xa, 0xff, 0x0,
    0x8f, 0xe0, 0xaf, 0xf9, 0x3d, 0xfc, 0xa, 0xfd,
    0xef, 0xff, 0x80, 0xaf, 0xc3, 0xcf, 0xb0, 0xa,
    0xfc, 0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0,
    0xa, 0xfc, 0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0,
    0x0, 0xa, 0xfc, 0x0, 0x0, 0x0, 0xaf, 0xc0,
    0x0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0x0, 0xaf,
    0xc0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x8, 0xfe, 0x60, 0xfe, 0x3f, 0xff, 0xf8, 0xfe,
    0x8f, 0xf5, 0x6f, 0xfe, 0xaf, 0xc0, 0xa, 0xfe,
    0xbf, 0xa0, 0x8, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xcf, 0xa0, 0x7, 0xfe, 0xcf, 0xa0, 0x7, 0xfe,
    0xbf, 0xa0, 0x8, 0xfe, 0xaf, 0xc0, 0xa, 0xfe,
    0x8f, 0xf5, 0x5f, 0xfe, 0x3f, 0xff, 0xfc, 0xfe,
    0x8, 0xfe, 0x67, 0xfe, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x7, 0xfe,

    /* U+0072 "r" */
    0xaf, 0x53, 0xdf, 0x8a, 0xf9, 0xff, 0xf4, 0xaf,
    0xfd, 0xad, 0xa, 0xff, 0x10, 0x0, 0xaf, 0xd0,
    0x0, 0xa, 0xfc, 0x0, 0x0, 0xaf, 0xc0, 0x0,
    0xa, 0xfc, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0xa,
    0xfc, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0xa, 0xfc,
    0x0, 0x0, 0xaf, 0xc0, 0x0, 0xa, 0xfc, 0x0,
    0x0, 0xaf, 0xc0, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0xaf, 0xc0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0xaf,
    0xc0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0xaf, 0xc0,
    0x0, 0xa, 0xfc, 0x0, 0x0, 0xaf, 0xc0, 0x0,
    0x0,

    /* U+0073 "s" */
    0x0, 0x6d, 0xff, 0xc4, 0x0, 0x5f, 0xff, 0xff,
    0xf3, 0xc, 0xfd, 0x45, 0xef, 0xa0, 0xff, 0x60,
    0x9, 0xfd, 0x1f, 0xf4, 0x0, 0x7f, 0xf1, 0xff,
    0x40, 0x7, 0xff, 0xf, 0xf6, 0x0, 0x7f, 0xf0,
    0xef, 0x90, 0x5, 0xcc, 0xb, 0xfe, 0x0, 0x0,
    0x0, 0x5f, 0xf9, 0x0, 0x0, 0x0, 0xbf, 0xf8,
    0x0, 0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0, 0x1,
    0xdf, 0xf7, 0x0, 0x0, 0x1, 0xef, 0xf2, 0x0,
    0x0, 0x4, 0xff, 0x81, 0xbb, 0x30, 0xd, 0xfc,
    0x2f, 0xf4, 0x0, 0x9f, 0xe1, 0xff, 0x40, 0x7,
    0xff, 0x1f, 0xf4, 0x0, 0x7f, 0xe0, 0xff, 0x60,
    0xa, 0xfd, 0xc, 0xfd, 0x46, 0xff, 0x90, 0x6f,
    0xff, 0xff, 0xf3, 0x0, 0x7d, 0xff, 0xb3, 0x0,

    /* U+0074 "t" */
    0x0, 0x88, 0x20, 0x0, 0xf, 0xf5, 0x0, 0x0,
    0xff, 0x50, 0x0, 0xf, 0xf5, 0x0, 0x0, 0xff,
    0x50, 0x0, 0xf, 0xf5, 0x0, 0xdf, 0xff, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0x23, 0xff, 0x73, 0x20,
    0xf, 0xf5, 0x0, 0x0, 0xff, 0x50, 0x0, 0xf,
    0xf5, 0x0, 0x0, 0xff, 0x50, 0x0, 0xf, 0xf5,
    0x0, 0x0, 0xff, 0x50, 0x0, 0xf, 0xf5, 0x0,
    0x0, 0xff, 0x50, 0x0, 0xf, 0xf5, 0x0, 0x0,
    0xff, 0x50, 0x0, 0xf, 0xf5, 0x0, 0x0, 0xff,
    0x50, 0x0, 0xf, 0xf5, 0x0, 0x0, 0xff, 0x50,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0xff, 0x50, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0xcf, 0xd3, 0x20, 0x7,
    0xff, 0xff, 0x0, 0x9, 0xef, 0xd0,

    /* U+0075 "u" */
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xbf, 0xa0, 0x7, 0xff, 0xbf, 0xa0, 0x7, 0xff,
    0xaf, 0xb0, 0x7, 0xff, 0x9f, 0xd0, 0xa, 0xff,
    0x7f, 0xf5, 0x5f, 0xff, 0x2f, 0xff, 0xf8, 0xff,
    0x7, 0xfe, 0x60, 0xff,

    /* U+0076 "v" */
    0x9f, 0xe0, 0x0, 0x8f, 0xf0, 0x7f, 0xf0, 0x0,
    0x9f, 0xe0, 0x5f, 0xf1, 0x0, 0xbf, 0xb0, 0x3f,
    0xf3, 0x0, 0xcf, 0x90, 0x1f, 0xf4, 0x0, 0xef,
    0x70, 0xf, 0xf6, 0x0, 0xff, 0x50, 0xd, 0xf7,
    0x1, 0xff, 0x30, 0xa, 0xf9, 0x2, 0xff, 0x10,
    0x8, 0xfa, 0x4, 0xff, 0x0, 0x6, 0xfc, 0x6,
    0xfd, 0x0, 0x4, 0xfd, 0x7, 0xfa, 0x0, 0x2,
    0xff, 0x9, 0xf8, 0x0, 0x0, 0xff, 0xa, 0xf6,
    0x0, 0x0, 0xdf, 0x2c, 0xf4, 0x0, 0x0, 0xbf,
    0x3d, 0xf2, 0x0, 0x0, 0x9f, 0x5f, 0xf0, 0x0,
    0x0, 0x7f, 0x7f, 0xe0, 0x0, 0x0, 0x5f, 0xaf,
    0xc0, 0x0, 0x0, 0x3f, 0xdf, 0xa0, 0x0, 0x0,
    0x1f, 0xff, 0x70, 0x0, 0x0, 0xe, 0xff, 0x50,
    0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0, 0xa,
    0xff, 0x10, 0x0,

    /* U+0077 "w" */
    0x9f, 0xe0, 0x0, 0x8f, 0xf4, 0x0, 0x3f, 0xf5,
    0x7f, 0xf0, 0x0, 0xaf, 0xf5, 0x0, 0x5f, 0xf3,
    0x5f, 0xf1, 0x0, 0xbf, 0xf7, 0x0, 0x6f, 0xf0,
    0x3f, 0xf3, 0x0, 0xdf, 0xf8, 0x0, 0x8f, 0xe0,
    0x1f, 0xf5, 0x0, 0xef, 0xfa, 0x0, 0x9f, 0xc0,
    0xf, 0xf6, 0x0, 0xff, 0xfc, 0x0, 0xbf, 0xa0,
    0xd, 0xf8, 0x2, 0xfd, 0xfd, 0x0, 0xdf, 0x80,
    0xa, 0xf9, 0x3, 0xfa, 0xff, 0x0, 0xef, 0x60,
    0x8, 0xfb, 0x5, 0xf8, 0xef, 0x0, 0xff, 0x40,
    0x6, 0xfd, 0x7, 0xf7, 0xcf, 0x21, 0xff, 0x20,
    0x4, 0xfe, 0x8, 0xf5, 0xaf, 0x43, 0xff, 0x0,
    0x2, 0xff, 0xa, 0xf3, 0x9f, 0x55, 0xfd, 0x0,
    0x0, 0xff, 0x1b, 0xf2, 0x7f, 0x76, 0xfb, 0x0,
    0x0, 0xdf, 0x3d, 0xf0, 0x5f, 0x98, 0xf9, 0x0,
    0x0, 0xbf, 0x5f, 0xe0, 0x3f, 0xa9, 0xf7, 0x0,
    0x0, 0x9f, 0x7f, 0xd0, 0x2f, 0xcb, 0xf5, 0x0,
    0x0, 0x7f, 0xaf, 0xb0, 0xf, 0xdc, 0xf3, 0x0,
    0x0, 0x5f, 0xdf, 0x90, 0xe, 0xfe, 0xf0, 0x0,
    0x0, 0x3f, 0xff, 0x80, 0xc, 0xff, 0xe0, 0x0,
    0x0, 0x1f, 0xff, 0x60, 0xb, 0xff, 0xc0, 0x0,
    0x0, 0xe, 0xff, 0x40, 0x9, 0xff, 0xa0, 0x0,
    0x0, 0xc, 0xff, 0x20, 0x7, 0xff, 0x80, 0x0,
    0x0, 0xa, 0xff, 0x10, 0x5, 0xff, 0x60, 0x0,

    /* U+0078 "x" */
    0x9f, 0xb0, 0x0, 0x3f, 0xf3, 0x5f, 0xf0, 0x0,
    0x7f, 0xe0, 0xf, 0xf4, 0x0, 0xcf, 0x90, 0xb,
    0xf9, 0x0, 0xff, 0x40, 0x6, 0xfd, 0x4, 0xff,
    0x0, 0x2, 0xff, 0x29, 0xfa, 0x0, 0x0, 0xdf,
    0x7d, 0xf5, 0x0, 0x0, 0x8f, 0xdf, 0xf0, 0x0,
    0x0, 0x4f, 0xff, 0xb0, 0x0, 0x0, 0xe, 0xff,
    0x60, 0x0, 0x0, 0xa, 0xff, 0x10, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0x0, 0xc, 0xff, 0x30,
    0x0, 0x0, 0x1f, 0xff, 0x80, 0x0, 0x0, 0x5f,
    0xef, 0xd0, 0x0, 0x0, 0xaf, 0x7f, 0xf2, 0x0,
    0x0, 0xef, 0x2c, 0xf7, 0x0, 0x3, 0xfe, 0x7,
    0xfc, 0x0, 0x8, 0xfa, 0x3, 0xff, 0x10, 0xc,
    0xf6, 0x0, 0xef, 0x50, 0x1f, 0xf2, 0x0, 0xaf,
    0xa0, 0x5f, 0xe0, 0x0, 0x5f, 0xf0, 0xaf, 0xa0,
    0x0, 0x1f, 0xf4,

    /* U+0079 "y" */
    0x7f, 0xf1, 0x0, 0xf, 0xf8, 0x5f, 0xf2, 0x0,
    0x2f, 0xf6, 0x3f, 0xf4, 0x0, 0x3f, 0xf3, 0x1f,
    0xf5, 0x0, 0x5f, 0xf1, 0xf, 0xf7, 0x0, 0x7f,
    0xe0, 0xd, 0xf9, 0x0, 0x9f, 0xc0, 0xb, 0xfa,
    0x0, 0xaf, 0xa0, 0x8, 0xfc, 0x0, 0xcf, 0x70,
    0x6, 0xfe, 0x0, 0xef, 0x50, 0x4, 0xff, 0x0,
    0xff, 0x20, 0x2, 0xff, 0x11, 0xff, 0x0, 0x0,
    0xff, 0x33, 0xfe, 0x0, 0x0, 0xef, 0x45, 0xfb,
    0x0, 0x0, 0xcf, 0x66, 0xf9, 0x0, 0x0, 0xaf,
    0x88, 0xf7, 0x0, 0x0, 0x8f, 0x9a, 0xf4, 0x0,
    0x0, 0x5f, 0xbc, 0xf2, 0x0, 0x0, 0x3f, 0xdd,
    0xf0, 0x0, 0x0, 0x1f, 0xef, 0xd0, 0x0, 0x0,
    0xf, 0xff, 0xb0, 0x0, 0x0, 0xd, 0xff, 0x80,
    0x0, 0x0, 0xb, 0xff, 0x60, 0x0, 0x0, 0x9,
    0xff, 0x30, 0x0, 0x0, 0x7, 0xff, 0x10, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x9, 0xfc,
    0x0, 0x0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0x0,
    0xf, 0xf7, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x8f,
    0xf0, 0x0, 0x0,

    /* U+007A "z" */
    0xe, 0xff, 0xff, 0xff, 0xe0, 0xef, 0xff, 0xff,
    0xfd, 0x2, 0x33, 0x34, 0xff, 0x90, 0x0, 0x0,
    0x5f, 0xf4, 0x0, 0x0, 0xa, 0xff, 0x0, 0x0,
    0x0, 0xff, 0xb0, 0x0, 0x0, 0x4f, 0xf6, 0x0,
    0x0, 0x9, 0xff, 0x10, 0x0, 0x0, 0xef, 0xd0,
    0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x7, 0xff,
    0x30, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0, 0x1f,
    0xfa, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0,
    0xbf, 0xf0, 0x0, 0x0, 0xf, 0xfb, 0x0, 0x0,
    0x4, 0xff, 0x70, 0x0, 0x0, 0x9f, 0xf2, 0x0,
    0x0, 0xe, 0xfd, 0x0, 0x0, 0x3, 0xff, 0x80,
    0x0, 0x0, 0x8f, 0xf6, 0x33, 0x33, 0x1a, 0xff,
    0xff, 0xff, 0xf6, 0xbf, 0xff, 0xff, 0xff, 0x60,

    /* U+007B "{" */
    0x0, 0x0, 0x3c, 0xff, 0xa0, 0x0, 0xe, 0xff,
    0xfa, 0x0, 0x4, 0xff, 0x61, 0x10, 0x0, 0x6f,
    0xe0, 0x0, 0x0, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x0, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x8f, 0xc0, 0x0, 0x0, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x8, 0xfc,
    0x0, 0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x8,
    0xfc, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x1, 0xff, 0x80, 0x0,
    0x0, 0x9f, 0xf2, 0x0, 0x6, 0xcf, 0xf8, 0x0,
    0x0, 0xef, 0xfb, 0x0, 0x0, 0xb, 0xff, 0xf6,
    0x0, 0x0, 0x1, 0xcf, 0xf1, 0x0, 0x0, 0x1,
    0xff, 0x70, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0,
    0x0, 0x9f, 0xc0, 0x0, 0x0, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x8, 0xfc,
    0x0, 0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x8,
    0xfc, 0x0, 0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x8f, 0xc0, 0x0,
    0x0, 0x8, 0xfc, 0x0, 0x0, 0x0, 0x7f, 0xd0,
    0x0, 0x0, 0x5, 0xff, 0x30, 0x0, 0x0, 0xe,
    0xff, 0xfa, 0x0, 0x0, 0x4f, 0xff, 0xa0, 0x0,
    0x0, 0x2, 0x32,

    /* U+007C "|" */
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,
    0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3, 0x3f, 0xf3,

    /* U+007D "}" */
    0x7f, 0xfd, 0x50, 0x0, 0x0, 0x7f, 0xff, 0xf1,
    0x0, 0x0, 0x1, 0x3f, 0xf7, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0,
    0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x5, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x5f,
    0xfd, 0x70, 0x0, 0x0, 0x8, 0xff, 0xf1, 0x0,
    0x0, 0x3f, 0xff, 0xd0, 0x0, 0x0, 0xef, 0xd3,
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x0, 0x0,
    0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0,
    0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x1e, 0xf8, 0x0, 0x0, 0x7f,
    0xff, 0xf2, 0x0, 0x0, 0x7f, 0xff, 0x70, 0x0,
    0x0, 0x13, 0x21, 0x0, 0x0, 0x0,

    /* U+007E "~" */
    0x1, 0x9e, 0xfd, 0x94, 0x0, 0x0, 0x96, 0x1,
    0xdf, 0xff, 0xff, 0xfe, 0xb9, 0xcf, 0xf3, 0x9f,
    0xe8, 0x7a, 0xef, 0xff, 0xff, 0xf8, 0x0, 0x92,
    0x0, 0x0, 0x49, 0xdf, 0xc5, 0x0,

    /* U+3002 "。" */
    0x0, 0x3, 0x66, 0x20, 0x0, 0x1, 0xbf, 0xde,
    0xfa, 0x0, 0xc, 0xd3, 0x0, 0x4f, 0x80, 0x3f,
    0x30, 0x0, 0x6, 0xe0, 0x6e, 0x0, 0x0, 0x2,
    0xf1, 0x6f, 0x0, 0x0, 0x3, 0xf0, 0x1f, 0x50,
    0x0, 0x9, 0xc0, 0x7, 0xf7, 0x22, 0x9f, 0x40,
    0x0, 0x6e, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0,

    /* U+4E09 "三" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x16, 0x31,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x6f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0xdc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xfd, 0x10, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x1, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4E2D "中" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x20, 0xc, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0x50, 0xdf, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdf, 0xfe, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xef, 0xff, 0x2d, 0xf8, 0x22, 0x22, 0x22, 0x22,
    0x22, 0xcf, 0x92, 0x22, 0x22, 0x22, 0x22, 0x24,
    0xff, 0x30, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x0, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0xc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0xc,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0, 0xcf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0xc, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x0, 0xcf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0xc, 0xf8, 0x22,
    0x22, 0x22, 0x22, 0x22, 0xcf, 0x92, 0x22, 0x22,
    0x22, 0x22, 0x24, 0xff, 0x0, 0xcf, 0xed, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdf, 0xfe, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf1, 0xd, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x10, 0xdf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xe1, 0x9, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4EAE "亮" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe3, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd2, 0x0, 0x0, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8b, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdf, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf7, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xef, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0x30, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xe8, 0x0, 0xa, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x60, 0x0, 0x7,
    0xff, 0xa0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x2e, 0x30,
    0x0, 0x0, 0xef, 0xf2, 0x0, 0x0, 0x0, 0xbb,
    0x10, 0x0, 0x0, 0x0, 0x1, 0xcc, 0x10, 0x0,
    0x9, 0x20, 0x0, 0x0, 0x1, 0x51, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xc, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0, 0x0,
    0x1, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x2c, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xb4,
    0x33, 0x33, 0x33, 0xbf, 0xfb, 0x0, 0x0, 0x2,
    0x9f, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x2, 0x6c, 0xe9, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x78, 0x88, 0x88, 0x88,
    0x75, 0x0, 0xa, 0x96, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4ECA "今" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xfc, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xeb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x4a, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa,
    0x1, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xe1, 0x0, 0x7d, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x50, 0x0, 0xc,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa,
    0x0, 0x0, 0x2, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xd0, 0x0, 0x0, 0x0, 0x6f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x9, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x50, 0x6, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf5, 0x0, 0x7, 0xe5,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x0, 0x9f, 0xb2, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xe3, 0x0, 0x0, 0x0, 0xc, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xfd, 0x72, 0x0,
    0x0, 0x0, 0x6, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf8, 0x30, 0x0, 0x2c,
    0xc2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0x70,
    0x0, 0x6, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x37, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4F53 "体" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xda, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x2d, 0x20, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x11, 0x11, 0x11, 0x11, 0x11, 0xcf, 0x71,
    0x11, 0x11, 0x13, 0xef, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0xaf, 0xff, 0xee, 0xee,
    0xff, 0xff, 0xef, 0xee, 0xee, 0xee, 0xee, 0xec,
    0x10, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0, 0x4,
    0x10, 0x0, 0x0, 0xff, 0xff, 0x6c, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf9, 0x10, 0x0, 0x0, 0x0, 0x5, 0xff, 0xdf,
    0x68, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xce, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0xcf, 0x63, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x2e, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf3, 0xcf, 0x60, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf7,
    0xe, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0xcf, 0x60, 0x9b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xb0, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x30, 0xcf, 0x60, 0x3f, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0xe, 0xf2,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0xcf, 0x60,
    0xd, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xe2,
    0x0, 0xe, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0xf2,
    0x0, 0xcf, 0x60, 0x6, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0x20, 0x0, 0xe, 0xf2, 0x0, 0x0,
    0x0, 0xaf, 0x80, 0x0, 0xcf, 0x60, 0x0, 0xed,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x0, 0x0, 0xe,
    0xf2, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0xd, 0xf3,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x1e, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf2, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf2, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0xef, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0xc, 0xd0,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x4f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf2,
    0x0, 0x8e, 0x10, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x19, 0xa, 0xff, 0xf9, 0x10, 0x0, 0x0,
    0x0, 0xe, 0xf2, 0x5, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x60, 0x1, 0xdf, 0xb1, 0xdf, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0xe, 0xf2, 0x4d, 0x20,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x2d, 0x81, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf3, 0xb1, 0x0, 0x0, 0x52, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x71,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5149 "光" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xe8, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xb1, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x10, 0x0, 0x0, 0x3, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbe, 0x30,
    0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x9,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf5, 0x0, 0x0, 0x1, 0xff, 0x10,
    0x0, 0x0, 0xe, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x60, 0x0,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x5f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf3, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0,
    0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfb, 0x0, 0x1, 0xff,
    0x10, 0x0, 0x2, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x0, 0x1, 0xff, 0x10, 0x0, 0xa, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfc, 0x0, 0x1, 0xff, 0x10, 0x0,
    0x2f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xe5, 0x0, 0x1,
    0xff, 0x10, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x10, 0x2, 0xf2, 0x0,
    0x0, 0x0, 0x4d, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x10,
    0xb, 0x50, 0x0, 0x0, 0x3, 0xff, 0xd1, 0x0,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x35, 0x21, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x4, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf3, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x4, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x90, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x10, 0x0, 0x0, 0x4, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf0, 0x0, 0x0, 0x0, 0x4, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x40, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xb3,
    0x22, 0x22, 0x22, 0x4e, 0xff, 0xe0, 0x0, 0x0,
    0x4e, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x2b, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9b, 0xbb, 0xbb,
    0xbb, 0xba, 0x71, 0x0, 0xa, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5747 "均" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x16, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x40, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcf, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0xa,
    0xf8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0xef,
    0xa0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x4,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0xb, 0xf4, 0x0, 0x8f, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x4, 0xdd,
    0xdd, 0xde, 0xff, 0xde, 0xff, 0xf4, 0x1f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf4,
    0x0, 0x4, 0x21, 0x11, 0x6f, 0xd1, 0x11, 0x11,
    0x19, 0xe1, 0x5, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x30, 0x0, 0x0, 0x0, 0x5, 0xfd,
    0x0, 0x0, 0x2, 0xf4, 0x0, 0x1e, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0x0, 0xb8, 0x0, 0x0,
    0x4f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x5a,
    0x0, 0x0, 0x0, 0xaf, 0xf6, 0x0, 0x0, 0x0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x0, 0x3, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf2,
    0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x60, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0x2,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd9, 0x0,
    0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x72, 0x4, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0, 0x5f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x0, 0x35, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xc2,
    0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x0, 0x17, 0xdd, 0x50, 0x0, 0x0, 0x3,
    0xcf, 0x70, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xd5, 0xbf, 0xc5, 0x0, 0x0,
    0x0, 0x3b, 0xfc, 0x20, 0x0, 0x0, 0x7, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xfc, 0x40,
    0x0, 0x0, 0x5, 0xcf, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xc0, 0x0, 0x0, 0x26, 0xbf, 0xff,
    0xc4, 0x0, 0x0, 0x1, 0x7e, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfa, 0x0, 0x8, 0xdf,
    0xff, 0xfc, 0x40, 0x0, 0x0, 0x9, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90,
    0x0, 0x5f, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf7, 0x0, 0x0, 0x9f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x96, 0x42, 0x12, 0x7f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xcf,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfd, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x45, 0x0, 0x0, 0x0, 0x0,

    /* U+5927 "大" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf9, 0x0, 0x2, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x23, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf7, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe1,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xb0, 0xc8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x70, 0x5f, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x20, 0xe, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x0, 0x7, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x7f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0xd, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xe7, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xfb, 0x50, 0x0, 0x0,
    0x4, 0xde, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfb,
    0x70, 0x0, 0x2, 0xbe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xcf, 0x40, 0x0, 0x2, 0xac, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x8,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5929 "天" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x20, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x46,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x50, 0x0, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x2b, 0xfb, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x4f, 0xff, 0xf5, 0x0, 0x2e, 0xff,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xfe, 0xdf,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0x30,
    0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf1, 0xc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xd0, 0x7, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x2,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x30, 0x0, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfd, 0x0, 0x0, 0x6e, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0x0, 0x0,
    0x7, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0xec, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x2d, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xfa, 0x0, 0x6, 0xf9, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xef, 0xfc, 0x41, 0x2, 0xcb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0xc0, 0x0, 0x7a, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5E73 "平" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x9c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xce, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x19, 0x64, 0x33, 0x33, 0x33,
    0x33, 0x36, 0xff, 0x43, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x99, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0x30, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xff, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xf7, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x0, 0x0, 0x6, 0xff, 0xe5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0x4, 0xff, 0x10, 0x0, 0x0, 0xe, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfb, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x0,
    0x6f, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x90, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0xee, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf1,
    0x0, 0x4, 0xff, 0x10, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf3, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x3f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x4,
    0xff, 0x10, 0x0, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0x30, 0x0, 0x4, 0xff, 0x10, 0x8, 0x60, 0x0,
    0x0, 0x0, 0xa, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x2, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf5, 0x0,
    0x2a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbc, 0xff, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbc,
    0xff, 0xff, 0x50, 0x6, 0x96, 0x54, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x47, 0xff, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9c, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x0, 0xc7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xfc, 0x0, 0x0,
    0x0, 0xc, 0xfd, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xfc, 0xbb, 0xbb, 0xbb, 0xbb, 0xbe, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xcf, 0xa3, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x31, 0x0, 0x0, 0xb, 0xf9,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0, 0x0,
    0x3, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x90, 0x0, 0x0, 0x0, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0,
    0xf, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x2f, 0xe0, 0x0, 0x8, 0x60, 0x0, 0x0, 0x0,
    0xa, 0xf9, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x8, 0xff, 0x60,
    0x0, 0x0, 0x0, 0xbf, 0x99, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0xb, 0xf8, 0x4,
    0x10, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x80, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf5, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x11, 0x11,
    0x11, 0x13, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0xff,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0xf, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90,
    0x0, 0x0, 0x20, 0x9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1d, 0x20,
    0x0, 0x0, 0x0, 0x1e, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0x10, 0x0, 0x0, 0xc, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0x0, 0x0, 0x9,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdc,
    0x0, 0x9, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xed, 0x28, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfe, 0xef, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xdf, 0xe6, 0x1, 0xbf, 0xff,
    0xb5, 0x10, 0x0, 0x0, 0x0, 0x1, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0xfd, 0x60, 0x0,
    0x0, 0x5d, 0xff, 0xff, 0xeb, 0x86, 0x43, 0x10,
    0x87, 0x0, 0x0, 0x0, 0x0, 0x49, 0xef, 0x93,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff,
    0xff, 0xd6, 0x1c, 0x0, 0x0, 0x1, 0x59, 0xed,
    0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0xcf, 0xff, 0x60, 0x6, 0x30, 0x0, 0x4b,
    0xb8, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x60, 0x0,

    /* U+5F3A "强" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x30, 0x0, 0x3, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xc1, 0x0, 0x0, 0x12, 0x22, 0x22,
    0x22, 0x23, 0xef, 0x70, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x1,
    0xde, 0xcc, 0xcc, 0xcc, 0xcf, 0xfc, 0x0, 0x3,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf1,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x20, 0x0, 0x2f, 0xf3, 0x33, 0x33,
    0x33, 0x33, 0x35, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x2, 0xff,
    0xbb, 0xbb, 0xdb, 0xbb, 0xbb, 0xcf, 0xf0, 0x0,
    0x0, 0x0, 0x48, 0x10, 0x0, 0x0, 0xef, 0x20,
    0x0, 0x3f, 0xd0, 0x0, 0xd, 0xc7, 0x10, 0x2,
    0xd9, 0x0, 0x0, 0x0, 0x5, 0xff, 0xdd, 0xdd,
    0xdf, 0xf3, 0x0, 0x1, 0x30, 0x0, 0x0, 0xdf,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xb1, 0x11, 0x11, 0xef, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf9, 0x0, 0x0, 0xb, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0xd, 0xf2,
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0xa, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcb, 0x20, 0x0,
    0x0, 0xdf, 0x30, 0x0, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xee, 0xef,
    0xff, 0x50, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0xdf, 0x20,
    0x0, 0x0, 0x7f, 0xa0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x1, 0xe8, 0x0, 0xa, 0xf5, 0x0, 0x0,
    0xd, 0xf2, 0x0, 0x0, 0x7, 0xf9, 0x0, 0x0,
    0xbf, 0xfe, 0xee, 0xee, 0xef, 0xfa, 0x0, 0xaf,
    0x50, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x0, 0x7f,
    0x90, 0x0, 0x2, 0xea, 0x11, 0x11, 0x15, 0xff,
    0x10, 0xa, 0xf5, 0x0, 0x0, 0xd, 0xf2, 0x0,
    0x0, 0x7, 0xf9, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x6f, 0xc0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0xdf, 0x20, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0xb, 0xf5,
    0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x7, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf6, 0x0, 0xc, 0xf5, 0x0, 0x0, 0xd,
    0xf2, 0x0, 0x0, 0x7, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0xca, 0x10,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x0,
    0x2, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x1,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x20, 0x0, 0x8, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf2, 0x0, 0x0, 0xb, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf2,
    0x0, 0x0, 0x23, 0xbf, 0xf2, 0x0, 0x7, 0x83,
    0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x12, 0x45,
    0x68, 0xff, 0xcd, 0xef, 0xff, 0xfd, 0xff, 0xb0,
    0x0, 0x8, 0xff, 0xef, 0xf7, 0x0, 0x6b, 0xde,
    0xff, 0xff, 0xff, 0xff, 0xdb, 0x97, 0x53, 0x10,
    0x9, 0xfe, 0x0, 0x0, 0x6, 0xff, 0xfb, 0x0,
    0x3, 0xff, 0xff, 0xfc, 0x97, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0xc,
    0xf8, 0x0, 0x0, 0xb, 0xe8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd3, 0x0,
    0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+6587 "文" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf6, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe5, 0x0, 0x5, 0x20, 0x0, 0x0, 0xa, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x79, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc7, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x30,
    0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc9, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf1, 0x0, 0x0, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x80,
    0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x10, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xf8, 0x0, 0x2, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf2,
    0x0, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xc0, 0x3f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x7c, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x6b,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfe, 0x30, 0xa, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2c, 0xfb, 0x10, 0x0, 0x8, 0xff, 0xfa,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf6, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xff, 0xc6, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xcf, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xc8,
    0x52, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xfb,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19,
    0xff, 0xff, 0xff, 0xff, 0xe7, 0x0, 0x0, 0x4,
    0xcf, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x8e, 0xff, 0xff, 0x92, 0x0,
    0x3, 0x9d, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xae,
    0x90, 0x0, 0x9, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+65E5 "日" */
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x6d, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfb, 0x10, 0x6f, 0xfe, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0xd0, 0x6f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x20, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x5f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x0, 0x5f, 0xe2, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x25, 0xff, 0x0, 0x5f, 0xfd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xff, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x0,
    0x6f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x0, 0x6f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x6a, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+65F6 "时" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xee, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x6,
    0xb3, 0x0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0xef,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x5, 0x10, 0x4, 0xfe, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x6, 0xfd, 0x10, 0x4f,
    0xe0, 0x0, 0x0, 0xe, 0xf5, 0x9b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbe, 0xfd, 0xbb, 0xff, 0xfe,
    0x24, 0xfe, 0x0, 0x0, 0x0, 0xef, 0x41, 0x97,
    0x44, 0x33, 0x33, 0x33, 0x33, 0xdf, 0x83, 0x33,
    0x33, 0x32, 0x4f, 0xe0, 0x0, 0x0, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x3, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x4f, 0xfc, 0xcc, 0xcc, 0xcf, 0xf4, 0x0, 0x1e,
    0x80, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x22, 0x22, 0x22, 0xef, 0x40,
    0x0, 0x5f, 0xc1, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0xe,
    0xf4, 0x0, 0x0, 0xbf, 0xd1, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0xef, 0x40, 0x0, 0x3, 0xff, 0xc0, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0xc, 0xff,
    0x40, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x6f, 0xf4, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0xe, 0xf4, 0x0,
    0x0, 0x1, 0xfd, 0x0, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x6, 0x30, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x0, 0x0, 0xef, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0xe, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xdd, 0xdd, 0xdd, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0x5f, 0xe1, 0x11, 0x11, 0x1e, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0,
    0xef, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x5f, 0xe0, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2b, 0x97, 0x65, 0x9f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xcf, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6700 "最" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x39, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x71, 0x0,
    0x5f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x60, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x2, 0x10, 0x4, 0xfe, 0x0, 0x0, 0x0, 0xe,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0xe, 0xf2, 0x56, 0x66,
    0x66, 0x66, 0x66, 0x66, 0xef, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x15, 0xc9, 0xe8, 0x88, 0x88, 0x88, 0x8f, 0xfd,
    0x30, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0xa, 0x30, 0x0, 0x0,
    0x5, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x68,
    0x0, 0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xf1,
    0x0, 0x2, 0xe0, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0xd, 0x50, 0x0, 0x9,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x8d,
    0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x2, 0xf6, 0x0, 0xaf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xe, 0xf1, 0x0, 0x0, 0xa, 0xe1, 0x3f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0xef, 0x11, 0x47, 0x80, 0x3f,
    0xbc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0xe9,
    0x50, 0x0, 0xaf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xe2, 0x58, 0xbd, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x4, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x6a, 0xff, 0xff,
    0xfd, 0x95, 0x1e, 0xf1, 0x0, 0x0, 0x2, 0xef,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xea, 0x61, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x1, 0xde, 0x34, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xd8, 0x30, 0x0, 0x0, 0x0, 0xe,
    0xf1, 0x0, 0x2, 0xed, 0x20, 0x4, 0xff, 0xf7,
    0x10, 0x0, 0x0, 0x1d, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x3, 0xea, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xb8, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x6, 0xe5,
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x2a, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x67, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6708 "月" */
    0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xe3, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x9f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x40, 0x0, 0x0, 0x2, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x40, 0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0x97, 0x65, 0x5a, 0xff, 0x30,
    0x0, 0x6, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6b, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x6e, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xff, 0xf6, 0x0, 0x7, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xee, 0x50, 0x0, 0x46, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x0,

    /* U+6A19 "標" */
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xb3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0xe,
    0xf0, 0x0, 0x0, 0x14, 0x10, 0x0, 0xf, 0xc0,
    0x0, 0x4f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x4f, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x4f,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf0, 0x0, 0x40, 0x2, 0x60, 0x0, 0xf,
    0xc0, 0x0, 0x4f, 0x60, 0x0, 0x69, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf0, 0x7, 0xfc, 0x12,
    0xfd, 0xaa, 0xaf, 0xea, 0xaa, 0xcf, 0xca, 0xab,
    0xff, 0xb0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0xfd, 0x33, 0x3f, 0xc3, 0x33,
    0x7f, 0x83, 0x33, 0xff, 0x60, 0x0, 0x2, 0x20,
    0x0, 0x4f, 0xf0, 0x0, 0x0, 0x2, 0xfd, 0x0,
    0xf, 0xc0, 0x0, 0x4f, 0x60, 0x0, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0xf, 0xc0, 0x0, 0x4f, 0x60,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0xf, 0xc0,
    0x0, 0x4f, 0x60, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf6, 0x0, 0x0, 0x1, 0xfd,
    0x0, 0xf, 0xc0, 0x0, 0x4f, 0x60, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfb, 0xd3,
    0x0, 0x1, 0xfd, 0x0, 0xf, 0xc0, 0x0, 0x4f,
    0x60, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf1, 0xef, 0x60, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xf0, 0x6f, 0xf4, 0x2,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xae, 0xf0,
    0xd, 0xf9, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x2e, 0xf0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0x10, 0x0,
    0x0, 0x0, 0x1, 0xfa, 0xe, 0xf0, 0x1, 0xb1,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0xcf, 0xc0, 0x0, 0x0, 0x0, 0x7, 0xf2, 0xe,
    0xf0, 0x0, 0x0, 0x0, 0xaf, 0xdc, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xb7, 0x0, 0x0, 0x0,
    0xe, 0x90, 0xe, 0xf0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x10, 0xe, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x43, 0x0, 0x0, 0xc7, 0x0,
    0xe, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x30,
    0x3, 0xd0, 0x0, 0xe, 0xf0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0xb, 0x30, 0x0, 0xe, 0xf0,
    0x0, 0x0, 0x42, 0x0, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0x0,
    0x0, 0xe, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xa1,
    0x0, 0x1, 0xfe, 0x0, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0xe, 0xf0, 0x0, 0x0,
    0x0, 0x9, 0xfd, 0x10, 0x1, 0xfe, 0x0, 0x8c,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf0, 0x0, 0x0, 0x0, 0x4f, 0xfb, 0x50, 0x1,
    0xfe, 0x0, 0x6, 0xfc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x2, 0xff,
    0x80, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x1e, 0xf6, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x5, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x1, 0xdf, 0x40, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x1d, 0xd2,
    0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0xe, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x1, 0xd9, 0x0, 0x0, 0x3, 0x10, 0x5, 0xfe,
    0x0, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x2c, 0x40, 0x0, 0x0, 0x6,
    0xdf, 0xef, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x61,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+6C60 "池" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xc2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x48, 0x0, 0x33,
    0x0, 0xf8, 0x0, 0x0, 0xc, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x10, 0xf, 0xfc, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0xff, 0x20,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x6b, 0x20, 0x0, 0x0, 0x0, 0x57, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0x6f, 0xf6, 0x0, 0x0, 0xaf, 0x80, 0x0, 0x0,
    0xa, 0x30, 0x0, 0xff, 0x10, 0x0, 0xc, 0xf4,
    0x2, 0x6a, 0xdc, 0xff, 0x60, 0x0, 0x0, 0xdf,
    0xc1, 0x0, 0x0, 0xd0, 0x0, 0xe, 0xf1, 0x0,
    0x0, 0xcf, 0xbc, 0xb7, 0x30, 0x2f, 0xe0, 0x0,
    0x0, 0x3, 0xff, 0xc0, 0x0, 0x69, 0x0, 0x0,
    0xef, 0x10, 0x48, 0xbf, 0xf5, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x0, 0xa, 0xff, 0x20, 0xb,
    0x40, 0x0, 0x1f, 0xfc, 0xb8, 0x40, 0xcf, 0x40,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x3f,
    0xf1, 0x1, 0xf1, 0x7a, 0xcb, 0xff, 0x20, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x2, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xba, 0x0, 0x7b, 0x7, 0x30, 0xe,
    0xf1, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x0, 0xef, 0x10, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x3, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xf1, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0,
    0xcf, 0x40, 0x0, 0x0, 0x4f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0xc, 0xf4, 0x0, 0x0, 0x4, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x70, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf3, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0xc,
    0xf4, 0x0, 0x0, 0x5, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbe, 0x0, 0x0, 0x0, 0xe, 0xf1,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x7f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x90, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0xc, 0xf4, 0x6a, 0x86,
    0x6e, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4,
    0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0xcf,
    0x40, 0x4c, 0xff, 0xff, 0x20, 0x0, 0x4, 0xcb,
    0xad, 0xff, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0xd, 0xf4, 0x0, 0x8, 0xff, 0x60, 0x5,
    0x0, 0x0, 0x6e, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xe, 0xf1, 0x0, 0x0, 0xdf, 0x50, 0x0, 0x8,
    0x30, 0x0, 0xd0, 0x0, 0x0, 0x1e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0xe, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0xbf, 0x70, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0,
    0x0, 0xec, 0x30, 0x0, 0x0, 0x0, 0x1, 0xf0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x20, 0x0, 0x0, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf8, 0x0, 0x0, 0x3f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf4,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x0, 0x0, 0xc,
    0xfa, 0x21, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x4e, 0xff, 0xb0, 0x0, 0x6f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x1, 0xef,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x71, 0x0,
    0x0, 0x0, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+706F "灯" */
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xc4, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x0, 0x8e, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x10, 0x0, 0x0, 0x64,
    0x21, 0x11, 0x11, 0x4f, 0xf2, 0x11, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x26, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x10, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf1, 0x3, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x80, 0x1, 0xff, 0x10, 0xdf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x69, 0x0, 0x1f,
    0xf1, 0xaf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x90, 0x0, 0xff, 0x8e, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xf8, 0x0, 0xf, 0xfb, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf4,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfd, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbb, 0x20, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf4, 0xdb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x2, 0xee, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x4, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf2,
    0x0, 0xa, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0x30, 0x0, 0x0, 0xaf,
    0x70, 0x0, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x1, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x97, 0x54, 0x22, 0xbf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xe5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x7e, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7167 "照" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4c, 0x10, 0x0, 0x0, 0x3, 0xc3,
    0x0, 0x0, 0x0, 0xcf, 0x40, 0x1a, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xfe, 0x30, 0x0,
    0x0, 0x3f, 0xfe, 0xee, 0xee, 0xef, 0xfe, 0x10,
    0x39, 0x65, 0x4f, 0xf8, 0x44, 0x44, 0x44, 0xdf,
    0xc2, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0x2, 0xff, 0x20, 0x0,
    0x0, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x6f,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x0, 0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x1, 0xff, 0x20, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xb0,
    0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x0,
    0x2f, 0xe0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x1e, 0xf2, 0x0, 0x86, 0x33, 0x6f, 0xf4, 0x0,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xa, 0xf8, 0x0, 0x2, 0x9f, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x5, 0xfb, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xbb, 0xbb, 0xbb, 0xff, 0x0, 0x2, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0xbb, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf4, 0x44, 0x44, 0x4f, 0xf0,
    0x1, 0xeb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x52, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0xff, 0x2, 0xd8, 0xd, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xe4, 0x0, 0x0, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0xf, 0xf1, 0x72, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0xd, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x44, 0x44, 0x4f, 0xf1, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xbb, 0xbb, 0xbb, 0xff, 0x20, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0xf,
    0xf2, 0x0, 0x0, 0xef, 0x60, 0x0, 0x0, 0x0,
    0x3, 0xfd, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0,
    0x0, 0x0, 0xfa, 0x10, 0x0, 0x8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0x0,
    0x0, 0xd, 0x20, 0x0, 0x0, 0x4, 0xd1, 0x0,
    0x0, 0x0, 0x7c, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xd0, 0x0, 0x0, 0x8e, 0x20, 0x0, 0x0,
    0xc, 0xe2, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x1, 0xfd,
    0x10, 0x0, 0x0, 0x4f, 0xe2, 0x0, 0x0, 0x2,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0x0,
    0x0, 0xc, 0xfa, 0x0, 0x0, 0x0, 0xdf, 0xd0,
    0x0, 0x0, 0x8, 0xff, 0x90, 0x0, 0x0, 0x1e,
    0xfa, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x7, 0xff, 0x70, 0x0, 0x0, 0x1f, 0xff, 0x50,
    0x0, 0xb, 0xff, 0x70, 0x0, 0x0, 0x4, 0xff,
    0x60, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0, 0x0,
    0x8f, 0xf9, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x3, 0xff, 0x80, 0x2, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0x8, 0x90, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0,
    0x2, 0x42, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7259 "牙" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x8c, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xef, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x19, 0x64,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x36, 0xfe,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xae, 0x20, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfd, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xd5, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x34, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf8, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xb0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfe,
    0x10, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf3, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x40, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x50, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xed, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2c, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x17,
    0x65, 0x43, 0x4d, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xb3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x9e, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7535 "电" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf8, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0x0, 0x0, 0x0,
    0xec, 0x32, 0x22, 0x22, 0x22, 0x25, 0xff, 0x22,
    0x22, 0x22, 0x22, 0x23, 0xef, 0x50, 0x0, 0x0,
    0xef, 0xdc, 0xcc, 0xcc, 0xcc, 0xcd, 0xff, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcd, 0xff, 0xe1, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x0,
    0xef, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xb3, 0x0, 0x62, 0x0,
    0xed, 0x30, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xcd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0xb6, 0x0,

    /* U+7684 "的" */
    0x0, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x70, 0x0, 0xf2, 0x0,
    0x0, 0x3b, 0x10, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x7, 0x0, 0x2f, 0xd7, 0x9e, 0x77,
    0x77, 0x7f, 0xfc, 0x10, 0x0, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x2, 0xff, 0x77, 0x77,
    0x77, 0x77, 0xff, 0xf5, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x9, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfb, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x1, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x70, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x9f, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf6, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x3f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x60, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0xe, 0xf1, 0xc, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf6, 0x1,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xef, 0x16, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x50,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0xe, 0xf3, 0xc1,
    0x1, 0xc1, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf5,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xef, 0x31,
    0x0, 0x7, 0xe2, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x50, 0xf, 0xf0, 0x0, 0x0, 0x0, 0xe, 0xf1,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0x0, 0x1f,
    0xf4, 0x0, 0xff, 0xdd, 0xdd, 0xdd, 0xdd, 0xff,
    0x10, 0x0, 0x0, 0x6f, 0xe1, 0x0, 0x0, 0x1,
    0xff, 0x40, 0xf, 0xf2, 0x22, 0x22, 0x22, 0x2e,
    0xf1, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0,
    0x1f, 0xf3, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0xc, 0xfe, 0x0, 0x0,
    0x2, 0xff, 0x30, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xe, 0xf2, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x20, 0x0, 0x0, 0x4, 0xf8, 0x0,
    0x0, 0x3, 0xff, 0x20, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0xe, 0xf2, 0x0, 0x0, 0x0, 0x5, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x10, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfd, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x1, 0x20, 0x0, 0x0, 0x2f, 0xf8, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x0,
    0x0, 0x0, 0x3c, 0xfc, 0xa9, 0x9e, 0xff, 0x30,
    0x2, 0xff, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0xc0,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0xd, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xd2,
    0x0, 0x2, 0x80, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+79BB "离" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xfc, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x90, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x2,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x82, 0x0, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0x0, 0x1, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfb, 0x11, 0x8b, 0x61, 0x0, 0x0, 0x6f, 0xfb,
    0x0, 0x2f, 0xe8, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x80, 0x0, 0x19, 0xfa, 0x40,
    0x8f, 0xf9, 0x20, 0x2, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0,
    0x2, 0xaf, 0xef, 0xb2, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x20, 0x0, 0x0, 0x1, 0xcf, 0xfd, 0x60, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf2, 0x0, 0x0, 0x4, 0xee, 0x6a,
    0xff, 0xd3, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x1a,
    0xe7, 0x0, 0x5, 0xff, 0xf3, 0x1, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf2,
    0x0, 0x7d, 0x71, 0x0, 0x0, 0x3, 0xef, 0x90,
    0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x24, 0xa5, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xf8, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x10, 0xf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xe1, 0x0, 0x0, 0x0, 0xa, 0xb2, 0x0,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x92, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0x20, 0x0, 0x0, 0x0, 0x4f, 0x73,
    0x33, 0x33, 0x33, 0x33, 0x4f, 0xf6, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x36, 0xfe, 0x30, 0x0, 0x0,
    0x4, 0xff, 0xcc, 0xcc, 0xcc, 0xcc, 0xcf, 0xfd,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcf, 0xfa,
    0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfd, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0,
    0x0, 0x0, 0x1, 0xed, 0x10, 0x0, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0, 0x0,
    0x2f, 0xe0, 0x0, 0x0, 0x0, 0xdd, 0x10, 0x0,
    0x6, 0xe8, 0x10, 0x0, 0x0, 0x3, 0xfd, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x1, 0xcd,
    0x10, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0, 0x0,
    0x3f, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xe0, 0x0,
    0x16, 0xec, 0x10, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xd1, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x9, 0xff, 0xcc, 0xcc, 0xcd, 0xdd,
    0xde, 0xde, 0xff, 0xa0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0xe0, 0x0, 0x1f, 0xff, 0xeb,
    0x97, 0x64, 0x32, 0x0, 0xa, 0xfc, 0x0, 0x3,
    0xfd, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x89, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x80, 0x0, 0x3f, 0xd0, 0x0, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x41, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x22, 0x10, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xef, 0xfb, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x17, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x3, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0x10, 0x0, 0x0, 0x0,

    /* U+7B80 "简" */
    0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x90, 0x0, 0x0, 0x5, 0x10, 0x0,
    0x8f, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x60, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x6f,
    0xd1, 0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0xa,
    0xfa, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0xef, 0x30,
    0x9, 0xb2, 0x0, 0x0, 0x0, 0x5f, 0xd1, 0x0,
    0x7a, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf3, 0x0, 0x0, 0xef, 0x40, 0x0, 0x1, 0xec,
    0x10, 0x0, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x40, 0x0, 0x0, 0x8f, 0xd0, 0x0,
    0xb, 0xc1, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xe4, 0x0, 0x12, 0x0, 0x3f,
    0xc0, 0x0, 0x9a, 0x0, 0x0, 0x0, 0x0, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0x40, 0x0, 0x2e,
    0x80, 0x8, 0x20, 0x0, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x41, 0x0, 0x2, 0x0, 0x0, 0x52, 0x0,
    0x0, 0x4, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0xa4, 0x0, 0x1f, 0xf9,
    0x0, 0x14, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x40, 0x0, 0x0, 0x0, 0xff, 0xd1,
    0xb, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x70, 0x5, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0xb5, 0x0,
    0x0, 0x0, 0x7, 0xd2, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0,
    0xcf, 0xed, 0xdd, 0xdd, 0xdf, 0xff, 0x30, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xef, 0x20,
    0x0, 0x0, 0xbf, 0x61, 0x11, 0x11, 0x1f, 0xfa,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x20, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0xbf, 0x50,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xef, 0x20,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x20, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0xbf, 0x50,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff, 0x20,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0xf, 0xf1,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0xcf, 0x50,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff, 0x20,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0xf, 0xe2,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0xdd, 0x30, 0x0, 0x0,
    0x4, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x30, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbb, 0x98,
    0x8c, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x9f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xc4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0,
    0x0, 0x1, 0xd4, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x90, 0x0, 0x0, 0x1d, 0xff, 0x20, 0x0, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xee,
    0xee, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xc1,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0xa2, 0x4,
    0x10, 0x0, 0x0, 0xdc, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xec, 0x0, 0x0, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x5, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xe2, 0x0,
    0x0, 0xd, 0xff, 0xa0, 0x0, 0x0, 0xd, 0xfc,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x40, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0, 0x0,
    0x8f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1a, 0xf9, 0x23, 0x45, 0x67, 0xff, 0x70, 0x0,
    0x0, 0x3, 0xf9, 0x0, 0x0, 0x47, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xda, 0x7e, 0xf8,
    0x0, 0x0, 0x0, 0x1d, 0xa0, 0x0, 0x0, 0xa,
    0xe7, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x61, 0x0,
    0x8f, 0xa0, 0x0, 0x0, 0x0, 0xbb, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xd4, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0xa, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xd1, 0x0, 0x0, 0x1,
    0xbe, 0x10, 0x0, 0x0, 0x12, 0x34, 0x46, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x1, 0xee, 0x20, 0x0,
    0x0, 0x1f, 0xfe, 0xdd, 0xef, 0xff, 0xff, 0xff,
    0xa8, 0x7e, 0xff, 0x0, 0x0, 0x0, 0x1d, 0xf3,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0x83,
    0x10, 0xff, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0,
    0xcf, 0x40, 0x0, 0x0, 0x0, 0x3, 0xe8, 0x22,
    0xff, 0x20, 0x0, 0xff, 0x0, 0x0, 0xb7, 0x0,
    0x0, 0x2d, 0xf4, 0x0, 0x0, 0x24, 0x68, 0x40,
    0x0, 0x2, 0xff, 0x20, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xb8, 0xac, 0xef, 0xeb,
    0x73, 0x0, 0x0, 0x2, 0xff, 0x10, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfc,
    0x73, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xe8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfa, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x8b, 0xa0, 0x0, 0xf,
    0xf4, 0x0, 0x0, 0xff, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x9d, 0xfc, 0x61, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x9, 0x30, 0x3, 0x58, 0xbf, 0xff, 0xd7, 0x20,
    0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xa, 0x40, 0x1f, 0xff, 0xff, 0x93,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0,
    0x0, 0xff, 0x0, 0x0, 0xb, 0x50, 0x9, 0xff,
    0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0xc, 0x70,
    0x1, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0x80, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0xe, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xa4, 0x33, 0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1a, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xe9, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xaa, 0xaa, 0xa9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x85, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+7F6E "置" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfe, 0x22, 0x22, 0x22,
    0xff, 0x52, 0x22, 0x22, 0xef, 0x32, 0x22, 0x22,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x30,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x6f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0xff, 0x30, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x55, 0x55, 0x55, 0xff,
    0x75, 0x55, 0x55, 0xff, 0x65, 0x55, 0x55, 0x9f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xdf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xa, 0xd8,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6a, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x8, 0x10, 0x0, 0x0, 0x0, 0x1, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x2c, 0xf9, 0x22, 0x22,
    0x22, 0x22, 0x23, 0xdf, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x15, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0,
    0x0, 0x0, 0x6, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0x51, 0x11, 0x11,
    0x5f, 0x81, 0x11, 0x11, 0x11, 0x3f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x2f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xef, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf2, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x2f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x0,
    0x0, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf2, 0x0, 0x2d, 0xfd, 0x20, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x3, 0x85, 0x32, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x9, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xf, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x30, 0x0, 0x0, 0x0, 0xf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf2, 0x0, 0x0, 0x0, 0x5e, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x7, 0xff,
    0xf4, 0x0, 0x1, 0xce, 0xee, 0xee, 0xee, 0xee,
    0xff, 0xee, 0xee, 0xee, 0xee, 0xef, 0xfe, 0xee,
    0xee, 0xef, 0xff, 0xff, 0x40, 0x0, 0x36, 0x32,
    0x11, 0x11, 0x12, 0xfe, 0x11, 0x11, 0x11, 0x11,
    0x1e, 0xf3, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0x0, 0x10, 0xd, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x50, 0x0, 0x0, 0x0,
    0x5e, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa3,
    0x0, 0x0, 0x0, 0x8f, 0xfb, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x81,
    0x0, 0x0, 0xef, 0xb2, 0x0, 0x0, 0xdf, 0xc2,
    0x0, 0x0, 0x0, 0x7d, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfe, 0x70, 0x0, 0xdf, 0xb0, 0x0,
    0x1, 0xff, 0x40, 0x0, 0x0, 0xa, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0,
    0xdf, 0x60, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0xdf, 0x60, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x2f, 0xd0, 0x24, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x9f, 0x40, 0xa, 0xe9,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0xcf, 0x60, 0x0, 0xfa,
    0x0, 0x0, 0x5f, 0xfc, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0xdf,
    0x60, 0x7, 0xd0, 0x0, 0x0, 0x3, 0xef, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd,
    0x0, 0x0, 0xdf, 0x60, 0x2e, 0x20, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfe, 0x0, 0x0, 0xef, 0x70, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xd4, 0x0, 0x0,
    0xef, 0x50, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0x92, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfc, 0x0, 0x0, 0xcf, 0x50, 0x0,
    0x4f, 0xe0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0xcf, 0x50, 0x0, 0x4f, 0xe0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfc, 0x0, 0x0, 0xcf, 0x50, 0x0, 0x4f, 0xe0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0xcf, 0x50,
    0x0, 0x4f, 0xe0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0x0, 0xcf, 0x50, 0x0, 0x4f, 0xe0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfc, 0x0, 0x0, 0xcf, 0x50, 0x0, 0x4f,
    0xe0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfc, 0x0, 0x0, 0xcf,
    0x50, 0x0, 0x4f, 0xe0, 0x0, 0x4, 0xfe, 0x0,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x4f, 0xe0, 0x0,
    0x4, 0xfe, 0x0, 0x9, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xfc, 0x0, 0x0, 0xcf, 0x50, 0x0,
    0x4f, 0xe0, 0x0, 0x4, 0xfe, 0x0, 0x8f, 0xfe,
    0x10, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x52, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbe, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x60, 0x4d,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xef, 0xff, 0xf6,
    0x5, 0x52, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4d, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xfb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0,

    /* U+8A9E "語" */
    0x0, 0x0, 0x5, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xec, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xe2, 0x0, 0x0, 0x0, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x3e, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0xcc, 0xef, 0xec, 0xcc, 0xcc,
    0xcc, 0xcc, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfc, 0x0, 0x4, 0x0, 0x2, 0x0, 0x0, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x8f, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x30, 0x0, 0x0, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x11, 0xff, 0x21, 0x11, 0x18, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xfe, 0xff, 0xee,
    0xee, 0xef, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x20, 0x0, 0x0, 0x11,
    0x4, 0xfd, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0xbb, 0xbb, 0xbb, 0xdf, 0xf4,
    0x0, 0x0, 0x0, 0x5, 0xfb, 0x0, 0x0, 0xb,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x6, 0x95, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x0, 0x7, 0xf9,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf7, 0x0, 0x0, 0xd, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x0, 0x0,
    0xe, 0xf5, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf4, 0x0, 0x0, 0xf, 0xf5, 0x1, 0xdc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x80, 0x5,
    0x66, 0x66, 0x6f, 0xf7, 0x66, 0x66, 0x6f, 0xf8,
    0x6d, 0xff, 0x90, 0x0, 0x2d, 0xee, 0xee, 0xee,
    0xff, 0xf7, 0x6, 0xfc, 0xa9, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x92, 0x0, 0x3,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x70, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x2, 0x50, 0x0, 0x0,
    0x6e, 0x60, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfa,
    0x0, 0x0, 0x0, 0x3e, 0x60, 0x0, 0x0, 0xb,
    0xf6, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x0,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0xb, 0xf5, 0x0,
    0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf7, 0x0, 0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0,
    0xb, 0xf4, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf7, 0x0, 0x0, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0xb, 0xf4,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf7, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0,
    0x0, 0xb, 0xf4, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0xb, 0xf5, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf7,
    0x0, 0x0, 0x0, 0x1f, 0xf4, 0x44, 0x44, 0x4c,
    0xf5, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x2f, 0xfa,
    0xaa, 0xaa, 0xae, 0xf5, 0x0, 0x0, 0x5f, 0xfd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xf8, 0x0, 0x0,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0xb, 0xf6, 0x0,
    0x0, 0x5f, 0xe1, 0x11, 0x11, 0x11, 0x11, 0x1c,
    0xf8, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0,
    0xb, 0xf6, 0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0, 0x3e,
    0x70, 0x0, 0x0, 0x5, 0x30, 0x0, 0x0, 0x6d,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x7, 0x62, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+8BA1 "计" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfc, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf1, 0x0, 0x0, 0x0, 0x7, 0xa0,
    0x1, 0x33, 0x33, 0x33, 0xdf, 0xd1, 0x0, 0x11,
    0x11, 0x11, 0x11, 0x15, 0xff, 0x21, 0x11, 0x11,
    0x17, 0xff, 0xa0, 0x1d, 0xfd, 0xbb, 0xbf, 0xff,
    0x40, 0x4f, 0xff, 0xee, 0xee, 0xee, 0xef, 0xfe,
    0xee, 0xee, 0xee, 0xee, 0xee, 0x70, 0x0, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x22, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf1, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x4f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x1c, 0x20, 0x0,
    0x0, 0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x2e,
    0x50, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x4f, 0x90, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x5f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x9f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x92, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+8BBE "设" */
    0x0, 0x0, 0x57, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xec, 0x10, 0x0,
    0x0, 0x0, 0x3, 0xe7, 0x11, 0x11, 0x11, 0x1c,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0xdd,
    0xdd, 0xdd, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfe, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x30, 0x0, 0x0, 0xe, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf4, 0x0,
    0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0xef,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x20, 0x0, 0x0, 0x2, 0xff, 0x20, 0x0,
    0x0, 0xe, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x80, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0xef, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0xef, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf6,
    0x0, 0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x10, 0x0, 0x0, 0x0, 0xef, 0xa0,
    0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x62,
    0x0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xdb, 0xcf, 0xff, 0x50, 0x12, 0x22,
    0x22, 0x4f, 0xf6, 0x0, 0x0, 0x4f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xfb,
    0x2, 0xef, 0xec, 0xcd, 0xff, 0xe2, 0x0, 0x2e,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22,
    0x22, 0x21, 0x0, 0x1, 0x10, 0x0, 0x4f, 0xf1,
    0x0, 0x2e, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x3e, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x5c, 0x30, 0x6f,
    0xff, 0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x2,
    0x0, 0x0, 0x10, 0x57, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x1, 0xd0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x30, 0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x99, 0x0, 0x0, 0x0, 0x3f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf1, 0x0,
    0x0, 0xa, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x80, 0x0, 0x2, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x1b, 0x10, 0x0, 0x7f, 0x10, 0x0, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x2d, 0x50, 0x0, 0x0, 0xfa, 0x0,
    0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x3e, 0x70, 0x0, 0x0,
    0x8, 0xf5, 0xe, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x4f, 0x90,
    0x0, 0x0, 0x0, 0xe, 0xfb, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0x9f, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xfd, 0x20, 0x5f,
    0xff, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xce, 0x10, 0x0, 0x0, 0x1, 0x9f, 0xf7,
    0x0, 0x0, 0x3d, 0xff, 0xfd, 0x84, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x7,
    0xef, 0xa2, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xeb, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x8e, 0xf9, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xaf, 0xff, 0xf7, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5b, 0xfb, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x29, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xa8, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+8BED "语" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xad, 0x30, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x23, 0xdf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x1d,
    0xfe, 0xdc, 0xcc, 0xff, 0xdc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcb, 0x10, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x30, 0x0, 0x1, 0x10, 0x0, 0x2, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7e, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x0, 0x24, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x11,
    0x11, 0x1a, 0xf9, 0x11, 0x11, 0x11, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfe, 0xef, 0xfe, 0xee, 0xee,
    0xee, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0xf,
    0xf3, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x0, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbe,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0xff, 0x30, 0x0, 0x0, 0x0, 0x3c,
    0xdd, 0xdd, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xb0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x5, 0x74, 0x22, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x80, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x20, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x50,
    0x0, 0x0, 0x0, 0xff, 0x30, 0xb, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x20, 0xb, 0xdd, 0xdd,
    0xdd, 0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xff, 0xdd,
    0xef, 0xff, 0x80, 0x0, 0x0, 0x0, 0xef, 0x20,
    0x1, 0x74, 0x21, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x20, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x23, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x6, 0xd4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x20, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x2, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x20,
    0x2, 0xc4, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x20, 0x5f, 0x50, 0x4, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x29, 0xf7, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xef, 0x80, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfa, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xd0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x20, 0x0, 0x0, 0x4, 0xff, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x82, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+8DDD "距" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x0, 0x0, 0x0, 0x4, 0xd5, 0x22, 0x22,
    0x22, 0x29, 0xf8, 0x0, 0x3d, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x4,
    0xff, 0xcc, 0xcc, 0xcc, 0xcf, 0xfd, 0x10, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x0, 0xc,
    0xf1, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0,
    0x0, 0x0, 0xc, 0xf1, 0x0, 0x2f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x0, 0x0,
    0xc, 0xf1, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfc,
    0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0, 0x0,
    0x0, 0xc, 0xf2, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xc1, 0x0, 0x0, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x1f,
    0xfb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xfc, 0x10,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x1f, 0xf3, 0x33, 0x33, 0x33, 0x33,
    0x3f, 0xf9, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0,
    0x1f, 0xc0, 0xc, 0x80, 0x0, 0x1f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0x4, 0xb4, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x1f, 0xc0, 0x0, 0x20,
    0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0xe, 0xfd, 0x10, 0x1f,
    0xc0, 0xc, 0xe2, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0xd,
    0xf3, 0x0, 0x1f, 0xff, 0xff, 0xfe, 0x20, 0x1f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x0, 0xd, 0xf1, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xc1, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x0, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0x1f, 0xc0, 0x0, 0x0,
    0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x1f,
    0xc0, 0x0, 0x0, 0x41, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf1, 0x0, 0x1f, 0xc0, 0x4, 0x9d, 0x70, 0x1f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf1, 0x0, 0x1f, 0xda, 0xec,
    0x50, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x37,
    0xcf, 0xf9, 0x30, 0x0, 0x0, 0x1f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xfe, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xa0, 0x0, 0x4c, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xfc, 0x0, 0x1e, 0xff, 0xa2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x5, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+901F "速" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x25, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x3, 0xef,
    0x50, 0x0, 0x0, 0x0, 0xa, 0xff, 0x80, 0x1c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xb0, 0x1, 0x63, 0x10, 0x0, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x57, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x75, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xba, 0x33, 0x33, 0x33, 0xef, 0x43, 0x33, 0x34,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xdc, 0xcc, 0xcc, 0xff,
    0xcc, 0xcc, 0xcc, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xb2, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x2d, 0xee, 0xee,
    0xef, 0xfe, 0x10, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x4, 0x73, 0x21, 0x1f, 0xf3, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0xef, 0x10, 0x0, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0xcf, 0x40, 0x0, 0x3f, 0xff, 0x10, 0x0, 0x1,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0xba, 0x10, 0x0, 0xdf, 0xff,
    0x10, 0x0, 0x0, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfd, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xe1, 0xef, 0xcc, 0x72,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x20,
    0xff, 0x14, 0xdf, 0xc6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0x2e, 0xe3, 0x0, 0xff, 0x10, 0x8, 0xff, 0xe7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x1, 0xde, 0x20, 0x0, 0xff, 0x10,
    0x0, 0x3d, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x2e, 0xc1, 0x0,
    0x0, 0xff, 0x20, 0x0, 0x1, 0xcf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x3,
    0xe9, 0x0, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0,
    0x1d, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf0, 0x0, 0x6d, 0x40, 0x0, 0x0, 0x0, 0xff,
    0x20, 0x0, 0x0, 0x1, 0xde, 0x0, 0x0, 0x0,
    0x0, 0x19, 0xfd, 0xec, 0x18, 0x80, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x13,
    0x0, 0x0, 0x0, 0x6, 0xef, 0x70, 0xb, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xf7,
    0x0, 0x0, 0xaf, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0x80, 0x0, 0x0, 0x9, 0xfd, 0x61,
    0x0, 0x0, 0x2, 0x93, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x6, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x4d, 0xff, 0xda, 0x86, 0x65, 0x44, 0x45,
    0x56, 0x67, 0x89, 0xac, 0xdf, 0xd3, 0x0, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0x9b, 0xde, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x62, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x23, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x62, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x23, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x60,
    0x0, 0x0, 0x26, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0x50, 0x9,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcf,
    0xff, 0xf4, 0x1, 0x96, 0x43, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x10, 0x0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xaf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0xaf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0,
    0xaf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0xaf, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0xaf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xee, 0xee, 0xee, 0xee, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfd, 0x11, 0x11, 0x11, 0x11, 0xef,
    0x21, 0x11, 0x11, 0x11, 0xae, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xa8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0xef, 0x21, 0x11, 0x11, 0x11, 0x1b, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfe, 0xee,
    0xee, 0xee, 0xee, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfd, 0x10, 0x2, 0xcd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xff,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xef, 0xff,
    0xc0, 0x0, 0x47, 0x43, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x20,

    /* U+95F4 "间" */
    0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x0, 0x0, 0xdf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xed, 0x10, 0x0, 0x0, 0x5, 0xff, 0x80, 0x4,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x3, 0x70, 0x0, 0xe, 0xfc, 0x0,
    0x4, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x30, 0x4f, 0xe7, 0x0, 0x9f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x3, 0xff, 0xa1, 0x2, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x3, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x3f, 0xf0, 0x0,
    0x0, 0x5, 0xa1, 0x0, 0x0, 0x0, 0x0, 0xad,
    0x10, 0x0, 0x0, 0x4f, 0xe0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x5f, 0xfc, 0xcc, 0xcc, 0xcc, 0xdf,
    0xfd, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x2f, 0xf0,
    0x0, 0x0, 0x4, 0xfd, 0x22, 0x22, 0x22, 0x23,
    0xff, 0x40, 0x0, 0x0, 0x4f, 0xe0, 0x2, 0xff,
    0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x2f,
    0xf0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x2,
    0xff, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0,
    0x0, 0x1f, 0xe0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x2f, 0xf0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x2, 0xff, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x2f, 0xf0, 0x0, 0x0, 0x3, 0xff, 0xcc,
    0xcc, 0xcc, 0xcc, 0xfe, 0x0, 0x0, 0x0, 0x4f,
    0xe0, 0x2, 0xff, 0x0, 0x0, 0x0, 0x3f, 0xd3,
    0x33, 0x33, 0x33, 0x4f, 0xf0, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x3, 0xfd,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x2, 0xff, 0x0, 0x0, 0x0, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x3,
    0xfd, 0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x2, 0xff, 0x0, 0x0, 0x0,
    0x4f, 0xd0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x4, 0xfe, 0x0, 0x2f, 0xf0, 0x0, 0x0,
    0x4, 0xfd, 0x0, 0x0, 0x0, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x2, 0xff, 0x0, 0x0,
    0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x2f, 0xf0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x2, 0xff, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x2f, 0xf0,
    0x0, 0x0, 0x5, 0xfb, 0x0, 0x0, 0x0, 0x1,
    0xf8, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x2, 0xff,
    0x0, 0x0, 0x0, 0x35, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x3,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x59, 0x75, 0x32, 0xaf, 0xd0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xff, 0xfb,
    0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x40, 0x4, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd,
    0x40, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x0, 0x0, 0x0,

    /* U+9650 "限" */
    0x10, 0x0, 0x0, 0x0, 0x3, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x20, 0x0,
    0x0, 0xc9, 0x21, 0x11, 0x11, 0x8f, 0x60, 0x5,
    0xc2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xe3,
    0x0, 0x0, 0xcf, 0xee, 0xee, 0xee, 0xff, 0xf2,
    0x4, 0xff, 0xee, 0xee, 0xee, 0xee, 0xee, 0xff,
    0xfe, 0x10, 0x0, 0xcf, 0x50, 0x0, 0x2, 0xff,
    0x30, 0x4, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0xcf, 0x50, 0x0, 0x6,
    0xfb, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0xa, 0xf5, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0xbf, 0x50,
    0x0, 0xe, 0xe0, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0xbf,
    0x50, 0x0, 0x2f, 0x70, 0x0, 0x3, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0x6f, 0x10, 0x0, 0x3, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0xaa, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0xe3, 0x0, 0x0,
    0x3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf0, 0x0, 0x0, 0xbf, 0x50, 0x2, 0xd0, 0x0,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf0, 0x0, 0x0, 0xbf, 0x50, 0x6, 0x70,
    0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf0, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0xc1, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0, 0xbf, 0x50,
    0x0, 0x4c, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0, 0xbf,
    0x50, 0x0, 0xc, 0x90, 0x0, 0x3, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0x3, 0xf5, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0x0, 0xdd, 0x0, 0x3,
    0xfd, 0x0, 0xe, 0x0, 0x0, 0x0, 0x4f, 0x90,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x7f, 0x50,
    0x3, 0xfd, 0x0, 0xb, 0x40, 0x0, 0x0, 0x11,
    0x9, 0x10, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x3f,
    0xb0, 0x3, 0xfd, 0x0, 0x8, 0x90, 0x0, 0x0,
    0x0, 0xaf, 0xc0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0x1f, 0xe0, 0x3, 0xfd, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x9, 0xff, 0xfa, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0xf, 0xf1, 0x3, 0xfd, 0x0, 0x0, 0xf6,
    0x0, 0x0, 0x8f, 0xfa, 0x40, 0x0, 0xbf, 0x50,
    0x0, 0x0, 0x1f, 0xf1, 0x3, 0xfd, 0x0, 0x0,
    0xbd, 0x0, 0xa, 0xfb, 0x20, 0x0, 0x0, 0xbf,
    0x50, 0x0, 0x0, 0x7f, 0xf0, 0x3, 0xfd, 0x0,
    0x0, 0x5f, 0x51, 0xcd, 0x40, 0x0, 0x0, 0x0,
    0xbf, 0x59, 0xec, 0xbd, 0xff, 0xb0, 0x3, 0xfd,
    0x0, 0x0, 0xe, 0xde, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x50, 0x2d, 0xff, 0xff, 0x40, 0x3,
    0xfd, 0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0xef, 0xe5, 0x0,
    0x3, 0xfd, 0x0, 0x0, 0x0, 0xdf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x67, 0x10,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x3f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x6, 0x8,
    0xff, 0x70, 0x0, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x18, 0xd4,
    0x0, 0xaf, 0xfb, 0x20, 0x0, 0x0, 0xbf, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x19, 0xfa,
    0x0, 0x0, 0xb, 0xff, 0xfa, 0x40, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x94,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xa5, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xf4, 0x0, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x95, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+97F3 "音" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x6, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe1, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x20, 0x0, 0x42,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfe, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf6, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf0, 0x0, 0x0, 0x0, 0xdf,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x20, 0x0,
    0x0, 0x3f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0xa, 0xc0, 0x0, 0x0, 0x0,
    0x4, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb3, 0x0, 0x0, 0x1, 0xf2, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xe2, 0x0, 0x9b, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xde, 0xbb, 0xbb, 0xbb, 0xbb, 0xff, 0xff, 0xe2,
    0x1, 0x97, 0x43, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfe, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x71, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xeb, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xea, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+984C "題" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x70, 0x0, 0x0, 0x0, 0xae, 0x53, 0x33,
    0x33, 0x34, 0xef, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xf8, 0x0, 0x0, 0x0,
    0x9f, 0xdb, 0xbb, 0xbb, 0xbb, 0xff, 0x81, 0xce,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x9f, 0x60, 0x0, 0x0, 0x0,
    0xfe, 0x0, 0x27, 0x31, 0x0, 0xc, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x60,
    0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x60, 0x0, 0x0, 0x0, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x60, 0x0, 0x0,
    0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x10,
    0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xed, 0xdd, 0xdd, 0xdd, 0xfe, 0x0, 0x0, 0xb7,
    0x0, 0x4c, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x72, 0x22, 0x22, 0x22, 0xfe,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x8f, 0x60, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0xbf, 0x30, 0x0,
    0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x60, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0xbf, 0x30, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x83, 0x33, 0x33, 0x33,
    0xff, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xcb,
    0xbb, 0xbb, 0xbb, 0xff, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x40, 0x0, 0x0, 0x0, 0xe5, 0x0,
    0x0, 0xaf, 0x30, 0x0, 0x0, 0x0, 0xd, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0,
    0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x53, 0x0, 0xaf,
    0x30, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x20, 0xaf, 0x75, 0x55, 0x55, 0x55, 0x5d,
    0xf1, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0xaf, 0xba, 0xaa,
    0xaa, 0xaa, 0xae, 0xf1, 0x0, 0x0, 0x0, 0x14,
    0x10, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x30, 0x0, 0x0, 0x0, 0xd, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x30, 0x0, 0x0, 0x0,
    0xd, 0xf2, 0x0, 0x0, 0x0, 0x2, 0xe6, 0x0,
    0x2, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x30,
    0x0, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x90, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x2, 0xfb,
    0x0, 0x8, 0xe2, 0x0, 0xcc, 0x10, 0x0, 0x0,
    0x0, 0xa, 0x50, 0x0, 0x0, 0x0, 0x3, 0xfa,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x20,
    0xc, 0x40, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf7, 0x0, 0x2, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x2d, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xf9, 0x0, 0x2,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xd5,
    0x0, 0x3, 0xfb, 0x10, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0x60, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0x0, 0x6f, 0xe4, 0x0,
    0x0, 0x0, 0xd, 0xe1, 0xd6, 0x2, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x20, 0x0, 0x0, 0xf, 0x90, 0x2e,
    0x72, 0xfb, 0x0, 0x0, 0x0, 0x5, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x4f, 0x40, 0x3, 0xfc, 0xfb, 0x0, 0x0, 0x0,
    0x5e, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x20, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x3e, 0xfe,
    0x50, 0x0, 0x4, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x1, 0xbf, 0xff, 0xa6, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x24, 0x57, 0xad, 0xd1,
    0x5, 0xd0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff,
    0xff, 0xfe, 0xee, 0xee, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xc, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x8c, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x57, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x23,
    0x44, 0x44, 0x44, 0x33, 0x32, 0x22, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+9AD8 "高" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x50, 0x0, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x46, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x2a, 0xff, 0x40, 0x8,
    0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdc, 0x20, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfa, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x34, 0xef, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xd1, 0x0, 0x0,
    0x0, 0x4f, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xff,
    0xd1, 0x0, 0x0, 0x4, 0xfe, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x5f, 0xf6, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0,
    0x3, 0xfe, 0x0, 0x0, 0xc, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xb0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0,
    0x0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xe2, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x3f, 0xe0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x2f, 0xe0, 0x0,
    0x0, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x3, 0xfd,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xe0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x4,
    0xfe, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0,
    0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x0, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xe7, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x0, 0x0, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xe0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9d,
    0xba, 0x8c, 0xfe, 0x0, 0x0, 0x0, 0x4, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xdf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x4f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf5,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0x0, 0x0, 0x0,

    /* U+FE87 "ﺇ" */
    0x3, 0xff, 0xf5, 0x0, 0x3f, 0xff, 0x50, 0x3,
    0xff, 0xf5, 0x0, 0x3f, 0xff, 0x50, 0x3, 0xff,
    0xf5, 0x0, 0x3f, 0xff, 0x50, 0x3, 0xff, 0xf5,
    0x0, 0x3f, 0xff, 0x50, 0x3, 0xff, 0xf5, 0x0,
    0x3f, 0xff, 0x50, 0x3, 0xff, 0xf5, 0x0, 0x3f,
    0xff, 0x50, 0x3, 0xff, 0xf5, 0x0, 0x3f, 0xff,
    0x50, 0x3, 0xff, 0xf5, 0x0, 0x3f, 0xff, 0x50,
    0x3, 0xff, 0xf5, 0x0, 0x3f, 0xff, 0x50, 0x3,
    0xff, 0xf5, 0x0, 0x3f, 0xff, 0x50, 0x3, 0xff,
    0xf5, 0x0, 0x3f, 0xff, 0x50, 0x3, 0xff, 0xf5,
    0x0, 0x3f, 0xff, 0x50, 0x3, 0xff, 0xf5, 0x0,
    0x3f, 0xff, 0x50, 0x3, 0xff, 0xf5, 0x0, 0x3f,
    0xff, 0x50, 0x3, 0xff, 0xf5, 0x0, 0x3f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xde, 0xb2,
    0x4f, 0xff, 0xff, 0x6b, 0xf8, 0x0, 0x33, 0xdf,
    0x0, 0x0, 0xb, 0xf5, 0x0, 0x0, 0x5f, 0xf9,
    0x79, 0x95, 0xdf, 0xff, 0xfd, 0xef, 0xfe, 0xa6,
    0x37, 0x51, 0x0, 0x0,

    /* U+FE8B "ﺋ" */
    0x0, 0x0, 0x18, 0xde, 0xc4, 0x0, 0x0, 0x1e,
    0xff, 0xff, 0xa0, 0x0, 0x6, 0xfb, 0x10, 0x25,
    0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0x7,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfa, 0x67,
    0xa0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0x10, 0x0,
    0x9f, 0xff, 0xd9, 0x60, 0x0, 0x7, 0x84, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xde, 0xe9, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90,
    0x0, 0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0x30, 0x3, 0x9b, 0xff, 0xff, 0xd0,
    0x0, 0x6f, 0xff, 0xff, 0xf5, 0x0, 0x6, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x6f, 0xfe, 0xb5, 0x0,
    0x0, 0x0,

    /* U+FE8D "ﺍ" */
    0x3f, 0xff, 0x53, 0xff, 0xf5, 0x3f, 0xff, 0x53,
    0xff, 0xf5, 0x3f, 0xff, 0x53, 0xff, 0xf5, 0x3f,
    0xff, 0x53, 0xff, 0xf5, 0x3f, 0xff, 0x53, 0xff,
    0xf5, 0x3f, 0xff, 0x53, 0xff, 0xf5, 0x3f, 0xff,
    0x53, 0xff, 0xf5, 0x3f, 0xff, 0x53, 0xff, 0xf5,
    0x3f, 0xff, 0x53, 0xff, 0xf5, 0x3f, 0xff, 0x53,
    0xff, 0xf5, 0x3f, 0xff, 0x53, 0xff, 0xf5, 0x3f,
    0xff, 0x53, 0xff, 0xf5, 0x3f, 0xff, 0x53, 0xff,
    0xf5, 0x3f, 0xff, 0x53, 0xff, 0xf5, 0x3f, 0xff,
    0x53, 0xff, 0xf5,

    /* U+FE8E "ﺎ" */
    0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f,
    0xff, 0x50, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f, 0xff,
    0x50, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0,
    0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f,
    0xff, 0x50, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f, 0xff,
    0x50, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0,
    0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f,
    0xff, 0x50, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x3f, 0xff,
    0x50, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x1f, 0xff, 0xa0, 0x0, 0x0, 0xe, 0xff, 0xf5,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xda, 0x95, 0x1,
    0xef, 0xff, 0xff, 0xf9, 0x0, 0x4f, 0xff, 0xff,
    0xf9, 0x0, 0x1, 0x9d, 0xff, 0xf9,

    /* U+FE95 "ﺕ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf9,
    0x7, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xee,
    0x80, 0x6e, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x17,
    0x77, 0x20, 0x15, 0x55, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf6, 0xb, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x81, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf9, 0x4f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x86, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf5, 0x6f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x15, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xef, 0xff, 0x90, 0x1f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xff, 0xd0, 0x0, 0x9f, 0xff,
    0xfc, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,
    0x8c, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xfc, 0x98, 0x77, 0x89, 0xac, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd9, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x69, 0xce, 0xff, 0xff, 0xec,
    0xb8, 0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEA3 "ﺣ" */
    0x0, 0x0, 0xde, 0xff, 0xed, 0xc9, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc8, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x40, 0x0, 0x0, 0xa, 0xab, 0xbc, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x36, 0x9d, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xdf, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xff, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xc3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xaf, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x39, 0x9a, 0xac, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xfc, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfe, 0xc9,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEA9 "ﺩ" */
    0x0, 0x0, 0x58, 0x88, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf1, 0x20, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xb0, 0x9f, 0xca, 0x9b, 0xff, 0xff, 0xff,
    0x20, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xfc, 0x30, 0x0, 0x28,
    0xce, 0xfe, 0xc8, 0x40, 0x0, 0x0,

    /* U+FEAA "ﺪ" */
    0x0, 0x0, 0x58, 0x88, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x59, 0x88, 0x8b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc9, 0x93, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x6f, 0xff, 0xff, 0xf6,
    0x9f, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x8, 0xff,
    0xff, 0xf6, 0x9f, 0xff, 0xfe, 0xc9, 0x40, 0x0,
    0x0, 0x5c, 0xff, 0xf6,

    /* U+FEBC "ﺼ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x35, 0x54, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x18, 0xef, 0xff, 0xff,
    0xd8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22, 0x0,
    0x0, 0x3, 0xef, 0xff, 0xfc, 0x51, 0x1, 0x5d,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xf0, 0x0, 0x5, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x5, 0xff, 0xff,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf3, 0x3,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xa1, 0xef, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xef, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x15, 0xbf, 0xff, 0xff,
    0xfb, 0x20, 0x0, 0x3, 0x99, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x99, 0x99, 0xac, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc9, 0x90, 0x6f, 0xff,
    0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff,
    0x16, 0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x20, 0x9f,
    0xff, 0xff, 0xf1, 0x6f, 0xff, 0xd9, 0x20, 0x0,
    0x29, 0xdf, 0xff, 0xff, 0xff, 0xed, 0xb8, 0x40,
    0x0, 0x0, 0x3a, 0xef, 0xff, 0x10,

    /* U+FECB "ﻋ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xad, 0xef,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xfb, 0x86, 0x52,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x70, 0x0, 0x0, 0x9f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x3a, 0xfd, 0x0, 0x0,
    0x2, 0xff, 0xff, 0x50, 0x0, 0x5, 0xcf, 0xff,
    0xd0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xc9, 0xae,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x10, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xfe, 0x71, 0x0,
    0x0, 0x0, 0x1, 0x5b, 0xff, 0xff, 0xff, 0xe6,
    0x0, 0x0, 0x3, 0x99, 0xbd, 0xff, 0xff, 0xff,
    0xfd, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xe8, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfe, 0xb7, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEF3 "ﻳ" */
    0x0, 0x0, 0x7, 0x88, 0x50, 0x0, 0x0, 0x0,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xf, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x0,
    0x1d, 0xff, 0xf4, 0x0, 0x39, 0xbf, 0xff, 0xfe,
    0x0, 0x6, 0xff, 0xff, 0xff, 0x60, 0x0, 0x6f,
    0xff, 0xff, 0xa0, 0x0, 0x6, 0xff, 0xeb, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xd0, 0x3f, 0xfc,
    0x0, 0x1f, 0xfd, 0x3, 0xff, 0xc0, 0x0, 0xee,
    0xc0, 0x2e, 0xeb,

    /* U+FEF4 "ﻴ" */
    0x0, 0x0, 0x7, 0x88, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0, 0x0,
    0x39, 0xbf, 0xff, 0xff, 0xff, 0xea, 0x94, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x6f, 0xff,
    0xff, 0xad, 0xff, 0xff, 0xf7, 0x6f, 0xfe, 0xb4,
    0x0, 0x7c, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfd, 0x3, 0xff, 0xc0, 0x0,
    0x0, 0x1f, 0xfd, 0x3, 0xff, 0xc0, 0x0, 0x0,
    0xe, 0xec, 0x2, 0xee, 0xb0, 0x0,

    /* U+FEF9 "ﻹ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfb, 0x1, 0x33, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xb0, 0x1f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfb, 0x0, 0x8f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xb0,
    0x1, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfb, 0x0, 0xa, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xb0, 0x0, 0x2f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xdf, 0xfb, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xb0, 0x0, 0x4,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0xdf, 0xfb, 0x0,
    0x0, 0xc, 0xff, 0xa0, 0x0, 0x0, 0xd, 0xff,
    0xb0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0, 0x0,
    0xdf, 0xfb, 0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0,
    0x0, 0xd, 0xff, 0xb0, 0x0, 0x0, 0x6, 0xff,
    0xf1, 0x0, 0x0, 0xef, 0xfb, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x70, 0x0, 0xf, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf6, 0x0, 0x1f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x4, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x50, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfc, 0xc, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf8, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xdf, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x3f, 0xca, 0xad, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x9d, 0xef, 0xd9,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19,
    0xdf, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xfc, 0x10, 0x15, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfc,
    0x89, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0xb7, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 78, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 95, .box_w = 4, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 62, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 22},
    {.bitmap_index = 98, .adv_w = 296, .box_w = 18, .box_h = 27, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 341, .adv_w = 193, .box_w = 10, .box_h = 36, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 521, .adv_w = 379, .box_w = 22, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 862, .adv_w = 208, .box_w = 12, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1060, .adv_w = 69, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = 22},
    {.bitmap_index = 1078, .adv_w = 107, .box_w = 7, .box_h = 38, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 1211, .adv_w = 107, .box_w = 6, .box_h = 38, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 1325, .adv_w = 172, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 20},
    {.bitmap_index = 1386, .adv_w = 270, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 1491, .adv_w = 122, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 1523, .adv_w = 123, .box_w = 6, .box_h = 3, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 1532, .adv_w = 95, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1540, .adv_w = 180, .box_w = 11, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1711, .adv_w = 205, .box_w = 11, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1893, .adv_w = 138, .box_w = 7, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2002, .adv_w = 185, .box_w = 10, .box_h = 32, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2162, .adv_w = 197, .box_w = 10, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2327, .adv_w = 191, .box_w = 12, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2513, .adv_w = 195, .box_w = 10, .box_h = 32, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2673, .adv_w = 202, .box_w = 11, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2855, .adv_w = 185, .box_w = 12, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3041, .adv_w = 202, .box_w = 10, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3206, .adv_w = 202, .box_w = 10, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3371, .adv_w = 95, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3407, .adv_w = 110, .box_w = 7, .box_h = 23, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 3488, .adv_w = 270, .box_w = 14, .box_h = 27, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 3677, .adv_w = 270, .box_w = 15, .box_h = 9, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 3745, .adv_w = 270, .box_w = 13, .box_h = 27, .ofs_x = 2, .ofs_y = 1},
    {.bitmap_index = 3921, .adv_w = 176, .box_w = 11, .box_h = 32, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4097, .adv_w = 429, .box_w = 26, .box_h = 37, .ofs_x = 0, .ofs_y = -9},
    {.bitmap_index = 4578, .adv_w = 184, .box_w = 12, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4764, .adv_w = 196, .box_w = 11, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4935, .adv_w = 191, .box_w = 10, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 5100, .adv_w = 196, .box_w = 10, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5255, .adv_w = 171, .box_w = 9, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5395, .adv_w = 168, .box_w = 9, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5535, .adv_w = 201, .box_w = 11, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 5717, .adv_w = 201, .box_w = 10, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5872, .adv_w = 89, .box_w = 4, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5934, .adv_w = 89, .box_w = 6, .box_h = 39, .ofs_x = -1, .ofs_y = -8},
    {.bitmap_index = 6051, .adv_w = 190, .box_w = 12, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6237, .adv_w = 135, .box_w = 8, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6361, .adv_w = 280, .box_w = 15, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6594, .adv_w = 215, .box_w = 11, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6765, .adv_w = 197, .box_w = 10, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 6930, .adv_w = 187, .box_w = 10, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7085, .adv_w = 197, .box_w = 10, .box_h = 38, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 7275, .adv_w = 190, .box_w = 11, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7446, .adv_w = 189, .box_w = 10, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7611, .adv_w = 171, .box_w = 11, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7782, .adv_w = 199, .box_w = 10, .box_h = 32, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7942, .adv_w = 177, .box_w = 11, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8113, .adv_w = 292, .box_w = 18, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8392, .adv_w = 191, .box_w = 13, .box_h = 31, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8594, .adv_w = 189, .box_w = 12, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8780, .adv_w = 168, .box_w = 10, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8935, .adv_w = 124, .box_w = 7, .box_h = 38, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 9068, .adv_w = 180, .box_w = 11, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9239, .adv_w = 124, .box_w = 6, .box_h = 38, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 9353, .adv_w = 270, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 17},
    {.bitmap_index = 9444, .adv_w = 183, .box_w = 10, .box_h = 2, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9454, .adv_w = 63, .box_w = 6, .box_h = 5, .ofs_x = -1, .ofs_y = 26},
    {.bitmap_index = 9469, .adv_w = 161, .box_w = 8, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9561, .adv_w = 164, .box_w = 9, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9701, .adv_w = 158, .box_w = 8, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9793, .adv_w = 164, .box_w = 8, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9917, .adv_w = 160, .box_w = 8, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10009, .adv_w = 116, .box_w = 8, .box_h = 31, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10133, .adv_w = 161, .box_w = 10, .box_h = 31, .ofs_x = 0, .ofs_y = -8},
    {.bitmap_index = 10288, .adv_w = 164, .box_w = 8, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10412, .adv_w = 81, .box_w = 5, .box_h = 29, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10485, .adv_w = 81, .box_w = 7, .box_h = 38, .ofs_x = -2, .ofs_y = -8},
    {.bitmap_index = 10618, .adv_w = 166, .box_w = 10, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10773, .adv_w = 82, .box_w = 3, .box_h = 31, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10820, .adv_w = 249, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10981, .adv_w = 165, .box_w = 9, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11085, .adv_w = 163, .box_w = 8, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11177, .adv_w = 164, .box_w = 9, .box_h = 31, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 11317, .adv_w = 164, .box_w = 8, .box_h = 31, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 11441, .adv_w = 122, .box_w = 7, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11522, .adv_w = 157, .box_w = 9, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11626, .adv_w = 114, .box_w = 7, .box_h = 29, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11728, .adv_w = 165, .box_w = 8, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11820, .adv_w = 151, .box_w = 10, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11935, .adv_w = 251, .box_w = 16, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12119, .adv_w = 153, .box_w = 10, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12234, .adv_w = 157, .box_w = 10, .box_h = 31, .ofs_x = 0, .ofs_y = -8},
    {.bitmap_index = 12389, .adv_w = 148, .box_w = 9, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12493, .adv_w = 147, .box_w = 9, .box_h = 38, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12664, .adv_w = 96, .box_w = 4, .box_h = 40, .ofs_x = 1, .ofs_y = -9},
    {.bitmap_index = 12744, .adv_w = 147, .box_w = 10, .box_h = 38, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12934, .adv_w = 270, .box_w = 15, .box_h = 4, .ofs_x = 1, .ofs_y = 20},
    {.bitmap_index = 12964, .adv_w = 640, .box_w = 10, .box_h = 10, .ofs_x = 5, .ofs_y = -1},
    {.bitmap_index = 13014, .adv_w = 640, .box_w = 38, .box_h = 31, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 13603, .adv_w = 640, .box_w = 31, .box_h = 39, .ofs_x = 5, .ofs_y = -5},
    {.bitmap_index = 14208, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 14893, .adv_w = 640, .box_w = 38, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 15596, .adv_w = 640, .box_w = 38, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 16299, .adv_w = 640, .box_w = 38, .box_h = 38, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 17021, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 17706, .adv_w = 640, .box_w = 38, .box_h = 38, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 18428, .adv_w = 640, .box_w = 36, .box_h = 37, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 19094, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 19778, .adv_w = 640, .box_w = 37, .box_h = 38, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 20481, .adv_w = 640, .box_w = 37, .box_h = 36, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 21147, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 21832, .adv_w = 640, .box_w = 26, .box_h = 36, .ofs_x = 8, .ofs_y = -4},
    {.bitmap_index = 22300, .adv_w = 640, .box_w = 35, .box_h = 37, .ofs_x = 3, .ofs_y = -4},
    {.bitmap_index = 22948, .adv_w = 640, .box_w = 37, .box_h = 38, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 23651, .adv_w = 640, .box_w = 30, .box_h = 36, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 24191, .adv_w = 640, .box_w = 38, .box_h = 38, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 24913, .adv_w = 640, .box_w = 37, .box_h = 36, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 25579, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26264, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26949, .adv_w = 640, .box_w = 38, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 27652, .adv_w = 640, .box_w = 32, .box_h = 36, .ofs_x = 6, .ofs_y = -3},
    {.bitmap_index = 28228, .adv_w = 640, .box_w = 33, .box_h = 37, .ofs_x = 4, .ofs_y = -4},
    {.bitmap_index = 28839, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 29524, .adv_w = 640, .box_w = 36, .box_h = 38, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 30208, .adv_w = 640, .box_w = 36, .box_h = 37, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 30874, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 31558, .adv_w = 640, .box_w = 38, .box_h = 37, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 32261, .adv_w = 640, .box_w = 34, .box_h = 37, .ofs_x = 3, .ofs_y = -4},
    {.bitmap_index = 32890, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 33574, .adv_w = 640, .box_w = 37, .box_h = 36, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 34240, .adv_w = 640, .box_w = 37, .box_h = 36, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 34906, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 35590, .adv_w = 640, .box_w = 38, .box_h = 34, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 36236, .adv_w = 640, .box_w = 38, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 36939, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 37623, .adv_w = 640, .box_w = 33, .box_h = 37, .ofs_x = 4, .ofs_y = -4},
    {.bitmap_index = 38234, .adv_w = 640, .box_w = 34, .box_h = 36, .ofs_x = 4, .ofs_y = -4},
    {.bitmap_index = 38846, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 39531, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 40215, .adv_w = 640, .box_w = 37, .box_h = 37, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 40900, .adv_w = 178, .box_w = 7, .box_h = 40, .ofs_x = 2, .ofs_y = -10},
    {.bitmap_index = 41040, .adv_w = 178, .box_w = 11, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 41178, .adv_w = 178, .box_w = 5, .box_h = 30, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 41253, .adv_w = 195, .box_w = 10, .box_h = 30, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 41403, .adv_w = 603, .box_w = 33, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 41667, .adv_w = 396, .box_w = 23, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 41851, .adv_w = 285, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 41977, .adv_w = 336, .box_w = 20, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 42157, .adv_w = 555, .box_w = 37, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 42435, .adv_w = 382, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 42656, .adv_w = 178, .box_w = 11, .box_h = 18, .ofs_x = -1, .ofs_y = -6},
    {.bitmap_index = 42755, .adv_w = 193, .box_w = 14, .box_h = 18, .ofs_x = -1, .ofs_y = -6},
    {.bitmap_index = 42881, .adv_w = 365, .box_w = 19, .box_h = 40, .ofs_x = 0, .ofs_y = -10}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1e07, 0x1e2b, 0x1eac, 0x1ec8, 0x1f51, 0x2147, 0x2745,
    0x2925, 0x2927, 0x2e71, 0x2ea4, 0x2f38, 0x3585, 0x35e3, 0x35f4,
    0x36fe, 0x3706, 0x3a17, 0x3c5e, 0x406d, 0x4165, 0x4257, 0x4533,
    0x4682, 0x49b9, 0x4b7e, 0x4edd, 0x4f6c, 0x54db, 0x59fe, 0x5a9c,
    0x5b9f, 0x5bbc, 0x5beb, 0x5ddb, 0x601d, 0x61cd, 0x65f2, 0x664e,
    0x67f1, 0x684a, 0x6ad6, 0xce85, 0xce89, 0xce8b, 0xce8c, 0xce93,
    0xcea1, 0xcea7, 0xcea8, 0xceba, 0xcec9, 0xcef1, 0xcef2, 0xcef7
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 12290, .range_length = 52984, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 56, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 1, 0, 2, 0, 0, 0, 3,
    2, 4, 5, 6, 0, 7, 8, 7,
    9, 10, 11, 12, 13, 14, 15, 16,
    17, 16, 18, 19, 19, 0, 0, 0,
    0, 20, 21, 22, 23, 24, 25, 26,
    27, 0, 0, 0, 28, 29, 0, 0,
    30, 31, 30, 32, 33, 34, 35, 36,
    37, 38, 39, 40, 41, 42, 0, 0,
    0, 0, 43, 44, 45, 46, 47, 48,
    49, 50, 0, 0, 51, 0, 50, 50,
    52, 44, 53, 54, 55, 56, 57, 58,
    59, 60, 61, 62, 63, 0, 64, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 1, 0, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 8,
    10, 11, 12, 13, 14, 15, 16, 11,
    17, 18, 18, 19, 19, 0, 0, 0,
    20, 21, 22, 23, 24, 23, 23, 23,
    24, 23, 23, 25, 23, 23, 23, 23,
    24, 23, 24, 23, 26, 27, 28, 29,
    30, 31, 32, 33, 0, 34, 35, 0,
    0, 0, 36, 0, 37, 38, 37, 39,
    40, 0, 0, 41, 0, 0, 42, 42,
    37, 42, 38, 42, 43, 44, 45, 46,
    47, 48, 49, 50, 51, 0, 52, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, 0, 0,
    0, 0, -9, 0, -9, -9, 0, -12,
    0, 0, 0, 0, 0, 0, -7, 0,
    0, 0, 0, -6, 0, -8, -8, 0,
    -8, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, -35, -26, -24, 0, 0,
    0, 0, -12, 0, 0, 0, -9, 0,
    -25, -17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -5, -6, 0, -6, 0, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 20, 0, 0, 0,
    0, 0, 4, 0, 0, 0, 0, -10,
    -10, -10, 0, 0, 27, -8, -10, 0,
    -9, -8, -8, 0, 0, 0, -10, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -8, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    -9, -10, 0, -10, 0, -6, -9, 0,
    -5, 0, 0, 0, 0, -6, 0, 0,
    0, -35, 0, 0, 0, 0, 0, 0,
    0, 0, -7, -17, 0, -5, -10, -7,
    0, -6, 0, 0, 0, 0, 0, -8,
    0, -6, -28, -8, -27, -24, 0, -37,
    0, 0, 0, 0, 0, 0, -6, 0,
    0, 0, 0, -5, 0, -12, -12, 0,
    -10, 0, 0, 0, 0, -26, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13,
    -10, 0, 0, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -15, 0,
    -7, -6, -9, -21, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -92, 0, 0, 0, 0, -14, 0,
    0, 0, 0, 0, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -14, -13, -13, 0, -15,
    0, -11, -13, 0, -10, -6, -5, -6,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -12, 0, 0, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 0, 0,
    0, -6, 0, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -8, 0,
    -5, 0, 0, -12, 0, 0, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -18, 0, 0, 7, 0, -35,
    -21, -30, 0, 4, 0, 0, -15, 0,
    0, 0, 0, 0, 0, -15, 0, 0,
    0, 0, 6, 0, 6, 4, 10, 6,
    0, 4, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -14, 0, -6, -4, 0, -16,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -21, 0,
    -7, -6, -11, -21, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, -17, 0, 0, 0, 0, -14, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    3, 0, 0, -6, 0, 0, 0, -2,
    0, -2, -22, 0, -10, -9, 0, -20,
    0, -15, -12, -2, 0, 0, -3, 0,
    0, 0, 0, -4, 0, -6, -5, 0,
    -4, 0, 0, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, -5, 0,
    -4, -4, -4, -8, 0, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    -2, 0, -4, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, -47, 0, -19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, -12, 0, 0, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -6, -6, -2, -6, 0, -5, -5, 0,
    -4, -2, -2, -8, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, -4, -3, -2, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 8, 0, 0,
    0, 0, -8, 0, 12, 0, 0, 0,
    0, 0, 0, -3, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -5, -4, -4, 0, 0, 0, -2, -4,
    -2, -10, -10, 0, -9, 0, 0, 0,
    -8, -24, 0, 0, 0, 0, -22, 0,
    -16, 0, 0, -13, 0, 0, -17, 0,
    0, 0, 0, -11, 0, 0, 0, 0,
    0, 0, -26, -2, -20, -18, 0, -29,
    0, -19, -13, 0, 0, 0, -9, 0,
    0, 0, 0, -9, -2, -17, -18, 0,
    -16, 0, 0, -12, 0, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    -2, -2, -3, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, 0, 0, -47,
    -8, -23, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -12, -13, 0, 0,
    0, 0, 0, 0, 0, 0, -6, 0,
    -3, 0, 0, -2, -2, -2, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -3, -2, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, -4, -3, 0, -4,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, 0,
    0, 0, 0, -28, -15, -17, 0, 3,
    0, 0, -15, 0, 0, 0, -14, 0,
    -19, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -26,
    -22, -22, -16, -22, 0, -22, -26, -12,
    -22, -26, -26, -26, -24, -29, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, -9, 0, 0, 0,
    0, 0, 0, -27, -7, -14, 0, 0,
    0, 0, -5, 0, 0, 0, -6, 0,
    -12, -10, 0, -3, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    -10, -10, 0, -10, 0, -7, -8, 0,
    -6, 0, 0, -2, 0, -6, 0, 0,
    -9, 0, 0, 0, 0, 0, 0, -24,
    -6, -12, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -9, -9, 0, -3,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, -8, -8, 0, -9,
    0, -5, -6, 0, -4, 0, 0, -2,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 3, 0, 0, -9, 0, 0, 0,
    0, 0, -5, 0, 5, 0, 0, 0,
    0, 0, 0, -3, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    -6, -6, -4, 0, 0, -2, -4, -4,
    -4, -9, -9, 0, -7, 0, 0, 0,
    -12, 0, 0, -5, 0, 0, 0, -37,
    -21, -24, -6, 0, 0, 0, -18, 0,
    0, 0, -16, 0, -24, -20, 0, -6,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -26, -24, -23, -3, -24,
    0, -22, -24, -2, -20, -7, -7, -10,
    -6, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -2, -2, -3, 0, 0, -2, 0, -2,
    -2, -6, -6, 0, -4, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -13, 0,
    0, 0, 0, 0, 0, -12, 0, 0,
    15, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -13, -13, -13, 0, 0,
    23, -11, -12, 0, -11, -7, -7, -7,
    0, -10, -12, 0, 0, -24, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -17, 0,
    -14, -12, 5, -24, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, -7, 0, -6, 0, 0, 0,
    0, -5, 0, 0, 0, -9, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -22, 0, -11, -9, 0, -23,
    0, -13, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, 0, -6, 0, 0,
    0, -10, -10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -22, 0,
    -10, -8, -5, -23, -2, -13, -13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, -13,
    0, -5, 0, 0, 0, -10, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -28, 0, -8, -6, -4, -24,
    0, -13, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -2, -2,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, -10, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -25, 0, -9, -6, -4, -24,
    0, -13, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -2, -3,
    0, 0, 0, -12, -8, 0, 0, 0,
    0, 11, 0, -8, -4, -6, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -6, -5, 0, 0, 0, 0, 5, 0,
    4, 3, 8, 4, 0, 7, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -24, 0, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 0, 0,
    0, -9, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -22, 0,
    -11, -8, 0, -23, 0, -13, -13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -27, 0, -2, 0, 0, -10,
    0, -6, -6, -2, -3, -2, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, -5, 0, -5, 0, 0,
    0, -10, -10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, -22, 0,
    -10, -8, -6, -24, -2, -13, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, -2, -3, 0, 0, 0, -12,
    0, 0, 0, 0, 0, -8, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -22, 0, -6, -5, 0, -21,
    0, -10, -11, 0, 0, 0, 0, 0,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, -10, 0, 0, 0,
    0, -7, 0, -17, -13, -15, 0, 0,
    0, 0, 0, 0, 0, 0, -12, 0,
    -14, -13, -2, 0, -2, 0, -32, 0,
    0, 0, -10, -3, -6, 0, -5, -4,
    -4, -3, 0, -4, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, -5, 0, 0, 0, -9, -9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -26, 0, -8, -6, -4, -24,
    0, -12, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -2, -2,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -20, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -22, 0, -6, -5, 0, -21,
    0, -10, -11, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, -8, 0, 0, 0,
    0, -8, 0, -12, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, -26, 0,
    0, 0, -9, -7, -6, -6, -6, -4,
    -3, 0, 0, -4, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    -8, 0, 0, 0, 0, -8, 0, -12,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, 0, -26, 0, 0, 0, -8, -8,
    -5, 0, -7, -3, -2, 0, 0, -3,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -26, -2,
    -2, 0, 0, -9, 0, -6, -6, -2,
    -3, -2, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -9, 0, 0, 0, 0, -7, 0, -13,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, 0, -26, 0, 0, 0, -9, -6,
    -6, 0, -5, -4, -3, 0, 0, -4,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -29, 0,
    0, 0, 0, -8, 0, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -14, 0,
    0, 0, 0, 0, 0, -11, 0, 0,
    15, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, -12, -13, 0, 0,
    23, -11, -12, 0, -10, -7, -7, -6,
    0, -9, -13, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -11, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 64,
    .right_class_cnt     = 52,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
    /*Store all the custom data of the font*/
    static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_ebike_trump_40 = {
#else
lv_font_t font_ebike_trump_40 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 44,          /*The maximum line height required by the font*/
    .base_line = 10,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -5,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_EBIKE_TRUMP_40*/

