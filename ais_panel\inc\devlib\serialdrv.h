/**
 * @file    serialdrv.h
 * @brief   serialdrv implementation file
 * <AUTHOR>
 * @version 0.1
 * @date    2024-12-21
 * 
 * @copyright Copyright (c) 2024
 */

#ifndef  _SERIAL_CONTROL_H_
#define  _SERIAL_CONTROL_H_

#include <string>
#include <fcntl.h>
#include <errno.h>
#include <sys/stat.h>
#include "const.h"
#include "vk_log.h"

#ifdef _WIN32
#include <io.h>

class CSerialDrv
{
public:
  CSerialDrv() {}
  ~CSerialDrv() {}

  /*!
   * @brief Open serial port
   * @param dev device name
   * @param baud baud rate
   * @return file descriptor
   */
  int open(const std::wstring dev, int baud)
  {
    if (fd != INVALID_HANDLE_VALUE) {
      CloseHandle(fd);
    }

    fd = CreateFile(dev.c_str(), GENERIC_READ | GENERIC_WRITE, 0, 0, O<PERSON><PERSON>_EXISTING, 0, 0);

    if (fd == INVALID_HANDLE_VALUE)
        return -1;

    // Get current configuration of serial communication port.
    DCB dcb;
    if (GetCommState(fd, &dcb) == 0) {
        return -1;
    }

    dcb.BaudRate = baud;
    dcb.ByteSize = 8;
    dcb.Parity = 0;
    dcb.StopBits = ONESTOPBIT;
    dcb.fBinary = true;
    dcb.fDsrSensitivity = false;
    dcb.fParity = NOPARITY;
    dcb.fOutX = false;
    dcb.fInX = false;
    dcb.fNull = false;
    dcb.fAbortOnError = true;
    dcb.fOutxCtsFlow = false;
    dcb.fOutxDsrFlow = false;
    dcb.fDtrControl = DTR_CONTROL_DISABLE;
    dcb.fDsrSensitivity = false;
    dcb.fRtsControl = RTS_CONTROL_DISABLE;
    dcb.fOutxCtsFlow = false;
    dcb.fOutxCtsFlow = false;

    bPortReady = SetCommState(fd, &dcb);

    if (bPortReady == 0) {
        CloseHandle(fd);
        return -1;
    }

    COMMTIMEOUTS CommTimeouts;
    if ((bPortReady = GetCommTimeouts(fd, &CommTimeouts)) == 0)
        return -1;

    CommTimeouts.ReadIntervalTimeout = 0;
    CommTimeouts.ReadTotalTimeoutConstant = 0;
    CommTimeouts.ReadTotalTimeoutMultiplier = 0;
    CommTimeouts.WriteTotalTimeoutConstant = 0;
    CommTimeouts.WriteTotalTimeoutMultiplier = 0;

    bPortReady = SetCommTimeouts(fd, &CommTimeouts);

    if (bPortReady == 0)
    {
        CloseHandle(fd);
        return -1;
    }

    return 0;
  }

  void close()
  {
    if (fd != INVALID_HANDLE_VALUE) {
      CloseHandle(fd);
    }
  }

  HANDLE getFd() const { return fd; }
  auto isOpened() { return fd != INVALID_HANDLE_VALUE; }

  /*!
   * @brief Read line from serial port
   * @param buffer buffer to store the read data
   * @param n number of bytes to read
   * @return number of bytes read
   */
  size_t readLine(void *buffer, size_t n)
  {
    DWORD numRead = 0;
    size_t totRead = 0;
    char *buf;
    char ch;

    if (fd == INVALID_HANDLE_VALUE || n <= 0 || buffer == NULL) {
        errno = EINVAL;
        return -1;
    }

    buf = (char *)buffer;
    totRead = 0;
    int metCR = 0;
    while (true) {
      if (!ReadFile(fd, &ch, 1, &numRead, 0)) {
          return -1;
      } else if (numRead == 0) {  /* EOF */
        if (totRead == 0) {       /* No bytes read; return 0 */
          return 0;
        } else {                  /* Some bytes read; add '\0' */
          break;
        }
      } else {                    /* 'numRead' must be 1 if we get here */
        if (totRead < n - 1) {    /* Discard > (n - 1) bytes */
          totRead++;
          *buf++ = ch;
        }

        if (ch == '\n')
          break;
      }
    }

    *buf = '\0';
    return totRead;
  }

  /*!
   * @brief Write data to serial port
   * @param vptr pointer to the data to write
   * @param n number of bytes to write
   * @return number of bytes written
   */
  size_t write(const void *vptr, size_t n)
  {
    size_t nleft;
    DWORD  nwritten;
    const char *ptr;

    if (fd == INVALID_HANDLE_VALUE) {
        return -1;
    }

    ptr = (char *)vptr;
    nleft = n;
    while (nleft > 0) {
      if (!WriteFile(fd, ptr, 1, &nwritten, NULL)) {
          return -1;
      }
      nleft -= nwritten;
      ptr += nwritten;
    }
    return (n);
  }

private:
  HANDLE fd = INVALID_HANDLE_VALUE;
  bool bPortReady;
};

#else
#include <termios.h>

class CSerialDrv
{
public:
    CSerialDrv() {}
    ~CSerialDrv() {}

    /*!
     * @brief Open serial port
     * @param dev device name
     * @param baud baud rate
     * @return file descriptor
     */
  int open(const std::string &dev, int baud)
  {
    if (fd > 0) {
      ::close(fd);
    }

    fd = ::open(dev.c_str(), O_RDWR | O_NOCTTY | O_SYNC);
    if (fd < 0) {
      ERROR_LOG("%s> uart dev open fail [%d]\r\n", __func__, fd);
      return -1;
    }

    struct termios tty = {};
    tty.c_iflag = IGNPAR; // non-parity
    tty.c_oflag = OPOST | ONLCR;
    tty.c_oflag &= ~ONLCR; // Prevent conversion of newline to carriage return/line feed
    tty.c_cflag = CS8 | CLOCAL | CREAD; // NO-rts/cts

    switch (baud) {
      case 4800:
        tty.c_cflag |= B4800;
        break;
      case 9600:
        tty.c_cflag |= B9600;
        break;
      case 19200:
        tty.c_cflag |= B19200;
        break;
      case 38400:
        tty.c_cflag |= B38400;
        break;
      case 115200:
        tty.c_cflag |= B115200;
        break;
      default:
        tty.c_cflag |= B38400;
        break;
    }
    tty.c_lflag = 0;
    tty.c_cc[VTIME] = 10;   // timeout n x 0.1 sec.
    tty.c_cc[VMIN] = 0;     // wait until received # of n characters.

    tcflush(fd, TCIFLUSH);

    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
      return -1;
    }
    return 0;
  }

  void close()
  {
    if (fd > 0) {
      ::close(fd);
    }
    fd = -1;
  }

  int getFd() const { return fd; }
  auto isOpened() { return fd > 0; }

  /*!
   * @brief Read line from serial port
   * @param buffer buffer to store the read data
   * @param n number of bytes to read
   * @return number of bytes read
   */
  size_t readLine(void *buffer, size_t n)
  {
    size_t numRead = 0;
    size_t totRead = 0;
    char *buf;
    char ch;

    if (fd < 0 || n <= 0 || buffer == NULL) {
      errno = EINVAL;
      return -1;
    }

    buf = (char *)buffer;
    totRead = 0;
    int metCR = 0;
    while (true) {
      numRead = ::read(fd, &ch, 1);

      if (numRead == -1) {
        if (errno == EINTR) {
          continue;
        } else { // other err
          return -1;
        }

      } else if (numRead == 0) {  /* EOF */
        if (totRead == 0) {       /* No bytes read; return 0 */
          return 0;
        } else {                  /* Some bytes read; add '\0' */
          break;
        }
      } else {                    /* 'numRead' must be 1 if we get here */
        if (totRead < n - 1) {    /* Discard > (n - 1) bytes */
          totRead++;
          *buf++ = ch;
        }

        if (ch == '\n')
          break;
      }
    }

    *buf = '\0';
    return totRead;
  }

  /*!
   * @brief Write data to serial port
   * @param vptr pointer to the data to write
   * @param n number of bytes to write
   * @return number of bytes written
   */
  size_t write(const void *vptr, size_t n)
  {
    size_t nleft;
    size_t nwritten;
    const char *ptr;

    if (fd < 0) {
      return -1;
    }

    ptr = (char *)vptr;
    nleft = n;
    while (nleft > 0) {
      if ((nwritten = ::write(fd, ptr, nleft)) <= 0) {
        if (errno == EINTR) {
          nwritten = 0;
        } else {
          return -1;
        }
      }
      nleft -= nwritten;
      ptr += nwritten;
    }
    return (n);
  }

private:
  int fd = -1;
};
#endif
#endif  /*_SERIAL_CONTROL_H_*/