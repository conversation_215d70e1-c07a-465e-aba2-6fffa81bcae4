#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: weibo.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_weibo_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x24,0xD9,0x45,0xE1,0x25,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xE9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x25,0xE1,0x45,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE3,0xE0,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x04,0xD9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xD9,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x04,0xD9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0xE3,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xD9,0x00,0x00,
0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x69,0xE2,0x75,0xF5,0x96,0xF5,0x10,0xEC,0xA6,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x00,0x00,0x24,0xD9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x28,0xE2,0xB2,0xF4,0x55,0xF5,0x3C,0xFF,0x3C,0xFF,0x49,0xE2,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xE9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x69,0xE2,0x34,0xF5,0xBB,0xFE,0x92,0xF4,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x2C,0xEB,0x6D,0xEB,0xC7,0xE1,0x18,0xF6,0xDB,0xFE,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x25,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0xB2,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBE,0xFF,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x30,0xEC,0x59,0xFE,0x38,0xF6,0xC7,0xE1,0xBE,0xFF,0xCB,0xEA,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x66,0xE1,0xF7,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9E,0xFF,
0xB3,0xF4,0x59,0xFE,0x3C,0xFF,0x38,0xF6,0x86,0xE1,0x9E,0xFF,0xA7,0xE1,0xBA,0xFE,0xCF,0xEB,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0xF7,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x28,0xE2,0x92,0xF4,0x45,0xE1,0x3C,0xFF,0x4D,0xEB,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x71,0xF4,0xFF,0xFF,0xFF,0xFF,0x7D,0xFF,0xD3,0xF4,0x6D,0xEB,0xAA,0xEA,0x0C,0xEB,0x10,0xEC,0x38,0xF6,0xFF,0xFF,0xFF,0xFF,0x8A,0xEA,0x45,0xE1,0x45,0xE1,0x0C,0xEB,0x65,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0xA6,0xE1,0xBE,0xFF,0xFF,0xFF,0x35,0xF5,0x66,0xE1,0xA6,0xE1,0xCF,0xEB,0x51,0xEC,0x8A,0xEA,
0x45,0xE1,0x45,0xE1,0x8E,0xEB,0xFF,0xFF,0xFF,0xFF,0x14,0xF5,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x4D,0xEB,0xFF,0xFF,0xD7,0xF5,0x45,0xE1,0x69,0xE2,0x3C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x4D,0xEB,0x45,0xE1,0x45,0xE1,0x18,0xF6,0xFF,0xFF,0xFF,0xFF,0xCB,0xEA,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x8E,0xEB,0xFF,0xFF,0xAE,0xEB,0x45,0xE1,0x96,0xF5,0xDB,0xFE,0x14,0xF5,0x38,0xF6,0xFF,0xFF,0x96,0xF5,0x45,0xE1,0x45,0xE1,0xB6,0xF5,0xFF,0xFF,0xFF,0xFF,0xCB,0xEA,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x28,0xE2,0xFF,0xFF,0x51,0xEC,0x45,0xE1,0x75,0xF5,0x10,0xEC,0x69,0xE2,0xFF,0xFF,0xFF,0xFF,
0x71,0xF4,0x45,0xE1,0xE7,0xE1,0x9E,0xFF,0xFF,0xFF,0xDB,0xFE,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x71,0xF4,0x9E,0xFF,0xCB,0xEA,0x08,0xE2,0xBA,0xFE,0xFF,0xFF,0x9E,0xFF,0xD3,0xF4,0x45,0xE1,0x69,0xE2,0xDB,0xFE,0xFF,0xFF,0xFB,0xFE,0x08,0xE2,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x25,0xE1,
0x45,0xE9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x4D,0xEB,0xBB,0xFE,0x18,0xF6,0x10,0xEC,0x4D,0xEB,0x6D,0xEB,0x51,0xEC,0x18,0xF6,0xFF,0xFF,0x7D,0xFF,0xB2,0xF4,0x86,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE9,0x00,0x00,0x25,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0xAA,0xEA,0x31,0xEC,0x55,0xF5,0x96,0xF5,0x34,0xF5,
0x71,0xF4,0xEB,0xEA,0x65,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xE1,0x00,0x00,
0x00,0x00,0x00,0x00,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xD9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xD9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xD9,0x25,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x24,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x25,0xE1,0x45,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x24,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xD9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x24,0xD9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xD9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xD9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x24,0xE1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x45,0xE9,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,
0x45,0xE1,0x45,0xE1,0x45,0xE1,0x45,0xE1,0x25,0xE1,0x45,0xE9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x1B,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_weibo_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_weibo_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_weibo_icon_data};

