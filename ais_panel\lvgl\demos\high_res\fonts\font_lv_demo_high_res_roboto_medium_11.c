/*******************************************************************************
 * Size: 11 px
 * Bpp: 8
 * Opts: --bpp 8 --size 11 --no-compress --font Roboto-Medium.ttf --range 32-127,176 --format lvgl -o font_lv_demo_high_res_roboto_medium_11.c
 ******************************************************************************/

#include "../../../lvgl.h"

#if LV_USE_DEMO_HIGH_RES

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x2e, 0xff, 0x2c, 0x28, 0xff, 0x26, 0x23, 0xff,
    0x1f, 0x1e, 0xff, 0x19, 0x19, 0xff, 0x12, 0xc,
    0x98, 0x8, 0x4, 0x53, 0x5, 0x1f, 0xe8, 0x21,

    /* U+0022 "\"" */
    0x74, 0x7b, 0xd8, 0x17, 0x74, 0x6a, 0xd7, 0x7,
    0x63, 0x49, 0xad, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0x4c, 0x98, 0x30, 0xb4, 0x0, 0x0,
    0x0, 0x81, 0x62, 0x65, 0x7e, 0x0, 0x11, 0xdc,
    0xf7, 0xe0, 0xf4, 0xe4, 0x71, 0x0, 0x0, 0xda,
    0x9, 0xc4, 0x24, 0x0, 0x0, 0x4, 0xdf, 0x0,
    0xe3, 0x4, 0x0, 0x68, 0xde, 0xf5, 0xda, 0xf9,
    0xd8, 0x17, 0x0, 0x5d, 0x8b, 0x41, 0xa5, 0x0,
    0x0, 0x0, 0x8f, 0x59, 0x73, 0x71, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x9, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xb9, 0x0, 0x0, 0x0, 0x6e, 0xf4, 0xff,
    0xc1, 0x11, 0x10, 0xfc, 0x5e, 0x17, 0xdf, 0x7f,
    0x17, 0xff, 0x48, 0x0, 0x4d, 0x4a, 0x0, 0x93,
    0xf7, 0x9b, 0x29, 0x0, 0x0, 0x0, 0x3f, 0xae,
    0xf8, 0x42, 0x3a, 0x77, 0x0, 0x0, 0xb1, 0xa5,
    0x3f, 0xfd, 0x52, 0x2e, 0xd7, 0x8c, 0x0, 0x75,
    0xe3, 0xf4, 0xa7, 0x11, 0x0, 0x0, 0x40, 0x98,
    0x0, 0x0,

    /* U+0025 "%" */
    0x17, 0xc6, 0xd3, 0x55, 0x0, 0x5, 0x0, 0x0,
    0x6d, 0x84, 0x24, 0xcc, 0x2, 0xc0, 0x16, 0x0,
    0x67, 0x8e, 0x2f, 0xc8, 0x6f, 0x80, 0x0, 0x0,
    0xe, 0xb2, 0xcd, 0x62, 0xca, 0x6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0x69, 0xc5, 0xbe, 0x1d,
    0x0, 0x0, 0x55, 0x97, 0xa4, 0x53, 0x6c, 0x8a,
    0x0, 0xb, 0xcc, 0xf, 0xa8, 0x47, 0x60, 0x90,
    0x0, 0x0, 0x15, 0x0, 0x3d, 0xd7, 0xd3, 0x2b,

    /* U+0026 "&" */
    0x0, 0x44, 0xdf, 0xf0, 0x77, 0x0, 0x0, 0x0,
    0xcd, 0x8d, 0x43, 0xfe, 0xc, 0x0, 0x0, 0xc6,
    0x86, 0x5f, 0xde, 0x0, 0x0, 0x0, 0x51, 0xfc,
    0xdd, 0x2a, 0x0, 0x0, 0x15, 0xd7, 0xcf, 0xeb,
    0x21, 0x88, 0x59, 0x78, 0xdd, 0x2, 0xa2, 0xd4,
    0xe6, 0x4c, 0x5f, 0xed, 0x1b, 0x16, 0xee, 0xf1,
    0x5, 0x3, 0x92, 0xec, 0xee, 0xb0, 0xe0, 0x8c,

    /* U+0027 "'" */
    0x90, 0x6f, 0x90, 0x61, 0x75, 0x43,

    /* U+0028 "(" */
    0x0, 0x0, 0x3c, 0x64, 0x0, 0x1d, 0xe0, 0x25,
    0x0, 0xa0, 0x7f, 0x0, 0x8, 0xf6, 0x28, 0x0,
    0x34, 0xf7, 0x0, 0x0, 0x4a, 0xe2, 0x0, 0x0,
    0x45, 0xeb, 0x0, 0x0, 0x24, 0xfc, 0x8, 0x0,
    0x0, 0xe1, 0x40, 0x0, 0x0, 0x75, 0xa8, 0x0,
    0x0, 0x6, 0xc5, 0x58, 0x0, 0x0, 0xc, 0x30,

    /* U+0029 ")" */
    0x7b, 0x23, 0x0, 0x0, 0x43, 0xd7, 0xa, 0x0,
    0x0, 0xae, 0x7a, 0x0, 0x0, 0x57, 0xda, 0x0,
    0x0, 0x25, 0xff, 0x13, 0x0, 0x11, 0xff, 0x2c,
    0x0, 0x18, 0xff, 0x24, 0x0, 0x31, 0xfa, 0x8,
    0x0, 0x6d, 0xbd, 0x0, 0x1, 0xd2, 0x4d, 0x0,
    0x7f, 0xa2, 0x0, 0x0, 0x39, 0x4, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xe8, 0x0, 0x0, 0x76, 0x58, 0xdb,
    0x58, 0x5d, 0x3c, 0xac, 0xff, 0xa1, 0x3c, 0xa,
    0xd1, 0x91, 0xad, 0x0, 0x12, 0x5f, 0x1, 0x76,
    0x6,

    /* U+002B "+" */
    0x0, 0x0, 0x71, 0x86, 0x0, 0x0, 0x0, 0x0,
    0x94, 0xb0, 0x0, 0x0, 0xa4, 0xff, 0xff, 0xff,
    0xff, 0xb8, 0x1e, 0x30, 0xa8, 0xbf, 0x30, 0x22,
    0x0, 0x0, 0x94, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x94, 0xb0, 0x0, 0x0,

    /* U+002C "," */
    0x4c, 0xdf, 0x62, 0xc5, 0xa1, 0x49, 0x1, 0x0,

    /* U+002D "-" */
    0x98, 0xf4, 0xf4, 0x31, 0x14, 0x20, 0x20, 0x6,

    /* U+002E "." */
    0x7, 0x5a, 0x5, 0x28, 0xe9, 0x21,

    /* U+002F "/" */
    0x0, 0x0, 0x19, 0xe6, 0x4, 0x0, 0x0, 0x73,
    0x90, 0x0, 0x0, 0x0, 0xd0, 0x33, 0x0, 0x0,
    0x2c, 0xd7, 0x0, 0x0, 0x0, 0x89, 0x7a, 0x0,
    0x0, 0x2, 0xe2, 0x1f, 0x0, 0x0, 0x43, 0xc1,
    0x0, 0x0, 0x0, 0x9f, 0x64, 0x0, 0x0, 0x0,
    0x72, 0xf, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x64, 0xe2, 0xed, 0x92, 0x3, 0x1e, 0xfc,
    0x56, 0x2f, 0xee, 0x58, 0x59, 0xea, 0x0, 0x0,
    0xaf, 0x97, 0x6e, 0xdc, 0x0, 0x0, 0xa0, 0xae,
    0x6e, 0xdc, 0x0, 0x0, 0xa0, 0xad, 0x58, 0xeb,
    0x0, 0x0, 0xaf, 0x97, 0x1d, 0xfa, 0x57, 0x2f,
    0xee, 0x57, 0x0, 0x61, 0xe2, 0xed, 0x92, 0x3,

    /* U+0031 "1" */
    0x0, 0x18, 0x74, 0xd4, 0x20, 0x15, 0xf6, 0xc9,
    0xff, 0x20, 0x6, 0x13, 0x30, 0xff, 0x20, 0x0,
    0x0, 0x30, 0xff, 0x20, 0x0, 0x0, 0x30, 0xff,
    0x20, 0x0, 0x0, 0x30, 0xff, 0x20, 0x0, 0x0,
    0x30, 0xff, 0x20, 0x0, 0x0, 0x30, 0xff, 0x20,

    /* U+0032 "2" */
    0x1, 0x84, 0xeb, 0xf0, 0x9f, 0x5, 0x58, 0xf4,
    0x3b, 0x31, 0xf2, 0x63, 0x48, 0x68, 0x0, 0x0,
    0xd7, 0x79, 0x0, 0x0, 0x0, 0x41, 0xfa, 0x27,
    0x0, 0x0, 0x26, 0xeb, 0x71, 0x0, 0x0, 0x1a,
    0xe0, 0x8e, 0x0, 0x0, 0x11, 0xd3, 0xac, 0x11,
    0x10, 0xd, 0x6b, 0xff, 0xff, 0xff, 0xff, 0xd8,

    /* U+0033 "3" */
    0x5, 0x8d, 0xf0, 0xee, 0x9c, 0x5, 0x63, 0xec,
    0x38, 0x2d, 0xf0, 0x62, 0x0, 0x0, 0x0, 0x1a,
    0xeb, 0x5c, 0x0, 0x0, 0xd6, 0xfe, 0xad, 0x0,
    0x0, 0x0, 0x19, 0x45, 0xf2, 0x53, 0x32, 0x46,
    0x0, 0x0, 0xba, 0x92, 0x60, 0xf0, 0x34, 0x30,
    0xec, 0x62, 0x2, 0x8f, 0xec, 0xec, 0x8e, 0x2,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0xaa, 0xff, 0x0, 0x0, 0x0,
    0x4c, 0xfc, 0xff, 0x0, 0x0, 0xc, 0xe1, 0x8f,
    0xff, 0x0, 0x0, 0x91, 0xa2, 0x4c, 0xff, 0x0,
    0x36, 0xea, 0x15, 0x4c, 0xff, 0x0, 0xae, 0xfa,
    0xf0, 0xf4, 0xff, 0xe8, 0x16, 0x20, 0x20, 0x62,
    0xff, 0x1f, 0x0, 0x0, 0x0, 0x4c, 0xff, 0x0,

    /* U+0035 "5" */
    0x0, 0xb1, 0xff, 0xff, 0xff, 0x90, 0x0, 0xca,
    0x7b, 0x24, 0x24, 0x14, 0x0, 0xe3, 0x4f, 0x0,
    0x0, 0x0, 0x1, 0xfa, 0xd5, 0xe6, 0xae, 0x15,
    0x3, 0x68, 0x45, 0x36, 0xda, 0x97, 0x8, 0x1b,
    0x0, 0x0, 0x8a, 0xc5, 0x25, 0xfe, 0x5b, 0x22,
    0xd1, 0x99, 0x0, 0x62, 0xe1, 0xf3, 0xb1, 0x14,

    /* U+0036 "6" */
    0x0, 0x3, 0x80, 0xdf, 0xa0, 0x0, 0x0, 0x89,
    0xe8, 0x58, 0x17, 0x0, 0x14, 0xf7, 0x50, 0x0,
    0x0, 0x0, 0x40, 0xfe, 0xa7, 0xe3, 0xb0, 0x13,
    0x5c, 0xfd, 0x6a, 0x34, 0xd9, 0x93, 0x49, 0xf7,
    0x1, 0x0, 0x8a, 0xc1, 0x10, 0xef, 0x6c, 0x23,
    0xd2, 0x91, 0x0, 0x43, 0xd7, 0xf2, 0xac, 0x10,

    /* U+0037 "7" */
    0xa0, 0xff, 0xff, 0xff, 0xff, 0xc8, 0xa, 0x10,
    0x10, 0x10, 0xcd, 0x77, 0x0, 0x0, 0x0, 0x3a,
    0xf6, 0x12, 0x0, 0x0, 0x0, 0xad, 0x9b, 0x0,
    0x0, 0x0, 0x23, 0xfc, 0x2d, 0x0, 0x0, 0x0,
    0x94, 0xbf, 0x0, 0x0, 0x0, 0x12, 0xf4, 0x51,
    0x0, 0x0, 0x0, 0x7a, 0xe0, 0x3, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x77, 0xe8, 0xf1, 0xa3, 0x7, 0x28, 0xff,
    0x54, 0x2a, 0xed, 0x68, 0x26, 0xfe, 0x36, 0xe,
    0xe5, 0x64, 0x0, 0x84, 0xfe, 0xfa, 0xbe, 0x1,
    0x26, 0xf6, 0x5c, 0x3d, 0xeb, 0x5a, 0x66, 0xe5,
    0x0, 0x0, 0xa9, 0xa5, 0x3d, 0xfd, 0x48, 0x28,
    0xe2, 0x7c, 0x0, 0x7a, 0xe7, 0xf0, 0xa3, 0xa,

    /* U+0039 "9" */
    0x0, 0x7d, 0xec, 0xe6, 0x6f, 0x0, 0x42, 0xfa,
    0x40, 0x40, 0xf9, 0x35, 0x77, 0xd0, 0x0, 0x0,
    0xbf, 0x7e, 0x55, 0xf4, 0x20, 0x16, 0xdd, 0x94,
    0x3, 0xb0, 0xfb, 0xe1, 0xe5, 0x7b, 0x0, 0x0,
    0xd, 0xb, 0xee, 0x4f, 0x0, 0x9, 0x33, 0xb0,
    0xd4, 0x2, 0x0, 0x5b, 0xef, 0xab, 0x1e, 0x0,

    /* U+003A ":" */
    0x2c, 0xe6, 0x1a, 0x9, 0x5c, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0x5a, 0x3, 0x2b,
    0xe8, 0x1b,

    /* U+003B ";" */
    0x45, 0xe2, 0x7, 0x13, 0x58, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xb, 0x0, 0x34,
    0xf7, 0x0, 0x4d, 0xda, 0x0, 0x86, 0x57, 0x0,
    0x0, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x8, 0x65, 0xac, 0x14, 0x7e, 0xe9,
    0xd8, 0x67, 0xa7, 0xf5, 0x5d, 0x0, 0x0, 0x26,
    0x9b, 0xf5, 0xba, 0x4f, 0x0, 0x0, 0x16, 0x82,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x3,

    /* U+003D "=" */
    0x35, 0xf4, 0xf4, 0xf4, 0xf4, 0x63, 0x7, 0x24,
    0x24, 0x24, 0x24, 0xe, 0x35, 0xf4, 0xf4, 0xf4,
    0xf4, 0x63, 0x7, 0x24, 0x24, 0x24, 0x24, 0xe,

    /* U+003E ">" */
    0x4a, 0xa4, 0x34, 0x0, 0x0, 0x0, 0x1c, 0x9a,
    0xf0, 0xc4, 0x54, 0x3, 0x0, 0x0, 0xb, 0x9f,
    0xff, 0x53, 0x14, 0x83, 0xe2, 0xde, 0x71, 0xc,
    0x4f, 0xc1, 0x51, 0x2, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x15, 0xb8, 0xf5, 0xd8, 0x40, 0x8d, 0xd3, 0x31,
    0x9d, 0xd3, 0xa, 0xa, 0x0, 0x7b, 0xd6, 0x0,
    0x0, 0x37, 0xf2, 0x56, 0x0, 0x3, 0xe5, 0x7d,
    0x0, 0x0, 0x10, 0xa0, 0x16, 0x0, 0x0, 0x3,
    0x53, 0x6, 0x0, 0x0, 0x1c, 0xe8, 0x24, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x23, 0xa4, 0xd0, 0xcc, 0xc4, 0x53,
    0x0, 0x0, 0x0, 0x2b, 0xd6, 0x44, 0x0, 0x0,
    0x12, 0xa9, 0x65, 0x0, 0x0, 0xc7, 0x3c, 0xd,
    0xac, 0xd9, 0x83, 0xb, 0xdc, 0x6, 0x2c, 0xc7,
    0x0, 0x94, 0x86, 0x22, 0xd3, 0x0, 0x9e, 0x3c,
    0x60, 0x8f, 0x0, 0xe6, 0x1a, 0x36, 0xbf, 0x0,
    0x87, 0x51, 0x6d, 0x80, 0x19, 0xf4, 0x0, 0x4b,
    0xaa, 0x0, 0x8b, 0x44, 0x58, 0x99, 0xf, 0xf6,
    0x9, 0x8a, 0xa6, 0x5, 0xca, 0x11, 0x16, 0xe1,
    0xa, 0x90, 0xcb, 0x77, 0xc9, 0xc6, 0x5a, 0x0,
    0x0, 0x85, 0xb1, 0x18, 0x0, 0x0, 0x9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x69, 0xcd, 0xd4, 0xcf,
    0x81, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x24, 0xff, 0x72, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x82, 0xf8, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0x79, 0xfc, 0x30, 0x0, 0x0,
    0x0, 0x3f, 0xfa, 0x12, 0xbb, 0x8f, 0x0, 0x0,
    0x0, 0x9e, 0xb7, 0x0, 0x64, 0xea, 0x4, 0x0,
    0x9, 0xf2, 0xfa, 0xf4, 0xf6, 0xff, 0x4d, 0x0,
    0x5b, 0xf7, 0x2b, 0x24, 0x24, 0xb9, 0xac, 0x0,
    0xb9, 0xaf, 0x0, 0x0, 0x0, 0x5c, 0xf9, 0x12,

    /* U+0042 "B" */
    0x34, 0xff, 0xff, 0xfb, 0xdd, 0x65, 0x0, 0x34,
    0xff, 0x3f, 0x1f, 0x7c, 0xfe, 0x16, 0x34, 0xff,
    0x28, 0x0, 0x5b, 0xfa, 0x15, 0x34, 0xff, 0xef,
    0xef, 0xff, 0x7b, 0x0, 0x34, 0xff, 0x3f, 0x1d,
    0x67, 0xf9, 0x24, 0x34, 0xff, 0x28, 0x0, 0x5,
    0xff, 0x56, 0x34, 0xff, 0x3c, 0x1a, 0x6e, 0xfe,
    0x2b, 0x34, 0xff, 0xff, 0xfd, 0xe0, 0x69, 0x0,

    /* U+0043 "C" */
    0x0, 0x24, 0xba, 0xf5, 0xe2, 0x7c, 0x0, 0x7,
    0xdd, 0xae, 0x2c, 0x4a, 0xf3, 0x63, 0x4a, 0xff,
    0x16, 0x0, 0x0, 0x74, 0x74, 0x6e, 0xed, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xed, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4d, 0xff, 0x14, 0x0, 0x0,
    0x72, 0x71, 0x8, 0xe0, 0xa9, 0x2a, 0x4a, 0xf3,
    0x62, 0x0, 0x28, 0xbf, 0xf6, 0xe1, 0x79, 0x0,

    /* U+0044 "D" */
    0x34, 0xff, 0xff, 0xf3, 0xb3, 0x22, 0x0, 0x34,
    0xff, 0x3f, 0x2f, 0xa8, 0xe4, 0x10, 0x34, 0xff,
    0x28, 0x0, 0xa, 0xee, 0x6a, 0x34, 0xff, 0x28,
    0x0, 0x0, 0xc5, 0x97, 0x34, 0xff, 0x28, 0x0,
    0x0, 0xc6, 0x97, 0x34, 0xff, 0x28, 0x0, 0xb,
    0xef, 0x6a, 0x34, 0xff, 0x3c, 0x2d, 0xab, 0xe5,
    0x10, 0x34, 0xff, 0xff, 0xf2, 0xb3, 0x23, 0x0,

    /* U+0045 "E" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x34, 0xff,
    0x3f, 0x1c, 0x1c, 0x18, 0x34, 0xff, 0x28, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xf5, 0xf4, 0xf4, 0x57,
    0x34, 0xff, 0x46, 0x24, 0x24, 0xc, 0x34, 0xff,
    0x28, 0x0, 0x0, 0x0, 0x34, 0xff, 0x3c, 0x18,
    0x18, 0x15, 0x34, 0xff, 0xff, 0xff, 0xff, 0xe8,

    /* U+0046 "F" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xc4, 0x34, 0xff,
    0x3f, 0x1c, 0x1c, 0x15, 0x34, 0xff, 0x28, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xf5, 0xf4, 0xf4, 0x48,
    0x34, 0xff, 0x46, 0x24, 0x24, 0xa, 0x34, 0xff,
    0x28, 0x0, 0x0, 0x0, 0x34, 0xff, 0x28, 0x0,
    0x0, 0x0, 0x34, 0xff, 0x28, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x20, 0xb6, 0xf0, 0xe5, 0x85, 0x1, 0x4,
    0xd8, 0xb6, 0x2e, 0x47, 0xef, 0x6b, 0x43, 0xff,
    0x1f, 0x0, 0x0, 0x5d, 0x67, 0x66, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6a, 0xf7, 0x0, 0x28,
    0xec, 0xec, 0xbc, 0x45, 0xff, 0x27, 0x4, 0x1c,
    0x9c, 0xcc, 0x5, 0xd6, 0xc1, 0x30, 0x29, 0xbc,
    0xc9, 0x0, 0x1d, 0xb1, 0xf3, 0xf3, 0xba, 0x31,

    /* U+0048 "H" */
    0x34, 0xff, 0x28, 0x0, 0x0, 0x58, 0xff, 0x34,
    0xff, 0x28, 0x0, 0x0, 0x58, 0xff, 0x34, 0xff,
    0x28, 0x0, 0x0, 0x58, 0xff, 0x34, 0xff, 0xf5,
    0xf4, 0xf4, 0xf8, 0xff, 0x34, 0xff, 0x46, 0x24,
    0x24, 0x6f, 0xff, 0x34, 0xff, 0x28, 0x0, 0x0,
    0x58, 0xff, 0x34, 0xff, 0x28, 0x0, 0x0, 0x58,
    0xff, 0x34, 0xff, 0x28, 0x0, 0x0, 0x58, 0xff,

    /* U+0049 "I" */
    0x20, 0xff, 0x3c, 0x20, 0xff, 0x3c, 0x20, 0xff,
    0x3c, 0x20, 0xff, 0x3c, 0x20, 0xff, 0x3c, 0x20,
    0xff, 0x3c, 0x20, 0xff, 0x3c, 0x20, 0xff, 0x3c,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0xff, 0x58, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x58, 0x0, 0x0, 0x0, 0x0, 0xff, 0x58,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x58, 0x46, 0x3b,
    0x0, 0x4, 0xff, 0x53, 0x95, 0xd8, 0x2a, 0x6a,
    0xfc, 0x23, 0x15, 0xb3, 0xf3, 0xe1, 0x5f, 0x0,

    /* U+004B "K" */
    0x34, 0xff, 0x28, 0x0, 0x3d, 0xfa, 0x6e, 0x34,
    0xff, 0x28, 0x1e, 0xea, 0x9d, 0x0, 0x34, 0xff,
    0x32, 0xcf, 0xc5, 0x6, 0x0, 0x34, 0xff, 0xc7,
    0xfe, 0x22, 0x0, 0x0, 0x34, 0xff, 0xec, 0xe9,
    0x97, 0x0, 0x0, 0x34, 0xff, 0x45, 0x4f, 0xff,
    0x48, 0x0, 0x34, 0xff, 0x28, 0x0, 0xa1, 0xe5,
    0x11, 0x34, 0xff, 0x28, 0x0, 0xf, 0xe3, 0xa7,

    /* U+004C "L" */
    0x34, 0xff, 0x28, 0x0, 0x0, 0x0, 0x34, 0xff,
    0x28, 0x0, 0x0, 0x0, 0x34, 0xff, 0x28, 0x0,
    0x0, 0x0, 0x34, 0xff, 0x28, 0x0, 0x0, 0x0,
    0x34, 0xff, 0x28, 0x0, 0x0, 0x0, 0x34, 0xff,
    0x28, 0x0, 0x0, 0x0, 0x34, 0xff, 0x3c, 0x18,
    0x18, 0x10, 0x34, 0xff, 0xff, 0xff, 0xff, 0xb4,

    /* U+004D "M" */
    0x34, 0xff, 0xbe, 0x0, 0x0, 0x0, 0x20, 0xfe,
    0xd0, 0x34, 0xff, 0xfd, 0x1e, 0x0, 0x0, 0x7e,
    0xfe, 0xd0, 0x34, 0xff, 0xc8, 0x7b, 0x0, 0x0,
    0xda, 0xc6, 0xd0, 0x34, 0xff, 0x73, 0xd8, 0x0,
    0x3a, 0xf0, 0x80, 0xd0, 0x34, 0xff, 0x2a, 0xf3,
    0x37, 0x98, 0x99, 0x80, 0xd0, 0x34, 0xff, 0x27,
    0x9f, 0x9c, 0xee, 0x39, 0x87, 0xd0, 0x34, 0xff,
    0x28, 0x40, 0xfe, 0xda, 0x0, 0x88, 0xd0, 0x34,
    0xff, 0x28, 0x1, 0xde, 0x7b, 0x0, 0x88, 0xd0,

    /* U+004E "N" */
    0x34, 0xff, 0x76, 0x0, 0x0, 0x58, 0xff, 0x34,
    0xff, 0xf4, 0x1e, 0x0, 0x58, 0xff, 0x34, 0xff,
    0xdc, 0xb0, 0x0, 0x58, 0xff, 0x34, 0xff, 0x4f,
    0xf9, 0x4d, 0x58, 0xff, 0x34, 0xff, 0x28, 0x83,
    0xe0, 0x62, 0xff, 0x34, 0xff, 0x28, 0x8, 0xdc,
    0xd8, 0xff, 0x34, 0xff, 0x28, 0x0, 0x48, 0xff,
    0xff, 0x34, 0xff, 0x28, 0x0, 0x0, 0xab, 0xff,

    /* U+004F "O" */
    0x0, 0x1e, 0xb4, 0xf4, 0xe5, 0x7b, 0x1, 0x0,
    0x4, 0xd5, 0xbd, 0x36, 0x5a, 0xf3, 0x71, 0x0,
    0x44, 0xff, 0x20, 0x0, 0x0, 0x8c, 0xda, 0x0,
    0x6c, 0xf0, 0x0, 0x0, 0x0, 0x5d, 0xfd, 0x3,
    0x6d, 0xf0, 0x0, 0x0, 0x0, 0x5c, 0xfd, 0x2,
    0x46, 0xff, 0x20, 0x0, 0x0, 0x89, 0xd9, 0x0,
    0x5, 0xd6, 0xbc, 0x33, 0x54, 0xf1, 0x71, 0x0,
    0x0, 0x1f, 0xb5, 0xf5, 0xe6, 0x7e, 0x1, 0x0,

    /* U+0050 "P" */
    0x34, 0xff, 0xff, 0xfe, 0xe3, 0x7c, 0x0, 0x34,
    0xff, 0x3f, 0x1c, 0x4f, 0xf8, 0x5c, 0x34, 0xff,
    0x28, 0x0, 0x0, 0xc2, 0x97, 0x34, 0xff, 0x28,
    0x0, 0x21, 0xed, 0x72, 0x34, 0xff, 0xf5, 0xf4,
    0xfe, 0xac, 0x7, 0x34, 0xff, 0x46, 0x23, 0xa,
    0x0, 0x0, 0x34, 0xff, 0x28, 0x0, 0x0, 0x0,
    0x0, 0x34, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x22, 0xb8, 0xf5, 0xe3, 0x76, 0x1, 0x0,
    0x7, 0xda, 0xb7, 0x35, 0x5e, 0xf6, 0x6a, 0x0,
    0x4c, 0xfe, 0x19, 0x0, 0x0, 0x93, 0xd3, 0x0,
    0x74, 0xe8, 0x0, 0x0, 0x0, 0x65, 0xfa, 0x1,
    0x75, 0xe8, 0x0, 0x0, 0x0, 0x64, 0xfb, 0x1,
    0x4e, 0xfe, 0x19, 0x0, 0x0, 0x91, 0xd6, 0x0,
    0x7, 0xdc, 0xb6, 0x31, 0x57, 0xf3, 0x6e, 0x0,
    0x0, 0x24, 0xba, 0xf6, 0xf8, 0xe7, 0x13, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0xbc, 0xb8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x11, 0x0,

    /* U+0052 "R" */
    0x34, 0xff, 0xff, 0xfc, 0xdb, 0x5f, 0x0, 0x34,
    0xff, 0x3f, 0x1f, 0x78, 0xfd, 0x27, 0x34, 0xff,
    0x28, 0x0, 0x6, 0xff, 0x56, 0x34, 0xff, 0x28,
    0x0, 0x58, 0xff, 0x2e, 0x34, 0xff, 0xf5, 0xf7,
    0xfe, 0x6f, 0x0, 0x34, 0xff, 0x46, 0x3d, 0xfb,
    0x4e, 0x0, 0x34, 0xff, 0x28, 0x0, 0x96, 0xd7,
    0x4, 0x34, 0xff, 0x28, 0x0, 0x16, 0xf3, 0x69,

    /* U+0053 "S" */
    0x0, 0x62, 0xdb, 0xf8, 0xd0, 0x41, 0x0, 0x31,
    0xff, 0x61, 0x21, 0x8e, 0xf2, 0xf, 0x4b, 0xff,
    0x29, 0x0, 0x10, 0x64, 0x14, 0x5, 0xb6, 0xf6,
    0xa2, 0x42, 0x0, 0x0, 0x0, 0x0, 0x43, 0xa2,
    0xf7, 0xa4, 0x0, 0x45, 0x5f, 0x0, 0x0, 0x42,
    0xff, 0x2b, 0x5a, 0xf8, 0x53, 0x1f, 0x7b, 0xfb,
    0x18, 0x0, 0x70, 0xdd, 0xf8, 0xd7, 0x56, 0x0,

    /* U+0054 "T" */
    0xc4, 0xff, 0xff, 0xff, 0xff, 0xff, 0x74, 0x15,
    0x1c, 0x6a, 0xff, 0x1c, 0x1c, 0xc, 0x0, 0x0,
    0x58, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x58, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x58, 0xff, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x54, 0xff, 0x8, 0x0, 0x0, 0xd8, 0x84, 0x54,
    0xff, 0x8, 0x0, 0x0, 0xd8, 0x84, 0x54, 0xff,
    0x8, 0x0, 0x0, 0xd8, 0x84, 0x54, 0xff, 0x8,
    0x0, 0x0, 0xd8, 0x84, 0x54, 0xff, 0x8, 0x0,
    0x0, 0xd8, 0x84, 0x48, 0xff, 0x11, 0x0, 0x0,
    0xe2, 0x78, 0x15, 0xf2, 0x8b, 0x22, 0x64, 0xfe,
    0x36, 0x0, 0x44, 0xce, 0xf6, 0xda, 0x5e, 0x0,

    /* U+0056 "V" */
    0xbc, 0xbc, 0x0, 0x0, 0x0, 0xa1, 0xda, 0x0,
    0x62, 0xfb, 0x12, 0x0, 0x4, 0xee, 0x80, 0x0,
    0xf, 0xf8, 0x5e, 0x0, 0x45, 0xff, 0x26, 0x0,
    0x0, 0xae, 0xaf, 0x0, 0x97, 0xcc, 0x0, 0x0,
    0x0, 0x54, 0xf6, 0xc, 0xe6, 0x72, 0x0, 0x0,
    0x0, 0x8, 0xf1, 0x8d, 0xfd, 0x1a, 0x0, 0x0,
    0x0, 0x0, 0xa0, 0xfc, 0xbe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x45, 0xff, 0x64, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0xa0, 0xb5, 0x0, 0x0, 0xd0, 0x92, 0x0, 0x3,
    0xf6, 0x5e, 0x66, 0xe8, 0x0, 0x11, 0xfd, 0xd1,
    0x0, 0x2e, 0xff, 0x24, 0x2d, 0xff, 0x1b, 0x50,
    0xd6, 0xf8, 0x12, 0x61, 0xea, 0x0, 0x2, 0xf1,
    0x4e, 0x90, 0x8a, 0xc5, 0x4f, 0x95, 0xb0, 0x0,
    0x0, 0xba, 0x81, 0xd0, 0x48, 0x84, 0x8e, 0xc9,
    0x76, 0x0, 0x0, 0x80, 0xc6, 0xf8, 0xb, 0x43,
    0xd2, 0xf7, 0x3c, 0x0, 0x0, 0x47, 0xff, 0xc3,
    0x0, 0x9, 0xf8, 0xf9, 0x8, 0x0, 0x0, 0xf,
    0xfd, 0x81, 0x0, 0x0, 0xc1, 0xc8, 0x0, 0x0,

    /* U+0058 "X" */
    0x6c, 0xfa, 0x26, 0x0, 0x2c, 0xfc, 0x66, 0x3,
    0xd0, 0xb3, 0x0, 0xbc, 0xcb, 0x2, 0x0, 0x3c,
    0xff, 0x93, 0xfe, 0x36, 0x0, 0x0, 0x0, 0xa3,
    0xff, 0x9b, 0x0, 0x0, 0x0, 0x0, 0xae, 0xff,
    0xa5, 0x0, 0x0, 0x0, 0x47, 0xff, 0x7f, 0xff,
    0x3f, 0x0, 0x6, 0xda, 0xa8, 0x0, 0xb1, 0xd4,
    0x4, 0x7b, 0xf6, 0x1e, 0x0, 0x24, 0xf9, 0x73,

    /* U+0059 "Y" */
    0xb6, 0xc8, 0x0, 0x0, 0x1e, 0xfa, 0x65, 0x31,
    0xfe, 0x43, 0x0, 0x94, 0xdc, 0x4, 0x0, 0xaa,
    0xbe, 0x17, 0xf7, 0x5b, 0x0, 0x0, 0x28, 0xfc,
    0xbb, 0xd4, 0x2, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x52, 0x0, 0x0, 0x0, 0x0, 0x54, 0xff, 0x8,
    0x0, 0x0, 0x0, 0x0, 0x54, 0xff, 0x8, 0x0,
    0x0, 0x0, 0x0, 0x54, 0xff, 0x8, 0x0, 0x0,

    /* U+005A "Z" */
    0x8c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1e, 0xf,
    0x1c, 0x1c, 0x1f, 0xd6, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x78, 0xee, 0x1a, 0x0, 0x0, 0x0, 0x2a,
    0xf8, 0x5c, 0x0, 0x0, 0x0, 0x4, 0xcb, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0xed, 0x18, 0x0,
    0x0, 0x0, 0x2d, 0xf9, 0x70, 0x18, 0x18, 0x18,
    0x6, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,

    /* U+005B "[" */
    0x46, 0xec, 0xd5, 0x4c, 0xff, 0x1c, 0x4c, 0xff,
    0x4, 0x4c, 0xff, 0x4, 0x4c, 0xff, 0x4, 0x4c,
    0xff, 0x4, 0x4c, 0xff, 0x4, 0x4c, 0xff, 0x4,
    0x4c, 0xff, 0x4, 0x4c, 0xff, 0x4, 0x4c, 0xff,
    0xd6, 0x8, 0x1c, 0x19,

    /* U+005C "\\" */
    0xb3, 0x94, 0x0, 0x0, 0x0, 0x51, 0xee, 0x8,
    0x0, 0x0, 0x5, 0xe9, 0x59, 0x0, 0x0, 0x0,
    0x8c, 0xbc, 0x0, 0x0, 0x0, 0x29, 0xfd, 0x1f,
    0x0, 0x0, 0x0, 0xc7, 0x80, 0x0, 0x0, 0x0,
    0x65, 0xe1, 0x2, 0x0, 0x0, 0xd, 0xf5, 0x45,
    0x0, 0x0, 0x0, 0x5c, 0x47,

    /* U+005D "]" */
    0xdd, 0xec, 0x3e, 0x21, 0xff, 0x44, 0x8, 0xff,
    0x44, 0x8, 0xff, 0x44, 0x8, 0xff, 0x44, 0x8,
    0xff, 0x44, 0x8, 0xff, 0x44, 0x8, 0xff, 0x44,
    0x8, 0xff, 0x44, 0x8, 0xff, 0x44, 0xdd, 0xff,
    0x44, 0x1a, 0x1c, 0x7,

    /* U+005E "^" */
    0x0, 0x50, 0xf4, 0xd, 0x0, 0x0, 0xb7, 0xe7,
    0x68, 0x0, 0x20, 0xec, 0x4d, 0xce, 0x0, 0x85,
    0x94, 0x1, 0xdf, 0x34,

    /* U+005F "_" */
    0xec, 0xf0, 0xf0, 0xf0, 0xe1, 0x1b, 0x1c, 0x1c,
    0x1c, 0x1a,

    /* U+0060 "`" */
    0x3f, 0x74, 0x1, 0x8, 0xbc, 0x72,

    /* U+0061 "a" */
    0x6, 0x91, 0xe8, 0xec, 0x7a, 0x0, 0x33, 0xa0,
    0x22, 0x39, 0xff, 0x24, 0x2, 0x85, 0xd0, 0xd2,
    0xff, 0x3f, 0x5e, 0xe9, 0x15, 0xc, 0xff, 0x40,
    0x70, 0xee, 0x2a, 0x6c, 0xff, 0x40, 0xf, 0xbc,
    0xf4, 0xab, 0xf5, 0x57,

    /* U+0062 "b" */
    0x54, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x54, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x54, 0xfa, 0xaf, 0xf2,
    0xb0, 0xd, 0x54, 0xfe, 0x59, 0x32, 0xe5, 0x7c,
    0x54, 0xf8, 0x0, 0x0, 0x95, 0xb4, 0x54, 0xf8,
    0x0, 0x0, 0x94, 0xb5, 0x54, 0xfe, 0x57, 0x2d,
    0xe3, 0x7c, 0x54, 0xe8, 0xaf, 0xf3, 0xb1, 0xd,

    /* U+0063 "c" */
    0x0, 0x75, 0xe5, 0xed, 0x85, 0x0, 0x42, 0xfa,
    0x41, 0x36, 0xf4, 0x43, 0x84, 0xc7, 0x0, 0x0,
    0x26, 0x13, 0x83, 0xc6, 0x0, 0x0, 0x6, 0x3,
    0x41, 0xf9, 0x3f, 0x31, 0xef, 0x4c, 0x0, 0x75,
    0xe6, 0xf0, 0x7d, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0xc8, 0x84, 0x0, 0x0,
    0x0, 0x0, 0xc8, 0x84, 0x0, 0x8c, 0xef, 0xca,
    0xdf, 0x84, 0x48, 0xfd, 0x4f, 0x3d, 0xed, 0x84,
    0x84, 0xcd, 0x0, 0x0, 0xc8, 0x84, 0x84, 0xc6,
    0x0, 0x0, 0xc8, 0x84, 0x47, 0xf8, 0x2b, 0x1e,
    0xe7, 0x84, 0x0, 0x8c, 0xf0, 0xd1, 0xd7, 0x84,

    /* U+0065 "e" */
    0x0, 0x66, 0xe6, 0xee, 0x97, 0x2, 0x34, 0xf5,
    0x41, 0x2f, 0xe3, 0x5b, 0x7e, 0xfe, 0xf8, 0xf8,
    0xfa, 0x88, 0x7c, 0xde, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xfe, 0x6d, 0x23, 0xa2, 0x2e, 0x0, 0x66,
    0xdf, 0xf6, 0xa8, 0xc,

    /* U+0066 "f" */
    0x0, 0x5b, 0xe6, 0xdb, 0x0, 0xd7, 0x9b, 0x17,
    0xc0, 0xff, 0xfd, 0x99, 0x0, 0xe0, 0x70, 0x0,
    0x0, 0xe0, 0x70, 0x0, 0x0, 0xe0, 0x70, 0x0,
    0x0, 0xe0, 0x70, 0x0, 0x0, 0xe0, 0x70, 0x0,

    /* U+0067 "g" */
    0x0, 0x85, 0xee, 0xd1, 0xca, 0x90, 0x43, 0xfd,
    0x52, 0x35, 0xe5, 0x90, 0x80, 0xcc, 0x0, 0x0,
    0xbc, 0x90, 0x7f, 0xca, 0x0, 0x0, 0xbc, 0x90,
    0x43, 0xfc, 0x4b, 0x32, 0xe5, 0x90, 0x0, 0x87,
    0xef, 0xd1, 0xdd, 0x8b, 0x6, 0x6f, 0x1d, 0x33,
    0xee, 0x5a, 0x9, 0x9c, 0xf1, 0xeb, 0x8c, 0x1,

    /* U+0068 "h" */
    0x58, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x58, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x58, 0xf7, 0xa2, 0xf2,
    0xbf, 0xb, 0x58, 0xfd, 0x56, 0x3a, 0xf5, 0x5b,
    0x58, 0xf4, 0x0, 0x0, 0xd8, 0x73, 0x58, 0xf4,
    0x0, 0x0, 0xd8, 0x74, 0x58, 0xf4, 0x0, 0x0,
    0xd8, 0x74, 0x58, 0xf4, 0x0, 0x0, 0xd8, 0x74,

    /* U+0069 "i" */
    0x33, 0xe6, 0xe, 0xa, 0x4e, 0x0, 0x40, 0xff,
    0x10, 0x40, 0xff, 0x10, 0x40, 0xff, 0x10, 0x40,
    0xff, 0x10, 0x40, 0xff, 0x10, 0x40, 0xff, 0x10,

    /* U+006A "j" */
    0x0, 0x41, 0xe2, 0x5, 0x0, 0xe, 0x4b, 0x0,
    0x0, 0x48, 0xff, 0x8, 0x0, 0x48, 0xff, 0x8,
    0x0, 0x48, 0xff, 0x8, 0x0, 0x48, 0xff, 0x8,
    0x0, 0x48, 0xff, 0x8, 0x0, 0x48, 0xff, 0x8,
    0xa, 0x73, 0xf9, 0x2, 0x61, 0xf5, 0x82, 0x0,

    /* U+006B "k" */
    0x54, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x54, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x54, 0xfc, 0x0, 0x71,
    0xf2, 0x2e, 0x54, 0xfc, 0x4c, 0xf9, 0x48, 0x0,
    0x54, 0xfe, 0xf2, 0x99, 0x0, 0x0, 0x54, 0xff,
    0xbe, 0xf3, 0x22, 0x0, 0x54, 0xfc, 0x0, 0xb9,
    0xc2, 0x2, 0x54, 0xfc, 0x0, 0x1d, 0xf0, 0x74,

    /* U+006C "l" */
    0x40, 0xff, 0x10, 0x40, 0xff, 0x10, 0x40, 0xff,
    0x10, 0x40, 0xff, 0x10, 0x40, 0xff, 0x10, 0x40,
    0xff, 0x10, 0x40, 0xff, 0x10, 0x40, 0xff, 0x10,

    /* U+006D "m" */
    0x54, 0xef, 0xa9, 0xf1, 0xb8, 0x65, 0xe4, 0xe8,
    0x4e, 0x54, 0xfd, 0x4d, 0x37, 0xf9, 0xbe, 0x28,
    0xa5, 0xcf, 0x54, 0xf8, 0x0, 0x0, 0xe0, 0x70,
    0x0, 0x68, 0xe7, 0x54, 0xf8, 0x0, 0x0, 0xe0,
    0x70, 0x0, 0x68, 0xe8, 0x54, 0xf8, 0x0, 0x0,
    0xe0, 0x70, 0x0, 0x68, 0xe8, 0x54, 0xf8, 0x0,
    0x0, 0xe0, 0x70, 0x0, 0x68, 0xe8,

    /* U+006E "n" */
    0x58, 0xeb, 0xa2, 0xf2, 0xc1, 0xc, 0x58, 0xfd,
    0x57, 0x32, 0xf5, 0x5c, 0x58, 0xf4, 0x0, 0x0,
    0xd8, 0x73, 0x58, 0xf4, 0x0, 0x0, 0xd8, 0x74,
    0x58, 0xf4, 0x0, 0x0, 0xd8, 0x74, 0x58, 0xf4,
    0x0, 0x0, 0xd8, 0x74,

    /* U+006F "o" */
    0x0, 0x6f, 0xe6, 0xee, 0x98, 0x7, 0x3f, 0xfd,
    0x4d, 0x2e, 0xe3, 0x7b, 0x87, 0xcc, 0x0, 0x0,
    0x88, 0xc4, 0x81, 0xc9, 0x0, 0x0, 0x89, 0xc7,
    0x3b, 0xfc, 0x49, 0x2b, 0xe0, 0x80, 0x0, 0x6b,
    0xe2, 0xf3, 0x9e, 0x8,

    /* U+0070 "p" */
    0x54, 0xf0, 0xbd, 0xf3, 0xb0, 0xd, 0x54, 0xfd,
    0x37, 0x1b, 0xe3, 0x7c, 0x54, 0xf8, 0x0, 0x0,
    0x97, 0xb4, 0x54, 0xf8, 0x0, 0x0, 0x9d, 0xb4,
    0x54, 0xfd, 0x4c, 0x2f, 0xea, 0x7a, 0x54, 0xfa,
    0xb4, 0xf3, 0xaf, 0xc, 0x54, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x54, 0xf8, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x8e, 0xef, 0xcd, 0xd0, 0x84, 0x4b, 0xfc,
    0x46, 0x31, 0xea, 0x84, 0x85, 0xcb, 0x0, 0x0,
    0xcc, 0x84, 0x83, 0xc9, 0x0, 0x0, 0xcc, 0x84,
    0x47, 0xfb, 0x44, 0x33, 0xeb, 0x84, 0x0, 0x8b,
    0xef, 0xce, 0xe4, 0x84, 0x0, 0x0, 0x0, 0x0,
    0xcc, 0x84, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x84,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x1, 0x54, 0xf2, 0xbf, 0xb6,
    0x54, 0xfe, 0x6c, 0x23, 0x54, 0xf8, 0x0, 0x0,
    0x54, 0xf8, 0x0, 0x0, 0x54, 0xf8, 0x0, 0x0,
    0x54, 0xf8, 0x0, 0x0,

    /* U+0073 "s" */
    0x5, 0x9e, 0xf2, 0xe6, 0x6c, 0x0, 0x54, 0xf4,
    0x1b, 0x56, 0xdb, 0x11, 0x24, 0xed, 0xaf, 0x62,
    0xe, 0x0, 0x0, 0x15, 0x64, 0xbf, 0xdc, 0x8,
    0x75, 0xcb, 0x19, 0x3e, 0xff, 0x20, 0xb, 0xa1,
    0xed, 0xe9, 0x79, 0x0,

    /* U+0074 "t" */
    0x0, 0x80, 0x26, 0x0, 0x0, 0xff, 0x4c, 0x0,
    0xf0, 0xff, 0xfd, 0x52, 0x0, 0xff, 0x4c, 0x0,
    0x0, 0xff, 0x4c, 0x0, 0x0, 0xff, 0x4c, 0x0,
    0x0, 0xf8, 0x72, 0xb, 0x0, 0x93, 0xf6, 0x53,

    /* U+0075 "u" */
    0x5c, 0xf0, 0x0, 0x0, 0xdc, 0x74, 0x5c, 0xf0,
    0x0, 0x0, 0xdc, 0x74, 0x5c, 0xf0, 0x0, 0x0,
    0xdc, 0x74, 0x5b, 0xf0, 0x0, 0x0, 0xdc, 0x74,
    0x42, 0xfe, 0x38, 0x43, 0xf4, 0x74, 0x2, 0xab,
    0xf4, 0xc1, 0xdf, 0x74,

    /* U+0076 "v" */
    0xb5, 0x9d, 0x0, 0x32, 0xff, 0x21, 0x5e, 0xe7,
    0x1, 0x7c, 0xca, 0x0, 0xe, 0xf8, 0x35, 0xc7,
    0x74, 0x0, 0x0, 0xb1, 0x95, 0xfc, 0x1f, 0x0,
    0x0, 0x5a, 0xfc, 0xc8, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x72, 0x0, 0x0,

    /* U+0077 "w" */
    0xb3, 0x90, 0x0, 0x92, 0xb5, 0x0, 0x6a, 0xd5,
    0x6f, 0xcb, 0x0, 0xdb, 0xf6, 0xb, 0xa4, 0x92,
    0x2b, 0xfb, 0x30, 0xe0, 0xc0, 0x4f, 0xde, 0x4f,
    0x0, 0xe6, 0xb1, 0x94, 0x72, 0xb4, 0xfc, 0xf,
    0x0, 0xa3, 0xff, 0x49, 0x24, 0xff, 0xc8, 0x0,
    0x0, 0x5e, 0xf5, 0x8, 0x0, 0xd7, 0x85, 0x0,

    /* U+0078 "x" */
    0x78, 0xe6, 0xb, 0x6e, 0xed, 0x14, 0x6, 0xd8,
    0x93, 0xed, 0x68, 0x0, 0x0, 0x46, 0xff, 0xcc,
    0x2, 0x0, 0x0, 0x54, 0xff, 0xd6, 0x5, 0x0,
    0xb, 0xe2, 0x7e, 0xe2, 0x77, 0x0, 0x87, 0xdd,
    0x6, 0x59, 0xf4, 0x1d,

    /* U+0079 "y" */
    0xc2, 0x9e, 0x0, 0x43, 0xfe, 0x1c, 0x65, 0xeb,
    0x2, 0x8f, 0xc0, 0x0, 0x10, 0xf8, 0x3c, 0xda,
    0x66, 0x0, 0x0, 0xab, 0xb1, 0xfa, 0x12, 0x0,
    0x0, 0x4e, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x5,
    0xf7, 0x57, 0x0, 0x0, 0xe, 0x6b, 0xef, 0xa,
    0x0, 0x0, 0x71, 0xee, 0x5a, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x80, 0xff, 0xff, 0xff, 0xfe, 0xa, 0x8, 0x10,
    0x1e, 0xe5, 0x8f, 0x0, 0x0, 0x0, 0xa7, 0xce,
    0x6, 0x0, 0x0, 0x62, 0xf4, 0x26, 0x0, 0x0,
    0x27, 0xf4, 0x70, 0x10, 0x10, 0x2, 0x8e, 0xff,
    0xff, 0xff, 0xff, 0x28,

    /* U+007B "{" */
    0x0, 0x1, 0x74, 0x51, 0x0, 0x61, 0xd7, 0xc,
    0x0, 0xa0, 0x95, 0x0, 0x0, 0xa8, 0x8e, 0x0,
    0xb, 0xd5, 0x64, 0x0, 0xb3, 0xdf, 0x5, 0x0,
    0xc, 0xd4, 0x62, 0x0, 0x0, 0xa8, 0x8e, 0x0,
    0x0, 0xa0, 0x95, 0x0, 0x0, 0x60, 0xd9, 0xd,
    0x0, 0x0, 0x72, 0x50,

    /* U+007C "|" */
    0x10, 0xd4, 0x10, 0xd4, 0x10, 0xd4, 0x10, 0xd4,
    0x10, 0xd4, 0x10, 0xd4, 0x10, 0xd4, 0x10, 0xd4,
    0x10, 0xd4, 0x6, 0x52,

    /* U+007D "}" */
    0x87, 0x3f, 0x0, 0x0, 0x38, 0xf6, 0x19, 0x0,
    0x0, 0xe9, 0x4f, 0x0, 0x0, 0xe2, 0x58, 0x0,
    0x0, 0xb5, 0x8e, 0x1, 0x0, 0x31, 0xfd, 0x5f,
    0x0, 0xb6, 0x8d, 0x1, 0x0, 0xe2, 0x58, 0x0,
    0x0, 0xe9, 0x4f, 0x0, 0x3c, 0xf6, 0x18, 0x0,
    0x85, 0x3f, 0x0, 0x0,

    /* U+007E "~" */
    0x2, 0xa4, 0xe7, 0x9d, 0x16, 0x7c, 0x95, 0x47,
    0xd8, 0x1c, 0x97, 0xfc, 0xef, 0x33, 0x5, 0xa,
    0x0, 0x0, 0x12, 0x8, 0x0,

    /* U+00B0 "°" */
    0x8, 0xab, 0xbb, 0x1d, 0x43, 0x8a, 0x5d, 0x73,
    0xb, 0xb3, 0xc2, 0x22
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 44, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 47, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24, .adv_w = 57, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 36, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 92, .adv_w = 100, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 158, .adv_w = 129, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 222, .adv_w = 112, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 278, .adv_w = 30, .box_w = 2, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 284, .adv_w = 61, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 332, .adv_w = 62, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 380, .adv_w = 78, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 405, .adv_w = 98, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 441, .adv_w = 39, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 449, .adv_w = 58, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 457, .adv_w = 49, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 463, .adv_w = 70, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 508, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 556, .adv_w = 100, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 596, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 644, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 692, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 740, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 788, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 836, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 884, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 932, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 980, .adv_w = 47, .box_w = 3, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 998, .adv_w = 42, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1025, .adv_w = 89, .box_w = 5, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1055, .adv_w = 98, .box_w = 6, .box_h = 4, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1079, .adv_w = 92, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1115, .adv_w = 86, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1155, .adv_w = 157, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1255, .adv_w = 117, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1319, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1375, .adv_w = 115, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1431, .adv_w = 115, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1487, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1535, .adv_w = 97, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1583, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1639, .adv_w = 125, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1695, .adv_w = 50, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1719, .adv_w = 98, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1767, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1823, .adv_w = 95, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1871, .adv_w = 154, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1943, .adv_w = 125, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1999, .adv_w = 122, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2063, .adv_w = 112, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2119, .adv_w = 122, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2199, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2255, .adv_w = 106, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2311, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2367, .adv_w = 115, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2423, .adv_w = 114, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2487, .adv_w = 155, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2567, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2623, .adv_w = 107, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2679, .adv_w = 106, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2735, .adv_w = 48, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2771, .adv_w = 74, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2816, .adv_w = 48, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2852, .adv_w = 75, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 2872, .adv_w = 79, .box_w = 5, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2882, .adv_w = 57, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 2888, .adv_w = 95, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2924, .adv_w = 99, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2972, .adv_w = 92, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3008, .adv_w = 99, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3056, .adv_w = 94, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3092, .adv_w = 62, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3124, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3172, .adv_w = 98, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3220, .adv_w = 45, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3244, .adv_w = 44, .box_w = 4, .box_h = 10, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3284, .adv_w = 92, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3332, .adv_w = 45, .box_w = 3, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3356, .adv_w = 153, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3410, .adv_w = 98, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3446, .adv_w = 100, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3482, .adv_w = 99, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3530, .adv_w = 100, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3578, .adv_w = 62, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3606, .adv_w = 91, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3642, .adv_w = 59, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3674, .adv_w = 98, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3710, .adv_w = 87, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3746, .adv_w = 131, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3794, .adv_w = 89, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3830, .adv_w = 86, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3878, .adv_w = 89, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3914, .adv_w = 59, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3958, .adv_w = 44, .box_w = 2, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3978, .adv_w = 59, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4022, .adv_w = 117, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4043, .adv_w = 67, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 5}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 1, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    1, 53,
    3, 3,
    3, 8,
    3, 34,
    3, 66,
    3, 68,
    3, 69,
    3, 70,
    3, 72,
    3, 78,
    3, 79,
    3, 80,
    3, 81,
    3, 82,
    3, 84,
    3, 88,
    8, 3,
    8, 8,
    8, 34,
    8, 66,
    8, 68,
    8, 69,
    8, 70,
    8, 72,
    8, 78,
    8, 79,
    8, 80,
    8, 81,
    8, 82,
    8, 84,
    8, 88,
    9, 55,
    9, 56,
    9, 58,
    13, 3,
    13, 8,
    15, 3,
    15, 8,
    16, 16,
    34, 3,
    34, 8,
    34, 32,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 78,
    34, 79,
    34, 80,
    34, 81,
    34, 85,
    34, 86,
    34, 87,
    34, 88,
    34, 90,
    34, 91,
    35, 53,
    35, 55,
    35, 58,
    36, 10,
    36, 53,
    36, 62,
    36, 94,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 57,
    37, 58,
    37, 59,
    38, 53,
    38, 68,
    38, 69,
    38, 70,
    38, 71,
    38, 72,
    38, 80,
    38, 82,
    38, 86,
    38, 87,
    38, 88,
    38, 90,
    39, 13,
    39, 15,
    39, 34,
    39, 43,
    39, 53,
    39, 66,
    39, 68,
    39, 69,
    39, 70,
    39, 72,
    39, 80,
    39, 82,
    39, 83,
    39, 86,
    39, 87,
    39, 90,
    41, 34,
    41, 53,
    41, 57,
    41, 58,
    42, 34,
    42, 53,
    42, 57,
    42, 58,
    43, 34,
    44, 14,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 80,
    44, 82,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 86,
    45, 87,
    45, 88,
    45, 90,
    46, 34,
    46, 53,
    46, 57,
    46, 58,
    47, 34,
    47, 53,
    47, 57,
    47, 58,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 57,
    48, 58,
    48, 59,
    49, 13,
    49, 15,
    49, 34,
    49, 43,
    49, 57,
    49, 59,
    49, 66,
    49, 68,
    49, 69,
    49, 70,
    49, 72,
    49, 80,
    49, 82,
    49, 85,
    49, 87,
    49, 90,
    50, 53,
    50, 55,
    50, 56,
    50, 58,
    51, 53,
    51, 55,
    51, 58,
    53, 1,
    53, 13,
    53, 14,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 43,
    53, 48,
    53, 50,
    53, 52,
    53, 53,
    53, 55,
    53, 56,
    53, 58,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    55, 10,
    55, 13,
    55, 14,
    55, 15,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 62,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 80,
    55, 82,
    55, 83,
    55, 86,
    55, 87,
    55, 90,
    55, 94,
    56, 10,
    56, 13,
    56, 14,
    56, 15,
    56, 34,
    56, 53,
    56, 62,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 83,
    56, 86,
    56, 94,
    57, 14,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 55,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 80,
    57, 82,
    57, 86,
    57, 87,
    57, 90,
    58, 7,
    58, 10,
    58, 11,
    58, 13,
    58, 14,
    58, 15,
    58, 34,
    58, 36,
    58, 40,
    58, 43,
    58, 48,
    58, 50,
    58, 52,
    58, 53,
    58, 54,
    58, 55,
    58, 56,
    58, 57,
    58, 58,
    58, 62,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    58, 94,
    59, 34,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    59, 68,
    59, 69,
    59, 70,
    59, 72,
    59, 80,
    59, 82,
    59, 86,
    59, 87,
    59, 88,
    59, 90,
    60, 43,
    60, 54,
    66, 3,
    66, 8,
    66, 87,
    66, 90,
    67, 3,
    67, 8,
    67, 87,
    67, 89,
    67, 90,
    67, 91,
    68, 3,
    68, 8,
    70, 3,
    70, 8,
    70, 87,
    70, 90,
    71, 3,
    71, 8,
    71, 10,
    71, 62,
    71, 68,
    71, 69,
    71, 70,
    71, 72,
    71, 82,
    71, 94,
    73, 3,
    73, 8,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 82,
    78, 3,
    78, 8,
    79, 3,
    79, 8,
    80, 3,
    80, 8,
    80, 87,
    80, 89,
    80, 90,
    80, 91,
    81, 3,
    81, 8,
    81, 87,
    81, 89,
    81, 90,
    81, 91,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 72,
    83, 80,
    83, 82,
    83, 85,
    83, 87,
    83, 88,
    83, 90,
    85, 80,
    87, 3,
    87, 8,
    87, 13,
    87, 15,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 71,
    87, 72,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 80,
    89, 82,
    90, 3,
    90, 8,
    90, 13,
    90, 15,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 71,
    90, 72,
    90, 80,
    90, 82,
    91, 68,
    91, 69,
    91, 70,
    91, 72,
    91, 80,
    91, 82,
    92, 43,
    92, 54
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -5, -3, -3, -10, -4, -5, -5, -5,
    -5, -2, -2, -8, -2, -5, -8, 1,
    -3, -3, -10, -4, -5, -5, -5, -5,
    -2, -2, -8, -2, -5, -8, 1, 2,
    3, 2, -24, -24, -24, -24, -21, -10,
    -10, -7, -2, -2, -2, -2, -10, -1,
    -7, -3, -13, -4, -4, -1, -4, -1,
    -1, -4, -3, -4, 1, -2, -2, -5,
    -2, -2, -1, -1, -10, -10, -2, -7,
    -2, -2, -4, -2, 2, -2, -2, -2,
    -2, -2, -2, -2, -1, -2, -2, -2,
    -24, -24, -16, -18, 2, -3, -2, -2,
    -2, -2, -2, -2, -2, -2, -2, -2,
    2, -2, 1, -2, 2, -2, 1, -2,
    -2, -14, -3, -3, -3, -3, -2, -2,
    -2, -2, -2, -2, -2, -3, -5, -3,
    -25, -25, 2, -5, -5, -5, -5, -18,
    -2, -18, -8, -24, -1, -11, -4, -11,
    2, -2, 1, -2, 2, -2, 1, -2,
    -10, -10, -2, -7, -2, -2, -4, -2,
    -35, -35, -15, -16, -4, -3, -1, -1,
    -1, -1, -1, -1, -1, 1, 1, 1,
    -3, -2, -2, -3, -4, -2, -4, -5,
    -22, -23, -22, -10, -2, -2, -19, -2,
    -2, -1, 1, 1, 1, 1, -14, -8,
    -8, -8, -8, -8, -8, -18, -8, -8,
    -6, -7, -6, -7, -4, -7, -7, -5,
    -2, 2, -18, -13, -18, -6, -1, -1,
    -1, -1, 1, -4, -4, -4, -4, -4,
    -4, -4, -3, -2, -1, -1, 2, 1,
    -12, -5, -12, -4, 1, 1, -3, -3,
    -3, -3, -3, -3, -3, -2, -2, 1,
    -13, -2, -2, -2, -2, 1, -2, -2,
    -2, -2, -2, -2, -2, -3, -3, -3,
    2, -4, -20, -13, -20, -13, -2, -2,
    -8, -2, -2, -1, 1, -8, 2, 1,
    1, 2, 2, -5, -6, -6, -6, -2,
    -6, -3, -3, -6, -3, -6, -3, -5,
    -2, -3, -2, -2, -2, -3, 2, 1,
    -2, -2, -2, -2, -2, -2, -2, -2,
    -2, -2, -2, -2, -2, -2, -2, -2,
    -1, -1, -1, -1, -2, -2, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1,
    1, 1, 2, 2, -2, -2, -2, -2,
    -2, 2, -7, -7, -2, -2, -2, -2,
    -2, -7, -7, -7, -7, -8, -8, -1,
    -2, -1, -1, -2, -2, -1, -1, -1,
    -1, 1, 1, -15, -15, -3, -2, -2,
    -2, 2, -2, -3, -2, 4, 2, 1,
    2, -3, 1, 1, -14, -14, -1, -1,
    -1, -1, 1, -1, -1, -1, -11, -11,
    -2, -2, -2, -2, -3, -2, 1, 1,
    -14, -14, -1, -1, -1, -1, 1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1,
    -2, -2
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 434,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 8,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_lv_demo_high_res_roboto_medium_11 = {
#else
lv_font_t font_lv_demo_high_res_roboto_medium_11 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 13,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_USE_DEMO_HIGH_RES*/

