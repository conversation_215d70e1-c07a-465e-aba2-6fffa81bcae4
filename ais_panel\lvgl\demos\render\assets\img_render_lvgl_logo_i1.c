#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif


#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_RENDER_LVGL_LOGO_I1
#define LV_ATTRIBUTE_IMG_RENDER_LVGL_LOGO_I1
#endif

static const
LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_RENDER_LVGL_LOGO_I1
uint8_t img_render_lvgl_logo_i1_map[] = {

    0x00,0x00,0x00,0xff,0xff,0xff,0xff,0xff,

    0x20,0x00,0x00,0x18,
    0xc0,0x00,0x00,0x04,
    0x80,0x00,0x00,0x00,
    0x00,0x00,0x00,0x70,
    0x00,0x00,0x00,0xf0,
    0x00,0x00,0x00,0x60,
    0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,
    0xff,0x80,0x00,0x00,
    0xff,0xc0,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0x80,0x60,0x00,0x00,
    0xff,0xe0,0x00,0x00,
    0x7f,0x9f,0xf0,0x00,
    0x80,0x60,0x18,0x00,
    0x80,0x60,0x18,0x00,
    0x80,0x60,0x18,0x00,
    0x80,0x60,0x18,0x00,
    0x80,0x60,0x18,0x00,
    0x80,0x60,0x18,0x00,
    0x80,0x60,0x18,0x00,
    0x80,0x60,0x18,0x04,
    0x40,0x00,0x18,0x0c,

};

const lv_image_dsc_t img_render_lvgl_logo_i1 = {
  .header.magic = LV_IMAGE_HEADER_MAGIC,
  .header.cf = LV_COLOR_FORMAT_I1,
  .header.flags = 0,
  .header.w = 30,
  .header.h = 30,
  .header.stride = 4,
  .data_size = sizeof(img_render_lvgl_logo_i1_map),
  .data = img_render_lvgl_logo_i1_map,
};

