#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: vkontakte.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_vkontakte_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x4B,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x1B,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3A,0x43,0x3A,0x3B,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x3A,0x43,0x3A,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9C,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x1B,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3B,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5A,0x43,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x1B,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x9C,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5A,0x43,0x00,0x00,
0x00,0x00,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x00,0x00,0x7B,0x4B,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x1B,0x3B,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x1B,0x64,0x3B,0x64,0x1B,0x64,0x5A,0x43,0x3A,0x43,0x7C,0x74,0x1D,0xB6,0x1D,0xB6,0xDD,0xAD,0xBB,0x53,0x3A,0x43,0x3A,0x43,0xDB,0x5B,0x3B,0x64,0x3B,0x64,0xFB,0x5B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x7F,0xEF,0xFF,0xFF,0xFF,0xFF,0xDD,0xAD,0x3A,0x43,0xFB,0x5B,0xBF,0xF7,0xFF,0xFF,0xFF,0xFF,0x3C,0x8D,0x3A,0x43,0x9B,0x53,0xBF,0xF7,0xFF,0xFF,0xFF,0xFF,0x7F,0xEF,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5D,0x95,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0x9B,0x53,0x3A,0x43,0x1E,0xDF,0xFF,0xFF,
0xFF,0xFF,0x1C,0x8D,0x3A,0x43,0xDD,0xAD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3C,0x95,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5B,0x43,0x7F,0xEF,0xFF,0xFF,0xFF,0xFF,0x3E,0xBE,0x3A,0x43,0xFE,0xD6,0xFF,0xFF,0xFF,0xFF,0xFC,0x8C,0xDB,0x5B,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9E,0xCE,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,
0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x1C,0x8D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3D,0x95,0x9F,0xF7,0xFF,0xFF,0xFF,0xFF,0x1D,0xB6,0x1E,0xDF,0xFF,0xFF,0xFF,0xFF,0x7F,0xEF,0x9B,0x53,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0xFE,0xD6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9D,0x9D,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0xDB,0x5B,0xDF,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xF7,0x3B,0x64,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0xBC,0x7C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0xE7,0xBE,0xCE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0xEF,0xDB,0x5B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x7C,0x74,0x7F,0xEF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0x7D,0x9D,0x5A,0x43,0x9E,0xC6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xD6,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5A,0x43,0x9C,0x7C,0x9D,0x9D,0xBD,0xA5,0xBB,0x53,0x3A,0x43,0x5A,0x43,0x3C,0x95,0x1D,0xB6,0x1D,0xB6,0x7D,0x9D,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,
0x5A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5A,0x43,0x00,0x00,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x00,0x00,0x00,0x00,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x1B,0x43,0x00,0x00,
0x00,0x00,0x00,0x00,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1B,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x7B,0x4B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x5B,0x43,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3B,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1B,0x3B,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,
0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5A,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x4B,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x3A,0x43,0x7B,0x4B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x1B,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x3A,0x3B,0x3A,0x3B,
0x3A,0x3B,0x3A,0x3B,0x3A,0x43,0x3A,0x43,0x3A,0x43,0x5A,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x1B,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_vkontakte_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_vkontakte_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_vkontakte_icon_data};

