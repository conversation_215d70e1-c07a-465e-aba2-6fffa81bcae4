#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_22
    #define LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_22
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_22 uint8_t
img_multilang_avatar_22_map[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd2, 0xd2, 0x87, 0x11, 0xd9, 0xd0, 0x8f, 0x37, 0xd0, 0xcb, 0x8d, 0x5e, 0xc5, 0xc9, 0x90, 0x86, 0xce, 0xc8, 0x90, 0xa8, 0xde, 0xd8, 0x95, 0xbf, 0xe4, 0xea, 0xa5, 0xd2, 0xdd, 0xde, 0xa0, 0xe5, 0xd2, 0xc4, 0x81, 0xee, 0x8c, 0x6a, 0x11, 0xf2, 0xad, 0x93, 0x3e, 0xff, 0xea, 0xe6, 0xce, 0xff, 0xce, 0xd9, 0xd9, 0xf2, 0xd7, 0xde, 0xbc, 0xee, 0xd6, 0xc8, 0xac, 0xe5, 0xd5, 0xd2, 0xbb, 0xd2, 0xe0, 0xe5, 0xca, 0xbf, 0x65, 0x4b, 0x39, 0xa8, 0x13, 0x0b, 0x0b, 0x86, 0x30, 0x30, 0x23, 0x5e, 0x25, 0x29, 0x6f, 0x37, 0x2d, 0x2d, 0x96, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0xc1, 0x7a, 0x19, 0xd8, 0xcf, 0x99, 0x55, 0xd2, 0xce, 0x95, 0x8f, 0xc7, 0xcb, 0x95, 0xc1, 0xcb, 0xcd, 0x9d, 0xe9, 0xa7, 0xa3, 0x5f, 0xff, 0xad, 0xa7, 0x59, 0xff, 0xd5, 0xd0, 0x80, 0xff, 0xdf, 0xdb, 0x8f, 0xff, 0xe5, 0xea, 0x91, 0xff, 0xbd, 0xc3, 0x7b, 0xff, 0x99, 0x9b, 0x74, 0xff, 0xc6, 0xc9, 0x95, 0xff, 0xff, 0xff, 0xbb, 0xff, 0x8f, 0x6d, 0x49, 0xff, 0x7d, 0x58, 0x1f, 0xff, 0xeb, 0xe0, 0x98, 0xff, 0xd9, 0xe1, 0xb9, 0xff, 0xd6, 0xde, 0xb1, 0xff, 0xd4, 0xd5, 0xa6, 0xff, 0xca, 0xd4, 0xb6, 0xff, 0xe1, 0xeb, 0xe4, 0xff, 0xbc, 0xba, 0xa6, 0xff, 0x2a, 0x2b, 0x19, 0xff, 0x30, 0x11, 0x15, 0xff, 0x65, 0x59, 0x8a, 0xff, 0x26, 0x45, 0xab, 0xff, 0x2b, 0x54, 0xb3, 0xe9, 0x40, 0x5c, 0xbc, 0xc1, 0x29, 0x4c, 0xab, 0x8f, 0x6f, 0xcf, 0xff, 0x55, 0x47, 0x7a, 0xcc, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0x91, 0x48, 0x07, 0x7b, 0x5b, 0x20, 0x46, 0xb1, 0xa1, 0x68, 0x8e, 0xe6, 0xdd, 0x9e, 0xd7, 0xb2, 0xad, 0x5f, 0xff, 0xd2, 0xc6, 0x89, 0xff, 0xe1, 0xdc, 0x9d, 0xff, 0xcd, 0xce, 0x92, 0xff, 0xce, 0xce, 0x91, 0xff, 0xbd, 0xbd, 0x6e, 0xff, 0xc7, 0xc6, 0x72, 0xff, 0xf2, 0xf1, 0xa6, 0xff, 0xc4, 0xc2, 0x7f, 0xff, 0x6b, 0x69, 0x48, 0xff, 0x2a, 0x22, 0x2a, 0xff, 0x07, 0x00, 0x0b, 0xff, 0x4a, 0x4f, 0x33, 0xff, 0xbd, 0xc4, 0x83, 0xff, 0x98, 0x92, 0x60, 0xff, 0x6e, 0x52, 0x22, 0xff, 0xd9, 0xcb, 0x7e, 0xff, 0xee, 0xf7, 0xb6, 0xff, 0xdf, 0xe4, 0xb6, 0xff, 0xc7, 0xcf, 0x8e, 0xff, 0xd0, 0xdc, 0xb5, 0xff, 0xdc, 0xe0, 0xe8, 0xff, 0xf5, 0xfd, 0xe2, 0xff, 0x8f, 0x84, 0x5a, 0xff, 0x2f, 0x02, 0x14, 0xff, 0x4b, 0x36, 0x3f, 0xff, 0x5d, 0x9d, 0xe2, 0xff, 0x3d, 0x61, 0xc8, 0xff, 0x4c, 0x82, 0xd1, 0xff, 0x12, 0x1a, 0x81, 0xff, 0x14, 0x4d, 0xc5, 0xff, 0x70, 0xbb, 0xff, 0xff, 0x42, 0x45, 0x83, 0xd7, 0x13, 0x1a, 0x27, 0x8e, 0x2b, 0x28, 0x20, 0x46, 0x24, 0x24, 0x24, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xcc, 0x05, 0xe9, 0xe6, 0xc6, 0x48, 0x6e, 0x5b, 0x30, 0x9f, 0xb7, 0x98, 0x47, 0xef, 0x82, 0x5c, 0x2f, 0xff, 0xa2, 0x92, 0x59, 0xff, 0xe8, 0xdd, 0x9a, 0xff, 0xa3, 0x9a, 0x45, 0xff, 0xd1, 0xc2, 0x7d, 0xff, 0xca, 0xc7, 0x87, 0xff, 0xcf, 0xcc, 0x7f, 0xff, 0xe5, 0xde, 0x89, 0xff, 0xf9, 0xf6, 0xaa, 0xff, 0xce, 0xd0, 0x95, 0xff, 0x6e, 0x73, 0x52, 0xff, 0x1d, 0x22, 0x16, 0xff, 0x07, 0x04, 0x0f, 0xff, 0x1e, 0x1b, 0x23, 0xff, 0x28, 0x2b, 0x28, 0xff, 0x1a, 0x1a, 0x1f, 0xff, 0x14, 0x0c, 0x16, 0xff, 0x2e, 0x27, 0x24, 0xff, 0x36, 0x29, 0x20, 0xff, 0x57, 0x54, 0x2f, 0xff, 0x8c, 0x94, 0x68, 0xff, 0xcc, 0xcd, 0xb0, 0xff, 0xef, 0xf1, 0xbd, 0xff, 0xf6, 0xf1, 0xcc, 0xff, 0xfc, 0xf8, 0xfa, 0xff, 0xff, 0xff, 0xec, 0xff, 0xc5, 0xa3, 0x72, 0xff, 0x35, 0x1d, 0x23, 0xff, 0x2a, 0x17, 0x00, 0xff, 0x5b, 0x8e, 0xa0, 0xff, 0x4c, 0x89, 0xf8, 0xff, 0x4b, 0x80, 0xe5, 0xff, 0x64, 0x9e, 0xf2, 0xff, 0x1a, 0x26, 0x8d, 0xff, 0x2c, 0x49, 0x88, 0xff, 0x2d, 0x31, 0x2e, 0xff, 0x25, 0x22, 0x15, 0xff, 0x29, 0x29, 0x2f, 0xff, 0x27, 0x28, 0x26, 0xef, 0x2b, 0x29, 0x23, 0x9f, 0x2a, 0x26, 0x23, 0x48, 0x33, 0x33, 0x33, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0xda, 0xa7, 0x23, 0xe8, 0xe4, 0xc7, 0x86, 0xdc, 0xdc, 0xab, 0xe5, 0xda, 0xdd, 0xa2, 0xff, 0x70, 0x5b, 0x38, 0xff, 0xa6, 0x83, 0x41, 0xff, 0x77, 0x51, 0x34, 0xff, 0x9c, 0x92, 0x51, 0xff, 0xd8, 0xcf, 0x86, 0xff, 0xb1, 0xa6, 0x4b, 0xff, 0xda, 0xca, 0x82, 0xff, 0xcc, 0xcc, 0x86, 0xff, 0xee, 0xee, 0xab, 0xff, 0xcd, 0xc7, 0x8e, 0xff, 0x7e, 0x7a, 0x54, 0xff, 0x2b, 0x2a, 0x1c, 0xff, 0x05, 0x08, 0x0a, 0xff, 0x16, 0x1c, 0x29, 0xff, 0x27, 0x29, 0x33, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x2a, 0x25, 0xff, 0x28, 0x24, 0x2e, 0xff, 0x27, 0x20, 0x34, 0xff, 0x24, 0x1f, 0x2f, 0xff, 0x22, 0x23, 0x2a, 0xff, 0x10, 0x13, 0x14, 0xff, 0x0d, 0x0d, 0x04, 0xff, 0x30, 0x32, 0x24, 0xff, 0x6a, 0x69, 0x5c, 0xff, 0x83, 0x78, 0x65, 0xff, 0xba, 0xb5, 0xa9, 0xff, 0xbd, 0xad, 0xa0, 0xff, 0x65, 0x43, 0x29, 0xff, 0x35, 0x2b, 0x25, 0xff, 0x40, 0x23, 0x0a, 0xff, 0x54, 0x5a, 0x58, 0xff, 0x50, 0x9a, 0xfa, 0xff, 0x27, 0x48, 0xc9, 0xff, 0x75, 0xbc, 0xff, 0xff, 0x38, 0x4e, 0x5c, 0xff, 0x26, 0x04, 0x00, 0xff, 0x25, 0x24, 0x20, 0xff, 0x25, 0x29, 0x35, 0xff, 0x29, 0x28, 0x2b, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2a, 0x29, 0x26, 0xff, 0x2a, 0x2b, 0x28, 0xff, 0x19, 0x1c, 0x22, 0xe5, 0x20, 0x16, 0x1a, 0x86, 0x24, 0x15, 0x15, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xc4, 0x89, 0x41, 0xd4, 0xd7, 0xa1, 0xb4, 0xdc, 0xe5, 0xad, 0xfe, 0xc2, 0xc0, 0x89, 0xff, 0xb6, 0xb3, 0x5e, 0xff, 0xd3, 0xcd, 0x74, 0xff, 0x75, 0x57, 0x33, 0xff, 0x6d, 0x3f, 0x13, 0xff, 0x5d, 0x37, 0x25, 0xff, 0xa5, 0xa1, 0x5a, 0xff, 0xed, 0xe3, 0x98, 0xff, 0xaa, 0x9f, 0x3b, 0xff, 0xe7, 0xdc, 0x86, 0xff, 0xe4, 0xe4, 0xa0, 0xff, 0x70, 0x75, 0x57, 0xff, 0x21, 0x25, 0x1f, 0xff, 0x05, 0x04, 0x0b, 0xff, 0x1a, 0x17, 0x27, 0xff, 0x29, 0x27, 0x31, 0xff, 0x29, 0x29, 0x27, 0xff, 0x27, 0x2d, 0x1f, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x29, 0x22, 0x34, 0xff, 0x2b, 0x2a, 0x25, 0xff, 0x27, 0x2e, 0x19, 0xff, 0x24, 0x27, 0x29, 0xff, 0x22, 0x27, 0x2c, 0xff, 0x27, 0x26, 0x2f, 0xff, 0x2e, 0x27, 0x2d, 0xff, 0x19, 0x1f, 0x16, 0xff, 0x03, 0x05, 0x16, 0xff, 0x08, 0x08, 0x07, 0xff, 0x27, 0x28, 0x19, 0xff, 0x2f, 0x26, 0x2a, 0xff, 0x1c, 0x17, 0x17, 0xff, 0x3a, 0x2c, 0x28, 0xff, 0x60, 0x30, 0x2c, 0xff, 0x47, 0x28, 0x2c, 0xff, 0x7d, 0xb4, 0xf9, 0xff, 0x4b, 0x9b, 0xde, 0xff, 0x1c, 0x1a, 0x41, 0xff, 0x23, 0x21, 0x1e, 0xff, 0x2a, 0x2e, 0x20, 0xff, 0x35, 0x27, 0x28, 0xff, 0x20, 0x27, 0x2d, 0xff, 0x27, 0x2a, 0x28, 0xff, 0x2e, 0x2b, 0x1f, 0xff, 0x2a, 0x26, 0x1c, 0xff, 0x1c, 0x1e, 0x23, 0xff, 0x2d, 0x39, 0x46, 0xff, 0x2e, 0x43, 0x6c, 0xff, 0x51, 0x77, 0xc0, 0xfe, 0x6b, 0x9e, 0xf0, 0xb4, 0x4a, 0x71, 0xc8, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x93, 0x8c, 0x4d, 0x4c, 0xaa, 0x9f, 0x69, 0xcb, 0xed, 0xe4, 0xb6, 0xff, 0xd7, 0xdf, 0xa8, 0xff, 0xc5, 0xc7, 0x7d, 0xff, 0xc5, 0xca, 0x80, 0xff, 0xc2, 0xc0, 0x70, 0xff, 0xac, 0x8e, 0x4b, 0xff, 0x4c, 0x2d, 0x22, 0xff, 0x39, 0x1e, 0x20, 0xff, 0x3c, 0x21, 0x1a, 0xff, 0xb6, 0xae, 0x66, 0xff, 0xff, 0xff, 0xa8, 0xff, 0x9f, 0x92, 0x4a, 0xff, 0x6a, 0x67, 0x32, 0xff, 0x3c, 0x40, 0x2e, 0xff, 0x01, 0x02, 0x09, 0xff, 0x1b, 0x1b, 0x20, 0xff, 0x29, 0x2a, 0x2f, 0xff, 0x28, 0x27, 0x2b, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x28, 0x26, 0xff, 0x28, 0x29, 0x24, 0xff, 0x28, 0x29, 0x28, 0xff, 0x28, 0x26, 0x2a, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x2a, 0x24, 0xff, 0x28, 0x27, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x2c, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x1e, 0x1f, 0x1d, 0xff, 0x1a, 0x1d, 0x1e, 0xff, 0x24, 0x2a, 0x2c, 0xff, 0x29, 0x27, 0x27, 0xff, 0x3e, 0x2d, 0x30, 0xff, 0x56, 0x2d, 0x29, 0xff, 0x7e, 0x92, 0xb7, 0xff, 0x3d, 0x7c, 0xaf, 0xff, 0x16, 0x08, 0x12, 0xff, 0x31, 0x26, 0x20, 0xff, 0x1e, 0x2d, 0x29, 0xff, 0x2b, 0x27, 0x2b, 0xff, 0x2d, 0x2a, 0x27, 0xff, 0x1e, 0x25, 0x16, 0xff, 0x23, 0x24, 0x1c, 0xff, 0x32, 0x2a, 0x43, 0xff, 0x24, 0x22, 0x64, 0xff, 0x6f, 0x91, 0xe1, 0xff, 0x5c, 0x85, 0xe4, 0xff, 0x5d, 0x78, 0xd3, 0xff, 0x7e, 0xb9, 0xff, 0xff, 0x64, 0xb3, 0xff, 0xff, 0x39, 0x52, 0xa4, 0xcb, 0x38, 0x1d, 0x3b, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe5, 0xd3, 0xa0, 0x46, 0xd8, 0xd8, 0xac, 0xc9, 0xcf, 0xd1, 0xb4, 0xff, 0xe0, 0xea, 0xc3, 0xff, 0xcf, 0xd8, 0x92, 0xff, 0xaa, 0xa8, 0x4b, 0xff, 0xbc, 0xaf, 0x4f, 0xff, 0xc8, 0xcb, 0x76, 0xff, 0xf1, 0xf0, 0xa0, 0xff, 0xa7, 0x8b, 0x53, 0xff, 0x2d, 0x18, 0x1c, 0xff, 0x25, 0x1d, 0x36, 0xff, 0x36, 0x21, 0x20, 0xff, 0xa0, 0x9b, 0x51, 0xff, 0xa7, 0xad, 0x53, 0xff, 0x4a, 0x38, 0x34, 0xff, 0x14, 0x02, 0x1a, 0xff, 0x18, 0x1a, 0x13, 0xff, 0x2a, 0x2e, 0x27, 0xff, 0x28, 0x28, 0x2b, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x24, 0x28, 0x27, 0xff, 0x24, 0x28, 0x28, 0xff, 0x27, 0x24, 0x26, 0xff, 0x46, 0x2e, 0x2e, 0xff, 0x2c, 0x2f, 0x38, 0xff, 0x0f, 0x1d, 0x2b, 0xff, 0x37, 0x27, 0x21, 0xff, 0x33, 0x2b, 0x22, 0xff, 0x21, 0x2b, 0x29, 0xff, 0x23, 0x28, 0x2b, 0xff, 0x2b, 0x21, 0x24, 0xff, 0x44, 0x3d, 0x5b, 0xff, 0x39, 0x3e, 0x7f, 0xff, 0x1b, 0x27, 0x5e, 0xff, 0x22, 0x30, 0x4b, 0xff, 0x2d, 0x36, 0x57, 0xff, 0x23, 0x2c, 0x4b, 0xff, 0x1a, 0x1d, 0x18, 0xff, 0x27, 0x30, 0x3a, 0xff, 0x2b, 0x41, 0x74, 0xff, 0x3f, 0x3a, 0x62, 0xff, 0x32, 0x25, 0x29, 0xff, 0x2c, 0x24, 0x2e, 0xc9, 0x2f, 0x2f, 0x20, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x1c, 0x15, 0x24, 0x9d, 0x7b, 0x59, 0xb4, 0xe7, 0xde, 0xa6, 0xff, 0xd5, 0xd9, 0xbc, 0xff, 0xf1, 0xf4, 0xef, 0xff, 0xcb, 0xdb, 0xa6, 0xff, 0xa1, 0xae, 0x44, 0xff, 0xb7, 0xac, 0x3e, 0xff, 0xc6, 0xb7, 0x4c, 0xff, 0xb8, 0xbe, 0x5b, 0xff, 0xcd, 0xcf, 0x6f, 0xff, 0x8d, 0x78, 0x2e, 0xff, 0x2e, 0x1e, 0x12, 0xff, 0x2b, 0x26, 0x30, 0xff, 0x4f, 0x46, 0x27, 0xff, 0x4e, 0x46, 0x25, 0xff, 0x15, 0x0d, 0x16, 0xff, 0x16, 0x13, 0x22, 0xff, 0x27, 0x2c, 0x29, 0xff, 0x2b, 0x26, 0x2e, 0xff, 0x2b, 0x26, 0x2e, 0xff, 0x27, 0x29, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x25, 0x28, 0x28, 0xff, 0x22, 0x29, 0x28, 0xff, 0x25, 0x28, 0x27, 0xff, 0x28, 0x24, 0x27, 0xff, 0x25, 0x20, 0x1c, 0xff, 0x32, 0x28, 0x1a, 0xff, 0x3a, 0x2b, 0x1f, 0xff, 0x2c, 0x29, 0x26, 0xff, 0x20, 0x29, 0x2e, 0xff, 0x28, 0x28, 0x29, 0xff, 0x26, 0x28, 0x23, 0xff, 0x36, 0x42, 0x4d, 0xff, 0x2f, 0x37, 0x51, 0xff, 0x1b, 0x21, 0x1e, 0xff, 0x29, 0x2d, 0x0e, 0xff, 0x24, 0x1d, 0x18, 0xff, 0x23, 0x19, 0x2b, 0xff, 0x31, 0x24, 0x24, 0xff, 0x2a, 0x22, 0x0a, 0xff, 0x18, 0x1b, 0x05, 0xff, 0x1f, 0x22, 0x16, 0xff, 0x1e, 0x28, 0x2d, 0xff, 0x21, 0x28, 0x30, 0xff, 0x32, 0x2e, 0x22, 0xff, 0x1d, 0x1d, 0x0c, 0xb5, 0x2a, 0x1c, 0x38, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x1c, 0x1c, 0x09, 0x64, 0x29, 0x27, 0x87, 0x48, 0x1f, 0x10, 0xf9, 0xa1, 0x95, 0x69, 0xff, 0xe0, 0xf3, 0xc1, 0xff, 0xd9, 0xe3, 0xc9, 0xff, 0xe8, 0xdc, 0xc0, 0xff, 0xca, 0xc1, 0x70, 0xff, 0xc5, 0xc2, 0x54, 0xff, 0xd4, 0xcb, 0x7a, 0xff, 0xd8, 0xca, 0x8a, 0xff, 0xca, 0xcc, 0x8e, 0xff, 0xeb, 0xe4, 0xa1, 0xff, 0xb0, 0x8c, 0x55, 0xff, 0x40, 0x21, 0x1e, 0xff, 0x48, 0x2d, 0x34, 0xff, 0x3e, 0x26, 0x2e, 0xff, 0x22, 0x18, 0x26, 0xff, 0x21, 0x27, 0x22, 0xff, 0x1f, 0x2f, 0x23, 0xff, 0x20, 0x28, 0x29, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x25, 0x28, 0x28, 0xff, 0x24, 0x29, 0x28, 0xff, 0x24, 0x29, 0x28, 0xff, 0x25, 0x28, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x26, 0x28, 0x27, 0xff, 0x22, 0x27, 0x2d, 0xff, 0x24, 0x27, 0x2f, 0xff, 0x2c, 0x2b, 0x24, 0xff, 0x38, 0x2a, 0x26, 0xff, 0x2c, 0x22, 0x21, 0xff, 0x21, 0x27, 0x17, 0xff, 0x2c, 0x2b, 0x22, 0xff, 0x32, 0x25, 0x29, 0xff, 0x30, 0x27, 0x28, 0xff, 0x34, 0x2c, 0x1d, 0xff, 0x2f, 0x26, 0x1a, 0xff, 0x1e, 0x23, 0x1f, 0xff, 0x25, 0x2d, 0x25, 0xff, 0x25, 0x2a, 0x29, 0xff, 0x32, 0x23, 0x2c, 0xff, 0x34, 0x25, 0x28, 0xff, 0x2a, 0x26, 0x2b, 0xff, 0x37, 0x32, 0x28, 0xff, 0x36, 0x2d, 0x27, 0xf9, 0x44, 0x20, 0x7a, 0x87, 0x55, 0x38, 0xc6, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, 0x27, 0x1b, 0x40, 0x5f, 0x36, 0x32, 0xda, 0x73, 0x3d, 0x3e, 0xff, 0x52, 0x2e, 0x28, 0xff, 0xad, 0xa7, 0x8b, 0xff, 0xe9, 0xf9, 0xe0, 0xff, 0xcf, 0xda, 0xc8, 0xff, 0xdf, 0xda, 0xb5, 0xff, 0xda, 0xd1, 0x96, 0xff, 0xd4, 0xd0, 0x8d, 0xff, 0xd0, 0xcd, 0x95, 0xff, 0xd3, 0xcc, 0x91, 0xff, 0xca, 0xd1, 0x94, 0xff, 0xe3, 0xe3, 0x9d, 0xff, 0xa6, 0x8a, 0x4d, 0xff, 0x39, 0x25, 0x1a, 0xff, 0x34, 0x26, 0x1f, 0xff, 0x2d, 0x23, 0x26, 0xff, 0x25, 0x21, 0x38, 0xff, 0x24, 0x2b, 0x24, 0xff, 0x24, 0x31, 0x19, 0xff, 0x26, 0x26, 0x2d, 0xff, 0x29, 0x28, 0x26, 0xff, 0x29, 0x2b, 0x22, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x29, 0x26, 0xff, 0x24, 0x25, 0x2e, 0xff, 0x21, 0x26, 0x2f, 0xff, 0x1f, 0x2a, 0x28, 0xff, 0x25, 0x28, 0x2b, 0xff, 0x2c, 0x26, 0x2d, 0xff, 0x2d, 0x2c, 0x23, 0xff, 0x2b, 0x26, 0x2c, 0xff, 0x2b, 0x24, 0x37, 0xff, 0x21, 0x25, 0x20, 0xff, 0x1b, 0x17, 0x0f, 0xff, 0x21, 0x18, 0x15, 0xff, 0x16, 0x27, 0x14, 0xff, 0x1a, 0x34, 0x2e, 0xff, 0x20, 0x2e, 0x56, 0xff, 0x23, 0x1a, 0x42, 0xff, 0x3b, 0x1c, 0x20, 0xff, 0x23, 0x18, 0x0f, 0xff, 0x1b, 0x21, 0x10, 0xff, 0x19, 0x15, 0x14, 0xff, 0x70, 0x63, 0x75, 0xff, 0xe8, 0xdc, 0xce, 0xff, 0xdb, 0xc8, 0xc8, 0xff, 0xb2, 0x8d, 0xcd, 0xff, 0x6b, 0x39, 0xbf, 0xda, 0x62, 0x36, 0xb4, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x24, 0x24, 0x07, 0x37, 0x13, 0x1c, 0x8e, 0x73, 0x58, 0x55, 0xfe, 0xe1, 0xce, 0xcb, 0xff, 0xd5, 0xc4, 0xc9, 0xff, 0xc6, 0xb6, 0xbe, 0xff, 0xe4, 0xd9, 0xd4, 0xff, 0xf5, 0xee, 0xf0, 0xff, 0xe8, 0xec, 0xec, 0xff, 0xe4, 0xf3, 0xe7, 0xff, 0xd3, 0xe3, 0xd8, 0xff, 0xda, 0xe1, 0xc7, 0xff, 0xdb, 0xdc, 0xa0, 0xff, 0xd5, 0xcd, 0x87, 0xff, 0xde, 0xe2, 0xa8, 0xff, 0xef, 0xf5, 0xb5, 0xff, 0x6c, 0x60, 0x2f, 0xff, 0x16, 0x15, 0x17, 0xff, 0x19, 0x24, 0x32, 0xff, 0x21, 0x32, 0x17, 0xff, 0x24, 0x29, 0x28, 0xff, 0x29, 0x20, 0x37, 0xff, 0x2d, 0x29, 0x24, 0xff, 0x2a, 0x25, 0x2d, 0xff, 0x27, 0x26, 0x2d, 0xff, 0x27, 0x29, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2b, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x29, 0x26, 0x2b, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x27, 0x2c, 0x20, 0xff, 0x2d, 0x2a, 0x24, 0xff, 0x31, 0x27, 0x2c, 0xff, 0x24, 0x28, 0x2f, 0xff, 0x28, 0x2e, 0x1c, 0xff, 0x1c, 0x1e, 0x19, 0xff, 0x2e, 0x40, 0x7b, 0xff, 0x50, 0x6d, 0xb4, 0xff, 0x2d, 0x43, 0x71, 0xff, 0x2a, 0x2a, 0x47, 0xff, 0x25, 0x1f, 0x37, 0xff, 0x28, 0x3e, 0x79, 0xff, 0x4b, 0x6f, 0xc1, 0xff, 0x28, 0x45, 0x75, 0xff, 0x53, 0x7f, 0xa6, 0xff, 0x24, 0x31, 0x42, 0xff, 0x15, 0x05, 0x00, 0xff, 0x4d, 0x47, 0x42, 0xff, 0xcc, 0xb2, 0xc3, 0xff, 0xe3, 0xc0, 0xd0, 0xff, 0xa9, 0x93, 0x8c, 0xff, 0x5e, 0x34, 0x8a, 0xff, 0x72, 0x26, 0xe4, 0xfe, 0x96, 0x56, 0xdc, 0x8e, 0x48, 0x48, 0x48, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x1f, 0x1f, 0x29, 0x2b, 0x24, 0x23, 0xd1, 0x24, 0x1a, 0x1a, 0xff, 0x5a, 0x48, 0x43, 0xff, 0xe0, 0xbb, 0xb2, 0xff, 0xe3, 0xb8, 0xb7, 0xff, 0xbd, 0xa4, 0xa4, 0xff, 0xd5, 0xd8, 0xc7, 0xff, 0xf4, 0xf9, 0xf6, 0xff, 0xe7, 0xec, 0xf6, 0xff, 0xe7, 0xf2, 0xf5, 0xff, 0xdc, 0xe0, 0xd2, 0xff, 0xdb, 0xd6, 0xad, 0xff, 0xd6, 0xd3, 0x97, 0xff, 0xde, 0xda, 0x94, 0xff, 0xf9, 0xf3, 0xbf, 0xff, 0x7a, 0x77, 0x66, 0xff, 0x0c, 0x0c, 0x0c, 0xff, 0x22, 0x24, 0x2a, 0xff, 0x25, 0x26, 0x2f, 0xff, 0x27, 0x29, 0x26, 0xff, 0x28, 0x2a, 0x26, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x28, 0x27, 0x2b, 0xff, 0x28, 0x27, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x29, 0x29, 0x26, 0xff, 0x29, 0x28, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x29, 0x28, 0x29, 0xff, 0x2c, 0x1f, 0x1b, 0xff, 0x28, 0x2b, 0x37, 0xff, 0x5d, 0x90, 0xd2, 0xff, 0x6e, 0xb7, 0xff, 0xff, 0x4b, 0x65, 0xc2, 0xff, 0x26, 0x18, 0x40, 0xff, 0x3b, 0x5c, 0xb4, 0xff, 0x72, 0xbd, 0xff, 0xff, 0x1c, 0x43, 0x6b, 0xff, 0x44, 0x6e, 0xb1, 0xff, 0x87, 0xb1, 0xe5, 0xff, 0x42, 0x25, 0x2a, 0xff, 0x11, 0x13, 0x06, 0xff, 0x24, 0x2d, 0x25, 0xff, 0x6f, 0x42, 0x4e, 0xff, 0x61, 0x43, 0x22, 0xff, 0x40, 0x2e, 0x46, 0xff, 0x6e, 0x2a, 0xcf, 0xff, 0xaf, 0x5a, 0xff, 0xff, 0x6c, 0x4e, 0x72, 0xd1, 0x12, 0x1f, 0x00, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x82, 0x73, 0x73, 0x56, 0x10, 0x12, 0x14, 0xf3, 0x21, 0x29, 0x2a, 0xff, 0x1b, 0x2b, 0x29, 0xff, 0x23, 0x23, 0x1a, 0xff, 0x73, 0x4e, 0x44, 0xff, 0x98, 0x60, 0x5b, 0xff, 0x6f, 0x46, 0x3b, 0xff, 0xb8, 0xa6, 0x8b, 0xff, 0xf7, 0xfa, 0xe0, 0xff, 0xd9, 0xe5, 0xd6, 0xff, 0xde, 0xe6, 0xd5, 0xff, 0xdd, 0xdc, 0xb4, 0xff, 0xd1, 0xcc, 0x8c, 0xff, 0xd8, 0xd9, 0x8e, 0xff, 0xe8, 0xe8, 0xaa, 0xff, 0x6c, 0x68, 0x48, 0xff, 0x07, 0x05, 0x02, 0xff, 0x21, 0x23, 0x29, 0xff, 0x27, 0x2a, 0x29, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x26, 0xff, 0x22, 0x25, 0x30, 0xff, 0x2b, 0x2b, 0x2d, 0xff, 0x29, 0x23, 0x07, 0xff, 0x0e, 0x16, 0x18, 0xff, 0x2a, 0x49, 0x7e, 0xff, 0x41, 0x5e, 0xa9, 0xff, 0x23, 0x29, 0x61, 0xff, 0x45, 0x62, 0xb7, 0xff, 0x49, 0x74, 0xe8, 0xff, 0x2e, 0x28, 0x63, 0xff, 0x09, 0x08, 0x31, 0xff, 0x7c, 0xc1, 0xef, 0xff, 0x82, 0x96, 0xa4, 0xff, 0x0c, 0x08, 0x04, 0xff, 0x1c, 0x24, 0x16, 0xff, 0x3c, 0x1e, 0x26, 0xff, 0x5e, 0x3b, 0x21, 0xff, 0x3d, 0x27, 0x21, 0xff, 0x58, 0x29, 0x99, 0xff, 0x89, 0x3e, 0xf4, 0xff, 0x77, 0x37, 0xc3, 0xff, 0x28, 0x23, 0x23, 0xf3, 0x1a, 0x29, 0x11, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0x55, 0x55, 0x03, 0x81, 0x42, 0x40, 0x8a, 0x53, 0x28, 0x2a, 0xfe, 0x33, 0x25, 0x28, 0xff, 0x2a, 0x2b, 0x2f, 0xff, 0x20, 0x29, 0x2b, 0xff, 0x21, 0x1e, 0x17, 0xff, 0x5e, 0x3f, 0x31, 0xff, 0x89, 0x55, 0x4b, 0xff, 0x63, 0x32, 0x22, 0xff, 0xac, 0x85, 0x65, 0xff, 0xf6, 0xee, 0xc3, 0xff, 0xd5, 0xe1, 0xb9, 0xff, 0xd7, 0xe0, 0xbb, 0xff, 0xd9, 0xdb, 0xa8, 0xff, 0xd4, 0xd0, 0x8d, 0xff, 0xeb, 0xec, 0x9e, 0xff, 0x6f, 0x71, 0x47, 0xff, 0x06, 0x05, 0x01, 0xff, 0x24, 0x23, 0x29, 0xff, 0x26, 0x27, 0x2e, 0xff, 0x27, 0x29, 0x28, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x24, 0x27, 0x2b, 0xff, 0x1e, 0x26, 0x2c, 0xff, 0x2d, 0x2d, 0x23, 0xff, 0x36, 0x2b, 0x10, 0xff, 0x1f, 0x14, 0x06, 0xff, 0x1e, 0x23, 0x47, 0xff, 0x21, 0x29, 0x46, 0xff, 0x1b, 0x26, 0x22, 0xff, 0x22, 0x37, 0x8b, 0xff, 0x43, 0x59, 0xd0, 0xff, 0x29, 0x39, 0x89, 0xff, 0x58, 0x87, 0xdc, 0xff, 0xa5, 0xd0, 0xe3, 0xff, 0x1f, 0x23, 0x23, 0xff, 0x29, 0x1f, 0x1d, 0xff, 0x33, 0x2b, 0x34, 0xff, 0x4b, 0x2e, 0x1c, 0xff, 0x37, 0x15, 0x00, 0xff, 0x37, 0x25, 0x40, 0xff, 0x76, 0x3a, 0xd6, 0xff, 0x90, 0x30, 0xff, 0xff, 0x56, 0x29, 0x77, 0xff, 0x1e, 0x24, 0x05, 0xfe, 0x26, 0x2f, 0x17, 0x8b, 0x55, 0x00, 0x55, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2e, 0x2e, 0x2e, 0x0b, 0x4d, 0x36, 0x30, 0xae, 0x5f, 0x3f, 0x3b, 0xff, 0x42, 0x2d, 0x28, 0xff, 0x2f, 0x2d, 0x25, 0xff, 0x23, 0x2b, 0x23, 0xff, 0x37, 0x27, 0x28, 0xff, 0x59, 0x36, 0x32, 0xff, 0x74, 0x4c, 0x3d, 0xff, 0x84, 0x55, 0x46, 0xff, 0x67, 0x3d, 0x29, 0xff, 0x92, 0x71, 0x4f, 0xff, 0xf0, 0xe5, 0xba, 0xff, 0xdd, 0xe2, 0xba, 0xff, 0xd4, 0xdb, 0xb9, 0xff, 0xe3, 0xe2, 0xba, 0xff, 0xf7, 0xf1, 0xc2, 0xff, 0x81, 0x7d, 0x4e, 0xff, 0x08, 0x0b, 0x00, 0xff, 0x21, 0x23, 0x2a, 0xff, 0x27, 0x25, 0x31, 0xff, 0x26, 0x27, 0x2b, 0xff, 0x28, 0x29, 0x25, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x29, 0xff, 0x2e, 0x2a, 0x23, 0xff, 0x26, 0x29, 0x26, 0xff, 0x15, 0x25, 0x33, 0xff, 0x24, 0x29, 0x27, 0xff, 0x38, 0x2c, 0x1b, 0xff, 0x28, 0x22, 0x22, 0xff, 0x2f, 0x25, 0x29, 0xff, 0x21, 0x1e, 0x09, 0xff, 0x29, 0x51, 0x8c, 0xff, 0x52, 0x87, 0xef, 0xff, 0x21, 0x37, 0x78, 0xff, 0x29, 0x56, 0xc6, 0xff, 0xaf, 0xe9, 0xff, 0xff, 0x51, 0x5c, 0x58, 0xff, 0x20, 0x10, 0x13, 0xff, 0x18, 0x1b, 0x23, 0xff, 0x3f, 0x31, 0x30, 0xff, 0x84, 0x64, 0x55, 0xff, 0x33, 0x2e, 0x10, 0xff, 0x54, 0x2c, 0x85, 0xff, 0x93, 0x35, 0xff, 0xff, 0x7c, 0x39, 0xcc, 0xff, 0x2b, 0x2b, 0x26, 0xff, 0x21, 0x27, 0x1a, 0xff, 0x27, 0x27, 0x27, 0xaf, 0x2e, 0x2e, 0x45, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x22, 0x17, 0x16, 0x1e, 0x20, 0x1f, 0xc9, 0x17, 0x17, 0x1b, 0xff, 0x17, 0x12, 0x13, 0xff, 0x0e, 0x10, 0x12, 0xff, 0x0e, 0x12, 0x1a, 0xff, 0x18, 0x16, 0x28, 0xff, 0x59, 0x32, 0x38, 0xff, 0x8c, 0x59, 0x4b, 0xff, 0x86, 0x58, 0x47, 0xff, 0x82, 0x53, 0x46, 0xff, 0x75, 0x4b, 0x3b, 0xff, 0x7a, 0x56, 0x3c, 0xff, 0xd7, 0xcb, 0xa6, 0xff, 0xe3, 0xe7, 0xc8, 0xff, 0xd3, 0xd6, 0xc0, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xa0, 0x9c, 0x88, 0xff, 0x08, 0x04, 0x00, 0xff, 0x20, 0x23, 0x1e, 0xff, 0x27, 0x29, 0x2c, 0xff, 0x28, 0x26, 0x2d, 0xff, 0x2a, 0x27, 0x28, 0xff, 0x28, 0x2a, 0x24, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x2e, 0x2a, 0x23, 0xff, 0x2c, 0x2a, 0x23, 0xff, 0x1b, 0x26, 0x32, 0xff, 0x19, 0x25, 0x34, 0xff, 0x24, 0x28, 0x2b, 0xff, 0x2f, 0x2c, 0x1f, 0xff, 0x32, 0x25, 0x26, 0xff, 0x2e, 0x1b, 0x1f, 0xff, 0x26, 0x36, 0x4c, 0xff, 0x47, 0x87, 0xe4, 0xff, 0x43, 0x87, 0xf1, 0xff, 0x1c, 0x44, 0x8a, 0xff, 0x79, 0x8f, 0xa1, 0xff, 0x5e, 0x79, 0x87, 0xff, 0x0d, 0x05, 0x00, 0xff, 0x2b, 0x27, 0x2c, 0xff, 0xa6, 0xb0, 0xc5, 0xff, 0xff, 0xff, 0xff, 0xff, 0x88, 0x70, 0x64, 0xff, 0x2c, 0x13, 0x1c, 0xff, 0x80, 0x3c, 0xcc, 0xff, 0x7d, 0x41, 0xed, 0xff, 0x35, 0x2e, 0x63, 0xff, 0x19, 0x21, 0x18, 0xff, 0x28, 0x29, 0x25, 0xff, 0x2b, 0x2c, 0x30, 0xc9, 0x2e, 0x2e, 0x39, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd7, 0xcd, 0x9c, 0x1a, 0x91, 0x7f, 0x46, 0xd6, 0x82, 0x6c, 0x2a, 0xff, 0xa6, 0x90, 0x50, 0xff, 0x9e, 0x90, 0x57, 0xff, 0x85, 0x80, 0x50, 0xff, 0x5d, 0x51, 0x34, 0xff, 0x51, 0x31, 0x24, 0xff, 0x67, 0x37, 0x2e, 0xff, 0x69, 0x3c, 0x29, 0xff, 0x77, 0x4b, 0x35, 0xff, 0x83, 0x52, 0x48, 0xff, 0x85, 0x51, 0x4a, 0xff, 0x73, 0x3c, 0x2f, 0xff, 0xb5, 0xa4, 0x86, 0xff, 0xfb, 0xff, 0xe7, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xd2, 0xd3, 0xc7, 0xff, 0x1a, 0x18, 0x11, 0xff, 0x16, 0x14, 0x1f, 0xff, 0x29, 0x2a, 0x29, 0xff, 0x2b, 0x2a, 0x24, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x2a, 0x29, 0x26, 0xff, 0x29, 0x2a, 0x24, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x24, 0x27, 0x2b, 0xff, 0x29, 0x28, 0x28, 0xff, 0x34, 0x2a, 0x20, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x1f, 0x26, 0x30, 0xff, 0x26, 0x28, 0x29, 0xff, 0x20, 0x2b, 0x24, 0xff, 0x25, 0x2a, 0x26, 0xff, 0x20, 0x13, 0x07, 0xff, 0x37, 0x49, 0x8b, 0xff, 0x6a, 0xa4, 0xff, 0xff, 0x1e, 0x4a, 0xa1, 0xff, 0x69, 0x71, 0x88, 0xff, 0x6f, 0x93, 0x9b, 0xff, 0x02, 0x01, 0x00, 0xff, 0x53, 0x47, 0x49, 0xff, 0xcf, 0xe7, 0xff, 0xff, 0xd1, 0xd3, 0xef, 0xff, 0xa2, 0x76, 0x82, 0xff, 0x2b, 0x13, 0x00, 0xff, 0x61, 0x37, 0x8a, 0xff, 0x5f, 0x2e, 0xe7, 0xff, 0x2a, 0x25, 0x76, 0xff, 0x1f, 0x2a, 0x1f, 0xff, 0x27, 0x29, 0x1b, 0xff, 0x27, 0x27, 0x2c, 0xff, 0x2f, 0x2e, 0x39, 0xd6, 0x31, 0x27, 0x44, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf7, 0xef, 0xbf, 0x20, 0xff, 0xf3, 0xbd, 0xde, 0xbd, 0xac, 0x63, 0xff, 0x81, 0x69, 0x04, 0xff, 0xc4, 0xb1, 0x52, 0xff, 0xff, 0xfb, 0xb9, 0xff, 0xf6, 0xfa, 0xc8, 0xff, 0xe7, 0xe8, 0xb7, 0xff, 0xd6, 0xd1, 0x96, 0xff, 0xc6, 0xb2, 0x7f, 0xff, 0xa6, 0x88, 0x62, 0xff, 0x7b, 0x5a, 0x3b, 0xff, 0x67, 0x3f, 0x2b, 0xff, 0x7b, 0x4c, 0x3f, 0xff, 0x7c, 0x47, 0x3d, 0xff, 0x82, 0x5d, 0x46, 0xff, 0xf6, 0xed, 0xd5, 0xff, 0xfb, 0xff, 0xf9, 0xff, 0x2a, 0x32, 0x2f, 0xff, 0x0b, 0x08, 0x0b, 0xff, 0x33, 0x29, 0x2e, 0xff, 0x2b, 0x28, 0x29, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2d, 0x29, 0x25, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x27, 0x28, 0x29, 0xff, 0x29, 0x27, 0x29, 0xff, 0x1f, 0x2a, 0x28, 0xff, 0x1a, 0x2a, 0x29, 0xff, 0x37, 0x26, 0x17, 0xff, 0x2f, 0x1b, 0x22, 0xff, 0x36, 0x5e, 0xb8, 0xff, 0x22, 0x63, 0xe1, 0xff, 0x8d, 0xce, 0xff, 0xff, 0x9b, 0xbe, 0xba, 0xff, 0x1d, 0x0d, 0x0f, 0xff, 0x22, 0x26, 0x2a, 0xff, 0x52, 0x53, 0x6d, 0xff, 0x3a, 0x38, 0x3e, 0xff, 0x36, 0x22, 0x19, 0xff, 0x3f, 0x1f, 0x0b, 0xff, 0x3d, 0x28, 0x31, 0xff, 0x5e, 0x32, 0xcb, 0xff, 0x4d, 0x2d, 0xc2, 0xff, 0x3f, 0x30, 0x6e, 0xff, 0x22, 0x22, 0x15, 0xff, 0x28, 0x27, 0x24, 0xff, 0x2c, 0x31, 0x26, 0xff, 0x32, 0x23, 0x5a, 0xde, 0x2f, 0x27, 0x47, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd1, 0xd1, 0x91, 0x1c, 0xcf, 0xce, 0x9a, 0xdb, 0xde, 0xd9, 0x9c, 0xff, 0xcf, 0xcb, 0x76, 0xff, 0xbd, 0xaa, 0x53, 0xff, 0xda, 0xc8, 0x83, 0xff, 0xde, 0xda, 0xae, 0xff, 0xd1, 0xd3, 0xb7, 0xff, 0xdb, 0xdc, 0xbb, 0xff, 0xed, 0xeb, 0xc1, 0xff, 0xf3, 0xf3, 0xc4, 0xff, 0xf5, 0xf1, 0xc7, 0xff, 0xe5, 0xd5, 0xb4, 0xff, 0xac, 0x8f, 0x76, 0xff, 0x72, 0x47, 0x34, 0xff, 0x76, 0x43, 0x32, 0xff, 0x83, 0x4b, 0x39, 0xff, 0xa3, 0x7d, 0x6e, 0xff, 0x59, 0x51, 0x49, 0xff, 0x09, 0x0a, 0x0c, 0xff, 0x2a, 0x29, 0x2e, 0xff, 0x27, 0x28, 0x27, 0xff, 0x28, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x29, 0x28, 0x27, 0xff, 0x27, 0x29, 0x27, 0xff, 0x2d, 0x26, 0x29, 0xff, 0x28, 0x25, 0x2f, 0xff, 0x22, 0x2a, 0x2c, 0xff, 0x1c, 0x1e, 0x0e, 0xff, 0x25, 0x33, 0x3e, 0xff, 0x4e, 0x80, 0xe1, 0xff, 0x73, 0xbf, 0xff, 0xff, 0x7c, 0x95, 0xc6, 0xff, 0x21, 0x10, 0x12, 0xff, 0x24, 0x33, 0x3a, 0xff, 0x0d, 0x23, 0x49, 0xff, 0x1d, 0x28, 0x61, 0xff, 0x34, 0x37, 0x52, 0xff, 0xb0, 0x89, 0x8a, 0xff, 0x61, 0x4b, 0x36, 0xff, 0x54, 0x27, 0x7d, 0xff, 0x8f, 0x4e, 0xff, 0xff, 0x88, 0x46, 0xf0, 0xff, 0x39, 0x26, 0x45, 0xff, 0x1d, 0x22, 0x15, 0xff, 0x28, 0x2a, 0x24, 0xff, 0x31, 0x2e, 0x3e, 0xff, 0x33, 0x2a, 0x53, 0xdc, 0x2d, 0x2d, 0x48, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc2, 0xc2, 0x79, 0x15, 0xc4, 0xc5, 0x7a, 0xd6, 0xc4, 0xc4, 0x85, 0xff, 0xc8, 0xc6, 0x87, 0xff, 0xdf, 0xdf, 0x91, 0xff, 0xc8, 0xc0, 0x63, 0xff, 0xac, 0xa1, 0x4c, 0xff, 0xd3, 0xd1, 0x8f, 0xff, 0xcf, 0xd0, 0x9a, 0xff, 0xd0, 0xd0, 0x9c, 0xff, 0xd9, 0xd4, 0x9e, 0xff, 0xd5, 0xdb, 0xb3, 0xff, 0xdd, 0xe7, 0xc7, 0xff, 0xfb, 0xf8, 0xd6, 0xff, 0xf2, 0xe4, 0xc3, 0xff, 0xde, 0xc4, 0xa8, 0xff, 0x97, 0x75, 0x5c, 0xff, 0x81, 0x4f, 0x42, 0xff, 0x5e, 0x38, 0x31, 0xff, 0x14, 0x0b, 0x06, 0xff, 0x2b, 0x2a, 0x2b, 0xff, 0x26, 0x26, 0x29, 0xff, 0x1f, 0x29, 0x28, 0xff, 0x24, 0x29, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x30, 0x27, 0x28, 0xff, 0x33, 0x25, 0x29, 0xff, 0x16, 0x27, 0x33, 0xff, 0x1e, 0x2d, 0x28, 0xff, 0x28, 0x14, 0x00, 0xff, 0x31, 0x45, 0x6e, 0xff, 0xa5, 0xe8, 0xff, 0xff, 0x66, 0x96, 0xb6, 0xff, 0x12, 0x0a, 0x00, 0xff, 0x35, 0x43, 0x4a, 0xff, 0x2c, 0x57, 0xba, 0xff, 0x30, 0x42, 0xbb, 0xff, 0x75, 0x77, 0xea, 0xff, 0xeb, 0xd3, 0xde, 0xff, 0x6f, 0x5b, 0x40, 0xff, 0x39, 0x18, 0x1d, 0xff, 0x8d, 0x45, 0xdd, 0xff, 0xad, 0x52, 0xff, 0xff, 0x6d, 0x3d, 0xb3, 0xff, 0x18, 0x1f, 0x10, 0xff, 0x25, 0x27, 0x23, 0xff, 0x28, 0x2f, 0x21, 0xff, 0x34, 0x2e, 0x4a, 0xff, 0x31, 0x2e, 0x49, 0xd7, 0x30, 0x30, 0x3c, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb2, 0xb2, 0x66, 0x0a, 0xee, 0xea, 0x96, 0xc8, 0xda, 0xda, 0x8a, 0xff, 0xd3, 0xd2, 0x91, 0xff, 0xd4, 0xd0, 0x9c, 0xff, 0xdc, 0xd7, 0xa8, 0xff, 0xbd, 0xb9, 0x6e, 0xff, 0x9b, 0x98, 0x44, 0xff, 0xd6, 0xd6, 0x8f, 0xff, 0xd9, 0xdb, 0x9b, 0xff, 0xd6, 0xd5, 0x9c, 0xff, 0xd6, 0xd3, 0x9a, 0xff, 0xd6, 0xd2, 0xac, 0xff, 0xdf, 0xde, 0xbd, 0xff, 0xe2, 0xe4, 0xb7, 0xff, 0xc8, 0xc9, 0x9c, 0xff, 0xf0, 0xef, 0xcb, 0xff, 0xff, 0xff, 0xe9, 0xff, 0x98, 0x8c, 0x80, 0xff, 0x12, 0x0e, 0x09, 0xff, 0x1f, 0x28, 0x22, 0xff, 0x25, 0x29, 0x28, 0xff, 0x2b, 0x28, 0x29, 0xff, 0x27, 0x27, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x25, 0x29, 0x27, 0xff, 0x30, 0x2c, 0x1d, 0xff, 0x2e, 0x27, 0x29, 0xff, 0x20, 0x22, 0x37, 0xff, 0x33, 0x26, 0x1b, 0xff, 0x05, 0x0a, 0x08, 0xff, 0x9d, 0xca, 0xd9, 0xff, 0x99, 0xd3, 0xd5, 0xff, 0x0d, 0x0b, 0x00, 0xff, 0x2d, 0x2d, 0x4f, 0xff, 0x4c, 0x7c, 0xf5, 0xff, 0x12, 0x26, 0x7d, 0xff, 0x67, 0x64, 0xd8, 0xff, 0xac, 0xaf, 0xc7, 0xff, 0x2f, 0x2c, 0x11, 0xff, 0x33, 0x1e, 0x0e, 0xff, 0x56, 0x31, 0x6d, 0xff, 0x88, 0x46, 0xfe, 0xff, 0x7a, 0x3b, 0xf8, 0xff, 0x32, 0x2e, 0x3c, 0xff, 0x1f, 0x24, 0x14, 0xff, 0x26, 0x25, 0x2b, 0xff, 0x2b, 0x2f, 0x2a, 0xff, 0x2d, 0x27, 0x3f, 0xff, 0x2c, 0x27, 0x3a, 0xc8, 0x33, 0x33, 0x33, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xaa, 0xff, 0x03, 0x98, 0x7b, 0x92, 0xb1, 0xbe, 0xac, 0x8d, 0xff, 0xee, 0xe8, 0xae, 0xff, 0xe2, 0xe1, 0xa1, 0xff, 0xde, 0xda, 0xab, 0xff, 0xdb, 0xd2, 0xb1, 0xff, 0xe1, 0xdb, 0xb1, 0xff, 0xe6, 0xe1, 0xb3, 0xff, 0xe2, 0xde, 0xb2, 0xff, 0xe0, 0xdb, 0xb0, 0xff, 0xdb, 0xd5, 0xac, 0xff, 0xd8, 0xd1, 0xae, 0xff, 0xdb, 0xcf, 0xa9, 0xff, 0xdf, 0xd6, 0xa4, 0xff, 0xcf, 0xcf, 0x96, 0xff, 0xcb, 0xd1, 0x9a, 0xff, 0xe9, 0xef, 0xc7, 0xff, 0xff, 0xff, 0xf1, 0xff, 0x65, 0x67, 0x64, 0xff, 0x01, 0x0d, 0x0d, 0xff, 0x1d, 0x2b, 0x29, 0xff, 0x26, 0x28, 0x28, 0xff, 0x2f, 0x27, 0x28, 0xff, 0x2b, 0x27, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x24, 0x29, 0x27, 0xff, 0x27, 0x2b, 0x23, 0xff, 0x33, 0x27, 0x25, 0xff, 0x2c, 0x21, 0x32, 0xff, 0x24, 0x26, 0x36, 0xff, 0x13, 0x16, 0x07, 0xff, 0x45, 0x57, 0x4e, 0xff, 0x83, 0xa1, 0xb6, 0xff, 0x16, 0x0e, 0x00, 0xff, 0x2c, 0x28, 0x60, 0xff, 0x6a, 0xb1, 0xff, 0xff, 0x34, 0x59, 0x89, 0xff, 0x19, 0x1f, 0x54, 0xff, 0x3f, 0x46, 0x99, 0xff, 0x1f, 0x1c, 0x1d, 0xff, 0x33, 0x28, 0x23, 0xff, 0x26, 0x27, 0x16, 0xff, 0x55, 0x37, 0x9a, 0xff, 0x74, 0x33, 0xfc, 0xff, 0x53, 0x32, 0x93, 0xff, 0x1e, 0x23, 0x17, 0xff, 0x26, 0x24, 0x2c, 0xff, 0x29, 0x2b, 0x23, 0xff, 0x2f, 0x2b, 0x34, 0xff, 0x2d, 0x2a, 0x37, 0xff, 0x28, 0x2c, 0x2c, 0xb1, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8b, 0x4f, 0xc8, 0x8d, 0xca, 0x97, 0xf9, 0xff, 0xa3, 0x7c, 0xae, 0xff, 0xb5, 0xaa, 0x8b, 0xff, 0xe2, 0xe4, 0xa0, 0xff, 0xdc, 0xdc, 0xa5, 0xff, 0xd8, 0xd5, 0xaf, 0xff, 0xd9, 0xd8, 0xb6, 0xff, 0xda, 0xdc, 0xaf, 0xff, 0xd8, 0xd8, 0xa1, 0xff, 0xdd, 0xdb, 0xa2, 0xff, 0xe0, 0xdf, 0xab, 0xff, 0xd9, 0xd7, 0xad, 0xff, 0xd7, 0xd4, 0xa2, 0xff, 0xda, 0xd8, 0x9a, 0xff, 0xcb, 0xcd, 0x8b, 0xff, 0xc8, 0xcc, 0x8f, 0xff, 0xff, 0xff, 0xdd, 0xff, 0x9d, 0x95, 0x82, 0xff, 0x09, 0x00, 0x00, 0xff, 0x28, 0x25, 0x2d, 0xff, 0x25, 0x28, 0x29, 0xff, 0x2c, 0x28, 0x28, 0xff, 0x2f, 0x27, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x25, 0x28, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2d, 0x28, 0x26, 0xff, 0x22, 0x24, 0x31, 0xff, 0x1f, 0x27, 0x2e, 0xff, 0x34, 0x2c, 0x1a, 0xff, 0x26, 0x28, 0x28, 0xff, 0x27, 0x2a, 0x2e, 0xff, 0x19, 0x11, 0x1e, 0xff, 0x3b, 0x3a, 0x37, 0xff, 0x41, 0x36, 0x1d, 0xff, 0x36, 0x47, 0x6d, 0xff, 0x73, 0xcb, 0xff, 0xff, 0x61, 0x8d, 0xe6, 0xff, 0x10, 0x17, 0x38, 0xff, 0x2a, 0x28, 0x8f, 0xff, 0x3b, 0x28, 0x63, 0xff, 0x29, 0x26, 0x10, 0xff, 0x21, 0x28, 0x14, 0xff, 0x3e, 0x2c, 0x5b, 0xff, 0x73, 0x34, 0xe8, 0xff, 0x6d, 0x30, 0xe9, 0xff, 0x34, 0x2e, 0x42, 0xff, 0x1e, 0x27, 0x0c, 0xff, 0x2b, 0x26, 0x32, 0xff, 0x31, 0x30, 0x2c, 0xff, 0x26, 0x20, 0x31, 0xff, 0x22, 0x1e, 0x31, 0xff, 0x27, 0x22, 0x32, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb7, 0x81, 0xf3, 0x55, 0x9e, 0x67, 0xce, 0xfe, 0x88, 0x3a, 0xbc, 0xff, 0xbd, 0x84, 0xed, 0xff, 0xcc, 0xb5, 0xe2, 0xff, 0xd9, 0xdb, 0xaa, 0xff, 0xdd, 0xe4, 0x9d, 0xff, 0xd8, 0xde, 0xca, 0xff, 0xef, 0xf6, 0xff, 0xff, 0xdc, 0xd7, 0xa3, 0xff, 0x8a, 0x78, 0x03, 0xff, 0xac, 0xa3, 0x41, 0xff, 0xe1, 0xe2, 0xb4, 0xff, 0xe2, 0xe0, 0xc3, 0xff, 0xde, 0xe6, 0xbd, 0xff, 0xda, 0xe1, 0xbd, 0xff, 0xe1, 0xdd, 0xbe, 0xff, 0xef, 0xee, 0xc2, 0xff, 0xd3, 0xd4, 0xac, 0xff, 0x2a, 0x27, 0x17, 0xff, 0x1c, 0x17, 0x1a, 0xff, 0x2b, 0x28, 0x2b, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x27, 0x27, 0x2b, 0xff, 0x24, 0x28, 0x2a, 0xff, 0x2b, 0x2a, 0x23, 0xff, 0x29, 0x29, 0x26, 0xff, 0x28, 0x27, 0x2b, 0xff, 0x24, 0x22, 0x29, 0xff, 0x2b, 0x2a, 0x11, 0xff, 0x3b, 0x28, 0x18, 0xff, 0x56, 0x84, 0xaf, 0xff, 0x8d, 0xee, 0xff, 0xff, 0x98, 0xcd, 0xff, 0xff, 0x32, 0x3c, 0x97, 0xff, 0x47, 0x5c, 0xbc, 0xff, 0x6a, 0x87, 0xdc, 0xff, 0x30, 0x21, 0x27, 0xff, 0x47, 0x2a, 0x23, 0xff, 0x38, 0x22, 0x1d, 0xff, 0x5d, 0x36, 0xa5, 0xff, 0x81, 0x34, 0xff, 0xff, 0x62, 0x2a, 0xa2, 0xff, 0x1d, 0x1d, 0x0c, 0xff, 0x1d, 0x29, 0x14, 0xff, 0x34, 0x2e, 0x30, 0xff, 0x6c, 0x32, 0xa7, 0xff, 0x72, 0x36, 0xae, 0xff, 0x76, 0x3c, 0xb0, 0xfe, 0x7e, 0x3f, 0xb7, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd3, 0xa1, 0xfe, 0x29, 0xe1, 0xc7, 0xfb, 0xf3, 0xd9, 0xa3, 0xff, 0xff, 0x85, 0x37, 0xc3, 0xff, 0x6b, 0x31, 0xad, 0xff, 0xc5, 0x9c, 0xf9, 0xff, 0xec, 0xcb, 0xee, 0xff, 0xe4, 0xdf, 0xa6, 0xff, 0xe7, 0xe7, 0xbd, 0xff, 0xed, 0xf5, 0xf0, 0xff, 0xdb, 0xd0, 0xaa, 0xff, 0x9b, 0x7e, 0x22, 0xff, 0xc0, 0xb1, 0x5c, 0xff, 0xde, 0xe0, 0xaf, 0xff, 0xd9, 0xda, 0xb8, 0xff, 0xdb, 0xe1, 0xba, 0xff, 0xdb, 0xe0, 0xbd, 0xff, 0xe2, 0xdd, 0xbe, 0xff, 0xff, 0xff, 0xe1, 0xff, 0x77, 0x7a, 0x5b, 0xff, 0x03, 0x02, 0x00, 0xff, 0x2a, 0x2a, 0x2d, 0xff, 0x27, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x24, 0x29, 0x28, 0xff, 0x25, 0x23, 0x26, 0xff, 0x0e, 0x05, 0x08, 0xff, 0x73, 0x9a, 0xac, 0xff, 0xa8, 0xff, 0xff, 0xff, 0x81, 0xd6, 0xff, 0xff, 0x49, 0x66, 0xcd, 0xff, 0x23, 0x30, 0xa7, 0xff, 0x5a, 0x8e, 0xff, 0xff, 0x3d, 0x47, 0x76, 0xff, 0x5f, 0x29, 0x14, 0xff, 0x4a, 0x32, 0x29, 0xff, 0x36, 0x26, 0x52, 0xff, 0x7a, 0x37, 0xdb, 0xff, 0x9c, 0x51, 0xf4, 0xff, 0x59, 0x3f, 0x76, 0xff, 0x11, 0x21, 0x03, 0xff, 0x21, 0x1d, 0x1f, 0xff, 0xae, 0x7d, 0xd1, 0xff, 0xf7, 0xbc, 0xff, 0xff, 0xdc, 0x9e, 0xff, 0xff, 0xd5, 0x97, 0xff, 0xf4, 0xc8, 0x91, 0xff, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x91, 0x48, 0xda, 0x07, 0xb4, 0x77, 0xf7, 0xd1, 0xb9, 0x81, 0xfc, 0xff, 0x88, 0x45, 0xd3, 0xff, 0x66, 0x28, 0xab, 0xff, 0x5a, 0x23, 0xa4, 0xff, 0x48, 0x21, 0x6e, 0xff, 0x87, 0x4b, 0xb6, 0xff, 0xb1, 0x7f, 0xae, 0xff, 0xe0, 0xd6, 0x86, 0xff, 0xd9, 0xe4, 0xa6, 0xff, 0xde, 0xda, 0xae, 0xff, 0xed, 0xd4, 0xa4, 0xff, 0xe7, 0xdb, 0xa0, 0xff, 0xdb, 0xdf, 0xa4, 0xff, 0xd7, 0xd9, 0xad, 0xff, 0xda, 0xd6, 0xac, 0xff, 0xd3, 0xd0, 0xa0, 0xff, 0xe8, 0xe6, 0xb6, 0xff, 0xba, 0xba, 0x8e, 0xff, 0x1c, 0x1d, 0x08, 0xff, 0x1f, 0x1c, 0x27, 0xff, 0x27, 0x26, 0x2d, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x24, 0x28, 0x28, 0xff, 0x29, 0x24, 0x2e, 0xff, 0x0f, 0x12, 0x1a, 0xff, 0x3b, 0x46, 0x4c, 0xff, 0x9f, 0xde, 0xfb, 0xff, 0x43, 0xa3, 0xff, 0xff, 0x36, 0x63, 0xde, 0xff, 0x37, 0x41, 0xc5, 0xff, 0x3a, 0x5c, 0xec, 0xff, 0x3f, 0x5d, 0xc1, 0xff, 0x48, 0x20, 0x29, 0xff, 0x81, 0x60, 0x51, 0xff, 0x87, 0x7a, 0x66, 0xff, 0x81, 0x3c, 0xc3, 0xff, 0xc7, 0x7e, 0xff, 0xff, 0xa1, 0x6f, 0xe0, 0xff, 0x21, 0x21, 0x1f, 0xff, 0x15, 0x1e, 0x01, 0xff, 0x52, 0x3f, 0x62, 0xff, 0x93, 0x76, 0xb0, 0xff, 0x73, 0x61, 0x82, 0xff, 0x5f, 0x4f, 0x6d, 0xff, 0x4b, 0x3f, 0x5c, 0xd1, 0x48, 0x48, 0x48, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9c, 0x56, 0xd3, 0x8e, 0xad, 0x64, 0xfa, 0xff, 0x82, 0x48, 0xcd, 0xff, 0x4a, 0x24, 0x6d, 0xff, 0x52, 0x2b, 0x82, 0xff, 0x6f, 0x3b, 0xb1, 0xff, 0x58, 0x2d, 0x77, 0xff, 0x2e, 0x0d, 0x2e, 0xff, 0x73, 0x2c, 0xa0, 0xff, 0xbc, 0x8e, 0x92, 0xff, 0xde, 0xe9, 0x89, 0xff, 0xdb, 0xe1, 0xa1, 0xff, 0xdf, 0xd7, 0xa8, 0xff, 0xd8, 0xd3, 0xa3, 0xff, 0xd6, 0xd8, 0xa4, 0xff, 0xd7, 0xd7, 0xad, 0xff, 0xdf, 0xd8, 0xac, 0xff, 0xe2, 0xdc, 0xa7, 0xff, 0xf4, 0xf3, 0xc2, 0xff, 0x53, 0x52, 0x34, 0xff, 0x0c, 0x0b, 0x06, 0xff, 0x27, 0x27, 0x35, 0xff, 0x26, 0x26, 0x2b, 0xff, 0x29, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x27, 0x2b, 0xff, 0x30, 0x2d, 0x1c, 0xff, 0x29, 0x29, 0x2e, 0xff, 0x14, 0x06, 0x03, 0xff, 0x4e, 0x6a, 0x79, 0xff, 0x4c, 0x86, 0xff, 0xff, 0x2f, 0x5c, 0xeb, 0xff, 0x47, 0x66, 0xe0, 0xff, 0x26, 0x25, 0x69, 0xff, 0x3a, 0x46, 0xcb, 0xff, 0x31, 0x3a, 0x94, 0xff, 0x52, 0x2a, 0x0e, 0xff, 0x99, 0x72, 0x51, 0xff, 0x74, 0x43, 0x86, 0xff, 0x92, 0x4a, 0xeb, 0xff, 0x9a, 0x52, 0xff, 0xff, 0x50, 0x2e, 0x7d, 0xff, 0x15, 0x1e, 0x00, 0xff, 0x1a, 0x21, 0x0a, 0xff, 0x13, 0x0f, 0x0f, 0xff, 0x13, 0x14, 0x0f, 0xff, 0x13, 0x17, 0x0d, 0xff, 0x14, 0x1e, 0x09, 0xff, 0x13, 0x23, 0x05, 0x8f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x47, 0xaf, 0x40, 0xb8, 0x68, 0xf0, 0xfe, 0xa6, 0x5b, 0xe9, 0xff, 0x56, 0x34, 0x7d, 0xff, 0x28, 0x20, 0x36, 0xff, 0x3c, 0x22, 0x5b, 0xff, 0x92, 0x50, 0xda, 0xff, 0x72, 0x3b, 0x98, 0xff, 0x28, 0x1f, 0x12, 0xff, 0x4c, 0x2e, 0x61, 0xff, 0x9e, 0x5c, 0xc1, 0xff, 0xd6, 0xbf, 0xb0, 0xff, 0xec, 0xef, 0xb9, 0xff, 0xd5, 0xdc, 0xa6, 0xff, 0xd2, 0xd4, 0xa8, 0xff, 0xd8, 0xd3, 0xb0, 0xff, 0xda, 0xd8, 0xac, 0xff, 0xd9, 0xd4, 0xa4, 0xff, 0xff, 0xfc, 0xc9, 0xff, 0xa7, 0xa6, 0x80, 0xff, 0x0f, 0x0a, 0x03, 0xff, 0x26, 0x22, 0x2d, 0xff, 0x22, 0x28, 0x28, 0xff, 0x26, 0x29, 0x26, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x27, 0xff, 0x2b, 0x26, 0x2c, 0xff, 0x2c, 0x2e, 0x1c, 0xff, 0x2c, 0x2b, 0x24, 0xff, 0x30, 0x21, 0x21, 0xff, 0x16, 0x18, 0x14, 0xff, 0x43, 0x51, 0xb4, 0xff, 0x3c, 0x6c, 0xfb, 0xff, 0x2c, 0x54, 0xd4, 0xff, 0x2f, 0x20, 0x51, 0xff, 0x35, 0x39, 0xa9, 0xff, 0x34, 0x5d, 0xe5, 0xff, 0x46, 0x36, 0x3c, 0xff, 0x5d, 0x24, 0x1a, 0xff, 0x42, 0x2e, 0x10, 0xff, 0x65, 0x30, 0xa6, 0xff, 0x98, 0x4a, 0xff, 0xff, 0x89, 0x3d, 0xe7, 0xff, 0x47, 0x2b, 0x67, 0xff, 0x1a, 0x25, 0x0a, 0xff, 0x1c, 0x24, 0x06, 0xff, 0x28, 0x27, 0x22, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x25, 0x21, 0x27, 0xff, 0x2f, 0x27, 0x3a, 0xfe, 0x2f, 0x23, 0x36, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x38, 0x38, 0x09, 0x5e, 0x30, 0x74, 0xdb, 0xb5, 0x5f, 0xec, 0xff, 0x9d, 0x58, 0xd3, 0xff, 0x3d, 0x1e, 0x66, 0xff, 0x26, 0x22, 0x46, 0xff, 0x42, 0x27, 0x61, 0xff, 0xb0, 0x67, 0xee, 0xff, 0x80, 0x45, 0xaa, 0xff, 0x2a, 0x1e, 0x30, 0xff, 0x1b, 0x1d, 0x1d, 0xff, 0x5b, 0x2a, 0x8f, 0xff, 0xa7, 0x6c, 0xc5, 0xff, 0xf4, 0xe8, 0xd7, 0xff, 0xe8, 0xf8, 0xc6, 0xff, 0xd6, 0xdc, 0xbb, 0xff, 0xdb, 0xcf, 0xbf, 0xff, 0xd7, 0xd8, 0xa4, 0xff, 0xe2, 0xe6, 0xaa, 0xff, 0xe1, 0xe2, 0xb4, 0xff, 0x3b, 0x3b, 0x23, 0xff, 0x15, 0x0d, 0x1a, 0xff, 0x27, 0x23, 0x36, 0xff, 0x22, 0x2c, 0x21, 0xff, 0x26, 0x2b, 0x22, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x26, 0x2c, 0xff, 0x20, 0x24, 0x33, 0xff, 0x2a, 0x2d, 0x1d, 0xff, 0x29, 0x2b, 0x2c, 0xff, 0x22, 0x1c, 0x0d, 0xff, 0x27, 0x27, 0x34, 0xff, 0x33, 0x65, 0xec, 0xff, 0x4f, 0x70, 0xff, 0xff, 0x41, 0x41, 0xa1, 0xff, 0x1c, 0x27, 0x6e, 0xff, 0x5d, 0x83, 0xff, 0xff, 0x66, 0x82, 0xb4, 0xff, 0x47, 0x1c, 0x11, 0xff, 0x4d, 0x2d, 0x1c, 0xff, 0x37, 0x24, 0x3b, 0xff, 0x71, 0x3e, 0xb3, 0xff, 0x9e, 0x45, 0xff, 0xff, 0x91, 0x46, 0xfc, 0xff, 0x62, 0x30, 0xbd, 0xff, 0x55, 0x33, 0x85, 0xff, 0x3c, 0x36, 0x36, 0xff, 0x3f, 0x27, 0x57, 0xff, 0x6e, 0x3b, 0xbd, 0xff, 0x57, 0x3a, 0x7f, 0xff, 0x35, 0x26, 0x28, 0xdb, 0x1c, 0x1c, 0x38, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x43, 0x2d, 0x45, 0x88, 0x38, 0x1e, 0x36, 0xff, 0x51, 0x2e, 0x6b, 0xff, 0x5d, 0x39, 0x8b, 0xff, 0x43, 0x25, 0x75, 0xff, 0x33, 0x24, 0x52, 0xff, 0x73, 0x3d, 0x9e, 0xff, 0xb8, 0x6b, 0xf8, 0xff, 0xb4, 0x7c, 0xe6, 0xff, 0x5f, 0x39, 0x84, 0xff, 0x46, 0x1f, 0x6f, 0xff, 0x59, 0x26, 0x8d, 0xff, 0x74, 0x30, 0xa5, 0xff, 0xbc, 0x9d, 0xc3, 0xff, 0xf1, 0xfd, 0xda, 0xff, 0xf2, 0xf6, 0xde, 0xff, 0xf6, 0xec, 0xe9, 0xff, 0xd2, 0xd8, 0xaa, 0xff, 0xf9, 0xff, 0xbd, 0xff, 0x8c, 0x87, 0x6c, 0xff, 0x05, 0x02, 0x08, 0xff, 0x26, 0x2b, 0x2d, 0xff, 0x23, 0x2a, 0x23, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x25, 0x27, 0x2c, 0xff, 0x28, 0x29, 0x26, 0xff, 0x26, 0x29, 0x28, 0xff, 0x2c, 0x2a, 0x28, 0xff, 0x24, 0x21, 0x08, 0xff, 0x27, 0x33, 0x6c, 0xff, 0x47, 0x68, 0xff, 0xff, 0x31, 0x3f, 0xaa, 0xff, 0x16, 0x11, 0x49, 0xff, 0x4b, 0x75, 0xee, 0xff, 0x83, 0xcb, 0xff, 0xff, 0x51, 0x4d, 0x4c, 0xff, 0x49, 0x1a, 0x1d, 0xff, 0x45, 0x26, 0x2b, 0xff, 0x27, 0x23, 0x17, 0xff, 0x47, 0x32, 0x86, 0xff, 0x75, 0x37, 0xd9, 0xff, 0x78, 0x38, 0xd0, 0xff, 0x68, 0x3e, 0xb3, 0xff, 0x3d, 0x2a, 0x54, 0xff, 0x33, 0x28, 0x3c, 0xff, 0x50, 0x35, 0x76, 0xff, 0x35, 0x22, 0x4b, 0xff, 0x2e, 0x26, 0x26, 0xff, 0x30, 0x27, 0x2b, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2e, 0x28, 0x28, 0x26, 0x3a, 0x29, 0x36, 0xfa, 0x3e, 0x31, 0x36, 0xff, 0x2b, 0x28, 0x24, 0xff, 0x2a, 0x20, 0x2f, 0xff, 0x44, 0x28, 0x62, 0xff, 0x55, 0x28, 0x8a, 0xff, 0xa3, 0x5b, 0xd6, 0xff, 0xbc, 0x6c, 0xff, 0xff, 0x9f, 0x60, 0xea, 0xff, 0x70, 0x39, 0xb2, 0xff, 0x66, 0x28, 0xb4, 0xff, 0x71, 0x39, 0xb3, 0xff, 0xaa, 0x62, 0xe4, 0xff, 0x81, 0x44, 0xaf, 0xff, 0xd5, 0xc4, 0xc1, 0xff, 0xff, 0xfe, 0xcb, 0xff, 0xbf, 0xb9, 0x77, 0xff, 0xe9, 0xdf, 0xb6, 0xff, 0xd9, 0xd6, 0xb3, 0xff, 0x2a, 0x28, 0x14, 0xff, 0x1a, 0x16, 0x24, 0xff, 0x24, 0x2a, 0x29, 0xff, 0x26, 0x2d, 0x1f, 0xff, 0x2b, 0x26, 0x2b, 0xff, 0x29, 0x26, 0x2b, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x29, 0x27, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x2a, 0x27, 0x1f, 0xff, 0x20, 0x1e, 0x12, 0xff, 0x3e, 0x4c, 0xbb, 0xff, 0x40, 0x5b, 0xe6, 0xff, 0x1c, 0x2a, 0x79, 0xff, 0x2e, 0x3c, 0xb8, 0xff, 0x83, 0xcd, 0xff, 0xff, 0x6f, 0x90, 0xbf, 0xff, 0x3a, 0x16, 0x18, 0xff, 0x4e, 0x33, 0x1e, 0xff, 0x39, 0x2c, 0x1a, 0xff, 0x15, 0x1f, 0x13, 0xff, 0x26, 0x2b, 0x25, 0xff, 0x24, 0x29, 0x28, 0xff, 0x1c, 0x21, 0x1f, 0xff, 0x28, 0x28, 0x1f, 0xff, 0x1e, 0x2b, 0x20, 0xff, 0x25, 0x1b, 0x1f, 0xff, 0x33, 0x26, 0x26, 0xff, 0x29, 0x2c, 0x2c, 0xff, 0x14, 0x0f, 0x12, 0xfa, 0x1a, 0x21, 0x14, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2c, 0x2a, 0x1f, 0xb3, 0x2b, 0x29, 0x23, 0xff, 0x2e, 0x2b, 0x2c, 0xff, 0x32, 0x31, 0x24, 0xff, 0x30, 0x30, 0x13, 0xff, 0x36, 0x1c, 0x36, 0xff, 0x7a, 0x3b, 0xb7, 0xff, 0xc1, 0x78, 0xf6, 0xff, 0xb9, 0x75, 0xf2, 0xff, 0x6c, 0x37, 0xad, 0xff, 0x43, 0x1d, 0x77, 0xff, 0x65, 0x31, 0xa9, 0xff, 0x9a, 0x64, 0xdc, 0xff, 0xce, 0x90, 0xff, 0xff, 0x90, 0x50, 0xc6, 0xff, 0xb7, 0x7d, 0xcc, 0xff, 0xe2, 0xc3, 0x99, 0xff, 0xa3, 0x93, 0x36, 0xff, 0xff, 0xff, 0xc5, 0xff, 0x83, 0x84, 0x69, 0xff, 0x00, 0x02, 0x00, 0xff, 0x2a, 0x28, 0x32, 0xff, 0x25, 0x28, 0x2a, 0xff, 0x2a, 0x2a, 0x24, 0xff, 0x2e, 0x27, 0x28, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x26, 0x21, 0x0e, 0xff, 0x2c, 0x2c, 0x38, 0xff, 0x3f, 0x58, 0xe4, 0xff, 0x36, 0x58, 0xe0, 0xff, 0x42, 0x3c, 0xb5, 0xff, 0x6f, 0xa4, 0xfb, 0xff, 0x7b, 0xc4, 0xff, 0xff, 0x36, 0x3a, 0x53, 0xff, 0x07, 0x0a, 0x00, 0xff, 0x48, 0x22, 0x3a, 0xff, 0x43, 0x28, 0x37, 0xff, 0x26, 0x28, 0x19, 0xff, 0x19, 0x26, 0x29, 0xff, 0x2b, 0x29, 0x23, 0xff, 0x31, 0x2b, 0x23, 0xff, 0x1b, 0x2b, 0x31, 0xff, 0x36, 0x29, 0x26, 0xff, 0x30, 0x2c, 0x0b, 0xff, 0x00, 0x0b, 0x06, 0xff, 0x6c, 0x7c, 0x93, 0xff, 0x86, 0x8d, 0x8c, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x25, 0x30, 0x44, 0x2d, 0x28, 0x2a, 0xff, 0x29, 0x2c, 0x22, 0xff, 0x26, 0x25, 0x2e, 0xff, 0x2c, 0x26, 0x35, 0xff, 0x3a, 0x33, 0x2b, 0xff, 0x3f, 0x2a, 0x32, 0xff, 0x5e, 0x30, 0x74, 0xff, 0xb2, 0x6e, 0xed, 0xff, 0xa2, 0x64, 0xe6, 0xff, 0x47, 0x2a, 0x6a, 0xff, 0x30, 0x20, 0x45, 0xff, 0x59, 0x31, 0x89, 0xff, 0xb7, 0x76, 0xff, 0xff, 0xc6, 0x8f, 0xff, 0xff, 0xab, 0x7d, 0xe1, 0xff, 0x66, 0x33, 0x82, 0xff, 0x4a, 0x28, 0x21, 0xff, 0xc4, 0xb0, 0x82, 0xff, 0xdf, 0xd8, 0xa9, 0xff, 0x24, 0x29, 0x13, 0xff, 0x1a, 0x1b, 0x21, 0xff, 0x25, 0x26, 0x2b, 0xff, 0x27, 0x27, 0x29, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x2e, 0x27, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2b, 0x2a, 0x27, 0xff, 0x23, 0x21, 0x00, 0xff, 0x2c, 0x2f, 0x6b, 0xff, 0x4a, 0x65, 0xfb, 0xff, 0x33, 0x3c, 0xbf, 0xff, 0x4d, 0x72, 0xce, 0xff, 0x65, 0xaf, 0xff, 0xff, 0x54, 0x84, 0xd9, 0xff, 0x55, 0x76, 0x74, 0xff, 0x38, 0x2f, 0x1e, 0xff, 0x4d, 0x2d, 0x2a, 0xff, 0x3e, 0x2b, 0x2c, 0xff, 0x2f, 0x2c, 0x27, 0xff, 0x29, 0x22, 0x16, 0xff, 0x1c, 0x1d, 0x14, 0xff, 0x15, 0x29, 0x19, 0xff, 0x2a, 0x27, 0x1b, 0xff, 0x1d, 0x1b, 0x2b, 0xff, 0x28, 0x51, 0x85, 0xff, 0xd3, 0xff, 0xff, 0xff, 0xa8, 0xb6, 0xb8, 0xff, 0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x23, 0x33, 0xc8, 0x2b, 0x26, 0x2e, 0xff, 0x2a, 0x2b, 0x27, 0xff, 0x27, 0x24, 0x2e, 0xff, 0x28, 0x20, 0x36, 0xff, 0x2f, 0x27, 0x35, 0xff, 0x39, 0x2e, 0x33, 0xff, 0x33, 0x22, 0x29, 0xff, 0x51, 0x2d, 0x68, 0xff, 0x54, 0x31, 0x79, 0xff, 0x2c, 0x2a, 0x31, 0xff, 0x24, 0x27, 0x20, 0xff, 0x41, 0x22, 0x5c, 0xff, 0x9a, 0x57, 0xcf, 0xff, 0xac, 0x6a, 0xf3, 0xff, 0x6b, 0x38, 0xb8, 0xff, 0x27, 0x17, 0x37, 0xff, 0x1f, 0x12, 0x13, 0xff, 0x38, 0x30, 0x22, 0xff, 0x35, 0x33, 0x2c, 0xff, 0x1a, 0x19, 0x22, 0xff, 0x2b, 0x2d, 0x2b, 0xff, 0x27, 0x28, 0x26, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x26, 0x26, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x23, 0x2a, 0x29, 0xff, 0x2d, 0x28, 0x20, 0xff, 0x24, 0x20, 0x10, 0xff, 0x31, 0x42, 0x88, 0xff, 0x2b, 0x32, 0xbb, 0xff, 0x25, 0x39, 0x81, 0xff, 0x4e, 0x7f, 0xee, 0xff, 0x31, 0x7a, 0xf9, 0xff, 0xa7, 0xdb, 0xff, 0xff, 0x4d, 0x68, 0x51, 0xff, 0x16, 0x0a, 0x00, 0xff, 0x43, 0x2e, 0x31, 0xff, 0x33, 0x26, 0x21, 0xff, 0x2e, 0x2d, 0x59, 0xff, 0x26, 0x36, 0x7b, 0xff, 0x29, 0x30, 0x46, 0xff, 0x28, 0x29, 0x51, 0xff, 0x46, 0x50, 0xbd, 0xff, 0x5d, 0x8e, 0xf1, 0xff, 0xad, 0xf9, 0xff, 0xff, 0x6a, 0x7a, 0x8c, 0xff, 0x00, 0x00, 0x00, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2b, 0x2e, 0x21, 0x4d, 0x29, 0x29, 0x2b, 0xff, 0x24, 0x26, 0x23, 0xff, 0x28, 0x25, 0x26, 0xff, 0x2e, 0x2b, 0x27, 0xff, 0x28, 0x2c, 0x22, 0xff, 0x25, 0x26, 0x29, 0xff, 0x2a, 0x27, 0x2f, 0xff, 0x2f, 0x2e, 0x28, 0xff, 0x28, 0x26, 0x17, 0xff, 0x28, 0x22, 0x2e, 0xff, 0x2a, 0x2d, 0x3b, 0xff, 0x2e, 0x2b, 0x3e, 0xff, 0x5a, 0x2d, 0x86, 0xff, 0x93, 0x48, 0xc5, 0xff, 0xa5, 0x55, 0xe1, 0xff, 0x66, 0x2f, 0xab, 0xff, 0x3f, 0x26, 0x78, 0xff, 0x30, 0x2b, 0x44, 0xff, 0x23, 0x26, 0x1e, 0xff, 0x15, 0x19, 0x1b, 0xff, 0x29, 0x28, 0x30, 0xff, 0x2a, 0x29, 0x25, 0xff, 0x2a, 0x2c, 0x22, 0xff, 0x29, 0x28, 0x27, 0xff, 0x26, 0x27, 0x2b, 0xff, 0x23, 0x29, 0x29, 0xff, 0x27, 0x29, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x25, 0x26, 0x26, 0xff, 0x28, 0x27, 0x26, 0xff, 0x29, 0x29, 0x28, 0xff, 0x27, 0x27, 0x28, 0xff, 0x2a, 0x27, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x26, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x2a, 0x26, 0xff, 0x29, 0x24, 0x35, 0xff, 0x24, 0x29, 0x17, 0xff, 0x12, 0x23, 0x08, 0xff, 0x44, 0x38, 0x9b, 0xff, 0x25, 0x1f, 0x81, 0xff, 0x4a, 0x71, 0xca, 0xff, 0x39, 0x81, 0xf2, 0xff, 0x5c, 0x95, 0xf9, 0xff, 0x8b, 0xb0, 0xd2, 0xff, 0x0e, 0x13, 0x14, 0xff, 0x28, 0x22, 0x1f, 0xff, 0x1b, 0x1d, 0x18, 0xff, 0x37, 0x62, 0xb5, 0xff, 0x4a, 0x7c, 0xff, 0xff, 0x42, 0x44, 0x8b, 0xff, 0x25, 0x28, 0x79, 0xff, 0x2b, 0x45, 0xc3, 0xff, 0x3a, 0x6b, 0xbc, 0xff, 0x9c, 0xee, 0xff, 0xff, 0x4c, 0x56, 0x62, 0xff, 0x12, 0x0a, 0x00, 0xff, 0x27, 0x42, 0x77, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x26, 0x21, 0xcd, 0x2a, 0x24, 0x33, 0xff, 0x47, 0x33, 0x2f, 0xff, 0x35, 0x33, 0x29, 0xff, 0x28, 0x29, 0x21, 0xff, 0x26, 0x26, 0x1c, 0xff, 0x21, 0x21, 0x1e, 0xff, 0x1f, 0x21, 0x24, 0xff, 0x23, 0x28, 0x27, 0xff, 0x2a, 0x29, 0x18, 0xff, 0x18, 0x23, 0x1a, 0xff, 0x17, 0x1d, 0x40, 0xff, 0x4e, 0x24, 0x83, 0xff, 0x88, 0x34, 0xc3, 0xff, 0x90, 0x3c, 0xce, 0xff, 0x7f, 0x39, 0xcb, 0xff, 0x64, 0x32, 0xa4, 0xff, 0x61, 0x35, 0xaa, 0xff, 0x4e, 0x30, 0x86, 0xff, 0x28, 0x2b, 0x1f, 0xff, 0x25, 0x27, 0x1f, 0xff, 0x28, 0x25, 0x2e, 0xff, 0x29, 0x29, 0x27, 0xff, 0x29, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x2b, 0x26, 0xff, 0x2b, 0x2b, 0x22, 0xff, 0x2d, 0x28, 0x25, 0xff, 0x24, 0x25, 0x2b, 0xff, 0x24, 0x2c, 0x36, 0xff, 0x24, 0x2a, 0x2d, 0xff, 0x23, 0x29, 0x26, 0xff, 0x30, 0x32, 0x31, 0xff, 0x2e, 0x28, 0x29, 0xff, 0x24, 0x25, 0x27, 0xff, 0x25, 0x2b, 0x2d, 0xff, 0x2a, 0x26, 0x2b, 0xff, 0x2c, 0x26, 0x27, 0xff, 0x28, 0x29, 0x26, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x24, 0xff, 0x2b, 0x29, 0x25, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x27, 0x2a, 0x2d, 0xff, 0x23, 0x27, 0x2c, 0xff, 0x26, 0x29, 0x2c, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x29, 0x29, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x27, 0x2b, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x1e, 0x29, 0x03, 0xff, 0x3a, 0x36, 0x61, 0xff, 0x3b, 0x2c, 0x9f, 0xff, 0x16, 0x32, 0x7d, 0xff, 0x69, 0xa1, 0xff, 0xff, 0x49, 0x99, 0xfa, 0xff, 0x85, 0xc6, 0xff, 0xff, 0x61, 0x70, 0x7c, 0xff, 0x12, 0x0a, 0x01, 0xff, 0x31, 0x1f, 0x17, 0xff, 0x3f, 0x6a, 0xb8, 0xff, 0x51, 0xa2, 0xff, 0xff, 0x39, 0x44, 0x92, 0xff, 0x1f, 0x33, 0x97, 0xff, 0x22, 0x5c, 0xd5, 0xff, 0x5d, 0x8f, 0xc5, 0xff, 0xa0, 0xe5, 0xf7, 0xff, 0x32, 0x36, 0x46, 0xff, 0x1e, 0x0d, 0x00, 0xff, 0x3a, 0x52, 0x88, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2b, 0x2b, 0x23, 0x40, 0x16, 0x1a, 0x13, 0xff, 0x13, 0x08, 0x17, 0xff, 0x58, 0x38, 0x27, 0xff, 0x5a, 0x3c, 0x2f, 0xff, 0x4c, 0x2b, 0x2b, 0xff, 0x69, 0x45, 0x45, 0xff, 0x6e, 0x49, 0x48, 0xff, 0x65, 0x40, 0x42, 0xff, 0x5a, 0x34, 0x38, 0xff, 0x6f, 0x41, 0x68, 0xff, 0x61, 0x3e, 0x64, 0xff, 0x4d, 0x30, 0x62, 0xff, 0x84, 0x4f, 0xbc, 0xff, 0xa0, 0x5b, 0xe0, 0xff, 0x82, 0x47, 0xb0, 0xff, 0x55, 0x23, 0x81, 0xff, 0x5b, 0x24, 0xa0, 0xff, 0x7d, 0x45, 0xd0, 0xff, 0x52, 0x3e, 0x6a, 0xff, 0x1d, 0x1c, 0x19, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x29, 0x29, 0x26, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x29, 0x27, 0xff, 0x26, 0x27, 0x25, 0xff, 0x28, 0x29, 0x24, 0xff, 0x27, 0x2f, 0x2f, 0xff, 0x21, 0x2c, 0x33, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x2f, 0x29, 0x24, 0xff, 0x31, 0x2f, 0x2d, 0xff, 0x2e, 0x3e, 0x4c, 0xff, 0x24, 0x29, 0x36, 0xff, 0x25, 0x29, 0x2c, 0xff, 0x28, 0x41, 0x44, 0xff, 0x26, 0x29, 0x30, 0xff, 0x33, 0x21, 0x2a, 0xff, 0x2e, 0x2c, 0x32, 0xff, 0x23, 0x2b, 0x2a, 0xff, 0x28, 0x2b, 0x29, 0xff, 0x28, 0x29, 0x2f, 0xff, 0x29, 0x2a, 0x31, 0xff, 0x27, 0x28, 0x2e, 0xff, 0x25, 0x29, 0x2a, 0xff, 0x29, 0x2b, 0x29, 0xff, 0x2c, 0x2a, 0x29, 0xff, 0x2a, 0x2e, 0x30, 0xff, 0x25, 0x29, 0x2f, 0xff, 0x29, 0x2c, 0x2e, 0xff, 0x2d, 0x2c, 0x2b, 0xff, 0x2b, 0x29, 0x27, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x2a, 0x29, 0x2a, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x26, 0x2c, 0x21, 0xff, 0x26, 0x2b, 0x23, 0xff, 0x2b, 0x21, 0x36, 0xff, 0x2b, 0x26, 0x2d, 0xff, 0x11, 0x1f, 0x0e, 0xff, 0x3e, 0x43, 0x7b, 0xff, 0x33, 0x25, 0x7a, 0xff, 0x38, 0x59, 0x9b, 0xff, 0x55, 0xa8, 0xff, 0xff, 0x4b, 0xaa, 0xff, 0xff, 0x78, 0xb4, 0xde, 0xff, 0x22, 0x17, 0x04, 0xff, 0x15, 0x0b, 0x1d, 0xff, 0x4a, 0x86, 0xf4, 0xff, 0x58, 0xa5, 0xff, 0xff, 0x2f, 0x30, 0x7a, 0xff, 0x44, 0x54, 0xb4, 0xff, 0x30, 0x6d, 0xef, 0xff, 0x58, 0x90, 0xd5, 0xff, 0x82, 0xce, 0xde, 0xff, 0x16, 0x21, 0x2f, 0xff, 0x2e, 0x1d, 0x07, 0xff, 0x1f, 0x30, 0x62, 0xff, 0x2b, 0x2f, 0x73, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x29, 0x20, 0xb3, 0xac, 0x90, 0x7a, 0xff, 0x8a, 0x78, 0x8a, 0xff, 0x22, 0x16, 0x1e, 0xff, 0x2a, 0x17, 0x14, 0xff, 0x47, 0x31, 0x2f, 0xff, 0x4f, 0x3c, 0x34, 0xff, 0x5b, 0x42, 0x3b, 0xff, 0x60, 0x40, 0x3d, 0xff, 0x55, 0x34, 0x32, 0xff, 0x88, 0x56, 0x81, 0xff, 0xda, 0x87, 0xe6, 0xff, 0xd8, 0x88, 0xee, 0xff, 0xdd, 0xb5, 0xff, 0xff, 0xd5, 0xc0, 0xff, 0xff, 0xa8, 0x75, 0xdc, 0xff, 0x62, 0x26, 0x8d, 0xff, 0xa5, 0x5e, 0xf6, 0xff, 0x7f, 0x54, 0xb2, 0xff, 0x1a, 0x19, 0x0e, 0xff, 0x26, 0x24, 0x28, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x28, 0x2a, 0x25, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x2b, 0x2a, 0x27, 0xff, 0x27, 0x28, 0x26, 0xff, 0x29, 0x2a, 0x28, 0xff, 0x28, 0x2b, 0x29, 0xff, 0x22, 0x25, 0x23, 0xff, 0x2b, 0x2f, 0x2f, 0xff, 0x2e, 0x40, 0x53, 0xff, 0x24, 0x3b, 0x59, 0xff, 0x2b, 0x2d, 0x33, 0xff, 0x30, 0x27, 0x1d, 0xff, 0x31, 0x3d, 0x4a, 0xff, 0x36, 0x4c, 0x62, 0xff, 0x1f, 0x2d, 0x3b, 0xff, 0x29, 0x34, 0x47, 0xff, 0x3e, 0x49, 0x5a, 0xff, 0x1d, 0x27, 0x31, 0xff, 0x2c, 0x2b, 0x31, 0xff, 0x31, 0x30, 0x32, 0xff, 0x28, 0x2d, 0x2b, 0xff, 0x2c, 0x2d, 0x30, 0xff, 0x2a, 0x2c, 0x30, 0xff, 0x28, 0x2c, 0x30, 0xff, 0x23, 0x29, 0x2e, 0xff, 0x26, 0x29, 0x2b, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x2b, 0x2c, 0xff, 0x2b, 0x2d, 0x31, 0xff, 0x2b, 0x2d, 0x2e, 0xff, 0x2e, 0x2e, 0x2e, 0xff, 0x29, 0x29, 0x2b, 0xff, 0x26, 0x28, 0x28, 0xff, 0x29, 0x29, 0x28, 0xff, 0x2c, 0x2b, 0x2b, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x26, 0x29, 0x27, 0xff, 0x24, 0x26, 0x2d, 0xff, 0x2b, 0x2d, 0x1f, 0xff, 0x2d, 0x21, 0x35, 0xff, 0x21, 0x24, 0x1f, 0xff, 0x1e, 0x2e, 0x1f, 0xff, 0x4a, 0x36, 0x8d, 0xff, 0x25, 0x27, 0x72, 0xff, 0x48, 0x8a, 0xe1, 0xff, 0x4e, 0xa7, 0xff, 0xff, 0x5e, 0x9f, 0xff, 0xff, 0x2f, 0x5a, 0x92, 0xff, 0x2b, 0x54, 0x7f, 0xff, 0x51, 0xa1, 0xff, 0xff, 0x41, 0x88, 0xee, 0xff, 0x21, 0x29, 0x73, 0xff, 0x46, 0x65, 0xb6, 0xff, 0x35, 0x81, 0xec, 0xff, 0x8a, 0xbd, 0xff, 0xff, 0x99, 0xb4, 0xc4, 0xff, 0x1b, 0x0f, 0x16, 0xff, 0x39, 0x2f, 0x1d, 0xff, 0x44, 0x6f, 0x9e, 0xff, 0x2d, 0x4b, 0x8a, 0xb3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x1e, 0x52, 0x22, 0x26, 0x1a, 0x0d, 0xfe, 0xcb, 0xc7, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaa, 0x86, 0xcf, 0xff, 0x21, 0x16, 0x2f, 0xff, 0x11, 0x1b, 0x17, 0xff, 0x21, 0x2b, 0x21, 0xff, 0x1e, 0x28, 0x19, 0xff, 0x25, 0x26, 0x1d, 0xff, 0x27, 0x23, 0x1f, 0xff, 0x23, 0x1c, 0x20, 0xff, 0x68, 0x39, 0x73, 0xff, 0x87, 0x43, 0x9e, 0xff, 0xb5, 0x96, 0xd6, 0xff, 0xd8, 0xc5, 0xff, 0xff, 0x97, 0x56, 0xe3, 0xff, 0x84, 0x30, 0xe5, 0xff, 0xa4, 0x66, 0xeb, 0xff, 0x3f, 0x31, 0x3a, 0xff, 0x1b, 0x1e, 0x0e, 0xff, 0x2a, 0x29, 0x2b, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x28, 0x28, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x26, 0x27, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x2c, 0x2a, 0xff, 0x29, 0x2d, 0x2c, 0xff, 0x24, 0x28, 0x2a, 0xff, 0x2f, 0x35, 0x3a, 0xff, 0x2a, 0x34, 0x3b, 0xff, 0x26, 0x22, 0x1a, 0xff, 0x32, 0x3a, 0x44, 0xff, 0x35, 0x5f, 0x91, 0xff, 0x34, 0x58, 0x82, 0xff, 0x26, 0x33, 0x40, 0xff, 0x25, 0x27, 0x2a, 0xff, 0x36, 0x4b, 0x58, 0xff, 0x49, 0x5e, 0x76, 0xff, 0x2c, 0x32, 0x4f, 0xff, 0x2a, 0x3f, 0x5a, 0xff, 0x36, 0x55, 0x67, 0xff, 0x25, 0x2c, 0x3b, 0xff, 0x26, 0x25, 0x2c, 0xff, 0x25, 0x2b, 0x2a, 0xff, 0x2d, 0x2c, 0x2d, 0xff, 0x2d, 0x2a, 0x2c, 0xff, 0x2a, 0x2b, 0x2c, 0xff, 0x2a, 0x2e, 0x2e, 0xff, 0x27, 0x2b, 0x2d, 0xff, 0x2d, 0x2f, 0x35, 0xff, 0x2c, 0x2e, 0x32, 0xff, 0x28, 0x29, 0x2e, 0xff, 0x2b, 0x2e, 0x32, 0xff, 0x2b, 0x2d, 0x31, 0xff, 0x26, 0x2b, 0x32, 0xff, 0x23, 0x29, 0x2f, 0xff, 0x27, 0x28, 0x29, 0xff, 0x2c, 0x2b, 0x2a, 0xff, 0x2e, 0x2d, 0x2d, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x25, 0x26, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x26, 0xff, 0x26, 0x24, 0x31, 0xff, 0x24, 0x25, 0x30, 0xff, 0x2b, 0x32, 0x14, 0xff, 0x2d, 0x28, 0x24, 0xff, 0x2d, 0x23, 0x35, 0xff, 0x20, 0x23, 0x1a, 0xff, 0x24, 0x24, 0x34, 0xff, 0x1f, 0x1a, 0x54, 0xff, 0x4f, 0x75, 0xc8, 0xff, 0x75, 0xc3, 0xff, 0xff, 0x4e, 0xa1, 0xee, 0xff, 0x57, 0xb0, 0xff, 0xff, 0x79, 0xba, 0xf5, 0xff, 0x71, 0xbd, 0xf1, 0xff, 0x44, 0x8b, 0xe8, 0xff, 0x30, 0x5a, 0xb5, 0xff, 0x55, 0x9d, 0xe5, 0xff, 0x68, 0xbf, 0xf1, 0xff, 0xd0, 0xfa, 0xff, 0xff, 0xa4, 0x9e, 0xa9, 0xff, 0x19, 0x02, 0x00, 0xff, 0x35, 0x38, 0x37, 0xff, 0x79, 0xbf, 0xf4, 0xff, 0x4d, 0x86, 0xdc, 0xfe, 0x43, 0x96, 0xd9, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x39, 0xaf, 0x86, 0x32, 0x17, 0x23, 0xff, 0xad, 0xa1, 0x4f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xb0, 0x88, 0xb7, 0xff, 0x1e, 0x05, 0x1f, 0xff, 0x25, 0x1c, 0x1e, 0xff, 0x30, 0x30, 0x25, 0xff, 0x31, 0x2b, 0x24, 0xff, 0x37, 0x28, 0x2c, 0xff, 0x22, 0x25, 0x2a, 0xff, 0x20, 0x27, 0x1f, 0xff, 0x1f, 0x12, 0x18, 0xff, 0x5e, 0x3a, 0x79, 0xff, 0xb9, 0x85, 0xfa, 0xff, 0x61, 0x26, 0xae, 0xff, 0x6b, 0x29, 0xca, 0xff, 0x39, 0x1f, 0x52, 0xff, 0x19, 0x1b, 0x09, 0xff, 0x2c, 0x31, 0x23, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x28, 0x25, 0x2e, 0xff, 0x28, 0x29, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x29, 0x2c, 0x2d, 0xff, 0x27, 0x2a, 0x2b, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x2b, 0x2e, 0xff, 0x24, 0x2a, 0x2e, 0xff, 0x2a, 0x31, 0x37, 0xff, 0x2d, 0x35, 0x40, 0xff, 0x21, 0x2c, 0x3c, 0xff, 0x36, 0x42, 0x55, 0xff, 0x38, 0x3b, 0x42, 0xff, 0x22, 0x27, 0x2d, 0xff, 0x34, 0x52, 0x74, 0xff, 0x56, 0x86, 0xba, 0xff, 0x3d, 0x66, 0x95, 0xff, 0x1b, 0x29, 0x3b, 0xff, 0x22, 0x20, 0x2b, 0xff, 0x42, 0x55, 0x6c, 0xff, 0x45, 0x71, 0x8d, 0xff, 0x25, 0x42, 0x66, 0xff, 0x3f, 0x48, 0x6d, 0xff, 0x42, 0x5d, 0x7a, 0xff, 0x2a, 0x47, 0x5b, 0xff, 0x1a, 0x26, 0x38, 0xff, 0x23, 0x2a, 0x38, 0xff, 0x31, 0x33, 0x3c, 0xff, 0x2e, 0x32, 0x37, 0xff, 0x2e, 0x38, 0x3c, 0xff, 0x22, 0x2c, 0x35, 0xff, 0x20, 0x25, 0x30, 0xff, 0x35, 0x39, 0x41, 0xff, 0x2e, 0x30, 0x38, 0xff, 0x27, 0x2b, 0x34, 0xff, 0x2a, 0x2f, 0x3b, 0xff, 0x2a, 0x34, 0x41, 0xff, 0x27, 0x31, 0x3d, 0xff, 0x25, 0x29, 0x2d, 0xff, 0x29, 0x29, 0x29, 0xff, 0x30, 0x32, 0x33, 0xff, 0x28, 0x2d, 0x33, 0xff, 0x25, 0x27, 0x2b, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x29, 0x26, 0xff, 0x25, 0x29, 0x27, 0xff, 0x28, 0x22, 0x33, 0xff, 0x2b, 0x2c, 0x1f, 0xff, 0x2a, 0x2c, 0x1f, 0xff, 0x27, 0x24, 0x34, 0xff, 0x30, 0x29, 0x12, 0xff, 0x19, 0x16, 0x14, 0xff, 0x28, 0x42, 0x5a, 0xff, 0x7b, 0xba, 0xe2, 0xff, 0x8d, 0xec, 0xff, 0xff, 0x8c, 0xde, 0xff, 0xff, 0x90, 0xdb, 0xff, 0xff, 0x95, 0xd4, 0xff, 0xff, 0x53, 0x87, 0xea, 0xff, 0x41, 0x74, 0xdd, 0xff, 0x5f, 0xbc, 0xfd, 0xff, 0x80, 0xd9, 0xf2, 0xff, 0xc3, 0xfd, 0xff, 0xff, 0x60, 0x74, 0x7a, 0xff, 0x08, 0x01, 0x00, 0xff, 0x28, 0x39, 0x3f, 0xff, 0x61, 0xa2, 0xe7, 0xff, 0x28, 0x38, 0xa2, 0xff, 0x31, 0x73, 0xc2, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x3f, 0xbf, 0x04, 0x95, 0x4c, 0xe3, 0xe7, 0x30, 0x16, 0x42, 0xff, 0x9b, 0x90, 0x38, 0xff, 0xff, 0xff, 0xd1, 0xff, 0xdb, 0xc2, 0x96, 0xff, 0xdd, 0xa2, 0xce, 0xff, 0xa4, 0x7d, 0xb9, 0xff, 0x1c, 0x10, 0x17, 0xff, 0x23, 0x27, 0x1f, 0xff, 0x29, 0x2d, 0x27, 0xff, 0x2a, 0x29, 0x2c, 0xff, 0x30, 0x29, 0x2f, 0xff, 0x21, 0x2c, 0x1d, 0xff, 0x27, 0x2e, 0x21, 0xff, 0x47, 0x20, 0x49, 0xff, 0x7b, 0x3d, 0x9c, 0xff, 0x63, 0x3a, 0xad, 0xff, 0x3c, 0x2b, 0x62, 0xff, 0x1f, 0x20, 0x18, 0xff, 0x25, 0x2c, 0x1d, 0xff, 0x24, 0x27, 0x2b, 0xff, 0x26, 0x27, 0x2c, 0xff, 0x29, 0x29, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x29, 0x29, 0xff, 0x2c, 0x2e, 0x2e, 0xff, 0x2e, 0x30, 0x30, 0xff, 0x25, 0x27, 0x27, 0xff, 0x2a, 0x2b, 0x28, 0xff, 0x29, 0x2d, 0x33, 0xff, 0x2c, 0x33, 0x3f, 0xff, 0x2d, 0x35, 0x42, 0xff, 0x2e, 0x38, 0x4b, 0xff, 0x30, 0x3c, 0x55, 0xff, 0x29, 0x33, 0x4f, 0xff, 0x2e, 0x46, 0x6b, 0xff, 0x36, 0x44, 0x55, 0xff, 0x28, 0x26, 0x1f, 0xff, 0x38, 0x57, 0x74, 0xff, 0x5c, 0x94, 0xd4, 0xff, 0x4e, 0x75, 0xab, 0xff, 0x1f, 0x2b, 0x3d, 0xff, 0x14, 0x1b, 0x26, 0xff, 0x40, 0x56, 0x74, 0xff, 0x53, 0x79, 0xa5, 0xff, 0x2f, 0x5a, 0x89, 0xff, 0x2a, 0x4d, 0x73, 0xff, 0x4a, 0x6d, 0x95, 0xff, 0x44, 0x69, 0x93, 0xff, 0x29, 0x44, 0x63, 0xff, 0x23, 0x31, 0x49, 0xff, 0x36, 0x42, 0x57, 0xff, 0x2c, 0x3e, 0x50, 0xff, 0x30, 0x3d, 0x4d, 0xff, 0x2d, 0x31, 0x3f, 0xff, 0x29, 0x2c, 0x36, 0xff, 0x3c, 0x3e, 0x47, 0xff, 0x2b, 0x30, 0x3b, 0xff, 0x27, 0x2f, 0x3f, 0xff, 0x2b, 0x39, 0x4c, 0xff, 0x30, 0x3f, 0x50, 0xff, 0x26, 0x2c, 0x34, 0xff, 0x25, 0x27, 0x27, 0xff, 0x2d, 0x31, 0x34, 0xff, 0x30, 0x38, 0x44, 0xff, 0x26, 0x2a, 0x2f, 0xff, 0x2c, 0x29, 0x26, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x27, 0x2b, 0x23, 0xff, 0x26, 0x2a, 0x25, 0xff, 0x28, 0x20, 0x37, 0xff, 0x29, 0x28, 0x27, 0xff, 0x29, 0x2e, 0x1d, 0xff, 0x26, 0x28, 0x28, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x2d, 0x28, 0x28, 0xff, 0x1d, 0x11, 0x0c, 0xff, 0x1c, 0x20, 0x1d, 0xff, 0x5d, 0x85, 0x94, 0xff, 0x83, 0xc7, 0xfa, 0xff, 0x72, 0xbb, 0xff, 0xff, 0x3f, 0x79, 0xd7, 0xff, 0x2e, 0x44, 0xb2, 0xff, 0x28, 0x3e, 0xb3, 0xff, 0x53, 0xa5, 0xee, 0xff, 0x68, 0xca, 0xf6, 0xff, 0x9d, 0xe6, 0xff, 0xff, 0x51, 0x5d, 0x65, 0xff, 0x13, 0x06, 0x00, 0xff, 0x27, 0x37, 0x3f, 0xff, 0x59, 0x92, 0xcd, 0xff, 0x29, 0x2d, 0x7d, 0xff, 0x36, 0x63, 0xaf, 0xe7, 0x3f, 0xbf, 0xff, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x92, 0x37, 0xdc, 0x49, 0xac, 0x67, 0xe9, 0xff, 0x31, 0x14, 0x42, 0xff, 0x66, 0x63, 0x43, 0xff, 0xff, 0xfa, 0xb2, 0xff, 0xd0, 0x9a, 0x2d, 0xff, 0xa4, 0x5d, 0x51, 0xff, 0xff, 0xdd, 0xff, 0xff, 0x77, 0x60, 0x89, 0xff, 0x13, 0x09, 0x08, 0xff, 0x23, 0x2c, 0x2c, 0xff, 0x2b, 0x29, 0x2a, 0xff, 0x31, 0x2b, 0x27, 0xff, 0x29, 0x2b, 0x25, 0xff, 0x33, 0x31, 0x2b, 0xff, 0x36, 0x25, 0x31, 0xff, 0x3a, 0x1c, 0x4e, 0xff, 0x62, 0x3e, 0x9c, 0xff, 0x2d, 0x28, 0x39, 0xff, 0x20, 0x27, 0x18, 0xff, 0x28, 0x2a, 0x26, 0xff, 0x26, 0x26, 0x2a, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x29, 0x26, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x28, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x26, 0x26, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2b, 0x29, 0x29, 0xff, 0x2b, 0x29, 0x29, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x27, 0x28, 0xff, 0x29, 0x25, 0x2a, 0xff, 0x29, 0x27, 0x2b, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x27, 0x29, 0x28, 0xff, 0x25, 0x2b, 0x26, 0xff, 0x25, 0x29, 0x2b, 0xff, 0x29, 0x2a, 0x32, 0xff, 0x38, 0x37, 0x3a, 0xff, 0x2b, 0x2a, 0x2c, 0xff, 0x2c, 0x2e, 0x32, 0xff, 0x29, 0x33, 0x37, 0xff, 0x27, 0x29, 0x32, 0xff, 0x38, 0x42, 0x5f, 0xff, 0x3a, 0x55, 0x7e, 0xff, 0x20, 0x32, 0x4f, 0xff, 0x2c, 0x34, 0x47, 0xff, 0x38, 0x4b, 0x6d, 0xff, 0x37, 0x4e, 0x7d, 0xff, 0x3c, 0x51, 0x72, 0xff, 0x36, 0x4a, 0x56, 0xff, 0x17, 0x24, 0x33, 0xff, 0x48, 0x5f, 0x85, 0xff, 0x6b, 0xa7, 0xe8, 0xff, 0x5c, 0x97, 0xca, 0xff, 0x27, 0x33, 0x4f, 0xff, 0x13, 0x0a, 0x23, 0xff, 0x2e, 0x44, 0x5f, 0xff, 0x47, 0x81, 0xa7, 0xff, 0x47, 0x72, 0x9d, 0xff, 0x32, 0x4d, 0x7b, 0xff, 0x42, 0x67, 0x99, 0xff, 0x48, 0x7e, 0xb2, 0xff, 0x2a, 0x64, 0x92, 0xff, 0x24, 0x46, 0x6d, 0xff, 0x2a, 0x37, 0x57, 0xff, 0x35, 0x43, 0x68, 0xff, 0x4d, 0x5d, 0x83, 0xff, 0x45, 0x51, 0x6b, 0xff, 0x2c, 0x36, 0x46, 0xff, 0x2b, 0x35, 0x40, 0xff, 0x30, 0x3b, 0x47, 0xff, 0x2a, 0x36, 0x47, 0xff, 0x32, 0x46, 0x60, 0xff, 0x2f, 0x44, 0x5e, 0xff, 0x1f, 0x26, 0x2c, 0xff, 0x30, 0x2c, 0x28, 0xff, 0x34, 0x45, 0x5a, 0xff, 0x22, 0x35, 0x4d, 0xff, 0x24, 0x25, 0x25, 0xff, 0x2d, 0x2b, 0x27, 0xff, 0x2b, 0x2b, 0x27, 0xff, 0x25, 0x29, 0x2b, 0xff, 0x2a, 0x28, 0x2a, 0xff, 0x2b, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x25, 0xff, 0x23, 0x27, 0x2d, 0xff, 0x28, 0x25, 0x2e, 0xff, 0x34, 0x2b, 0x2b, 0xff, 0x25, 0x22, 0x12, 0xff, 0x07, 0x0a, 0x00, 0xff, 0x1a, 0x2b, 0x3f, 0xff, 0x3d, 0x50, 0xa8, 0xff, 0x1e, 0x34, 0xb5, 0xff, 0x2f, 0x2d, 0x95, 0xff, 0x3d, 0x3d, 0xaa, 0xff, 0x50, 0x8d, 0xe8, 0xff, 0x33, 0x7f, 0xcc, 0xff, 0xab, 0xd8, 0xff, 0xff, 0x58, 0x5c, 0x5f, 0xff, 0x15, 0x0d, 0x00, 0xff, 0x33, 0x30, 0x38, 0xff, 0x43, 0x79, 0xc5, 0xff, 0x20, 0x31, 0x6d, 0xff, 0x39, 0x59, 0xa8, 0xff, 0x4b, 0x89, 0xea, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9e, 0x55, 0xed, 0x9f, 0xb8, 0x76, 0xef, 0xff, 0x4f, 0x24, 0x5a, 0xff, 0x49, 0x43, 0x31, 0xff, 0xe7, 0xe2, 0x9c, 0xff, 0xe2, 0xb7, 0x6c, 0xff, 0x5b, 0x2f, 0x06, 0xff, 0x69, 0x53, 0x70, 0xff, 0xeb, 0xc0, 0xff, 0xff, 0x4e, 0x32, 0x46, 0xff, 0x14, 0x1d, 0x0b, 0xff, 0x2d, 0x29, 0x2f, 0xff, 0x2e, 0x29, 0x2a, 0xff, 0x2d, 0x2b, 0x2a, 0xff, 0x31, 0x2a, 0x25, 0xff, 0x29, 0x23, 0x21, 0xff, 0x2c, 0x21, 0x34, 0xff, 0x4a, 0x35, 0x5f, 0xff, 0x25, 0x1f, 0x2a, 0xff, 0x27, 0x2a, 0x24, 0xff, 0x28, 0x27, 0x29, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x29, 0x28, 0x29, 0xff, 0x27, 0x26, 0x29, 0xff, 0x2b, 0x28, 0x2a, 0xff, 0x2c, 0x29, 0x2a, 0xff, 0x26, 0x28, 0x27, 0xff, 0x23, 0x26, 0x23, 0xff, 0x1b, 0x2b, 0x2f, 0xff, 0x2b, 0x3d, 0x47, 0xff, 0x31, 0x37, 0x41, 0xff, 0x1c, 0x1f, 0x2a, 0xff, 0x37, 0x3a, 0x4a, 0xff, 0x37, 0x3b, 0x51, 0xff, 0x2b, 0x31, 0x45, 0xff, 0x32, 0x48, 0x6e, 0xff, 0x40, 0x66, 0x9c, 0xff, 0x52, 0x6d, 0x90, 0xff, 0x26, 0x34, 0x47, 0xff, 0x22, 0x3b, 0x5c, 0xff, 0x48, 0x62, 0x91, 0xff, 0x40, 0x60, 0x8f, 0xff, 0x2c, 0x55, 0x7b, 0xff, 0x27, 0x36, 0x4c, 0xff, 0x27, 0x2a, 0x3d, 0xff, 0x43, 0x63, 0x85, 0xff, 0x66, 0xa6, 0xda, 0xff, 0x60, 0xac, 0xe4, 0xff, 0x21, 0x57, 0x7c, 0xff, 0x14, 0x18, 0x1f, 0xff, 0x3c, 0x29, 0x30, 0xff, 0x45, 0x56, 0x89, 0xff, 0x3e, 0x6a, 0xa1, 0xff, 0x3d, 0x5c, 0x81, 0xff, 0x43, 0x50, 0x7d, 0xff, 0x47, 0x61, 0x94, 0xff, 0x3f, 0x77, 0xa8, 0xff, 0x43, 0x78, 0xa2, 0xff, 0x38, 0x63, 0x95, 0xff, 0x2e, 0x58, 0x91, 0xff, 0x4a, 0x70, 0x9d, 0xff, 0x46, 0x61, 0x85, 0xff, 0x29, 0x37, 0x4e, 0xff, 0x29, 0x28, 0x37, 0xff, 0x3b, 0x3c, 0x48, 0xff, 0x2c, 0x40, 0x5a, 0xff, 0x35, 0x51, 0x78, 0xff, 0x39, 0x45, 0x58, 0xff, 0x26, 0x26, 0x27, 0xff, 0x2d, 0x3a, 0x4f, 0xff, 0x3c, 0x54, 0x77, 0xff, 0x27, 0x37, 0x4b, 0xff, 0x24, 0x2a, 0x2e, 0xff, 0x29, 0x2a, 0x27, 0xff, 0x29, 0x2b, 0x2b, 0xff, 0x2a, 0x28, 0x2b, 0xff, 0x2b, 0x27, 0x28, 0xff, 0x2a, 0x28, 0x26, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x26, 0x28, 0x2a, 0xff, 0x27, 0x2a, 0x23, 0xff, 0x28, 0x2b, 0x24, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x19, 0x16, 0x16, 0xff, 0x13, 0x1a, 0x25, 0xff, 0x3c, 0x40, 0x98, 0xff, 0x3b, 0x2f, 0xa4, 0xff, 0x2f, 0x40, 0x94, 0xff, 0x4b, 0x91, 0xee, 0xff, 0x50, 0x98, 0xda, 0xff, 0xbf, 0xe1, 0xff, 0xff, 0x45, 0x54, 0x53, 0xff, 0x0f, 0x12, 0x07, 0xff, 0x26, 0x27, 0x29, 0xff, 0x4b, 0x7e, 0xcc, 0xff, 0x2d, 0x39, 0x67, 0xff, 0x2e, 0x4e, 0x91, 0xff, 0x3b, 0x8e, 0xeb, 0x9f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x24, 0x91, 0x07, 0xac, 0x74, 0xf7, 0xef, 0xba, 0x76, 0xf8, 0xff, 0x5d, 0x2f, 0x7d, 0xff, 0x25, 0x26, 0x06, 0xff, 0xd4, 0xd8, 0x97, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa9, 0xa1, 0x7e, 0xff, 0x00, 0x00, 0x00, 0xff, 0xbf, 0x90, 0xe2, 0xff, 0xc8, 0x97, 0xd1, 0xff, 0x1b, 0x13, 0x09, 0xff, 0x27, 0x28, 0x23, 0xff, 0x2e, 0x29, 0x2d, 0xff, 0x2f, 0x2a, 0x29, 0xff, 0x2f, 0x26, 0x27, 0xff, 0x38, 0x2d, 0x30, 0xff, 0x55, 0x4b, 0x4d, 0xff, 0x2f, 0x27, 0x24, 0xff, 0x25, 0x25, 0x23, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x28, 0x2b, 0x2a, 0xff, 0x26, 0x29, 0x27, 0xff, 0x2c, 0x2b, 0x2b, 0xff, 0x2f, 0x2e, 0x2e, 0xff, 0x2c, 0x2e, 0x32, 0xff, 0x2c, 0x2d, 0x33, 0xff, 0x29, 0x40, 0x4e, 0xff, 0x35, 0x56, 0x6a, 0xff, 0x15, 0x2a, 0x40, 0xff, 0x28, 0x3a, 0x56, 0xff, 0x35, 0x48, 0x6d, 0xff, 0x38, 0x4c, 0x79, 0xff, 0x2f, 0x4a, 0x74, 0xff, 0x32, 0x5a, 0x8c, 0xff, 0x32, 0x62, 0x9d, 0xff, 0x5a, 0x83, 0xb2, 0xff, 0x62, 0x81, 0xa5, 0xff, 0x2b, 0x4e, 0x77, 0xff, 0x29, 0x51, 0x83, 0xff, 0x37, 0x65, 0x9d, 0xff, 0x4e, 0x7f, 0xb6, 0xff, 0x3c, 0x5c, 0x87, 0xff, 0x29, 0x38, 0x55, 0xff, 0x29, 0x36, 0x4b, 0xff, 0x38, 0x56, 0x80, 0xff, 0x56, 0x8e, 0xc1, 0xff, 0x78, 0xb6, 0xe1, 0xff, 0x5c, 0x7f, 0xad, 0xff, 0x28, 0x35, 0x5e, 0xff, 0x13, 0x2d, 0x43, 0xff, 0x1d, 0x3a, 0x57, 0xff, 0x35, 0x55, 0x7b, 0xff, 0x3b, 0x63, 0x7e, 0xff, 0x4b, 0x61, 0x7f, 0xff, 0x49, 0x54, 0x7d, 0xff, 0x32, 0x4e, 0x80, 0xff, 0x3d, 0x6c, 0x9e, 0xff, 0x48, 0x80, 0xaf, 0xff, 0x45, 0x7d, 0xb2, 0xff, 0x63, 0x94, 0xc7, 0xff, 0x5c, 0x7c, 0xa4, 0xff, 0x32, 0x40, 0x5a, 0xff, 0x36, 0x3a, 0x48, 0xff, 0x39, 0x45, 0x5c, 0xff, 0x2a, 0x45, 0x6f, 0xff, 0x45, 0x5b, 0x7e, 0xff, 0x2f, 0x38, 0x4b, 0xff, 0x1e, 0x23, 0x30, 0xff, 0x4a, 0x63, 0x85, 0xff, 0x41, 0x66, 0x94, 0xff, 0x22, 0x37, 0x4f, 0xff, 0x23, 0x29, 0x2f, 0xff, 0x2c, 0x2c, 0x2b, 0xff, 0x2a, 0x2d, 0x34, 0xff, 0x28, 0x29, 0x2f, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x29, 0x27, 0x26, 0xff, 0x29, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x27, 0x29, 0x27, 0xff, 0x27, 0x2a, 0x25, 0xff, 0x29, 0x28, 0x29, 0xff, 0x2b, 0x26, 0x2c, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2a, 0x2a, 0x2b, 0xff, 0x21, 0x26, 0x0d, 0xff, 0x25, 0x27, 0x2f, 0xff, 0x35, 0x2a, 0x8e, 0xff, 0x2b, 0x4c, 0x9e, 0xff, 0x47, 0xa2, 0xf3, 0xff, 0x6b, 0xbc, 0xff, 0xff, 0xc0, 0xe6, 0xff, 0xff, 0x43, 0x4c, 0x4e, 0xff, 0x12, 0x12, 0x12, 0xff, 0x19, 0x21, 0x22, 0xff, 0x59, 0x7d, 0xb7, 0xff, 0x3a, 0x3c, 0x5e, 0xff, 0x1d, 0x34, 0x5b, 0xff, 0x41, 0x9c, 0xe9, 0xef, 0x48, 0x91, 0xda, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x74, 0x45, 0xa7, 0x46, 0xb9, 0x88, 0xfe, 0xff, 0xb8, 0x73, 0xf6, 0xff, 0x57, 0x29, 0x88, 0xff, 0x0f, 0x11, 0x00, 0xff, 0xb7, 0xb6, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0xda, 0xcb, 0xff, 0x1a, 0x13, 0x00, 0xff, 0x41, 0x2b, 0x3c, 0xff, 0xef, 0xb2, 0xff, 0xff, 0x78, 0x52, 0x6f, 0xff, 0x17, 0x1b, 0x06, 0xff, 0x2c, 0x2d, 0x28, 0xff, 0x30, 0x2b, 0x24, 0xff, 0x2a, 0x1d, 0x24, 0xff, 0x4e, 0x3f, 0x4d, 0xff, 0x70, 0x66, 0x6f, 0xff, 0x24, 0x1e, 0x1f, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x27, 0x2b, 0x29, 0xff, 0x24, 0x29, 0x27, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x2a, 0x2c, 0x2f, 0xff, 0x1f, 0x27, 0x31, 0xff, 0x35, 0x41, 0x50, 0xff, 0x54, 0x68, 0x86, 0xff, 0x35, 0x4c, 0x73, 0xff, 0x22, 0x3a, 0x5e, 0xff, 0x46, 0x65, 0x8e, 0xff, 0x37, 0x5e, 0x8f, 0xff, 0x48, 0x79, 0xb1, 0xff, 0x46, 0x75, 0xae, 0xff, 0x31, 0x60, 0x9e, 0xff, 0x45, 0x7d, 0xbe, 0xff, 0x4c, 0x82, 0xc0, 0xff, 0x69, 0x9b, 0xd4, 0xff, 0x5e, 0x91, 0xcb, 0xff, 0x40, 0x6e, 0xa1, 0xff, 0x2c, 0x5e, 0x93, 0xff, 0x3f, 0x76, 0xbb, 0xff, 0x59, 0x8c, 0xcd, 0xff, 0x3b, 0x65, 0x98, 0xff, 0x28, 0x4c, 0x7f, 0xff, 0x28, 0x41, 0x6c, 0xff, 0x41, 0x4a, 0x6a, 0xff, 0x59, 0x68, 0x97, 0xff, 0x58, 0x91, 0xd4, 0xff, 0x58, 0xac, 0xed, 0xff, 0x50, 0x8f, 0xaa, 0xff, 0x3d, 0x56, 0x73, 0xff, 0x29, 0x31, 0x62, 0xff, 0x2b, 0x3c, 0x5c, 0xff, 0x33, 0x56, 0x67, 0xff, 0x34, 0x59, 0x6a, 0xff, 0x3b, 0x4f, 0x6e, 0xff, 0x3a, 0x48, 0x66, 0xff, 0x3f, 0x58, 0x74, 0xff, 0x3c, 0x64, 0x8f, 0xff, 0x2d, 0x61, 0x93, 0xff, 0x51, 0x82, 0xb0, 0xff, 0x50, 0x7e, 0xa4, 0xff, 0x2b, 0x40, 0x57, 0xff, 0x24, 0x2c, 0x42, 0xff, 0x24, 0x3e, 0x66, 0xff, 0x35, 0x50, 0x79, 0xff, 0x44, 0x55, 0x73, 0xff, 0x22, 0x26, 0x34, 0xff, 0x29, 0x3d, 0x5d, 0xff, 0x50, 0x7c, 0xb4, 0xff, 0x40, 0x64, 0x8e, 0xff, 0x25, 0x39, 0x4f, 0xff, 0x23, 0x28, 0x2d, 0xff, 0x2f, 0x36, 0x46, 0xff, 0x29, 0x31, 0x3f, 0xff, 0x29, 0x2b, 0x2d, 0xff, 0x20, 0x23, 0x27, 0xff, 0x26, 0x28, 0x2a, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x29, 0x26, 0xff, 0x28, 0x2a, 0x24, 0xff, 0x28, 0x27, 0x2b, 0xff, 0x29, 0x25, 0x2d, 0xff, 0x2a, 0x26, 0x29, 0xff, 0x2b, 0x2b, 0x22, 0xff, 0x35, 0x2b, 0x23, 0xff, 0x29, 0x22, 0x0e, 0xff, 0x19, 0x1d, 0x30, 0xff, 0x36, 0x4d, 0xa6, 0xff, 0x48, 0x98, 0xff, 0xff, 0x5d, 0xb4, 0xfa, 0xff, 0xbc, 0xe6, 0xff, 0xff, 0x4f, 0x4c, 0x50, 0xff, 0x19, 0x13, 0x14, 0xff, 0x24, 0x22, 0x25, 0xff, 0x2c, 0x36, 0x61, 0xff, 0x21, 0x1f, 0x44, 0xff, 0x3f, 0x3b, 0x62, 0xff, 0x75, 0xb0, 0xff, 0xff, 0x36, 0x95, 0xf4, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8e, 0x51, 0xc1, 0x8d, 0xc0, 0x89, 0xfd, 0xff, 0xaf, 0x65, 0xe9, 0xff, 0x48, 0x23, 0x85, 0xff, 0x09, 0x0b, 0x06, 0xff, 0x93, 0x7e, 0x53, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0xf5, 0xf0, 0xff, 0x56, 0x4b, 0x32, 0xff, 0x00, 0x00, 0x00, 0xff, 0x8c, 0x63, 0xb5, 0xff, 0xc9, 0x8e, 0xd6, 0xff, 0x24, 0x1f, 0x17, 0xff, 0x25, 0x2b, 0x1d, 0xff, 0x2f, 0x2b, 0x26, 0xff, 0x30, 0x27, 0x2d, 0xff, 0x31, 0x26, 0x31, 0xff, 0x26, 0x1f, 0x29, 0xff, 0x26, 0x23, 0x2b, 0xff, 0x28, 0x28, 0x2a, 0xff, 0x28, 0x29, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x29, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2b, 0x2b, 0x2a, 0xff, 0x28, 0x2b, 0x2a, 0xff, 0x24, 0x29, 0x29, 0xff, 0x2d, 0x2f, 0x30, 0xff, 0x22, 0x28, 0x2e, 0xff, 0x1b, 0x2c, 0x3d, 0xff, 0x3d, 0x5b, 0x76, 0xff, 0x44, 0x5a, 0x7d, 0xff, 0x1d, 0x2a, 0x4c, 0xff, 0x2d, 0x3d, 0x5d, 0xff, 0x3d, 0x52, 0x73, 0xff, 0x33, 0x53, 0x77, 0xff, 0x3f, 0x6d, 0x91, 0xff, 0x4e, 0x77, 0xa4, 0xff, 0x4c, 0x71, 0xa3, 0xff, 0x65, 0x93, 0xc6, 0xff, 0x60, 0x96, 0xd2, 0xff, 0x63, 0x9c, 0xdb, 0xff, 0x7e, 0xb5, 0xf6, 0xff, 0x6e, 0xa3, 0xd6, 0xff, 0x49, 0x7d, 0xb1, 0xff, 0x3f, 0x73, 0xb8, 0xff, 0x4b, 0x87, 0xc8, 0xff, 0x51, 0x8e, 0xcb, 0xff, 0x33, 0x68, 0xb0, 0xff, 0x2f, 0x64, 0x98, 0xff, 0x3d, 0x69, 0x95, 0xff, 0x43, 0x5f, 0x9b, 0xff, 0x30, 0x52, 0x79, 0xff, 0x40, 0x71, 0x8f, 0xff, 0x69, 0x9a, 0xde, 0xff, 0x6b, 0xa6, 0xe6, 0xff, 0x60, 0x9b, 0xc1, 0xff, 0x4d, 0x6c, 0x98, 0xff, 0x2e, 0x36, 0x5e, 0xff, 0x29, 0x31, 0x52, 0xff, 0x39, 0x5b, 0x76, 0xff, 0x45, 0x5f, 0x7b, 0xff, 0x3e, 0x48, 0x69, 0xff, 0x2a, 0x45, 0x6b, 0xff, 0x15, 0x3c, 0x65, 0xff, 0x2b, 0x55, 0x7e, 0xff, 0x50, 0x7c, 0xa5, 0xff, 0x66, 0x82, 0xa2, 0xff, 0x52, 0x61, 0x7c, 0xff, 0x45, 0x5c, 0x7c, 0xff, 0x40, 0x59, 0x7d, 0xff, 0x42, 0x58, 0x7a, 0xff, 0x44, 0x50, 0x65, 0xff, 0x1c, 0x29, 0x3d, 0xff, 0x42, 0x64, 0x90, 0xff, 0x5c, 0x8b, 0xc2, 0xff, 0x36, 0x5b, 0x85, 0xff, 0x23, 0x34, 0x48, 0xff, 0x2d, 0x3c, 0x56, 0xff, 0x2a, 0x3a, 0x53, 0xff, 0x31, 0x3c, 0x48, 0xff, 0x27, 0x30, 0x3c, 0xff, 0x18, 0x1e, 0x26, 0xff, 0x27, 0x2a, 0x2c, 0xff, 0x2c, 0x29, 0x28, 0xff, 0x2b, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2b, 0x27, 0x29, 0xff, 0x27, 0x27, 0x29, 0xff, 0x24, 0x28, 0x29, 0xff, 0x27, 0x29, 0x28, 0xff, 0x29, 0x2a, 0x24, 0xff, 0x2f, 0x27, 0x29, 0xff, 0x32, 0x29, 0x29, 0xff, 0x1d, 0x1d, 0x05, 0xff, 0x2b, 0x32, 0x5f, 0xff, 0x62, 0x9d, 0xff, 0xff, 0x56, 0xb2, 0xf1, 0xff, 0xb3, 0xe4, 0xff, 0xff, 0x56, 0x58, 0x59, 0xff, 0x19, 0x11, 0x0c, 0xff, 0x2c, 0x24, 0x26, 0xff, 0x1a, 0x17, 0x32, 0xff, 0x29, 0x37, 0x74, 0xff, 0xac, 0xa9, 0xe5, 0xff, 0xaa, 0xb6, 0xff, 0xff, 0x4f, 0x8e, 0xee, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9e, 0x4f, 0xd5, 0xd7, 0xbb, 0x7a, 0xf4, 0xff, 0x98, 0x46, 0xcd, 0xff, 0x45, 0x28, 0x89, 0xff, 0x11, 0x10, 0x20, 0xff, 0x77, 0x50, 0x2c, 0xff, 0xee, 0xf5, 0xc9, 0xff, 0xff, 0xff, 0xfd, 0xff, 0x82, 0x73, 0x58, 0xff, 0x05, 0x09, 0x00, 0xff, 0x26, 0x22, 0x30, 0xff, 0x80, 0x42, 0x94, 0xff, 0x42, 0x2a, 0x48, 0xff, 0x21, 0x28, 0x19, 0xff, 0x2e, 0x29, 0x2d, 0xff, 0x30, 0x2c, 0x2a, 0xff, 0x2c, 0x29, 0x25, 0xff, 0x26, 0x26, 0x22, 0xff, 0x26, 0x2c, 0x27, 0xff, 0x26, 0x29, 0x27, 0xff, 0x29, 0x28, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2d, 0x2c, 0x2b, 0xff, 0x2c, 0x2c, 0x2a, 0xff, 0x26, 0x28, 0x27, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x28, 0x28, 0xff, 0x27, 0x2a, 0x2a, 0xff, 0x2b, 0x2d, 0x2c, 0xff, 0x28, 0x2b, 0x2c, 0xff, 0x25, 0x28, 0x2b, 0xff, 0x2f, 0x31, 0x33, 0xff, 0x1d, 0x23, 0x2c, 0xff, 0x3a, 0x52, 0x69, 0xff, 0x41, 0x6c, 0x93, 0xff, 0x1f, 0x3f, 0x58, 0xff, 0x2a, 0x37, 0x44, 0xff, 0x29, 0x31, 0x3d, 0xff, 0x19, 0x1a, 0x24, 0xff, 0x10, 0x12, 0x1c, 0xff, 0x1f, 0x22, 0x2c, 0xff, 0x1e, 0x29, 0x3a, 0xff, 0x35, 0x45, 0x5b, 0xff, 0x41, 0x59, 0x74, 0xff, 0x43, 0x6d, 0x9a, 0xff, 0x4d, 0x81, 0xbc, 0xff, 0x77, 0xa3, 0xdd, 0xff, 0x7c, 0xb4, 0xeb, 0xff, 0x62, 0x9d, 0xda, 0xff, 0x57, 0x85, 0xc7, 0xff, 0x59, 0x92, 0xc8, 0xff, 0x54, 0x94, 0xc3, 0xff, 0x4a, 0x81, 0xbc, 0xff, 0x46, 0x75, 0xb7, 0xff, 0x3d, 0x73, 0xae, 0xff, 0x36, 0x6d, 0xa2, 0xff, 0x49, 0x6e, 0xa2, 0xff, 0x37, 0x52, 0x86, 0xff, 0x2a, 0x4c, 0x7c, 0xff, 0x3f, 0x6d, 0x9b, 0xff, 0x4d, 0x88, 0xb7, 0xff, 0x4a, 0x8b, 0xc0, 0xff, 0x4b, 0x84, 0xc4, 0xff, 0x4f, 0x7f, 0xc1, 0xff, 0x57, 0x7b, 0xb1, 0xff, 0x46, 0x6d, 0xa7, 0xff, 0x44, 0x71, 0xb1, 0xff, 0x56, 0x7b, 0xad, 0xff, 0x57, 0x72, 0x97, 0xff, 0x42, 0x54, 0x74, 0xff, 0x37, 0x40, 0x5e, 0xff, 0x35, 0x49, 0x6a, 0xff, 0x41, 0x5c, 0x79, 0xff, 0x49, 0x5b, 0x73, 0xff, 0x42, 0x55, 0x71, 0xff, 0x30, 0x47, 0x68, 0xff, 0x2e, 0x43, 0x5f, 0xff, 0x3d, 0x45, 0x4e, 0xff, 0x31, 0x45, 0x5d, 0xff, 0x50, 0x86, 0xc8, 0xff, 0x4d, 0x80, 0xbb, 0xff, 0x34, 0x4e, 0x6e, 0xff, 0x2b, 0x3f, 0x5f, 0xff, 0x31, 0x48, 0x69, 0xff, 0x2e, 0x41, 0x58, 0xff, 0x52, 0x60, 0x74, 0xff, 0x21, 0x29, 0x34, 0xff, 0x1f, 0x22, 0x24, 0xff, 0x2d, 0x2b, 0x27, 0xff, 0x2c, 0x29, 0x26, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x27, 0xff, 0x2b, 0x26, 0x2b, 0xff, 0x2c, 0x24, 0x2e, 0xff, 0x26, 0x28, 0x28, 0xff, 0x22, 0x2b, 0x24, 0xff, 0x23, 0x2a, 0x26, 0xff, 0x28, 0x26, 0x2b, 0xff, 0x24, 0x27, 0x2b, 0xff, 0x24, 0x28, 0x29, 0xff, 0x38, 0x24, 0x22, 0xff, 0x19, 0x16, 0x09, 0xff, 0x4b, 0x85, 0xb5, 0xff, 0x61, 0xb5, 0xff, 0xff, 0xa8, 0xe4, 0xff, 0xff, 0x5d, 0x6f, 0x6f, 0xff, 0x10, 0x11, 0x00, 0xff, 0x2b, 0x25, 0x24, 0xff, 0x1c, 0x1d, 0x2f, 0xff, 0x55, 0x7d, 0xdc, 0xff, 0x61, 0x79, 0xc0, 0xff, 0x35, 0x37, 0x3b, 0xff, 0x30, 0x3c, 0x51, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x75, 0x31, 0xa6, 0x1a, 0x94, 0x4c, 0xc9, 0xff, 0x9f, 0x5e, 0xd5, 0xff, 0x78, 0x32, 0xc0, 0xff, 0x58, 0x2d, 0xa7, 0xff, 0x20, 0x1d, 0x32, 0xff, 0x3d, 0x37, 0x02, 0xff, 0xcd, 0xc0, 0x74, 0xff, 0xff, 0xfd, 0xd6, 0xff, 0xa7, 0xa2, 0x75, 0xff, 0x23, 0x17, 0x11, 0xff, 0x16, 0x17, 0x0f, 0xff, 0x5d, 0x3d, 0x6b, 0xff, 0x83, 0x45, 0x8f, 0xff, 0x26, 0x1c, 0x1b, 0xff, 0x26, 0x2f, 0x23, 0xff, 0x27, 0x2d, 0x23, 0xff, 0x24, 0x28, 0x27, 0xff, 0x26, 0x27, 0x2a, 0xff, 0x28, 0x29, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x25, 0x27, 0x2b, 0xff, 0x26, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x2a, 0x27, 0xff, 0x29, 0x29, 0x2b, 0xff, 0x2e, 0x2e, 0x32, 0xff, 0x29, 0x3e, 0x55, 0xff, 0x27, 0x2e, 0x35, 0xff, 0x2c, 0x27, 0x21, 0xff, 0x23, 0x29, 0x2f, 0xff, 0x2f, 0x2b, 0x26, 0xff, 0x28, 0x2e, 0x36, 0xff, 0x2c, 0x28, 0x32, 0xff, 0x2c, 0x30, 0x2d, 0xff, 0x19, 0x24, 0x2d, 0xff, 0x35, 0x43, 0x60, 0xff, 0x52, 0x7d, 0xa8, 0xff, 0x35, 0x56, 0x86, 0xff, 0x68, 0x7d, 0xac, 0xff, 0x92, 0xb1, 0xdf, 0xff, 0x6b, 0x92, 0xbd, 0xff, 0x58, 0x7b, 0xa4, 0xff, 0x4a, 0x5e, 0x84, 0xff, 0x33, 0x37, 0x5d, 0xff, 0x22, 0x23, 0x33, 0xff, 0x18, 0x19, 0x1d, 0xff, 0x0f, 0x11, 0x19, 0xff, 0x17, 0x1d, 0x2c, 0xff, 0x26, 0x32, 0x49, 0xff, 0x35, 0x43, 0x59, 0xff, 0x4e, 0x6e, 0x94, 0xff, 0x60, 0x8c, 0xc2, 0xff, 0x58, 0x83, 0xba, 0xff, 0x60, 0x95, 0xcc, 0xff, 0x5d, 0x98, 0xcd, 0xff, 0x5a, 0x94, 0xca, 0xff, 0x65, 0x9c, 0xd8, 0xff, 0x5b, 0x90, 0xc8, 0xff, 0x53, 0x85, 0xbe, 0xff, 0x38, 0x6a, 0xb3, 0xff, 0x45, 0x79, 0xc6, 0xff, 0x4d, 0x81, 0xb4, 0xff, 0x2a, 0x5b, 0x89, 0xff, 0x2a, 0x50, 0x89, 0xff, 0x46, 0x66, 0x9e, 0xff, 0x48, 0x71, 0x9d, 0xff, 0x49, 0x71, 0x96, 0xff, 0x4e, 0x70, 0x95, 0xff, 0x51, 0x71, 0xa1, 0xff, 0x54, 0x71, 0xa3, 0xff, 0x4d, 0x63, 0x88, 0xff, 0x56, 0x63, 0x7d, 0xff, 0x48, 0x51, 0x61, 0xff, 0x22, 0x28, 0x34, 0xff, 0x00, 0x0e, 0x1c, 0xff, 0x0a, 0x14, 0x25, 0xff, 0x24, 0x19, 0x26, 0xff, 0x23, 0x24, 0x28, 0xff, 0x34, 0x4a, 0x4a, 0xff, 0x22, 0x34, 0x48, 0xff, 0x2d, 0x2c, 0x3b, 0xff, 0x31, 0x33, 0x41, 0xff, 0x2e, 0x53, 0x86, 0xff, 0x57, 0x91, 0xd0, 0xff, 0x49, 0x80, 0xb4, 0xff, 0x2c, 0x4b, 0x74, 0xff, 0x2b, 0x49, 0x76, 0xff, 0x24, 0x3a, 0x51, 0xff, 0x4d, 0x63, 0x7a, 0xff, 0x4b, 0x65, 0x82, 0xff, 0x27, 0x27, 0x20, 0xff, 0x1d, 0x27, 0x26, 0xff, 0x20, 0x29, 0x2d, 0xff, 0x29, 0x27, 0x27, 0xff, 0x26, 0x27, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x29, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x27, 0xff, 0x27, 0x29, 0x28, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x29, 0x29, 0x27, 0xff, 0x27, 0x28, 0x26, 0xff, 0x2b, 0x25, 0x31, 0xff, 0x20, 0x19, 0x0b, 0xff, 0x31, 0x3c, 0x3c, 0xff, 0x57, 0x86, 0xe6, 0xff, 0x89, 0xc7, 0xff, 0xff, 0x68, 0x87, 0x8a, 0xff, 0x05, 0x0b, 0x00, 0xff, 0x37, 0x2d, 0x1b, 0xff, 0x23, 0x1e, 0x21, 0xff, 0x3f, 0x5d, 0xb4, 0xff, 0x2d, 0x50, 0xb6, 0xff, 0x0d, 0x10, 0x1b, 0xff, 0x0a, 0x04, 0x00, 0xff, 0x2f, 0x38, 0x55, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x4e, 0x25, 0x3c, 0x58, 0x55, 0x33, 0x5d, 0xff, 0x57, 0x2c, 0x83, 0xff, 0x5d, 0x2e, 0x96, 0xff, 0x58, 0x2b, 0x90, 0xff, 0x3a, 0x24, 0x59, 0xff, 0x25, 0x23, 0x0e, 0xff, 0xaf, 0xa3, 0x50, 0xff, 0xf8, 0xf2, 0xb3, 0xff, 0xe2, 0xdf, 0xac, 0xff, 0x46, 0x39, 0x21, 0xff, 0x09, 0x08, 0x00, 0xff, 0x65, 0x52, 0x72, 0xff, 0xc4, 0x7e, 0xd7, 0xff, 0x35, 0x1e, 0x2e, 0xff, 0x23, 0x29, 0x1c, 0xff, 0x27, 0x2c, 0x22, 0xff, 0x23, 0x26, 0x29, 0xff, 0x26, 0x25, 0x2f, 0xff, 0x29, 0x26, 0x2a, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x27, 0x27, 0x29, 0xff, 0x28, 0x28, 0x25, 0xff, 0x2a, 0x2a, 0x27, 0xff, 0x29, 0x2d, 0x2f, 0xff, 0x2e, 0x32, 0x3a, 0xff, 0x31, 0x4e, 0x73, 0xff, 0x29, 0x37, 0x45, 0xff, 0x2a, 0x26, 0x20, 0xff, 0x23, 0x29, 0x30, 0xff, 0x33, 0x2c, 0x28, 0xff, 0x2a, 0x2e, 0x38, 0xff, 0x23, 0x25, 0x2f, 0xff, 0x2f, 0x29, 0x31, 0xff, 0x25, 0x3d, 0x55, 0xff, 0x57, 0x81, 0x9d, 0xff, 0x41, 0x60, 0x82, 0xff, 0x51, 0x76, 0xa1, 0xff, 0x9d, 0xc2, 0xf6, 0xff, 0x9e, 0xc0, 0xfc, 0xff, 0x82, 0xb3, 0xf0, 0xff, 0x85, 0xbd, 0xf9, 0xff, 0x8a, 0xbe, 0xf7, 0xff, 0x79, 0xa4, 0xd9, 0xff, 0x5f, 0x89, 0xc0, 0xff, 0x4a, 0x71, 0xa7, 0xff, 0x3e, 0x5a, 0x84, 0xff, 0x33, 0x46, 0x64, 0xff, 0x37, 0x45, 0x59, 0xff, 0x10, 0x24, 0x31, 0xff, 0x0d, 0x18, 0x2e, 0xff, 0x17, 0x20, 0x44, 0xff, 0x25, 0x3c, 0x68, 0xff, 0x47, 0x69, 0xa0, 0xff, 0x69, 0x92, 0xd3, 0xff, 0x76, 0xa4, 0xe5, 0xff, 0x6e, 0xa9, 0xe8, 0xff, 0x67, 0xa5, 0xe1, 0xff, 0x60, 0x91, 0xce, 0xff, 0x52, 0x83, 0xc3, 0xff, 0x47, 0x77, 0xb0, 0xff, 0x49, 0x6e, 0x9a, 0xff, 0x41, 0x57, 0x78, 0xff, 0x30, 0x40, 0x5c, 0xff, 0x33, 0x3f, 0x53, 0xff, 0x32, 0x37, 0x45, 0xff, 0x2a, 0x2f, 0x3b, 0xff, 0x19, 0x21, 0x33, 0xff, 0x18, 0x1d, 0x1f, 0xff, 0x23, 0x24, 0x18, 0xff, 0x16, 0x17, 0x17, 0xff, 0x0d, 0x11, 0x1d, 0xff, 0x2b, 0x34, 0x4e, 0xff, 0x3c, 0x47, 0x6e, 0xff, 0x49, 0x5c, 0x82, 0xff, 0x4e, 0x62, 0x84, 0xff, 0x3c, 0x47, 0x63, 0xff, 0x13, 0x1a, 0x23, 0xff, 0x28, 0x30, 0x2f, 0xff, 0x3f, 0x45, 0x4e, 0xff, 0x20, 0x2c, 0x40, 0xff, 0x20, 0x25, 0x33, 0xff, 0x2e, 0x2d, 0x3a, 0xff, 0x4c, 0x75, 0xa4, 0xff, 0x46, 0x8f, 0xda, 0xff, 0x40, 0x72, 0xa8, 0xff, 0x38, 0x4c, 0x70, 0xff, 0x2f, 0x44, 0x67, 0xff, 0x25, 0x3a, 0x58, 0xff, 0x58, 0x76, 0xa1, 0xff, 0x2f, 0x43, 0x5f, 0xff, 0x27, 0x24, 0x1e, 0xff, 0x29, 0x26, 0x23, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x27, 0xff, 0x27, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2b, 0x29, 0x25, 0xff, 0x2a, 0x28, 0x29, 0xff, 0x22, 0x25, 0x2e, 0xff, 0x31, 0x2a, 0x25, 0xff, 0x1c, 0x13, 0x0c, 0xff, 0x1e, 0x39, 0x7e, 0xff, 0x76, 0xaa, 0xff, 0xff, 0x8d, 0xa4, 0xb1, 0xff, 0x0c, 0x0e, 0x16, 0xff, 0x21, 0x28, 0x31, 0xff, 0x30, 0x20, 0x0d, 0xff, 0x23, 0x29, 0x3e, 0xff, 0x41, 0x74, 0xdf, 0xff, 0x3f, 0x5c, 0xb7, 0xff, 0x29, 0x40, 0x79, 0xff, 0x39, 0x59, 0x93, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x3a, 0x26, 0x25, 0x90, 0x2d, 0x23, 0x2e, 0xff, 0x33, 0x21, 0x59, 0xff, 0x51, 0x2f, 0x77, 0xff, 0x5e, 0x2d, 0x89, 0xff, 0x60, 0x30, 0x92, 0xff, 0x27, 0x11, 0x20, 0xff, 0x8f, 0x8f, 0x42, 0xff, 0xe1, 0xe1, 0x93, 0xff, 0xfd, 0xf7, 0xc7, 0xff, 0x89, 0x87, 0x63, 0xff, 0x04, 0x07, 0x00, 0xff, 0x3e, 0x2a, 0x47, 0xff, 0xcc, 0x92, 0xec, 0xff, 0x61, 0x43, 0x67, 0xff, 0x1d, 0x15, 0x15, 0xff, 0x2c, 0x29, 0x28, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x25, 0x26, 0x2c, 0xff, 0x26, 0x29, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x2d, 0x29, 0x25, 0xff, 0x2b, 0x29, 0x24, 0xff, 0x27, 0x28, 0x26, 0xff, 0x26, 0x2b, 0x30, 0xff, 0x2b, 0x32, 0x3d, 0xff, 0x37, 0x53, 0x77, 0xff, 0x2e, 0x44, 0x5a, 0xff, 0x20, 0x24, 0x26, 0xff, 0x26, 0x29, 0x2d, 0xff, 0x2f, 0x2e, 0x2d, 0xff, 0x2e, 0x30, 0x33, 0xff, 0x23, 0x28, 0x27, 0xff, 0x26, 0x27, 0x3c, 0xff, 0x50, 0x74, 0x9f, 0xff, 0x38, 0x6a, 0x86, 0xff, 0x44, 0x54, 0x70, 0xff, 0x7e, 0xab, 0xe0, 0xff, 0x80, 0xb4, 0xee, 0xff, 0x8d, 0xac, 0xe3, 0xff, 0x86, 0xaa, 0xe7, 0xff, 0x75, 0x9e, 0xde, 0xff, 0x66, 0x98, 0xd8, 0xff, 0x6c, 0xa8, 0xe7, 0xff, 0x73, 0xad, 0xf5, 0xff, 0x74, 0xa6, 0xf2, 0xff, 0x64, 0x93, 0xd7, 0xff, 0x68, 0x95, 0xd3, 0xff, 0x63, 0x91, 0xcd, 0xff, 0x48, 0x81, 0xc0, 0xff, 0x53, 0x7a, 0xa3, 0xff, 0x49, 0x62, 0x7f, 0xff, 0x2e, 0x52, 0x7d, 0xff, 0x45, 0x73, 0xa7, 0xff, 0x64, 0x99, 0xd6, 0xff, 0x67, 0xa0, 0xe3, 0xff, 0x68, 0xa1, 0xe5, 0xff, 0x67, 0x9e, 0xde, 0xff, 0x5b, 0x94, 0xce, 0xff, 0x50, 0x88, 0xbf, 0xff, 0x46, 0x6c, 0x9a, 0xff, 0x29, 0x34, 0x50, 0xff, 0x2a, 0x23, 0x29, 0xff, 0x2b, 0x23, 0x20, 0xff, 0x25, 0x20, 0x22, 0xff, 0x1f, 0x1d, 0x24, 0xff, 0x1d, 0x20, 0x2c, 0xff, 0x23, 0x29, 0x39, 0xff, 0x2a, 0x30, 0x41, 0xff, 0x2f, 0x35, 0x48, 0xff, 0x33, 0x3e, 0x57, 0xff, 0x31, 0x48, 0x6c, 0xff, 0x3e, 0x5f, 0x92, 0xff, 0x47, 0x74, 0xb1, 0xff, 0x6b, 0x93, 0xd7, 0xff, 0x62, 0x8e, 0xd6, 0xff, 0x46, 0x83, 0xc0, 0xff, 0x24, 0x40, 0x69, 0xff, 0x29, 0x20, 0x30, 0xff, 0x34, 0x32, 0x31, 0xff, 0x1b, 0x32, 0x3c, 0xff, 0x26, 0x31, 0x3c, 0xff, 0x35, 0x23, 0x1e, 0xff, 0x2a, 0x35, 0x4e, 0xff, 0x43, 0x7a, 0xbb, 0xff, 0x51, 0x8d, 0xc9, 0xff, 0x4a, 0x69, 0x93, 0xff, 0x39, 0x5a, 0x95, 0xff, 0x1f, 0x32, 0x57, 0xff, 0x3c, 0x4a, 0x68, 0xff, 0x41, 0x69, 0xa6, 0xff, 0x34, 0x34, 0x43, 0xff, 0x31, 0x26, 0x1d, 0xff, 0x27, 0x28, 0x26, 0xff, 0x2b, 0x2a, 0x29, 0xff, 0x29, 0x28, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x27, 0x27, 0x29, 0xff, 0x26, 0x25, 0x2d, 0xff, 0x31, 0x2c, 0x24, 0xff, 0x21, 0x20, 0x15, 0xff, 0x19, 0x24, 0x3c, 0xff, 0x51, 0x8c, 0xdb, 0xff, 0x93, 0xcb, 0xff, 0xff, 0x52, 0x46, 0x43, 0xff, 0x11, 0x0e, 0x19, 0xff, 0x2b, 0x2a, 0x25, 0xff, 0x1d, 0x1c, 0x09, 0xff, 0x35, 0x5b, 0x88, 0xff, 0x5c, 0x96, 0xff, 0xff, 0x41, 0x75, 0xe2, 0xff, 0x2e, 0x74, 0xdd, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x79, 0x53, 0x95, 0xc6, 0x1a, 0x1e, 0x19, 0xff, 0x36, 0x2d, 0x57, 0xff, 0x58, 0x2c, 0x91, 0xff, 0x85, 0x41, 0xc6, 0xff, 0x76, 0x3c, 0xaa, 0xff, 0x21, 0x0d, 0x0f, 0xff, 0x7a, 0x74, 0x37, 0xff, 0xe1, 0xdf, 0x8c, 0xff, 0xef, 0xea, 0xbf, 0xff, 0xee, 0xf5, 0xd0, 0xff, 0x37, 0x3a, 0x20, 0xff, 0x04, 0x00, 0x00, 0xff, 0xb5, 0x86, 0xd1, 0xff, 0x8d, 0x6c, 0x9c, 0xff, 0x1a, 0x0e, 0x14, 0xff, 0x2e, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x25, 0x27, 0x2a, 0xff, 0x26, 0x29, 0x28, 0xff, 0x27, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x2d, 0x29, 0x25, 0xff, 0x2d, 0x2a, 0x24, 0xff, 0x27, 0x29, 0x26, 0xff, 0x24, 0x28, 0x2f, 0xff, 0x28, 0x2e, 0x3a, 0xff, 0x42, 0x5e, 0x80, 0xff, 0x3b, 0x56, 0x77, 0xff, 0x1b, 0x22, 0x2a, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x2a, 0x2f, 0x36, 0xff, 0x31, 0x31, 0x34, 0xff, 0x22, 0x1e, 0x1a, 0xff, 0x37, 0x55, 0x68, 0xff, 0x45, 0x6e, 0x9d, 0xff, 0x16, 0x2b, 0x59, 0xff, 0x60, 0x7b, 0xad, 0xff, 0x7c, 0x9f, 0xf1, 0xff, 0x68, 0x88, 0xd5, 0xff, 0x52, 0x6c, 0xab, 0xff, 0x52, 0x62, 0xa9, 0xff, 0x4d, 0x57, 0xa4, 0xff, 0x43, 0x50, 0xa1, 0xff, 0x40, 0x53, 0xa8, 0xff, 0x3f, 0x60, 0xac, 0xff, 0x37, 0x5d, 0x9f, 0xff, 0x33, 0x52, 0x94, 0xff, 0x29, 0x48, 0x86, 0xff, 0x3b, 0x5f, 0x9b, 0xff, 0x6b, 0x92, 0xcf, 0xff, 0x79, 0xaf, 0xeb, 0xff, 0x75, 0xb4, 0xed, 0xff, 0x72, 0xac, 0xe4, 0xff, 0x6b, 0xa5, 0xde, 0xff, 0x6a, 0xa4, 0xdc, 0xff, 0x69, 0xa5, 0xdf, 0xff, 0x6f, 0xa2, 0xdf, 0xff, 0x68, 0x98, 0xd4, 0xff, 0x50, 0x8a, 0xc5, 0xff, 0x42, 0x7f, 0xbe, 0xff, 0x34, 0x61, 0x9a, 0xff, 0x26, 0x35, 0x5f, 0xff, 0x2f, 0x2d, 0x3c, 0xff, 0x2e, 0x2c, 0x31, 0xff, 0x24, 0x28, 0x37, 0xff, 0x2a, 0x35, 0x4b, 0xff, 0x3f, 0x4f, 0x6d, 0xff, 0x41, 0x55, 0x73, 0xff, 0x3a, 0x4a, 0x79, 0xff, 0x39, 0x45, 0x7e, 0xff, 0x33, 0x43, 0x77, 0xff, 0x33, 0x4b, 0x7e, 0xff, 0x2f, 0x50, 0x83, 0xff, 0x27, 0x51, 0x83, 0xff, 0x40, 0x63, 0xa3, 0xff, 0x36, 0x67, 0xb7, 0xff, 0x2a, 0x75, 0xc0, 0xff, 0x3e, 0x62, 0x9d, 0xff, 0x3a, 0x30, 0x4c, 0xff, 0x2c, 0x2a, 0x27, 0xff, 0x1c, 0x29, 0x1e, 0xff, 0x2c, 0x32, 0x33, 0xff, 0x31, 0x2e, 0x3b, 0xff, 0x1e, 0x1a, 0x26, 0xff, 0x26, 0x34, 0x45, 0xff, 0x4c, 0x7b, 0xa8, 0xff, 0x4c, 0x8a, 0xd5, 0xff, 0x42, 0x79, 0xbf, 0xff, 0x3b, 0x5b, 0x82, 0xff, 0x1d, 0x24, 0x2d, 0xff, 0x43, 0x64, 0x8f, 0xff, 0x31, 0x5a, 0x90, 0xff, 0x2e, 0x34, 0x3c, 0xff, 0x25, 0x27, 0x29, 0xff, 0x23, 0x25, 0x27, 0xff, 0x30, 0x2e, 0x2d, 0xff, 0x2b, 0x2a, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x27, 0x2c, 0xff, 0x2c, 0x29, 0x23, 0xff, 0x2c, 0x2d, 0x20, 0xff, 0x15, 0x0f, 0x09, 0xff, 0x2a, 0x5c, 0x8e, 0xff, 0x65, 0xce, 0xff, 0xff, 0x72, 0x85, 0x9b, 0xff, 0x29, 0x0b, 0x00, 0xff, 0x2d, 0x29, 0x26, 0xff, 0x1f, 0x22, 0x25, 0xff, 0x15, 0x1a, 0x11, 0xff, 0x4c, 0x74, 0x94, 0xff, 0x8d, 0xb8, 0xfb, 0xff, 0x68, 0xaa, 0xf3, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0xff, 0xff, 0xff, 0x01, 0x83, 0x5f, 0x9f, 0xf2, 0x0d, 0x1e, 0x00, 0xff, 0x2f, 0x1d, 0x54, 0xff, 0x63, 0x29, 0xa9, 0xff, 0x9b, 0x57, 0xe1, 0xff, 0x72, 0x3c, 0xa0, 0xff, 0x27, 0x10, 0x0d, 0xff, 0x6c, 0x58, 0x23, 0xff, 0xe2, 0xdd, 0x8d, 0xff, 0xd8, 0xd9, 0xbb, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xb8, 0xb8, 0xa4, 0xff, 0x16, 0x04, 0x00, 0xff, 0xaf, 0x8c, 0xb3, 0xff, 0xd9, 0xbe, 0xed, 0xff, 0x28, 0x20, 0x28, 0xff, 0x23, 0x24, 0x1f, 0xff, 0x26, 0x29, 0x25, 0xff, 0x26, 0x28, 0x29, 0xff, 0x28, 0x29, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2c, 0x2b, 0x27, 0xff, 0x28, 0x29, 0x26, 0xff, 0x21, 0x26, 0x29, 0xff, 0x2a, 0x2e, 0x37, 0xff, 0x3b, 0x52, 0x6e, 0xff, 0x3b, 0x5b, 0x83, 0xff, 0x22, 0x2b, 0x38, 0xff, 0x2b, 0x2b, 0x2c, 0xff, 0x23, 0x30, 0x40, 0xff, 0x27, 0x27, 0x2c, 0xff, 0x2d, 0x30, 0x39, 0xff, 0x4c, 0x72, 0x87, 0xff, 0x19, 0x32, 0x4f, 0xff, 0x35, 0x3b, 0x70, 0xff, 0x5a, 0x7c, 0xbd, 0xff, 0x60, 0x72, 0xc0, 0xff, 0x46, 0x50, 0x75, 0xff, 0x0e, 0x23, 0x2b, 0xff, 0x15, 0x22, 0x31, 0xff, 0x1e, 0x25, 0x34, 0xff, 0x28, 0x2a, 0x3d, 0xff, 0x20, 0x1f, 0x39, 0xff, 0x25, 0x24, 0x39, 0xff, 0x27, 0x27, 0x38, 0xff, 0x30, 0x30, 0x4b, 0xff, 0x2e, 0x36, 0x5c, 0xff, 0x1b, 0x32, 0x63, 0xff, 0x2d, 0x4c, 0x85, 0xff, 0x62, 0x8e, 0xd4, 0xff, 0x83, 0xb3, 0xfc, 0xff, 0x8e, 0xb8, 0xf8, 0xff, 0x8b, 0xb1, 0xed, 0xff, 0x81, 0xa8, 0xdf, 0xff, 0x84, 0xa8, 0xde, 0xff, 0x78, 0xa4, 0xdc, 0xff, 0x61, 0x94, 0xce, 0xff, 0x48, 0x7c, 0xba, 0xff, 0x35, 0x6b, 0xb1, 0xff, 0x2a, 0x56, 0x97, 0xff, 0x32, 0x49, 0x7a, 0xff, 0x2e, 0x35, 0x4a, 0xff, 0x23, 0x29, 0x35, 0xff, 0x20, 0x2d, 0x42, 0xff, 0x2b, 0x3b, 0x56, 0xff, 0x2a, 0x3c, 0x5a, 0xff, 0x1e, 0x33, 0x54, 0xff, 0x20, 0x2f, 0x42, 0xff, 0x27, 0x2e, 0x36, 0xff, 0x21, 0x26, 0x31, 0xff, 0x33, 0x32, 0x43, 0xff, 0x2f, 0x2b, 0x45, 0xff, 0x2d, 0x25, 0x44, 0xff, 0x30, 0x34, 0x56, 0xff, 0x29, 0x48, 0x77, 0xff, 0x2d, 0x59, 0x9c, 0xff, 0x41, 0x63, 0xa2, 0xff, 0x36, 0x3f, 0x5f, 0xff, 0x25, 0x26, 0x29, 0xff, 0x2e, 0x2b, 0x1b, 0xff, 0x24, 0x25, 0x22, 0xff, 0x1c, 0x25, 0x3c, 0xff, 0x32, 0x2f, 0x30, 0xff, 0x2a, 0x24, 0x16, 0xff, 0x1c, 0x2e, 0x41, 0xff, 0x39, 0x6e, 0xaf, 0xff, 0x57, 0x91, 0xc8, 0xff, 0x67, 0x98, 0xc9, 0xff, 0x3b, 0x55, 0x70, 0xff, 0x13, 0x26, 0x39, 0xff, 0x38, 0x6d, 0xa7, 0xff, 0x2b, 0x4a, 0x6c, 0xff, 0x26, 0x2f, 0x3b, 0xff, 0x1f, 0x26, 0x2e, 0xff, 0x2b, 0x2c, 0x2d, 0xff, 0x2f, 0x2d, 0x2c, 0xff, 0x26, 0x26, 0x26, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x26, 0xff, 0x27, 0x29, 0x26, 0xff, 0x29, 0x27, 0x2b, 0xff, 0x27, 0x27, 0x29, 0xff, 0x26, 0x2a, 0x25, 0xff, 0x23, 0x1c, 0x13, 0xff, 0x29, 0x31, 0x35, 0xff, 0x39, 0x8d, 0xd9, 0xff, 0x53, 0xa5, 0xff, 0xff, 0x4a, 0x50, 0x59, 0xff, 0x1d, 0x0f, 0x00, 0xff, 0x24, 0x26, 0x35, 0xff, 0x27, 0x21, 0x1e, 0xff, 0x18, 0x1e, 0x09, 0xff, 0x51, 0x64, 0x67, 0xff, 0x8e, 0xad, 0xbe, 0xf2, 0xff, 0xff, 0xff, 0x01,
    0xc1, 0x92, 0xd8, 0x21, 0x66, 0x43, 0x78, 0xff, 0x18, 0x11, 0x27, 0xff, 0x62, 0x38, 0x9b, 0xff, 0xbb, 0x86, 0xe5, 0xff, 0xce, 0xa0, 0xf6, 0xff, 0xba, 0x8d, 0xe7, 0xff, 0x52, 0x2b, 0x48, 0xff, 0x7d, 0x5e, 0x22, 0xff, 0xe8, 0xe3, 0x92, 0xff, 0xdf, 0xe5, 0xd9, 0xff, 0xdf, 0xed, 0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x88, 0x74, 0x66, 0xff, 0xa9, 0x94, 0x92, 0xff, 0xd3, 0xc5, 0xe6, 0xff, 0x13, 0x11, 0x1b, 0xff, 0x20, 0x27, 0x1c, 0xff, 0x24, 0x2b, 0x24, 0xff, 0x26, 0x28, 0x29, 0xff, 0x29, 0x27, 0x29, 0xff, 0x28, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x25, 0x27, 0x2b, 0xff, 0x27, 0x27, 0x2b, 0xff, 0x2a, 0x2a, 0x29, 0xff, 0x28, 0x29, 0x26, 0xff, 0x26, 0x28, 0x2a, 0xff, 0x2b, 0x2a, 0x30, 0xff, 0x26, 0x39, 0x50, 0xff, 0x3f, 0x64, 0x8d, 0xff, 0x36, 0x41, 0x4d, 0xff, 0x2c, 0x2b, 0x2a, 0xff, 0x1e, 0x30, 0x48, 0xff, 0x24, 0x27, 0x2d, 0xff, 0x3e, 0x57, 0x73, 0xff, 0x37, 0x47, 0x68, 0xff, 0x21, 0x1e, 0x27, 0xff, 0x3a, 0x5a, 0x80, 0xff, 0x37, 0x54, 0x90, 0xff, 0x24, 0x2f, 0x4e, 0xff, 0x27, 0x1f, 0x25, 0xff, 0x38, 0x2c, 0x31, 0xff, 0x32, 0x35, 0x32, 0xff, 0x21, 0x2a, 0x20, 0xff, 0x21, 0x26, 0x1c, 0xff, 0x2a, 0x25, 0x1f, 0xff, 0x2a, 0x24, 0x1e, 0xff, 0x29, 0x25, 0x20, 0xff, 0x28, 0x23, 0x24, 0xff, 0x27, 0x27, 0x36, 0xff, 0x29, 0x33, 0x4f, 0xff, 0x1c, 0x2c, 0x4f, 0xff, 0x2d, 0x4e, 0x7f, 0xff, 0x7d, 0xa7, 0xdd, 0xff, 0xa4, 0xcc, 0xfb, 0xff, 0x9b, 0xc8, 0xf8, 0xff, 0x86, 0xba, 0xee, 0xff, 0x77, 0xaf, 0xe5, 0xff, 0x67, 0xa5, 0xe0, 0xff, 0x55, 0x90, 0xcd, 0xff, 0x4b, 0x79, 0xb9, 0xff, 0x3f, 0x66, 0xa8, 0xff, 0x3c, 0x58, 0x8f, 0xff, 0x34, 0x42, 0x61, 0xff, 0x28, 0x28, 0x2e, 0xff, 0x2b, 0x29, 0x2a, 0xff, 0x28, 0x2b, 0x31, 0xff, 0x24, 0x28, 0x2f, 0xff, 0x23, 0x25, 0x2a, 0xff, 0x28, 0x25, 0x23, 0xff, 0x27, 0x27, 0x26, 0xff, 0x20, 0x22, 0x25, 0xff, 0x2e, 0x2e, 0x2c, 0xff, 0x39, 0x38, 0x32, 0xff, 0x23, 0x22, 0x1d, 0xff, 0x25, 0x26, 0x24, 0xff, 0x24, 0x27, 0x1f, 0xff, 0x29, 0x29, 0x33, 0xff, 0x33, 0x39, 0x6e, 0xff, 0x3a, 0x55, 0x8f, 0xff, 0x28, 0x48, 0x67, 0xff, 0x26, 0x27, 0x2f, 0xff, 0x31, 0x29, 0x28, 0xff, 0x26, 0x26, 0x28, 0xff, 0x28, 0x26, 0x28, 0xff, 0x27, 0x2c, 0x26, 0xff, 0x28, 0x30, 0x29, 0xff, 0x27, 0x1f, 0x22, 0xff, 0x22, 0x22, 0x2d, 0xff, 0x42, 0x68, 0x8b, 0xff, 0x62, 0x9c, 0xe0, 0xff, 0x5d, 0x9b, 0xe6, 0xff, 0x2e, 0x42, 0x5c, 0xff, 0x26, 0x34, 0x4a, 0xff, 0x48, 0x6d, 0x9e, 0xff, 0x30, 0x43, 0x59, 0xff, 0x27, 0x33, 0x40, 0xff, 0x24, 0x26, 0x29, 0xff, 0x29, 0x26, 0x24, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x27, 0x29, 0x25, 0xff, 0x28, 0x2b, 0x22, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x25, 0x26, 0x30, 0xff, 0x21, 0x28, 0x2d, 0xff, 0x30, 0x2b, 0x28, 0xff, 0x23, 0x0f, 0x02, 0xff, 0x49, 0x60, 0x84, 0xff, 0x49, 0x9d, 0xff, 0xff, 0x45, 0x95, 0xeb, 0xff, 0x21, 0x41, 0x54, 0xff, 0x0d, 0x0d, 0x06, 0xff, 0x29, 0x17, 0x18, 0xff, 0x2c, 0x1a, 0x20, 0xff, 0x09, 0x0a, 0x00, 0xff, 0x12, 0x13, 0x0b, 0xff, 0x3d, 0x36, 0x3d, 0x21,
    0xbe, 0x8b, 0xcc, 0x4b, 0xba, 0x77, 0xea, 0xff, 0x40, 0x1b, 0x71, 0xff, 0x9c, 0x5f, 0xc6, 0xff, 0xee, 0xd8, 0xff, 0xff, 0xdc, 0xc4, 0xff, 0xff, 0xa6, 0x79, 0xd0, 0xff, 0x25, 0x10, 0x2e, 0xff, 0x65, 0x54, 0x23, 0xff, 0xe8, 0xe4, 0x99, 0xff, 0xde, 0xe1, 0xb5, 0xff, 0xd3, 0xd5, 0xbd, 0xff, 0xef, 0xf1, 0xcc, 0xff, 0xde, 0xde, 0xa0, 0xff, 0x91, 0x77, 0x71, 0xff, 0x36, 0x29, 0x3b, 0xff, 0x15, 0x19, 0x19, 0xff, 0x28, 0x2b, 0x27, 0xff, 0x27, 0x29, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x2b, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x29, 0x2a, 0x2b, 0xff, 0x27, 0x29, 0x29, 0xff, 0x26, 0x28, 0x27, 0xff, 0x27, 0x2a, 0x27, 0xff, 0x26, 0x29, 0x29, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x2a, 0x2a, 0x28, 0xff, 0x23, 0x2f, 0x3f, 0xff, 0x39, 0x56, 0x7d, 0xff, 0x31, 0x42, 0x58, 0xff, 0x2f, 0x2b, 0x29, 0xff, 0x2a, 0x38, 0x4c, 0xff, 0x26, 0x31, 0x41, 0xff, 0x33, 0x44, 0x60, 0xff, 0x29, 0x2e, 0x37, 0xff, 0x2c, 0x26, 0x32, 0xff, 0x38, 0x44, 0x74, 0xff, 0x23, 0x33, 0x4d, 0xff, 0x19, 0x1c, 0x16, 0xff, 0x32, 0x2a, 0x2c, 0xff, 0x24, 0x1a, 0x27, 0xff, 0x20, 0x17, 0x25, 0xff, 0x1f, 0x18, 0x24, 0xff, 0x27, 0x22, 0x28, 0xff, 0x2c, 0x29, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x23, 0x23, 0x29, 0xff, 0x32, 0x30, 0x33, 0xff, 0x35, 0x36, 0x35, 0xff, 0x20, 0x24, 0x2b, 0xff, 0x31, 0x33, 0x48, 0xff, 0x1e, 0x2c, 0x4c, 0xff, 0x4d, 0x72, 0xa1, 0xff, 0x9a, 0xc8, 0xff, 0xff, 0xb6, 0xda, 0xfc, 0xff, 0xb4, 0xd7, 0xef, 0xff, 0x7f, 0xbc, 0xef, 0xff, 0x5f, 0xa2, 0xe3, 0xff, 0x51, 0x8a, 0xc5, 0xff, 0x4c, 0x83, 0xc0, 0xff, 0x48, 0x70, 0xaf, 0xff, 0x3e, 0x52, 0x80, 0xff, 0x2d, 0x2e, 0x3e, 0xff, 0x28, 0x2a, 0x21, 0xff, 0x2b, 0x2c, 0x28, 0xff, 0x2d, 0x25, 0x30, 0xff, 0x26, 0x26, 0x26, 0xff, 0x22, 0x29, 0x1c, 0xff, 0x31, 0x29, 0x26, 0xff, 0x2c, 0x23, 0x29, 0xff, 0x26, 0x24, 0x28, 0xff, 0x36, 0x37, 0x37, 0xff, 0x2a, 0x2e, 0x2b, 0xff, 0x24, 0x28, 0x24, 0xff, 0x1d, 0x21, 0x1b, 0xff, 0x1c, 0x27, 0x24, 0xff, 0x20, 0x26, 0x26, 0xff, 0x33, 0x2b, 0x37, 0xff, 0x36, 0x41, 0x65, 0xff, 0x26, 0x41, 0x6a, 0xff, 0x24, 0x2a, 0x38, 0xff, 0x31, 0x2a, 0x24, 0xff, 0x29, 0x29, 0x28, 0xff, 0x26, 0x2a, 0x2a, 0xff, 0x22, 0x29, 0x28, 0xff, 0x24, 0x26, 0x26, 0xff, 0x30, 0x2d, 0x2c, 0xff, 0x30, 0x22, 0x27, 0xff, 0x1f, 0x1d, 0x31, 0xff, 0x3f, 0x64, 0x89, 0xff, 0x53, 0x9b, 0xd7, 0xff, 0x67, 0xa2, 0xd8, 0xff, 0x3d, 0x45, 0x61, 0xff, 0x2e, 0x3d, 0x60, 0xff, 0x3d, 0x5e, 0x87, 0xff, 0x2b, 0x3a, 0x51, 0xff, 0x35, 0x38, 0x3d, 0xff, 0x27, 0x27, 0x23, 0xff, 0x25, 0x28, 0x26, 0xff, 0x26, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x29, 0x26, 0xff, 0x29, 0x28, 0x29, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x26, 0x27, 0x29, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x1c, 0x13, 0x0d, 0xff, 0x3c, 0x45, 0x54, 0xff, 0x57, 0x98, 0xe0, 0xff, 0x41, 0x8e, 0xf9, 0xff, 0x4b, 0xa5, 0xef, 0xff, 0x3f, 0x69, 0x86, 0xff, 0x3c, 0x2e, 0x40, 0xff, 0x2a, 0x21, 0x36, 0xff, 0x2b, 0x29, 0x36, 0xff, 0x24, 0x1e, 0x29, 0xff, 0x11, 0x06, 0x0d, 0x4b,
    0xb0, 0x85, 0xb2, 0x6b, 0xf7, 0xb4, 0xff, 0xff, 0x3a, 0x1a, 0x61, 0xff, 0x6c, 0x3c, 0x94, 0xff, 0xd1, 0xa9, 0xff, 0xff, 0xd8, 0x9f, 0xff, 0xff, 0x46, 0x21, 0x6c, 0xff, 0x00, 0x0e, 0x0d, 0xff, 0x40, 0x2f, 0x20, 0xff, 0xbe, 0xa6, 0x51, 0xff, 0xe0, 0xd5, 0x69, 0xff, 0xe0, 0xd3, 0x85, 0xff, 0xdd, 0xd8, 0x95, 0xff, 0xf9, 0xff, 0xbb, 0xff, 0x5c, 0x51, 0x4c, 0xff, 0x03, 0x00, 0x06, 0xff, 0x2c, 0x2d, 0x2b, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x27, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x29, 0x28, 0xff, 0x2d, 0x2e, 0x31, 0xff, 0x2f, 0x31, 0x35, 0xff, 0x29, 0x2b, 0x2c, 0xff, 0x27, 0x29, 0x29, 0xff, 0x26, 0x28, 0x28, 0xff, 0x27, 0x29, 0x28, 0xff, 0x27, 0x29, 0x28, 0xff, 0x27, 0x29, 0x28, 0xff, 0x26, 0x29, 0x29, 0xff, 0x25, 0x27, 0x28, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x2b, 0x2c, 0x29, 0xff, 0x23, 0x27, 0x2e, 0xff, 0x31, 0x45, 0x66, 0xff, 0x24, 0x39, 0x59, 0xff, 0x26, 0x27, 0x2b, 0xff, 0x2b, 0x38, 0x4d, 0xff, 0x2b, 0x37, 0x4b, 0xff, 0x28, 0x30, 0x3f, 0xff, 0x22, 0x2b, 0x21, 0xff, 0x27, 0x2e, 0x41, 0xff, 0x34, 0x39, 0x6f, 0xff, 0x1e, 0x2f, 0x3a, 0xff, 0x29, 0x28, 0x1a, 0xff, 0x1f, 0x1c, 0x1c, 0xff, 0x50, 0x53, 0x56, 0xff, 0x4d, 0x48, 0x4a, 0xff, 0x3a, 0x34, 0x35, 0xff, 0x21, 0x1d, 0x1d, 0xff, 0x23, 0x23, 0x22, 0xff, 0x24, 0x28, 0x26, 0xff, 0x1b, 0x1e, 0x21, 0xff, 0x1d, 0x1e, 0x22, 0xff, 0x25, 0x2a, 0x25, 0xff, 0x23, 0x29, 0x26, 0xff, 0x2c, 0x2c, 0x3a, 0xff, 0x27, 0x2f, 0x51, 0xff, 0x2e, 0x51, 0x8b, 0xff, 0x8f, 0xbb, 0xfb, 0xff, 0xc3, 0xdd, 0xfe, 0xff, 0xd6, 0xe7, 0xf5, 0xff, 0x95, 0xc5, 0xf2, 0xff, 0x63, 0x9d, 0xde, 0xff, 0x5c, 0x90, 0xca, 0xff, 0x66, 0xa0, 0xdb, 0xff, 0x4a, 0x76, 0xb6, 0xff, 0x2f, 0x43, 0x79, 0xff, 0x22, 0x29, 0x41, 0xff, 0x23, 0x29, 0x2a, 0xff, 0x27, 0x29, 0x25, 0xff, 0x2d, 0x27, 0x28, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x1c, 0x24, 0x24, 0xff, 0x23, 0x20, 0x23, 0xff, 0x2b, 0x26, 0x28, 0xff, 0x2b, 0x2d, 0x2b, 0xff, 0x31, 0x32, 0x31, 0xff, 0x23, 0x24, 0x25, 0xff, 0x1b, 0x1b, 0x1e, 0xff, 0x34, 0x30, 0x38, 0xff, 0x2f, 0x32, 0x34, 0xff, 0x25, 0x27, 0x1c, 0xff, 0x2b, 0x23, 0x21, 0xff, 0x2f, 0x36, 0x57, 0xff, 0x27, 0x3b, 0x6c, 0xff, 0x25, 0x2d, 0x40, 0xff, 0x31, 0x2d, 0x26, 0xff, 0x27, 0x29, 0x26, 0xff, 0x1d, 0x24, 0x28, 0xff, 0x25, 0x29, 0x2b, 0xff, 0x2c, 0x29, 0x26, 0xff, 0x27, 0x26, 0x29, 0xff, 0x27, 0x2a, 0x32, 0xff, 0x24, 0x25, 0x27, 0xff, 0x18, 0x1e, 0x28, 0xff, 0x38, 0x54, 0x7c, 0xff, 0x61, 0x97, 0xd8, 0xff, 0x70, 0xa2, 0xd8, 0xff, 0x39, 0x5d, 0x87, 0xff, 0x27, 0x4c, 0x7c, 0xff, 0x2b, 0x45, 0x68, 0xff, 0x28, 0x37, 0x4a, 0xff, 0x34, 0x3a, 0x3f, 0xff, 0x26, 0x28, 0x26, 0xff, 0x29, 0x29, 0x26, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x26, 0xff, 0x28, 0x28, 0x27, 0xff, 0x27, 0x28, 0x27, 0xff, 0x21, 0x1e, 0x1c, 0xff, 0x27, 0x28, 0x30, 0xff, 0x85, 0xb4, 0xd8, 0xff, 0x8d, 0xbf, 0xfc, 0xff, 0x79, 0xbc, 0xf6, 0xff, 0xa2, 0xca, 0xff, 0xff, 0x86, 0xb8, 0xde, 0xff, 0x4a, 0xa4, 0xb9, 0xff, 0x5e, 0x9a, 0xb6, 0xff, 0x9f, 0xbe, 0xd7, 0xff, 0x7e, 0x87, 0x96, 0x6b,
    0x6e, 0x54, 0x75, 0x8b, 0x86, 0x60, 0x9d, 0xff, 0x32, 0x1c, 0x45, 0xff, 0x5b, 0x30, 0x88, 0xff, 0x7f, 0x3f, 0xa8, 0xff, 0xcd, 0x7e, 0xef, 0xff, 0x69, 0x46, 0x80, 0xff, 0x09, 0x15, 0x06, 0xff, 0x3a, 0x2b, 0x2c, 0xff, 0xa5, 0x83, 0x35, 0xff, 0xb8, 0x9f, 0x1d, 0xff, 0xbb, 0xa6, 0x32, 0xff, 0xff, 0xee, 0xa5, 0xff, 0x99, 0x95, 0x71, 0xff, 0x09, 0x0f, 0x0b, 0xff, 0x24, 0x26, 0x2a, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x28, 0x2a, 0x2b, 0xff, 0x2c, 0x2e, 0x30, 0xff, 0x28, 0x2a, 0x2b, 0xff, 0x28, 0x2a, 0x2b, 0xff, 0x27, 0x29, 0x29, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x27, 0x29, 0x29, 0xff, 0x26, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x27, 0x29, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x33, 0x30, 0x2c, 0xff, 0x3b, 0x48, 0x5e, 0xff, 0x31, 0x49, 0x6d, 0xff, 0x2f, 0x39, 0x49, 0xff, 0x2e, 0x40, 0x5d, 0xff, 0x36, 0x3d, 0x4c, 0xff, 0x29, 0x2e, 0x33, 0xff, 0x25, 0x30, 0x33, 0xff, 0x30, 0x44, 0x62, 0xff, 0x33, 0x4b, 0x7f, 0xff, 0x21, 0x35, 0x4e, 0xff, 0x24, 0x26, 0x20, 0xff, 0x0f, 0x07, 0x0a, 0xff, 0xac, 0xa7, 0xae, 0xff, 0xed, 0xeb, 0xe3, 0xff, 0xa8, 0xa4, 0x96, 0xff, 0x72, 0x6f, 0x66, 0xff, 0x2a, 0x25, 0x29, 0xff, 0x44, 0x47, 0x41, 0xff, 0x30, 0x35, 0x2c, 0xff, 0x34, 0x34, 0x33, 0xff, 0x38, 0x3a, 0x35, 0xff, 0x24, 0x27, 0x23, 0xff, 0x1d, 0x16, 0x1a, 0xff, 0x26, 0x33, 0x58, 0xff, 0x5a, 0x84, 0xc4, 0xff, 0x98, 0xc1, 0xfd, 0xff, 0xba, 0xd3, 0xf6, 0xff, 0xd3, 0xe8, 0xfd, 0xff, 0xa0, 0xc9, 0xf1, 0xff, 0x65, 0x98, 0xd3, 0xff, 0x6f, 0xa0, 0xdb, 0xff, 0x83, 0xb5, 0xf0, 0xff, 0x45, 0x6f, 0xb2, 0xff, 0x27, 0x43, 0x81, 0xff, 0x2b, 0x40, 0x6c, 0xff, 0x25, 0x2d, 0x48, 0xff, 0x23, 0x21, 0x21, 0xff, 0x1b, 0x1c, 0x09, 0xff, 0x5e, 0x5d, 0x60, 0xff, 0x8e, 0x8b, 0xa2, 0xff, 0x32, 0x2f, 0x2e, 0xff, 0x3b, 0x3b, 0x34, 0xff, 0x43, 0x44, 0x49, 0xff, 0x47, 0x47, 0x45, 0xff, 0x24, 0x22, 0x1f, 0xff, 0x65, 0x61, 0x61, 0xff, 0xb7, 0xb4, 0xba, 0xff, 0x39, 0x32, 0x2b, 0xff, 0x28, 0x1c, 0x0e, 0xff, 0x23, 0x22, 0x32, 0xff, 0x2e, 0x42, 0x75, 0xff, 0x37, 0x55, 0x91, 0xff, 0x2f, 0x40, 0x65, 0xff, 0x21, 0x26, 0x32, 0xff, 0x28, 0x2b, 0x2e, 0xff, 0x32, 0x33, 0x32, 0xff, 0x28, 0x28, 0x24, 0xff, 0x28, 0x29, 0x28, 0xff, 0x2a, 0x26, 0x29, 0xff, 0x24, 0x2c, 0x2b, 0xff, 0x25, 0x31, 0x29, 0xff, 0x2e, 0x21, 0x25, 0xff, 0x24, 0x18, 0x31, 0xff, 0x2e, 0x40, 0x6f, 0xff, 0x3f, 0x7c, 0xb2, 0xff, 0x60, 0x9d, 0xcf, 0xff, 0x59, 0x7f, 0xaf, 0xff, 0x39, 0x60, 0x90, 0xff, 0x15, 0x33, 0x5a, 0xff, 0x27, 0x34, 0x4b, 0xff, 0x38, 0x35, 0x38, 0xff, 0x2c, 0x27, 0x23, 0xff, 0x25, 0x27, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x26, 0x26, 0x22, 0xff, 0x17, 0x1a, 0x1a, 0xff, 0x6a, 0xa5, 0xba, 0xff, 0x80, 0xd7, 0xfb, 0xff, 0x7c, 0xb6, 0xe2, 0xff, 0x74, 0xad, 0xde, 0xff, 0x44, 0xb0, 0xdc, 0xff, 0x55, 0xb8, 0xe1, 0xff, 0x82, 0xbf, 0xe9, 0xff, 0xef, 0xff, 0xff, 0xff, 0xbe, 0xc0, 0xc7, 0x8b,
    0x21, 0x19, 0x25, 0xaa, 0x1d, 0x10, 0x21, 0xff, 0x47, 0x30, 0x4d, 0xff, 0x78, 0x36, 0x96, 0xff, 0x57, 0x25, 0x71, 0xff, 0x54, 0x37, 0x64, 0xff, 0x98, 0x6a, 0x95, 0xff, 0x4d, 0x2c, 0x28, 0xff, 0x25, 0x29, 0x1c, 0xff, 0x58, 0x49, 0x2a, 0xff, 0x61, 0x47, 0x0a, 0xff, 0x6c, 0x5a, 0x0c, 0xff, 0x87, 0x73, 0x40, 0xff, 0x31, 0x26, 0x1d, 0xff, 0x19, 0x1e, 0x1f, 0xff, 0x26, 0x29, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x26, 0x28, 0x28, 0xff, 0x25, 0x28, 0x28, 0xff, 0x28, 0x2a, 0x2b, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x27, 0x29, 0x29, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x27, 0x29, 0x29, 0xff, 0x26, 0x28, 0x27, 0xff, 0x25, 0x27, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x29, 0x2b, 0xff, 0x38, 0x2f, 0x26, 0xff, 0x37, 0x3f, 0x50, 0xff, 0x36, 0x4f, 0x73, 0xff, 0x2a, 0x37, 0x4a, 0xff, 0x34, 0x49, 0x67, 0xff, 0x34, 0x39, 0x44, 0xff, 0x22, 0x28, 0x31, 0xff, 0x30, 0x41, 0x60, 0xff, 0x3f, 0x5d, 0x93, 0xff, 0x4a, 0x69, 0xa4, 0xff, 0x41, 0x52, 0x81, 0xff, 0x25, 0x2c, 0x42, 0xff, 0x1f, 0x1a, 0x28, 0xff, 0x46, 0x3e, 0x47, 0xff, 0xd9, 0xd5, 0xcd, 0xff, 0xef, 0xec, 0xda, 0xff, 0xcf, 0xc9, 0xbb, 0xff, 0x8d, 0x85, 0x86, 0xff, 0x67, 0x5a, 0x4a, 0xff, 0x63, 0x53, 0x3e, 0xff, 0xbd, 0xb1, 0xb1, 0xff, 0x88, 0x84, 0x8c, 0xff, 0x14, 0x18, 0x28, 0xff, 0x41, 0x43, 0x5e, 0xff, 0x57, 0x6c, 0x9b, 0xff, 0x9e, 0xc7, 0xff, 0xff, 0xa6, 0xca, 0xff, 0xff, 0xb1, 0xcc, 0xf0, 0xff, 0xc6, 0xdf, 0xfb, 0xff, 0xa2, 0xc8, 0xf0, 0xff, 0x6a, 0x9a, 0xd3, 0xff, 0x74, 0xa3, 0xdf, 0xff, 0x75, 0xa5, 0xdf, 0xff, 0x3c, 0x65, 0xa8, 0xff, 0x2b, 0x4c, 0x90, 0xff, 0x40, 0x60, 0x9a, 0xff, 0x3e, 0x50, 0x80, 0xff, 0x21, 0x28, 0x38, 0xff, 0x15, 0x1f, 0x11, 0xff, 0x36, 0x38, 0x41, 0xff, 0x5c, 0x5a, 0x7c, 0xff, 0x3c, 0x3f, 0x42, 0xff, 0x29, 0x29, 0x22, 0xff, 0x3a, 0x35, 0x3a, 0xff, 0x34, 0x2e, 0x2b, 0xff, 0x5c, 0x56, 0x4e, 0xff, 0x9d, 0x97, 0x93, 0xff, 0x60, 0x58, 0x5d, 0xff, 0x19, 0x13, 0x17, 0xff, 0x25, 0x25, 0x30, 0xff, 0x25, 0x37, 0x60, 0xff, 0x39, 0x5d, 0xa4, 0xff, 0x4a, 0x74, 0xbf, 0xff, 0x40, 0x65, 0xa2, 0xff, 0x22, 0x37, 0x5c, 0xff, 0x2d, 0x31, 0x3c, 0xff, 0x34, 0x30, 0x2d, 0xff, 0x2a, 0x27, 0x22, 0xff, 0x25, 0x28, 0x2b, 0xff, 0x29, 0x29, 0x2c, 0xff, 0x2a, 0x29, 0x2b, 0xff, 0x2c, 0x2d, 0x2f, 0xff, 0x2f, 0x2d, 0x2e, 0xff, 0x2b, 0x29, 0x2b, 0xff, 0x20, 0x27, 0x34, 0xff, 0x2b, 0x3e, 0x59, 0xff, 0x33, 0x54, 0x7a, 0xff, 0x52, 0x7a, 0xa7, 0xff, 0x62, 0x8b, 0xbc, 0xff, 0x4a, 0x6e, 0x9d, 0xff, 0x19, 0x2d, 0x4f, 0xff, 0x2b, 0x2c, 0x39, 0xff, 0x2f, 0x2d, 0x2f, 0xff, 0x23, 0x27, 0x2a, 0xff, 0x24, 0x27, 0x28, 0xff, 0x26, 0x29, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x2e, 0x2a, 0x21, 0xff, 0x1c, 0x16, 0x0d, 0xff, 0x37, 0x7d, 0x9d, 0xff, 0x43, 0xb8, 0xf3, 0xff, 0x50, 0x95, 0xd6, 0xff, 0x5d, 0x9d, 0xcf, 0xff, 0x5c, 0xb8, 0xdd, 0xff, 0xb7, 0xdd, 0xff, 0xff, 0xd3, 0xe5, 0xff, 0xff, 0xb1, 0xaa, 0xc2, 0xff, 0x47, 0x3b, 0x44, 0xab,
    0x24, 0x29, 0x29, 0xbe, 0x24, 0x26, 0x1e, 0xff, 0x53, 0x34, 0x57, 0xff, 0x85, 0x3d, 0x95, 0xff, 0x56, 0x35, 0x61, 0xff, 0x0a, 0x19, 0x11, 0xff, 0x6d, 0x4c, 0x5f, 0xff, 0x9e, 0x63, 0x5c, 0xff, 0x13, 0x0d, 0x03, 0xff, 0x12, 0x0b, 0x24, 0xff, 0x57, 0x3a, 0x64, 0xff, 0x62, 0x52, 0x56, 0xff, 0x27, 0x1e, 0x0d, 0xff, 0x24, 0x1a, 0x1e, 0xff, 0x2d, 0x2b, 0x2e, 0xff, 0x28, 0x2a, 0x28, 0xff, 0x27, 0x27, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x25, 0x27, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x28, 0x29, 0x29, 0xff, 0x25, 0x27, 0x27, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x29, 0x2b, 0x2c, 0xff, 0x28, 0x2b, 0x2b, 0xff, 0x2a, 0x25, 0x20, 0xff, 0x2a, 0x35, 0x49, 0xff, 0x37, 0x4c, 0x6d, 0xff, 0x1c, 0x22, 0x30, 0xff, 0x34, 0x45, 0x61, 0xff, 0x28, 0x2d, 0x36, 0xff, 0x25, 0x33, 0x47, 0xff, 0x44, 0x5e, 0x9b, 0xff, 0x51, 0x73, 0xb9, 0xff, 0x65, 0x87, 0xbc, 0xff, 0x74, 0x84, 0xc0, 0xff, 0x52, 0x64, 0x97, 0xff, 0x3e, 0x4f, 0x72, 0xff, 0x34, 0x3b, 0x58, 0xff, 0x39, 0x40, 0x51, 0xff, 0x52, 0x53, 0x5b, 0xff, 0x34, 0x32, 0x33, 0xff, 0x41, 0x41, 0x44, 0xff, 0x3f, 0x33, 0x27, 0xff, 0x4a, 0x38, 0x30, 0xff, 0x71, 0x69, 0x7c, 0xff, 0x29, 0x2f, 0x4d, 0xff, 0x0f, 0x20, 0x46, 0xff, 0x6d, 0x81, 0xb4, 0xff, 0xab, 0xc8, 0xfa, 0xff, 0xab, 0xce, 0xfc, 0xff, 0xac, 0xce, 0xf8, 0xff, 0xae, 0xcd, 0xf1, 0xff, 0xaf, 0xd2, 0xf8, 0xff, 0x93, 0xc2, 0xf2, 0xff, 0x69, 0x9d, 0xd8, 0xff, 0x6a, 0x9d, 0xd8, 0xff, 0x5f, 0x90, 0xc8, 0xff, 0x39, 0x64, 0xa4, 0xff, 0x32, 0x58, 0x9f, 0xff, 0x49, 0x70, 0xb0, 0xff, 0x51, 0x76, 0xb0, 0xff, 0x42, 0x5c, 0x89, 0xff, 0x32, 0x42, 0x58, 0xff, 0x1c, 0x26, 0x3c, 0xff, 0x16, 0x20, 0x39, 0xff, 0x23, 0x2b, 0x38, 0xff, 0x26, 0x28, 0x31, 0xff, 0x21, 0x21, 0x2b, 0xff, 0x2b, 0x29, 0x2d, 0xff, 0x42, 0x42, 0x44, 0xff, 0x22, 0x24, 0x30, 0xff, 0x0e, 0x11, 0x27, 0xff, 0x28, 0x34, 0x52, 0xff, 0x2b, 0x45, 0x70, 0xff, 0x42, 0x66, 0xa4, 0xff, 0x53, 0x80, 0xca, 0xff, 0x53, 0x80, 0xc9, 0xff, 0x49, 0x79, 0xc1, 0xff, 0x36, 0x5a, 0x94, 0xff, 0x20, 0x28, 0x3e, 0xff, 0x27, 0x21, 0x1e, 0xff, 0x2c, 0x28, 0x24, 0xff, 0x23, 0x2a, 0x33, 0xff, 0x28, 0x2c, 0x32, 0xff, 0x31, 0x29, 0x2f, 0xff, 0x28, 0x27, 0x36, 0xff, 0x2c, 0x3d, 0x4b, 0xff, 0x20, 0x32, 0x35, 0xff, 0x28, 0x2e, 0x2c, 0xff, 0x38, 0x2f, 0x3a, 0xff, 0x27, 0x2c, 0x46, 0xff, 0x21, 0x3d, 0x5d, 0xff, 0x2d, 0x4a, 0x6e, 0xff, 0x50, 0x6d, 0x94, 0xff, 0x54, 0x6b, 0x8c, 0xff, 0x27, 0x36, 0x4f, 0xff, 0x25, 0x2c, 0x38, 0xff, 0x27, 0x2a, 0x2c, 0xff, 0x22, 0x26, 0x2a, 0xff, 0x25, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x24, 0x28, 0x2a, 0xff, 0x36, 0x29, 0x20, 0xff, 0x2c, 0x0d, 0x09, 0xff, 0x5b, 0x80, 0xa8, 0xff, 0x92, 0xd0, 0xff, 0xff, 0xa3, 0xcc, 0xff, 0xff, 0xd9, 0xe6, 0xff, 0xff, 0xca, 0xe8, 0xfd, 0xff, 0x8d, 0xb4, 0xc1, 0xff, 0x43, 0x57, 0x61, 0xff, 0x0c, 0x10, 0x13, 0xff, 0x18, 0x12, 0x11, 0xbe,
    0x21, 0x2b, 0x27, 0xd3, 0x25, 0x2e, 0x19, 0xff, 0x54, 0x2a, 0x59, 0xff, 0x68, 0x36, 0x77, 0xff, 0x4e, 0x25, 0x52, 0xff, 0x20, 0x25, 0x25, 0xff, 0x2b, 0x2d, 0x25, 0xff, 0x79, 0x53, 0x2e, 0xff, 0x61, 0x33, 0x4b, 0xff, 0xb1, 0x93, 0xcf, 0xff, 0xff, 0xde, 0xff, 0xff, 0x9f, 0x87, 0xbd, 0xff, 0x0f, 0x12, 0x14, 0xff, 0x23, 0x26, 0x27, 0xff, 0x29, 0x28, 0x2b, 0xff, 0x28, 0x29, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x2b, 0x2a, 0x2a, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x2b, 0x2a, 0x2a, 0xff, 0x28, 0x2a, 0x28, 0xff, 0x27, 0x2a, 0x28, 0xff, 0x27, 0x29, 0x29, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x28, 0x2a, 0x2c, 0xff, 0x28, 0x29, 0x2d, 0xff, 0x29, 0x2a, 0x2b, 0xff, 0x2a, 0x2c, 0x2a, 0xff, 0x28, 0x2a, 0x29, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x2a, 0x2c, 0x2c, 0xff, 0x2a, 0x2c, 0x2b, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x1f, 0x2f, 0x49, 0xff, 0x39, 0x4d, 0x67, 0xff, 0x33, 0x33, 0x38, 0xff, 0x1d, 0x2c, 0x43, 0xff, 0x2b, 0x2f, 0x36, 0xff, 0x3c, 0x54, 0x6b, 0xff, 0x46, 0x6d, 0xb6, 0xff, 0x5f, 0x88, 0xcc, 0xff, 0x84, 0xa8, 0xca, 0xff, 0x95, 0xa5, 0xe0, 0xff, 0x91, 0xb1, 0xee, 0xff, 0x75, 0x9e, 0xd6, 0xff, 0x70, 0x8e, 0xcc, 0xff, 0x78, 0x8c, 0xc3, 0xff, 0x3f, 0x4b, 0x75, 0xff, 0x1c, 0x25, 0x3f, 0xff, 0x1a, 0x23, 0x2d, 0xff, 0x1c, 0x24, 0x29, 0xff, 0x21, 0x28, 0x3d, 0xff, 0x0c, 0x1a, 0x43, 0xff, 0x25, 0x3f, 0x6c, 0xff, 0x47, 0x69, 0x96, 0xff, 0x80, 0xa5, 0xd4, 0xff, 0xbe, 0xdf, 0xff, 0xff, 0xb4, 0xd3, 0xf9, 0xff, 0xaf, 0xce, 0xf0, 0xff, 0xad, 0xd0, 0xf3, 0xff, 0xa0, 0xcb, 0xf8, 0xff, 0x83, 0xbb, 0xf4, 0xff, 0x62, 0x9c, 0xdb, 0xff, 0x59, 0x91, 0xca, 0xff, 0x4d, 0x80, 0xb4, 0xff, 0x3d, 0x68, 0xa6, 0xff, 0x37, 0x5d, 0xa4, 0xff, 0x4f, 0x77, 0xb6, 0xff, 0x60, 0x93, 0xd2, 0xff, 0x58, 0x83, 0xc9, 0xff, 0x41, 0x52, 0x91, 0xff, 0x21, 0x33, 0x53, 0xff, 0x24, 0x3a, 0x45, 0xff, 0x31, 0x39, 0x4e, 0xff, 0x30, 0x36, 0x51, 0xff, 0x29, 0x34, 0x4b, 0xff, 0x2a, 0x33, 0x46, 0xff, 0x1f, 0x2a, 0x3e, 0xff, 0x1c, 0x2d, 0x4e, 0xff, 0x24, 0x3c, 0x6b, 0xff, 0x3b, 0x5b, 0x92, 0xff, 0x54, 0x7d, 0xbc, 0xff, 0x64, 0x90, 0xd4, 0xff, 0x69, 0x92, 0xd2, 0xff, 0x65, 0x8b, 0xc4, 0xff, 0x53, 0x81, 0xc3, 0xff, 0x3e, 0x69, 0xaa, 0xff, 0x28, 0x37, 0x56, 0xff, 0x2e, 0x28, 0x26, 0xff, 0x2a, 0x26, 0x23, 0xff, 0x1e, 0x2a, 0x38, 0xff, 0x29, 0x2d, 0x36, 0xff, 0x2f, 0x30, 0x31, 0xff, 0x24, 0x31, 0x42, 0xff, 0x33, 0x48, 0x66, 0xff, 0x31, 0x3f, 0x54, 0xff, 0x1b, 0x21, 0x2d, 0xff, 0x32, 0x3e, 0x56, 0xff, 0x32, 0x3d, 0x55, 0xff, 0x28, 0x2d, 0x3e, 0xff, 0x28, 0x33, 0x49, 0xff, 0x1a, 0x2b, 0x44, 0xff, 0x37, 0x4d, 0x6b, 0xff, 0x3e, 0x5d, 0x82, 0xff, 0x23, 0x35, 0x4c, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x25, 0x28, 0x2d, 0xff, 0x24, 0x28, 0x2b, 0xff, 0x27, 0x27, 0x28, 0xff, 0x28, 0x27, 0x27, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x2d, 0x28, 0x25, 0xff, 0x0e, 0x0a, 0x0a, 0xff, 0x7a, 0x92, 0x9e, 0xff, 0xea, 0xef, 0xff, 0xff, 0x98, 0xb3, 0xbe, 0xff, 0x79, 0x81, 0x8e, 0xff, 0x5b, 0x47, 0x56, 0xff, 0x19, 0x18, 0x16, 0xff, 0x0f, 0x0d, 0x0a, 0xff, 0x26, 0x24, 0x20, 0xff, 0x2b, 0x2b, 0x27, 0xd3,
    0x2d, 0x2b, 0x29, 0xe7, 0x34, 0x2d, 0x31, 0xff, 0x3d, 0x26, 0x42, 0xff, 0x39, 0x29, 0x3a, 0xff, 0x38, 0x27, 0x3e, 0xff, 0x2d, 0x28, 0x36, 0xff, 0x18, 0x1d, 0x09, 0xff, 0x48, 0x35, 0x38, 0xff, 0xe4, 0xc2, 0xea, 0xff, 0xff, 0xd6, 0xff, 0xff, 0xc0, 0x95, 0xf2, 0xff, 0x3c, 0x2f, 0x58, 0xff, 0x0d, 0x16, 0x11, 0xff, 0x26, 0x2b, 0x26, 0xff, 0x2b, 0x24, 0x2e, 0xff, 0x2d, 0x27, 0x27, 0xff, 0x27, 0x28, 0x27, 0xff, 0x26, 0x2b, 0x30, 0xff, 0x2c, 0x2d, 0x2d, 0xff, 0x2f, 0x2c, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x25, 0x28, 0x28, 0xff, 0x29, 0x2a, 0x29, 0xff, 0x2b, 0x28, 0x29, 0xff, 0x2b, 0x27, 0x29, 0xff, 0x27, 0x29, 0x2d, 0xff, 0x23, 0x2c, 0x31, 0xff, 0x25, 0x2b, 0x32, 0xff, 0x29, 0x2c, 0x32, 0xff, 0x27, 0x2a, 0x2d, 0xff, 0x27, 0x2b, 0x2d, 0xff, 0x2b, 0x2c, 0x31, 0xff, 0x2a, 0x28, 0x2f, 0xff, 0x29, 0x28, 0x2f, 0xff, 0x24, 0x32, 0x4a, 0xff, 0x33, 0x45, 0x61, 0xff, 0x2d, 0x32, 0x35, 0xff, 0x1c, 0x23, 0x29, 0xff, 0x2c, 0x3e, 0x62, 0xff, 0x39, 0x60, 0x94, 0xff, 0x56, 0x81, 0xbb, 0xff, 0x7e, 0xa2, 0xdb, 0xff, 0x9c, 0xbc, 0xea, 0xff, 0x9d, 0xb9, 0xec, 0xff, 0xa6, 0xc7, 0xfb, 0xff, 0xa4, 0xc9, 0xfb, 0xff, 0x95, 0xba, 0xf1, 0xff, 0x8d, 0xb0, 0xec, 0xff, 0x80, 0xa3, 0xe1, 0xff, 0x5c, 0x7e, 0xba, 0xff, 0x3d, 0x5d, 0x95, 0xff, 0x33, 0x53, 0x88, 0xff, 0x2d, 0x53, 0x8a, 0xff, 0x35, 0x5e, 0x9c, 0xff, 0x4e, 0x7b, 0xb6, 0xff, 0x7c, 0xa7, 0xd7, 0xff, 0xae, 0xd4, 0xfe, 0xff, 0xb4, 0xd4, 0xf9, 0xff, 0xb3, 0xd1, 0xf3, 0xff, 0xae, 0xcf, 0xf1, 0xff, 0xa9, 0xd0, 0xf6, 0xff, 0x9d, 0xcb, 0xf9, 0xff, 0x84, 0xbc, 0xf0, 0xff, 0x64, 0x9b, 0xd8, 0xff, 0x4d, 0x81, 0xbf, 0xff, 0x3f, 0x70, 0xab, 0xff, 0x3d, 0x6a, 0xa8, 0xff, 0x39, 0x63, 0xa3, 0xff, 0x4e, 0x77, 0xb7, 0xff, 0x6b, 0x97, 0xd5, 0xff, 0x70, 0x9a, 0xdc, 0xff, 0x50, 0x72, 0xb5, 0xff, 0x26, 0x46, 0x7b, 0xff, 0x1c, 0x38, 0x5f, 0xff, 0x2a, 0x3a, 0x5f, 0xff, 0x35, 0x46, 0x6f, 0xff, 0x33, 0x49, 0x76, 0xff, 0x2d, 0x46, 0x76, 0xff, 0x25, 0x43, 0x76, 0xff, 0x2a, 0x4c, 0x83, 0xff, 0x4a, 0x6f, 0xaa, 0xff, 0x74, 0x9c, 0xd9, 0xff, 0x7f, 0xa9, 0xe7, 0xff, 0x70, 0x9a, 0xd6, 0xff, 0x6d, 0x96, 0xcf, 0xff, 0x67, 0x90, 0xca, 0xff, 0x53, 0x7e, 0xc3, 0xff, 0x48, 0x6f, 0xb3, 0xff, 0x2f, 0x46, 0x6f, 0xff, 0x2b, 0x28, 0x28, 0xff, 0x25, 0x25, 0x21, 0xff, 0x2a, 0x31, 0x37, 0xff, 0x29, 0x30, 0x3e, 0xff, 0x30, 0x35, 0x41, 0xff, 0x24, 0x3a, 0x56, 0xff, 0x40, 0x5f, 0x81, 0xff, 0x41, 0x5f, 0x89, 0xff, 0x16, 0x16, 0x20, 0xff, 0x33, 0x3d, 0x48, 0xff, 0x49, 0x6b, 0x96, 0xff, 0x2a, 0x3d, 0x5f, 0xff, 0x25, 0x23, 0x2a, 0xff, 0x28, 0x33, 0x46, 0xff, 0x1b, 0x23, 0x33, 0xff, 0x30, 0x42, 0x5b, 0xff, 0x32, 0x4c, 0x71, 0xff, 0x29, 0x33, 0x46, 0xff, 0x1f, 0x27, 0x33, 0xff, 0x25, 0x28, 0x2b, 0xff, 0x2b, 0x29, 0x27, 0xff, 0x2c, 0x2b, 0x26, 0xff, 0x2a, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x25, 0x29, 0x27, 0xff, 0x19, 0x27, 0x1f, 0xff, 0x2f, 0x3a, 0x32, 0xff, 0x3d, 0x3c, 0x36, 0xff, 0x12, 0x1b, 0x15, 0xff, 0x00, 0x09, 0x0a, 0xff, 0x14, 0x0d, 0x17, 0xff, 0x2c, 0x21, 0x28, 0xff, 0x30, 0x2a, 0x29, 0xff, 0x2c, 0x29, 0x28, 0xff, 0x27, 0x26, 0x2b, 0xe6,
    0x2f, 0x2a, 0x2a, 0xf0, 0x38, 0x2c, 0x37, 0xff, 0x3a, 0x30, 0x3c, 0xff, 0x34, 0x2f, 0x2b, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x19, 0x14, 0x00, 0xff, 0x67, 0x4e, 0x6d, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xa7, 0x83, 0xca, 0xff, 0x23, 0x07, 0x42, 0xff, 0x17, 0x16, 0x1e, 0xff, 0x25, 0x2f, 0x22, 0xff, 0x26, 0x2c, 0x21, 0xff, 0x2d, 0x26, 0x29, 0xff, 0x2f, 0x28, 0x27, 0xff, 0x25, 0x28, 0x2a, 0xff, 0x22, 0x29, 0x30, 0xff, 0x2a, 0x2b, 0x2c, 0xff, 0x2c, 0x28, 0x26, 0xff, 0x25, 0x27, 0x28, 0xff, 0x24, 0x27, 0x27, 0xff, 0x29, 0x28, 0x26, 0xff, 0x2d, 0x28, 0x26, 0xff, 0x2e, 0x27, 0x27, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x25, 0x2a, 0x2b, 0xff, 0x26, 0x2c, 0x36, 0xff, 0x32, 0x38, 0x42, 0xff, 0x28, 0x2e, 0x34, 0xff, 0x25, 0x29, 0x2e, 0xff, 0x2c, 0x2e, 0x35, 0xff, 0x2a, 0x28, 0x32, 0xff, 0x2c, 0x2b, 0x33, 0xff, 0x32, 0x3d, 0x53, 0xff, 0x2d, 0x3e, 0x5b, 0xff, 0x23, 0x2a, 0x2b, 0xff, 0x26, 0x2a, 0x29, 0xff, 0x2c, 0x45, 0x79, 0xff, 0x3f, 0x6b, 0xae, 0xff, 0x6a, 0x94, 0xc8, 0xff, 0x98, 0xb7, 0xec, 0xff, 0xa8, 0xc3, 0xf8, 0xff, 0xa5, 0xc5, 0xf4, 0xff, 0xaa, 0xca, 0xf6, 0xff, 0xac, 0xca, 0xf5, 0xff, 0xad, 0xcc, 0xfa, 0xff, 0x9b, 0xc0, 0xf5, 0xff, 0x7e, 0xa8, 0xe4, 0xff, 0x61, 0x8e, 0xd1, 0xff, 0x59, 0x84, 0xcb, 0xff, 0x5e, 0x88, 0xca, 0xff, 0x5c, 0x8b, 0xc9, 0xff, 0x68, 0x99, 0xd7, 0xff, 0x7c, 0xab, 0xe5, 0xff, 0x97, 0xc0, 0xf0, 0xff, 0xb3, 0xd3, 0xf8, 0xff, 0xb8, 0xd5, 0xf7, 0xff, 0xb1, 0xd0, 0xf3, 0xff, 0xab, 0xce, 0xf2, 0xff, 0xa7, 0xcf, 0xf7, 0xff, 0x9c, 0xca, 0xf8, 0xff, 0x87, 0xbc, 0xee, 0xff, 0x68, 0x9d, 0xd7, 0xff, 0x4a, 0x7b, 0xbc, 0xff, 0x3b, 0x6b, 0xaa, 0xff, 0x3b, 0x69, 0xa7, 0xff, 0x3b, 0x66, 0xa5, 0xff, 0x47, 0x70, 0xb0, 0xff, 0x66, 0x8e, 0xca, 0xff, 0x80, 0xaa, 0xe2, 0xff, 0x82, 0xad, 0xe8, 0xff, 0x54, 0x7c, 0xba, 0xff, 0x31, 0x51, 0x8c, 0xff, 0x33, 0x4e, 0x7f, 0xff, 0x3d, 0x58, 0x88, 0xff, 0x39, 0x59, 0x8f, 0xff, 0x38, 0x5b, 0x96, 0xff, 0x41, 0x69, 0xa7, 0xff, 0x65, 0x8d, 0xcb, 0xff, 0x89, 0xb0, 0xea, 0xff, 0x8d, 0xb3, 0xed, 0xff, 0x89, 0xae, 0xea, 0xff, 0x7d, 0xa3, 0xdc, 0xff, 0x6b, 0x94, 0xcd, 0xff, 0x61, 0x8d, 0xcc, 0xff, 0x4b, 0x78, 0xbf, 0xff, 0x44, 0x6c, 0xb1, 0xff, 0x31, 0x4b, 0x7b, 0xff, 0x24, 0x25, 0x26, 0xff, 0x1f, 0x21, 0x1f, 0xff, 0x33, 0x37, 0x38, 0xff, 0x2e, 0x35, 0x44, 0xff, 0x36, 0x3a, 0x4b, 0xff, 0x2f, 0x49, 0x6d, 0xff, 0x48, 0x6f, 0x92, 0xff, 0x4f, 0x7a, 0xaa, 0xff, 0x23, 0x21, 0x25, 0xff, 0x22, 0x22, 0x23, 0xff, 0x34, 0x55, 0x7f, 0xff, 0x48, 0x6a, 0x9b, 0xff, 0x27, 0x2e, 0x3b, 0xff, 0x28, 0x2b, 0x36, 0xff, 0x2c, 0x2d, 0x34, 0xff, 0x22, 0x26, 0x32, 0xff, 0x30, 0x40, 0x5b, 0xff, 0x34, 0x45, 0x61, 0xff, 0x34, 0x3b, 0x4c, 0xff, 0x2c, 0x2e, 0x31, 0xff, 0x2e, 0x2c, 0x29, 0xff, 0x2d, 0x2d, 0x2b, 0xff, 0x27, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x27, 0xff, 0x2b, 0x2c, 0x2b, 0xff, 0x1d, 0x12, 0x1f, 0xff, 0x14, 0x07, 0x1d, 0xff, 0x25, 0x16, 0x2b, 0xff, 0x1b, 0x17, 0x1c, 0xff, 0x0f, 0x14, 0x12, 0xff, 0x1d, 0x1f, 0x21, 0xff, 0x2b, 0x2c, 0x2a, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x29, 0x28, 0x2c, 0xf0,
    0x2c, 0x28, 0x24, 0xf3, 0x2e, 0x28, 0x28, 0xff, 0x31, 0x29, 0x2e, 0xff, 0x31, 0x29, 0x2b, 0xff, 0x26, 0x28, 0x27, 0xff, 0x27, 0x2c, 0x23, 0xff, 0x1d, 0x16, 0x06, 0xff, 0x88, 0x60, 0x93, 0xff, 0xf0, 0xbb, 0xff, 0xff, 0x47, 0x34, 0x50, 0xff, 0x12, 0x0b, 0x13, 0xff, 0x27, 0x27, 0x28, 0xff, 0x26, 0x2b, 0x24, 0xff, 0x27, 0x2b, 0x22, 0xff, 0x2b, 0x2a, 0x24, 0xff, 0x2d, 0x29, 0x24, 0xff, 0x26, 0x29, 0x2c, 0xff, 0x28, 0x2f, 0x38, 0xff, 0x28, 0x2a, 0x2c, 0xff, 0x2a, 0x27, 0x25, 0xff, 0x27, 0x2a, 0x2d, 0xff, 0x2a, 0x2b, 0x2c, 0xff, 0x2b, 0x27, 0x25, 0xff, 0x2b, 0x27, 0x25, 0xff, 0x2d, 0x29, 0x27, 0xff, 0x2e, 0x2a, 0x27, 0xff, 0x2b, 0x28, 0x21, 0xff, 0x22, 0x26, 0x2f, 0xff, 0x39, 0x42, 0x51, 0xff, 0x32, 0x39, 0x43, 0xff, 0x1c, 0x21, 0x28, 0xff, 0x32, 0x35, 0x3e, 0xff, 0x32, 0x32, 0x3c, 0xff, 0x28, 0x2b, 0x34, 0xff, 0x30, 0x3d, 0x55, 0xff, 0x2a, 0x39, 0x53, 0xff, 0x23, 0x26, 0x23, 0xff, 0x24, 0x29, 0x29, 0xff, 0x31, 0x4a, 0x7c, 0xff, 0x4d, 0x78, 0xbc, 0xff, 0x7b, 0xa3, 0xdc, 0xff, 0xa9, 0xc7, 0xf9, 0xff, 0xb3, 0xcc, 0xf9, 0xff, 0xaf, 0xca, 0xf5, 0xff, 0xaa, 0xc8, 0xf5, 0xff, 0xac, 0xc8, 0xf4, 0xff, 0xab, 0xc8, 0xf5, 0xff, 0x9f, 0xc4, 0xf6, 0xff, 0x92, 0xbb, 0xf3, 0xff, 0x84, 0xb2, 0xec, 0xff, 0x7e, 0xad, 0xe7, 0xff, 0x7a, 0xa9, 0xe1, 0xff, 0x7b, 0xaa, 0xe2, 0xff, 0x83, 0xb1, 0xe8, 0xff, 0x94, 0xbc, 0xf0, 0xff, 0xad, 0xce, 0xfa, 0xff, 0xbb, 0xd4, 0xf7, 0xff, 0xba, 0xd5, 0xf6, 0xff, 0xb1, 0xd0, 0xf2, 0xff, 0xac, 0xce, 0xf2, 0xff, 0xa9, 0xd0, 0xf8, 0xff, 0x9a, 0xc9, 0xf9, 0xff, 0x83, 0xbb, 0xef, 0xff, 0x69, 0x9f, 0xdc, 0xff, 0x47, 0x7a, 0xb9, 0xff, 0x38, 0x69, 0xa7, 0xff, 0x39, 0x67, 0xa6, 0xff, 0x3c, 0x67, 0xa6, 0xff, 0x3f, 0x6a, 0xaa, 0xff, 0x5f, 0x88, 0xc5, 0xff, 0x7a, 0xa5, 0xdf, 0xff, 0x86, 0xb2, 0xed, 0xff, 0x71, 0x9d, 0xd9, 0xff, 0x48, 0x71, 0xad, 0xff, 0x3b, 0x62, 0x9b, 0xff, 0x3c, 0x64, 0x9e, 0xff, 0x41, 0x6a, 0xa6, 0xff, 0x4a, 0x76, 0xb3, 0xff, 0x5c, 0x89, 0xc6, 0xff, 0x79, 0xa3, 0xe0, 0xff, 0x85, 0xb0, 0xe8, 0xff, 0x8b, 0xaf, 0xe8, 0xff, 0x8f, 0xaf, 0xec, 0xff, 0x84, 0xa8, 0xe4, 0xff, 0x72, 0x99, 0xd6, 0xff, 0x58, 0x82, 0xc3, 0xff, 0x45, 0x73, 0xb8, 0xff, 0x3d, 0x69, 0xaf, 0xff, 0x30, 0x4c, 0x82, 0xff, 0x25, 0x27, 0x2e, 0xff, 0x21, 0x23, 0x23, 0xff, 0x2b, 0x2f, 0x32, 0xff, 0x32, 0x38, 0x45, 0xff, 0x2f, 0x34, 0x43, 0xff, 0x2b, 0x46, 0x6b, 0xff, 0x46, 0x6d, 0x95, 0xff, 0x5e, 0x8a, 0xbc, 0xff, 0x2d, 0x2c, 0x34, 0xff, 0x21, 0x22, 0x23, 0xff, 0x24, 0x39, 0x53, 0xff, 0x55, 0x7a, 0xb2, 0xff, 0x36, 0x53, 0x7e, 0xff, 0x2d, 0x2e, 0x37, 0xff, 0x2c, 0x32, 0x41, 0xff, 0x24, 0x2a, 0x39, 0xff, 0x2d, 0x2f, 0x3a, 0xff, 0x2d, 0x3a, 0x52, 0xff, 0x36, 0x3b, 0x48, 0xff, 0x2b, 0x2d, 0x30, 0xff, 0x26, 0x26, 0x27, 0xff, 0x24, 0x28, 0x29, 0xff, 0x25, 0x28, 0x2a, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x27, 0x25, 0x2c, 0xff, 0x2a, 0x21, 0x2b, 0xff, 0x42, 0x3a, 0x2a, 0xff, 0x4d, 0x44, 0x23, 0xff, 0x42, 0x3a, 0x19, 0xff, 0x4c, 0x45, 0x33, 0xff, 0x5d, 0x57, 0x4b, 0xff, 0x42, 0x3c, 0x2c, 0xff, 0x2b, 0x25, 0x1f, 0xff, 0x2d, 0x29, 0x29, 0xff, 0x29, 0x27, 0x29, 0xf3,
    0x2c, 0x2a, 0x25, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x2c, 0x28, 0x25, 0xff, 0x2d, 0x26, 0x2d, 0xff, 0x28, 0x28, 0x2b, 0xff, 0x27, 0x2b, 0x24, 0xff, 0x23, 0x18, 0x15, 0xff, 0x8b, 0x5b, 0xa7, 0xff, 0x95, 0x5a, 0xca, 0xff, 0x20, 0x17, 0x1b, 0xff, 0x27, 0x2b, 0x1e, 0xff, 0x28, 0x27, 0x28, 0xff, 0x28, 0x26, 0x2b, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2c, 0x28, 0x24, 0xff, 0x27, 0x2a, 0x2d, 0xff, 0x2a, 0x31, 0x3b, 0xff, 0x28, 0x2a, 0x2d, 0xff, 0x2a, 0x27, 0x25, 0xff, 0x26, 0x28, 0x2b, 0xff, 0x2e, 0x2d, 0x2f, 0xff, 0x30, 0x30, 0x30, 0xff, 0x24, 0x26, 0x2a, 0xff, 0x21, 0x24, 0x27, 0xff, 0x27, 0x28, 0x29, 0xff, 0x2b, 0x28, 0x24, 0xff, 0x25, 0x2a, 0x38, 0xff, 0x25, 0x30, 0x44, 0xff, 0x3f, 0x47, 0x55, 0xff, 0x28, 0x31, 0x3b, 0xff, 0x23, 0x29, 0x33, 0xff, 0x39, 0x3c, 0x48, 0xff, 0x2b, 0x33, 0x3d, 0xff, 0x2e, 0x3d, 0x54, 0xff, 0x2e, 0x3b, 0x51, 0xff, 0x22, 0x21, 0x1b, 0xff, 0x27, 0x2b, 0x2b, 0xff, 0x35, 0x52, 0x88, 0xff, 0x52, 0x7c, 0xc6, 0xff, 0x8b, 0xb0, 0xec, 0xff, 0xad, 0xc9, 0xf8, 0xff, 0xba, 0xd0, 0xf7, 0xff, 0xb7, 0xcd, 0xf4, 0xff, 0xad, 0xc9, 0xf4, 0xff, 0xab, 0xc8, 0xf4, 0xff, 0xa8, 0xc6, 0xf3, 0xff, 0x9e, 0xc3, 0xf4, 0xff, 0x91, 0xbc, 0xf2, 0xff, 0x86, 0xb6, 0xec, 0xff, 0x80, 0xb2, 0xe8, 0xff, 0x7d, 0xae, 0xe4, 0xff, 0x85, 0xb6, 0xec, 0xff, 0x8f, 0xbc, 0xf0, 0xff, 0xa1, 0xc5, 0xf2, 0xff, 0xb7, 0xd2, 0xf7, 0xff, 0xc4, 0xd7, 0xf6, 0xff, 0xbd, 0xd5, 0xf5, 0xff, 0xb0, 0xcf, 0xf2, 0xff, 0xad, 0xcf, 0xf4, 0xff, 0xa6, 0xce, 0xf8, 0xff, 0x93, 0xc4, 0xf4, 0xff, 0x7d, 0xb5, 0xed, 0xff, 0x64, 0x9d, 0xd9, 0xff, 0x44, 0x7a, 0xb8, 0xff, 0x3c, 0x6e, 0xac, 0xff, 0x3d, 0x6d, 0xab, 0xff, 0x3b, 0x67, 0xa6, 0xff, 0x3c, 0x68, 0xa7, 0xff, 0x57, 0x82, 0xbe, 0xff, 0x7a, 0xa5, 0xdf, 0xff, 0x89, 0xb5, 0xf1, 0xff, 0x78, 0xa6, 0xe2, 0xff, 0x59, 0x88, 0xc7, 0xff, 0x41, 0x71, 0xb1, 0xff, 0x3a, 0x6b, 0xab, 0xff, 0x3f, 0x71, 0xb0, 0xff, 0x46, 0x77, 0xb8, 0xff, 0x53, 0x83, 0xc3, 0xff, 0x63, 0x92, 0xce, 0xff, 0x7b, 0xa8, 0xde, 0xff, 0x8c, 0xb1, 0xe9, 0xff, 0x92, 0xb2, 0xee, 0xff, 0x89, 0xac, 0xea, 0xff, 0x70, 0x95, 0xd5, 0xff, 0x47, 0x71, 0xb3, 0xff, 0x38, 0x65, 0xa9, 0xff, 0x3b, 0x65, 0xac, 0xff, 0x2f, 0x4b, 0x84, 0xff, 0x27, 0x27, 0x31, 0xff, 0x22, 0x24, 0x24, 0xff, 0x23, 0x28, 0x2b, 0xff, 0x39, 0x40, 0x4d, 0xff, 0x2c, 0x2f, 0x3d, 0xff, 0x1e, 0x36, 0x59, 0xff, 0x48, 0x70, 0x99, 0xff, 0x59, 0x87, 0xbe, 0xff, 0x2a, 0x2b, 0x34, 0xff, 0x2d, 0x2a, 0x27, 0xff, 0x1b, 0x29, 0x38, 0xff, 0x3f, 0x68, 0xa3, 0xff, 0x4a, 0x79, 0xbc, 0xff, 0x2f, 0x42, 0x5e, 0xff, 0x25, 0x37, 0x54, 0xff, 0x26, 0x34, 0x4d, 0xff, 0x36, 0x3b, 0x47, 0xff, 0x25, 0x30, 0x47, 0xff, 0x2e, 0x34, 0x40, 0xff, 0x2e, 0x31, 0x36, 0xff, 0x25, 0x28, 0x2a, 0xff, 0x21, 0x28, 0x2a, 0xff, 0x23, 0x28, 0x2a, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x2a, 0x28, 0xff, 0x28, 0x25, 0x2e, 0xff, 0x25, 0x1d, 0x21, 0xff, 0x67, 0x64, 0x2e, 0xff, 0xa1, 0xa2, 0x40, 0xff, 0x9f, 0x9f, 0x41, 0xff, 0xd0, 0xcc, 0x96, 0xff, 0xed, 0xe8, 0xc7, 0xff, 0x79, 0x70, 0x55, 0xff, 0x1b, 0x11, 0x0e, 0xff, 0x2b, 0x27, 0x2b, 0xff, 0x27, 0x29, 0x27, 0xff,
    0x2a, 0x29, 0x28, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x2b, 0x29, 0x27, 0xff, 0x2d, 0x25, 0x2e, 0xff, 0x27, 0x28, 0x2b, 0xff, 0x24, 0x2b, 0x26, 0xff, 0x26, 0x1c, 0x1e, 0xff, 0x6b, 0x43, 0x85, 0xff, 0x68, 0x3d, 0x8d, 0xff, 0x1f, 0x1f, 0x1a, 0xff, 0x27, 0x2c, 0x24, 0xff, 0x28, 0x27, 0x2b, 0xff, 0x28, 0x26, 0x2d, 0xff, 0x28, 0x26, 0x2d, 0xff, 0x28, 0x26, 0x2b, 0xff, 0x28, 0x25, 0x25, 0xff, 0x27, 0x2b, 0x2f, 0xff, 0x2c, 0x35, 0x3f, 0xff, 0x25, 0x28, 0x2c, 0xff, 0x27, 0x27, 0x26, 0xff, 0x25, 0x28, 0x2c, 0xff, 0x29, 0x2a, 0x2f, 0xff, 0x2e, 0x33, 0x3a, 0xff, 0x33, 0x40, 0x4e, 0xff, 0x19, 0x2a, 0x3a, 0xff, 0x19, 0x25, 0x35, 0xff, 0x27, 0x2d, 0x36, 0xff, 0x30, 0x39, 0x4d, 0xff, 0x32, 0x3f, 0x56, 0xff, 0x2e, 0x3b, 0x4a, 0xff, 0x31, 0x3b, 0x48, 0xff, 0x17, 0x20, 0x2d, 0xff, 0x41, 0x49, 0x57, 0xff, 0x2e, 0x39, 0x46, 0xff, 0x33, 0x43, 0x5a, 0xff, 0x2a, 0x34, 0x48, 0xff, 0x25, 0x20, 0x17, 0xff, 0x2c, 0x2e, 0x2d, 0xff, 0x33, 0x50, 0x8c, 0xff, 0x5c, 0x85, 0xd6, 0xff, 0x94, 0xb7, 0xf6, 0xff, 0xb0, 0xca, 0xf5, 0xff, 0xc0, 0xd3, 0xf3, 0xff, 0xbd, 0xd2, 0xf4, 0xff, 0xb3, 0xcb, 0xf6, 0xff, 0xa9, 0xc6, 0xf4, 0xff, 0xa2, 0xc3, 0xf2, 0xff, 0x9a, 0xc1, 0xf4, 0xff, 0x8f, 0xbb, 0xf1, 0xff, 0x85, 0xb4, 0xea, 0xff, 0x81, 0xb1, 0xe7, 0xff, 0x84, 0xb4, 0xeb, 0xff, 0x8f, 0xbd, 0xf2, 0xff, 0x9c, 0xc4, 0xf3, 0xff, 0xad, 0xcd, 0xf4, 0xff, 0xbd, 0xd6, 0xf6, 0xff, 0xc8, 0xda, 0xf4, 0xff, 0xc0, 0xd7, 0xf5, 0xff, 0xb1, 0xd0, 0xf3, 0xff, 0xaf, 0xd1, 0xf6, 0xff, 0xa4, 0xcd, 0xf8, 0xff, 0x8c, 0xbf, 0xf1, 0xff, 0x74, 0xaf, 0xe9, 0xff, 0x62, 0x9b, 0xd8, 0xff, 0x45, 0x7b, 0xb8, 0xff, 0x3e, 0x71, 0xaf, 0xff, 0x40, 0x70, 0xae, 0xff, 0x3c, 0x69, 0xa7, 0xff, 0x39, 0x65, 0xa4, 0xff, 0x4c, 0x77, 0xb4, 0xff, 0x7c, 0xa5, 0xe0, 0xff, 0x8e, 0xb6, 0xf1, 0xff, 0x7e, 0xa8, 0xe2, 0xff, 0x67, 0x94, 0xd0, 0xff, 0x4b, 0x7d, 0xbf, 0xff, 0x3c, 0x6f, 0xae, 0xff, 0x3f, 0x71, 0xad, 0xff, 0x46, 0x77, 0xb6, 0xff, 0x4f, 0x7f, 0xbe, 0xff, 0x60, 0x8d, 0xca, 0xff, 0x73, 0x9e, 0xd6, 0xff, 0x88, 0xae, 0xe7, 0xff, 0x8f, 0xb2, 0xee, 0xff, 0x86, 0xa9, 0xe9, 0xff, 0x60, 0x83, 0xc8, 0xff, 0x3d, 0x61, 0xa7, 0xff, 0x37, 0x60, 0xa5, 0xff, 0x3b, 0x5e, 0xa4, 0xff, 0x33, 0x49, 0x7d, 0xff, 0x2a, 0x26, 0x2c, 0xff, 0x27, 0x27, 0x27, 0xff, 0x22, 0x25, 0x29, 0xff, 0x2e, 0x36, 0x46, 0xff, 0x2c, 0x2c, 0x35, 0xff, 0x20, 0x30, 0x47, 0xff, 0x4e, 0x74, 0x99, 0xff, 0x46, 0x77, 0xac, 0xff, 0x1e, 0x21, 0x2a, 0xff, 0x2f, 0x2b, 0x27, 0xff, 0x21, 0x29, 0x32, 0xff, 0x24, 0x3e, 0x67, 0xff, 0x4c, 0x7c, 0xbe, 0xff, 0x31, 0x58, 0x90, 0xff, 0x27, 0x40, 0x64, 0xff, 0x2d, 0x40, 0x5e, 0xff, 0x3a, 0x4a, 0x66, 0xff, 0x24, 0x32, 0x4a, 0xff, 0x24, 0x2c, 0x3a, 0xff, 0x30, 0x33, 0x39, 0xff, 0x29, 0x2b, 0x2d, 0xff, 0x23, 0x27, 0x28, 0xff, 0x24, 0x27, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x26, 0x28, 0x2a, 0xff, 0x26, 0x26, 0x21, 0xff, 0x49, 0x3c, 0x1d, 0xff, 0x6b, 0x56, 0x1d, 0xff, 0x85, 0x74, 0x32, 0xff, 0xca, 0xc7, 0x88, 0xff, 0xda, 0xdb, 0xa3, 0xff, 0x6b, 0x66, 0x46, 0xff, 0x1b, 0x11, 0x14, 0xff, 0x28, 0x25, 0x31, 0xff, 0x24, 0x29, 0x26, 0xff,
    0x28, 0x27, 0x2c, 0xf3, 0x29, 0x28, 0x2b, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x2e, 0x27, 0x2a, 0xff, 0x27, 0x2b, 0x23, 0xff, 0x22, 0x2e, 0x22, 0xff, 0x28, 0x29, 0x20, 0xff, 0x3d, 0x26, 0x49, 0xff, 0x3f, 0x2f, 0x49, 0xff, 0x23, 0x29, 0x20, 0xff, 0x24, 0x27, 0x28, 0xff, 0x28, 0x29, 0x26, 0xff, 0x29, 0x29, 0x25, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x24, 0x23, 0xff, 0x2d, 0x31, 0x37, 0xff, 0x32, 0x3b, 0x47, 0xff, 0x1e, 0x22, 0x26, 0xff, 0x2b, 0x29, 0x27, 0xff, 0x2a, 0x2d, 0x30, 0xff, 0x2b, 0x2d, 0x35, 0xff, 0x19, 0x22, 0x31, 0xff, 0x36, 0x4c, 0x62, 0xff, 0x4e, 0x68, 0x83, 0xff, 0x20, 0x36, 0x51, 0xff, 0x2c, 0x3d, 0x56, 0xff, 0x44, 0x50, 0x6d, 0xff, 0x2e, 0x3b, 0x56, 0xff, 0x1d, 0x2c, 0x41, 0xff, 0x2b, 0x37, 0x45, 0xff, 0x33, 0x3d, 0x4b, 0xff, 0x2b, 0x35, 0x44, 0xff, 0x2d, 0x39, 0x47, 0xff, 0x2f, 0x3f, 0x57, 0xff, 0x23, 0x2c, 0x3f, 0xff, 0x2c, 0x25, 0x16, 0xff, 0x22, 0x23, 0x24, 0xff, 0x2e, 0x4e, 0x8d, 0xff, 0x66, 0x91, 0xe5, 0xff, 0x90, 0xb3, 0xf5, 0xff, 0xb4, 0xcb, 0xf5, 0xff, 0xc5, 0xd7, 0xf2, 0xff, 0xc1, 0xd5, 0xf4, 0xff, 0xb6, 0xcc, 0xf5, 0xff, 0xa6, 0xc6, 0xf5, 0xff, 0x9c, 0xc2, 0xf5, 0xff, 0x94, 0xbd, 0xf3, 0xff, 0x8d, 0xb8, 0xef, 0xff, 0x86, 0xb3, 0xeb, 0xff, 0x85, 0xb1, 0xe9, 0xff, 0x88, 0xb6, 0xed, 0xff, 0x95, 0xc0, 0xf4, 0xff, 0xa4, 0xc7, 0xf3, 0xff, 0xb2, 0xd0, 0xf3, 0xff, 0xbf, 0xd9, 0xf3, 0xff, 0xca, 0xdf, 0xf6, 0xff, 0xc1, 0xd9, 0xf8, 0xff, 0xb2, 0xd0, 0xf1, 0xff, 0xaf, 0xd1, 0xf5, 0xff, 0xa1, 0xcc, 0xf8, 0xff, 0x8b, 0xbe, 0xf2, 0xff, 0x75, 0xb1, 0xed, 0xff, 0x5f, 0x99, 0xd8, 0xff, 0x4a, 0x80, 0xbd, 0xff, 0x39, 0x70, 0xad, 0xff, 0x3e, 0x71, 0xaf, 0xff, 0x3b, 0x6a, 0xa8, 0xff, 0x36, 0x63, 0xa2, 0xff, 0x45, 0x6f, 0xaf, 0xff, 0x78, 0x9e, 0xda, 0xff, 0x8e, 0xb3, 0xec, 0xff, 0x84, 0xa9, 0xe0, 0xff, 0x6f, 0x98, 0xd0, 0xff, 0x51, 0x7e, 0xbe, 0xff, 0x43, 0x72, 0xae, 0xff, 0x40, 0x6e, 0xa6, 0xff, 0x45, 0x74, 0xb0, 0xff, 0x4d, 0x7b, 0xb9, 0xff, 0x5a, 0x86, 0xc3, 0xff, 0x74, 0x9b, 0xd7, 0xff, 0x85, 0xae, 0xe6, 0xff, 0x87, 0xaf, 0xe9, 0xff, 0x79, 0x9d, 0xdd, 0xff, 0x50, 0x72, 0xb8, 0xff, 0x3a, 0x5a, 0xa2, 0xff, 0x33, 0x57, 0x9e, 0xff, 0x38, 0x56, 0x99, 0xff, 0x31, 0x40, 0x6f, 0xff, 0x2d, 0x24, 0x27, 0xff, 0x2c, 0x28, 0x25, 0xff, 0x25, 0x29, 0x2c, 0xff, 0x27, 0x34, 0x45, 0xff, 0x2f, 0x2c, 0x31, 0xff, 0x21, 0x29, 0x37, 0xff, 0x45, 0x69, 0x89, 0xff, 0x2e, 0x5e, 0x95, 0xff, 0x22, 0x24, 0x2c, 0xff, 0x2e, 0x2b, 0x28, 0xff, 0x29, 0x2b, 0x2e, 0xff, 0x2f, 0x2e, 0x36, 0xff, 0x36, 0x59, 0x89, 0xff, 0x33, 0x67, 0xaf, 0xff, 0x4a, 0x5c, 0x7c, 0xff, 0x32, 0x41, 0x58, 0xff, 0x21, 0x3e, 0x66, 0xff, 0x38, 0x48, 0x63, 0xff, 0x22, 0x2a, 0x3b, 0xff, 0x2a, 0x2c, 0x32, 0xff, 0x2d, 0x2e, 0x2e, 0xff, 0x27, 0x28, 0x27, 0xff, 0x27, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x24, 0x26, 0x2c, 0xff, 0x25, 0x27, 0x29, 0xff, 0x44, 0x3c, 0x1a, 0xff, 0x74, 0x62, 0x1e, 0xff, 0x8b, 0x7b, 0x2e, 0xff, 0xc1, 0xbc, 0x7d, 0xff, 0xd0, 0xd5, 0x9e, 0xff, 0x66, 0x67, 0x43, 0xff, 0x14, 0x0e, 0x17, 0xff, 0x24, 0x23, 0x36, 0xff, 0x21, 0x29, 0x23, 0xf3,
    0x25, 0x2a, 0x28, 0xf0, 0x29, 0x2b, 0x27, 0xff, 0x2d, 0x25, 0x2e, 0xff, 0x25, 0x26, 0x2e, 0xff, 0x21, 0x2a, 0x2a, 0xff, 0x27, 0x2b, 0x1b, 0xff, 0x38, 0x2a, 0x29, 0xff, 0x34, 0x18, 0x39, 0xff, 0x22, 0x1c, 0x1d, 0xff, 0x28, 0x2d, 0x24, 0xff, 0x27, 0x27, 0x29, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x29, 0x26, 0xff, 0x28, 0x29, 0x26, 0xff, 0x27, 0x29, 0x27, 0xff, 0x23, 0x26, 0x21, 0xff, 0x33, 0x37, 0x3b, 0xff, 0x30, 0x37, 0x44, 0xff, 0x1f, 0x21, 0x28, 0xff, 0x28, 0x2d, 0x29, 0xff, 0x30, 0x2e, 0x30, 0xff, 0x29, 0x2e, 0x2e, 0xff, 0x26, 0x26, 0x29, 0xff, 0x1f, 0x28, 0x40, 0xff, 0x5f, 0x85, 0xb4, 0xff, 0x4b, 0x7d, 0xb2, 0xff, 0x3d, 0x61, 0x89, 0xff, 0x26, 0x32, 0x44, 0xff, 0x20, 0x28, 0x36, 0xff, 0x2e, 0x32, 0x3a, 0xff, 0x2f, 0x41, 0x59, 0xff, 0x35, 0x45, 0x59, 0xff, 0x23, 0x22, 0x21, 0xff, 0x37, 0x41, 0x4f, 0xff, 0x39, 0x4a, 0x62, 0xff, 0x24, 0x2a, 0x30, 0xff, 0x29, 0x28, 0x22, 0xff, 0x26, 0x1e, 0x1a, 0xff, 0x32, 0x4c, 0x81, 0xff, 0x61, 0x95, 0xea, 0xff, 0x8d, 0xb1, 0xf2, 0xff, 0xb0, 0xc5, 0xf5, 0xff, 0xca, 0xd8, 0xf2, 0xff, 0xc7, 0xd8, 0xf3, 0xff, 0xbb, 0xce, 0xf9, 0xff, 0xa6, 0xc5, 0xf6, 0xff, 0x98, 0xc1, 0xf1, 0xff, 0x90, 0xb9, 0xef, 0xff, 0x85, 0xb2, 0xe9, 0xff, 0x84, 0xb2, 0xea, 0xff, 0x86, 0xb4, 0xea, 0xff, 0x8d, 0xba, 0xec, 0xff, 0x97, 0xbf, 0xf1, 0xff, 0x9f, 0xc6, 0xf2, 0xff, 0xaf, 0xcf, 0xf4, 0xff, 0xc1, 0xd8, 0xf5, 0xff, 0xd0, 0xe0, 0xf6, 0xff, 0xc4, 0xdb, 0xef, 0xff, 0xa1, 0xcd, 0xf4, 0xff, 0x9f, 0xce, 0xfc, 0xff, 0xa2, 0xcc, 0xf1, 0xff, 0x8a, 0xbf, 0xf2, 0xff, 0x80, 0xb8, 0xf1, 0xff, 0x6a, 0xa4, 0xdb, 0xff, 0x50, 0x89, 0xc3, 0xff, 0x43, 0x74, 0xae, 0xff, 0x42, 0x72, 0xb3, 0xff, 0x3b, 0x6a, 0xae, 0xff, 0x3a, 0x60, 0x9c, 0xff, 0x41, 0x69, 0xa3, 0xff, 0x6b, 0x95, 0xd4, 0xff, 0x88, 0xae, 0xeb, 0xff, 0x7f, 0xa5, 0xe0, 0xff, 0x6a, 0x93, 0xce, 0xff, 0x4f, 0x7f, 0xbb, 0xff, 0x45, 0x73, 0xb0, 0xff, 0x44, 0x6e, 0xac, 0xff, 0x45, 0x71, 0xaf, 0xff, 0x4c, 0x78, 0xb5, 0xff, 0x58, 0x83, 0xc1, 0xff, 0x70, 0x9b, 0xd8, 0xff, 0x7f, 0xaa, 0xe5, 0xff, 0x7e, 0xa6, 0xe2, 0xff, 0x62, 0x88, 0xc9, 0xff, 0x3e, 0x61, 0xa6, 0xff, 0x32, 0x51, 0x99, 0xff, 0x31, 0x4e, 0x96, 0xff, 0x36, 0x51, 0x99, 0xff, 0x31, 0x3a, 0x5d, 0xff, 0x2b, 0x26, 0x19, 0xff, 0x28, 0x2a, 0x26, 0xff, 0x2a, 0x29, 0x32, 0xff, 0x2a, 0x35, 0x48, 0xff, 0x2a, 0x33, 0x44, 0xff, 0x22, 0x26, 0x2a, 0xff, 0x3e, 0x5e, 0x86, 0xff, 0x39, 0x5e, 0x84, 0xff, 0x23, 0x28, 0x33, 0xff, 0x27, 0x2c, 0x30, 0xff, 0x28, 0x26, 0x27, 0xff, 0x21, 0x2b, 0x30, 0xff, 0x2e, 0x3b, 0x5a, 0xff, 0x42, 0x68, 0x9b, 0xff, 0x43, 0x71, 0x97, 0xff, 0x2b, 0x38, 0x5c, 0xff, 0x30, 0x44, 0x68, 0xff, 0x3c, 0x51, 0x70, 0xff, 0x26, 0x38, 0x55, 0xff, 0x21, 0x2a, 0x3a, 0xff, 0x2c, 0x2c, 0x2c, 0xff, 0x2c, 0x29, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x27, 0x27, 0xff, 0x1e, 0x23, 0x28, 0xff, 0x2b, 0x26, 0x2c, 0xff, 0x6e, 0x57, 0x1d, 0xff, 0xa5, 0x93, 0x2f, 0xff, 0x9f, 0x9d, 0x50, 0xff, 0xb6, 0xbc, 0x79, 0xff, 0xd5, 0xd5, 0xa0, 0xff, 0x6d, 0x6a, 0x53, 0xff, 0x11, 0x0f, 0x19, 0xff, 0x28, 0x2d, 0x29, 0xff, 0x2a, 0x28, 0x27, 0xf0,
    0x29, 0x29, 0x25, 0xe7, 0x28, 0x27, 0x2d, 0xff, 0x29, 0x2b, 0x26, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x2e, 0x27, 0x2b, 0xff, 0x38, 0x29, 0x1e, 0xff, 0x60, 0x35, 0x4a, 0xff, 0x9e, 0x64, 0xa3, 0xff, 0x6b, 0x4a, 0x6a, 0xff, 0x20, 0x1f, 0x1e, 0xff, 0x26, 0x28, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x25, 0x28, 0x24, 0xff, 0x2e, 0x31, 0x33, 0xff, 0x28, 0x2d, 0x35, 0xff, 0x2a, 0x29, 0x2d, 0xff, 0x21, 0x28, 0x28, 0xff, 0x30, 0x32, 0x39, 0xff, 0x26, 0x34, 0x43, 0xff, 0x19, 0x21, 0x31, 0xff, 0x35, 0x4f, 0x6a, 0xff, 0x5a, 0x99, 0xc9, 0xff, 0x7e, 0xbb, 0xee, 0xff, 0x26, 0x3d, 0x58, 0xff, 0x17, 0x1c, 0x22, 0xff, 0x2d, 0x35, 0x3c, 0xff, 0x32, 0x31, 0x30, 0xff, 0x22, 0x38, 0x55, 0xff, 0x26, 0x38, 0x51, 0xff, 0x2f, 0x29, 0x24, 0xff, 0x31, 0x3c, 0x4d, 0xff, 0x37, 0x48, 0x60, 0xff, 0x23, 0x27, 0x24, 0xff, 0x24, 0x29, 0x2a, 0xff, 0x28, 0x1d, 0x16, 0xff, 0x32, 0x47, 0x6f, 0xff, 0x5f, 0x93, 0xe8, 0xff, 0x8a, 0xad, 0xf4, 0xff, 0xaa, 0xbe, 0xf5, 0xff, 0xc4, 0xd1, 0xf2, 0xff, 0xc8, 0xda, 0xf4, 0xff, 0xbe, 0xcf, 0xf8, 0xff, 0xa5, 0xc3, 0xf3, 0xff, 0x99, 0xc1, 0xf0, 0xff, 0x96, 0xbf, 0xf1, 0xff, 0x89, 0xb6, 0xec, 0xff, 0x86, 0xb4, 0xe9, 0xff, 0x85, 0xb4, 0xe9, 0xff, 0x8d, 0xba, 0xec, 0xff, 0x97, 0xc0, 0xf0, 0xff, 0x9d, 0xc5, 0xf2, 0xff, 0xac, 0xcd, 0xf5, 0xff, 0xbd, 0xd6, 0xf7, 0xff, 0xd0, 0xdf, 0xf5, 0xff, 0xbd, 0xd6, 0xee, 0xff, 0x9c, 0xc9, 0xfa, 0xff, 0xab, 0xce, 0xf4, 0xff, 0xc9, 0xde, 0xef, 0xff, 0xa0, 0xcb, 0xfa, 0xff, 0x6d, 0xa8, 0xeb, 0xff, 0x5f, 0x99, 0xd6, 0xff, 0x79, 0xb0, 0xe7, 0xff, 0x5b, 0x89, 0xc2, 0xff, 0x40, 0x6e, 0xb1, 0xff, 0x46, 0x75, 0xbc, 0xff, 0x36, 0x61, 0xa1, 0xff, 0x35, 0x5f, 0x9a, 0xff, 0x60, 0x8b, 0xc9, 0xff, 0x7f, 0xa6, 0xe5, 0xff, 0x75, 0x9c, 0xdb, 0xff, 0x5e, 0x8a, 0xc8, 0xff, 0x48, 0x7a, 0xb6, 0xff, 0x40, 0x70, 0xae, 0xff, 0x43, 0x6e, 0xae, 0xff, 0x45, 0x71, 0xb0, 0xff, 0x4f, 0x7c, 0xb9, 0xff, 0x5c, 0x88, 0xc5, 0xff, 0x6b, 0x98, 0xd4, 0xff, 0x73, 0x9e, 0xdc, 0xff, 0x6d, 0x93, 0xd5, 0xff, 0x4c, 0x70, 0xb5, 0xff, 0x34, 0x53, 0x9b, 0xff, 0x2f, 0x4a, 0x90, 0xff, 0x34, 0x4c, 0x92, 0xff, 0x35, 0x4d, 0x96, 0xff, 0x2d, 0x35, 0x51, 0xff, 0x29, 0x27, 0x13, 0xff, 0x24, 0x2a, 0x28, 0xff, 0x2d, 0x2a, 0x34, 0xff, 0x30, 0x37, 0x48, 0xff, 0x2a, 0x3b, 0x56, 0xff, 0x26, 0x2d, 0x34, 0xff, 0x3a, 0x5a, 0x8a, 0xff, 0x35, 0x52, 0x70, 0xff, 0x27, 0x2a, 0x37, 0xff, 0x24, 0x2a, 0x33, 0xff, 0x31, 0x2c, 0x2d, 0xff, 0x23, 0x2b, 0x31, 0xff, 0x22, 0x28, 0x45, 0xff, 0x53, 0x74, 0xa0, 0xff, 0x2a, 0x63, 0x93, 0xff, 0x22, 0x39, 0x64, 0xff, 0x44, 0x4e, 0x6c, 0xff, 0x25, 0x3a, 0x58, 0xff, 0x2a, 0x41, 0x62, 0xff, 0x26, 0x32, 0x47, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x2d, 0x2a, 0x29, 0xff, 0x28, 0x27, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x26, 0x26, 0x26, 0xff, 0x2e, 0x2d, 0x2e, 0xff, 0x28, 0x2f, 0x30, 0xff, 0x2d, 0x27, 0x2c, 0xff, 0x7a, 0x5d, 0x21, 0xff, 0x9f, 0x8c, 0x2a, 0xff, 0x8f, 0x90, 0x4f, 0xff, 0xbe, 0xc9, 0x8b, 0xff, 0xdf, 0xde, 0xad, 0xff, 0x6c, 0x66, 0x57, 0xff, 0x14, 0x0d, 0x18, 0xff, 0x30, 0x2e, 0x21, 0xff, 0x2d, 0x1f, 0x23, 0xe6,
    0x31, 0x25, 0x20, 0xd3, 0x22, 0x21, 0x1b, 0xff, 0x24, 0x27, 0x27, 0xff, 0x38, 0x2c, 0x26, 0xff, 0x40, 0x2d, 0x2b, 0xff, 0x4b, 0x33, 0x2d, 0xff, 0x72, 0x43, 0x59, 0xff, 0xd1, 0x92, 0xd6, 0xff, 0x90, 0x61, 0x95, 0xff, 0x16, 0x12, 0x15, 0xff, 0x26, 0x2a, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x27, 0xff, 0x2b, 0x2a, 0x29, 0xff, 0x27, 0x29, 0x2c, 0xff, 0x20, 0x24, 0x2a, 0xff, 0x30, 0x2c, 0x2f, 0xff, 0x1a, 0x20, 0x29, 0xff, 0x1d, 0x2b, 0x44, 0xff, 0x32, 0x57, 0x7e, 0xff, 0x2b, 0x48, 0x6c, 0xff, 0x46, 0x6a, 0x8b, 0xff, 0x30, 0x69, 0x94, 0xff, 0x73, 0xa5, 0xcb, 0xff, 0x49, 0x54, 0x62, 0xff, 0x1e, 0x1f, 0x26, 0xff, 0x29, 0x32, 0x3e, 0xff, 0x2f, 0x2f, 0x31, 0xff, 0x1d, 0x30, 0x4c, 0xff, 0x2a, 0x3c, 0x56, 0xff, 0x27, 0x27, 0x29, 0xff, 0x26, 0x33, 0x47, 0xff, 0x31, 0x3f, 0x55, 0xff, 0x24, 0x26, 0x25, 0xff, 0x22, 0x28, 0x2a, 0xff, 0x26, 0x20, 0x1a, 0xff, 0x29, 0x38, 0x55, 0xff, 0x55, 0x83, 0xd4, 0xff, 0x84, 0xa7, 0xf4, 0xff, 0xa1, 0xb8, 0xf6, 0xff, 0xb6, 0xc8, 0xf1, 0xff, 0xc1, 0xd6, 0xf2, 0xff, 0xbd, 0xd1, 0xf4, 0xff, 0xab, 0xc7, 0xf3, 0xff, 0xa2, 0xc6, 0xf3, 0xff, 0x9f, 0xc5, 0xf6, 0xff, 0x92, 0xbd, 0xf2, 0xff, 0x88, 0xb4, 0xea, 0xff, 0x86, 0xb2, 0xe7, 0xff, 0x8d, 0xb7, 0xe9, 0xff, 0x97, 0xc2, 0xf0, 0xff, 0x9e, 0xc6, 0xf4, 0xff, 0xa9, 0xc9, 0xf4, 0xff, 0xb5, 0xd0, 0xf2, 0xff, 0xc8, 0xdc, 0xf3, 0xff, 0xb4, 0xce, 0xf0, 0xff, 0xa3, 0xc8, 0xfd, 0xff, 0xba, 0xd3, 0xf6, 0xff, 0xcc, 0xde, 0xf5, 0xff, 0x8c, 0xbb, 0xf1, 0xff, 0x55, 0x94, 0xdd, 0xff, 0x48, 0x7c, 0xba, 0xff, 0x67, 0x93, 0xcb, 0xff, 0x54, 0x80, 0xb9, 0xff, 0x38, 0x62, 0x9e, 0xff, 0x51, 0x7e, 0xbc, 0xff, 0x3d, 0x71, 0xb6, 0xff, 0x2c, 0x5a, 0x9c, 0xff, 0x5c, 0x83, 0xc0, 0xff, 0x73, 0x9b, 0xd9, 0xff, 0x63, 0x8d, 0xca, 0xff, 0x54, 0x80, 0xbd, 0xff, 0x44, 0x73, 0xb0, 0xff, 0x3d, 0x6d, 0xa9, 0xff, 0x3f, 0x6b, 0xa8, 0xff, 0x44, 0x71, 0xae, 0xff, 0x51, 0x7e, 0xba, 0xff, 0x5c, 0x88, 0xc5, 0xff, 0x65, 0x91, 0xcd, 0xff, 0x63, 0x8e, 0xd0, 0xff, 0x51, 0x79, 0xc2, 0xff, 0x3b, 0x5b, 0xa4, 0xff, 0x31, 0x4c, 0x94, 0xff, 0x35, 0x49, 0x8e, 0xff, 0x36, 0x49, 0x8b, 0xff, 0x32, 0x49, 0x88, 0xff, 0x28, 0x30, 0x46, 0xff, 0x2c, 0x26, 0x19, 0xff, 0x21, 0x27, 0x26, 0xff, 0x29, 0x2b, 0x2e, 0xff, 0x31, 0x38, 0x43, 0xff, 0x30, 0x41, 0x5f, 0xff, 0x2c, 0x3b, 0x4f, 0xff, 0x3b, 0x5a, 0x8d, 0xff, 0x27, 0x41, 0x61, 0xff, 0x2b, 0x2a, 0x33, 0xff, 0x29, 0x2d, 0x36, 0xff, 0x36, 0x32, 0x31, 0xff, 0x2e, 0x1f, 0x22, 0xff, 0x22, 0x30, 0x50, 0xff, 0x45, 0x6d, 0xa3, 0xff, 0x25, 0x4a, 0x80, 0xff, 0x32, 0x54, 0x78, 0xff, 0x31, 0x3a, 0x54, 0xff, 0x1f, 0x2f, 0x4a, 0xff, 0x37, 0x4a, 0x66, 0xff, 0x30, 0x3a, 0x4a, 0xff, 0x28, 0x26, 0x29, 0xff, 0x2b, 0x2a, 0x2c, 0xff, 0x2a, 0x2c, 0x2e, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x2e, 0x2d, 0x2c, 0xff, 0x24, 0x27, 0x2d, 0xff, 0x22, 0x20, 0x23, 0xff, 0x7d, 0x6e, 0x36, 0xff, 0xb2, 0xaa, 0x4e, 0xff, 0xae, 0xb3, 0x67, 0xff, 0xca, 0xcf, 0x9f, 0xff, 0xdd, 0xde, 0xb6, 0xff, 0x6d, 0x68, 0x56, 0xff, 0x16, 0x07, 0x15, 0xff, 0x4a, 0x39, 0x37, 0xff, 0x50, 0x3b, 0x43, 0xd3,
    0x44, 0x29, 0x3c, 0xbe, 0x40, 0x2d, 0x31, 0xff, 0x30, 0x1f, 0x2f, 0xff, 0x2f, 0x2a, 0x28, 0xff, 0x2f, 0x2c, 0x2b, 0xff, 0x32, 0x2d, 0x25, 0xff, 0x30, 0x1e, 0x1e, 0xff, 0x48, 0x29, 0x4a, 0xff, 0x42, 0x31, 0x48, 0xff, 0x21, 0x21, 0x21, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x27, 0xff, 0x2d, 0x28, 0x28, 0xff, 0x1f, 0x24, 0x2a, 0xff, 0x22, 0x32, 0x3d, 0xff, 0x45, 0x46, 0x54, 0xff, 0x5c, 0x68, 0x7d, 0xff, 0x27, 0x44, 0x69, 0xff, 0x5b, 0x98, 0xbd, 0xff, 0x53, 0x73, 0x91, 0xff, 0x14, 0x1b, 0x3e, 0xff, 0x33, 0x47, 0x79, 0xff, 0x74, 0x9b, 0xcc, 0xff, 0x48, 0x6b, 0x88, 0xff, 0x19, 0x26, 0x36, 0xff, 0x2a, 0x31, 0x40, 0xff, 0x2d, 0x2d, 0x30, 0xff, 0x3e, 0x51, 0x6b, 0xff, 0x3e, 0x4f, 0x69, 0xff, 0x1e, 0x1e, 0x22, 0xff, 0x2c, 0x39, 0x53, 0xff, 0x33, 0x40, 0x55, 0xff, 0x28, 0x28, 0x24, 0xff, 0x21, 0x29, 0x2e, 0xff, 0x24, 0x20, 0x1b, 0xff, 0x27, 0x2b, 0x3a, 0xff, 0x50, 0x7a, 0xc3, 0xff, 0x7f, 0xa3, 0xf5, 0xff, 0x95, 0xb0, 0xf8, 0xff, 0xa9, 0xc0, 0xf3, 0xff, 0xbb, 0xd1, 0xf2, 0xff, 0xbe, 0xd3, 0xf4, 0xff, 0xb5, 0xce, 0xf2, 0xff, 0xab, 0xcb, 0xf4, 0xff, 0xa4, 0xc7, 0xf6, 0xff, 0x99, 0xc1, 0xf2, 0xff, 0x8e, 0xb7, 0xeb, 0xff, 0x8a, 0xb4, 0xe9, 0xff, 0x91, 0xbb, 0xec, 0xff, 0x99, 0xc1, 0xf1, 0xff, 0x9f, 0xc7, 0xf7, 0xff, 0xa7, 0xc9, 0xf6, 0xff, 0xae, 0xcc, 0xf0, 0xff, 0xbf, 0xd5, 0xf3, 0xff, 0xb6, 0xd0, 0xf0, 0xff, 0xa5, 0xcf, 0xfe, 0xff, 0xb1, 0xdd, 0xff, 0xff, 0xa4, 0xd4, 0xff, 0xff, 0x5c, 0xa2, 0xed, 0xff, 0x53, 0x90, 0xc8, 0xff, 0x4b, 0x6d, 0x96, 0xff, 0x29, 0x49, 0x7c, 0xff, 0x2e, 0x52, 0x83, 0xff, 0x2d, 0x4c, 0x74, 0xff, 0x3f, 0x60, 0x8b, 0xff, 0x50, 0x83, 0xc3, 0xff, 0x33, 0x62, 0xa6, 0xff, 0x57, 0x7c, 0xb9, 0xff, 0x68, 0x92, 0xd0, 0xff, 0x52, 0x7f, 0xbb, 0xff, 0x49, 0x76, 0xb3, 0xff, 0x44, 0x70, 0xae, 0xff, 0x3e, 0x6c, 0xa8, 0xff, 0x40, 0x6d, 0xa8, 0xff, 0x46, 0x74, 0xb0, 0xff, 0x54, 0x80, 0xbd, 0xff, 0x5f, 0x8a, 0xc9, 0xff, 0x64, 0x90, 0xce, 0xff, 0x59, 0x82, 0xc6, 0xff, 0x3d, 0x64, 0xac, 0xff, 0x31, 0x4e, 0x97, 0xff, 0x32, 0x46, 0x8e, 0xff, 0x36, 0x47, 0x8a, 0xff, 0x34, 0x45, 0x86, 0xff, 0x2d, 0x43, 0x78, 0xff, 0x24, 0x2a, 0x35, 0xff, 0x2f, 0x25, 0x1e, 0xff, 0x23, 0x27, 0x2a, 0xff, 0x28, 0x2b, 0x29, 0xff, 0x31, 0x34, 0x3b, 0xff, 0x2e, 0x3f, 0x63, 0xff, 0x2b, 0x41, 0x5e, 0xff, 0x37, 0x54, 0x87, 0xff, 0x2e, 0x40, 0x5b, 0xff, 0x26, 0x20, 0x25, 0xff, 0x31, 0x33, 0x3e, 0xff, 0x32, 0x35, 0x37, 0xff, 0x2c, 0x22, 0x2c, 0xff, 0x27, 0x3f, 0x61, 0xff, 0x2e, 0x56, 0x89, 0xff, 0x40, 0x55, 0x8a, 0xff, 0x30, 0x52, 0x72, 0xff, 0x26, 0x2f, 0x44, 0xff, 0x2d, 0x38, 0x4b, 0xff, 0x37, 0x46, 0x5d, 0xff, 0x31, 0x3a, 0x48, 0xff, 0x2b, 0x2b, 0x2c, 0xff, 0x2a, 0x2b, 0x2f, 0xff, 0x28, 0x2b, 0x2e, 0xff, 0x29, 0x29, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x24, 0x24, 0x2a, 0xff, 0x19, 0x19, 0x15, 0xff, 0x76, 0x6e, 0x45, 0xff, 0xd4, 0xd0, 0x84, 0xff, 0xc0, 0xc6, 0x76, 0xff, 0xc8, 0xc8, 0x9f, 0xff, 0xd8, 0xda, 0xb1, 0xff, 0x6d, 0x6a, 0x55, 0xff, 0x0c, 0x00, 0x0b, 0xff, 0x81, 0x6c, 0x6b, 0xff, 0xd6, 0xbb, 0xc6, 0xbe,
    0x4c, 0x1c, 0x46, 0xaa, 0xb1, 0x81, 0xb2, 0xff, 0x74, 0x55, 0x68, 0xff, 0x17, 0x1a, 0x13, 0xff, 0x1e, 0x29, 0x2f, 0xff, 0x20, 0x2c, 0x25, 0xff, 0x29, 0x2e, 0x1d, 0xff, 0x25, 0x22, 0x24, 0xff, 0x1c, 0x27, 0x20, 0xff, 0x26, 0x2b, 0x26, 0xff, 0x29, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x27, 0x1f, 0x1e, 0xff, 0x29, 0x39, 0x43, 0xff, 0x32, 0x5b, 0x75, 0xff, 0x1f, 0x2e, 0x52, 0xff, 0x70, 0x81, 0xa0, 0xff, 0x7c, 0xa6, 0xc9, 0xff, 0x5b, 0x9d, 0xc1, 0xff, 0x6d, 0x8c, 0xa9, 0xff, 0x20, 0x23, 0x43, 0xff, 0x59, 0x6a, 0x9f, 0xff, 0x5b, 0x86, 0xc1, 0xff, 0x1f, 0x4d, 0x7b, 0xff, 0x19, 0x30, 0x4b, 0xff, 0x27, 0x32, 0x47, 0xff, 0x27, 0x27, 0x2c, 0xff, 0x4a, 0x5b, 0x76, 0xff, 0x3f, 0x4d, 0x65, 0xff, 0x20, 0x1b, 0x1c, 0xff, 0x2c, 0x3a, 0x57, 0xff, 0x36, 0x41, 0x57, 0xff, 0x30, 0x2b, 0x26, 0xff, 0x1e, 0x28, 0x30, 0xff, 0x27, 0x24, 0x20, 0xff, 0x23, 0x1f, 0x22, 0xff, 0x47, 0x6e, 0xac, 0xff, 0x83, 0xab, 0xfd, 0xff, 0x8f, 0xaa, 0xf9, 0xff, 0x9d, 0xb7, 0xf4, 0xff, 0xb1, 0xc9, 0xf4, 0xff, 0xbe, 0xd0, 0xf4, 0xff, 0xba, 0xd1, 0xf3, 0xff, 0xb4, 0xd0, 0xf4, 0xff, 0xab, 0xca, 0xf5, 0xff, 0x9d, 0xc2, 0xf2, 0xff, 0x93, 0xbb, 0xed, 0xff, 0x91, 0xba, 0xed, 0xff, 0x98, 0xc0, 0xf1, 0xff, 0x9d, 0xc5, 0xf4, 0xff, 0xa0, 0xc8, 0xf8, 0xff, 0xa2, 0xc7, 0xf4, 0xff, 0xa7, 0xc7, 0xf1, 0xff, 0xb9, 0xd2, 0xf6, 0xff, 0xb4, 0xd5, 0xf7, 0xff, 0x9d, 0xcc, 0xf6, 0xff, 0x66, 0x90, 0xb6, 0xff, 0x74, 0xa2, 0xcd, 0xff, 0x62, 0xa3, 0xe6, 0xff, 0x2c, 0x5c, 0x86, 0xff, 0x38, 0x47, 0x5b, 0xff, 0x1a, 0x2a, 0x4a, 0xff, 0x1a, 0x30, 0x4e, 0xff, 0x1b, 0x27, 0x35, 0xff, 0x1c, 0x28, 0x3a, 0xff, 0x4a, 0x6d, 0xa0, 0xff, 0x45, 0x70, 0xb2, 0xff, 0x5d, 0x82, 0xc0, 0xff, 0x71, 0x9c, 0xda, 0xff, 0x4f, 0x7d, 0xba, 0xff, 0x44, 0x71, 0xae, 0xff, 0x3b, 0x67, 0xa5, 0xff, 0x38, 0x65, 0xa0, 0xff, 0x3f, 0x6e, 0xa6, 0xff, 0x49, 0x77, 0xb2, 0xff, 0x56, 0x82, 0xc0, 0xff, 0x5f, 0x8a, 0xca, 0xff, 0x5f, 0x8a, 0xcb, 0xff, 0x49, 0x72, 0xb3, 0xff, 0x34, 0x57, 0x98, 0xff, 0x30, 0x48, 0x8c, 0xff, 0x33, 0x43, 0x86, 0xff, 0x35, 0x44, 0x84, 0xff, 0x30, 0x43, 0x83, 0xff, 0x29, 0x40, 0x6b, 0xff, 0x25, 0x28, 0x29, 0xff, 0x31, 0x24, 0x22, 0xff, 0x24, 0x28, 0x2d, 0xff, 0x22, 0x28, 0x21, 0xff, 0x2e, 0x30, 0x35, 0xff, 0x2c, 0x3e, 0x65, 0xff, 0x2c, 0x43, 0x61, 0xff, 0x38, 0x53, 0x81, 0xff, 0x2c, 0x3b, 0x4d, 0xff, 0x2a, 0x21, 0x22, 0xff, 0x27, 0x2b, 0x33, 0xff, 0x1c, 0x26, 0x37, 0xff, 0x35, 0x4c, 0x6a, 0xff, 0x3b, 0x5f, 0x84, 0xff, 0x4e, 0x6d, 0x97, 0xff, 0x45, 0x51, 0x77, 0xff, 0x26, 0x3a, 0x55, 0xff, 0x2a, 0x37, 0x46, 0xff, 0x2a, 0x2f, 0x3c, 0xff, 0x2d, 0x38, 0x4b, 0xff, 0x29, 0x31, 0x3d, 0xff, 0x2a, 0x2b, 0x2c, 0xff, 0x2a, 0x2e, 0x33, 0xff, 0x25, 0x29, 0x2e, 0xff, 0x2d, 0x2c, 0x2b, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x26, 0x2a, 0xff, 0x28, 0x27, 0x1c, 0xff, 0x38, 0x32, 0x1d, 0xff, 0x93, 0x8d, 0x5b, 0xff, 0xae, 0xae, 0x63, 0xff, 0xc3, 0xbf, 0x94, 0xff, 0xd0, 0xd4, 0x9f, 0xff, 0x69, 0x63, 0x4a, 0xff, 0x0c, 0x00, 0x0d, 0xff, 0x5a, 0x4f, 0x4c, 0xff, 0xb2, 0x99, 0xa4, 0xab,
    0x5d, 0x2f, 0x2a, 0x8b, 0xe4, 0xb3, 0xd7, 0xff, 0x9d, 0x73, 0xa9, 0xff, 0x14, 0x0d, 0x10, 0xff, 0x23, 0x27, 0x2d, 0xff, 0x27, 0x2a, 0x2a, 0xff, 0x31, 0x31, 0x1f, 0xff, 0x30, 0x28, 0x2d, 0xff, 0x26, 0x27, 0x29, 0xff, 0x27, 0x2a, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x1d, 0x11, 0x0f, 0xff, 0x4b, 0x68, 0x76, 0xff, 0x5b, 0x97, 0xc2, 0xff, 0x07, 0x27, 0x5b, 0xff, 0x40, 0x58, 0x82, 0xff, 0x8d, 0xbc, 0xdb, 0xff, 0x75, 0xb2, 0xdc, 0xff, 0x84, 0xaa, 0xcd, 0xff, 0x3b, 0x67, 0x84, 0xff, 0x44, 0x85, 0xaf, 0xff, 0x3f, 0x7b, 0xb0, 0xff, 0x45, 0x5a, 0x86, 0xff, 0x32, 0x3f, 0x5e, 0xff, 0x2a, 0x3c, 0x57, 0xff, 0x24, 0x24, 0x28, 0xff, 0x2d, 0x3d, 0x56, 0xff, 0x2d, 0x38, 0x4e, 0xff, 0x28, 0x20, 0x1d, 0xff, 0x26, 0x36, 0x56, 0xff, 0x31, 0x3b, 0x52, 0xff, 0x36, 0x2f, 0x24, 0xff, 0x1c, 0x28, 0x32, 0xff, 0x2c, 0x2a, 0x27, 0xff, 0x26, 0x1a, 0x14, 0xff, 0x34, 0x5a, 0x93, 0xff, 0x7b, 0xa7, 0xf8, 0xff, 0x8b, 0xa9, 0xf9, 0xff, 0x94, 0xaf, 0xf4, 0xff, 0xa6, 0xc1, 0xef, 0xff, 0xba, 0xcc, 0xf4, 0xff, 0xbf, 0xd1, 0xf4, 0xff, 0xb8, 0xd0, 0xf2, 0xff, 0xaf, 0xcd, 0xf6, 0xff, 0xa0, 0xc4, 0xf3, 0xff, 0x96, 0xbd, 0xef, 0xff, 0x94, 0xbc, 0xed, 0xff, 0x97, 0xc0, 0xee, 0xff, 0x9d, 0xc7, 0xf4, 0xff, 0x9d, 0xc5, 0xf4, 0xff, 0x98, 0xbf, 0xec, 0xff, 0xa7, 0xca, 0xf6, 0xff, 0xbd, 0xd8, 0xff, 0xff, 0xab, 0xd8, 0xff, 0xff, 0x59, 0x87, 0xb5, 0xff, 0x0d, 0x10, 0x0c, 0xff, 0x0e, 0x08, 0x00, 0xff, 0x57, 0x71, 0x93, 0xff, 0x37, 0x52, 0x7c, 0xff, 0x13, 0x14, 0x1e, 0xff, 0x2d, 0x30, 0x38, 0xff, 0x23, 0x2a, 0x34, 0xff, 0x21, 0x18, 0x0d, 0xff, 0x28, 0x1e, 0x16, 0xff, 0x2d, 0x3e, 0x62, 0xff, 0x30, 0x54, 0x96, 0xff, 0x5d, 0x85, 0xc5, 0xff, 0x85, 0xb2, 0xef, 0xff, 0x5e, 0x8c, 0xca, 0xff, 0x4b, 0x7a, 0xb7, 0xff, 0x3b, 0x68, 0xa6, 0xff, 0x37, 0x64, 0x9e, 0xff, 0x3c, 0x6b, 0xa2, 0xff, 0x4c, 0x7a, 0xb3, 0xff, 0x57, 0x82, 0xc0, 0xff, 0x5b, 0x84, 0xc5, 0xff, 0x4d, 0x79, 0xbd, 0xff, 0x3a, 0x62, 0x9f, 0xff, 0x31, 0x50, 0x89, 0xff, 0x2e, 0x41, 0x82, 0xff, 0x33, 0x3f, 0x7f, 0xff, 0x36, 0x43, 0x7f, 0xff, 0x33, 0x49, 0x89, 0xff, 0x1e, 0x3c, 0x60, 0xff, 0x24, 0x26, 0x21, 0xff, 0x36, 0x26, 0x26, 0xff, 0x24, 0x29, 0x2f, 0xff, 0x23, 0x2a, 0x20, 0xff, 0x2b, 0x2b, 0x2f, 0xff, 0x27, 0x3b, 0x64, 0xff, 0x2a, 0x45, 0x5d, 0xff, 0x37, 0x51, 0x76, 0xff, 0x29, 0x35, 0x3f, 0xff, 0x34, 0x2b, 0x26, 0xff, 0x1b, 0x21, 0x22, 0xff, 0x36, 0x44, 0x65, 0xff, 0x70, 0xaf, 0xe4, 0xff, 0x58, 0x89, 0xb1, 0xff, 0x34, 0x4b, 0x6c, 0xff, 0x30, 0x39, 0x51, 0xff, 0x2c, 0x2f, 0x45, 0xff, 0x29, 0x3b, 0x46, 0xff, 0x2b, 0x2d, 0x39, 0xff, 0x25, 0x2f, 0x42, 0xff, 0x28, 0x2d, 0x38, 0xff, 0x32, 0x30, 0x32, 0xff, 0x2c, 0x30, 0x37, 0xff, 0x28, 0x2d, 0x35, 0xff, 0x2c, 0x2c, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x26, 0x26, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x2a, 0x24, 0x27, 0xff, 0x44, 0x3f, 0x30, 0xff, 0x24, 0x20, 0x1d, 0xff, 0x3c, 0x31, 0x19, 0xff, 0xa2, 0x9b, 0x56, 0xff, 0xca, 0xc5, 0x92, 0xff, 0xcd, 0xd2, 0x8e, 0xff, 0x67, 0x5e, 0x43, 0xff, 0x15, 0x0f, 0x1b, 0xff, 0x2a, 0x2b, 0x23, 0xff, 0x4e, 0x3a, 0x42, 0x8b,
    0xad, 0x96, 0x45, 0x6b, 0xf9, 0xe3, 0xb6, 0xff, 0xbf, 0x89, 0xe1, 0xff, 0x25, 0x16, 0x2f, 0xff, 0x1e, 0x21, 0x1f, 0xff, 0x2a, 0x2b, 0x28, 0xff, 0x31, 0x2c, 0x24, 0xff, 0x33, 0x26, 0x31, 0xff, 0x29, 0x26, 0x2e, 0xff, 0x26, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x28, 0xff, 0x10, 0x0a, 0x07, 0xff, 0x6f, 0x77, 0x83, 0xff, 0x86, 0xca, 0xf6, 0xff, 0x28, 0x6a, 0xb0, 0xff, 0x49, 0x86, 0xc0, 0xff, 0x64, 0x8c, 0xa7, 0xff, 0x9a, 0xc3, 0xde, 0xff, 0x92, 0xc7, 0xe8, 0xff, 0x65, 0xa2, 0xcc, 0xff, 0x45, 0x89, 0xba, 0xff, 0x3a, 0x73, 0xa6, 0xff, 0x30, 0x51, 0x82, 0xff, 0x2f, 0x58, 0x85, 0xff, 0x36, 0x50, 0x75, 0xff, 0x20, 0x1e, 0x35, 0xff, 0x1f, 0x2c, 0x41, 0xff, 0x2e, 0x40, 0x4c, 0xff, 0x28, 0x1f, 0x1c, 0xff, 0x2f, 0x35, 0x46, 0xff, 0x2b, 0x3c, 0x51, 0xff, 0x2a, 0x2f, 0x34, 0xff, 0x24, 0x28, 0x30, 0xff, 0x2b, 0x2a, 0x2c, 0xff, 0x23, 0x1c, 0x16, 0xff, 0x2f, 0x47, 0x66, 0xff, 0x72, 0x9a, 0xeb, 0xff, 0x7d, 0xa5, 0xfb, 0xff, 0x8e, 0xb1, 0xf2, 0xff, 0xa6, 0xbb, 0xf4, 0xff, 0xb1, 0xc8, 0xf6, 0xff, 0xba, 0xd2, 0xf2, 0xff, 0xbc, 0xd2, 0xf4, 0xff, 0xb5, 0xd0, 0xf5, 0xff, 0xab, 0xca, 0xf2, 0xff, 0x9e, 0xc2, 0xf1, 0xff, 0x94, 0xbc, 0xf0, 0xff, 0x97, 0xbd, 0xed, 0xff, 0xa0, 0xc5, 0xf3, 0xff, 0x9a, 0xc2, 0xf2, 0xff, 0x95, 0xbb, 0xea, 0xff, 0xb2, 0xd0, 0xf8, 0xff, 0xcf, 0xe2, 0xfb, 0xff, 0x9d, 0xce, 0xfd, 0xff, 0x2e, 0x5d, 0x90, 0xff, 0x00, 0x00, 0x00, 0xff, 0x23, 0x24, 0x2d, 0xff, 0x9b, 0xae, 0xcb, 0xff, 0x55, 0x7d, 0xa6, 0xff, 0x00, 0x0d, 0x23, 0xff, 0x2c, 0x31, 0x42, 0xff, 0x41, 0x57, 0x70, 0xff, 0x30, 0x37, 0x45, 0xff, 0x24, 0x1d, 0x24, 0xff, 0x18, 0x14, 0x1d, 0xff, 0x0e, 0x27, 0x53, 0xff, 0x65, 0x92, 0xd4, 0xff, 0x8e, 0xba, 0xf4, 0xff, 0x73, 0x9f, 0xda, 0xff, 0x5e, 0x8d, 0xc8, 0xff, 0x4c, 0x7c, 0xb7, 0xff, 0x43, 0x70, 0xab, 0xff, 0x44, 0x72, 0xac, 0xff, 0x4e, 0x7e, 0xbb, 0xff, 0x54, 0x82, 0xc2, 0xff, 0x54, 0x7f, 0xbf, 0xff, 0x45, 0x6d, 0xad, 0xff, 0x37, 0x57, 0x94, 0xff, 0x34, 0x49, 0x83, 0xff, 0x2f, 0x40, 0x79, 0xff, 0x2e, 0x40, 0x7d, 0xff, 0x30, 0x45, 0x83, 0xff, 0x34, 0x4c, 0x8b, 0xff, 0x29, 0x38, 0x53, 0xff, 0x26, 0x25, 0x1d, 0xff, 0x2c, 0x27, 0x26, 0xff, 0x28, 0x28, 0x29, 0xff, 0x26, 0x2a, 0x26, 0xff, 0x25, 0x26, 0x2c, 0xff, 0x32, 0x37, 0x46, 0xff, 0x2d, 0x40, 0x58, 0xff, 0x25, 0x49, 0x7c, 0xff, 0x31, 0x31, 0x34, 0xff, 0x27, 0x21, 0x1f, 0xff, 0x1f, 0x33, 0x52, 0xff, 0x72, 0x9a, 0xca, 0xff, 0x6e, 0xa2, 0xc2, 0xff, 0x1f, 0x3a, 0x50, 0xff, 0x22, 0x24, 0x35, 0xff, 0x38, 0x34, 0x41, 0xff, 0x2a, 0x27, 0x38, 0xff, 0x2c, 0x36, 0x46, 0xff, 0x28, 0x2d, 0x36, 0xff, 0x2e, 0x32, 0x3c, 0xff, 0x23, 0x2c, 0x35, 0xff, 0x34, 0x3e, 0x4b, 0xff, 0x39, 0x43, 0x4e, 0xff, 0x28, 0x2c, 0x2f, 0xff, 0x28, 0x27, 0x26, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x23, 0x27, 0xff, 0x4d, 0x47, 0x32, 0xff, 0x5d, 0x52, 0x2f, 0xff, 0x34, 0x1f, 0x0d, 0xff, 0x71, 0x68, 0x39, 0xff, 0xd5, 0xd8, 0x9e, 0xff, 0xcd, 0xc9, 0x98, 0xff, 0x65, 0x5d, 0x40, 0xff, 0x19, 0x16, 0x17, 0xff, 0x23, 0x21, 0x26, 0xff, 0x36, 0x28, 0x2a, 0x6b,
    0xf1, 0xe7, 0x81, 0x4b, 0xc5, 0xc3, 0x57, 0xff, 0xdb, 0xb6, 0xcd, 0xff, 0x6a, 0x45, 0x7b, 0xff, 0x10, 0x0c, 0x0a, 0xff, 0x2a, 0x2c, 0x27, 0xff, 0x2e, 0x26, 0x32, 0xff, 0x2f, 0x27, 0x2d, 0xff, 0x2a, 0x2d, 0x1d, 0xff, 0x27, 0x2a, 0x25, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x0b, 0x09, 0x07, 0xff, 0x6e, 0x6d, 0x77, 0xff, 0xa7, 0xe2, 0xff, 0xff, 0x5d, 0xa5, 0xdf, 0xff, 0x5d, 0xa6, 0xd8, 0xff, 0x2d, 0x5d, 0x7e, 0xff, 0x86, 0xa7, 0xbf, 0xff, 0x9f, 0xd5, 0xf5, 0xff, 0x6b, 0xa2, 0xd2, 0xff, 0x46, 0x7c, 0xb3, 0xff, 0x4c, 0x81, 0xb6, 0xff, 0x4c, 0x81, 0xb7, 0xff, 0x4d, 0x7f, 0xb6, 0xff, 0x5f, 0x8b, 0xb8, 0xff, 0x55, 0x77, 0x9d, 0xff, 0x1f, 0x30, 0x50, 0xff, 0x32, 0x38, 0x4b, 0xff, 0x28, 0x27, 0x24, 0xff, 0x2d, 0x35, 0x38, 0xff, 0x33, 0x42, 0x51, 0xff, 0x26, 0x2e, 0x3a, 0xff, 0x2c, 0x2c, 0x31, 0xff, 0x2f, 0x2f, 0x34, 0xff, 0x28, 0x24, 0x23, 0xff, 0x22, 0x2f, 0x40, 0xff, 0x64, 0x87, 0xd2, 0xff, 0x79, 0xa6, 0xfd, 0xff, 0x89, 0xb3, 0xee, 0xff, 0xa4, 0xb7, 0xf6, 0xff, 0xa7, 0xc3, 0xf4, 0xff, 0xb1, 0xd0, 0xf1, 0xff, 0xbd, 0xd2, 0xf6, 0xff, 0xb9, 0xd2, 0xf5, 0xff, 0xb2, 0xce, 0xf3, 0xff, 0xa6, 0xc7, 0xf2, 0xff, 0x9a, 0xc1, 0xf4, 0xff, 0x9c, 0xc0, 0xef, 0xff, 0xa3, 0xc7, 0xf4, 0xff, 0x9e, 0xc4, 0xf5, 0xff, 0x9c, 0xc0, 0xed, 0xff, 0xb3, 0xd0, 0xf5, 0xff, 0xd0, 0xe1, 0xf8, 0xff, 0xb7, 0xd5, 0xf9, 0xff, 0x89, 0xaa, 0xd8, 0xff, 0x6b, 0x7d, 0xa9, 0xff, 0xb3, 0xc9, 0xeb, 0xff, 0xbc, 0xdd, 0xff, 0xff, 0x5e, 0x8d, 0xd2, 0xff, 0x3d, 0x68, 0x99, 0xff, 0x1f, 0x4a, 0x74, 0xff, 0x3a, 0x73, 0xaa, 0xff, 0x5a, 0x89, 0xbd, 0xff, 0x2a, 0x46, 0x6e, 0xff, 0x28, 0x3d, 0x58, 0xff, 0x5a, 0x7d, 0xa9, 0xff, 0x85, 0xb1, 0xef, 0xff, 0x82, 0xad, 0xe5, 0xff, 0x7d, 0xa9, 0xe2, 0xff, 0x72, 0x9f, 0xd9, 0xff, 0x5e, 0x8f, 0xc7, 0xff, 0x52, 0x7f, 0xb9, 0xff, 0x54, 0x7f, 0xbd, 0xff, 0x54, 0x85, 0xc4, 0xff, 0x54, 0x86, 0xc5, 0xff, 0x4e, 0x78, 0xb8, 0xff, 0x39, 0x5e, 0x9b, 0xff, 0x2f, 0x4c, 0x89, 0xff, 0x30, 0x43, 0x7f, 0xff, 0x31, 0x41, 0x79, 0xff, 0x30, 0x46, 0x83, 0xff, 0x32, 0x4c, 0x8b, 0xff, 0x34, 0x4b, 0x87, 0xff, 0x2c, 0x34, 0x49, 0xff, 0x27, 0x24, 0x1b, 0xff, 0x28, 0x2a, 0x28, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x28, 0x28, 0x2b, 0xff, 0x25, 0x28, 0x2f, 0xff, 0x2e, 0x2b, 0x2b, 0xff, 0x28, 0x39, 0x57, 0xff, 0x2a, 0x47, 0x73, 0xff, 0x2c, 0x32, 0x40, 0xff, 0x2a, 0x32, 0x40, 0xff, 0x4d, 0x6d, 0x9f, 0xff, 0x46, 0x65, 0x8a, 0xff, 0x0e, 0x19, 0x26, 0xff, 0x19, 0x1d, 0x27, 0xff, 0x3b, 0x37, 0x3d, 0xff, 0x2e, 0x2c, 0x32, 0xff, 0x26, 0x2e, 0x39, 0xff, 0x2a, 0x32, 0x43, 0xff, 0x2c, 0x32, 0x3d, 0xff, 0x38, 0x37, 0x3c, 0xff, 0x22, 0x29, 0x34, 0xff, 0x2c, 0x3a, 0x4c, 0xff, 0x2f, 0x39, 0x46, 0xff, 0x24, 0x26, 0x28, 0xff, 0x2b, 0x2a, 0x29, 0xff, 0x2c, 0x2c, 0x2c, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x25, 0x26, 0x29, 0xff, 0x2b, 0x29, 0x27, 0xff, 0x4a, 0x3f, 0x1d, 0xff, 0x7a, 0x67, 0x2a, 0xff, 0x97, 0x7f, 0x48, 0xff, 0x4f, 0x44, 0x10, 0xff, 0xa5, 0xa3, 0x67, 0xff, 0xd9, 0xcf, 0xa4, 0xff, 0x5a, 0x58, 0x3b, 0xff, 0x15, 0x19, 0x12, 0xff, 0x25, 0x25, 0x31, 0xff, 0x2c, 0x25, 0x28, 0x4b,
    0xd0, 0xc8, 0x64, 0x21, 0x95, 0x8e, 0x1c, 0xff, 0xc4, 0xa9, 0x78, 0xff, 0xd6, 0x9a, 0xdb, 0xff, 0x38, 0x1d, 0x38, 0xff, 0x15, 0x17, 0x15, 0xff, 0x29, 0x2c, 0x2c, 0xff, 0x2c, 0x2f, 0x1f, 0xff, 0x2a, 0x29, 0x26, 0xff, 0x28, 0x26, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x28, 0xff, 0x0e, 0x08, 0x08, 0xff, 0x60, 0x6b, 0x72, 0xff, 0xcd, 0xf7, 0xff, 0xff, 0x9d, 0xcf, 0xf3, 0xff, 0x8b, 0xc7, 0xed, 0xff, 0x28, 0x66, 0x92, 0xff, 0x69, 0x90, 0xb4, 0xff, 0xaa, 0xda, 0xfa, 0xff, 0x71, 0xa7, 0xd3, 0xff, 0x47, 0x81, 0xb5, 0xff, 0x49, 0x83, 0xb6, 0xff, 0x76, 0xaf, 0xe0, 0xff, 0x8f, 0xbf, 0xf1, 0xff, 0x49, 0x89, 0xbb, 0xff, 0x49, 0x93, 0xc3, 0xff, 0x43, 0x6a, 0x9e, 0xff, 0x29, 0x31, 0x55, 0xff, 0x1a, 0x25, 0x28, 0xff, 0x26, 0x32, 0x33, 0xff, 0x35, 0x3a, 0x43, 0xff, 0x2d, 0x2e, 0x34, 0xff, 0x26, 0x27, 0x29, 0xff, 0x2d, 0x2e, 0x31, 0xff, 0x2c, 0x2a, 0x2a, 0xff, 0x14, 0x1e, 0x2b, 0xff, 0x4b, 0x67, 0xa6, 0xff, 0x7a, 0xa7, 0xf6, 0xff, 0x87, 0xb4, 0xef, 0xff, 0x9e, 0xb7, 0xf5, 0xff, 0xa2, 0xc1, 0xf2, 0xff, 0xac, 0xcd, 0xf0, 0xff, 0xb8, 0xd2, 0xf6, 0xff, 0xba, 0xd4, 0xf8, 0xff, 0xb6, 0xd2, 0xf7, 0xff, 0xac, 0xcc, 0xf4, 0xff, 0xa4, 0xc7, 0xf3, 0xff, 0xa4, 0xc9, 0xf2, 0xff, 0xa7, 0xcd, 0xf4, 0xff, 0xa5, 0xca, 0xf5, 0xff, 0xa7, 0xca, 0xf5, 0xff, 0xb3, 0xd0, 0xf6, 0xff, 0xc2, 0xd6, 0xfa, 0xff, 0xca, 0xe0, 0xfb, 0xff, 0xd1, 0xea, 0xfa, 0xff, 0xdf, 0xfa, 0xff, 0xff, 0xc5, 0xec, 0xff, 0xff, 0x8b, 0xbf, 0xef, 0xff, 0x83, 0xb9, 0xf2, 0xff, 0x7e, 0xb1, 0xe9, 0xff, 0x54, 0x88, 0xc8, 0xff, 0x65, 0x9d, 0xe7, 0xff, 0x63, 0x94, 0xda, 0xff, 0x5f, 0x86, 0xc6, 0xff, 0x6c, 0x90, 0xd4, 0xff, 0x7a, 0xa4, 0xe3, 0xff, 0x73, 0x9e, 0xd8, 0xff, 0x78, 0xa2, 0xde, 0xff, 0x7e, 0xab, 0xe5, 0xff, 0x7b, 0xa9, 0xe1, 0xff, 0x72, 0xa0, 0xd8, 0xff, 0x64, 0x91, 0xcb, 0xff, 0x5d, 0x8a, 0xc6, 0xff, 0x5a, 0x8a, 0xc8, 0xff, 0x52, 0x83, 0xc1, 0xff, 0x3e, 0x68, 0xa7, 0xff, 0x30, 0x55, 0x92, 0xff, 0x2d, 0x4b, 0x88, 0xff, 0x30, 0x47, 0x82, 0xff, 0x35, 0x49, 0x81, 0xff, 0x36, 0x4c, 0x89, 0xff, 0x35, 0x4e, 0x89, 0xff, 0x31, 0x45, 0x79, 0xff, 0x24, 0x2c, 0x3f, 0xff, 0x25, 0x24, 0x1d, 0xff, 0x2b, 0x2a, 0x29, 0xff, 0x2a, 0x29, 0x27, 0xff, 0x27, 0x28, 0x29, 0xff, 0x25, 0x28, 0x2e, 0xff, 0x26, 0x27, 0x2e, 0xff, 0x28, 0x38, 0x54, 0xff, 0x27, 0x32, 0x45, 0xff, 0x27, 0x38, 0x53, 0xff, 0x49, 0x64, 0x8d, 0xff, 0x3d, 0x61, 0x8e, 0xff, 0x21, 0x1f, 0x2d, 0xff, 0x28, 0x19, 0x20, 0xff, 0x31, 0x31, 0x34, 0xff, 0x30, 0x37, 0x38, 0xff, 0x20, 0x2b, 0x2e, 0xff, 0x23, 0x2c, 0x35, 0xff, 0x26, 0x2e, 0x3e, 0xff, 0x2c, 0x30, 0x40, 0xff, 0x30, 0x2e, 0x35, 0xff, 0x30, 0x32, 0x3a, 0xff, 0x2e, 0x35, 0x42, 0xff, 0x27, 0x2c, 0x36, 0xff, 0x29, 0x29, 0x2c, 0xff, 0x29, 0x29, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x25, 0x25, 0x25, 0xff, 0x28, 0x28, 0x28, 0xff, 0x24, 0x25, 0x2b, 0xff, 0x30, 0x2f, 0x24, 0xff, 0x4f, 0x3d, 0x19, 0xff, 0x70, 0x5a, 0x20, 0xff, 0xa8, 0x9e, 0x3d, 0xff, 0x6e, 0x66, 0x1f, 0xff, 0x7f, 0x6f, 0x42, 0xff, 0xe3, 0xd7, 0xa4, 0xff, 0x51, 0x50, 0x36, 0xff, 0x14, 0x1b, 0x15, 0xff, 0x24, 0x2a, 0x30, 0xff, 0x26, 0x26, 0x2e, 0x21,
    0xff, 0xff, 0x00, 0x01, 0xa8, 0x91, 0x26, 0xf2, 0xad, 0x8e, 0x2f, 0xff, 0xe4, 0xbb, 0xa3, 0xff, 0xb6, 0x87, 0xcf, 0xff, 0x18, 0x0b, 0x31, 0xff, 0x0c, 0x1a, 0x03, 0xff, 0x27, 0x2c, 0x2a, 0xff, 0x23, 0x26, 0x31, 0xff, 0x26, 0x28, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x28, 0xff, 0x0d, 0x07, 0x07, 0xff, 0x60, 0x70, 0x79, 0xff, 0xe2, 0xfc, 0xff, 0xff, 0xb8, 0xdd, 0xf2, 0xff, 0xba, 0xec, 0xff, 0xff, 0x5a, 0x9c, 0xcc, 0xff, 0x3f, 0x6d, 0x99, 0xff, 0xad, 0xdb, 0xff, 0xff, 0x7b, 0xb0, 0xdb, 0xff, 0x34, 0x6d, 0x9e, 0xff, 0x3f, 0x7c, 0xad, 0xff, 0x4d, 0x8b, 0xbb, 0xff, 0x75, 0xbc, 0xeb, 0xff, 0x8c, 0xce, 0xff, 0xff, 0x3c, 0x7a, 0xb7, 0xff, 0x32, 0x76, 0xb6, 0xff, 0x40, 0x75, 0xa3, 0xff, 0x22, 0x33, 0x47, 0xff, 0x18, 0x1b, 0x23, 0xff, 0x2e, 0x31, 0x38, 0xff, 0x33, 0x33, 0x37, 0xff, 0x2b, 0x2d, 0x2e, 0xff, 0x26, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x15, 0x18, 0x1b, 0xff, 0x42, 0x55, 0x85, 0xff, 0x7d, 0xaa, 0xf2, 0xff, 0x84, 0xb5, 0xee, 0xff, 0x98, 0xb8, 0xf6, 0xff, 0x9e, 0xc1, 0xf3, 0xff, 0xa7, 0xc9, 0xf1, 0xff, 0xb0, 0xce, 0xf7, 0xff, 0xb7, 0xd4, 0xf9, 0xff, 0xb8, 0xd4, 0xf6, 0xff, 0xb4, 0xd1, 0xf3, 0xff, 0xae, 0xcd, 0xf1, 0xff, 0xaf, 0xd0, 0xf2, 0xff, 0xb0, 0xd2, 0xf5, 0xff, 0xae, 0xd1, 0xf5, 0xff, 0xac, 0xcd, 0xf4, 0xff, 0xb0, 0xcd, 0xf5, 0xff, 0xb8, 0xd1, 0xf6, 0xff, 0xac, 0xce, 0xf5, 0xff, 0xa6, 0xce, 0xf3, 0xff, 0xa6, 0xcf, 0xf2, 0xff, 0x92, 0xc7, 0xf5, 0xff, 0x70, 0xaa, 0xe3, 0xff, 0x74, 0xa8, 0xe1, 0xff, 0x85, 0xb8, 0xec, 0xff, 0x7d, 0xae, 0xea, 0xff, 0x75, 0x9e, 0xe4, 0xff, 0x5f, 0x86, 0xc9, 0xff, 0x53, 0x7b, 0xba, 0xff, 0x56, 0x7a, 0xc0, 0xff, 0x56, 0x7e, 0xc0, 0xff, 0x5f, 0x8e, 0xc7, 0xff, 0x70, 0x9d, 0xd8, 0xff, 0x80, 0xac, 0xe6, 0xff, 0x81, 0xad, 0xe6, 0xff, 0x83, 0xad, 0xe6, 0xff, 0x7d, 0xa7, 0xe0, 0xff, 0x6e, 0x9a, 0xd4, 0xff, 0x61, 0x8f, 0xcc, 0xff, 0x4f, 0x7e, 0xbd, 0xff, 0x38, 0x62, 0xa0, 0xff, 0x31, 0x54, 0x8f, 0xff, 0x31, 0x50, 0x8c, 0xff, 0x32, 0x4e, 0x88, 0xff, 0x36, 0x4d, 0x86, 0xff, 0x36, 0x50, 0x8b, 0xff, 0x34, 0x4e, 0x85, 0xff, 0x35, 0x44, 0x70, 0xff, 0x24, 0x29, 0x36, 0xff, 0x25, 0x25, 0x1f, 0xff, 0x29, 0x29, 0x27, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x26, 0x27, 0x28, 0xff, 0x25, 0x27, 0x2c, 0xff, 0x2a, 0x30, 0x3c, 0xff, 0x2b, 0x31, 0x41, 0xff, 0x2e, 0x34, 0x3f, 0xff, 0x28, 0x34, 0x48, 0xff, 0x2f, 0x5a, 0x99, 0xff, 0x2d, 0x4b, 0x75, 0xff, 0x2e, 0x28, 0x2b, 0xff, 0x30, 0x2a, 0x28, 0xff, 0x28, 0x2e, 0x2c, 0xff, 0x28, 0x33, 0x33, 0xff, 0x20, 0x29, 0x2e, 0xff, 0x32, 0x2c, 0x39, 0xff, 0x2f, 0x2e, 0x42, 0xff, 0x2f, 0x38, 0x4b, 0xff, 0x33, 0x30, 0x38, 0xff, 0x28, 0x28, 0x2e, 0xff, 0x2c, 0x32, 0x3b, 0xff, 0x32, 0x35, 0x3b, 0xff, 0x26, 0x25, 0x26, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x22, 0x23, 0x27, 0xff, 0x2f, 0x2e, 0x25, 0xff, 0x47, 0x35, 0x21, 0xff, 0x70, 0x5b, 0x2f, 0xff, 0x90, 0x90, 0x2b, 0xff, 0xb1, 0xad, 0x6d, 0xff, 0xa7, 0x97, 0x7e, 0xff, 0xa7, 0x98, 0x72, 0xff, 0x51, 0x48, 0x35, 0xff, 0x1f, 0x1d, 0x1b, 0xff, 0x2b, 0x29, 0x2e, 0xf2, 0x00, 0x00, 0x00, 0x01,
    0x00, 0x00, 0x00, 0x00, 0xbe, 0xa7, 0x32, 0xc6, 0xbf, 0xa7, 0x2d, 0xff, 0xb7, 0xa0, 0x42, 0xff, 0xdf, 0xc5, 0xa8, 0xff, 0xbc, 0x93, 0xce, 0xff, 0x4a, 0x2a, 0x5f, 0xff, 0x13, 0x12, 0x03, 0xff, 0x1a, 0x24, 0x1a, 0xff, 0x29, 0x29, 0x2d, 0xff, 0x27, 0x27, 0x26, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x26, 0xff, 0x14, 0x09, 0x08, 0xff, 0x65, 0x76, 0x83, 0xff, 0xe3, 0xf9, 0xff, 0xff, 0xb8, 0xd9, 0xea, 0xff, 0xc0, 0xe5, 0xfb, 0xff, 0x93, 0xcb, 0xf7, 0xff, 0x43, 0x7c, 0xa8, 0xff, 0x85, 0xb8, 0xde, 0xff, 0x8c, 0xc3, 0xec, 0xff, 0x4d, 0x87, 0xb4, 0xff, 0x3a, 0x76, 0xa5, 0xff, 0x34, 0x72, 0xa3, 0xff, 0x3b, 0x84, 0xb2, 0xff, 0x80, 0xb8, 0xe5, 0xff, 0x94, 0xc1, 0xf7, 0xff, 0x4a, 0x96, 0xd5, 0xff, 0x4d, 0x9d, 0xd5, 0xff, 0x51, 0x77, 0xa5, 0xff, 0x1a, 0x21, 0x37, 0xff, 0x25, 0x2a, 0x33, 0xff, 0x2d, 0x30, 0x38, 0xff, 0x2c, 0x2d, 0x2f, 0xff, 0x25, 0x27, 0x27, 0xff, 0x29, 0x2a, 0x2a, 0xff, 0x1e, 0x1c, 0x13, 0xff, 0x31, 0x3e, 0x5e, 0xff, 0x78, 0xa7, 0xe8, 0xff, 0x85, 0xbc, 0xf3, 0xff, 0x93, 0xb9, 0xf7, 0xff, 0x98, 0xc0, 0xf4, 0xff, 0x9f, 0xc7, 0xf3, 0xff, 0xa8, 0xc9, 0xf6, 0xff, 0xb2, 0xd0, 0xf6, 0xff, 0xba, 0xd5, 0xf4, 0xff, 0xbd, 0xd6, 0xf3, 0xff, 0xba, 0xd4, 0xf1, 0xff, 0xbb, 0xd5, 0xf2, 0xff, 0xbd, 0xd7, 0xf4, 0xff, 0xba, 0xd6, 0xf6, 0xff, 0xb2, 0xd1, 0xf5, 0xff, 0xa9, 0xcc, 0xf2, 0xff, 0xa4, 0xc5, 0xed, 0xff, 0x90, 0xc2, 0xee, 0xff, 0x8d, 0xc2, 0xef, 0xff, 0x9d, 0xc7, 0xf3, 0xff, 0x9b, 0xcb, 0xff, 0xff, 0x7c, 0xb3, 0xec, 0xff, 0x72, 0xa6, 0xd6, 0xff, 0x7b, 0xbc, 0xe5, 0xff, 0x79, 0xba, 0xee, 0xff, 0x7d, 0xa9, 0xe7, 0xff, 0x69, 0x9c, 0xd1, 0xff, 0x41, 0x80, 0xae, 0xff, 0x3b, 0x76, 0xad, 0xff, 0x48, 0x79, 0xb8, 0xff, 0x53, 0x82, 0xbe, 0xff, 0x63, 0x91, 0xcc, 0xff, 0x7d, 0xa8, 0xe2, 0xff, 0x8c, 0xb5, 0xee, 0xff, 0x8e, 0xb6, 0xec, 0xff, 0x89, 0xb1, 0xe8, 0xff, 0x78, 0xa4, 0xdf, 0xff, 0x61, 0x90, 0xcc, 0xff, 0x49, 0x78, 0xb5, 0xff, 0x3a, 0x62, 0xa0, 0xff, 0x33, 0x54, 0x90, 0xff, 0x35, 0x55, 0x90, 0xff, 0x33, 0x51, 0x8d, 0xff, 0x34, 0x4f, 0x89, 0xff, 0x31, 0x4e, 0x89, 0xff, 0x36, 0x4e, 0x83, 0xff, 0x38, 0x43, 0x67, 0xff, 0x23, 0x26, 0x2f, 0xff, 0x26, 0x26, 0x22, 0xff, 0x29, 0x29, 0x27, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x27, 0x29, 0x29, 0xff, 0x2a, 0x2d, 0x32, 0xff, 0x30, 0x33, 0x3e, 0xff, 0x28, 0x28, 0x2e, 0xff, 0x2c, 0x34, 0x42, 0xff, 0x2f, 0x34, 0x3d, 0xff, 0x27, 0x4d, 0x83, 0xff, 0x2f, 0x47, 0x70, 0xff, 0x29, 0x31, 0x3a, 0xff, 0x1f, 0x2a, 0x2a, 0xff, 0x31, 0x3b, 0x42, 0xff, 0x2a, 0x34, 0x3e, 0xff, 0x25, 0x2a, 0x38, 0xff, 0x37, 0x32, 0x44, 0xff, 0x34, 0x38, 0x4f, 0xff, 0x33, 0x41, 0x57, 0xff, 0x2d, 0x2e, 0x35, 0xff, 0x26, 0x26, 0x29, 0xff, 0x2c, 0x32, 0x39, 0xff, 0x31, 0x35, 0x39, 0xff, 0x26, 0x26, 0x25, 0xff, 0x28, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x26, 0x26, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x27, 0x29, 0x2b, 0xff, 0x30, 0x2c, 0x2a, 0xff, 0x44, 0x31, 0x1f, 0xff, 0x68, 0x58, 0x27, 0xff, 0x83, 0x7f, 0x30, 0xff, 0xb6, 0xb4, 0x78, 0xff, 0xc0, 0xb4, 0x8d, 0xff, 0x4a, 0x39, 0x1c, 0xff, 0x3c, 0x30, 0x2a, 0xff, 0x2a, 0x25, 0x27, 0xff, 0x2c, 0x28, 0x2a, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x8d, 0x7b, 0x1e, 0x90, 0x97, 0x86, 0x2c, 0xff, 0xa8, 0x8a, 0x2d, 0xff, 0xad, 0x9a, 0x25, 0xff, 0xdd, 0xbc, 0x85, 0xff, 0xec, 0xb3, 0xd3, 0xff, 0x9f, 0x75, 0xa1, 0xff, 0x3a, 0x31, 0x38, 0xff, 0x1c, 0x1e, 0x1b, 0xff, 0x27, 0x28, 0x27, 0xff, 0x31, 0x31, 0x31, 0xff, 0x33, 0x33, 0x33, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x2d, 0x2c, 0x2c, 0xff, 0x20, 0x11, 0x0e, 0xff, 0x4f, 0x5e, 0x72, 0xff, 0xbd, 0xd1, 0xf3, 0xff, 0xc3, 0xe6, 0xf5, 0xff, 0xc5, 0xe0, 0xf0, 0xff, 0xaa, 0xd7, 0xfc, 0xff, 0x63, 0xa5, 0xd1, 0xff, 0x85, 0xbe, 0xe4, 0xff, 0x79, 0xb4, 0xdc, 0xff, 0x6b, 0xa6, 0xd3, 0xff, 0x67, 0xa3, 0xd3, 0xff, 0x4a, 0x87, 0xbb, 0xff, 0x36, 0x64, 0x92, 0xff, 0x36, 0x67, 0x8c, 0xff, 0x74, 0xb6, 0xdb, 0xff, 0x90, 0xd3, 0xff, 0xff, 0x6b, 0xaa, 0xee, 0xff, 0x5c, 0xa1, 0xe3, 0xff, 0x38, 0x52, 0x71, 0xff, 0x1f, 0x22, 0x2d, 0xff, 0x28, 0x30, 0x3b, 0xff, 0x27, 0x2a, 0x2d, 0xff, 0x2e, 0x2e, 0x2e, 0xff, 0x28, 0x28, 0x27, 0xff, 0x2a, 0x25, 0x13, 0xff, 0x1a, 0x22, 0x3c, 0xff, 0x5a, 0x8c, 0xcd, 0xff, 0x8a, 0xc6, 0xfb, 0xff, 0x92, 0xba, 0xf9, 0xff, 0x96, 0xbf, 0xf6, 0xff, 0x9b, 0xc6, 0xf2, 0xff, 0xa4, 0xc6, 0xf6, 0xff, 0xaf, 0xce, 0xf5, 0xff, 0xbc, 0xd6, 0xf3, 0xff, 0xc1, 0xd9, 0xf2, 0xff, 0xc2, 0xdc, 0xf3, 0xff, 0xc7, 0xdb, 0xf4, 0xff, 0xc8, 0xd9, 0xf4, 0xff, 0xc1, 0xd8, 0xf7, 0xff, 0xb6, 0xd5, 0xf7, 0xff, 0xa2, 0xcb, 0xf2, 0xff, 0x91, 0xc3, 0xf0, 0xff, 0x9a, 0xcd, 0xf2, 0xff, 0xb6, 0xdc, 0xf9, 0xff, 0xc1, 0xd6, 0xfd, 0xff, 0x9d, 0xb7, 0xf0, 0xff, 0x80, 0xae, 0xed, 0xff, 0x86, 0xc3, 0xf5, 0xff, 0x82, 0xc8, 0xfb, 0xff, 0x70, 0x9f, 0xe3, 0xff, 0x74, 0x8d, 0xd7, 0xff, 0x8f, 0xb6, 0xf4, 0xff, 0x89, 0xc4, 0xf8, 0xff, 0x6c, 0x9d, 0xda, 0xff, 0x48, 0x77, 0xb8, 0xff, 0x42, 0x76, 0xb2, 0xff, 0x51, 0x81, 0xbd, 0xff, 0x6c, 0x98, 0xd2, 0xff, 0x8d, 0xb4, 0xeb, 0xff, 0x93, 0xb8, 0xed, 0xff, 0x8c, 0xb4, 0xea, 0xff, 0x78, 0xa5, 0xde, 0xff, 0x5a, 0x88, 0xc3, 0xff, 0x41, 0x6e, 0xac, 0xff, 0x38, 0x61, 0x9d, 0xff, 0x3a, 0x5a, 0x95, 0xff, 0x37, 0x58, 0x93, 0xff, 0x34, 0x54, 0x91, 0xff, 0x32, 0x4f, 0x8a, 0xff, 0x2e, 0x4d, 0x88, 0xff, 0x35, 0x4f, 0x80, 0xff, 0x33, 0x3e, 0x5b, 0xff, 0x22, 0x24, 0x2b, 0xff, 0x26, 0x27, 0x23, 0xff, 0x29, 0x29, 0x27, 0xff, 0x2a, 0x28, 0x26, 0xff, 0x29, 0x2a, 0x2a, 0xff, 0x2c, 0x31, 0x3b, 0xff, 0x32, 0x2e, 0x2e, 0xff, 0x23, 0x25, 0x2c, 0xff, 0x1f, 0x27, 0x36, 0xff, 0x2d, 0x2e, 0x36, 0xff, 0x3c, 0x4c, 0x67, 0xff, 0x3c, 0x5a, 0x88, 0xff, 0x18, 0x24, 0x3a, 0xff, 0x2a, 0x2e, 0x37, 0xff, 0x30, 0x36, 0x47, 0xff, 0x2e, 0x2f, 0x43, 0xff, 0x2f, 0x37, 0x4c, 0xff, 0x21, 0x38, 0x48, 0xff, 0x31, 0x4b, 0x61, 0xff, 0x28, 0x3a, 0x52, 0xff, 0x24, 0x2a, 0x2f, 0xff, 0x2c, 0x30, 0x2f, 0xff, 0x2d, 0x33, 0x37, 0xff, 0x2d, 0x34, 0x35, 0xff, 0x24, 0x26, 0x24, 0xff, 0x29, 0x29, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x27, 0x28, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x24, 0x28, 0x2c, 0xff, 0x3d, 0x35, 0x35, 0xff, 0x59, 0x42, 0x15, 0xff, 0x88, 0x79, 0x2c, 0xff, 0xae, 0xa1, 0x5d, 0xff, 0xb2, 0xb1, 0x65, 0xff, 0xcf, 0xc7, 0x75, 0xff, 0x65, 0x51, 0x30, 0xff, 0x15, 0x12, 0x1b, 0xff, 0x23, 0x2b, 0x2e, 0xff, 0x21, 0x28, 0x26, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x45, 0x2b, 0x1a, 0x58, 0x47, 0x34, 0x1f, 0xff, 0x5e, 0x4e, 0x15, 0xff, 0x70, 0x4f, 0x21, 0xff, 0x6e, 0x56, 0x10, 0xff, 0x8d, 0x69, 0x2e, 0xff, 0xd3, 0xa2, 0x89, 0xff, 0x69, 0x55, 0x54, 0xff, 0x10, 0x13, 0x16, 0xff, 0x28, 0x29, 0x29, 0xff, 0x26, 0x26, 0x26, 0xff, 0x25, 0x25, 0x25, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2b, 0x2a, 0x29, 0xff, 0x1e, 0x16, 0x14, 0xff, 0x39, 0x46, 0x52, 0xff, 0x96, 0xa8, 0xc7, 0xff, 0xb4, 0xd8, 0xf7, 0xff, 0xab, 0xd0, 0xe8, 0xff, 0xbf, 0xe2, 0xfc, 0xff, 0x83, 0xc0, 0xe8, 0xff, 0x98, 0xd0, 0xf9, 0xff, 0x5f, 0x93, 0xbd, 0xff, 0x35, 0x64, 0x8b, 0xff, 0x83, 0xb9, 0xe2, 0xff, 0x79, 0xbb, 0xf0, 0xff, 0x4e, 0x8b, 0xb6, 0xff, 0x1f, 0x4b, 0x6d, 0xff, 0x32, 0x56, 0x7d, 0xff, 0x77, 0xa4, 0xd0, 0xff, 0x95, 0xd8, 0xff, 0xff, 0x46, 0x9a, 0xd9, 0xff, 0x4b, 0x7b, 0xab, 0xff, 0x25, 0x2f, 0x4b, 0xff, 0x30, 0x31, 0x41, 0xff, 0x2e, 0x32, 0x38, 0xff, 0x23, 0x2a, 0x2e, 0xff, 0x2a, 0x2d, 0x2d, 0xff, 0x2f, 0x25, 0x20, 0xff, 0x19, 0x18, 0x29, 0xff, 0x4e, 0x6f, 0x9b, 0xff, 0x8e, 0xc5, 0xff, 0xff, 0x8a, 0xbd, 0xff, 0xff, 0x98, 0xc0, 0xf7, 0xff, 0x9d, 0xc4, 0xf0, 0xff, 0x9b, 0xc4, 0xf1, 0xff, 0xa8, 0xcb, 0xf3, 0xff, 0xb5, 0xd1, 0xf3, 0xff, 0xc0, 0xd7, 0xf4, 0xff, 0xc7, 0xdc, 0xf5, 0xff, 0xce, 0xdc, 0xf8, 0xff, 0xcf, 0xdc, 0xf4, 0xff, 0xc8, 0xdc, 0xee, 0xff, 0xbd, 0xd3, 0xf4, 0xff, 0xbc, 0xd2, 0xfb, 0xff, 0xc8, 0xe5, 0xfe, 0xff, 0xc1, 0xda, 0xfe, 0xff, 0x9c, 0xab, 0xf0, 0xff, 0x78, 0x8a, 0xe5, 0xff, 0x60, 0x77, 0xdd, 0xff, 0x4a, 0x66, 0xc7, 0xff, 0x54, 0x6e, 0xcd, 0xff, 0x55, 0x6a, 0xb2, 0xff, 0x2f, 0x38, 0x6e, 0xff, 0x25, 0x27, 0x62, 0xff, 0x34, 0x3c, 0x72, 0xff, 0x5b, 0x6c, 0xa6, 0xff, 0x8b, 0x98, 0xdf, 0xff, 0x8a, 0xb0, 0xf9, 0xff, 0x5e, 0x9a, 0xdd, 0xff, 0x4d, 0x81, 0xbb, 0xff, 0x59, 0x8a, 0xbf, 0xff, 0x75, 0xa7, 0xdb, 0xff, 0x84, 0xb8, 0xec, 0xff, 0x7a, 0xac, 0xe6, 0xff, 0x68, 0x95, 0xd2, 0xff, 0x4d, 0x7a, 0xb7, 0xff, 0x3d, 0x68, 0xa5, 0xff, 0x37, 0x5e, 0x99, 0xff, 0x35, 0x5a, 0x93, 0xff, 0x3a, 0x58, 0x95, 0xff, 0x38, 0x57, 0x8d, 0xff, 0x30, 0x55, 0x82, 0xff, 0x2f, 0x4d, 0x88, 0xff, 0x38, 0x48, 0x81, 0xff, 0x32, 0x39, 0x4b, 0xff, 0x25, 0x26, 0x1d, 0xff, 0x2c, 0x28, 0x23, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x23, 0x28, 0x2d, 0xff, 0x27, 0x2b, 0x31, 0xff, 0x2d, 0x2d, 0x31, 0xff, 0x31, 0x2a, 0x27, 0xff, 0x29, 0x29, 0x2e, 0xff, 0x1d, 0x22, 0x2c, 0xff, 0x34, 0x3a, 0x40, 0xff, 0x20, 0x29, 0x35, 0xff, 0x4c, 0x6a, 0x92, 0xff, 0x6a, 0x85, 0xac, 0xff, 0x27, 0x34, 0x4d, 0xff, 0x16, 0x15, 0x2a, 0xff, 0x2e, 0x45, 0x5c, 0xff, 0x37, 0x4e, 0x71, 0xff, 0x31, 0x3f, 0x6b, 0xff, 0x2e, 0x51, 0x70, 0xff, 0x2d, 0x39, 0x49, 0xff, 0x28, 0x29, 0x32, 0xff, 0x29, 0x2a, 0x29, 0xff, 0x2a, 0x31, 0x31, 0xff, 0x2b, 0x31, 0x36, 0xff, 0x23, 0x25, 0x2d, 0xff, 0x2a, 0x28, 0x2d, 0xff, 0x2b, 0x27, 0x24, 0xff, 0x2a, 0x2b, 0x27, 0xff, 0x25, 0x29, 0x28, 0xff, 0x22, 0x25, 0x26, 0xff, 0x48, 0x39, 0x38, 0xff, 0x5a, 0x3e, 0x1e, 0xff, 0x89, 0x76, 0x31, 0xff, 0xa4, 0x8e, 0x4c, 0xff, 0x7e, 0x6c, 0x2c, 0xff, 0xab, 0xa0, 0x43, 0xff, 0x95, 0x93, 0x4f, 0xff, 0x1d, 0x24, 0x25, 0xff, 0x1f, 0x22, 0x2d, 0xff, 0x22, 0x25, 0x2e, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x67, 0x42, 0x2f, 0x1b, 0x6c, 0x46, 0x27, 0xff, 0x6f, 0x58, 0x13, 0xff, 0x78, 0x4e, 0x1f, 0xff, 0x6f, 0x4d, 0x19, 0xff, 0x6e, 0x4d, 0x01, 0xff, 0xa4, 0x82, 0x16, 0xff, 0x6b, 0x5e, 0x3f, 0xff, 0x13, 0x13, 0x22, 0xff, 0x28, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x28, 0x27, 0xff, 0x1b, 0x16, 0x16, 0xff, 0x42, 0x4c, 0x4f, 0xff, 0x67, 0x76, 0x89, 0xff, 0x70, 0x92, 0xb4, 0xff, 0x87, 0xb1, 0xd1, 0xff, 0xcd, 0xe7, 0xff, 0xff, 0x94, 0xd0, 0xec, 0xff, 0x97, 0xd3, 0xf8, 0xff, 0x68, 0x9e, 0xc7, 0xff, 0x27, 0x50, 0x72, 0xff, 0x40, 0x66, 0x86, 0xff, 0x69, 0x96, 0xbf, 0xff, 0x7d, 0xb9, 0xea, 0xff, 0x52, 0x8b, 0xbc, 0xff, 0x1d, 0x49, 0x77, 0xff, 0x1f, 0x47, 0x74, 0xff, 0x77, 0xac, 0xd9, 0xff, 0x65, 0xb2, 0xe2, 0xff, 0x2f, 0x67, 0x9a, 0xff, 0x2b, 0x3b, 0x60, 0xff, 0x27, 0x25, 0x34, 0xff, 0x43, 0x47, 0x50, 0xff, 0x1b, 0x29, 0x31, 0xff, 0x1d, 0x24, 0x29, 0xff, 0x30, 0x29, 0x2b, 0xff, 0x1d, 0x1d, 0x20, 0xff, 0x35, 0x4d, 0x67, 0xff, 0x79, 0xac, 0xe4, 0xff, 0x87, 0xc1, 0xff, 0xff, 0x98, 0xc1, 0xf1, 0xff, 0x9c, 0xc5, 0xf0, 0xff, 0x98, 0xc6, 0xf3, 0xff, 0xa2, 0xc9, 0xf2, 0xff, 0xac, 0xcc, 0xf1, 0xff, 0xb8, 0xd3, 0xf5, 0xff, 0xc4, 0xd8, 0xf6, 0xff, 0xc8, 0xdd, 0xfa, 0xff, 0xc7, 0xe0, 0xfa, 0xff, 0xc5, 0xe2, 0xf4, 0xff, 0xd0, 0xee, 0xf6, 0xff, 0xd4, 0xe8, 0xff, 0xff, 0xb3, 0xb7, 0xf2, 0xff, 0x6e, 0x6d, 0xd3, 0xff, 0x47, 0x4f, 0xc8, 0xff, 0x43, 0x56, 0xd0, 0xff, 0x50, 0x64, 0xe0, 0xff, 0x4d, 0x5a, 0xca, 0xff, 0x26, 0x2c, 0x92, 0xff, 0x1b, 0x1b, 0x5b, 0xff, 0x25, 0x23, 0x46, 0xff, 0x2a, 0x2a, 0x4b, 0xff, 0x21, 0x22, 0x40, 0xff, 0x13, 0x15, 0x3a, 0xff, 0x17, 0x23, 0x4d, 0xff, 0x47, 0x57, 0x8a, 0xff, 0x76, 0x8d, 0xcd, 0xff, 0x6c, 0x94, 0xdb, 0xff, 0x58, 0x8a, 0xd7, 0xff, 0x62, 0x93, 0xdc, 0xff, 0x7a, 0xa0, 0xde, 0xff, 0x6a, 0x94, 0xd1, 0xff, 0x4b, 0x7b, 0xba, 0xff, 0x3e, 0x6b, 0xa7, 0xff, 0x39, 0x62, 0x9d, 0xff, 0x35, 0x5b, 0x95, 0xff, 0x33, 0x5a, 0x93, 0xff, 0x39, 0x56, 0x93, 0xff, 0x37, 0x55, 0x88, 0xff, 0x2e, 0x57, 0x7f, 0xff, 0x30, 0x4e, 0x88, 0xff, 0x34, 0x3f, 0x7a, 0xff, 0x2d, 0x33, 0x3e, 0xff, 0x25, 0x29, 0x1d, 0xff, 0x27, 0x28, 0x27, 0xff, 0x24, 0x28, 0x2b, 0xff, 0x24, 0x28, 0x2e, 0xff, 0x25, 0x29, 0x2e, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2d, 0x2b, 0x2e, 0xff, 0x2b, 0x2b, 0x31, 0xff, 0x21, 0x25, 0x2b, 0xff, 0x33, 0x38, 0x38, 0xff, 0x26, 0x2a, 0x2a, 0xff, 0x18, 0x1f, 0x2d, 0xff, 0x72, 0x94, 0xb8, 0xff, 0x6d, 0x8e, 0xb7, 0xff, 0x45, 0x5f, 0x7e, 0xff, 0x4a, 0x7a, 0x9d, 0xff, 0x29, 0x4f, 0x7f, 0xff, 0x40, 0x60, 0x95, 0xff, 0x33, 0x4d, 0x6e, 0xff, 0x22, 0x2b, 0x36, 0xff, 0x29, 0x29, 0x33, 0xff, 0x2f, 0x2f, 0x33, 0xff, 0x20, 0x29, 0x2b, 0xff, 0x34, 0x3a, 0x44, 0xff, 0x31, 0x32, 0x42, 0xff, 0x22, 0x22, 0x27, 0xff, 0x2e, 0x29, 0x23, 0xff, 0x27, 0x28, 0x23, 0xff, 0x22, 0x29, 0x2b, 0xff, 0x2a, 0x2a, 0x28, 0xff, 0x33, 0x2b, 0x2a, 0xff, 0x2d, 0x26, 0x24, 0xff, 0x43, 0x39, 0x1b, 0xff, 0x64, 0x53, 0x1d, 0xff, 0x61, 0x4d, 0x0c, 0xff, 0x97, 0x89, 0x2a, 0xff, 0x80, 0x7e, 0x2f, 0xff, 0x22, 0x23, 0x21, 0xff, 0x28, 0x1f, 0x26, 0xff, 0x2f, 0x25, 0x25, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa2, 0x82, 0x1a, 0xd7, 0xa2, 0x82, 0x17, 0xff, 0xa6, 0x7b, 0x22, 0xff, 0x9a, 0x7d, 0x23, 0xff, 0xa1, 0x80, 0x27, 0xff, 0xc3, 0xa0, 0x38, 0xff, 0x76, 0x6b, 0x3c, 0xff, 0x16, 0x17, 0x1f, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x20, 0x1a, 0x18, 0xff, 0x46, 0x4d, 0x50, 0xff, 0x45, 0x4e, 0x5f, 0xff, 0x62, 0x7d, 0x93, 0xff, 0x6a, 0x83, 0x9c, 0xff, 0xae, 0xc0, 0xd9, 0xff, 0xa3, 0xe0, 0xf2, 0xff, 0x92, 0xd3, 0xf2, 0xff, 0x81, 0xbf, 0xec, 0xff, 0x63, 0x92, 0xbb, 0xff, 0x31, 0x50, 0x6f, 0xff, 0x15, 0x2d, 0x47, 0xff, 0x4f, 0x6e, 0x9c, 0xff, 0x5b, 0x99, 0xcb, 0xff, 0x3f, 0x8b, 0xb6, 0xff, 0x1e, 0x4f, 0x7d, 0xff, 0x2c, 0x4b, 0x7d, 0xff, 0x6b, 0xa4, 0xd5, 0xff, 0x3e, 0x72, 0xa1, 0xff, 0x28, 0x3b, 0x5b, 0xff, 0x23, 0x25, 0x32, 0xff, 0x2c, 0x30, 0x38, 0xff, 0x4a, 0x58, 0x64, 0xff, 0x22, 0x2c, 0x3b, 0xff, 0x1d, 0x1c, 0x1c, 0xff, 0x1f, 0x23, 0x22, 0xff, 0x12, 0x2b, 0x41, 0xff, 0x5e, 0x8f, 0xbe, 0xff, 0x8d, 0xc5, 0xfd, 0xff, 0x96, 0xc4, 0xf1, 0xff, 0x97, 0xc4, 0xef, 0xff, 0x98, 0xc7, 0xf5, 0xff, 0x9d, 0xc8, 0xf2, 0xff, 0xa3, 0xc9, 0xf0, 0xff, 0xab, 0xcd, 0xf1, 0xff, 0xb0, 0xd0, 0xf4, 0xff, 0xc4, 0xd8, 0xf1, 0xff, 0xce, 0xe0, 0xed, 0xff, 0xd3, 0xec, 0xfc, 0xff, 0xc0, 0xd5, 0xff, 0xff, 0x7c, 0x8f, 0xf3, 0xff, 0x36, 0x51, 0xc2, 0xff, 0x2e, 0x3a, 0xb1, 0xff, 0x39, 0x3f, 0xa4, 0xff, 0x30, 0x3d, 0x87, 0xff, 0x34, 0x3a, 0x89, 0xff, 0x35, 0x37, 0x87, 0xff, 0x22, 0x30, 0x63, 0xff, 0x21, 0x2e, 0x3c, 0xff, 0x2a, 0x2c, 0x30, 0xff, 0x2c, 0x2c, 0x34, 0xff, 0x2c, 0x29, 0x36, 0xff, 0x2d, 0x2d, 0x40, 0xff, 0x28, 0x2a, 0x48, 0xff, 0x1d, 0x1b, 0x30, 0xff, 0x25, 0x2a, 0x41, 0xff, 0x3a, 0x5b, 0x91, 0xff, 0x44, 0x78, 0xc2, 0xff, 0x4c, 0x81, 0xca, 0xff, 0x5a, 0x81, 0xbc, 0xff, 0x53, 0x79, 0xb0, 0xff, 0x3e, 0x6a, 0xa3, 0xff, 0x39, 0x62, 0x9b, 0xff, 0x36, 0x5c, 0x95, 0xff, 0x34, 0x58, 0x91, 0xff, 0x36, 0x59, 0x91, 0xff, 0x36, 0x57, 0x91, 0xff, 0x32, 0x54, 0x8a, 0xff, 0x2d, 0x55, 0x86, 0xff, 0x32, 0x50, 0x89, 0xff, 0x2e, 0x3b, 0x6f, 0xff, 0x25, 0x2d, 0x38, 0xff, 0x21, 0x27, 0x24, 0xff, 0x23, 0x27, 0x2b, 0xff, 0x2b, 0x2a, 0x27, 0xff, 0x30, 0x29, 0x24, 0xff, 0x2c, 0x29, 0x25, 0xff, 0x27, 0x28, 0x29, 0xff, 0x26, 0x27, 0x2d, 0xff, 0x26, 0x29, 0x31, 0xff, 0x28, 0x2e, 0x33, 0xff, 0x31, 0x30, 0x2e, 0xff, 0x31, 0x2e, 0x2a, 0xff, 0x19, 0x15, 0x18, 0xff, 0x18, 0x32, 0x52, 0xff, 0x62, 0x94, 0xca, 0xff, 0x6f, 0xb0, 0xdd, 0xff, 0x48, 0x7c, 0xb3, 0xff, 0x3d, 0x71, 0xa9, 0xff, 0x4b, 0x8b, 0xb5, 0xff, 0x3f, 0x50, 0x6a, 0xff, 0x2e, 0x39, 0x46, 0xff, 0x45, 0x46, 0x53, 0xff, 0x43, 0x46, 0x4e, 0xff, 0x1c, 0x28, 0x2e, 0xff, 0x26, 0x2f, 0x3b, 0xff, 0x39, 0x3d, 0x4a, 0xff, 0x2a, 0x2e, 0x2e, 0xff, 0x28, 0x28, 0x24, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x21, 0x26, 0x2c, 0xff, 0x30, 0x29, 0x29, 0xff, 0x2f, 0x2e, 0x28, 0xff, 0x1c, 0x2a, 0x2b, 0xff, 0x2b, 0x28, 0x26, 0xff, 0x60, 0x57, 0x30, 0xff, 0x7f, 0x78, 0x24, 0xff, 0x96, 0x87, 0x24, 0xff, 0x7c, 0x6b, 0x30, 0xff, 0x2c, 0x24, 0x21, 0xff, 0x29, 0x20, 0x15, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xca, 0xc0, 0x64, 0x8e, 0xc8, 0xc1, 0x67, 0xff, 0xc9, 0xba, 0x65, 0xff, 0xc2, 0xbc, 0x64, 0xff, 0xca, 0xb9, 0x64, 0xff, 0xd4, 0xb7, 0x4a, 0xff, 0x95, 0x8d, 0x44, 0xff, 0x1d, 0x1e, 0x1f, 0xff, 0x24, 0x24, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x22, 0x20, 0xff, 0x34, 0x39, 0x3b, 0xff, 0x2b, 0x2f, 0x3d, 0xff, 0x77, 0x8b, 0x9c, 0xff, 0x46, 0x54, 0x67, 0xff, 0x58, 0x65, 0x77, 0xff, 0xbf, 0xec, 0xff, 0xff, 0x8e, 0xbf, 0xe5, 0xff, 0x6a, 0xa1, 0xd4, 0xff, 0x85, 0xb8, 0xe6, 0xff, 0x71, 0x9c, 0xbb, 0xff, 0x15, 0x3e, 0x57, 0xff, 0x11, 0x31, 0x46, 0xff, 0x39, 0x55, 0x6e, 0xff, 0x34, 0x5e, 0x7e, 0xff, 0x5f, 0x9a, 0xbf, 0xff, 0x2b, 0x67, 0x94, 0xff, 0x33, 0x5c, 0x97, 0xff, 0x5a, 0x84, 0xb6, 0xff, 0x26, 0x42, 0x5d, 0xff, 0x31, 0x32, 0x3c, 0xff, 0x19, 0x1e, 0x23, 0xff, 0x2a, 0x39, 0x45, 0xff, 0x58, 0x65, 0x75, 0xff, 0x3a, 0x3d, 0x45, 0xff, 0x1e, 0x20, 0x29, 0xff, 0x1a, 0x2b, 0x46, 0xff, 0x4c, 0x73, 0xa5, 0xff, 0x8c, 0xbe, 0xfd, 0xff, 0x91, 0xbf, 0xf7, 0xff, 0x95, 0xc3, 0xf2, 0xff, 0x97, 0xc7, 0xf4, 0xff, 0x9a, 0xc7, 0xf3, 0xff, 0x9d, 0xc7, 0xf2, 0xff, 0xa2, 0xc9, 0xf2, 0xff, 0xa6, 0xcc, 0xf4, 0xff, 0xa4, 0xd3, 0xea, 0xff, 0xaf, 0xda, 0xf9, 0xff, 0xa2, 0xb2, 0xfa, 0xff, 0x54, 0x59, 0xac, 0xff, 0x27, 0x2b, 0x70, 0xff, 0x1b, 0x1a, 0x68, 0xff, 0x1b, 0x18, 0x54, 0xff, 0x19, 0x1a, 0x28, 0xff, 0x1a, 0x1c, 0x17, 0xff, 0x21, 0x17, 0x21, 0xff, 0x28, 0x18, 0x2e, 0xff, 0x29, 0x23, 0x1e, 0xff, 0x23, 0x28, 0x18, 0xff, 0x22, 0x27, 0x1e, 0xff, 0x25, 0x26, 0x1e, 0xff, 0x28, 0x24, 0x22, 0xff, 0x2a, 0x25, 0x24, 0xff, 0x28, 0x25, 0x22, 0xff, 0x20, 0x1f, 0x26, 0xff, 0x1d, 0x1f, 0x31, 0xff, 0x1b, 0x22, 0x37, 0xff, 0x2b, 0x3c, 0x5c, 0xff, 0x38, 0x58, 0x85, 0xff, 0x39, 0x65, 0xa0, 0xff, 0x38, 0x60, 0x9b, 0xff, 0x39, 0x5c, 0x91, 0xff, 0x36, 0x5a, 0x91, 0xff, 0x33, 0x57, 0x8e, 0xff, 0x32, 0x57, 0x8d, 0xff, 0x34, 0x58, 0x8e, 0xff, 0x35, 0x57, 0x91, 0xff, 0x34, 0x58, 0x92, 0xff, 0x35, 0x5a, 0x90, 0xff, 0x37, 0x52, 0x89, 0xff, 0x30, 0x3e, 0x66, 0xff, 0x24, 0x2b, 0x35, 0xff, 0x22, 0x25, 0x22, 0xff, 0x2b, 0x2a, 0x27, 0xff, 0x31, 0x2a, 0x23, 0xff, 0x35, 0x2b, 0x1f, 0xff, 0x30, 0x29, 0x21, 0xff, 0x26, 0x26, 0x26, 0xff, 0x2b, 0x28, 0x2a, 0xff, 0x2f, 0x2d, 0x2f, 0xff, 0x29, 0x2e, 0x34, 0xff, 0x25, 0x29, 0x2e, 0xff, 0x21, 0x27, 0x31, 0xff, 0x35, 0x45, 0x61, 0xff, 0x39, 0x4f, 0x81, 0xff, 0x48, 0x82, 0xb9, 0xff, 0x40, 0x7e, 0xb2, 0xff, 0x48, 0x6b, 0xab, 0xff, 0x5f, 0x9b, 0xd2, 0xff, 0x2f, 0x58, 0x7c, 0xff, 0x3a, 0x46, 0x59, 0xff, 0x32, 0x3c, 0x49, 0xff, 0x34, 0x35, 0x44, 0xff, 0x2f, 0x34, 0x3e, 0xff, 0x26, 0x34, 0x3d, 0xff, 0x2f, 0x38, 0x46, 0xff, 0x30, 0x35, 0x3f, 0xff, 0x2f, 0x32, 0x31, 0xff, 0x2b, 0x2d, 0x2b, 0xff, 0x24, 0x29, 0x2b, 0xff, 0x23, 0x28, 0x2d, 0xff, 0x30, 0x29, 0x24, 0xff, 0x37, 0x2d, 0x1c, 0xff, 0x31, 0x28, 0x24, 0xff, 0x33, 0x1d, 0x29, 0xff, 0x42, 0x2c, 0x1b, 0xff, 0x60, 0x53, 0x16, 0xff, 0x68, 0x53, 0x1a, 0xff, 0x4e, 0x38, 0x18, 0xff, 0x3f, 0x38, 0x1a, 0xff, 0x7a, 0x71, 0x4f, 0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcf, 0xd3, 0x83, 0x46, 0xd0, 0xd5, 0x8a, 0xff, 0xd9, 0xd7, 0x8c, 0xff, 0xd6, 0xe0, 0x8c, 0xff, 0xd9, 0xd3, 0x83, 0xff, 0xdb, 0xc2, 0x54, 0xff, 0xcf, 0xc6, 0x6d, 0xff, 0x31, 0x32, 0x2e, 0xff, 0x1b, 0x1c, 0x23, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x26, 0x26, 0xff, 0x22, 0x27, 0x2a, 0xff, 0x16, 0x16, 0x21, 0xff, 0x65, 0x73, 0x81, 0xff, 0x46, 0x59, 0x69, 0xff, 0x1a, 0x24, 0x32, 0xff, 0xc7, 0xe7, 0xfa, 0xff, 0x90, 0xb0, 0xd6, 0xff, 0x3f, 0x64, 0x91, 0xff, 0x40, 0x66, 0x8e, 0xff, 0x80, 0xa9, 0xc7, 0xff, 0x78, 0xaa, 0xc9, 0xff, 0x28, 0x50, 0x5e, 0xff, 0x40, 0x4f, 0x5c, 0xff, 0x3d, 0x51, 0x6e, 0xff, 0x52, 0x8d, 0xb1, 0xff, 0x56, 0xa2, 0xcd, 0xff, 0x30, 0x5e, 0x95, 0xff, 0x3d, 0x68, 0x9b, 0xff, 0x2c, 0x4a, 0x68, 0xff, 0x2e, 0x2e, 0x37, 0xff, 0x2b, 0x2d, 0x2d, 0xff, 0x14, 0x1e, 0x25, 0xff, 0x28, 0x31, 0x3c, 0xff, 0x4f, 0x53, 0x60, 0xff, 0x31, 0x34, 0x43, 0xff, 0x1f, 0x2a, 0x45, 0xff, 0x40, 0x5f, 0x92, 0xff, 0x71, 0x9f, 0xe0, 0xff, 0x8b, 0xba, 0xf8, 0xff, 0x97, 0xc5, 0xf7, 0xff, 0x98, 0xc6, 0xf3, 0xff, 0x99, 0xc5, 0xf4, 0xff, 0x99, 0xc5, 0xf2, 0xff, 0x9c, 0xc6, 0xf4, 0xff, 0x9e, 0xc9, 0xf2, 0xff, 0xa4, 0xd1, 0xff, 0xff, 0x78, 0x9a, 0xe9, 0xff, 0x0d, 0x1e, 0x6b, 0xff, 0x00, 0x06, 0x33, 0xff, 0x14, 0x19, 0x35, 0xff, 0x2c, 0x24, 0x53, 0xff, 0x2e, 0x34, 0x6a, 0xff, 0x34, 0x47, 0x79, 0xff, 0x35, 0x43, 0x80, 0xff, 0x2e, 0x3b, 0x80, 0xff, 0x2b, 0x36, 0x7d, 0xff, 0x29, 0x36, 0x79, 0xff, 0x33, 0x33, 0x87, 0xff, 0x37, 0x2d, 0x8d, 0xff, 0x33, 0x2e, 0x80, 0xff, 0x32, 0x2e, 0x6f, 0xff, 0x2d, 0x2c, 0x56, 0xff, 0x2e, 0x35, 0x4a, 0xff, 0x3e, 0x43, 0x53, 0xff, 0x38, 0x33, 0x3d, 0xff, 0x32, 0x22, 0x1e, 0xff, 0x29, 0x1a, 0x11, 0xff, 0x20, 0x20, 0x24, 0xff, 0x1d, 0x32, 0x47, 0xff, 0x27, 0x49, 0x72, 0xff, 0x33, 0x55, 0x8a, 0xff, 0x33, 0x53, 0x88, 0xff, 0x32, 0x53, 0x88, 0xff, 0x33, 0x56, 0x8b, 0xff, 0x36, 0x58, 0x8e, 0xff, 0x38, 0x5c, 0x92, 0xff, 0x3a, 0x5d, 0x96, 0xff, 0x3a, 0x57, 0x92, 0xff, 0x38, 0x4e, 0x7f, 0xff, 0x30, 0x3e, 0x56, 0xff, 0x24, 0x2a, 0x2e, 0xff, 0x27, 0x26, 0x21, 0xff, 0x2d, 0x2a, 0x25, 0xff, 0x20, 0x22, 0x22, 0xff, 0x14, 0x17, 0x1c, 0xff, 0x16, 0x1c, 0x22, 0xff, 0x24, 0x2b, 0x34, 0xff, 0x30, 0x30, 0x34, 0xff, 0x31, 0x2f, 0x30, 0xff, 0x25, 0x2b, 0x35, 0xff, 0x1f, 0x28, 0x35, 0xff, 0x30, 0x3d, 0x55, 0xff, 0x2c, 0x4a, 0x77, 0xff, 0x39, 0x52, 0x8d, 0xff, 0x3c, 0x66, 0x95, 0xff, 0x1f, 0x3f, 0x6b, 0xff, 0x47, 0x60, 0x8d, 0xff, 0x2f, 0x5a, 0x81, 0xff, 0x2d, 0x36, 0x50, 0xff, 0x30, 0x3d, 0x48, 0xff, 0x1b, 0x23, 0x2d, 0xff, 0x2a, 0x2a, 0x36, 0xff, 0x28, 0x2e, 0x38, 0xff, 0x2e, 0x3b, 0x45, 0xff, 0x3b, 0x43, 0x50, 0xff, 0x21, 0x24, 0x2b, 0xff, 0x29, 0x2c, 0x2b, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x24, 0x28, 0x2c, 0xff, 0x1e, 0x21, 0x22, 0xff, 0x49, 0x43, 0x36, 0xff, 0x81, 0x70, 0x44, 0xff, 0x7b, 0x67, 0x28, 0xff, 0x7d, 0x68, 0x36, 0xff, 0x79, 0x69, 0x2f, 0xff, 0x87, 0x7d, 0x38, 0xff, 0x73, 0x6a, 0x25, 0xff, 0x5c, 0x53, 0x1c, 0xff, 0xae, 0xa9, 0x7a, 0xff, 0xd3, 0xcf, 0x99, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0xb6, 0x48, 0x07, 0xca, 0xb9, 0x5c, 0xef, 0xd3, 0xbf, 0x63, 0xff, 0xc8, 0xcb, 0x66, 0xff, 0xc1, 0xb4, 0x61, 0xff, 0xcc, 0xac, 0x3a, 0xff, 0xdb, 0xd4, 0x72, 0xff, 0x3f, 0x44, 0x41, 0xff, 0x15, 0x15, 0x1e, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x28, 0x28, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x27, 0x26, 0xff, 0x30, 0x2b, 0x30, 0xff, 0x2a, 0x32, 0x39, 0xff, 0x17, 0x16, 0x1f, 0xff, 0x5d, 0x6a, 0x76, 0xff, 0x50, 0x5f, 0x70, 0xff, 0x34, 0x3a, 0x4b, 0xff, 0x67, 0x8c, 0x9e, 0xff, 0x8a, 0xa9, 0xc5, 0xff, 0x5d, 0x75, 0x8f, 0xff, 0x2e, 0x3e, 0x51, 0xff, 0x1e, 0x2f, 0x43, 0xff, 0x54, 0x6d, 0x8e, 0xff, 0x5a, 0x80, 0xa3, 0xff, 0x51, 0x82, 0xa5, 0xff, 0x6b, 0xa2, 0xcb, 0xff, 0x45, 0x7a, 0xa9, 0xff, 0x49, 0x7d, 0xab, 0xff, 0x4b, 0x8a, 0xb3, 0xff, 0x2c, 0x66, 0x96, 0xff, 0x29, 0x43, 0x66, 0xff, 0x2c, 0x2a, 0x2f, 0xff, 0x2f, 0x2d, 0x27, 0xff, 0x25, 0x2d, 0x2d, 0xff, 0x21, 0x24, 0x2a, 0xff, 0x1b, 0x23, 0x29, 0xff, 0x35, 0x3f, 0x47, 0xff, 0x2e, 0x3b, 0x4f, 0xff, 0x26, 0x44, 0x6c, 0xff, 0x62, 0x91, 0xc6, 0xff, 0x8c, 0xc3, 0xf9, 0xff, 0x95, 0xc3, 0xf4, 0xff, 0x9d, 0xc5, 0xf4, 0xff, 0x99, 0xc4, 0xf4, 0xff, 0x98, 0xc4, 0xf4, 0xff, 0x97, 0xc5, 0xf5, 0xff, 0x97, 0xc4, 0xf4, 0xff, 0x9d, 0xca, 0xf8, 0xff, 0x75, 0x95, 0xd4, 0xff, 0x42, 0x4e, 0xa2, 0xff, 0x70, 0x75, 0xc0, 0xff, 0xa9, 0xb1, 0xe9, 0xff, 0xae, 0xb6, 0xf6, 0xff, 0x9e, 0xae, 0xef, 0xff, 0x7d, 0x8e, 0xf2, 0xff, 0x5e, 0x70, 0xff, 0xff, 0x5c, 0x7f, 0xfd, 0xff, 0x58, 0x82, 0xe8, 0xff, 0x56, 0x6e, 0xef, 0xff, 0x52, 0x65, 0xe9, 0xff, 0x46, 0x5e, 0xd3, 0xff, 0x43, 0x58, 0xd2, 0xff, 0x47, 0x59, 0xce, 0xff, 0x44, 0x53, 0xbc, 0xff, 0x37, 0x49, 0xa6, 0xff, 0x5d, 0x6c, 0xb7, 0xff, 0x50, 0x5c, 0x9b, 0xff, 0x3e, 0x49, 0x87, 0xff, 0x39, 0x45, 0x7b, 0xff, 0x29, 0x34, 0x62, 0xff, 0x2a, 0x36, 0x56, 0xff, 0x29, 0x3f, 0x67, 0xff, 0x2f, 0x4e, 0x81, 0xff, 0x32, 0x51, 0x83, 0xff, 0x33, 0x52, 0x86, 0xff, 0x32, 0x52, 0x88, 0xff, 0x31, 0x53, 0x8a, 0xff, 0x36, 0x5a, 0x8d, 0xff, 0x37, 0x59, 0x90, 0xff, 0x38, 0x52, 0x8f, 0xff, 0x30, 0x44, 0x70, 0xff, 0x26, 0x33, 0x43, 0xff, 0x24, 0x26, 0x25, 0xff, 0x2a, 0x27, 0x22, 0xff, 0x21, 0x27, 0x29, 0xff, 0x27, 0x39, 0x4d, 0xff, 0x3a, 0x57, 0x7a, 0xff, 0x4d, 0x69, 0x8d, 0xff, 0x3a, 0x4f, 0x6e, 0xff, 0x29, 0x33, 0x41, 0xff, 0x29, 0x2d, 0x34, 0xff, 0x1f, 0x29, 0x37, 0xff, 0x30, 0x37, 0x42, 0xff, 0x33, 0x39, 0x48, 0xff, 0x29, 0x33, 0x56, 0xff, 0x2d, 0x4e, 0x7d, 0xff, 0x29, 0x3d, 0x68, 0xff, 0x2f, 0x37, 0x51, 0xff, 0x2a, 0x4b, 0x5a, 0xff, 0x20, 0x2b, 0x40, 0xff, 0x2c, 0x30, 0x41, 0xff, 0x2b, 0x38, 0x3d, 0xff, 0x30, 0x35, 0x3a, 0xff, 0x30, 0x2d, 0x37, 0xff, 0x25, 0x2a, 0x34, 0xff, 0x3d, 0x4a, 0x54, 0xff, 0x46, 0x4d, 0x57, 0xff, 0x1f, 0x1e, 0x25, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x2c, 0x29, 0x2a, 0xff, 0x26, 0x26, 0x2b, 0xff, 0x22, 0x21, 0x1c, 0xff, 0x55, 0x48, 0x28, 0xff, 0x80, 0x6a, 0x36, 0xff, 0x79, 0x66, 0x25, 0xff, 0x7b, 0x70, 0x1f, 0xff, 0x73, 0x67, 0x1f, 0xff, 0x7c, 0x6b, 0x35, 0xff, 0xb9, 0xac, 0x67, 0xff, 0xbd, 0xb7, 0x71, 0xff, 0xc6, 0xc2, 0x8e, 0xef, 0xb6, 0xb6, 0x91, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa8, 0x87, 0x29, 0xa0, 0xb0, 0x92, 0x2c, 0xff, 0xaf, 0x9b, 0x36, 0xff, 0xab, 0x93, 0x37, 0xff, 0xb4, 0x96, 0x31, 0xff, 0xba, 0xa4, 0x3f, 0xff, 0x52, 0x41, 0x35, 0xff, 0x17, 0x19, 0x1e, 0xff, 0x20, 0x2e, 0x21, 0xff, 0x26, 0x27, 0x2c, 0xff, 0x2c, 0x26, 0x2a, 0xff, 0x26, 0x27, 0x28, 0xff, 0x22, 0x24, 0x24, 0xff, 0x45, 0x49, 0x54, 0xff, 0x72, 0x77, 0x86, 0xff, 0x6d, 0x77, 0x88, 0xff, 0x40, 0x49, 0x55, 0xff, 0x3f, 0x40, 0x4a, 0xff, 0x0a, 0x1a, 0x24, 0xff, 0x2b, 0x3d, 0x4c, 0xff, 0x57, 0x66, 0x78, 0xff, 0x52, 0x61, 0x74, 0xff, 0x36, 0x4b, 0x63, 0xff, 0x2a, 0x42, 0x62, 0xff, 0x39, 0x54, 0x7b, 0xff, 0x4d, 0x7c, 0x9f, 0xff, 0x45, 0x7d, 0x9d, 0xff, 0x43, 0x69, 0x8f, 0xff, 0x3e, 0x63, 0x8b, 0xff, 0x49, 0x92, 0xb7, 0xff, 0x40, 0x81, 0xac, 0xff, 0x2a, 0x46, 0x67, 0xff, 0x25, 0x27, 0x31, 0xff, 0x32, 0x2c, 0x2d, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x29, 0x2e, 0x2f, 0xff, 0x28, 0x2c, 0x2e, 0xff, 0x22, 0x22, 0x23, 0xff, 0x33, 0x38, 0x3f, 0xff, 0x2f, 0x4c, 0x6e, 0xff, 0x49, 0x7a, 0xb1, 0xff, 0x8a, 0xbe, 0xf4, 0xff, 0x91, 0xc1, 0xf3, 0xff, 0x95, 0xc3, 0xf5, 0xff, 0x99, 0xc3, 0xf4, 0xff, 0x9a, 0xc3, 0xf4, 0xff, 0x97, 0xc3, 0xf3, 0xff, 0x94, 0xc2, 0xf3, 0xff, 0x8a, 0xc1, 0xe5, 0xff, 0x96, 0xc7, 0xf1, 0xff, 0xb4, 0xd0, 0xff, 0xff, 0xba, 0xcb, 0xfd, 0xff, 0xc8, 0xd4, 0xf7, 0xff, 0xe2, 0xe4, 0xfe, 0xff, 0xe6, 0xe4, 0xff, 0xff, 0xc3, 0xc6, 0xff, 0xff, 0x73, 0x82, 0xee, 0xff, 0x45, 0x65, 0xda, 0xff, 0x4d, 0x71, 0xdd, 0xff, 0x55, 0x68, 0xe8, 0xff, 0x46, 0x61, 0xd3, 0xff, 0x38, 0x60, 0xc2, 0xff, 0x3b, 0x57, 0xcd, 0xff, 0x3d, 0x53, 0xc9, 0xff, 0x3f, 0x51, 0xb7, 0xff, 0x8d, 0x9c, 0xe0, 0xff, 0x8b, 0x98, 0xd5, 0xff, 0x41, 0x50, 0xb0, 0xff, 0x3b, 0x4e, 0xc1, 0xff, 0x35, 0x4f, 0xc1, 0xff, 0x6b, 0x8c, 0xe8, 0xff, 0x61, 0x82, 0xcb, 0xff, 0x36, 0x54, 0x8c, 0xff, 0x33, 0x4d, 0x7e, 0xff, 0x34, 0x50, 0x81, 0xff, 0x33, 0x50, 0x7f, 0xff, 0x33, 0x51, 0x82, 0xff, 0x31, 0x52, 0x82, 0xff, 0x2d, 0x51, 0x82, 0xff, 0x30, 0x51, 0x84, 0xff, 0x34, 0x48, 0x78, 0xff, 0x2e, 0x39, 0x5a, 0xff, 0x25, 0x29, 0x37, 0xff, 0x23, 0x26, 0x20, 0xff, 0x27, 0x2a, 0x25, 0xff, 0x2f, 0x38, 0x46, 0xff, 0x47, 0x5b, 0x76, 0xff, 0x44, 0x61, 0x89, 0xff, 0x44, 0x64, 0x8f, 0xff, 0x2d, 0x50, 0x78, 0xff, 0x26, 0x3a, 0x53, 0xff, 0x2a, 0x2c, 0x38, 0xff, 0x23, 0x26, 0x31, 0xff, 0x32, 0x33, 0x3a, 0xff, 0x28, 0x2d, 0x34, 0xff, 0x2b, 0x36, 0x46, 0xff, 0x35, 0x51, 0x6e, 0xff, 0x29, 0x38, 0x61, 0xff, 0x18, 0x21, 0x3e, 0xff, 0x32, 0x3e, 0x53, 0xff, 0x2b, 0x26, 0x35, 0xff, 0x2a, 0x2e, 0x30, 0xff, 0x2d, 0x2f, 0x31, 0xff, 0x2b, 0x2b, 0x33, 0xff, 0x33, 0x30, 0x35, 0xff, 0x2a, 0x29, 0x2b, 0xff, 0x3d, 0x41, 0x42, 0xff, 0x3e, 0x46, 0x4b, 0xff, 0x1c, 0x1f, 0x22, 0xff, 0x24, 0x21, 0x1c, 0xff, 0x1f, 0x21, 0x1f, 0xff, 0x1c, 0x21, 0x21, 0xff, 0x2d, 0x30, 0x2d, 0xff, 0x4e, 0x4b, 0x39, 0xff, 0x6e, 0x63, 0x54, 0xff, 0x68, 0x58, 0x4f, 0xff, 0x49, 0x3f, 0x27, 0xff, 0x3b, 0x31, 0x26, 0xff, 0x24, 0x18, 0x17, 0xff, 0x5e, 0x4c, 0x37, 0xff, 0xc4, 0xb7, 0x87, 0xff, 0xb4, 0xb2, 0x7a, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0x75, 0x1b, 0x4a, 0x98, 0x7c, 0x1f, 0xff, 0x9f, 0x82, 0x24, 0xff, 0xa1, 0x87, 0x26, 0xff, 0xac, 0x92, 0x36, 0xff, 0xb0, 0x8e, 0x27, 0xff, 0x67, 0x4b, 0x22, 0xff, 0x1e, 0x1b, 0x25, 0xff, 0x1c, 0x31, 0x23, 0xff, 0x24, 0x27, 0x2b, 0xff, 0x2f, 0x25, 0x2b, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x21, 0x26, 0x22, 0xff, 0x30, 0x31, 0x39, 0xff, 0x3c, 0x43, 0x52, 0xff, 0x22, 0x2a, 0x39, 0xff, 0x1e, 0x21, 0x2b, 0xff, 0x31, 0x31, 0x33, 0xff, 0x3b, 0x37, 0x3a, 0xff, 0x1b, 0x1b, 0x1f, 0xff, 0x28, 0x2d, 0x35, 0xff, 0x33, 0x40, 0x4f, 0xff, 0x37, 0x4b, 0x5e, 0xff, 0x46, 0x5b, 0x6f, 0xff, 0x45, 0x54, 0x6a, 0xff, 0x3a, 0x52, 0x66, 0xff, 0x3c, 0x5e, 0x71, 0xff, 0x35, 0x52, 0x6c, 0xff, 0x3c, 0x61, 0x84, 0xff, 0x60, 0xa4, 0xcc, 0xff, 0x5b, 0x98, 0xc6, 0xff, 0x26, 0x46, 0x66, 0xff, 0x25, 0x2b, 0x38, 0xff, 0x36, 0x30, 0x38, 0xff, 0x25, 0x24, 0x2b, 0xff, 0x26, 0x2d, 0x2d, 0xff, 0x2c, 0x2f, 0x34, 0xff, 0x2e, 0x29, 0x2a, 0xff, 0x1f, 0x1d, 0x1b, 0xff, 0x27, 0x3d, 0x57, 0xff, 0x39, 0x66, 0x9c, 0xff, 0x74, 0xa3, 0xe0, 0xff, 0x90, 0xc3, 0xf8, 0xff, 0x90, 0xc3, 0xf5, 0xff, 0x97, 0xc3, 0xf4, 0xff, 0x9a, 0xc3, 0xf3, 0xff, 0x97, 0xc1, 0xf2, 0xff, 0x91, 0xc1, 0xef, 0xff, 0x91, 0xc1, 0xee, 0xff, 0x96, 0xc3, 0xee, 0xff, 0x9e, 0xc9, 0xf6, 0xff, 0xa4, 0xc7, 0xff, 0xff, 0xa4, 0xb9, 0xfa, 0xff, 0xa0, 0xad, 0xec, 0xff, 0xa0, 0xa3, 0xe5, 0xff, 0x9d, 0x9f, 0xf2, 0xff, 0x60, 0x6b, 0xd5, 0xff, 0x31, 0x43, 0xbb, 0xff, 0x31, 0x45, 0xb8, 0xff, 0x3c, 0x4b, 0xba, 0xff, 0x33, 0x42, 0xa6, 0xff, 0x2d, 0x3d, 0x9d, 0xff, 0x32, 0x3f, 0xa7, 0xff, 0x2c, 0x35, 0x97, 0xff, 0x2f, 0x34, 0x7d, 0xff, 0x59, 0x5d, 0x89, 0xff, 0x3b, 0x42, 0x62, 0xff, 0x25, 0x2f, 0x60, 0xff, 0x32, 0x3d, 0x88, 0xff, 0x40, 0x58, 0xa4, 0xff, 0x4c, 0x70, 0xad, 0xff, 0x37, 0x64, 0x9b, 0xff, 0x31, 0x54, 0x88, 0xff, 0x35, 0x4c, 0x7b, 0xff, 0x32, 0x4c, 0x7b, 0xff, 0x34, 0x4f, 0x7d, 0xff, 0x33, 0x51, 0x7f, 0xff, 0x2e, 0x4e, 0x7e, 0xff, 0x2b, 0x4c, 0x7d, 0xff, 0x30, 0x4a, 0x77, 0xff, 0x2e, 0x3d, 0x5d, 0xff, 0x2a, 0x2e, 0x40, 0xff, 0x24, 0x24, 0x2b, 0xff, 0x27, 0x2a, 0x25, 0xff, 0x2b, 0x31, 0x2f, 0xff, 0x31, 0x38, 0x44, 0xff, 0x2a, 0x34, 0x47, 0xff, 0x28, 0x38, 0x52, 0xff, 0x3a, 0x50, 0x6d, 0xff, 0x40, 0x59, 0x75, 0xff, 0x36, 0x53, 0x68, 0xff, 0x2a, 0x44, 0x4f, 0xff, 0x36, 0x43, 0x4e, 0xff, 0x3c, 0x41, 0x4d, 0xff, 0x22, 0x26, 0x3b, 0xff, 0x22, 0x2b, 0x44, 0xff, 0x33, 0x45, 0x6a, 0xff, 0x34, 0x4f, 0x7a, 0xff, 0x28, 0x38, 0x57, 0xff, 0x30, 0x38, 0x4f, 0xff, 0x26, 0x38, 0x4c, 0xff, 0x27, 0x41, 0x5c, 0xff, 0x2b, 0x3e, 0x59, 0xff, 0x22, 0x2b, 0x3e, 0xff, 0x1a, 0x21, 0x2f, 0xff, 0x20, 0x23, 0x2c, 0xff, 0x37, 0x3b, 0x45, 0xff, 0x2d, 0x3b, 0x4a, 0xff, 0x23, 0x2e, 0x38, 0xff, 0x41, 0x40, 0x41, 0xff, 0x37, 0x39, 0x3e, 0xff, 0x34, 0x37, 0x3a, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x49, 0x44, 0x43, 0xff, 0x54, 0x4d, 0x4c, 0xff, 0x4a, 0x42, 0x3e, 0xff, 0x38, 0x2f, 0x2a, 0xff, 0x2d, 0x29, 0x2e, 0xff, 0x27, 0x26, 0x2d, 0xff, 0x28, 0x1d, 0x0c, 0xff, 0x92, 0x86, 0x57, 0xff, 0xb3, 0xaf, 0x7c, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7f, 0x00, 0x04, 0x95, 0x7b, 0x25, 0xe7, 0x97, 0x7e, 0x21, 0xff, 0x9e, 0x82, 0x22, 0xff, 0xa5, 0x87, 0x28, 0xff, 0xb6, 0x97, 0x2f, 0xff, 0x95, 0x83, 0x34, 0xff, 0x2c, 0x1e, 0x2c, 0xff, 0x1c, 0x24, 0x29, 0xff, 0x26, 0x29, 0x28, 0xff, 0x2d, 0x27, 0x28, 0xff, 0x26, 0x27, 0x29, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x28, 0x28, 0x2b, 0xff, 0x29, 0x2d, 0x34, 0xff, 0x23, 0x29, 0x30, 0xff, 0x2c, 0x2e, 0x34, 0xff, 0x36, 0x36, 0x38, 0xff, 0x3b, 0x37, 0x34, 0xff, 0x2f, 0x2b, 0x26, 0xff, 0x2c, 0x2b, 0x28, 0xff, 0x20, 0x21, 0x21, 0xff, 0x19, 0x1b, 0x1d, 0xff, 0x1f, 0x23, 0x24, 0xff, 0x2e, 0x33, 0x3b, 0xff, 0x1e, 0x2c, 0x39, 0xff, 0x2e, 0x44, 0x53, 0xff, 0x3d, 0x56, 0x6e, 0xff, 0x58, 0x7d, 0x9f, 0xff, 0x82, 0xbd, 0xe5, 0xff, 0x64, 0x9e, 0xcd, 0xff, 0x19, 0x3a, 0x5d, 0xff, 0x2c, 0x33, 0x41, 0xff, 0x33, 0x30, 0x39, 0xff, 0x22, 0x24, 0x2b, 0xff, 0x29, 0x2e, 0x2e, 0xff, 0x29, 0x33, 0x39, 0xff, 0x28, 0x2c, 0x2e, 0xff, 0x2a, 0x25, 0x1d, 0xff, 0x1e, 0x24, 0x2d, 0xff, 0x2b, 0x49, 0x71, 0xff, 0x5d, 0x89, 0xc9, 0xff, 0x8c, 0xbe, 0xf6, 0xff, 0x90, 0xc1, 0xf1, 0xff, 0x94, 0xc2, 0xf5, 0xff, 0x95, 0xc1, 0xf2, 0xff, 0x95, 0xc1, 0xf1, 0xff, 0x90, 0xbf, 0xef, 0xff, 0x8b, 0xbd, 0xe7, 0xff, 0x85, 0xb9, 0xe2, 0xff, 0x7f, 0xb2, 0xe2, 0xff, 0x7c, 0xae, 0xe5, 0xff, 0x89, 0xb6, 0xf1, 0xff, 0x90, 0xbc, 0xf1, 0xff, 0x76, 0x97, 0xe4, 0xff, 0x4d, 0x5d, 0xca, 0xff, 0x35, 0x3e, 0xad, 0xff, 0x30, 0x36, 0x98, 0xff, 0x28, 0x32, 0x79, 0xff, 0x23, 0x30, 0x6a, 0xff, 0x2b, 0x2f, 0x5e, 0xff, 0x2d, 0x2d, 0x51, 0xff, 0x2b, 0x2c, 0x4f, 0xff, 0x2a, 0x2b, 0x49, 0xff, 0x29, 0x27, 0x41, 0xff, 0x20, 0x18, 0x30, 0xff, 0x27, 0x25, 0x36, 0xff, 0x30, 0x36, 0x48, 0xff, 0x39, 0x43, 0x5f, 0xff, 0x31, 0x45, 0x60, 0xff, 0x21, 0x3c, 0x55, 0xff, 0x25, 0x40, 0x63, 0xff, 0x2f, 0x47, 0x70, 0xff, 0x2f, 0x48, 0x6f, 0xff, 0x2f, 0x4a, 0x75, 0xff, 0x34, 0x4f, 0x7e, 0xff, 0x34, 0x4f, 0x80, 0xff, 0x2c, 0x4b, 0x7d, 0xff, 0x30, 0x48, 0x79, 0xff, 0x32, 0x42, 0x68, 0xff, 0x29, 0x34, 0x48, 0xff, 0x21, 0x27, 0x29, 0xff, 0x26, 0x27, 0x23, 0xff, 0x2d, 0x2a, 0x2a, 0xff, 0x2a, 0x29, 0x2b, 0xff, 0x24, 0x29, 0x2c, 0xff, 0x27, 0x2c, 0x36, 0xff, 0x28, 0x2f, 0x3e, 0xff, 0x35, 0x3e, 0x4a, 0xff, 0x3a, 0x42, 0x4a, 0xff, 0x30, 0x40, 0x47, 0xff, 0x28, 0x3f, 0x4a, 0xff, 0x33, 0x42, 0x4f, 0xff, 0x3e, 0x48, 0x5b, 0xff, 0x42, 0x50, 0x6c, 0xff, 0x2f, 0x43, 0x6d, 0xff, 0x19, 0x2c, 0x51, 0xff, 0x3d, 0x55, 0x74, 0xff, 0x54, 0x85, 0xaf, 0xff, 0x4c, 0x84, 0xb7, 0xff, 0x57, 0x89, 0xbd, 0xff, 0x5d, 0x8f, 0xbc, 0xff, 0x68, 0x94, 0xbb, 0xff, 0x5d, 0x7e, 0xa4, 0xff, 0x46, 0x67, 0x8a, 0xff, 0x41, 0x60, 0x80, 0xff, 0x34, 0x50, 0x6e, 0xff, 0x30, 0x4a, 0x6c, 0xff, 0x26, 0x39, 0x55, 0xff, 0x33, 0x3c, 0x4d, 0xff, 0x26, 0x2c, 0x3e, 0xff, 0x29, 0x29, 0x35, 0xff, 0x38, 0x34, 0x35, 0xff, 0x43, 0x36, 0x2e, 0xff, 0x36, 0x29, 0x23, 0xff, 0x32, 0x29, 0x24, 0xff, 0x37, 0x2f, 0x24, 0xff, 0x31, 0x2c, 0x2b, 0xff, 0x27, 0x22, 0x26, 0xff, 0x4a, 0x40, 0x2a, 0xff, 0xa7, 0x9b, 0x6b, 0xe7, 0x99, 0x99, 0x66, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x69, 0x1a, 0x87, 0x9d, 0x81, 0x26, 0xff, 0xa7, 0x88, 0x2a, 0xff, 0xab, 0x8a, 0x2e, 0xff, 0xb1, 0x8f, 0x32, 0xff, 0xb2, 0xa0, 0x38, 0xff, 0x45, 0x33, 0x2c, 0xff, 0x17, 0x19, 0x27, 0xff, 0x27, 0x29, 0x29, 0xff, 0x2d, 0x29, 0x25, 0xff, 0x24, 0x28, 0x29, 0xff, 0x28, 0x26, 0x2c, 0xff, 0x26, 0x24, 0x23, 0xff, 0x30, 0x32, 0x33, 0xff, 0x32, 0x34, 0x36, 0xff, 0x31, 0x30, 0x32, 0xff, 0x35, 0x32, 0x31, 0xff, 0x24, 0x24, 0x22, 0xff, 0x28, 0x29, 0x26, 0xff, 0x2f, 0x2f, 0x2e, 0xff, 0x2d, 0x2d, 0x2c, 0xff, 0x26, 0x27, 0x26, 0xff, 0x33, 0x34, 0x32, 0xff, 0x47, 0x47, 0x47, 0xff, 0x1b, 0x1c, 0x21, 0xff, 0x32, 0x3c, 0x49, 0xff, 0x4e, 0x66, 0x7f, 0xff, 0x79, 0xa1, 0xc4, 0xff, 0x99, 0xce, 0xf8, 0xff, 0x52, 0x84, 0xb1, 0xff, 0x1d, 0x3b, 0x5b, 0xff, 0x3b, 0x42, 0x4d, 0xff, 0x2a, 0x29, 0x2f, 0xff, 0x25, 0x28, 0x2f, 0xff, 0x2a, 0x2f, 0x31, 0xff, 0x2a, 0x35, 0x3b, 0xff, 0x25, 0x2c, 0x2e, 0xff, 0x30, 0x2a, 0x20, 0xff, 0x24, 0x1f, 0x19, 0xff, 0x20, 0x2e, 0x44, 0xff, 0x46, 0x6a, 0xa2, 0xff, 0x7b, 0xac, 0xe5, 0xff, 0x88, 0xb9, 0xeb, 0xff, 0x91, 0xc1, 0xf4, 0xff, 0x91, 0xc1, 0xf3, 0xff, 0x92, 0xc1, 0xf1, 0xff, 0x91, 0xbe, 0xec, 0xff, 0x7d, 0xb1, 0xe6, 0xff, 0x69, 0xa1, 0xdb, 0xff, 0x58, 0x8d, 0xc6, 0xff, 0x43, 0x74, 0xaa, 0xff, 0x2e, 0x5a, 0x8a, 0xff, 0x2d, 0x52, 0x81, 0xff, 0x39, 0x61, 0x83, 0xff, 0x2b, 0x4e, 0x6e, 0xff, 0x1c, 0x28, 0x52, 0xff, 0x1f, 0x1e, 0x3a, 0xff, 0x24, 0x25, 0x28, 0xff, 0x25, 0x29, 0x20, 0xff, 0x26, 0x25, 0x29, 0xff, 0x26, 0x23, 0x2e, 0xff, 0x25, 0x26, 0x25, 0xff, 0x25, 0x27, 0x22, 0xff, 0x24, 0x24, 0x25, 0xff, 0x23, 0x1f, 0x2c, 0xff, 0x26, 0x22, 0x2c, 0xff, 0x29, 0x28, 0x2c, 0xff, 0x23, 0x25, 0x2c, 0xff, 0x1f, 0x28, 0x2a, 0xff, 0x22, 0x2f, 0x36, 0xff, 0x2c, 0x34, 0x50, 0xff, 0x2b, 0x3b, 0x5f, 0xff, 0x29, 0x45, 0x68, 0xff, 0x2e, 0x49, 0x71, 0xff, 0x32, 0x4b, 0x78, 0xff, 0x34, 0x4d, 0x7b, 0xff, 0x32, 0x49, 0x78, 0xff, 0x31, 0x3f, 0x69, 0xff, 0x32, 0x38, 0x52, 0xff, 0x26, 0x2c, 0x32, 0xff, 0x24, 0x27, 0x1d, 0xff, 0x2e, 0x2c, 0x22, 0xff, 0x2e, 0x29, 0x2d, 0xff, 0x28, 0x26, 0x2b, 0xff, 0x27, 0x2a, 0x27, 0xff, 0x27, 0x26, 0x28, 0xff, 0x24, 0x22, 0x24, 0xff, 0x21, 0x1f, 0x1f, 0xff, 0x21, 0x21, 0x1d, 0xff, 0x28, 0x20, 0x1e, 0xff, 0x2f, 0x1c, 0x20, 0xff, 0x25, 0x1c, 0x21, 0xff, 0x18, 0x1c, 0x29, 0xff, 0x1e, 0x35, 0x48, 0xff, 0x40, 0x6b, 0x81, 0xff, 0x57, 0x82, 0xa8, 0xff, 0x3b, 0x5f, 0x93, 0xff, 0x38, 0x6a, 0x95, 0xff, 0x42, 0x85, 0xac, 0xff, 0x56, 0xa1, 0xc9, 0xff, 0x60, 0x92, 0xc4, 0xff, 0x56, 0x82, 0xb5, 0xff, 0x49, 0x87, 0xb3, 0xff, 0x4c, 0x85, 0xb0, 0xff, 0x49, 0x7d, 0xa7, 0xff, 0x49, 0x78, 0x9f, 0xff, 0x5e, 0x88, 0xae, 0xff, 0x55, 0x77, 0x99, 0xff, 0x3a, 0x53, 0x6d, 0xff, 0x34, 0x48, 0x5d, 0xff, 0x34, 0x3d, 0x49, 0xff, 0x37, 0x3a, 0x3a, 0xff, 0x3f, 0x39, 0x31, 0xff, 0x3a, 0x32, 0x2e, 0xff, 0x34, 0x2e, 0x2c, 0xff, 0x33, 0x2e, 0x24, 0xff, 0x2d, 0x2a, 0x2a, 0xff, 0x24, 0x21, 0x24, 0xff, 0x4c, 0x41, 0x29, 0xff, 0xab, 0x9c, 0x6d, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x61, 0x16, 0x22, 0x9d, 0x7d, 0x28, 0xfe, 0xa6, 0x83, 0x2c, 0xff, 0xa8, 0x86, 0x32, 0xff, 0x9c, 0x79, 0x2d, 0xff, 0xad, 0x8d, 0x35, 0xff, 0x6f, 0x61, 0x2d, 0xff, 0x16, 0x16, 0x21, 0xff, 0x28, 0x28, 0x2d, 0xff, 0x2c, 0x29, 0x22, 0xff, 0x24, 0x29, 0x27, 0xff, 0x26, 0x26, 0x2c, 0xff, 0x2a, 0x29, 0x27, 0xff, 0x24, 0x24, 0x24, 0xff, 0x26, 0x26, 0x25, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x24, 0x29, 0x29, 0xff, 0x23, 0x2a, 0x2c, 0xff, 0x24, 0x29, 0x2c, 0xff, 0x24, 0x29, 0x2d, 0xff, 0x1e, 0x22, 0x27, 0xff, 0x2f, 0x33, 0x38, 0xff, 0x46, 0x4b, 0x4b, 0xff, 0x1f, 0x1f, 0x24, 0xff, 0x2d, 0x33, 0x44, 0xff, 0x66, 0x83, 0x9f, 0xff, 0x9a, 0xcb, 0xee, 0xff, 0x94, 0xc7, 0xf2, 0xff, 0x3e, 0x67, 0x8f, 0xff, 0x35, 0x4a, 0x60, 0xff, 0x30, 0x36, 0x3a, 0xff, 0x26, 0x27, 0x2a, 0xff, 0x25, 0x29, 0x31, 0xff, 0x2c, 0x32, 0x39, 0xff, 0x30, 0x35, 0x3a, 0xff, 0x21, 0x26, 0x2a, 0xff, 0x2c, 0x2b, 0x27, 0xff, 0x2e, 0x27, 0x1b, 0xff, 0x1e, 0x20, 0x25, 0xff, 0x2d, 0x46, 0x6b, 0xff, 0x65, 0x92, 0xc7, 0xff, 0x85, 0xb7, 0xec, 0xff, 0x8b, 0xbd, 0xf1, 0xff, 0x8e, 0xc1, 0xf3, 0xff, 0x91, 0xc1, 0xf4, 0xff, 0x92, 0xbe, 0xee, 0xff, 0x81, 0xb1, 0xea, 0xff, 0x6d, 0xa1, 0xe0, 0xff, 0x5c, 0x8b, 0xc5, 0xff, 0x45, 0x71, 0xa5, 0xff, 0x33, 0x58, 0x84, 0xff, 0x1d, 0x3d, 0x64, 0xff, 0x13, 0x34, 0x4d, 0xff, 0x26, 0x42, 0x5d, 0xff, 0x3c, 0x4a, 0x71, 0xff, 0x3d, 0x45, 0x64, 0xff, 0x33, 0x3a, 0x50, 0xff, 0x29, 0x2c, 0x48, 0xff, 0x20, 0x28, 0x3e, 0xff, 0x1f, 0x2b, 0x35, 0xff, 0x21, 0x2c, 0x33, 0xff, 0x23, 0x2f, 0x35, 0xff, 0x25, 0x30, 0x3b, 0xff, 0x24, 0x31, 0x44, 0xff, 0x26, 0x33, 0x49, 0xff, 0x2b, 0x35, 0x4e, 0xff, 0x2f, 0x38, 0x51, 0xff, 0x32, 0x3b, 0x4d, 0xff, 0x31, 0x3c, 0x4e, 0xff, 0x37, 0x3e, 0x60, 0xff, 0x30, 0x40, 0x6a, 0xff, 0x2a, 0x46, 0x6f, 0xff, 0x31, 0x4b, 0x76, 0xff, 0x32, 0x4b, 0x77, 0xff, 0x35, 0x4a, 0x71, 0xff, 0x33, 0x43, 0x66, 0xff, 0x2e, 0x36, 0x4f, 0xff, 0x2c, 0x2e, 0x38, 0xff, 0x24, 0x25, 0x27, 0xff, 0x2b, 0x28, 0x21, 0xff, 0x2f, 0x2b, 0x25, 0xff, 0x2b, 0x2a, 0x2c, 0xff, 0x25, 0x29, 0x2a, 0xff, 0x28, 0x2d, 0x2b, 0xff, 0x2d, 0x2a, 0x28, 0xff, 0x2d, 0x28, 0x26, 0xff, 0x2d, 0x28, 0x26, 0xff, 0x2e, 0x29, 0x25, 0xff, 0x27, 0x2c, 0x25, 0xff, 0x21, 0x2f, 0x26, 0xff, 0x27, 0x2d, 0x29, 0xff, 0x28, 0x28, 0x2a, 0xff, 0x20, 0x1e, 0x26, 0xff, 0x33, 0x2c, 0x34, 0xff, 0x49, 0x50, 0x67, 0xff, 0x3f, 0x62, 0x87, 0xff, 0x46, 0x6f, 0x8d, 0xff, 0x48, 0x70, 0x8f, 0xff, 0x3c, 0x64, 0x84, 0xff, 0x2f, 0x58, 0x7a, 0xff, 0x2b, 0x5d, 0x80, 0xff, 0x3d, 0x75, 0x9a, 0xff, 0x34, 0x66, 0x8b, 0xff, 0x2b, 0x58, 0x7c, 0xff, 0x39, 0x62, 0x81, 0xff, 0x40, 0x64, 0x7f, 0xff, 0x55, 0x76, 0x8f, 0xff, 0x65, 0x81, 0x98, 0xff, 0x50, 0x64, 0x75, 0xff, 0x32, 0x3d, 0x43, 0xff, 0x2e, 0x32, 0x30, 0xff, 0x2b, 0x2a, 0x25, 0xff, 0x2f, 0x2b, 0x29, 0xff, 0x30, 0x2b, 0x2a, 0xff, 0x2d, 0x28, 0x21, 0xff, 0x2c, 0x29, 0x2b, 0xff, 0x22, 0x1f, 0x23, 0xff, 0x4f, 0x43, 0x2a, 0xfe, 0xa7, 0x91, 0x66, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa1, 0x78, 0x20, 0xb4, 0xa4, 0x7d, 0x26, 0xff, 0xa2, 0x81, 0x34, 0xff, 0x7b, 0x5b, 0x1b, 0xff, 0x7f, 0x4d, 0x16, 0xff, 0x98, 0x93, 0x31, 0xff, 0x23, 0x25, 0x2f, 0xff, 0x23, 0x21, 0x2a, 0xff, 0x2d, 0x2c, 0x1e, 0xff, 0x24, 0x2a, 0x26, 0xff, 0x22, 0x27, 0x2b, 0xff, 0x27, 0x27, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x25, 0x27, 0x28, 0xff, 0x25, 0x29, 0x27, 0xff, 0x27, 0x2b, 0x29, 0xff, 0x25, 0x2a, 0x29, 0xff, 0x27, 0x2b, 0x2f, 0xff, 0x32, 0x36, 0x3d, 0xff, 0x2d, 0x31, 0x3b, 0xff, 0x1b, 0x28, 0x2b, 0xff, 0x18, 0x1c, 0x24, 0xff, 0x69, 0x6b, 0x81, 0xff, 0x9a, 0xbd, 0xdb, 0xff, 0x9c, 0xd8, 0xfe, 0xff, 0x60, 0x9a, 0xcb, 0xff, 0x2e, 0x53, 0x78, 0xff, 0x29, 0x38, 0x43, 0xff, 0x24, 0x24, 0x21, 0xff, 0x2c, 0x2b, 0x28, 0xff, 0x28, 0x2d, 0x35, 0xff, 0x2e, 0x36, 0x43, 0xff, 0x2d, 0x2b, 0x2e, 0xff, 0x23, 0x25, 0x28, 0xff, 0x23, 0x27, 0x2d, 0xff, 0x31, 0x2b, 0x21, 0xff, 0x23, 0x20, 0x1a, 0xff, 0x1a, 0x29, 0x3c, 0xff, 0x3f, 0x6d, 0x9f, 0xff, 0x74, 0xab, 0xe6, 0xff, 0x81, 0xb5, 0xea, 0xff, 0x8a, 0xbe, 0xf0, 0xff, 0x90, 0xc1, 0xf3, 0xff, 0x93, 0xc0, 0xf5, 0xff, 0x98, 0xc0, 0xed, 0xff, 0x95, 0xc0, 0xe8, 0xff, 0x8e, 0xbb, 0xed, 0xff, 0x81, 0xb3, 0xe6, 0xff, 0x75, 0xa5, 0xda, 0xff, 0x6f, 0x9e, 0xd0, 0xff, 0x7b, 0xaa, 0xda, 0xff, 0x8b, 0xb8, 0xf1, 0xff, 0x87, 0xb9, 0xf9, 0xff, 0x76, 0xb1, 0xe8, 0xff, 0x65, 0xa0, 0xd4, 0xff, 0x52, 0x87, 0xca, 0xff, 0x48, 0x79, 0xb8, 0xff, 0x43, 0x6f, 0xa6, 0xff, 0x40, 0x67, 0xa3, 0xff, 0x44, 0x69, 0xa5, 0xff, 0x46, 0x6f, 0xa9, 0xff, 0x43, 0x76, 0xaf, 0xff, 0x3d, 0x6e, 0x9f, 0xff, 0x38, 0x5b, 0x8e, 0xff, 0x34, 0x4e, 0x89, 0xff, 0x34, 0x4b, 0x7c, 0xff, 0x2f, 0x4a, 0x70, 0xff, 0x2c, 0x49, 0x74, 0xff, 0x2d, 0x47, 0x76, 0xff, 0x31, 0x49, 0x78, 0xff, 0x33, 0x4f, 0x81, 0xff, 0x30, 0x4a, 0x78, 0xff, 0x2d, 0x40, 0x61, 0xff, 0x2b, 0x35, 0x4c, 0xff, 0x2a, 0x31, 0x38, 0xff, 0x23, 0x28, 0x27, 0xff, 0x28, 0x24, 0x29, 0xff, 0x2d, 0x25, 0x2a, 0xff, 0x2d, 0x29, 0x2c, 0xff, 0x28, 0x2a, 0x29, 0xff, 0x22, 0x29, 0x27, 0xff, 0x27, 0x2c, 0x2c, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2c, 0x27, 0x28, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x27, 0x28, 0x2b, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x2c, 0x2a, 0x2c, 0xff, 0x2a, 0x2a, 0x2b, 0xff, 0x2e, 0x30, 0x30, 0xff, 0x23, 0x2a, 0x2a, 0xff, 0x13, 0x16, 0x0f, 0xff, 0x20, 0x1b, 0x13, 0xff, 0x30, 0x33, 0x3b, 0xff, 0x40, 0x57, 0x6d, 0xff, 0x3a, 0x62, 0x7f, 0xff, 0x4f, 0x76, 0x99, 0xff, 0x50, 0x70, 0x8e, 0xff, 0x38, 0x50, 0x6b, 0xff, 0x47, 0x5f, 0x7c, 0xff, 0x43, 0x5b, 0x76, 0xff, 0x36, 0x47, 0x5d, 0xff, 0x23, 0x2e, 0x3c, 0xff, 0x0e, 0x1b, 0x2a, 0xff, 0x1d, 0x2e, 0x40, 0xff, 0x3b, 0x44, 0x4e, 0xff, 0x34, 0x34, 0x35, 0xff, 0x2e, 0x27, 0x26, 0xff, 0x36, 0x2d, 0x2d, 0xff, 0x32, 0x2c, 0x2e, 0xff, 0x30, 0x2c, 0x2b, 0xff, 0x31, 0x2d, 0x26, 0xff, 0x2e, 0x2a, 0x2c, 0xff, 0x24, 0x21, 0x26, 0xff, 0x57, 0x4d, 0x31, 0xb5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x5b, 0x23, 0x40, 0x8f, 0x65, 0x24, 0xff, 0x9a, 0x76, 0x2a, 0xff, 0x64, 0x46, 0x1b, 0xff, 0x8a, 0x64, 0x2e, 0xff, 0xef, 0xdc, 0x6e, 0xff, 0x68, 0x5a, 0x3b, 0xff, 0x0e, 0x11, 0x20, 0xff, 0x1b, 0x2a, 0x2a, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x2a, 0x26, 0x2a, 0xff, 0x26, 0x27, 0x28, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x26, 0x27, 0x27, 0xff, 0x26, 0x27, 0x28, 0xff, 0x28, 0x2b, 0x28, 0xff, 0x27, 0x2c, 0x29, 0xff, 0x23, 0x2a, 0x27, 0xff, 0x2b, 0x30, 0x2f, 0xff, 0x41, 0x44, 0x44, 0xff, 0x2d, 0x2f, 0x2e, 0xff, 0x0f, 0x11, 0x10, 0xff, 0x46, 0x52, 0x60, 0xff, 0x8c, 0xac, 0xd0, 0xff, 0x90, 0xc5, 0xf3, 0xff, 0x7b, 0xaf, 0xd7, 0xff, 0x2f, 0x52, 0x72, 0xff, 0x1e, 0x28, 0x31, 0xff, 0x22, 0x23, 0x21, 0xff, 0x25, 0x2a, 0x2e, 0xff, 0x28, 0x2f, 0x36, 0xff, 0x36, 0x3d, 0x46, 0xff, 0x2d, 0x33, 0x3b, 0xff, 0x24, 0x23, 0x25, 0xff, 0x29, 0x27, 0x29, 0xff, 0x27, 0x27, 0x2b, 0xff, 0x2e, 0x2b, 0x29, 0xff, 0x26, 0x27, 0x24, 0xff, 0x23, 0x26, 0x28, 0xff, 0x1c, 0x33, 0x56, 0xff, 0x59, 0x80, 0xb5, 0xff, 0x84, 0xb5, 0xe4, 0xff, 0x82, 0xbb, 0xe9, 0xff, 0x8f, 0xc1, 0xf3, 0xff, 0x9e, 0xc3, 0xfc, 0xff, 0xa3, 0xc8, 0xf5, 0xff, 0xa4, 0xcc, 0xf0, 0xff, 0xab, 0xd0, 0xf6, 0xff, 0xaf, 0xd5, 0xfc, 0xff, 0xb0, 0xd7, 0xff, 0xff, 0xab, 0xd5, 0xff, 0xff, 0xb3, 0xd4, 0xfd, 0xff, 0xb2, 0xd1, 0xfa, 0xff, 0x9a, 0xc8, 0xf7, 0xff, 0x89, 0xc2, 0xf4, 0xff, 0x85, 0xc0, 0xf4, 0xff, 0x83, 0xb8, 0xf0, 0xff, 0x75, 0xaa, 0xeb, 0xff, 0x66, 0x9c, 0xe0, 0xff, 0x60, 0x94, 0xd6, 0xff, 0x60, 0x93, 0xd4, 0xff, 0x68, 0x98, 0xd9, 0xff, 0x7f, 0xad, 0xeb, 0xff, 0x7e, 0xae, 0xe7, 0xff, 0x58, 0x88, 0xc4, 0xff, 0x42, 0x6c, 0xb1, 0xff, 0x38, 0x5e, 0x9f, 0xff, 0x32, 0x55, 0x8e, 0xff, 0x34, 0x52, 0x85, 0xff, 0x30, 0x4e, 0x85, 0xff, 0x31, 0x50, 0x89, 0xff, 0x34, 0x4b, 0x79, 0xff, 0x2f, 0x3e, 0x5f, 0xff, 0x2a, 0x33, 0x45, 0xff, 0x27, 0x2f, 0x37, 0xff, 0x25, 0x29, 0x2a, 0xff, 0x27, 0x27, 0x24, 0xff, 0x29, 0x28, 0x29, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x2c, 0x2b, 0xff, 0x25, 0x29, 0x2a, 0xff, 0x27, 0x2b, 0x2f, 0xff, 0x29, 0x2a, 0x2a, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x29, 0x27, 0x27, 0xff, 0x26, 0x28, 0x2b, 0xff, 0x29, 0x28, 0x2c, 0xff, 0x2a, 0x26, 0x29, 0xff, 0x27, 0x2a, 0x2a, 0xff, 0x27, 0x2e, 0x2d, 0xff, 0x2d, 0x35, 0x34, 0xff, 0x29, 0x2b, 0x31, 0xff, 0x26, 0x26, 0x29, 0xff, 0x2a, 0x2b, 0x25, 0xff, 0x24, 0x21, 0x1b, 0xff, 0x21, 0x16, 0x13, 0xff, 0x20, 0x19, 0x1e, 0xff, 0x2b, 0x3f, 0x4d, 0xff, 0x41, 0x57, 0x67, 0xff, 0x36, 0x3c, 0x48, 0xff, 0x28, 0x31, 0x3d, 0xff, 0x2c, 0x34, 0x3d, 0xff, 0x28, 0x2b, 0x31, 0xff, 0x2c, 0x25, 0x28, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x28, 0x2b, 0x2f, 0xff, 0x25, 0x24, 0x25, 0xff, 0x2c, 0x29, 0x29, 0xff, 0x30, 0x2c, 0x2c, 0xff, 0x2f, 0x2b, 0x2b, 0xff, 0x2d, 0x29, 0x2b, 0xff, 0x2e, 0x2d, 0x2c, 0xff, 0x2c, 0x2e, 0x28, 0xff, 0x2b, 0x2b, 0x29, 0xff, 0x2b, 0x24, 0x22, 0xff, 0x4f, 0x3f, 0x2b, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x54, 0x3a, 0x21, 0xcd, 0x79, 0x5c, 0x24, 0xff, 0x58, 0x3e, 0x2a, 0xff, 0x76, 0x59, 0x27, 0xff, 0xce, 0xac, 0x51, 0xff, 0x8d, 0x72, 0x3d, 0xff, 0x15, 0x16, 0x20, 0xff, 0x15, 0x29, 0x2b, 0xff, 0x31, 0x28, 0x24, 0xff, 0x2d, 0x26, 0x27, 0xff, 0x26, 0x28, 0x27, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x27, 0x27, 0xff, 0x2d, 0x27, 0x27, 0xff, 0x2d, 0x24, 0x25, 0xff, 0x29, 0x24, 0x28, 0xff, 0x25, 0x22, 0x28, 0xff, 0x17, 0x14, 0x1b, 0xff, 0x10, 0x0b, 0x0f, 0xff, 0x5a, 0x63, 0x72, 0xff, 0xa2, 0xc2, 0xe1, 0xff, 0x76, 0xa9, 0xd3, 0xff, 0x6a, 0x9f, 0xc9, 0xff, 0x32, 0x4f, 0x68, 0xff, 0x19, 0x1d, 0x21, 0xff, 0x29, 0x22, 0x1a, 0xff, 0x2e, 0x2c, 0x2b, 0xff, 0x26, 0x2e, 0x39, 0xff, 0x30, 0x3b, 0x48, 0xff, 0x2f, 0x36, 0x3d, 0xff, 0x25, 0x26, 0x28, 0xff, 0x26, 0x26, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2c, 0x28, 0x27, 0xff, 0x29, 0x28, 0x29, 0xff, 0x23, 0x25, 0x28, 0xff, 0x30, 0x30, 0x32, 0xff, 0x25, 0x23, 0x2c, 0xff, 0x26, 0x35, 0x51, 0xff, 0x6d, 0x99, 0xc9, 0xff, 0x77, 0xb5, 0xf2, 0xff, 0x82, 0xb9, 0xf3, 0xff, 0xa1, 0xc2, 0xf2, 0xff, 0xa4, 0xc9, 0xf5, 0xff, 0xa4, 0xce, 0xf8, 0xff, 0xb3, 0xd3, 0xf4, 0xff, 0xc1, 0xd9, 0xf3, 0xff, 0xca, 0xde, 0xf6, 0xff, 0xcc, 0xdf, 0xf6, 0xff, 0xc1, 0xda, 0xf6, 0xff, 0xae, 0xd0, 0xf4, 0xff, 0x9c, 0xc8, 0xf1, 0xff, 0x8b, 0xbf, 0xf1, 0xff, 0x80, 0xb6, 0xf0, 0xff, 0x7a, 0xb0, 0xe9, 0xff, 0x74, 0xa7, 0xe3, 0xff, 0x6b, 0x9d, 0xdc, 0xff, 0x64, 0x9b, 0xd6, 0xff, 0x6a, 0xa0, 0xd7, 0xff, 0x79, 0xa7, 0xe0, 0xff, 0x94, 0xb9, 0xf2, 0xff, 0x9b, 0xbd, 0xf5, 0xff, 0x80, 0xa8, 0xe2, 0xff, 0x5d, 0x8a, 0xc8, 0xff, 0x48, 0x77, 0xb7, 0xff, 0x3c, 0x6b, 0xa6, 0xff, 0x39, 0x64, 0x97, 0xff, 0x3c, 0x61, 0x96, 0xff, 0x33, 0x51, 0x84, 0xff, 0x2d, 0x3c, 0x60, 0xff, 0x2f, 0x33, 0x48, 0xff, 0x2d, 0x2d, 0x34, 0xff, 0x24, 0x29, 0x28, 0xff, 0x26, 0x27, 0x25, 0xff, 0x2c, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x24, 0x25, 0x24, 0xff, 0x30, 0x32, 0x32, 0xff, 0x2c, 0x2e, 0x32, 0xff, 0x27, 0x2b, 0x30, 0xff, 0x29, 0x2b, 0x2b, 0xff, 0x29, 0x28, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x29, 0x29, 0xff, 0x2b, 0x2a, 0x2a, 0xff, 0x28, 0x28, 0x27, 0xff, 0x21, 0x28, 0x29, 0xff, 0x27, 0x30, 0x33, 0xff, 0x32, 0x38, 0x3d, 0xff, 0x2b, 0x29, 0x32, 0xff, 0x29, 0x25, 0x2b, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x29, 0xff, 0x27, 0x29, 0x28, 0xff, 0x2a, 0x29, 0x26, 0xff, 0x27, 0x1d, 0x16, 0xff, 0x1b, 0x18, 0x15, 0xff, 0x21, 0x2a, 0x31, 0xff, 0x2b, 0x2e, 0x32, 0xff, 0x26, 0x26, 0x26, 0xff, 0x27, 0x25, 0x24, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x2d, 0x2b, 0x29, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x27, 0x27, 0x26, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x28, 0x28, 0xff, 0x25, 0x28, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x25, 0x2c, 0x26, 0xff, 0x27, 0x2a, 0x27, 0xff, 0x2b, 0x25, 0x22, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x32, 0x25, 0x51, 0x62, 0x4c, 0x20, 0xff, 0x66, 0x46, 0x27, 0xff, 0x5c, 0x3a, 0x22, 0xff, 0x5c, 0x3b, 0x0d, 0xff, 0x5c, 0x41, 0x24, 0xff, 0x32, 0x2d, 0x2e, 0xff, 0x1d, 0x26, 0x23, 0xff, 0x2c, 0x29, 0x22, 0xff, 0x2c, 0x28, 0x28, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x35, 0x35, 0x35, 0xff, 0x33, 0x33, 0x32, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x31, 0x30, 0x33, 0xff, 0x2b, 0x2f, 0x38, 0xff, 0x25, 0x33, 0x41, 0xff, 0x43, 0x59, 0x6e, 0xff, 0x6f, 0x88, 0xa0, 0xff, 0x87, 0xad, 0xd6, 0xff, 0x6f, 0x9c, 0xc7, 0xff, 0x68, 0x8e, 0xae, 0xff, 0x2b, 0x45, 0x58, 0xff, 0x0d, 0x16, 0x19, 0xff, 0x2e, 0x26, 0x1f, 0xff, 0x2e, 0x2b, 0x2a, 0xff, 0x2a, 0x30, 0x36, 0xff, 0x2d, 0x33, 0x3b, 0xff, 0x29, 0x30, 0x38, 0xff, 0x21, 0x25, 0x29, 0xff, 0x2a, 0x28, 0x25, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x27, 0x28, 0xff, 0x23, 0x25, 0x2a, 0xff, 0x23, 0x2b, 0x33, 0xff, 0x37, 0x31, 0x2b, 0xff, 0x19, 0x17, 0x1a, 0xff, 0x21, 0x3c, 0x67, 0xff, 0x63, 0x93, 0xd5, 0xff, 0x7a, 0xb0, 0xee, 0xff, 0x8a, 0xb8, 0xe7, 0xff, 0x96, 0xc3, 0xef, 0xff, 0x9c, 0xc9, 0xf5, 0xff, 0xac, 0xd0, 0xf4, 0xff, 0xbe, 0xd9, 0xf5, 0xff, 0xcb, 0xdd, 0xf5, 0xff, 0xd5, 0xe1, 0xf3, 0xff, 0xca, 0xde, 0xf5, 0xff, 0xb6, 0xd4, 0xf6, 0xff, 0xa5, 0xcd, 0xf4, 0xff, 0x93, 0xc3, 0xf2, 0xff, 0x86, 0xba, 0xf0, 0xff, 0x7c, 0xb2, 0xe9, 0xff, 0x75, 0xa9, 0xe2, 0xff, 0x6e, 0xa1, 0xdb, 0xff, 0x6b, 0x9d, 0xd6, 0xff, 0x71, 0xa2, 0xd9, 0xff, 0x85, 0xb2, 0xe7, 0xff, 0x9a, 0xc2, 0xf5, 0xff, 0x8d, 0xb2, 0xea, 0xff, 0x6b, 0x94, 0xd1, 0xff, 0x4d, 0x7d, 0xbc, 0xff, 0x42, 0x75, 0xb4, 0xff, 0x3f, 0x74, 0xaf, 0xff, 0x34, 0x6e, 0xa4, 0xff, 0x3a, 0x5d, 0x88, 0xff, 0x36, 0x43, 0x63, 0xff, 0x26, 0x31, 0x48, 0xff, 0x27, 0x2b, 0x37, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2b, 0x29, 0x25, 0xff, 0x2b, 0x2a, 0x26, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x26, 0x26, 0xff, 0x2d, 0x2f, 0x2f, 0xff, 0x30, 0x33, 0x36, 0xff, 0x26, 0x2a, 0x2f, 0xff, 0x28, 0x2a, 0x2b, 0xff, 0x28, 0x27, 0x26, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x27, 0x2b, 0x28, 0xff, 0x23, 0x29, 0x25, 0xff, 0x22, 0x27, 0x33, 0xff, 0x2e, 0x34, 0x47, 0xff, 0x31, 0x37, 0x45, 0xff, 0x25, 0x28, 0x29, 0xff, 0x27, 0x29, 0x25, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x26, 0x26, 0x29, 0xff, 0x26, 0x25, 0x28, 0xff, 0x2a, 0x2b, 0x2a, 0xff, 0x2b, 0x2b, 0x2a, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x27, 0x2a, 0x25, 0xff, 0x25, 0x28, 0x28, 0xff, 0x28, 0x28, 0x2c, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x4b, 0x36, 0x25, 0xce, 0x5d, 0x38, 0x2d, 0xff, 0x56, 0x36, 0x38, 0xff, 0x51, 0x34, 0x2f, 0xff, 0x55, 0x3b, 0x2e, 0xff, 0x3c, 0x30, 0x2b, 0xff, 0x1e, 0x1f, 0x27, 0xff, 0x23, 0x26, 0x30, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x26, 0x26, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2f, 0x2e, 0x2d, 0xff, 0x23, 0x2c, 0x2d, 0xff, 0x25, 0x35, 0x38, 0xff, 0x2a, 0x3e, 0x45, 0xff, 0x4f, 0x6d, 0x7c, 0xff, 0x7b, 0xa6, 0xc0, 0xff, 0x6e, 0xa4, 0xc8, 0xff, 0x42, 0x6d, 0x94, 0xff, 0x39, 0x57, 0x75, 0xff, 0x2b, 0x3f, 0x4c, 0xff, 0x12, 0x19, 0x1b, 0xff, 0x2b, 0x2c, 0x2b, 0xff, 0x32, 0x2e, 0x2f, 0xff, 0x2d, 0x2e, 0x32, 0xff, 0x2d, 0x32, 0x37, 0xff, 0x27, 0x2b, 0x30, 0xff, 0x21, 0x24, 0x27, 0xff, 0x27, 0x27, 0x28, 0xff, 0x2e, 0x2a, 0x26, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x29, 0x27, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x1e, 0x22, 0x27, 0xff, 0x36, 0x3d, 0x49, 0xff, 0x42, 0x44, 0x45, 0xff, 0x25, 0x23, 0x20, 0xff, 0x18, 0x16, 0x23, 0xff, 0x2e, 0x37, 0x54, 0xff, 0x62, 0x86, 0xb4, 0xff, 0x6d, 0xac, 0xe6, 0xff, 0x75, 0xb3, 0xed, 0xff, 0x88, 0xba, 0xef, 0xff, 0x93, 0xc0, 0xf1, 0xff, 0xa6, 0xcb, 0xf4, 0xff, 0xb7, 0xd4, 0xf6, 0xff, 0xc7, 0xda, 0xf8, 0xff, 0xc2, 0xda, 0xf6, 0xff, 0xb4, 0xd6, 0xf6, 0xff, 0xa8, 0xce, 0xf7, 0xff, 0x97, 0xc4, 0xf4, 0xff, 0x89, 0xbc, 0xf1, 0xff, 0x7a, 0xb0, 0xe7, 0xff, 0x6a, 0xa3, 0xde, 0xff, 0x61, 0x9b, 0xda, 0xff, 0x63, 0x98, 0xd6, 0xff, 0x6b, 0x9e, 0xda, 0xff, 0x79, 0xa9, 0xe4, 0xff, 0x7d, 0xa9, 0xe2, 0xff, 0x62, 0x93, 0xd0, 0xff, 0x46, 0x7b, 0xbf, 0xff, 0x3f, 0x72, 0xb5, 0xff, 0x40, 0x6c, 0xae, 0xff, 0x43, 0x66, 0xa3, 0xff, 0x34, 0x52, 0x8c, 0xff, 0x30, 0x40, 0x63, 0xff, 0x2b, 0x31, 0x3e, 0xff, 0x23, 0x27, 0x33, 0xff, 0x28, 0x28, 0x2a, 0xff, 0x2d, 0x29, 0x27, 0xff, 0x2f, 0x2a, 0x24, 0xff, 0x2c, 0x29, 0x25, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x26, 0x27, 0x27, 0xff, 0x2e, 0x31, 0x34, 0xff, 0x2b, 0x30, 0x35, 0xff, 0x29, 0x2b, 0x2c, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x26, 0x2b, 0x29, 0xff, 0x21, 0x29, 0x25, 0xff, 0x26, 0x2c, 0x3b, 0xff, 0x35, 0x3c, 0x54, 0xff, 0x29, 0x2f, 0x3e, 0xff, 0x23, 0x27, 0x23, 0xff, 0x28, 0x2a, 0x23, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2a, 0x27, 0x28, 0xff, 0x2e, 0x28, 0x29, 0xff, 0x2b, 0x27, 0x27, 0xff, 0x27, 0x26, 0x25, 0xff, 0x29, 0x2a, 0x27, 0xff, 0x29, 0x2b, 0x28, 0xff, 0x27, 0x28, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x2b, 0x28, 0x28, 0xff, 0x28, 0x29, 0x24, 0xff, 0x25, 0x27, 0x28, 0xce, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, 0x2b, 0x20, 0x46, 0x3b, 0x2b, 0x23, 0xff, 0x42, 0x2a, 0x1d, 0xff, 0x45, 0x2a, 0x1b, 0xff, 0x51, 0x39, 0x18, 0xff, 0x56, 0x44, 0x1f, 0xff, 0x2c, 0x22, 0x22, 0xff, 0x1d, 0x23, 0x31, 0xff, 0x25, 0x29, 0x2b, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x26, 0x27, 0x26, 0xff, 0x2a, 0x25, 0x29, 0xff, 0x28, 0x20, 0x26, 0xff, 0x35, 0x32, 0x37, 0xff, 0x41, 0x44, 0x50, 0xff, 0x2c, 0x39, 0x4f, 0xff, 0x21, 0x38, 0x59, 0xff, 0x34, 0x47, 0x5a, 0xff, 0x25, 0x2f, 0x35, 0xff, 0x1a, 0x1e, 0x1e, 0xff, 0x31, 0x32, 0x2e, 0xff, 0x2d, 0x2f, 0x33, 0xff, 0x23, 0x2a, 0x36, 0xff, 0x26, 0x2a, 0x30, 0xff, 0x29, 0x2a, 0x29, 0xff, 0x26, 0x27, 0x29, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x28, 0x28, 0x27, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2b, 0x29, 0x29, 0xff, 0x2b, 0x29, 0x2a, 0xff, 0x22, 0x23, 0x25, 0xff, 0x23, 0x28, 0x2f, 0xff, 0x59, 0x60, 0x6e, 0xff, 0x2a, 0x35, 0x41, 0xff, 0x22, 0x26, 0x28, 0xff, 0x3b, 0x2e, 0x28, 0xff, 0x2b, 0x21, 0x1f, 0xff, 0x21, 0x32, 0x4b, 0xff, 0x3f, 0x71, 0xa8, 0xff, 0x69, 0xa2, 0xdf, 0xff, 0x78, 0xab, 0xe4, 0xff, 0x80, 0xb2, 0xeb, 0xff, 0x88, 0xb8, 0xee, 0xff, 0x90, 0xbd, 0xee, 0xff, 0x9b, 0xc2, 0xf0, 0xff, 0x9f, 0xc6, 0xf1, 0xff, 0x9f, 0xc9, 0xf3, 0xff, 0x97, 0xc2, 0xf3, 0xff, 0x8c, 0xbb, 0xf0, 0xff, 0x7c, 0xb1, 0xe8, 0xff, 0x6c, 0xa4, 0xda, 0xff, 0x5b, 0x95, 0xd0, 0xff, 0x49, 0x82, 0xc2, 0xff, 0x48, 0x79, 0xb8, 0xff, 0x4a, 0x78, 0xb6, 0xff, 0x4c, 0x79, 0xb5, 0xff, 0x4c, 0x78, 0xb4, 0xff, 0x3f, 0x6f, 0xab, 0xff, 0x36, 0x67, 0xa5, 0xff, 0x38, 0x62, 0x9f, 0xff, 0x3c, 0x5a, 0x92, 0xff, 0x3a, 0x4a, 0x78, 0xff, 0x33, 0x32, 0x5a, 0xff, 0x28, 0x26, 0x3c, 0xff, 0x23, 0x28, 0x2b, 0xff, 0x26, 0x2a, 0x2e, 0xff, 0x2a, 0x2c, 0x2b, 0xff, 0x2c, 0x2a, 0x28, 0xff, 0x2c, 0x29, 0x27, 0xff, 0x2c, 0x29, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x25, 0x26, 0x26, 0xff, 0x2a, 0x2e, 0x32, 0xff, 0x31, 0x36, 0x3d, 0xff, 0x2a, 0x2c, 0x2f, 0xff, 0x27, 0x27, 0x28, 0xff, 0x28, 0x29, 0x29, 0xff, 0x28, 0x2a, 0x2c, 0xff, 0x26, 0x29, 0x2d, 0xff, 0x24, 0x28, 0x2e, 0xff, 0x30, 0x36, 0x43, 0xff, 0x31, 0x3a, 0x48, 0xff, 0x21, 0x27, 0x2f, 0xff, 0x26, 0x28, 0x23, 0xff, 0x2b, 0x2b, 0x25, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x28, 0x29, 0x25, 0xff, 0x27, 0x27, 0x27, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x53, 0x24, 0xb4, 0x85, 0x6d, 0x2a, 0xff, 0x97, 0x7d, 0x40, 0xff, 0xb6, 0x9c, 0x47, 0xff, 0xdb, 0xc5, 0x65, 0xff, 0x83, 0x6f, 0x50, 0xff, 0x11, 0x17, 0x10, 0xff, 0x25, 0x2b, 0x27, 0xff, 0x29, 0x27, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x2b, 0xff, 0x29, 0x27, 0x28, 0xff, 0x2e, 0x29, 0x26, 0xff, 0x23, 0x20, 0x1e, 0xff, 0x14, 0x17, 0x1f, 0xff, 0x47, 0x53, 0x66, 0xff, 0x35, 0x3b, 0x41, 0xff, 0x0d, 0x0d, 0x0a, 0xff, 0x2e, 0x2b, 0x2a, 0xff, 0x2d, 0x2b, 0x2a, 0xff, 0x27, 0x29, 0x28, 0xff, 0x21, 0x27, 0x2b, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2d, 0x28, 0x24, 0xff, 0x2d, 0x29, 0x25, 0xff, 0x2a, 0x29, 0x27, 0xff, 0x28, 0x29, 0x29, 0xff, 0x25, 0x29, 0x2b, 0xff, 0x28, 0x27, 0x28, 0xff, 0x2c, 0x28, 0x27, 0xff, 0x2c, 0x28, 0x27, 0xff, 0x11, 0x12, 0x13, 0xff, 0x5c, 0x62, 0x6a, 0xff, 0x49, 0x54, 0x67, 0xff, 0x15, 0x19, 0x1e, 0xff, 0x2d, 0x2c, 0x28, 0xff, 0x2a, 0x2d, 0x2e, 0xff, 0x2c, 0x32, 0x3a, 0xff, 0x21, 0x2d, 0x40, 0xff, 0x24, 0x30, 0x4a, 0xff, 0x3b, 0x5a, 0x85, 0xff, 0x62, 0x90, 0xc5, 0xff, 0x7a, 0xaa, 0xe4, 0xff, 0x78, 0xac, 0xe7, 0xff, 0x79, 0xae, 0xe6, 0xff, 0x7d, 0xb0, 0xe5, 0xff, 0x7e, 0xb2, 0xe7, 0xff, 0x7f, 0xb3, 0xea, 0xff, 0x7b, 0xad, 0xe7, 0xff, 0x73, 0xa4, 0xe2, 0xff, 0x67, 0x9d, 0xda, 0xff, 0x58, 0x97, 0xd2, 0xff, 0x44, 0x7e, 0xb8, 0xff, 0x34, 0x64, 0x9e, 0xff, 0x31, 0x5a, 0x94, 0xff, 0x34, 0x57, 0x90, 0xff, 0x34, 0x58, 0x90, 0xff, 0x2e, 0x57, 0x8e, 0xff, 0x32, 0x53, 0x87, 0xff, 0x34, 0x4d, 0x7f, 0xff, 0x2d, 0x47, 0x76, 0xff, 0x26, 0x39, 0x5d, 0xff, 0x22, 0x2c, 0x3d, 0xff, 0x2a, 0x29, 0x26, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x2a, 0x2d, 0x33, 0xff, 0x2a, 0x2e, 0x2f, 0xff, 0x26, 0x2c, 0x2a, 0xff, 0x26, 0x2a, 0x29, 0xff, 0x27, 0x28, 0x2b, 0xff, 0x29, 0x28, 0x29, 0xff, 0x2a, 0x28, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x27, 0x27, 0xff, 0x24, 0x29, 0x2d, 0xff, 0x2b, 0x31, 0x39, 0xff, 0x2c, 0x2f, 0x33, 0xff, 0x25, 0x28, 0x28, 0xff, 0x27, 0x29, 0x29, 0xff, 0x27, 0x2a, 0x2a, 0xff, 0x27, 0x26, 0x2d, 0xff, 0x31, 0x2f, 0x3b, 0xff, 0x34, 0x3b, 0x40, 0xff, 0x21, 0x2d, 0x2b, 0xff, 0x21, 0x26, 0x24, 0xff, 0x2c, 0x28, 0x24, 0xff, 0x2c, 0x28, 0x26, 0xff, 0x28, 0x28, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x27, 0x28, 0xff, 0x27, 0x29, 0x29, 0xff, 0x23, 0x29, 0x28, 0xff, 0x24, 0x28, 0x29, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x28, 0x28, 0x2a, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x27, 0x2b, 0xff, 0x28, 0x27, 0x2d, 0xff, 0x2a, 0x2a, 0x24, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd1, 0xa3, 0x3a, 0x27, 0xce, 0xb1, 0x4a, 0xfa, 0xcf, 0xb3, 0x55, 0xff, 0xda, 0xb8, 0x4d, 0xff, 0xdd, 0xbb, 0x50, 0xff, 0xac, 0x96, 0x56, 0xff, 0x2d, 0x32, 0x11, 0xff, 0x1f, 0x24, 0x21, 0xff, 0x2a, 0x26, 0x2d, 0xff, 0x2b, 0x25, 0x2b, 0xff, 0x2b, 0x25, 0x2b, 0xff, 0x29, 0x28, 0x28, 0xff, 0x26, 0x2a, 0x24, 0xff, 0x23, 0x2a, 0x27, 0xff, 0x24, 0x29, 0x28, 0xff, 0x25, 0x28, 0x25, 0xff, 0x25, 0x28, 0x25, 0xff, 0x1f, 0x23, 0x22, 0xff, 0x2c, 0x33, 0x34, 0xff, 0x66, 0x6e, 0x7b, 0xff, 0x40, 0x45, 0x54, 0xff, 0x21, 0x22, 0x26, 0xff, 0x28, 0x28, 0x26, 0xff, 0x29, 0x2a, 0x25, 0xff, 0x29, 0x2a, 0x27, 0xff, 0x2b, 0x2a, 0x28, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x29, 0x27, 0x27, 0xff, 0x28, 0x27, 0x27, 0xff, 0x27, 0x27, 0x28, 0xff, 0x28, 0x28, 0x2b, 0xff, 0x2b, 0x2b, 0x2d, 0xff, 0x25, 0x27, 0x29, 0xff, 0x0e, 0x13, 0x1a, 0xff, 0x59, 0x60, 0x6a, 0xff, 0x76, 0x7d, 0x86, 0xff, 0x15, 0x1b, 0x23, 0xff, 0x25, 0x25, 0x27, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x25, 0x2c, 0x30, 0xff, 0x25, 0x2f, 0x39, 0xff, 0x2c, 0x37, 0x48, 0xff, 0x35, 0x39, 0x4d, 0xff, 0x22, 0x2e, 0x4e, 0xff, 0x24, 0x3f, 0x68, 0xff, 0x4b, 0x6d, 0x99, 0xff, 0x6b, 0x98, 0xcb, 0xff, 0x71, 0xa5, 0xde, 0xff, 0x6c, 0xa4, 0xe1, 0xff, 0x6d, 0xa3, 0xde, 0xff, 0x67, 0x9b, 0xd7, 0xff, 0x5c, 0x92, 0xd3, 0xff, 0x56, 0x8c, 0xcf, 0xff, 0x56, 0x8b, 0xc9, 0xff, 0x46, 0x7e, 0xb7, 0xff, 0x35, 0x5c, 0x8a, 0xff, 0x31, 0x49, 0x72, 0xff, 0x2d, 0x45, 0x6f, 0xff, 0x30, 0x43, 0x69, 0xff, 0x2f, 0x41, 0x64, 0xff, 0x2a, 0x3f, 0x61, 0xff, 0x33, 0x40, 0x5b, 0xff, 0x33, 0x38, 0x4c, 0xff, 0x22, 0x2d, 0x3c, 0xff, 0x1d, 0x26, 0x2e, 0xff, 0x23, 0x27, 0x2a, 0xff, 0x2b, 0x2d, 0x2c, 0xff, 0x2e, 0x30, 0x33, 0xff, 0x2c, 0x2f, 0x37, 0xff, 0x29, 0x2d, 0x30, 0xff, 0x26, 0x2b, 0x2d, 0xff, 0x26, 0x2a, 0x2c, 0xff, 0x25, 0x28, 0x2c, 0xff, 0x28, 0x28, 0x29, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x27, 0x27, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x29, 0x2b, 0x2c, 0xff, 0x28, 0x2a, 0x2e, 0xff, 0x2c, 0x2b, 0x2c, 0xff, 0x2c, 0x2b, 0x2d, 0xff, 0x24, 0x27, 0x2d, 0xff, 0x1f, 0x2a, 0x35, 0xff, 0x25, 0x30, 0x3e, 0xff, 0x2e, 0x36, 0x42, 0xff, 0x2c, 0x33, 0x3b, 0xff, 0x2b, 0x30, 0x34, 0xff, 0x28, 0x2a, 0x2c, 0xff, 0x2b, 0x27, 0x2a, 0xff, 0x2b, 0x29, 0x29, 0xff, 0x29, 0x2a, 0x2a, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x29, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x2a, 0xff, 0x27, 0x27, 0x2a, 0xfa, 0x2c, 0x2c, 0x26, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x49, 0x23, 0x88, 0x60, 0x45, 0x26, 0xff, 0x65, 0x43, 0x21, 0xff, 0x65, 0x3c, 0x1a, 0xff, 0x61, 0x43, 0x29, 0xff, 0x43, 0x35, 0x35, 0xff, 0x21, 0x24, 0x24, 0xff, 0x25, 0x2a, 0x25, 0xff, 0x2a, 0x28, 0x29, 0xff, 0x2c, 0x27, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x25, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x26, 0xff, 0x20, 0x1d, 0x19, 0xff, 0x45, 0x4c, 0x5a, 0xff, 0x51, 0x5b, 0x6c, 0xff, 0x26, 0x2a, 0x2d, 0xff, 0x25, 0x26, 0x22, 0xff, 0x2a, 0x2a, 0x26, 0xff, 0x29, 0x2a, 0x29, 0xff, 0x29, 0x29, 0x2c, 0xff, 0x26, 0x25, 0x29, 0xff, 0x25, 0x24, 0x27, 0xff, 0x27, 0x27, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x27, 0x29, 0xff, 0x25, 0x26, 0x26, 0xff, 0x2a, 0x30, 0x33, 0xff, 0x45, 0x4e, 0x5a, 0xff, 0x64, 0x70, 0x7d, 0xff, 0x22, 0x29, 0x31, 0xff, 0x1d, 0x1d, 0x1d, 0xff, 0x27, 0x2a, 0x2d, 0xff, 0x25, 0x2a, 0x30, 0xff, 0x2b, 0x2e, 0x2e, 0xff, 0x2c, 0x31, 0x34, 0xff, 0x27, 0x32, 0x41, 0xff, 0x2e, 0x3c, 0x58, 0xff, 0x3c, 0x48, 0x67, 0xff, 0x2e, 0x3c, 0x5a, 0xff, 0x1b, 0x33, 0x53, 0xff, 0x27, 0x49, 0x6f, 0xff, 0x41, 0x69, 0x96, 0xff, 0x56, 0x80, 0xb2, 0xff, 0x5a, 0x85, 0xbc, 0xff, 0x56, 0x83, 0xba, 0xff, 0x4e, 0x7d, 0xb4, 0xff, 0x40, 0x6b, 0x9d, 0xff, 0x2f, 0x50, 0x76, 0xff, 0x1d, 0x35, 0x50, 0xff, 0x21, 0x2e, 0x42, 0xff, 0x2a, 0x30, 0x43, 0xff, 0x2c, 0x35, 0x47, 0xff, 0x2d, 0x34, 0x45, 0xff, 0x2d, 0x32, 0x3f, 0xff, 0x29, 0x2e, 0x3a, 0xff, 0x29, 0x2b, 0x34, 0xff, 0x24, 0x27, 0x2a, 0xff, 0x23, 0x28, 0x29, 0xff, 0x2a, 0x2d, 0x2e, 0xff, 0x2e, 0x30, 0x36, 0xff, 0x2d, 0x2d, 0x3e, 0xff, 0x2c, 0x2e, 0x3c, 0xff, 0x2b, 0x2f, 0x35, 0xff, 0x29, 0x2c, 0x32, 0xff, 0x27, 0x2b, 0x30, 0xff, 0x28, 0x2a, 0x2e, 0xff, 0x28, 0x2a, 0x2b, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x27, 0xff, 0x29, 0x2b, 0x29, 0xff, 0x2a, 0x2c, 0x2c, 0xff, 0x2a, 0x27, 0x28, 0xff, 0x30, 0x2d, 0x30, 0xff, 0x25, 0x28, 0x32, 0xff, 0x2f, 0x3d, 0x50, 0xff, 0x30, 0x44, 0x54, 0xff, 0x2d, 0x3b, 0x47, 0xff, 0x2f, 0x35, 0x42, 0xff, 0x34, 0x33, 0x3e, 0xff, 0x2e, 0x2d, 0x34, 0xff, 0x2a, 0x29, 0x2e, 0xff, 0x2d, 0x2d, 0x2e, 0xff, 0x29, 0x29, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x27, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x1c, 0x1c, 0x09, 0x26, 0x24, 0x28, 0xdb, 0x26, 0x23, 0x2a, 0xff, 0x31, 0x26, 0x24, 0xff, 0x37, 0x29, 0x23, 0xff, 0x38, 0x2d, 0x2d, 0xff, 0x28, 0x2c, 0x2a, 0xff, 0x1e, 0x29, 0x22, 0xff, 0x22, 0x2a, 0x22, 0xff, 0x25, 0x2b, 0x23, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2b, 0x25, 0x2e, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x28, 0xff, 0x1c, 0x1d, 0x22, 0xff, 0x21, 0x22, 0x27, 0xff, 0x2b, 0x2c, 0x2a, 0xff, 0x2b, 0x2a, 0x25, 0xff, 0x2a, 0x2a, 0x28, 0xff, 0x28, 0x2a, 0x2c, 0xff, 0x23, 0x23, 0x26, 0xff, 0x2b, 0x29, 0x2b, 0xff, 0x2f, 0x2e, 0x30, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x29, 0x28, 0x28, 0xff, 0x32, 0x33, 0x34, 0xff, 0x3b, 0x3f, 0x45, 0xff, 0x15, 0x1a, 0x21, 0xff, 0x19, 0x1d, 0x20, 0xff, 0x2b, 0x2a, 0x2a, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x27, 0x2a, 0x2c, 0xff, 0x28, 0x2d, 0x2f, 0xff, 0x29, 0x30, 0x38, 0xff, 0x29, 0x34, 0x45, 0xff, 0x2f, 0x3c, 0x58, 0xff, 0x35, 0x47, 0x67, 0xff, 0x3a, 0x50, 0x6e, 0xff, 0x34, 0x4c, 0x6b, 0xff, 0x1e, 0x36, 0x56, 0xff, 0x19, 0x30, 0x4e, 0xff, 0x2c, 0x3d, 0x58, 0xff, 0x30, 0x4a, 0x6f, 0xff, 0x31, 0x53, 0x7d, 0xff, 0x38, 0x55, 0x78, 0xff, 0x31, 0x45, 0x5c, 0xff, 0x23, 0x2a, 0x32, 0xff, 0x29, 0x24, 0x1d, 0xff, 0x2d, 0x29, 0x24, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2a, 0x29, 0x2a, 0xff, 0x26, 0x27, 0x29, 0xff, 0x26, 0x27, 0x28, 0xff, 0x26, 0x27, 0x2d, 0xff, 0x29, 0x2b, 0x34, 0xff, 0x2c, 0x30, 0x37, 0xff, 0x2c, 0x32, 0x3a, 0xff, 0x2c, 0x31, 0x3d, 0xff, 0x28, 0x2f, 0x3f, 0xff, 0x2a, 0x2e, 0x3a, 0xff, 0x2c, 0x2e, 0x35, 0xff, 0x29, 0x2c, 0x31, 0xff, 0x28, 0x2b, 0x30, 0xff, 0x29, 0x2a, 0x2e, 0xff, 0x29, 0x2b, 0x2c, 0xff, 0x28, 0x29, 0x29, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x29, 0x2b, 0xff, 0x28, 0x2b, 0x2d, 0xff, 0x29, 0x28, 0x2b, 0xff, 0x2a, 0x2a, 0x2f, 0xff, 0x24, 0x28, 0x31, 0xff, 0x31, 0x3d, 0x49, 0xff, 0x39, 0x46, 0x51, 0xff, 0x30, 0x3a, 0x43, 0xff, 0x2e, 0x32, 0x3c, 0xff, 0x33, 0x33, 0x3a, 0xff, 0x37, 0x36, 0x39, 0xff, 0x29, 0x29, 0x2b, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xdb, 0x1c, 0x1c, 0x1c, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x23, 0x27, 0x23, 0x41, 0x1b, 0x26, 0x2e, 0xfe, 0x23, 0x29, 0x2c, 0xff, 0x2a, 0x29, 0x27, 0xff, 0x2b, 0x2d, 0x29, 0xff, 0x2f, 0x26, 0x30, 0xff, 0x26, 0x20, 0x2a, 0xff, 0x22, 0x28, 0x27, 0xff, 0x22, 0x2a, 0x26, 0xff, 0x26, 0x29, 0x27, 0xff, 0x2e, 0x26, 0x2a, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x29, 0x25, 0x22, 0xff, 0x29, 0x27, 0x23, 0xff, 0x2b, 0x2b, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x2c, 0xff, 0x35, 0x35, 0x38, 0xff, 0x34, 0x33, 0x34, 0xff, 0x2c, 0x2b, 0x2d, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x28, 0x2b, 0xff, 0x2f, 0x2c, 0x2c, 0xff, 0x2b, 0x29, 0x27, 0xff, 0x1f, 0x20, 0x20, 0xff, 0x23, 0x26, 0x28, 0xff, 0x28, 0x29, 0x29, 0xff, 0x2b, 0x27, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x2b, 0x2c, 0xff, 0x2a, 0x2e, 0x31, 0xff, 0x28, 0x2f, 0x3b, 0xff, 0x27, 0x34, 0x48, 0xff, 0x2e, 0x3c, 0x56, 0xff, 0x2a, 0x45, 0x68, 0xff, 0x2e, 0x4e, 0x77, 0xff, 0x37, 0x52, 0x79, 0xff, 0x37, 0x4d, 0x71, 0xff, 0x34, 0x43, 0x60, 0xff, 0x2a, 0x33, 0x4b, 0xff, 0x22, 0x2f, 0x44, 0xff, 0x20, 0x2f, 0x44, 0xff, 0x21, 0x2e, 0x40, 0xff, 0x20, 0x29, 0x37, 0xff, 0x26, 0x2a, 0x30, 0xff, 0x2d, 0x2b, 0x2b, 0xff, 0x2f, 0x2a, 0x28, 0xff, 0x2e, 0x2b, 0x29, 0xff, 0x2b, 0x2a, 0x2a, 0xff, 0x2a, 0x2b, 0x2c, 0xff, 0x29, 0x2b, 0x2f, 0xff, 0x2c, 0x2f, 0x35, 0xff, 0x30, 0x32, 0x39, 0xff, 0x31, 0x32, 0x3b, 0xff, 0x30, 0x32, 0x3d, 0xff, 0x2d, 0x32, 0x3e, 0xff, 0x2a, 0x31, 0x3f, 0xff, 0x27, 0x2f, 0x41, 0xff, 0x2a, 0x2f, 0x3b, 0xff, 0x2c, 0x2e, 0x35, 0xff, 0x2a, 0x2d, 0x32, 0xff, 0x28, 0x2b, 0x30, 0xff, 0x29, 0x2b, 0x2f, 0xff, 0x2a, 0x2c, 0x2d, 0xff, 0x29, 0x29, 0x2b, 0xff, 0x29, 0x28, 0x29, 0xff, 0x28, 0x29, 0x28, 0xff, 0x26, 0x28, 0x27, 0xff, 0x25, 0x27, 0x28, 0xff, 0x23, 0x25, 0x29, 0xff, 0x29, 0x2c, 0x31, 0xff, 0x2b, 0x2f, 0x33, 0xff, 0x26, 0x27, 0x2d, 0xff, 0x26, 0x28, 0x30, 0xff, 0x26, 0x2a, 0x33, 0xff, 0x24, 0x2a, 0x35, 0xff, 0x2e, 0x37, 0x3e, 0xff, 0x2e, 0x34, 0x39, 0xff, 0x31, 0x34, 0x39, 0xff, 0x31, 0x32, 0x36, 0xff, 0x2d, 0x2c, 0x2e, 0xff, 0x25, 0x28, 0x27, 0xff, 0x24, 0x27, 0x26, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xfe, 0x27, 0x27, 0x27, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x35, 0x2a, 0x8f, 0x3b, 0x31, 0x25, 0xff, 0x3c, 0x2f, 0x22, 0xff, 0x37, 0x2f, 0x28, 0xff, 0x4a, 0x32, 0x2b, 0xff, 0x40, 0x28, 0x23, 0xff, 0x26, 0x21, 0x22, 0xff, 0x24, 0x29, 0x2c, 0xff, 0x24, 0x28, 0x29, 0xff, 0x2a, 0x29, 0x25, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x2b, 0x29, 0x24, 0xff, 0x27, 0x29, 0x26, 0xff, 0x25, 0x29, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x29, 0x26, 0x28, 0xff, 0x34, 0x32, 0x35, 0xff, 0x2d, 0x2c, 0x2e, 0xff, 0x26, 0x25, 0x27, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x27, 0x26, 0x29, 0xff, 0x26, 0x26, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x27, 0x28, 0xff, 0x27, 0x29, 0x27, 0xff, 0x29, 0x2c, 0x2a, 0xff, 0x2b, 0x2e, 0x34, 0xff, 0x2b, 0x32, 0x41, 0xff, 0x29, 0x36, 0x4d, 0xff, 0x2c, 0x3d, 0x57, 0xff, 0x27, 0x43, 0x68, 0xff, 0x29, 0x4a, 0x78, 0xff, 0x31, 0x4c, 0x7a, 0xff, 0x30, 0x47, 0x74, 0xff, 0x31, 0x45, 0x6a, 0xff, 0x31, 0x42, 0x61, 0xff, 0x37, 0x40, 0x53, 0xff, 0x38, 0x3f, 0x4b, 0xff, 0x31, 0x39, 0x4a, 0xff, 0x2c, 0x36, 0x48, 0xff, 0x29, 0x33, 0x45, 0xff, 0x23, 0x2e, 0x42, 0xff, 0x26, 0x2d, 0x3b, 0xff, 0x2a, 0x2d, 0x36, 0xff, 0x27, 0x2d, 0x39, 0xff, 0x28, 0x30, 0x3d, 0xff, 0x2a, 0x33, 0x41, 0xff, 0x29, 0x34, 0x44, 0xff, 0x2d, 0x33, 0x41, 0xff, 0x30, 0x32, 0x3f, 0xff, 0x2f, 0x33, 0x42, 0xff, 0x2e, 0x32, 0x42, 0xff, 0x2a, 0x30, 0x3f, 0xff, 0x27, 0x30, 0x3d, 0xff, 0x2a, 0x30, 0x39, 0xff, 0x2b, 0x2e, 0x35, 0xff, 0x2a, 0x2d, 0x32, 0xff, 0x28, 0x2b, 0x30, 0xff, 0x29, 0x2b, 0x2f, 0xff, 0x29, 0x2c, 0x2c, 0xff, 0x2a, 0x28, 0x2c, 0xff, 0x2a, 0x27, 0x2b, 0xff, 0x28, 0x29, 0x27, 0xff, 0x25, 0x29, 0x26, 0xff, 0x24, 0x28, 0x28, 0xff, 0x22, 0x26, 0x2d, 0xff, 0x31, 0x34, 0x3c, 0xff, 0x2c, 0x30, 0x35, 0xff, 0x25, 0x29, 0x31, 0xff, 0x2d, 0x30, 0x3a, 0xff, 0x2a, 0x2d, 0x35, 0xff, 0x28, 0x2c, 0x32, 0xff, 0x1f, 0x23, 0x27, 0xff, 0x20, 0x22, 0x22, 0xff, 0x23, 0x23, 0x24, 0xff, 0x27, 0x25, 0x27, 0xff, 0x26, 0x25, 0x26, 0xff, 0x24, 0x27, 0x26, 0xff, 0x25, 0x28, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x48, 0x24, 0x07, 0x7e, 0x59, 0x13, 0xd1, 0x81, 0x58, 0x13, 0xff, 0x77, 0x54, 0x1b, 0xff, 0x7f, 0x5b, 0x16, 0xff, 0x76, 0x5a, 0x22, 0xff, 0x36, 0x2b, 0x1b, 0xff, 0x1f, 0x22, 0x28, 0xff, 0x22, 0x27, 0x2e, 0xff, 0x29, 0x29, 0x26, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x27, 0x29, 0x27, 0xff, 0x23, 0x28, 0x2a, 0xff, 0x21, 0x28, 0x2a, 0xff, 0x26, 0x27, 0x2a, 0xff, 0x2e, 0x27, 0x29, 0xff, 0x25, 0x22, 0x24, 0xff, 0x25, 0x25, 0x27, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x28, 0x27, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x27, 0x29, 0xff, 0x26, 0x2a, 0x26, 0xff, 0x29, 0x2c, 0x29, 0xff, 0x2d, 0x2e, 0x36, 0xff, 0x30, 0x34, 0x45, 0xff, 0x2e, 0x3c, 0x53, 0xff, 0x2d, 0x40, 0x5a, 0xff, 0x2e, 0x42, 0x5f, 0xff, 0x34, 0x48, 0x6b, 0xff, 0x32, 0x47, 0x72, 0xff, 0x2e, 0x44, 0x71, 0xff, 0x2f, 0x44, 0x6a, 0xff, 0x2e, 0x43, 0x60, 0xff, 0x2c, 0x3d, 0x55, 0xff, 0x2d, 0x3a, 0x52, 0xff, 0x2c, 0x38, 0x4b, 0xff, 0x2c, 0x35, 0x46, 0xff, 0x2c, 0x34, 0x44, 0xff, 0x2b, 0x33, 0x42, 0xff, 0x2a, 0x32, 0x44, 0xff, 0x29, 0x31, 0x44, 0xff, 0x2b, 0x32, 0x44, 0xff, 0x2c, 0x32, 0x46, 0xff, 0x2b, 0x34, 0x47, 0xff, 0x2a, 0x33, 0x46, 0xff, 0x2b, 0x33, 0x44, 0xff, 0x2a, 0x32, 0x43, 0xff, 0x2a, 0x30, 0x45, 0xff, 0x2b, 0x2f, 0x44, 0xff, 0x2a, 0x2f, 0x3e, 0xff, 0x2b, 0x32, 0x37, 0xff, 0x2b, 0x30, 0x36, 0xff, 0x2a, 0x2c, 0x34, 0xff, 0x28, 0x2b, 0x30, 0xff, 0x27, 0x2a, 0x2f, 0xff, 0x2a, 0x2b, 0x2f, 0xff, 0x29, 0x2c, 0x2b, 0xff, 0x2a, 0x28, 0x2b, 0xff, 0x2b, 0x27, 0x2b, 0xff, 0x29, 0x29, 0x26, 0xff, 0x25, 0x2a, 0x24, 0xff, 0x22, 0x28, 0x29, 0xff, 0x28, 0x2a, 0x38, 0xff, 0x35, 0x3a, 0x47, 0xff, 0x21, 0x29, 0x2f, 0xff, 0x2c, 0x31, 0x39, 0xff, 0x38, 0x3b, 0x44, 0xff, 0x33, 0x35, 0x3d, 0xff, 0x27, 0x2b, 0x2f, 0xff, 0x21, 0x23, 0x24, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x29, 0x29, 0x26, 0xff, 0x23, 0x28, 0x26, 0xff, 0x25, 0x28, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xd1, 0x1f, 0x1f, 0x1f, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa3, 0x8b, 0x36, 0x2a, 0xaa, 0x8b, 0x3b, 0xf3, 0xae, 0x8f, 0x4a, 0xff, 0xb0, 0xa1, 0x47, 0xff, 0xd2, 0xca, 0x79, 0xff, 0x79, 0x70, 0x4a, 0xff, 0x13, 0x0f, 0x08, 0xff, 0x25, 0x26, 0x2e, 0xff, 0x22, 0x25, 0x2f, 0xff, 0x26, 0x27, 0x29, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x26, 0x28, 0x2a, 0xff, 0x26, 0x28, 0x2a, 0xff, 0x27, 0x27, 0x28, 0xff, 0x2b, 0x28, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x26, 0x2a, 0x26, 0xff, 0x28, 0x2b, 0x2b, 0xff, 0x2b, 0x2d, 0x37, 0xff, 0x2f, 0x37, 0x4b, 0xff, 0x31, 0x42, 0x5f, 0xff, 0x2e, 0x48, 0x6b, 0xff, 0x32, 0x4b, 0x71, 0xff, 0x36, 0x4b, 0x73, 0xff, 0x33, 0x48, 0x72, 0xff, 0x31, 0x46, 0x6f, 0xff, 0x33, 0x44, 0x6a, 0xff, 0x2e, 0x41, 0x62, 0xff, 0x26, 0x39, 0x59, 0xff, 0x28, 0x38, 0x57, 0xff, 0x2d, 0x3b, 0x55, 0xff, 0x2c, 0x38, 0x4f, 0xff, 0x2e, 0x37, 0x4d, 0xff, 0x31, 0x39, 0x4b, 0xff, 0x2e, 0x38, 0x4e, 0xff, 0x2a, 0x37, 0x51, 0xff, 0x2d, 0x38, 0x50, 0xff, 0x2e, 0x39, 0x4e, 0xff, 0x2c, 0x36, 0x4b, 0xff, 0x29, 0x32, 0x48, 0xff, 0x29, 0x32, 0x45, 0xff, 0x29, 0x31, 0x42, 0xff, 0x29, 0x30, 0x41, 0xff, 0x2a, 0x2f, 0x3f, 0xff, 0x2d, 0x30, 0x3b, 0xff, 0x2e, 0x30, 0x38, 0xff, 0x2c, 0x2f, 0x36, 0xff, 0x29, 0x2e, 0x33, 0xff, 0x28, 0x2b, 0x30, 0xff, 0x27, 0x2b, 0x2f, 0xff, 0x2b, 0x2c, 0x30, 0xff, 0x2a, 0x2b, 0x30, 0xff, 0x2a, 0x27, 0x2d, 0xff, 0x28, 0x26, 0x2a, 0xff, 0x23, 0x29, 0x27, 0xff, 0x22, 0x2c, 0x26, 0xff, 0x22, 0x2a, 0x27, 0xff, 0x29, 0x2a, 0x30, 0xff, 0x38, 0x31, 0x30, 0xff, 0x2e, 0x27, 0x23, 0xff, 0x1e, 0x24, 0x31, 0xff, 0x2b, 0x35, 0x45, 0xff, 0x36, 0x3c, 0x43, 0xff, 0x2e, 0x31, 0x32, 0xff, 0x2d, 0x2d, 0x2b, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x29, 0x28, 0xff, 0x26, 0x29, 0x28, 0xff, 0x27, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x28, 0x29, 0x29, 0xff, 0x24, 0x29, 0x28, 0xff, 0x25, 0x28, 0x29, 0xff, 0x26, 0x27, 0x28, 0xf4, 0x2a, 0x24, 0x2a, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xed, 0xea, 0xa2, 0x55, 0xf1, 0xea, 0xa7, 0xfe, 0xfe, 0xfb, 0xb2, 0xff, 0xfa, 0xf5, 0xb5, 0xff, 0x84, 0x78, 0x4f, 0xff, 0x3d, 0x31, 0x1f, 0xff, 0x28, 0x22, 0x21, 0xff, 0x24, 0x27, 0x2d, 0xff, 0x26, 0x28, 0x2a, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x27, 0xff, 0x28, 0x2a, 0x2b, 0xff, 0x28, 0x2c, 0x36, 0xff, 0x2d, 0x38, 0x4e, 0xff, 0x33, 0x47, 0x6c, 0xff, 0x30, 0x50, 0x7e, 0xff, 0x33, 0x55, 0x86, 0xff, 0x34, 0x53, 0x82, 0xff, 0x31, 0x4e, 0x7b, 0xff, 0x31, 0x4a, 0x75, 0xff, 0x32, 0x47, 0x70, 0xff, 0x2f, 0x44, 0x6a, 0xff, 0x2e, 0x41, 0x65, 0xff, 0x2e, 0x41, 0x61, 0xff, 0x2c, 0x3f, 0x5f, 0xff, 0x2c, 0x3d, 0x5e, 0xff, 0x2e, 0x3e, 0x5d, 0xff, 0x2f, 0x3e, 0x5c, 0xff, 0x2f, 0x3e, 0x5b, 0xff, 0x2b, 0x3b, 0x57, 0xff, 0x2b, 0x39, 0x53, 0xff, 0x2e, 0x3a, 0x51, 0xff, 0x2d, 0x38, 0x4c, 0xff, 0x2b, 0x34, 0x48, 0xff, 0x2b, 0x32, 0x42, 0xff, 0x2c, 0x32, 0x3e, 0xff, 0x2b, 0x32, 0x3d, 0xff, 0x2b, 0x2f, 0x39, 0xff, 0x2c, 0x2f, 0x38, 0xff, 0x2d, 0x2e, 0x38, 0xff, 0x2c, 0x2e, 0x36, 0xff, 0x2a, 0x2d, 0x34, 0xff, 0x29, 0x2c, 0x31, 0xff, 0x29, 0x2c, 0x31, 0xff, 0x2b, 0x2b, 0x32, 0xff, 0x2a, 0x2a, 0x32, 0xff, 0x2b, 0x28, 0x2c, 0xff, 0x2a, 0x27, 0x27, 0xff, 0x28, 0x27, 0x27, 0xff, 0x26, 0x29, 0x28, 0xff, 0x26, 0x2a, 0x29, 0xff, 0x2a, 0x2c, 0x2e, 0xff, 0x20, 0x1d, 0x1d, 0xff, 0x21, 0x24, 0x2c, 0xff, 0x2c, 0x3e, 0x58, 0xff, 0x26, 0x3a, 0x54, 0xff, 0x35, 0x3f, 0x4b, 0xff, 0x36, 0x3d, 0x44, 0xff, 0x32, 0x35, 0x38, 0xff, 0x2a, 0x27, 0x25, 0xff, 0x28, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x28, 0x29, 0x29, 0xff, 0x24, 0x29, 0x28, 0xff, 0x25, 0x28, 0x28, 0xff, 0x26, 0x26, 0x29, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x97, 0x99, 0x5a, 0x8d, 0xa5, 0x9c, 0x67, 0xff, 0x84, 0x75, 0x4d, 0xff, 0x4f, 0x3f, 0x24, 0xff, 0x5f, 0x52, 0x44, 0xff, 0x2f, 0x28, 0x20, 0xff, 0x2b, 0x29, 0x23, 0xff, 0x29, 0x29, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x27, 0xff, 0x29, 0x2b, 0x29, 0xff, 0x27, 0x2b, 0x33, 0xff, 0x2a, 0x34, 0x4a, 0xff, 0x33, 0x4a, 0x71, 0xff, 0x36, 0x58, 0x8c, 0xff, 0x35, 0x5b, 0x8e, 0xff, 0x35, 0x5c, 0x8b, 0xff, 0x33, 0x57, 0x85, 0xff, 0x32, 0x51, 0x7e, 0xff, 0x30, 0x4d, 0x77, 0xff, 0x2f, 0x4b, 0x74, 0xff, 0x31, 0x4a, 0x71, 0xff, 0x33, 0x4a, 0x72, 0xff, 0x31, 0x49, 0x6f, 0xff, 0x30, 0x46, 0x6b, 0xff, 0x32, 0x46, 0x69, 0xff, 0x2e, 0x42, 0x65, 0xff, 0x2c, 0x3f, 0x5e, 0xff, 0x2c, 0x3b, 0x57, 0xff, 0x29, 0x38, 0x51, 0xff, 0x2e, 0x39, 0x4d, 0xff, 0x30, 0x37, 0x46, 0xff, 0x2f, 0x33, 0x40, 0xff, 0x2f, 0x32, 0x3d, 0xff, 0x2e, 0x30, 0x3a, 0xff, 0x2c, 0x2f, 0x39, 0xff, 0x2a, 0x2e, 0x37, 0xff, 0x2b, 0x2e, 0x38, 0xff, 0x2b, 0x2d, 0x38, 0xff, 0x29, 0x2c, 0x36, 0xff, 0x29, 0x2c, 0x34, 0xff, 0x2a, 0x2c, 0x33, 0xff, 0x2a, 0x2d, 0x31, 0xff, 0x2b, 0x2c, 0x32, 0xff, 0x29, 0x2a, 0x2f, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x2d, 0x27, 0x25, 0xff, 0x30, 0x26, 0x27, 0xff, 0x2f, 0x25, 0x2a, 0xff, 0x25, 0x23, 0x2e, 0xff, 0x11, 0x1a, 0x2a, 0xff, 0x33, 0x4a, 0x5e, 0xff, 0x68, 0x88, 0xa2, 0xff, 0x4c, 0x67, 0x7f, 0xff, 0x30, 0x40, 0x4c, 0xff, 0x29, 0x2b, 0x2d, 0xff, 0x24, 0x23, 0x27, 0xff, 0x22, 0x24, 0x28, 0xff, 0x28, 0x29, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2d, 0x28, 0x28, 0xff, 0x2b, 0x27, 0x27, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x1f, 0x1b, 0x0e, 0xb1, 0x22, 0x1c, 0x0e, 0xff, 0x2b, 0x23, 0x1c, 0xff, 0x2d, 0x26, 0x23, 0xff, 0x2d, 0x2a, 0x28, 0xff, 0x26, 0x29, 0x25, 0xff, 0x27, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x2b, 0x29, 0xff, 0x2a, 0x2c, 0x31, 0xff, 0x28, 0x31, 0x45, 0xff, 0x2e, 0x45, 0x6c, 0xff, 0x37, 0x5c, 0x94, 0xff, 0x3a, 0x64, 0x9b, 0xff, 0x39, 0x62, 0x94, 0xff, 0x33, 0x5c, 0x8c, 0xff, 0x34, 0x58, 0x86, 0xff, 0x32, 0x53, 0x80, 0xff, 0x32, 0x51, 0x7c, 0xff, 0x35, 0x50, 0x7d, 0xff, 0x34, 0x4f, 0x7c, 0xff, 0x34, 0x4f, 0x7b, 0xff, 0x33, 0x4c, 0x75, 0xff, 0x30, 0x49, 0x70, 0xff, 0x2a, 0x42, 0x6a, 0xff, 0x2a, 0x3f, 0x60, 0xff, 0x2c, 0x3d, 0x5a, 0xff, 0x2a, 0x38, 0x53, 0xff, 0x2c, 0x37, 0x4b, 0xff, 0x2e, 0x34, 0x43, 0xff, 0x2f, 0x32, 0x3d, 0xff, 0x2f, 0x32, 0x3b, 0xff, 0x2d, 0x2f, 0x39, 0xff, 0x2c, 0x2e, 0x38, 0xff, 0x2c, 0x2f, 0x39, 0xff, 0x2a, 0x2f, 0x3a, 0xff, 0x2a, 0x2e, 0x3a, 0xff, 0x29, 0x2c, 0x37, 0xff, 0x29, 0x2b, 0x34, 0xff, 0x2b, 0x2d, 0x35, 0xff, 0x2b, 0x2e, 0x34, 0xff, 0x2d, 0x2e, 0x32, 0xff, 0x2a, 0x2a, 0x2d, 0xff, 0x24, 0x29, 0x28, 0xff, 0x24, 0x2a, 0x25, 0xff, 0x2b, 0x26, 0x26, 0xff, 0x2a, 0x22, 0x29, 0xff, 0x2e, 0x35, 0x42, 0xff, 0x41, 0x58, 0x6b, 0xff, 0x6a, 0x8f, 0xaa, 0xff, 0x4b, 0x77, 0x99, 0xff, 0x35, 0x57, 0x71, 0xff, 0x26, 0x35, 0x41, 0xff, 0x16, 0x18, 0x17, 0xff, 0x1f, 0x1f, 0x23, 0xff, 0x20, 0x23, 0x2a, 0xff, 0x24, 0x27, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2c, 0x25, 0x28, 0xb1, 0x55, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x19, 0x33, 0x0a, 0x22, 0x27, 0x2b, 0xc8, 0x27, 0x28, 0x2c, 0xff, 0x28, 0x27, 0x2c, 0xff, 0x26, 0x28, 0x2b, 0xff, 0x22, 0x28, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x26, 0x29, 0x28, 0xff, 0x28, 0x2b, 0x2a, 0xff, 0x2c, 0x2c, 0x2f, 0xff, 0x2b, 0x30, 0x40, 0xff, 0x2b, 0x3f, 0x61, 0xff, 0x34, 0x57, 0x8a, 0xff, 0x3a, 0x63, 0x9a, 0xff, 0x38, 0x61, 0x95, 0xff, 0x36, 0x5d, 0x8f, 0xff, 0x37, 0x59, 0x89, 0xff, 0x31, 0x53, 0x80, 0xff, 0x32, 0x51, 0x7d, 0xff, 0x36, 0x51, 0x7e, 0xff, 0x34, 0x4e, 0x7b, 0xff, 0x33, 0x4d, 0x78, 0xff, 0x32, 0x4b, 0x75, 0xff, 0x30, 0x48, 0x6f, 0xff, 0x2c, 0x44, 0x6b, 0xff, 0x2d, 0x42, 0x64, 0xff, 0x2b, 0x3d, 0x59, 0xff, 0x2a, 0x3a, 0x54, 0xff, 0x2a, 0x36, 0x4c, 0xff, 0x2d, 0x35, 0x48, 0xff, 0x2b, 0x31, 0x40, 0xff, 0x28, 0x2f, 0x3f, 0xff, 0x28, 0x30, 0x40, 0xff, 0x2b, 0x33, 0x41, 0xff, 0x2c, 0x32, 0x40, 0xff, 0x2a, 0x2f, 0x3a, 0xff, 0x29, 0x2e, 0x38, 0xff, 0x2b, 0x2e, 0x38, 0xff, 0x2b, 0x2c, 0x37, 0xff, 0x2b, 0x2c, 0x36, 0xff, 0x2b, 0x2e, 0x35, 0xff, 0x2c, 0x2e, 0x32, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x23, 0x27, 0x27, 0xff, 0x22, 0x2a, 0x28, 0xff, 0x25, 0x28, 0x27, 0xff, 0x21, 0x25, 0x27, 0xff, 0x35, 0x41, 0x48, 0xff, 0x3b, 0x52, 0x5f, 0xff, 0x19, 0x30, 0x3c, 0xff, 0x1f, 0x36, 0x43, 0xff, 0x2f, 0x47, 0x57, 0xff, 0x28, 0x34, 0x3c, 0xff, 0x3e, 0x3d, 0x3c, 0xff, 0x3d, 0x3a, 0x39, 0xff, 0x2c, 0x2e, 0x32, 0xff, 0x27, 0x2c, 0x31, 0xff, 0x26, 0x29, 0x2c, 0xff, 0x26, 0x29, 0x29, 0xff, 0x28, 0x29, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x29, 0x29, 0x29, 0xff, 0x25, 0x25, 0x25, 0xff, 0x26, 0x26, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x27, 0x28, 0xc8, 0x19, 0x19, 0x33, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x24, 0x30, 0x15, 0x29, 0x29, 0x29, 0xd7, 0x2c, 0x28, 0x2a, 0xff, 0x2c, 0x28, 0x29, 0xff, 0x2a, 0x27, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x25, 0x29, 0x2b, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x2e, 0x2b, 0x2c, 0xff, 0x2d, 0x2e, 0x3a, 0xff, 0x2a, 0x3a, 0x57, 0xff, 0x35, 0x54, 0x7e, 0xff, 0x3a, 0x61, 0x94, 0xff, 0x3c, 0x60, 0x94, 0xff, 0x37, 0x59, 0x89, 0xff, 0x31, 0x52, 0x7f, 0xff, 0x31, 0x4d, 0x79, 0xff, 0x31, 0x4c, 0x76, 0xff, 0x2e, 0x48, 0x71, 0xff, 0x2d, 0x47, 0x70, 0xff, 0x30, 0x48, 0x70, 0xff, 0x2e, 0x44, 0x6b, 0xff, 0x2d, 0x44, 0x67, 0xff, 0x2c, 0x42, 0x66, 0xff, 0x2d, 0x40, 0x61, 0xff, 0x2d, 0x3f, 0x5b, 0xff, 0x2c, 0x3d, 0x58, 0xff, 0x2a, 0x38, 0x51, 0xff, 0x2a, 0x37, 0x4d, 0xff, 0x2b, 0x34, 0x47, 0xff, 0x2b, 0x37, 0x4d, 0xff, 0x29, 0x38, 0x50, 0xff, 0x2a, 0x37, 0x4b, 0xff, 0x2c, 0x35, 0x45, 0xff, 0x2e, 0x33, 0x3f, 0xff, 0x2c, 0x2e, 0x38, 0xff, 0x2c, 0x2e, 0x38, 0xff, 0x2c, 0x2e, 0x3a, 0xff, 0x2b, 0x2c, 0x37, 0xff, 0x2a, 0x2d, 0x34, 0xff, 0x29, 0x2c, 0x2d, 0xff, 0x25, 0x28, 0x28, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x2d, 0x26, 0x29, 0xff, 0x28, 0x29, 0x28, 0xff, 0x25, 0x29, 0x26, 0xff, 0x22, 0x25, 0x23, 0xff, 0x1f, 0x1b, 0x1d, 0xff, 0x23, 0x21, 0x22, 0xff, 0x2a, 0x3b, 0x43, 0xff, 0x3a, 0x58, 0x76, 0xff, 0x55, 0x72, 0x95, 0xff, 0x61, 0x76, 0x8f, 0xff, 0x5d, 0x6f, 0x87, 0xff, 0x41, 0x4c, 0x5c, 0xff, 0x2e, 0x31, 0x36, 0xff, 0x27, 0x2c, 0x31, 0xff, 0x26, 0x28, 0x2b, 0xff, 0x28, 0x29, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x29, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x27, 0x28, 0x2a, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x26, 0x22, 0x21, 0xff, 0x24, 0x1c, 0x18, 0xff, 0x2b, 0x25, 0x1f, 0xff, 0x29, 0x26, 0x25, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x2a, 0x29, 0x26, 0xd8, 0x24, 0x24, 0x24, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2d, 0x2d, 0x24, 0x1c, 0x2d, 0x28, 0x28, 0xdc, 0x2d, 0x29, 0x28, 0xff, 0x2d, 0x28, 0x28, 0xff, 0x2b, 0x29, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x27, 0x27, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x29, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x2b, 0x2a, 0x2f, 0xff, 0x2a, 0x2d, 0x35, 0xff, 0x2b, 0x39, 0x4f, 0xff, 0x32, 0x4f, 0x7c, 0xff, 0x3d, 0x62, 0x96, 0xff, 0x3c, 0x60, 0x93, 0xff, 0x34, 0x57, 0x89, 0xff, 0x2d, 0x4d, 0x7d, 0xff, 0x30, 0x4b, 0x78, 0xff, 0x32, 0x48, 0x73, 0xff, 0x2f, 0x45, 0x6d, 0xff, 0x2d, 0x43, 0x69, 0xff, 0x2f, 0x43, 0x68, 0xff, 0x2d, 0x41, 0x64, 0xff, 0x2b, 0x3e, 0x60, 0xff, 0x2b, 0x3d, 0x5f, 0xff, 0x2b, 0x3d, 0x59, 0xff, 0x30, 0x40, 0x59, 0xff, 0x2f, 0x3d, 0x59, 0xff, 0x2e, 0x3b, 0x58, 0xff, 0x2a, 0x39, 0x56, 0xff, 0x2a, 0x3b, 0x59, 0xff, 0x2d, 0x3f, 0x5f, 0xff, 0x2e, 0x3f, 0x60, 0xff, 0x2d, 0x3b, 0x58, 0xff, 0x2b, 0x36, 0x4c, 0xff, 0x2b, 0x33, 0x43, 0xff, 0x2a, 0x31, 0x3d, 0xff, 0x2c, 0x2f, 0x3b, 0xff, 0x2d, 0x2f, 0x39, 0xff, 0x2c, 0x2d, 0x34, 0xff, 0x2a, 0x2a, 0x2d, 0xff, 0x27, 0x28, 0x29, 0xff, 0x26, 0x29, 0x28, 0xff, 0x2b, 0x28, 0x2a, 0xff, 0x2e, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x29, 0x28, 0x25, 0xff, 0x2e, 0x27, 0x26, 0xff, 0x2e, 0x26, 0x26, 0xff, 0x23, 0x26, 0x28, 0xff, 0x29, 0x35, 0x43, 0xff, 0x37, 0x46, 0x58, 0xff, 0x2e, 0x3a, 0x47, 0xff, 0x2c, 0x34, 0x41, 0xff, 0x31, 0x35, 0x3d, 0xff, 0x2a, 0x2c, 0x2c, 0xff, 0x25, 0x27, 0x2b, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x28, 0x28, 0x29, 0xff, 0x29, 0x27, 0x28, 0xff, 0x2a, 0x27, 0x28, 0xff, 0x27, 0x26, 0x2a, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x2c, 0x28, 0x20, 0xff, 0x17, 0x16, 0x09, 0xff, 0x07, 0x16, 0x14, 0xff, 0x4d, 0x68, 0x78, 0xff, 0x2b, 0x42, 0x54, 0xff, 0x0b, 0x12, 0x16, 0xff, 0x2b, 0x26, 0x20, 0xff, 0x30, 0x29, 0x21, 0xdd, 0x24, 0x24, 0x24, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x26, 0x26, 0x21, 0x2a, 0x29, 0x29, 0xde, 0x2b, 0x29, 0x29, 0xff, 0x2c, 0x2a, 0x2a, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x27, 0x27, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x27, 0x2b, 0x31, 0xff, 0x2a, 0x2c, 0x33, 0xff, 0x28, 0x33, 0x48, 0xff, 0x31, 0x4f, 0x7d, 0xff, 0x45, 0x6c, 0xa4, 0xff, 0x42, 0x6a, 0x9e, 0xff, 0x38, 0x5f, 0x94, 0xff, 0x33, 0x56, 0x8a, 0xff, 0x30, 0x4d, 0x7e, 0xff, 0x2f, 0x48, 0x76, 0xff, 0x33, 0x48, 0x72, 0xff, 0x31, 0x45, 0x6c, 0xff, 0x2d, 0x41, 0x67, 0xff, 0x2e, 0x41, 0x65, 0xff, 0x30, 0x42, 0x65, 0xff, 0x2e, 0x40, 0x62, 0xff, 0x2c, 0x3f, 0x5b, 0xff, 0x2e, 0x40, 0x58, 0xff, 0x2d, 0x3b, 0x59, 0xff, 0x2b, 0x3a, 0x5b, 0xff, 0x2f, 0x40, 0x64, 0xff, 0x2e, 0x45, 0x6b, 0xff, 0x2e, 0x43, 0x6d, 0xff, 0x30, 0x42, 0x6b, 0xff, 0x2e, 0x3f, 0x62, 0xff, 0x2a, 0x38, 0x53, 0xff, 0x28, 0x34, 0x47, 0xff, 0x2a, 0x34, 0x44, 0xff, 0x2b, 0x32, 0x3f, 0xff, 0x2d, 0x2f, 0x39, 0xff, 0x2b, 0x2c, 0x30, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x29, 0x29, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x29, 0x28, 0xff, 0x28, 0x2a, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x28, 0xff, 0x26, 0x27, 0x2a, 0xff, 0x24, 0x24, 0x2a, 0xff, 0x27, 0x28, 0x29, 0xff, 0x2b, 0x2d, 0x2c, 0xff, 0x29, 0x2a, 0x2b, 0xff, 0x28, 0x26, 0x29, 0xff, 0x28, 0x26, 0x26, 0xff, 0x27, 0x27, 0x25, 0xff, 0x29, 0x28, 0x28, 0xff, 0x27, 0x27, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2c, 0x28, 0x26, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x16, 0x1a, 0x22, 0xff, 0x13, 0x0f, 0x0d, 0xff, 0x39, 0x37, 0x32, 0xff, 0x76, 0x8c, 0xa0, 0xff, 0x8e, 0xbb, 0xe6, 0xff, 0x70, 0x9a, 0xbf, 0xff, 0x38, 0x4c, 0x5e, 0xff, 0x1c, 0x1d, 0x21, 0xde, 0x2e, 0x26, 0x26, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x27, 0x27, 0x27, 0x1a, 0x2a, 0x29, 0x29, 0xd7, 0x2a, 0x28, 0x28, 0xff, 0x2b, 0x29, 0x29, 0xff, 0x2e, 0x2c, 0x2c, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x29, 0x29, 0xff, 0x29, 0x29, 0x2b, 0xff, 0x27, 0x2a, 0x2e, 0xff, 0x2a, 0x2b, 0x2f, 0xff, 0x25, 0x2f, 0x40, 0xff, 0x34, 0x4d, 0x75, 0xff, 0x53, 0x7d, 0xb2, 0xff, 0x4e, 0x7c, 0xb3, 0xff, 0x3e, 0x69, 0xa0, 0xff, 0x3a, 0x62, 0x98, 0xff, 0x35, 0x57, 0x8a, 0xff, 0x2f, 0x4e, 0x7d, 0xff, 0x31, 0x4b, 0x77, 0xff, 0x32, 0x48, 0x73, 0xff, 0x2f, 0x46, 0x6f, 0xff, 0x30, 0x46, 0x6e, 0xff, 0x32, 0x47, 0x6c, 0xff, 0x30, 0x45, 0x69, 0xff, 0x2d, 0x42, 0x63, 0xff, 0x2b, 0x3f, 0x5f, 0xff, 0x2b, 0x3f, 0x60, 0xff, 0x2e, 0x42, 0x68, 0xff, 0x33, 0x49, 0x73, 0xff, 0x32, 0x4e, 0x79, 0xff, 0x33, 0x4e, 0x7d, 0xff, 0x31, 0x49, 0x77, 0xff, 0x2e, 0x44, 0x6b, 0xff, 0x2b, 0x3c, 0x5b, 0xff, 0x2a, 0x37, 0x4e, 0xff, 0x2a, 0x35, 0x48, 0xff, 0x2d, 0x34, 0x42, 0xff, 0x2e, 0x30, 0x39, 0xff, 0x29, 0x2a, 0x2f, 0xff, 0x29, 0x28, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x27, 0x29, 0xff, 0x24, 0x26, 0x27, 0xff, 0x20, 0x24, 0x24, 0xff, 0x22, 0x24, 0x25, 0xff, 0x27, 0x26, 0x27, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2a, 0x29, 0x2b, 0xff, 0x28, 0x29, 0x28, 0xff, 0x27, 0x28, 0x27, 0xff, 0x29, 0x2a, 0x28, 0xff, 0x25, 0x27, 0x26, 0xff, 0x24, 0x24, 0x1c, 0xff, 0x1b, 0x1b, 0x15, 0xff, 0x32, 0x39, 0x47, 0xff, 0x92, 0x98, 0xaf, 0xff, 0xb9, 0xbc, 0xd9, 0xff, 0x9d, 0xa2, 0xcb, 0xff, 0x88, 0x97, 0xbe, 0xff, 0x97, 0xad, 0xc6, 0xff, 0xaa, 0xbd, 0xdb, 0xd7, 0x84, 0x97, 0xb3, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2e, 0x2e, 0x2e, 0x16, 0x2c, 0x29, 0x29, 0xca, 0x2d, 0x2b, 0x2b, 0xff, 0x2e, 0x2c, 0x2c, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x29, 0x2b, 0xff, 0x28, 0x29, 0x2c, 0xff, 0x2c, 0x2b, 0x2c, 0xff, 0x26, 0x2c, 0x39, 0xff, 0x2d, 0x41, 0x62, 0xff, 0x4d, 0x78, 0xad, 0xff, 0x51, 0x82, 0xbd, 0xff, 0x3f, 0x6c, 0xa5, 0xff, 0x38, 0x64, 0x9b, 0xff, 0x37, 0x5c, 0x91, 0xff, 0x35, 0x55, 0x85, 0xff, 0x32, 0x4f, 0x7b, 0xff, 0x32, 0x4b, 0x78, 0xff, 0x2f, 0x48, 0x74, 0xff, 0x2d, 0x44, 0x6e, 0xff, 0x2f, 0x45, 0x6d, 0xff, 0x2f, 0x44, 0x6a, 0xff, 0x2c, 0x42, 0x67, 0xff, 0x2b, 0x42, 0x66, 0xff, 0x2e, 0x44, 0x6b, 0xff, 0x34, 0x4c, 0x79, 0xff, 0x32, 0x4f, 0x7f, 0xff, 0x32, 0x56, 0x89, 0xff, 0x36, 0x59, 0x8e, 0xff, 0x31, 0x50, 0x85, 0xff, 0x2d, 0x47, 0x76, 0xff, 0x2f, 0x44, 0x68, 0xff, 0x2c, 0x3b, 0x55, 0xff, 0x29, 0x34, 0x48, 0xff, 0x2d, 0x33, 0x42, 0xff, 0x2d, 0x2f, 0x38, 0xff, 0x28, 0x29, 0x2e, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x2a, 0x28, 0x26, 0xff, 0x29, 0x27, 0x26, 0xff, 0x2a, 0x29, 0x28, 0xff, 0x2b, 0x29, 0x28, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x2b, 0xff, 0x29, 0x27, 0x28, 0xff, 0x29, 0x2a, 0x27, 0xff, 0x25, 0x27, 0x25, 0xff, 0x1e, 0x23, 0x22, 0xff, 0x30, 0x32, 0x31, 0xff, 0x1a, 0x19, 0x1e, 0xff, 0x50, 0x58, 0x6a, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xb1, 0xbd, 0xdb, 0xff, 0x1c, 0x24, 0x37, 0xff, 0x28, 0x29, 0x33, 0xff, 0x2d, 0x2e, 0x3a, 0xca, 0x45, 0x51, 0x5c, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2e, 0x2e, 0x2e, 0x0b, 0x2b, 0x28, 0x28, 0xaf, 0x29, 0x27, 0x27, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x29, 0xff, 0x27, 0x29, 0x2a, 0xff, 0x29, 0x29, 0x2a, 0xff, 0x2d, 0x2a, 0x29, 0xff, 0x26, 0x29, 0x33, 0xff, 0x28, 0x38, 0x54, 0xff, 0x44, 0x6a, 0x9d, 0xff, 0x52, 0x7f, 0xbb, 0xff, 0x41, 0x69, 0xa3, 0xff, 0x35, 0x5b, 0x93, 0xff, 0x36, 0x57, 0x8c, 0xff, 0x35, 0x53, 0x83, 0xff, 0x35, 0x4f, 0x7c, 0xff, 0x33, 0x4b, 0x76, 0xff, 0x2f, 0x48, 0x73, 0xff, 0x2f, 0x46, 0x70, 0xff, 0x30, 0x46, 0x6d, 0xff, 0x2e, 0x43, 0x68, 0xff, 0x2b, 0x41, 0x66, 0xff, 0x2c, 0x43, 0x6a, 0xff, 0x30, 0x46, 0x72, 0xff, 0x34, 0x4d, 0x7e, 0xff, 0x32, 0x55, 0x8a, 0xff, 0x37, 0x60, 0x97, 0xff, 0x36, 0x5f, 0x98, 0xff, 0x31, 0x55, 0x8e, 0xff, 0x30, 0x4f, 0x7f, 0xff, 0x31, 0x48, 0x6d, 0xff, 0x2b, 0x39, 0x53, 0xff, 0x2c, 0x34, 0x48, 0xff, 0x2e, 0x32, 0x40, 0xff, 0x2b, 0x2e, 0x37, 0xff, 0x29, 0x2a, 0x2f, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x29, 0x29, 0xff, 0x28, 0x29, 0x29, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x24, 0x29, 0x28, 0xff, 0x25, 0x28, 0x29, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2b, 0x28, 0x27, 0xff, 0x1f, 0x1f, 0x20, 0xff, 0x48, 0x4c, 0x52, 0xff, 0x65, 0x6b, 0x7a, 0xff, 0x3b, 0x3c, 0x50, 0xff, 0x17, 0x1a, 0x2d, 0xff, 0xbf, 0xcc, 0xe4, 0xff, 0xb9, 0xca, 0xdd, 0xff, 0x11, 0x1c, 0x15, 0xff, 0x28, 0x25, 0x18, 0xb0, 0x2e, 0x17, 0x17, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x2a, 0x28, 0x28, 0x8b, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x2b, 0x29, 0x2b, 0xff, 0x31, 0x2b, 0x29, 0xff, 0x28, 0x29, 0x2f, 0xff, 0x20, 0x30, 0x49, 0xff, 0x3a, 0x5d, 0x90, 0xff, 0x4a, 0x72, 0xad, 0xff, 0x3b, 0x5f, 0x97, 0xff, 0x36, 0x56, 0x8e, 0xff, 0x36, 0x52, 0x86, 0xff, 0x36, 0x4d, 0x7f, 0xff, 0x33, 0x4a, 0x78, 0xff, 0x30, 0x4a, 0x74, 0xff, 0x2f, 0x48, 0x71, 0xff, 0x31, 0x47, 0x6e, 0xff, 0x2f, 0x44, 0x69, 0xff, 0x2f, 0x41, 0x65, 0xff, 0x2d, 0x41, 0x67, 0xff, 0x2f, 0x45, 0x6e, 0xff, 0x32, 0x4a, 0x72, 0xff, 0x32, 0x4f, 0x7e, 0xff, 0x37, 0x5c, 0x93, 0xff, 0x3a, 0x66, 0x9d, 0xff, 0x3a, 0x65, 0xa1, 0xff, 0x3a, 0x60, 0x9b, 0xff, 0x36, 0x56, 0x86, 0xff, 0x2f, 0x46, 0x6c, 0xff, 0x29, 0x36, 0x50, 0xff, 0x2c, 0x32, 0x45, 0xff, 0x2d, 0x30, 0x3f, 0xff, 0x29, 0x2c, 0x37, 0xff, 0x29, 0x29, 0x2e, 0xff, 0x2a, 0x28, 0x29, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x29, 0x29, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x25, 0x29, 0x28, 0xff, 0x23, 0x28, 0x29, 0xff, 0x28, 0x28, 0x29, 0xff, 0x2c, 0x28, 0x29, 0xff, 0x29, 0x28, 0x29, 0xff, 0x24, 0x28, 0x29, 0xff, 0x22, 0x29, 0x29, 0xff, 0x24, 0x29, 0x28, 0xff, 0x2c, 0x28, 0x27, 0xff, 0x27, 0x20, 0x20, 0xff, 0x2a, 0x26, 0x2e, 0xff, 0x7a, 0x7a, 0x8e, 0xff, 0x7f, 0x89, 0xa6, 0xff, 0x42, 0x4c, 0x61, 0xff, 0x1c, 0x19, 0x20, 0xff, 0xa5, 0xa7, 0xb7, 0xff, 0xc1, 0xcd, 0xe4, 0xff, 0x1d, 0x1d, 0x22, 0x8b, 0x55, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x29, 0x29, 0x57, 0x28, 0x27, 0x27, 0xf3, 0x2a, 0x28, 0x28, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x2b, 0x2b, 0x2f, 0xff, 0x1f, 0x20, 0x22, 0xff, 0x07, 0x08, 0x07, 0xff, 0x17, 0x18, 0x14, 0xff, 0x25, 0x26, 0x21, 0xff, 0x2e, 0x2b, 0x28, 0xff, 0x2c, 0x29, 0x27, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x29, 0xff, 0x26, 0x26, 0x2c, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2b, 0x27, 0x28, 0xff, 0x2a, 0x27, 0x2a, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x28, 0x27, 0x29, 0xff, 0x27, 0x29, 0x26, 0xff, 0x29, 0x29, 0x28, 0xff, 0x2a, 0x29, 0x29, 0xff, 0x2a, 0x2a, 0x29, 0xff, 0x2b, 0x2b, 0x29, 0xff, 0x28, 0x29, 0x30, 0xff, 0x27, 0x29, 0x3b, 0xff, 0x39, 0x54, 0x7f, 0xff, 0x42, 0x6c, 0xa2, 0xff, 0x32, 0x5a, 0x8e, 0xff, 0x2f, 0x53, 0x87, 0xff, 0x2f, 0x4d, 0x7a, 0xff, 0x30, 0x48, 0x6f, 0xff, 0x30, 0x45, 0x6a, 0xff, 0x2e, 0x45, 0x68, 0xff, 0x30, 0x44, 0x68, 0xff, 0x2f, 0x42, 0x67, 0xff, 0x2f, 0x41, 0x66, 0xff, 0x2d, 0x40, 0x64, 0xff, 0x2f, 0x43, 0x66, 0xff, 0x2e, 0x44, 0x6e, 0xff, 0x2d, 0x46, 0x7a, 0xff, 0x37, 0x51, 0x89, 0xff, 0x41, 0x60, 0x98, 0xff, 0x40, 0x6c, 0xa6, 0xff, 0x42, 0x6f, 0xab, 0xff, 0x42, 0x68, 0x9f, 0xff, 0x34, 0x55, 0x85, 0xff, 0x2a, 0x3f, 0x65, 0xff, 0x29, 0x33, 0x4c, 0xff, 0x2f, 0x31, 0x40, 0xff, 0x2c, 0x2e, 0x38, 0xff, 0x29, 0x2c, 0x33, 0xff, 0x27, 0x2a, 0x2f, 0xff, 0x28, 0x29, 0x2a, 0xff, 0x29, 0x2a, 0x29, 0xff, 0x28, 0x2a, 0x2a, 0xff, 0x27, 0x29, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x25, 0x28, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x29, 0x27, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x26, 0x28, 0x29, 0xff, 0x26, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2d, 0x2d, 0x25, 0xff, 0x1b, 0x1a, 0x19, 0xff, 0x43, 0x43, 0x52, 0xff, 0x93, 0x98, 0xb3, 0xff, 0x97, 0xa2, 0xba, 0xff, 0x4c, 0x55, 0x66, 0xff, 0x44, 0x48, 0x59, 0xff, 0xbd, 0xc2, 0xda, 0xf3, 0xc7, 0xca, 0xe1, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x29, 0x29, 0x2b, 0x29, 0x28, 0x28, 0xd2, 0x25, 0x26, 0x27, 0xff, 0x13, 0x15, 0x18, 0xff, 0x7c, 0x7d, 0x82, 0xff, 0xa5, 0xa5, 0xab, 0xff, 0x41, 0x40, 0x49, 0xff, 0x10, 0x0d, 0x1a, 0xff, 0x0c, 0x09, 0x0c, 0xff, 0x20, 0x1f, 0x1a, 0xff, 0x2b, 0x2b, 0x28, 0xff, 0x29, 0x2a, 0x27, 0xff, 0x28, 0x29, 0x27, 0xff, 0x26, 0x28, 0x29, 0xff, 0x2b, 0x28, 0x28, 0xff, 0x2e, 0x27, 0x29, 0xff, 0x2b, 0x25, 0x2b, 0xff, 0x2a, 0x25, 0x2c, 0xff, 0x28, 0x27, 0x29, 0xff, 0x27, 0x2a, 0x25, 0xff, 0x2a, 0x28, 0x29, 0xff, 0x2b, 0x28, 0x2b, 0xff, 0x28, 0x2a, 0x27, 0xff, 0x23, 0x2a, 0x27, 0xff, 0x25, 0x2a, 0x2d, 0xff, 0x2e, 0x28, 0x2f, 0xff, 0x32, 0x41, 0x5f, 0xff, 0x3b, 0x5d, 0x8d, 0xff, 0x31, 0x53, 0x85, 0xff, 0x29, 0x4d, 0x7e, 0xff, 0x2b, 0x47, 0x74, 0xff, 0x2d, 0x44, 0x69, 0xff, 0x2c, 0x40, 0x5f, 0xff, 0x2d, 0x40, 0x5f, 0xff, 0x2f, 0x3f, 0x60, 0xff, 0x2e, 0x3d, 0x60, 0xff, 0x2e, 0x41, 0x63, 0xff, 0x2c, 0x42, 0x63, 0xff, 0x30, 0x45, 0x63, 0xff, 0x2f, 0x48, 0x6d, 0xff, 0x2c, 0x48, 0x7d, 0xff, 0x35, 0x51, 0x89, 0xff, 0x44, 0x64, 0x9a, 0xff, 0x48, 0x76, 0xb3, 0xff, 0x47, 0x78, 0xb8, 0xff, 0x43, 0x6b, 0xa5, 0xff, 0x31, 0x4f, 0x7d, 0xff, 0x25, 0x36, 0x54, 0xff, 0x2a, 0x2f, 0x42, 0xff, 0x2e, 0x2e, 0x3b, 0xff, 0x2d, 0x2e, 0x35, 0xff, 0x2a, 0x2e, 0x31, 0xff, 0x27, 0x2a, 0x30, 0xff, 0x26, 0x29, 0x2c, 0xff, 0x26, 0x29, 0x29, 0xff, 0x27, 0x29, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x28, 0x27, 0xff, 0x2b, 0x27, 0x2b, 0xff, 0x2d, 0x27, 0x29, 0xff, 0x25, 0x28, 0x1e, 0xff, 0x1e, 0x23, 0x22, 0xff, 0x6c, 0x70, 0x84, 0xff, 0x9f, 0xa8, 0xc4, 0xff, 0xa4, 0xac, 0xc0, 0xff, 0x65, 0x6a, 0x7d, 0xff, 0x73, 0x7e, 0x9b, 0xd2, 0xdc, 0xe7, 0xff, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x1f, 0x1f, 0x08, 0x29, 0x2a, 0x23, 0x8f, 0x19, 0x1b, 0x16, 0xfe, 0x3b, 0x3a, 0x40, 0xff, 0xac, 0xaa, 0xb9, 0xff, 0xd6, 0xd4, 0xec, 0xff, 0xc5, 0xc1, 0xe3, 0xff, 0x6b, 0x70, 0x80, 0xff, 0x19, 0x22, 0x24, 0xff, 0x02, 0x08, 0x0a, 0xff, 0x16, 0x1a, 0x18, 0xff, 0x2a, 0x2c, 0x24, 0xff, 0x2c, 0x2d, 0x1f, 0xff, 0x2a, 0x2a, 0x22, 0xff, 0x29, 0x27, 0x27, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x27, 0x2a, 0x26, 0xff, 0x26, 0x2a, 0x26, 0xff, 0x23, 0x28, 0x27, 0xff, 0x25, 0x29, 0x28, 0xff, 0x25, 0x2d, 0x3f, 0xff, 0x32, 0x3f, 0x61, 0xff, 0x3b, 0x4a, 0x71, 0xff, 0x36, 0x49, 0x76, 0xff, 0x2e, 0x41, 0x6d, 0xff, 0x2a, 0x3a, 0x65, 0xff, 0x28, 0x38, 0x5c, 0xff, 0x29, 0x39, 0x58, 0xff, 0x2d, 0x3a, 0x5a, 0xff, 0x2e, 0x3b, 0x5b, 0xff, 0x2c, 0x3e, 0x5c, 0xff, 0x29, 0x40, 0x5a, 0xff, 0x2c, 0x40, 0x5f, 0xff, 0x32, 0x46, 0x69, 0xff, 0x2e, 0x4c, 0x70, 0xff, 0x33, 0x5a, 0x83, 0xff, 0x3e, 0x6a, 0x9d, 0xff, 0x50, 0x80, 0xc0, 0xff, 0x4a, 0x7e, 0xc5, 0xff, 0x37, 0x64, 0xa3, 0xff, 0x2a, 0x43, 0x69, 0xff, 0x28, 0x30, 0x40, 0xff, 0x31, 0x30, 0x37, 0xff, 0x2d, 0x2f, 0x39, 0xff, 0x29, 0x2c, 0x34, 0xff, 0x28, 0x2b, 0x2f, 0xff, 0x26, 0x28, 0x2b, 0xff, 0x26, 0x28, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x2a, 0x28, 0x29, 0xff, 0x1b, 0x1c, 0x15, 0xff, 0x37, 0x39, 0x3b, 0xff, 0x87, 0x8a, 0xa0, 0xff, 0xa7, 0xb0, 0xca, 0xff, 0xa0, 0xa9, 0xc1, 0xfe, 0x89, 0x90, 0xa9, 0x8f, 0x9f, 0x9f, 0xbf, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2e, 0x2a, 0x2a, 0x42, 0x17, 0x16, 0x18, 0xdb, 0x00, 0x03, 0x09, 0xff, 0x39, 0x3d, 0x47, 0xff, 0x8f, 0x94, 0x9f, 0xff, 0xdb, 0xe4, 0xf5, 0xff, 0xd4, 0xdc, 0xf1, 0xff, 0x76, 0x7a, 0x8d, 0xff, 0x2f, 0x30, 0x3d, 0xff, 0x0c, 0x0c, 0x0f, 0xff, 0x20, 0x1f, 0x1b, 0xff, 0x2c, 0x2c, 0x27, 0xff, 0x29, 0x2b, 0x26, 0xff, 0x28, 0x2a, 0x27, 0xff, 0x27, 0x28, 0x26, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x29, 0x27, 0xff, 0x26, 0x2b, 0x25, 0xff, 0x26, 0x2a, 0x25, 0xff, 0x23, 0x29, 0x24, 0xff, 0x23, 0x24, 0x25, 0xff, 0x24, 0x22, 0x29, 0xff, 0x29, 0x2e, 0x3c, 0xff, 0x30, 0x39, 0x4e, 0xff, 0x33, 0x3e, 0x58, 0xff, 0x31, 0x3d, 0x57, 0xff, 0x2d, 0x37, 0x53, 0xff, 0x2d, 0x37, 0x51, 0xff, 0x2c, 0x37, 0x4f, 0xff, 0x2f, 0x3a, 0x4f, 0xff, 0x2d, 0x3b, 0x52, 0xff, 0x2a, 0x3c, 0x52, 0xff, 0x2a, 0x3a, 0x5c, 0xff, 0x30, 0x3c, 0x63, 0xff, 0x2f, 0x46, 0x66, 0xff, 0x2c, 0x56, 0x82, 0xff, 0x3a, 0x6d, 0xa9, 0xff, 0x5b, 0x86, 0xcb, 0xff, 0x4d, 0x7a, 0xbe, 0xff, 0x2b, 0x54, 0x8a, 0xff, 0x24, 0x34, 0x50, 0xff, 0x31, 0x2f, 0x33, 0xff, 0x32, 0x2e, 0x2b, 0xff, 0x26, 0x28, 0x2e, 0xff, 0x22, 0x26, 0x2c, 0xff, 0x24, 0x27, 0x28, 0xff, 0x25, 0x27, 0x28, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x1d, 0x20, 0x1b, 0xff, 0x35, 0x36, 0x3e, 0xff, 0x7b, 0x7d, 0x96, 0xff, 0xa0, 0xab, 0xc5, 0xdb, 0xaa, 0xb1, 0xcc, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x1c, 0x1c, 0x09, 0x29, 0x2d, 0x27, 0x87, 0x12, 0x19, 0x11, 0xf9, 0x00, 0x07, 0x00, 0xff, 0x1d, 0x23, 0x2b, 0xff, 0x84, 0x85, 0x9f, 0xff, 0xd9, 0xd8, 0xf0, 0xff, 0xbb, 0xb8, 0xd1, 0xff, 0x6a, 0x69, 0x7c, 0xff, 0x21, 0x21, 0x30, 0xff, 0x0b, 0x0c, 0x12, 0xff, 0x21, 0x23, 0x24, 0xff, 0x29, 0x2b, 0x2b, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x27, 0x29, 0x26, 0xff, 0x27, 0x29, 0x28, 0xff, 0x2b, 0x29, 0x22, 0xff, 0x2a, 0x28, 0x1e, 0xff, 0x21, 0x23, 0x1e, 0xff, 0x20, 0x23, 0x23, 0xff, 0x25, 0x2a, 0x2f, 0xff, 0x2a, 0x33, 0x39, 0xff, 0x2b, 0x32, 0x3e, 0xff, 0x2e, 0x31, 0x41, 0xff, 0x30, 0x35, 0x3f, 0xff, 0x31, 0x37, 0x3f, 0xff, 0x32, 0x3b, 0x4a, 0xff, 0x15, 0x21, 0x35, 0xff, 0x20, 0x2b, 0x4b, 0xff, 0x36, 0x3d, 0x5d, 0xff, 0x32, 0x40, 0x5e, 0xff, 0x2b, 0x4f, 0x83, 0xff, 0x3c, 0x6c, 0xb1, 0xff, 0x66, 0x89, 0xc5, 0xff, 0x5d, 0x77, 0xa4, 0xff, 0x33, 0x49, 0x68, 0xff, 0x24, 0x2d, 0x3b, 0xff, 0x2a, 0x28, 0x26, 0xff, 0x2b, 0x28, 0x22, 0xff, 0x24, 0x27, 0x27, 0xff, 0x23, 0x27, 0x29, 0xff, 0x26, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x29, 0x28, 0x2a, 0xff, 0x1b, 0x1d, 0x1b, 0xff, 0x34, 0x35, 0x40, 0xf9, 0x83, 0x85, 0xa1, 0x88, 0xaa, 0xaa, 0xc6, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x29, 0x29, 0x25, 0x2c, 0x29, 0x29, 0xb7, 0x1b, 0x1a, 0x14, 0xff, 0x01, 0x00, 0x00, 0xff, 0x4a, 0x48, 0x56, 0xff, 0xd1, 0xce, 0xe6, 0xff, 0xc8, 0xca, 0xe2, 0xff, 0x8c, 0x96, 0xa9, 0xff, 0x4d, 0x50, 0x5f, 0xff, 0x13, 0x0f, 0x19, 0xff, 0x0e, 0x0f, 0x10, 0xff, 0x27, 0x28, 0x25, 0xff, 0x2a, 0x2a, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x29, 0x26, 0x2b, 0xff, 0x29, 0x26, 0x2b, 0xff, 0x27, 0x28, 0x27, 0xff, 0x26, 0x29, 0x25, 0xff, 0x2a, 0x29, 0x26, 0xff, 0x2a, 0x28, 0x25, 0xff, 0x27, 0x26, 0x24, 0xff, 0x24, 0x24, 0x24, 0xff, 0x24, 0x23, 0x26, 0xff, 0x26, 0x25, 0x27, 0xff, 0x29, 0x2a, 0x26, 0xff, 0x21, 0x23, 0x23, 0xff, 0x32, 0x36, 0x41, 0xff, 0x79, 0x7d, 0x97, 0xff, 0x29, 0x35, 0x49, 0xff, 0x25, 0x2f, 0x37, 0xff, 0x34, 0x39, 0x50, 0xff, 0x29, 0x49, 0x80, 0xff, 0x49, 0x7b, 0xbb, 0xff, 0x50, 0x73, 0x96, 0xff, 0x2c, 0x35, 0x40, 0xff, 0x25, 0x24, 0x2d, 0xff, 0x24, 0x25, 0x29, 0xff, 0x26, 0x27, 0x27, 0xff, 0x27, 0x29, 0x26, 0xff, 0x26, 0x2a, 0x26, 0xff, 0x26, 0x29, 0x26, 0xff, 0x27, 0x28, 0x27, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2c, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x29, 0x29, 0x2c, 0xff, 0x21, 0x23, 0x25, 0xff, 0x1a, 0x1e, 0x1a, 0xb7, 0x57, 0x57, 0x64, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2b, 0x28, 0x28, 0x46, 0x27, 0x25, 0x1f, 0xca, 0x0b, 0x0c, 0x0c, 0xff, 0x59, 0x5a, 0x63, 0xff, 0xce, 0xd0, 0xe2, 0xff, 0xc8, 0xcb, 0xe4, 0xff, 0xbb, 0xbf, 0xd8, 0xff, 0x98, 0x9e, 0xb2, 0xff, 0x46, 0x4b, 0x5a, 0xff, 0x06, 0x09, 0x12, 0xff, 0x1b, 0x1c, 0x1d, 0xff, 0x30, 0x2d, 0x28, 0xff, 0x2b, 0x29, 0x26, 0xff, 0x28, 0x27, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x27, 0x28, 0x29, 0xff, 0x26, 0x28, 0x2b, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x29, 0xff, 0x2b, 0x28, 0x28, 0xff, 0x2b, 0x28, 0x26, 0xff, 0x28, 0x28, 0x24, 0xff, 0x25, 0x25, 0x24, 0xff, 0x0d, 0x0d, 0x13, 0xff, 0xa3, 0xa1, 0xb1, 0xff, 0x96, 0x99, 0xa9, 0xff, 0x11, 0x1b, 0x21, 0xff, 0x23, 0x35, 0x41, 0xff, 0x29, 0x44, 0x72, 0xff, 0x81, 0x9c, 0xd3, 0xff, 0x4a, 0x5b, 0x6d, 0xff, 0x02, 0x03, 0x00, 0xff, 0x2a, 0x26, 0x24, 0xff, 0x29, 0x29, 0x29, 0xff, 0x27, 0x28, 0x29, 0xff, 0x27, 0x28, 0x27, 0xff, 0x27, 0x29, 0x26, 0xff, 0x27, 0x29, 0x26, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x25, 0x28, 0x2a, 0xff, 0x27, 0x28, 0x28, 0xff, 0x29, 0x29, 0x27, 0xff, 0x28, 0x28, 0x26, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xca, 0x0e, 0x0e, 0x0e, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x27, 0x27, 0x24, 0x4d, 0x00, 0x02, 0x00, 0xcc, 0x4f, 0x51, 0x5a, 0xff, 0xdd, 0xd8, 0xf0, 0xff, 0xc7, 0xcc, 0xe7, 0xff, 0xca, 0xd7, 0xf0, 0xff, 0xc0, 0xc8, 0xe4, 0xff, 0x74, 0x7a, 0x8f, 0xff, 0x1a, 0x1a, 0x24, 0xff, 0x18, 0x15, 0x11, 0xff, 0x2f, 0x2c, 0x2a, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x28, 0x27, 0x29, 0xff, 0x27, 0x28, 0x27, 0xff, 0x28, 0x29, 0x26, 0xff, 0x28, 0x29, 0x25, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x28, 0xff, 0x1e, 0x1c, 0x1e, 0xff, 0x30, 0x2f, 0x35, 0xff, 0x7b, 0x73, 0x83, 0xff, 0x2b, 0x2c, 0x36, 0xff, 0x0b, 0x1f, 0x20, 0xff, 0x2b, 0x38, 0x52, 0xff, 0xa2, 0x9e, 0xc5, 0xff, 0x55, 0x50, 0x5a, 0xff, 0x17, 0x17, 0x11, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x25, 0x28, 0x29, 0xff, 0x23, 0x28, 0x2a, 0xff, 0x27, 0x28, 0x28, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x2a, 0x28, 0x25, 0xcc, 0x2b, 0x2b, 0x2b, 0x4d, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x41, 0x4c, 0x4f, 0x56, 0xb4, 0xe9, 0xec, 0xff, 0xfe, 0xc3, 0xc8, 0xdf, 0xff, 0xbc, 0xc2, 0xdb, 0xff, 0xd7, 0xdc, 0xf5, 0xff, 0x9b, 0xa0, 0xb2, 0xff, 0x16, 0x1b, 0x27, 0xff, 0x15, 0x17, 0x19, 0xff, 0x2c, 0x2c, 0x29, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x27, 0x29, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x27, 0x28, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2a, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x2b, 0x2a, 0x2e, 0xff, 0x1b, 0x1b, 0x23, 0xff, 0x36, 0x32, 0x3e, 0xff, 0x28, 0x25, 0x25, 0xff, 0x23, 0x24, 0x19, 0xff, 0x27, 0x24, 0x28, 0xff, 0x2b, 0x23, 0x34, 0xff, 0x24, 0x21, 0x24, 0xff, 0x2a, 0x2b, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x29, 0x28, 0xfe, 0x2a, 0x27, 0x27, 0xb4, 0x2b, 0x27, 0x27, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0x8a, 0x91, 0x23, 0xfd, 0xff, 0xff, 0x88, 0xcd, 0xd1, 0xe8, 0xe6, 0xce, 0xd4, 0xef, 0xff, 0xea, 0xf2, 0xff, 0xff, 0x87, 0x90, 0xa6, 0xff, 0x06, 0x0c, 0x14, 0xff, 0x22, 0x24, 0x21, 0xff, 0x27, 0x2a, 0x27, 0xff, 0x29, 0x29, 0x27, 0xff, 0x29, 0x27, 0x2a, 0xff, 0x27, 0x26, 0x2d, 0xff, 0x27, 0x27, 0x2a, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x28, 0x29, 0x27, 0xff, 0x28, 0x29, 0x27, 0xff, 0x25, 0x25, 0x25, 0xff, 0x31, 0x32, 0x34, 0xff, 0x27, 0x28, 0x33, 0xff, 0x38, 0x3a, 0x3e, 0xff, 0x3e, 0x3b, 0x31, 0xff, 0x32, 0x2b, 0x24, 0xff, 0x29, 0x22, 0x25, 0xff, 0x22, 0x25, 0x2a, 0xff, 0x26, 0x29, 0x2a, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xe7, 0x29, 0x27, 0x27, 0x88, 0x2b, 0x2b, 0x24, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x05, 0xdc, 0xe3, 0xfb, 0x4a, 0xda, 0xe2, 0xf8, 0xa0, 0xe7, 0xf4, 0xfe, 0xef, 0x5a, 0x63, 0x6e, 0xff, 0x08, 0x0c, 0x0f, 0xff, 0x29, 0x29, 0x29, 0xff, 0x2b, 0x28, 0x26, 0xff, 0x29, 0x28, 0x28, 0xff, 0x25, 0x27, 0x2b, 0xff, 0x26, 0x27, 0x29, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x29, 0x27, 0xff, 0x24, 0x25, 0x24, 0xff, 0x2c, 0x2e, 0x2e, 0xff, 0x35, 0x38, 0x3f, 0xff, 0x69, 0x6d, 0x75, 0xff, 0x40, 0x40, 0x43, 0xff, 0x39, 0x38, 0x37, 0xff, 0x33, 0x34, 0x34, 0xff, 0x1c, 0x22, 0x29, 0xff, 0x24, 0x27, 0x2a, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x27, 0x27, 0x27, 0xef, 0x27, 0x27, 0x27, 0xa0, 0x29, 0x29, 0x29, 0x4a, 0x33, 0x33, 0x33, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xb9, 0xc1, 0xcc, 0x46, 0x10, 0x15, 0x22, 0x8e, 0x23, 0x21, 0x23, 0xd7, 0x2d, 0x29, 0x24, 0xff, 0x2a, 0x29, 0x25, 0xff, 0x23, 0x29, 0x28, 0xff, 0x26, 0x28, 0x29, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x27, 0x29, 0xff, 0x2a, 0x27, 0x29, 0xff, 0x28, 0x28, 0x27, 0xff, 0x28, 0x2a, 0x27, 0xff, 0x28, 0x29, 0x27, 0xff, 0x23, 0x27, 0x27, 0xff, 0x2b, 0x2c, 0x28, 0xff, 0x39, 0x38, 0x38, 0xff, 0x1f, 0x21, 0x2d, 0xff, 0x23, 0x29, 0x2d, 0xff, 0x25, 0x2b, 0x26, 0xff, 0x29, 0x2a, 0x24, 0xff, 0x29, 0x28, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xd7, 0x27, 0x27, 0x27, 0x8e, 0x28, 0x28, 0x28, 0x46, 0x24, 0x24, 0x24, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, 0x25, 0x25, 0x1b, 0x2a, 0x27, 0x27, 0x55, 0x25, 0x29, 0x27, 0x8f, 0x26, 0x28, 0x27, 0xc2, 0x29, 0x28, 0x28, 0xea, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x29, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x27, 0xff, 0x26, 0x27, 0x26, 0xff, 0x26, 0x27, 0x29, 0xff, 0x33, 0x32, 0x2e, 0xff, 0x3a, 0x37, 0x33, 0xff, 0x26, 0x26, 0x2b, 0xff, 0x27, 0x29, 0x2c, 0xff, 0x26, 0x29, 0x25, 0xff, 0x2b, 0x29, 0x23, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x27, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xea, 0x27, 0x27, 0x27, 0xc2, 0x27, 0x27, 0x27, 0x8f, 0x27, 0x27, 0x27, 0x55, 0x25, 0x25, 0x25, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2d, 0x2d, 0x2d, 0x11, 0x29, 0x29, 0x29, 0x37, 0x28, 0x28, 0x28, 0x5f, 0x27, 0x27, 0x27, 0x86, 0x27, 0x28, 0x27, 0xa8, 0x27, 0x29, 0x26, 0xc0, 0x27, 0x27, 0x27, 0xd3, 0x26, 0x26, 0x27, 0xe6, 0x25, 0x25, 0x26, 0xee, 0x28, 0x27, 0x29, 0xf5, 0x2e, 0x2e, 0x30, 0xff, 0x33, 0x33, 0x34, 0xff, 0x28, 0x28, 0x26, 0xf5, 0x29, 0x29, 0x28, 0xee, 0x26, 0x26, 0x27, 0xe6, 0x25, 0x25, 0x27, 0xd3, 0x27, 0x27, 0x29, 0xc0, 0x27, 0x27, 0x27, 0xa8, 0x27, 0x27, 0x27, 0x86, 0x28, 0x28, 0x28, 0x5f, 0x29, 0x29, 0x29, 0x37, 0x2d, 0x2d, 0x2d, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_image_dsc_t img_multilang_avatar_22 = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 128,
    .header.h = 128,
    .header.stride = 512,
    .data = img_multilang_avatar_22_map,
    .data_size = sizeof(img_multilang_avatar_22_map),
};

#endif
