/**
 * @file lv_example_freetype.h
 *
 */

#ifndef LV_EXAMPLE_FREETYPE_H
#define LV_EXAMPLE_FREETYPE_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_freetype_1(void);
void lv_example_freetype_2(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_FREETYPE_H*/
