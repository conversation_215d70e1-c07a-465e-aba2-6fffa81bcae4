#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: line.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_line_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCC,0x34,
0xCC,0x34,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCC,0x34,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xE8,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE8,0x45,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0xE8,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,
0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x89,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,
0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE8,0x45,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xAB,0x55,0x2F,0x7E,0x72,0x8E,0x71,0x8E,0x2F,0x7E,0x8B,0x55,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xE8,0x45,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x0F,0x7E,0x59,0xCF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x39,0xCF,0x0F,0x76,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCC,0x34,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x8A,0x55,0xF7,0xBE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF6,0xB6,0x8A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xE8,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x17,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF7,0xB6,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x0F,0x7E,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x0F,0x7E,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x17,0xBF,0xFF,0xFF,0xDE,0xF7,0x93,0x9E,0xFF,0xFF,0x9B,0xDF,0xF6,0xB6,0x30,0x86,
0x9C,0xDF,0xB4,0x9E,0x9B,0xDF,0x0E,0x76,0x72,0x96,0xDE,0xF7,0xFF,0xFF,0x17,0xBF,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x7A,0xD7,0xFF,0xFF,0xBD,0xEF,0x93,0x96,0xFF,0xFF,0x7B,0xDF,0xD5,0xAE,0x51,0x86,0x51,0x8E,0x93,0x9E,0x7A,0xD7,0x2F,0x7E,0xD5,0xAE,0xFE,0xF7,0xFF,0xFF,0x7A,0xD7,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,
0x00,0x00,0xCC,0x34,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x5A,0xD7,0xFF,0xFF,0xBD,0xEF,0x93,0x96,0xFF,0xFF,0x7B,0xDF,0xD5,0xAE,0x71,0x8E,0xF7,0xB6,0xCD,0x65,0x7A,0xD7,0x93,0x9E,0xBD,0xE7,0xFF,0xFF,0xFF,0xFF,0x39,0xCF,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xCC,0x34,0x00,0x00,0x00,0x00,0xCC,0x34,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xF6,0xB6,0xFF,0xFF,0xDE,0xF7,0x0F,0x7E,0x72,0x96,0x7B,0xDF,0xF7,0xB6,0xB4,0x9E,
0xFF,0xFF,0x0F,0x7E,0x9C,0xE7,0x30,0x7E,0x72,0x96,0xBD,0xEF,0xFF,0xFF,0x72,0x96,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xE8,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xEE,0x6D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9C,0xE7,0x8A,0x55,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xD5,0xAE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x0E,0x76,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xB4,0xA6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x51,0x86,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xCD,0x65,0xF7,0xB6,0xDE,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDE,0xF7,0x30,0x7E,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xCC,0x34,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x8A,0x4D,0xEE,0x6D,0x9C,0xE7,0xFF,0xFF,0xFF,0xFF,0x7A,0xD7,0xCC,0x65,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0xE8,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x7B,0xDF,
0xDE,0xF7,0x93,0x9E,0x8A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE8,0x45,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x2F,0x7E,0x8B,0x55,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x4A,0x55,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,
0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE8,0x45,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,
0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x4A,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE8,0x45,0x6A,0x4D,0x89,0x4D,0x6A,0x4D,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0x6A,0x4D,0x6A,0x4D,0x89,0x4D,0xE8,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCC,0x34,0xCC,0x34,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x05,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x44,0x93,0xCF,0xE8,0xFE,0xFE,0xE8,0xCF,0x92,0x43,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x68,0xE6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE6,0x67,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0xD8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD7,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4B,0xF1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x52,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x52,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x2F,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xD8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD6,0x04,0x00,0x00,0x00,
0x00,0x00,0x00,0x69,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x65,0x00,0x00,0x00,0x00,0x00,0x05,0xE6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x04,0x00,0x00,0x00,0x00,0x45,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x42,0x00,0x00,
0x00,0x00,0x94,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x91,0x00,0x00,0x00,0x00,0xCF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCD,0x00,0x00,0x00,0x00,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE8,0x00,0x00,
0x00,0x05,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x05,0x00,0x00,0x05,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x04,0x00,0x00,0x00,0xE9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE7,0x00,0x00,
0x00,0x00,0xCF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCD,0x00,0x00,0x00,0x00,0x92,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x8F,0x00,0x00,0x00,0x00,0x43,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x40,0x00,0x00,
0x00,0x00,0x05,0xE6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x04,0x00,0x00,0x00,0x00,0x00,0x67,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xD7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD5,0x03,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2A,0xF1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x51,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0x4F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x4A,0xF1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x51,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x2E,0xD6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD4,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x65,0xE5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x64,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x42,0x91,0xCE,0xE7,0xFE,0xFE,0xE7,0xCE,0x90,0x41,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x05,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_line_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_line_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_line_icon_data};

