#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: whatsapp.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_whatsapp_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x22,0x25,0x23,0x2D,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x43,0x2D,0x82,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x43,0x1D,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xE4,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x03,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x42,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xE4,0x24,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x44,0x2D,0x2C,0x6E,0x16,0xB7,0x9B,0xE7,0xDE,0xF7,0xDE,0xF7,0x9B,0xE7,0x16,0xB7,0x2C,0x6E,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x43,0x2D,0x43,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x0B,0x6E,0x9C,0xE7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9C,0xE7,0x0B,0x6E,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xB2,0x9E,0xFF,0xFF,0xFF,0xFF,0xDE,0xF7,0xD4,0xAE,0x0C,0x6E,0xC9,0x55,0xC9,0x55,0x0C,0x6E,0xD4,0xAE,0xDE,0xF7,0xFF,0xFF,0xFF,0xFF,0xB2,0x9E,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x42,0x2D,0x00,0x00,
0x00,0x00,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xB2,0x9E,0xFF,0xFF,0xFF,0xFF,0xF5,0xAE,0x65,0x3D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x65,0x3D,0xF5,0xB6,0xFF,0xFF,0xFF,0xFF,0xB1,0x96,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x00,0x00,0x22,0x25,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x0B,0x66,0xFF,0xFF,0xFF,0xFF,0x91,0x8E,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x91,0x96,0xFF,0xFF,0xFF,0xFF,0xEB,0x65,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x82,0x25,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x43,0x2D,0x9B,0xDF,0xFF,0xFF,0xF5,0xB6,0x23,0x2D,0x65,0x3D,0x58,0xCF,0x0B,0x6E,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x16,0xB7,0xFF,0xFF,0x9B,0xDF,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x0B,0x66,0xFF,0xFF,0xDE,0xF7,0x65,0x3D,0x23,0x2D,0x37,0xC7,0xFF,0xFF,0x7A,0xD7,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x86,0x45,0xFE,0xF7,0xFF,0xFF,0xEB,0x65,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xF4,0xAE,0xFF,0xFF,0xF5,0xB6,0x23,0x2D,0x23,0x2D,0xFE,0xF7,0xFF,0xFF,0xFF,0xFF,0xA8,0x4D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x16,0xB7,0xFF,0xFF,0xD4,0xAE,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x79,0xD7,0xFF,0xFF,0x4E,0x7E,0x23,0x2D,0x23,0x2D,0x9B,0xE7,0xFF,0xFF,0x7A,0xD7,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x4E,0x7E,0xFF,0xFF,0x59,0xCF,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xBC,0xEF,0xFF,0xFF,0xEA,0x65,0x23,0x2D,0x23,0x2D,0x91,0x96,0xFF,0xFF,0x58,0xCF,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xEB,0x65,0xFF,0xFF,0xBC,0xE7,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xBC,0xE7,0xFF,0xFF,0xEB,0x65,0x23,0x2D,0x23,0x2D,0x44,0x35,0x9C,0xE7,0xFF,0xFF,0xF4,0xAE,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x0B,0x66,0xFF,0xFF,0xBC,0xE7,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x59,0xCF,0xFF,0xFF,0x4F,0x86,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xA7,0x4D,0xDD,0xEF,0xFF,0xFF,0x15,0xB7,0x44,0x35,0x23,0x2D,0x4E,0x7E,0x0B,0x6E,0x44,0x35,0x23,0x2D,0x23,0x2D,0x6F,0x86,0xFF,0xFF,0x58,0xCF,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xD3,0xA6,0xFF,0xFF,0x37,0xBF,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xC8,0x55,0xBC,0xE7,0xFF,0xFF,0xBC,0xE7,0x58,0xCF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC9,0x55,0x23,0x2D,0x37,0xC7,0xFF,0xFF,0xB3,0x9E,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xC9,0x55,0xFF,0xFF,0xFF,0xFF,0x87,0x45,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x65,0x3D,0x37,0xBF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9C,0xE7,0x65,0x35,0xA8,0x4D,0xFF,0xFF,0xFF,0xFF,0xC9,0x55,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x43,0x2D,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xBD,0xEF,0xFF,0xFF,0x2C,0x76,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xC9,0x55,0xD3,0xA6,0x37,0xC7,0xF4,0xAE,0x86,0x45,0x43,0x2D,0x58,0xCF,0xFF,0xFF,0x59,0xCF,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x43,0x2D,
0x42,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x65,0x35,0xFF,0xFF,0xFF,0xFF,0x87,0x45,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x43,0x2D,0xF5,0xAE,0xFF,0xFF,0xFF,0xFF,0xA8,0x55,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x42,0x2D,0x00,0x00,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xEA,0x65,0xFF,0xFF,0xDD,0xF7,0x43,0x2D,0xA8,0x4D,0x4E,0x7E,0xA8,0x4D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0xA8,0x55,0x59,0xCF,0xFF,0xFF,0xFF,0xFF,0x2D,0x7E,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x00,0x00,0x00,0x00,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x90,0x8E,0xFF,0xFF,0xDE,0xF7,0xDD,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x58,0xCF,0x91,0x96,0x2D,0x76,0x2D,0x76,0x91,0x96,0x59,0xCF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x76,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x22,0x25,0x00,0x00,
0x00,0x00,0x00,0x00,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x16,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBC,0xE7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x37,0xC7,0xA8,0x4D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE4,0x24,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x79,0xD7,0x59,0xCF,0xB2,0x9E,0x0B,0x6E,0x65,0x35,0x23,0x2D,0xA7,0x4D,0x91,0x96,0x37,0xBF,0x7A,0xD7,
0x7A,0xD7,0x16,0xBF,0x91,0x96,0xA7,0x4D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xA4,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x43,0x25,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x23,0x25,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x43,0x25,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x42,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA4,0x25,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0xA4,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x22,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x2D,0x23,0x2D,0x43,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,
0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x23,0x2D,0x42,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x1B,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_whatsapp_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_whatsapp_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_whatsapp_icon_data};

