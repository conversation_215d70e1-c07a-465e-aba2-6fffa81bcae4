#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: bluetooth_g.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_bluetooth_g_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x50,0x6E,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x11,0x66,0xF1,0x65,0x31,0x66,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF1,0x65,0x11,0x66,0x11,0x66,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,
0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0x11,0x66,0xF1,0x65,0x11,0x66,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0x56,0xF1,0x65,0xF1,0x5D,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF0,0x65,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0x11,0x5E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,
0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xF0,0x45,0x00,0x00,0xF3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xF2,0x55,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xF3,0x65,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x93,0x4D,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0x35,0x56,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0x00,0x00,0x00,0x00,0xB4,0x4D,
0x00,0x00,0x00,0x00,0xD3,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0x00,0x00,0x00,0x00,0xD4,0x4D,0xD4,0x4D,0xD5,0x45,0x00,0x00,0x00,0x00,0xB4,0x4D,0xB4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xB3,0x4D,0x00,0x00,
0x00,0x00,0xD4,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0x00,0x00,0x00,0x00,0xB5,0x4D,0xB5,0x4D,0xB4,0x4D,0xB5,0x4D,0x10,0x84,0x00,0x00,0xF3,0x45,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xD5,0x4D,0x00,0x00,0xB4,0x4D,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x00,0x00,0x00,0x00,0xB5,0x45,
0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x4D,0x00,0x00,0x00,0x00,0xB5,0x4D,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xF5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xFF,0x07,0x95,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x00,0x00,0x00,0x00,0xB6,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x00,0x00,0x00,0x00,0xB6,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x96,0x45,
0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0xB6,0x3D,0xD7,0x3D,0x00,0x00,0x00,0x00,0xB6,0x3D,0x96,0x3D,0x96,0x3D,0x00,0x00,0x00,0x00,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x79,0x36,0x00,0x00,0x79,0x36,0xB6,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x97,0x3D,0x10,0x04,0x00,0x00,0x96,0x35,0x96,0x3D,0x00,0x00,0x00,0x00,0x96,0x3D,
0x96,0x3D,0xB6,0x45,0x00,0x00,0x00,0x00,0x96,0x3D,0x97,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x97,0x3D,0x00,0x00,0x00,0x00,0x97,0x35,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,
0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x77,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0xD9,0x34,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,
0xD9,0x34,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x98,0x2D,0x00,0x00,0x00,0x00,0x99,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,
0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x1F,0x04,0x00,0x00,0x99,0x35,0x79,0x2D,0x00,0x00,0x00,0x00,0x79,0x2D,0x79,0x2D,0x59,0x2D,0x00,0x00,0x00,0x00,0x59,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x58,0x2D,0x00,0x00,0x00,0x00,0x59,0x2D,0x79,0x2D,0x79,0x2D,0x00,0x00,0x00,0x00,0x79,0x2D,
0x79,0x2D,0x79,0x2D,0x79,0x2D,0xD9,0x34,0x00,0x00,0xD9,0x34,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0xFF,0x07,0x7A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x00,0x00,0x00,0x00,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x00,0x00,0x00,0x00,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,
0x9B,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x00,0x00,0x00,0x00,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x00,0x00,0x00,0x00,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x2D,0x00,0x00,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x00,0x00,0x00,0x00,0x5B,0x1D,
0x5B,0x1D,0x3B,0x1D,0x5A,0x25,0x1F,0x04,0x00,0x00,0xFB,0x24,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x3A,0x1D,0x00,0x00,0x00,0x00,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x00,0x00,0x00,0x00,0x3B,0x1D,0x3B,0x1D,0x5C,0x1D,0x00,0x00,0x00,0x00,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x00,0x00,
0x00,0x00,0x00,0x00,0x3C,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x15,0x00,0x00,0x00,0x00,0x3B,0x15,0x00,0x00,0x00,0x00,0x3B,0x15,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFB,0x24,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x00,0x00,0x00,0x00,0x00,0x00,
0xDC,0x1C,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0xBB,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1D,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3D,0x15,0xFF,0x05,0x00,0x00,0x3D,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0xFD,0x14,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1D,0x15,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x3D,0x15,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x0C,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,
0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0xFD,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0x04,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x9F,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x04,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0xFF,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,
0xFF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,0x1F,0x05,0xBF,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC1,0xC6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0x04,0x00,0x57,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x00,0x00,0x00,0x0A,0x80,0xF8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x00,0x00,0x2E,0x00,0x00,0x25,0xB6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x00,0x00,0xF4,0x96,0x12,0x00,0x00,0x4E,0xDD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x00,0x00,0xF7,0xFF,0xEC,0x67,0x02,0x00,0x08,0x7E,0xF6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0xFB,0xFF,0xFF,0xFF,0xE4,0x00,0x00,0xF7,0xFF,0xFF,0xFF,0xCC,0x39,0x00,0x00,0x2E,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0x01,0x28,0xBA,0xFF,0xFF,0xE4,0x00,0x00,0xF7,0xFF,0xFF,0xFF,0xD4,0x42,0x00,0x00,0x27,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xD5,0x12,0x00,0x00,0x52,0xE0,0xE4,0x00,0x00,0xF7,0xFF,0xF1,0x72,0x05,0x00,0x05,0x73,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0x64,0x02,0x00,0x0A,0x6E,0x00,0x00,0xF6,0xA2,0x18,0x00,0x00,0x43,0xD6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCA,0x35,0x00,0x00,0x00,0x00,0x37,0x00,0x00,0x1D,0xAB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xA2,0x16,0x00,0x00,0x00,0x05,0x6F,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xA2,0x16,0x00,0x00,0x00,0x05,0x6F,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCA,0x35,0x00,0x00,0x00,0x00,0x37,0x00,0x00,0x1D,0xAA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xC9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0x64,0x02,0x00,0x0A,0x6E,0x00,0x00,0xF6,0xA2,0x18,0x00,0x00,0x43,0xD6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xD4,0x12,0x00,0x00,0x52,0xE0,0xE4,0x00,0x00,0xF7,0xFF,0xF1,0x72,0x05,0x00,0x05,0x73,0xF1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xC0,0x01,0x28,0xBA,0xFF,0xFF,0xE4,0x00,0x00,0xF7,0xFF,0xFF,0xFF,0xD4,0x42,0x00,0x00,0x27,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xE6,0xFB,0xFF,0xFF,0xFF,0xE4,0x00,0x00,0xF7,0xFF,0xFF,0xFF,0xCC,0x39,0x00,0x00,0x2D,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x00,0x00,0xF7,0xFF,0xEC,0x67,0x02,0x00,0x08,0x7E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x00,0x00,0xF4,0x96,0x12,0x00,0x00,0x4E,0xDD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x00,0x00,0x2E,0x00,0x00,0x25,0xB6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE4,0x00,0x00,0x00,0x0A,0x80,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x19,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x04,0x00,0x57,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x18,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC3,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_bluetooth_g_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_bluetooth_g_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_bluetooth_g_icon_data};

