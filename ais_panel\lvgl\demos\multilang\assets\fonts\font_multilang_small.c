/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 15 --format lvgl --output font_multilang_small.c --font NotoSerifHebrew-SemiBold.ttf --symbols קורא נלהב שצובר אוסף עצום של ספרים יקרים --font NotoSerifDevanagari-SemiBold.ttf --symbols हरित कार्यकर्ता, एक स्थायी कल के लिए प्रयासरत। --font NotoNaskhArabic-SemiBold.ttf -r 0xFEEA, 0xFEE4, 0xFEEB, 0xFED6, 0xFEB7, 0xFE8E, 0xFECB, 0xFEED, 0xFEA6, 0xFBFE, 0xFEAD, 0xFE8E, 0xFE97, 0xFED6, 0xFEB7, 0xFE8E, 0xFECB, 0xFEA9, 0xFEAD, 0xFEEE, 0xFEE3, 0xFE96, 0xFEE4, 0xFEB4, 0xFED7, 0xFEEA, 0xFED8, 0xFBFF, 0xFE98, 0xFECB, 0xFEB0, 0xFBFF, 0xFB7C, 0xFE96, 0xFEB3, 0xFE8D, 0xFEE5, 0xFEAE, 0xFED7, 0xFEE6, 0xFEE3, 0xFEEA, 0xFED7, 0xFEFC, 0xFECB, 0xFEF1, 0xFE8B, 0xFEE8, 0xFEF4, 0xFEAA, 0xFEE7, 0xFEE1, 0xFED3, 0xFEF8, 0xFEDF, 0xFECD, 0xFE92, 0xFEE0, 0xFEF4, 0xFE92, 0xFED4, 0xFEF4, 0xFE91, 0xFE90, 0xFEA0, 0xFECC, 0xFEF2, 0xFEBF --font NotoSansSC-Medium.otf --symbols 对编程和技术充满热情。 开源倡导者。 --font Montserrat-SemiBold.ttf -r 0x20-0x24f --symbols Любитель приключений на свежем воздухе, всегда ищущий новых острых ощущений
 ******************************************************************************/

#include "../../../../lvgl.h"

#ifndef FONT_MULTILANG_SMALL
    #define FONT_MULTILANG_SMALL 1
#endif

#if FONT_MULTILANG_SMALL

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0xf2, 0xf, 0xf1, 0xe, 0xf1, 0xd, 0xf0,
    0xc, 0xf0, 0xb, 0xe0, 0xa, 0xc0, 0x2, 0x20,
    0xf, 0xf2, 0xc, 0xe1,

    /* U+0022 "\"" */
    0x2f, 0x74, 0xf5, 0x1f, 0x63, 0xf4, 0x1f, 0x63,
    0xf4, 0x1f, 0x52, 0xf3, 0x1, 0x0, 0x10,

    /* U+0023 "#" */
    0x0, 0xb, 0xa0, 0xd, 0x80, 0x0, 0x0, 0xd8,
    0x0, 0xf6, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x57, 0xf9, 0x58, 0xf7, 0x51, 0x0, 0x3f,
    0x20, 0x5f, 0x0, 0x0, 0x16, 0xf1, 0x18, 0xf1,
    0x10, 0xaf, 0xff, 0xff, 0xff, 0xfd, 0x2, 0x4e,
    0xf4, 0x4f, 0xe4, 0x30, 0x0, 0xb9, 0x0, 0xd8,
    0x0, 0x0, 0xd, 0x70, 0xf, 0x60, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x63, 0x0, 0x0, 0x0, 0xc, 0x70,
    0x0, 0x0, 0x0, 0xc7, 0x0, 0x0, 0x8, 0xdf,
    0xfc, 0x71, 0xc, 0xfd, 0xff, 0xdf, 0x33, 0xfd,
    0xc, 0x70, 0x20, 0x2f, 0xe2, 0xc7, 0x0, 0x0,
    0x7f, 0xff, 0xf5, 0x0, 0x0, 0x27, 0xff, 0xfe,
    0x30, 0x0, 0xc, 0x77, 0xfc, 0x6, 0x0, 0xc7,
    0x2f, 0xd4, 0xff, 0xbf, 0xfe, 0xf7, 0x5, 0xbe,
    0xff, 0xc5, 0x0, 0x0, 0xc, 0x70, 0x0, 0x0,
    0x0, 0x63, 0x0, 0x0,

    /* U+0025 "%" */
    0x8, 0xee, 0x80, 0x0, 0x1f, 0x60, 0x4, 0xe1,
    0x1e, 0x40, 0xc, 0xa0, 0x0, 0x7b, 0x0, 0xb7,
    0x7, 0xe1, 0x0, 0x5, 0xe1, 0x1e, 0x53, 0xf4,
    0x0, 0x0, 0x9, 0xff, 0x91, 0xd8, 0x7d, 0xd4,
    0x0, 0x0, 0x0, 0xac, 0x4f, 0x46, 0xf1, 0x0,
    0x0, 0x5f, 0x29, 0xa0, 0xe, 0x50, 0x0, 0x2f,
    0x60, 0x9a, 0x0, 0xd5, 0x0, 0xc, 0xa0, 0x5,
    0xe1, 0x3f, 0x10, 0x8, 0xe1, 0x0, 0x8, 0xee,
    0x50,

    /* U+0026 "&" */
    0x0, 0x3b, 0xff, 0xa1, 0x0, 0x0, 0xe, 0xe6,
    0x7f, 0x90, 0x0, 0x1, 0xfa, 0x0, 0xfa, 0x0,
    0x0, 0xb, 0xf7, 0xce, 0x30, 0x0, 0x0, 0x4f,
    0xff, 0x10, 0x0, 0x0, 0x6f, 0xbe, 0xf7, 0xa,
    0x90, 0x2f, 0xa0, 0xa, 0xfc, 0xf9, 0x5, 0xf8,
    0x0, 0xa, 0xff, 0x20, 0x1e, 0xfb, 0x8a, 0xff,
    0xfd, 0x10, 0x2a, 0xef, 0xe9, 0x25, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x2f, 0x71, 0xf6, 0x1f, 0x61, 0xf5, 0x1, 0x0,

    /* U+0028 "(" */
    0x2, 0xfa, 0xb, 0xf2, 0x1f, 0xc0, 0x5f, 0x80,
    0x8f, 0x50, 0xaf, 0x30, 0xbf, 0x20, 0xbf, 0x20,
    0xaf, 0x30, 0x8f, 0x50, 0x5f, 0x80, 0x1f, 0xc0,
    0xa, 0xf2, 0x2, 0xfa,

    /* U+0029 ")" */
    0x6f, 0x60, 0xe, 0xe0, 0x9, 0xf4, 0x4, 0xf8,
    0x1, 0xfb, 0x0, 0xfd, 0x0, 0xee, 0x0, 0xee,
    0x0, 0xfd, 0x1, 0xfb, 0x4, 0xf8, 0x9, 0xf4,
    0xe, 0xe0, 0x6f, 0x60,

    /* U+002A "*" */
    0x0, 0x7a, 0x0, 0x8b, 0x9b, 0x9b, 0x19, 0xff,
    0xc2, 0x4d, 0xff, 0xf7, 0x46, 0x7a, 0x47, 0x0,
    0x47, 0x0,

    /* U+002B "+" */
    0x0, 0x4, 0xa2, 0x0, 0x0, 0x6, 0xf3, 0x0,
    0x2, 0x28, 0xf6, 0x22, 0xf, 0xff, 0xff, 0xfe,
    0x5, 0x5b, 0xf9, 0x54, 0x0, 0x6, 0xf3, 0x0,
    0x0, 0x4, 0xd3, 0x0,

    /* U+002C "," */
    0x5, 0x30, 0x4f, 0xf0, 0x2f, 0xe0, 0xd, 0x80,
    0x2f, 0x20,

    /* U+002D "-" */
    0x18, 0x88, 0x82, 0xff, 0xff,

    /* U+002E "." */
    0x5, 0x30, 0x5f, 0xf0, 0x2e, 0xb0,

    /* U+002F "/" */
    0x0, 0x0, 0xa, 0xf0, 0x0, 0x0, 0xf, 0xa0,
    0x0, 0x0, 0x6f, 0x40, 0x0, 0x0, 0xbe, 0x0,
    0x0, 0x1, 0xf9, 0x0, 0x0, 0x7, 0xf3, 0x0,
    0x0, 0xc, 0xe0, 0x0, 0x0, 0x2f, 0x80, 0x0,
    0x0, 0x8f, 0x20, 0x0, 0x0, 0xdd, 0x0, 0x0,
    0x3, 0xf7, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0,
    0xe, 0xc0, 0x0, 0x0, 0x4f, 0x60, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x3b, 0xef, 0xb3, 0x0, 0x3, 0xff, 0xcc,
    0xff, 0x40, 0xd, 0xf5, 0x0, 0x4f, 0xe0, 0x2f,
    0xd0, 0x0, 0xb, 0xf4, 0x4f, 0xa0, 0x0, 0x9,
    0xf6, 0x4f, 0xa0, 0x0, 0x9, 0xf6, 0x2f, 0xd0,
    0x0, 0xb, 0xf4, 0xd, 0xf5, 0x0, 0x3f, 0xe0,
    0x4, 0xff, 0xcb, 0xff, 0x50, 0x0, 0x3b, 0xef,
    0xb3, 0x0,

    /* U+0031 "1" */
    0xef, 0xff, 0x48, 0x9f, 0xf4, 0x0, 0xaf, 0x40,
    0xa, 0xf4, 0x0, 0xaf, 0x40, 0xa, 0xf4, 0x0,
    0xaf, 0x40, 0xa, 0xf4, 0x0, 0xaf, 0x40, 0xa,
    0xf4,

    /* U+0032 "2" */
    0x5, 0xcf, 0xfd, 0x60, 0x8, 0xff, 0xcc, 0xff,
    0x70, 0x18, 0x10, 0x5, 0xfc, 0x0, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x0, 0xb, 0xf5, 0x0, 0x0,
    0xb, 0xf8, 0x0, 0x0, 0x1d, 0xf7, 0x0, 0x0,
    0x2e, 0xf5, 0x0, 0x0, 0x3f, 0xfe, 0xaa, 0xaa,
    0x27, 0xff, 0xff, 0xff, 0xf4,

    /* U+0033 "3" */
    0x7f, 0xff, 0xff, 0xfb, 0x4, 0x99, 0x9a, 0xff,
    0x60, 0x0, 0x1, 0xcf, 0x60, 0x0, 0x0, 0xdf,
    0xa0, 0x0, 0x0, 0x1f, 0xff, 0xe3, 0x0, 0x0,
    0x1, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0xef, 0x4,
    0x60, 0x0, 0x2f, 0xf0, 0xcf, 0xeb, 0xbf, 0xf7,
    0x2, 0x9d, 0xff, 0xc6, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0xb, 0xf5, 0x0, 0x0, 0x0, 0x8,
    0xf8, 0x0, 0x0, 0x0, 0x5, 0xfb, 0x0, 0x0,
    0x0, 0x3, 0xfd, 0x10, 0x0, 0x0, 0x1, 0xef,
    0x20, 0xaf, 0x20, 0x0, 0xcf, 0x50, 0xa, 0xf2,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0x4, 0xaa,
    0xaa, 0xaf, 0xfc, 0xa0, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0,

    /* U+0035 "5" */
    0x8, 0xff, 0xff, 0xfb, 0x0, 0xaf, 0xc9, 0x99,
    0x60, 0xc, 0xf0, 0x0, 0x0, 0x0, 0xef, 0x99,
    0x72, 0x0, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x1, 0x5f, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x42,
    0x70, 0x0, 0x1e, 0xf2, 0x9f, 0xeb, 0xbf, 0xfa,
    0x1, 0x8c, 0xff, 0xd7, 0x0,

    /* U+0036 "6" */
    0x0, 0x18, 0xdf, 0xeb, 0x30, 0x1, 0xef, 0xda,
    0xad, 0x20, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x1f,
    0xd1, 0x57, 0x50, 0x0, 0x4f, 0xef, 0xff, 0xfe,
    0x20, 0x4f, 0xf9, 0x10, 0x7f, 0xc0, 0x3f, 0xf0,
    0x0, 0xe, 0xf0, 0xe, 0xf3, 0x0, 0x1f, 0xe0,
    0x5, 0xfe, 0x99, 0xef, 0x60, 0x0, 0x4b, 0xef,
    0xc5, 0x0,

    /* U+0037 "7" */
    0x9f, 0xff, 0xff, 0xff, 0x99, 0xfd, 0xaa, 0xaf,
    0xf7, 0x9f, 0x30, 0x1, 0xff, 0x2, 0x51, 0x0,
    0x8f, 0x80, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x7, 0xf9, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0,
    0x0, 0x6f, 0xa0, 0x0, 0x0, 0xd, 0xf3, 0x0,
    0x0, 0x5, 0xfb, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x7d, 0xff, 0xc5, 0x0, 0xa, 0xfd, 0x89,
    0xef, 0x60, 0xf, 0xf0, 0x0, 0x3f, 0xc0, 0xd,
    0xf5, 0x1, 0x8f, 0x90, 0x4, 0xff, 0xff, 0xff,
    0x10, 0xe, 0xf9, 0x67, 0xbf, 0xb0, 0x5f, 0xa0,
    0x0, 0xd, 0xf1, 0x4f, 0xc0, 0x0, 0x1e, 0xf1,
    0xd, 0xfc, 0x89, 0xef, 0x90, 0x1, 0x8d, 0xff,
    0xc6, 0x0,

    /* U+0039 "9" */
    0x3, 0xbe, 0xfd, 0x70, 0x2, 0xff, 0xa9, 0xcf,
    0xb0, 0x8f, 0x60, 0x0, 0xbf, 0x49, 0xf6, 0x0,
    0xc, 0xf9, 0x3f, 0xfa, 0x8c, 0xff, 0xb0, 0x3c,
    0xfe, 0xb7, 0xfa, 0x0, 0x0, 0x0, 0x8f, 0x80,
    0x0, 0x0, 0x3f, 0xf2, 0xb, 0xca, 0xcf, 0xf6,
    0x0, 0x9e, 0xfe, 0xa3, 0x0,

    /* U+003A ":" */
    0x2e, 0xb0, 0x5f, 0xf0, 0x5, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x30, 0x5f, 0xf0, 0x2e, 0xb0,

    /* U+003B ";" */
    0x2e, 0xb0, 0x5f, 0xf0, 0x5, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x30, 0x4f, 0xf0, 0x2f, 0xe0,
    0xd, 0x80, 0x2f, 0x20,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x16, 0x0, 0x0, 0x5b, 0xfd,
    0x3, 0x9e, 0xfc, 0x61, 0xf, 0xf8, 0x20, 0x0,
    0xe, 0xfe, 0x82, 0x0, 0x0, 0x39, 0xef, 0xc6,
    0x0, 0x0, 0x5, 0xbe, 0x0, 0x0, 0x0, 0x1,

    /* U+003D "=" */
    0xf, 0xff, 0xff, 0xfe, 0x7, 0x77, 0x77, 0x76,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x22,
    0xf, 0xff, 0xff, 0xfe, 0x5, 0x55, 0x55, 0x54,

    /* U+003E ">" */
    0x6, 0x10, 0x0, 0x0, 0xf, 0xfa, 0x40, 0x0,
    0x1, 0x7d, 0xfe, 0x82, 0x0, 0x0, 0x39, 0xff,
    0x0, 0x3, 0x9e, 0xfc, 0x7, 0xdf, 0xe8, 0x20,
    0xf, 0xa4, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x6, 0xcf, 0xfd, 0x70, 0x9f, 0xea, 0xaf, 0xf8,
    0x17, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x7, 0xf8,
    0x0, 0x0, 0x6f, 0xc0, 0x0, 0x5, 0xfb, 0x0,
    0x0, 0x5, 0x92, 0x0, 0x0, 0x1, 0x30, 0x0,
    0x0, 0xc, 0xf5, 0x0, 0x0, 0x9, 0xe3, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x5a, 0xef, 0xfd, 0x92, 0x0, 0x0,
    0x1, 0xcf, 0x95, 0x34, 0x6b, 0xf8, 0x0, 0x1,
    0xdc, 0x10, 0x0, 0x0, 0x14, 0xe9, 0x0, 0x9e,
    0x10, 0x9f, 0xfe, 0x7f, 0x83, 0xf3, 0xf, 0x60,
    0xaf, 0xa4, 0x6f, 0xf8, 0xb, 0x94, 0xf2, 0x1f,
    0xb0, 0x0, 0x5f, 0x80, 0x7c, 0x5f, 0x13, 0xf7,
    0x0, 0x1, 0xf8, 0x7, 0xd3, 0xf2, 0x1f, 0xc0,
    0x0, 0x6f, 0x80, 0x9b, 0xf, 0x60, 0x9f, 0xb6,
    0x9f, 0xfd, 0x7f, 0x50, 0x9e, 0x10, 0x7e, 0xfd,
    0x56, 0xee, 0x80, 0x1, 0xdc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0x95, 0x44, 0x7a,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xef, 0xec, 0x70,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0xaf,
    0x30, 0x0, 0x0, 0x7, 0xf6, 0x3, 0xfb, 0x0,
    0x0, 0x0, 0xee, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0, 0xe, 0xf9,
    0x88, 0x88, 0xff, 0x20, 0x5, 0xf9, 0x0, 0x0,
    0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0, 0x0, 0xe,
    0xf1,

    /* U+0042 "B" */
    0x9f, 0xff, 0xff, 0xea, 0x20, 0x9f, 0xb8, 0x88,
    0xcf, 0xe0, 0x9f, 0x50, 0x0, 0xd, 0xf2, 0x9f,
    0x50, 0x0, 0x4f, 0xd0, 0x9f, 0xff, 0xff, 0xff,
    0x50, 0x9f, 0xc8, 0x88, 0xaf, 0xf4, 0x9f, 0x50,
    0x0, 0x5, 0xfa, 0x9f, 0x50, 0x0, 0x5, 0xfa,
    0x9f, 0xb8, 0x88, 0xaf, 0xf5, 0x9f, 0xff, 0xff,
    0xfc, 0x50,

    /* U+0043 "C" */
    0x0, 0x5, 0xbe, 0xfd, 0x91, 0x0, 0xb, 0xff,
    0xdb, 0xdf, 0xf2, 0x9, 0xfc, 0x20, 0x0, 0x48,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xc2, 0x0, 0x4, 0x80, 0x0, 0xcf, 0xfd, 0xbd,
    0xff, 0x20, 0x0, 0x6c, 0xff, 0xd9, 0x10,

    /* U+0044 "D" */
    0x9f, 0xff, 0xff, 0xd8, 0x10, 0x9, 0xfc, 0xaa,
    0xae, 0xff, 0x40, 0x9f, 0x50, 0x0, 0x6, 0xff,
    0x19, 0xf5, 0x0, 0x0, 0x9, 0xf7, 0x9f, 0x50,
    0x0, 0x0, 0x4f, 0xa9, 0xf5, 0x0, 0x0, 0x4,
    0xfa, 0x9f, 0x50, 0x0, 0x0, 0x9f, 0x79, 0xf5,
    0x0, 0x0, 0x6f, 0xf1, 0x9f, 0xca, 0xaa, 0xef,
    0xf4, 0x9, 0xff, 0xff, 0xfd, 0x81, 0x0,

    /* U+0045 "E" */
    0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb, 0x99, 0x99,
    0x90, 0x9f, 0x50, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0x9, 0xfd,
    0x99, 0x99, 0x40, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9, 0x99, 0x99,
    0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+0046 "F" */
    0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb, 0x99, 0x99,
    0x90, 0x9f, 0x50, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x9f, 0xea, 0xaa, 0xa4, 0x9, 0xff,
    0xff, 0xff, 0x70, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9, 0xf5, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x5, 0xbe, 0xfe, 0xa2, 0x0, 0xb, 0xff,
    0xdb, 0xdf, 0xf4, 0x9, 0xfc, 0x20, 0x0, 0x38,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x0, 0x1, 0x4, 0xfa, 0x0, 0x0, 0x6,
    0xf6, 0x1f, 0xf1, 0x0, 0x0, 0x6f, 0x60, 0xaf,
    0xc2, 0x0, 0x9, 0xf6, 0x0, 0xbf, 0xfd, 0xbd,
    0xff, 0x70, 0x0, 0x6c, 0xef, 0xea, 0x30,

    /* U+0048 "H" */
    0x9f, 0x50, 0x0, 0x3, 0xfb, 0x9f, 0x50, 0x0,
    0x3, 0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb, 0x9f,
    0x50, 0x0, 0x3, 0xfb, 0x9f, 0xff, 0xff, 0xff,
    0xfb, 0x9f, 0xea, 0xaa, 0xac, 0xfb, 0x9f, 0x50,
    0x0, 0x3, 0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb,
    0x9f, 0x50, 0x0, 0x3, 0xfb, 0x9f, 0x50, 0x0,
    0x3, 0xfb,

    /* U+0049 "I" */
    0x9f, 0x59, 0xf5, 0x9f, 0x59, 0xf5, 0x9f, 0x59,
    0xf5, 0x9f, 0x59, 0xf5, 0x9f, 0x59, 0xf5,

    /* U+004A "J" */
    0x1, 0xff, 0xff, 0xf9, 0x0, 0x99, 0x9b, 0xf9,
    0x0, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0x5, 0xf8, 0x4, 0x30, 0x9, 0xf6,
    0xe, 0xfb, 0xbf, 0xf1, 0x3, 0xbf, 0xfc, 0x30,

    /* U+004B "K" */
    0x9f, 0x50, 0x0, 0x1d, 0xf4, 0x9, 0xf5, 0x0,
    0x1d, 0xf4, 0x0, 0x9f, 0x50, 0x1d, 0xf4, 0x0,
    0x9, 0xf5, 0x1d, 0xf4, 0x0, 0x0, 0x9f, 0x7e,
    0xfa, 0x0, 0x0, 0x9, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x9f, 0xf5, 0x5f, 0xe2, 0x0, 0x9, 0xf9,
    0x0, 0x7f, 0xd0, 0x0, 0x9f, 0x50, 0x0, 0x9f,
    0xb0, 0x9, 0xf5, 0x0, 0x0, 0xbf, 0x90,

    /* U+004C "L" */
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0xca, 0xaa, 0xa8, 0x9f, 0xff, 0xff, 0xfd,

    /* U+004D "M" */
    0x9f, 0x50, 0x0, 0x0, 0x1, 0xee, 0x9f, 0xe1,
    0x0, 0x0, 0xa, 0xfe, 0x9f, 0xf9, 0x0, 0x0,
    0x4f, 0xfe, 0x9f, 0xdf, 0x40, 0x0, 0xdc, 0xfe,
    0x9f, 0x5e, 0xd0, 0x8, 0xf3, 0xfe, 0x9f, 0x45,
    0xf8, 0x2f, 0x90, 0xfe, 0x9f, 0x40, 0xbf, 0xfe,
    0x10, 0xfe, 0x9f, 0x40, 0x2f, 0xf6, 0x0, 0xfe,
    0x9f, 0x40, 0x6, 0xa0, 0x0, 0xfe, 0x9f, 0x40,
    0x0, 0x0, 0x0, 0xfe,

    /* U+004E "N" */
    0x9f, 0x60, 0x0, 0x3, 0xfb, 0x9f, 0xf4, 0x0,
    0x3, 0xfb, 0x9f, 0xfe, 0x20, 0x3, 0xfb, 0x9f,
    0x9f, 0xd0, 0x3, 0xfb, 0x9f, 0x56, 0xfb, 0x3,
    0xfb, 0x9f, 0x50, 0x9f, 0x83, 0xfb, 0x9f, 0x50,
    0xb, 0xf8, 0xfb, 0x9f, 0x50, 0x1, 0xdf, 0xfb,
    0x9f, 0x50, 0x0, 0x2f, 0xfb, 0x9f, 0x50, 0x0,
    0x4, 0xfb,

    /* U+004F "O" */
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+0050 "P" */
    0x9f, 0xff, 0xfe, 0xc5, 0x0, 0x9f, 0xfa, 0xab,
    0xff, 0x70, 0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f,
    0x50, 0x0, 0xd, 0xf2, 0x9f, 0x50, 0x0, 0x2f,
    0xf0, 0x9f, 0xfa, 0xab, 0xff, 0x80, 0x9f, 0xff,
    0xff, 0xc5, 0x0, 0x9f, 0x50, 0x0, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xb,
    0xff, 0xdb, 0xef, 0xf6, 0x0, 0x9, 0xfc, 0x20,
    0x0, 0x5f, 0xf3, 0x1, 0xff, 0x10, 0x0, 0x0,
    0x7f, 0xb0, 0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe,
    0x4, 0xfa, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x1f,
    0xf1, 0x0, 0x0, 0x6, 0xfb, 0x0, 0x9f, 0xc2,
    0x0, 0x5, 0xff, 0x40, 0x0, 0xbf, 0xfd, 0xbe,
    0xff, 0x60, 0x0, 0x0, 0x5b, 0xff, 0xfa, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xb3, 0x4d, 0x20,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x30, 0x0,

    /* U+0052 "R" */
    0x9f, 0xff, 0xfe, 0xc5, 0x0, 0x9f, 0xfa, 0xab,
    0xff, 0x70, 0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f,
    0x50, 0x0, 0xd, 0xf2, 0x9f, 0x50, 0x0, 0x2f,
    0xf0, 0x9f, 0xe9, 0x9a, 0xff, 0x80, 0x9f, 0xff,
    0xff, 0xf6, 0x0, 0x9f, 0x50, 0x8, 0xf9, 0x0,
    0x9f, 0x50, 0x0, 0xcf, 0x40, 0x9f, 0x50, 0x0,
    0x2f, 0xe1,

    /* U+0053 "S" */
    0x0, 0x8d, 0xfe, 0xc7, 0x10, 0xcf, 0xda, 0xad,
    0xf3, 0x3f, 0xd0, 0x0, 0x2, 0x2, 0xfe, 0x20,
    0x0, 0x0, 0x7, 0xff, 0xd9, 0x50, 0x0, 0x2,
    0x7b, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x60, 0x0, 0x2, 0xfd, 0x4f, 0xfb, 0x9a, 0xef,
    0x70, 0x5b, 0xef, 0xfc, 0x50,

    /* U+0054 "T" */
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xaa, 0xcf, 0xca,
    0xa9, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0, 0x7f,
    0x70, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0x70, 0x0,

    /* U+0055 "U" */
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0,
    0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf,
    0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xaf, 0x40, 0x0, 0x6, 0xf8, 0x9f, 0x60,
    0x0, 0x9, 0xf6, 0x5f, 0xd1, 0x0, 0x2f, 0xf2,
    0xc, 0xff, 0xcc, 0xff, 0x90, 0x0, 0x7d, 0xff,
    0xc6, 0x0,

    /* U+0056 "V" */
    0xd, 0xf4, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x6f,
    0xb0, 0x0, 0x0, 0xaf, 0x50, 0x0, 0xef, 0x20,
    0x0, 0x1f, 0xd0, 0x0, 0x7, 0xfa, 0x0, 0x8,
    0xf6, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0xfe, 0x0,
    0x0, 0x0, 0x8f, 0x80, 0x7f, 0x70, 0x0, 0x0,
    0x1, 0xff, 0x1e, 0xf1, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0,
    0x0,

    /* U+0057 "W" */
    0x6f, 0x90, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x4f,
    0x91, 0xfe, 0x0, 0x0, 0xcf, 0xf1, 0x0, 0xa,
    0xf3, 0xb, 0xf4, 0x0, 0x2f, 0xff, 0x60, 0x0,
    0xfe, 0x0, 0x6f, 0xa0, 0x7, 0xf5, 0xfc, 0x0,
    0x5f, 0x80, 0x0, 0xff, 0x0, 0xde, 0xb, 0xf1,
    0xb, 0xf3, 0x0, 0xa, 0xf5, 0x3f, 0x90, 0x6f,
    0x71, 0xfd, 0x0, 0x0, 0x5f, 0xa9, 0xf3, 0x0,
    0xfc, 0x6f, 0x70, 0x0, 0x0, 0xff, 0xfd, 0x0,
    0xa, 0xfe, 0xf2, 0x0, 0x0, 0xa, 0xff, 0x80,
    0x0, 0x5f, 0xfc, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x0, 0x0, 0xff, 0x70, 0x0,

    /* U+0058 "X" */
    0x4f, 0xd0, 0x0, 0x7, 0xf9, 0x0, 0x8f, 0xa0,
    0x3, 0xfd, 0x0, 0x0, 0xcf, 0x61, 0xef, 0x20,
    0x0, 0x1, 0xef, 0xef, 0x50, 0x0, 0x0, 0x4,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x6f, 0xfd, 0x0,
    0x0, 0x0, 0x3f, 0xea, 0xf9, 0x0, 0x0, 0x1e,
    0xf3, 0xd, 0xf5, 0x0, 0xb, 0xf7, 0x0, 0x2f,
    0xf2, 0x7, 0xfb, 0x0, 0x0, 0x5f, 0xd0,

    /* U+0059 "Y" */
    0xc, 0xf4, 0x0, 0x0, 0x3f, 0xb0, 0x3, 0xfd,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x8f, 0x80, 0x7,
    0xf7, 0x0, 0x0, 0xe, 0xf2, 0x2f, 0xc0, 0x0,
    0x0, 0x4, 0xfc, 0xcf, 0x30, 0x0, 0x0, 0x0,
    0xaf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0,

    /* U+005A "Z" */
    0x4f, 0xff, 0xff, 0xff, 0xf6, 0x2a, 0xaa, 0xaa,
    0xcf, 0xf2, 0x0, 0x0, 0x1, 0xef, 0x50, 0x0,
    0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0xbf, 0x90,
    0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x7f,
    0xc0, 0x0, 0x0, 0x5, 0xfe, 0x10, 0x0, 0x0,
    0x3f, 0xfc, 0xaa, 0xaa, 0xa5, 0x6f, 0xff, 0xff,
    0xff, 0xf9,

    /* U+005B "[" */
    0x9f, 0xff, 0x9f, 0xa7, 0x9f, 0x40, 0x9f, 0x40,
    0x9f, 0x40, 0x9f, 0x40, 0x9f, 0x40, 0x9f, 0x40,
    0x9f, 0x40, 0x9f, 0x40, 0x9f, 0x40, 0x9f, 0x40,
    0x9f, 0xa7, 0x9f, 0xff,

    /* U+005C "\\" */
    0x6f, 0x40, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x5, 0xf5, 0x0, 0x0,
    0x0, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0x0, 0x4f, 0x60, 0x0, 0x0, 0xe, 0xb0, 0x0,
    0x0, 0x9, 0xf1, 0x0, 0x0, 0x3, 0xf7, 0x0,
    0x0, 0x0, 0xec, 0x0, 0x0, 0x0, 0x8f, 0x20,
    0x0, 0x0, 0x3f, 0x70, 0x0, 0x0, 0xd, 0xd0,

    /* U+005D "]" */
    0xcf, 0xfd, 0x58, 0xfd, 0x0, 0xfd, 0x0, 0xfd,
    0x0, 0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0, 0xfd,
    0x0, 0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0, 0xfd,
    0x58, 0xfd, 0xcf, 0xfd,

    /* U+005E "^" */
    0x0, 0x8, 0xf6, 0x0, 0x0, 0xf, 0xed, 0x0,
    0x0, 0x7e, 0x2f, 0x50, 0x0, 0xe8, 0xa, 0xc0,
    0x5, 0xf1, 0x3, 0xf3, 0xc, 0xa0, 0x0, 0xca,

    /* U+005F "_" */
    0xff, 0xff, 0xff, 0xf7, 0x22, 0x22, 0x22, 0x21,

    /* U+0060 "`" */
    0x1b, 0xf4, 0x0, 0x8, 0xf4,

    /* U+0061 "a" */
    0x4, 0xbe, 0xfd, 0x60, 0xc, 0xc9, 0x9e, 0xf7,
    0x0, 0x0, 0x2, 0xfc, 0x5, 0xdf, 0xff, 0xfe,
    0x2f, 0xd4, 0x33, 0xff, 0x5f, 0x80, 0x1, 0xff,
    0x1f, 0xe5, 0x5c, 0xff, 0x4, 0xdf, 0xe8, 0xdf,

    /* U+0062 "b" */
    0xcf, 0x10, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0x0, 0xc, 0xf6, 0xcf,
    0xe9, 0x10, 0xcf, 0xfc, 0x9c, 0xfd, 0xc, 0xfa,
    0x0, 0xa, 0xf7, 0xcf, 0x20, 0x0, 0x3f, 0xac,
    0xf2, 0x0, 0x3, 0xfa, 0xcf, 0xa0, 0x0, 0xbf,
    0x7c, 0xff, 0xc9, 0xdf, 0xd0, 0xcf, 0x4c, 0xfe,
    0x91, 0x0,

    /* U+0063 "c" */
    0x0, 0x5c, 0xfe, 0xb2, 0x0, 0x8f, 0xe9, 0xaf,
    0xf1, 0x2f, 0xd1, 0x0, 0x34, 0x6, 0xf8, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x2, 0xfe,
    0x10, 0x4, 0x50, 0x7, 0xfe, 0xab, 0xff, 0x10,
    0x5, 0xcf, 0xfb, 0x20,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x0,
    0xc, 0xf1, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0x6d, 0xfd, 0x7c, 0xf1, 0x9, 0xfe, 0x9a, 0xff,
    0xf1, 0x2f, 0xe1, 0x0, 0x4f, 0xf1, 0x6f, 0x80,
    0x0, 0xf, 0xf1, 0x6f, 0x80, 0x0, 0xf, 0xf1,
    0x2f, 0xe2, 0x0, 0x5f, 0xf1, 0x9, 0xfe, 0xab,
    0xff, 0xf1, 0x0, 0x6d, 0xfe, 0x7b, 0xf1,

    /* U+0065 "e" */
    0x0, 0x6d, 0xfe, 0xa1, 0x0, 0x9f, 0xc8, 0xaf,
    0xe1, 0x2f, 0xc0, 0x0, 0x4f, 0x86, 0xff, 0xff,
    0xff, 0xfb, 0x6f, 0xa3, 0x33, 0x33, 0x22, 0xfd,
    0x10, 0x1, 0x30, 0x8, 0xfe, 0xaa, 0xee, 0x0,
    0x5, 0xcf, 0xfc, 0x40,

    /* U+0066 "f" */
    0x0, 0x6d, 0xfb, 0x0, 0x3f, 0xd8, 0x70, 0x7,
    0xf5, 0x0, 0xd, 0xff, 0xff, 0x80, 0x6f, 0xfd,
    0x73, 0x0, 0x8f, 0x50, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x8f, 0x50, 0x0, 0x8, 0xf5, 0x0, 0x0,
    0x8f, 0x50, 0x0, 0x8, 0xf5, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x6d, 0xfe, 0x8a, 0xf3, 0x9, 0xfd, 0x89,
    0xef, 0xf3, 0x2f, 0xd0, 0x0, 0x2f, 0xf3, 0x6f,
    0x80, 0x0, 0xf, 0xf3, 0x6f, 0x80, 0x0, 0xf,
    0xf3, 0x2f, 0xe2, 0x0, 0x3f, 0xf3, 0x9, 0xfe,
    0x9a, 0xff, 0xf2, 0x0, 0x6d, 0xfe, 0x8c, 0xf2,
    0x1, 0x0, 0x0, 0x2f, 0xe0, 0xc, 0xfb, 0x9a,
    0xff, 0x70, 0x3, 0xae, 0xff, 0xc5, 0x0,

    /* U+0068 "h" */
    0xcf, 0x10, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0x0, 0xc, 0xf6, 0xcf,
    0xe9, 0x0, 0xcf, 0xfc, 0xae, 0xfa, 0xc, 0xf8,
    0x0, 0x1f, 0xf0, 0xcf, 0x20, 0x0, 0xcf, 0x1c,
    0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0, 0xcf,
    0x2c, 0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0,
    0xcf, 0x20,

    /* U+0069 "i" */
    0xb, 0xe2, 0xe, 0xf3, 0x1, 0x20, 0xc, 0xf1,
    0xc, 0xf1, 0xc, 0xf1, 0xc, 0xf1, 0xc, 0xf1,
    0xc, 0xf1, 0xc, 0xf1, 0xc, 0xf1,

    /* U+006A "j" */
    0x0, 0xa, 0xe3, 0x0, 0xd, 0xf5, 0x0, 0x0,
    0x20, 0x0, 0xb, 0xf3, 0x0, 0xb, 0xf3, 0x0,
    0xb, 0xf3, 0x0, 0xb, 0xf3, 0x0, 0xb, 0xf3,
    0x0, 0xb, 0xf3, 0x0, 0xb, 0xf3, 0x0, 0xb,
    0xf3, 0x0, 0xb, 0xf2, 0xa, 0x9f, 0xd0, 0x3e,
    0xfc, 0x30,

    /* U+006B "k" */
    0xcf, 0x10, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0x8f, 0xb0, 0xcf, 0x10, 0x9f, 0xb0, 0xc, 0xf2,
    0xbf, 0xb0, 0x0, 0xcf, 0xdf, 0xf2, 0x0, 0xc,
    0xff, 0xff, 0xc0, 0x0, 0xcf, 0xa0, 0xbf, 0x90,
    0xc, 0xf1, 0x1, 0xdf, 0x60, 0xcf, 0x10, 0x2,
    0xff, 0x30,

    /* U+006C "l" */
    0xcf, 0x1c, 0xf1, 0xcf, 0x1c, 0xf1, 0xcf, 0x1c,
    0xf1, 0xcf, 0x1c, 0xf1, 0xcf, 0x1c, 0xf1, 0xcf,
    0x10,

    /* U+006D "m" */
    0xcf, 0x5c, 0xfd, 0x70, 0x8e, 0xfc, 0x40, 0xcf,
    0xfb, 0xaf, 0xff, 0xfa, 0xbf, 0xf2, 0xcf, 0x80,
    0x4, 0xff, 0x40, 0x8, 0xf8, 0xcf, 0x20, 0x0,
    0xfe, 0x0, 0x4, 0xf9, 0xcf, 0x10, 0x0, 0xfd,
    0x0, 0x4, 0xfa, 0xcf, 0x10, 0x0, 0xfd, 0x0,
    0x4, 0xfa, 0xcf, 0x10, 0x0, 0xfd, 0x0, 0x4,
    0xfa, 0xcf, 0x10, 0x0, 0xfd, 0x0, 0x4, 0xfa,

    /* U+006E "n" */
    0xcf, 0x4c, 0xfe, 0x90, 0xc, 0xff, 0xca, 0xef,
    0xa0, 0xcf, 0x80, 0x1, 0xff, 0xc, 0xf2, 0x0,
    0xc, 0xf1, 0xcf, 0x10, 0x0, 0xcf, 0x2c, 0xf1,
    0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0, 0xcf, 0x2c,
    0xf1, 0x0, 0xc, 0xf2,

    /* U+006F "o" */
    0x0, 0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a,
    0xff, 0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f,
    0x80, 0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd,
    0xf0, 0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe,
    0xab, 0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+0070 "p" */
    0xcf, 0x4c, 0xfe, 0x91, 0xc, 0xff, 0xc9, 0xcf,
    0xd0, 0xcf, 0x90, 0x0, 0xaf, 0x7c, 0xf2, 0x0,
    0x3, 0xfa, 0xcf, 0x20, 0x0, 0x3f, 0xac, 0xfa,
    0x0, 0xb, 0xf7, 0xcf, 0xfc, 0x9d, 0xfd, 0xc,
    0xf6, 0xcf, 0xe9, 0x10, 0xcf, 0x10, 0x0, 0x0,
    0xc, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0,
    0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x6d, 0xfe, 0x7b, 0xf1, 0x9, 0xfe, 0x9a,
    0xff, 0xf1, 0x2f, 0xd1, 0x0, 0x4f, 0xf1, 0x6f,
    0x80, 0x0, 0xf, 0xf1, 0x6f, 0x80, 0x0, 0xf,
    0xf1, 0x2f, 0xe1, 0x0, 0x5f, 0xf1, 0x9, 0xfe,
    0xab, 0xff, 0xf1, 0x0, 0x6d, 0xfd, 0x7d, 0xf1,
    0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0xd, 0xf1,

    /* U+0072 "r" */
    0xcf, 0x4c, 0xdc, 0xff, 0xd9, 0xcf, 0x90, 0xc,
    0xf3, 0x0, 0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf,
    0x10, 0xc, 0xf1, 0x0,

    /* U+0073 "s" */
    0x4, 0xcf, 0xfd, 0x90, 0x3f, 0xe8, 0x8b, 0xa0,
    0x6f, 0x80, 0x0, 0x0, 0x2f, 0xfd, 0x96, 0x10,
    0x2, 0x7b, 0xef, 0xe1, 0x1, 0x0, 0x9, 0xf4,
    0x6f, 0xb9, 0x9f, 0xf1, 0x3a, 0xef, 0xfb, 0x30,

    /* U+0074 "t" */
    0x8, 0xf5, 0x0, 0x0, 0x8f, 0x50, 0x0, 0xdf,
    0xff, 0xf8, 0x6, 0xff, 0xd7, 0x30, 0x8, 0xf5,
    0x0, 0x0, 0x8f, 0x50, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x7f, 0x60, 0x0, 0x4, 0xfe, 0x98, 0x0,
    0x7, 0xef, 0xb0,

    /* U+0075 "u" */
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x10, 0x0, 0xff, 0xbf, 0x50, 0x5, 0xff,
    0x5f, 0xfb, 0xbf, 0xff, 0x6, 0xdf, 0xd7, 0xcf,

    /* U+0076 "v" */
    0xd, 0xf1, 0x0, 0x4, 0xf8, 0x6, 0xf8, 0x0,
    0xb, 0xf1, 0x0, 0xfe, 0x0, 0x2f, 0xa0, 0x0,
    0x9f, 0x50, 0x9f, 0x30, 0x0, 0x2f, 0xc1, 0xfc,
    0x0, 0x0, 0xb, 0xfa, 0xf5, 0x0, 0x0, 0x4,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0xdf, 0x80, 0x0,

    /* U+0077 "w" */
    0xcf, 0x0, 0x1, 0xfe, 0x0, 0x2, 0xf8, 0x6f,
    0x50, 0x6, 0xff, 0x40, 0x7, 0xf2, 0xf, 0xb0,
    0xc, 0xff, 0xa0, 0xd, 0xc0, 0xa, 0xf1, 0x2f,
    0x8b, 0xf0, 0x3f, 0x60, 0x4, 0xf6, 0x8f, 0x25,
    0xf5, 0x9f, 0x10, 0x0, 0xec, 0xec, 0x0, 0xec,
    0xeb, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x9f, 0xf5,
    0x0, 0x0, 0x2f, 0xf0, 0x0, 0x3f, 0xf0, 0x0,

    /* U+0078 "x" */
    0x5f, 0xb0, 0x2, 0xfd, 0x10, 0x9f, 0x70, 0xcf,
    0x30, 0x0, 0xcf, 0xdf, 0x60, 0x0, 0x2, 0xff,
    0xb0, 0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0x1d,
    0xfa, 0xf8, 0x0, 0xb, 0xf5, 0xc, 0xf4, 0x7,
    0xf9, 0x0, 0x1e, 0xe1,

    /* U+0079 "y" */
    0xd, 0xf1, 0x0, 0x4, 0xf8, 0x6, 0xf8, 0x0,
    0xb, 0xf1, 0x0, 0xee, 0x0, 0x2f, 0xa0, 0x0,
    0x8f, 0x50, 0x9f, 0x30, 0x0, 0x1f, 0xc0, 0xfc,
    0x0, 0x0, 0xa, 0xfa, 0xf5, 0x0, 0x0, 0x3,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0xff, 0x70, 0x0,
    0x0, 0x0, 0xdf, 0x10, 0x0, 0xc, 0xac, 0xf8,
    0x0, 0x0, 0x1a, 0xee, 0x90, 0x0, 0x0,

    /* U+007A "z" */
    0x5f, 0xff, 0xff, 0xf5, 0x27, 0x77, 0x9f, 0xe2,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0xa, 0xf6, 0x0,
    0x0, 0x8f, 0x90, 0x0, 0x5, 0xfc, 0x0, 0x0,
    0x3f, 0xf9, 0x77, 0x73, 0x7f, 0xff, 0xff, 0xf7,

    /* U+007B "{" */
    0x0, 0x3c, 0xf4, 0x0, 0xcf, 0xa2, 0x0, 0xfe,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0xfd, 0x0, 0x19, 0xfb, 0x0, 0x2f, 0xf6, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0xfe, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0xdf, 0x92, 0x0,
    0x3d, 0xf4,

    /* U+007C "|" */
    0x9f, 0x29, 0xf2, 0x9f, 0x29, 0xf2, 0x9f, 0x29,
    0xf2, 0x9f, 0x29, 0xf2, 0x9f, 0x29, 0xf2, 0x9f,
    0x29, 0xf2, 0x9f, 0x29, 0xf2,

    /* U+007D "}" */
    0xce, 0x90, 0x6, 0xdf, 0x50, 0x5, 0xf8, 0x0,
    0x5f, 0x90, 0x5, 0xf9, 0x0, 0x4f, 0x90, 0x2,
    0xfe, 0x60, 0xd, 0xfb, 0x4, 0xfa, 0x0, 0x5f,
    0x90, 0x5, 0xf9, 0x0, 0x5f, 0x80, 0x6d, 0xf6,
    0xc, 0xea, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x7f, 0xfa, 0x37,
    0xf0, 0x1f, 0x86, 0xef, 0xf8, 0x0, 0x40, 0x0,
    0x32, 0x0,

    /* U+00A0 " " */

    /* U+00A1 "¡" */
    0xc, 0xe1, 0xf, 0xf2, 0x1, 0x20, 0x8, 0xa0,
    0xb, 0xe0, 0xc, 0xf0, 0xd, 0xf0, 0xe, 0xf1,
    0xf, 0xf1, 0xf, 0xf2,

    /* U+00A2 "¢" */
    0x0, 0x0, 0xd6, 0x0, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x0, 0x5c, 0xff, 0xb2, 0x0, 0x8f, 0xef,
    0xff, 0xf1, 0x2f, 0xd1, 0xd6, 0x34, 0x6, 0xf8,
    0xd, 0x60, 0x0, 0x5f, 0x80, 0xd6, 0x0, 0x2,
    0xfe, 0x1d, 0x64, 0x50, 0x7, 0xfe, 0xff, 0xff,
    0x10, 0x5, 0xcf, 0xfb, 0x20, 0x0, 0x0, 0xd6,
    0x0, 0x0, 0x0, 0x6, 0x30, 0x0,

    /* U+00A3 "£" */
    0x0, 0x2, 0x9e, 0xfe, 0xb4, 0x0, 0x2f, 0xfd,
    0xbc, 0xf4, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xfc,
    0x0, 0x13, 0xff, 0x33, 0x32, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x36, 0xff, 0x66, 0x66, 0x62, 0x8f, 0xff, 0xff,
    0xff, 0xf6,

    /* U+00A4 "¤" */
    0x9, 0x10, 0x0, 0x0, 0x65, 0x4, 0xfd, 0x9e,
    0xfc, 0x9f, 0xb0, 0x5, 0xff, 0xa8, 0xdf, 0xf0,
    0x0, 0x6f, 0x40, 0x0, 0xce, 0x0, 0xb, 0xd0,
    0x0, 0x4, 0xf3, 0x0, 0xbd, 0x0, 0x0, 0x4f,
    0x30, 0x6, 0xf5, 0x0, 0xc, 0xe0, 0x0, 0x4f,
    0xfa, 0x8d, 0xfe, 0x0, 0x3f, 0xe9, 0xef, 0xca,
    0xfb, 0x1, 0xb1, 0x0, 0x0, 0x7, 0x60,

    /* U+00A5 "¥" */
    0xb, 0xf5, 0x0, 0x0, 0x6, 0xf8, 0x1, 0xef,
    0x20, 0x0, 0x3f, 0xc0, 0x0, 0x3f, 0xd1, 0x1,
    0xee, 0x10, 0x0, 0x6, 0xfb, 0xc, 0xf3, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x60, 0x0, 0x0, 0x8e,
    0xff, 0xff, 0xee, 0x50, 0x0, 0x1, 0x1b, 0xf8,
    0x11, 0x0, 0x0, 0x8e, 0xef, 0xff, 0xee, 0x50,
    0x0, 0x1, 0x1a, 0xf8, 0x11, 0x0, 0x0, 0x0,
    0x9, 0xf6, 0x0, 0x0,

    /* U+00A6 "¦" */
    0x9f, 0x29, 0xf2, 0x9f, 0x29, 0xf2, 0x9f, 0x21,
    0x30, 0x0, 0x0, 0x0, 0x13, 0x9, 0xf2, 0x9f,
    0x29, 0xf2, 0x9f, 0x29, 0xf2,

    /* U+00A7 "§" */
    0x1, 0xae, 0xfd, 0x91, 0xd, 0xe8, 0x79, 0xe0,
    0x2f, 0x80, 0x0, 0x0, 0xe, 0xf9, 0x40, 0x0,
    0xe, 0xff, 0xfe, 0x50, 0x6f, 0x40, 0x4d, 0xf1,
    0x6f, 0x60, 0x8, 0xf2, 0x1e, 0xfd, 0xef, 0xb0,
    0x0, 0x6a, 0xff, 0x70, 0x0, 0x0, 0xe, 0xd0,
    0x4b, 0x52, 0x5f, 0xb0, 0x5e, 0xff, 0xfd, 0x20,
    0x0, 0x34, 0x30, 0x0,

    /* U+00A8 "¨" */
    0x78, 0x8, 0x7a, 0xb0, 0xba,

    /* U+00A9 "©" */
    0x0, 0x4, 0xac, 0xda, 0x40, 0x0, 0x0, 0x9a,
    0x20, 0x3, 0xb8, 0x0, 0x7, 0xa0, 0x0, 0x0,
    0xa, 0x50, 0xd, 0x11, 0xaf, 0xfb, 0x11, 0xd0,
    0x3a, 0xc, 0xe5, 0x4a, 0x30, 0xb2, 0x58, 0xf,
    0x70, 0x0, 0x0, 0xa3, 0x3a, 0xd, 0xc2, 0x17,
    0x20, 0xb2, 0xd, 0x3, 0xdf, 0xfe, 0x21, 0xd0,
    0x7, 0x90, 0x2, 0x20, 0xb, 0x50, 0x0, 0x9a,
    0x20, 0x3, 0xb7, 0x0, 0x0, 0x4, 0xbd, 0xda,
    0x30, 0x0,

    /* U+00AA "ª" */
    0x1b, 0xfe, 0x70, 0x4, 0x4, 0xf2, 0x1b, 0xcd,
    0xf3, 0x7d, 0x3, 0xf3, 0x1b, 0xda, 0xe3,

    /* U+00AB "«" */
    0x0, 0x5b, 0x24, 0xb3, 0x2, 0xf8, 0x1e, 0xa0,
    0xd, 0xc0, 0xce, 0x10, 0x2f, 0xa1, 0xec, 0x0,
    0x5, 0xf5, 0x4f, 0x70, 0x0, 0x9f, 0x27, 0xf3,

    /* U+00AC "¬" */
    0x2, 0x22, 0x22, 0x22, 0xf, 0xff, 0xff, 0xfe,
    0x5, 0x55, 0x55, 0xce, 0x0, 0x0, 0x0, 0xbe,
    0x0, 0x0, 0x0, 0x9b,

    /* U+00AD "­" */
    0x18, 0x88, 0x82, 0xff, 0xff,

    /* U+00AE "®" */
    0x0, 0x4, 0xac, 0xda, 0x40, 0x0, 0x0, 0x9a,
    0x20, 0x3, 0xb8, 0x0, 0x7, 0xa0, 0x0, 0x0,
    0xa, 0x50, 0xd, 0x14, 0xff, 0xfb, 0x11, 0xd0,
    0x3a, 0x4, 0xf0, 0x1c, 0x90, 0xb2, 0x58, 0x4,
    0xf0, 0x1c, 0x90, 0xa3, 0x3a, 0x4, 0xff, 0xff,
    0x10, 0xb2, 0xd, 0x4, 0xf0, 0x4e, 0x11, 0xd0,
    0x7, 0x92, 0xa0, 0x7, 0x6b, 0x50, 0x0, 0x9a,
    0x20, 0x3, 0xb7, 0x0, 0x0, 0x4, 0xbd, 0xda,
    0x30, 0x0,

    /* U+00AF "¯" */
    0x1f, 0xff, 0xff, 0x10, 0x22, 0x22, 0x20,

    /* U+00B0 "°" */
    0x5, 0xde, 0x80, 0x2e, 0x31, 0xc6, 0x6b, 0x0,
    0x6a, 0x2e, 0x31, 0xc6, 0x5, 0xee, 0x80,

    /* U+00B1 "±" */
    0x0, 0x3, 0x81, 0x0, 0x0, 0x6, 0xf3, 0x0,
    0x2, 0x28, 0xf6, 0x22, 0xf, 0xff, 0xff, 0xfe,
    0x5, 0x5b, 0xf9, 0x54, 0x0, 0x6, 0xf3, 0x0,
    0x0, 0x5, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x77, 0x77, 0x76, 0xf, 0xff, 0xff, 0xfe,

    /* U+00B2 "²" */
    0x2b, 0xff, 0xa0, 0x38, 0x33, 0xf6, 0x0, 0x3,
    0xf4, 0x0, 0x5e, 0x60, 0x8, 0xf5, 0x10, 0x5f,
    0xff, 0xfc,

    /* U+00B3 "³" */
    0x7f, 0xff, 0xf7, 0x1, 0x2d, 0xa0, 0x0, 0xaf,
    0xc2, 0x0, 0x0, 0xbb, 0x47, 0x33, 0xcb, 0x4c,
    0xef, 0xb1,

    /* U+00B4 "´" */
    0x4, 0xfb, 0x14, 0xf8, 0x0,

    /* U+00B5 "µ" */
    0xcf, 0x10, 0x0, 0xdf, 0x1c, 0xf1, 0x0, 0xd,
    0xf1, 0xcf, 0x10, 0x0, 0xdf, 0x1c, 0xf1, 0x0,
    0xd, 0xf1, 0xcf, 0x20, 0x0, 0xdf, 0x1c, 0xf5,
    0x0, 0x3f, 0xf1, 0xcf, 0xf9, 0x9f, 0xff, 0x1c,
    0xfb, 0xfe, 0x9b, 0xf1, 0xcf, 0x10, 0x0, 0x0,
    0xc, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0,
    0x0, 0x0,

    /* U+00B6 "¶" */
    0x6, 0xdf, 0xff, 0xff, 0x96, 0xff, 0xff, 0x66,
    0xf9, 0xbf, 0xff, 0xf0, 0xf, 0x99, 0xff, 0xff,
    0x0, 0xf9, 0x2e, 0xff, 0xf0, 0xf, 0x90, 0x5,
    0xcf, 0x0, 0xf9, 0x0, 0x9, 0xf0, 0xf, 0x90,
    0x0, 0x9f, 0x0, 0xf9, 0x0, 0x9, 0xf0, 0xf,
    0x90, 0x0, 0x9f, 0x0, 0xf9, 0x0, 0x9, 0xf0,
    0xf, 0x90, 0x0, 0x9f, 0x0, 0xf9, 0x0, 0x3,
    0x50, 0x6, 0x30,

    /* U+00B7 "·" */
    0x3, 0x50, 0xf, 0xf4, 0xc, 0xe2,

    /* U+00B8 "¸" */
    0x0, 0x70, 0x0, 0x4f, 0x60, 0x1, 0x5f, 0x2,
    0xee, 0x80,

    /* U+00B9 "¹" */
    0xbf, 0xf2, 0x0, 0x4f, 0x20, 0x3, 0xf2, 0x0,
    0x3f, 0x20, 0x4, 0xf3, 0xd, 0xff, 0xf9,

    /* U+00BA "º" */
    0x7, 0xef, 0xa1, 0x5f, 0x42, 0xca, 0x8b, 0x0,
    0x6d, 0x5e, 0x20, 0xbb, 0x8, 0xff, 0xc2, 0x0,
    0x1, 0x0,

    /* U+00BB "»" */
    0x3b, 0x42, 0xb6, 0x0, 0xa, 0xe1, 0x8f, 0x30,
    0x0, 0xec, 0xc, 0xd0, 0x0, 0xbe, 0x19, 0xf2,
    0x7, 0xf4, 0x5f, 0x60, 0x3f, 0x82, 0xfa, 0x0,

    /* U+00BC "¼" */
    0xaf, 0xf2, 0x0, 0x0, 0x9, 0xb0, 0x0, 0x1,
    0x4f, 0x20, 0x0, 0x4, 0xf2, 0x0, 0x0, 0x3,
    0xf2, 0x0, 0x1, 0xe7, 0x0, 0x0, 0x0, 0x3f,
    0x20, 0x0, 0xac, 0x0, 0x0, 0x0, 0x3, 0xf2,
    0x0, 0x5f, 0x20, 0xb, 0x60, 0xc, 0xff, 0xf9,
    0x1e, 0x60, 0xa, 0xd0, 0x0, 0x12, 0x22, 0x1b,
    0xb0, 0x5, 0xf2, 0x10, 0x0, 0x0, 0x6, 0xf1,
    0x2, 0xf6, 0x1f, 0x20, 0x0, 0x2, 0xf6, 0x0,
    0x9f, 0xff, 0xff, 0x30, 0x0, 0xbb, 0x0, 0x1,
    0x22, 0x4f, 0x40, 0x0, 0x39, 0x10, 0x0, 0x0,
    0x1, 0xa1, 0x0,

    /* U+00BD "½" */
    0xaf, 0xf2, 0x0, 0x0, 0x9, 0xb0, 0x0, 0x14,
    0xf2, 0x0, 0x0, 0x5f, 0x20, 0x0, 0x3, 0xf2,
    0x0, 0x2, 0xf6, 0x0, 0x0, 0x3, 0xf2, 0x0,
    0xc, 0xa0, 0x0, 0x0, 0x3, 0xf2, 0x0, 0x8e,
    0x29, 0xee, 0xa1, 0xcf, 0xff, 0x93, 0xf3, 0x2a,
    0x33, 0xd8, 0x12, 0x22, 0x2d, 0x80, 0x0, 0x1,
    0xe6, 0x0, 0x0, 0xac, 0x0, 0x0, 0x2d, 0x90,
    0x0, 0x5, 0xf2, 0x0, 0x4, 0xf7, 0x0, 0x0,
    0x2f, 0x50, 0x0, 0x2f, 0xff, 0xfd, 0x0, 0x1,
    0x0, 0x0, 0x1, 0x11, 0x11,

    /* U+00BE "¾" */
    0x6e, 0xee, 0xf7, 0x0, 0x0, 0x9b, 0x0, 0x0,
    0x1, 0x18, 0xf1, 0x0, 0x4, 0xf2, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0x0, 0x1e, 0x70, 0x0, 0x0,
    0x0, 0x9f, 0xd4, 0x0, 0xac, 0x0, 0x0, 0x0,
    0x12, 0x0, 0xac, 0x5, 0xf2, 0x0, 0x85, 0x0,
    0x5e, 0xff, 0xe4, 0x1e, 0x60, 0x8, 0xe1, 0x0,
    0x0, 0x11, 0x0, 0xbb, 0x0, 0x4f, 0x31, 0x0,
    0x0, 0x0, 0x6, 0xf1, 0x1, 0xe6, 0x1f, 0x20,
    0x0, 0x0, 0x2f, 0x60, 0x9, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0xbb, 0x0, 0x1, 0x22, 0x4f, 0x40,
    0x0, 0x3, 0x91, 0x0, 0x0, 0x0, 0x1a, 0x10,

    /* U+00BF "¿" */
    0x0, 0x7, 0xf5, 0x0, 0x0, 0x0, 0xaf, 0x80,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x47,
    0x20, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0xc,
    0xf7, 0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0xf, 0xf5, 0x1, 0x9e,
    0x40, 0x7f, 0xff, 0xff, 0xc1, 0x0, 0x37, 0x98,
    0x40, 0x0,

    /* U+00C0 "À" */
    0x0, 0x0, 0x77, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0xaf, 0x30, 0x0, 0x0, 0x7, 0xf6, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0xee, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xe, 0xf9, 0x88, 0x88, 0xff, 0x20, 0x5, 0xf9,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0xe, 0xf1,

    /* U+00C1 "Á" */
    0x0, 0x0, 0x0, 0x6, 0x82, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0xaf, 0x30, 0x0, 0x0, 0x7, 0xf6, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0xee, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xe, 0xf9, 0x88, 0x88, 0xff, 0x20, 0x5, 0xf9,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0xe, 0xf1,

    /* U+00C2 "Â" */
    0x0, 0x0, 0x3, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xcc,
    0x9, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0xaf, 0x30, 0x0, 0x0, 0x7, 0xf6, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0xee, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xe, 0xf9, 0x88, 0x88, 0xff, 0x20, 0x5, 0xf9,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0xe, 0xf1,

    /* U+00C3 "Ã" */
    0x0, 0x0, 0x7e, 0x91, 0xc3, 0x0, 0x0, 0x0,
    0xf, 0x4a, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x8, 0xf6, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xfd, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x7f,
    0x60, 0x3f, 0xb0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0xcf, 0x20, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xef, 0x98, 0x88, 0x8f, 0xf2, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x6f, 0x90, 0xd, 0xf2,
    0x0, 0x0, 0x0, 0xef, 0x10,

    /* U+00C4 "Ä" */
    0x0, 0x0, 0x8d, 0x9, 0xc0, 0x0, 0x0, 0x0,
    0x6, 0xa0, 0x79, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x8, 0xf6, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xfd, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x7f,
    0x60, 0x3f, 0xb0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0xcf, 0x20, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xef, 0x98, 0x88, 0x8f, 0xf2, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x6f, 0x90, 0xd, 0xf2,
    0x0, 0x0, 0x0, 0xef, 0x10,

    /* U+00C5 "Å" */
    0x0, 0x0, 0x6, 0xb8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc0, 0x93, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x9, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6c, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8,
    0xf6, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xfd, 0xa,
    0xf3, 0x0, 0x0, 0x0, 0x7f, 0x60, 0x3f, 0xb0,
    0x0, 0x0, 0xe, 0xe0, 0x0, 0xcf, 0x20, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xef,
    0x98, 0x88, 0x8f, 0xf2, 0x0, 0x5f, 0x90, 0x0,
    0x0, 0x6f, 0x90, 0xd, 0xf2, 0x0, 0x0, 0x0,
    0xef, 0x10,

    /* U+00C6 "Æ" */
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xfb, 0x99, 0x99,
    0x90, 0x0, 0x0, 0x8, 0xf6, 0xbf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfc, 0xb, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x30, 0xbf, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x5f, 0xa0, 0xb, 0xfc,
    0x99, 0x99, 0x30, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x8, 0xff, 0x88, 0x8f,
    0xf4, 0x0, 0x0, 0x0, 0x2, 0xfd, 0x0, 0x0,
    0xbf, 0xb9, 0x99, 0x99, 0x10, 0xcf, 0x30, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xf3,

    /* U+00C7 "Ç" */
    0x0, 0x5, 0xbe, 0xfd, 0x91, 0x0, 0xb, 0xff,
    0xdb, 0xdf, 0xf2, 0x9, 0xfc, 0x20, 0x0, 0x48,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xc2, 0x0, 0x4, 0x80, 0x0, 0xcf, 0xfd, 0xbd,
    0xff, 0x20, 0x0, 0x6c, 0xff, 0xd9, 0x10, 0x0,
    0x0, 0xb, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0x80, 0x0, 0x0, 0x0, 0x9e, 0xd3, 0x0, 0x0,

    /* U+00C8 "È" */
    0x3, 0x85, 0x0, 0x0, 0x0, 0xa, 0xf5, 0x0,
    0x0, 0x0, 0x9, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb,
    0x99, 0x99, 0x90, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7,
    0x9, 0xfd, 0x99, 0x99, 0x40, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9,
    0x99, 0x99, 0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+00C9 "É" */
    0x0, 0x0, 0x17, 0x70, 0x0, 0x0, 0xc, 0xf3,
    0x0, 0x0, 0x9, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb,
    0x99, 0x99, 0x90, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7,
    0x9, 0xfd, 0x99, 0x99, 0x40, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9,
    0x99, 0x99, 0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+00CA "Ê" */
    0x0, 0x6, 0x82, 0x0, 0x0, 0x6, 0xff, 0xd1,
    0x0, 0x3, 0xf7, 0x1d, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb,
    0x99, 0x99, 0x90, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7,
    0x9, 0xfd, 0x99, 0x99, 0x40, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9,
    0x99, 0x99, 0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+00CB "Ë" */
    0x0, 0xd8, 0x1e, 0x70, 0x0, 0xa, 0x50, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xf1, 0x9f, 0xb9, 0x99, 0x99, 0x9, 0xf5,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0x70, 0x9f, 0xd9, 0x99, 0x94,
    0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xfb, 0x99, 0x99, 0x92, 0x9f, 0xff,
    0xff, 0xff, 0x40,

    /* U+00CC "Ì" */
    0x28, 0x60, 0x0, 0x8f, 0x60, 0x0, 0x7f, 0x40,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x9, 0xf5, 0x0,
    0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f, 0x50, 0x9,
    0xf5, 0x0, 0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f,
    0x50, 0x9, 0xf5,

    /* U+00CD "Í" */
    0x0, 0x77, 0x0, 0xaf, 0x50, 0x7f, 0x40, 0x0,
    0x0, 0x0, 0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f,
    0x50, 0x9, 0xf5, 0x0, 0x9f, 0x50, 0x9, 0xf5,
    0x0, 0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f, 0x50,
    0x9, 0xf5, 0x0,

    /* U+00CE "Î" */
    0x0, 0x58, 0x30, 0x0, 0x4f, 0xfe, 0x10, 0x2e,
    0x90, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x9f, 0x50,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x9, 0xf5, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x9, 0xf5,
    0x0,

    /* U+00CF "Ï" */
    0xca, 0xd, 0x89, 0x70, 0xa6, 0x0, 0x0, 0x0,
    0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f, 0x50, 0x9,
    0xf5, 0x0, 0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f,
    0x50, 0x9, 0xf5, 0x0, 0x9f, 0x50, 0x9, 0xf5,
    0x0,

    /* U+00D0 "Ð" */
    0x7, 0xff, 0xff, 0xfd, 0x92, 0x0, 0x7, 0xfd,
    0xaa, 0xad, 0xff, 0x60, 0x7, 0xf8, 0x0, 0x0,
    0x5f, 0xf3, 0x18, 0xfa, 0x11, 0x0, 0x7, 0xfa,
    0xef, 0xff, 0xff, 0x50, 0x2, 0xfc, 0x4b, 0xfd,
    0x44, 0x10, 0x2, 0xfc, 0x7, 0xf8, 0x0, 0x0,
    0x7, 0xfa, 0x7, 0xf8, 0x0, 0x0, 0x5f, 0xf3,
    0x7, 0xfd, 0xaa, 0xad, 0xff, 0x50, 0x7, 0xff,
    0xff, 0xfd, 0x92, 0x0,

    /* U+00D1 "Ñ" */
    0x0, 0x1c, 0xc3, 0x6a, 0x0, 0x0, 0x8a, 0x5e,
    0xf4, 0x0, 0x0, 0x10, 0x0, 0x10, 0x0, 0x9f,
    0x60, 0x0, 0x3, 0xfb, 0x9f, 0xf4, 0x0, 0x3,
    0xfb, 0x9f, 0xfe, 0x20, 0x3, 0xfb, 0x9f, 0x9f,
    0xd0, 0x3, 0xfb, 0x9f, 0x56, 0xfb, 0x3, 0xfb,
    0x9f, 0x50, 0x9f, 0x83, 0xfb, 0x9f, 0x50, 0xb,
    0xf8, 0xfb, 0x9f, 0x50, 0x1, 0xdf, 0xfb, 0x9f,
    0x50, 0x0, 0x2f, 0xfb, 0x9f, 0x50, 0x0, 0x4,
    0xfb,

    /* U+00D2 "Ò" */
    0x0, 0x2, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+00D3 "Ó" */
    0x0, 0x0, 0x0, 0x17, 0x70, 0x0, 0x0, 0x0,
    0x1, 0xdd, 0x20, 0x0, 0x0, 0x0, 0x5, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+00D4 "Ô" */
    0x0, 0x0, 0x6, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x30, 0x0, 0x0, 0x2, 0x82, 0x5,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+00D5 "Õ" */
    0x0, 0x2, 0xdf, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x22, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc,
    0x20, 0x0, 0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0,
    0x7, 0xfb, 0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe,
    0x4f, 0xa0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1,
    0x0, 0x0, 0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf4, 0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0,

    /* U+00D6 "Ö" */
    0x0, 0x0, 0xd9, 0xe, 0x70, 0x0, 0x0, 0x0,
    0xa6, 0xa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc,
    0x20, 0x0, 0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0,
    0x7, 0xfb, 0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe,
    0x4f, 0xa0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1,
    0x0, 0x0, 0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf4, 0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0,

    /* U+00D7 "×" */
    0x0, 0x0, 0x0, 0x2, 0xe3, 0x5, 0xd1, 0x1d,
    0xf8, 0xfc, 0x0, 0x1f, 0xff, 0x0, 0x5, 0xff,
    0xf3, 0x3, 0xfb, 0x2d, 0xf2, 0x8, 0x0, 0x18,
    0x0,

    /* U+00D8 "Ø" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xc0, 0x0, 0x5, 0xbe, 0xfe,
    0xef, 0x20, 0x0, 0xbf, 0xfd, 0xbf, 0xff, 0x60,
    0x9, 0xfc, 0x20, 0xc, 0xff, 0xf3, 0x1f, 0xf1,
    0x0, 0x7d, 0x7, 0xfb, 0x4f, 0xb0, 0x3, 0xf3,
    0x1, 0xfe, 0x4f, 0xa0, 0xd, 0x70, 0x0, 0xfe,
    0x1f, 0xf1, 0xab, 0x0, 0x6, 0xfb, 0x9, 0xff,
    0xf1, 0x0, 0x5f, 0xf4, 0x0, 0xdf, 0xfd, 0xbe,
    0xff, 0x60, 0x0, 0xcf, 0xbe, 0xfe, 0x92, 0x0,
    0x8, 0xd0, 0x0, 0x0, 0x0, 0x0,

    /* U+00D9 "Ù" */
    0x0, 0x58, 0x30, 0x0, 0x0, 0x0, 0xa, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x57, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+00DA "Ú" */
    0x0, 0x0, 0x4, 0x84, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0x0, 0x74, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+00DB "Û" */
    0x0, 0x1, 0x77, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xc0, 0x0, 0x0, 0x57, 0x1, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+00DC "Ü" */
    0x0, 0x4f, 0x34, 0xe2, 0x0, 0x0, 0x2b, 0x23,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xaf, 0x40, 0x0, 0x6, 0xf8, 0x9f, 0x60, 0x0,
    0x9, 0xf6, 0x5f, 0xd1, 0x0, 0x2f, 0xf2, 0xc,
    0xff, 0xcc, 0xff, 0x90, 0x0, 0x7d, 0xff, 0xc6,
    0x0,

    /* U+00DD "Ý" */
    0x0, 0x0, 0x0, 0x14, 0x30, 0x0, 0x0, 0x0,
    0x1, 0xde, 0x20, 0x0, 0x0, 0x0, 0xc, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x3f, 0xb0, 0x3, 0xfd,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x8f, 0x80, 0x7,
    0xf7, 0x0, 0x0, 0xe, 0xf2, 0x2f, 0xc0, 0x0,
    0x0, 0x4, 0xfc, 0xcf, 0x30, 0x0, 0x0, 0x0,
    0xaf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0,

    /* U+00DE "Þ" */
    0x9f, 0x50, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfe,
    0xc5, 0x0, 0x9f, 0xfa, 0xab, 0xff, 0x80, 0x9f,
    0x50, 0x0, 0x2f, 0xf0, 0x9f, 0x50, 0x0, 0xd,
    0xf2, 0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f, 0xfa,
    0xab, 0xff, 0x80, 0x9f, 0xff, 0xff, 0xc5, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x0,

    /* U+00DF "ß" */
    0x2, 0xbe, 0xfc, 0x60, 0x2, 0xef, 0xb9, 0xef,
    0x60, 0x8f, 0x60, 0x2, 0xfb, 0xb, 0xf2, 0x0,
    0x3f, 0xa0, 0xcf, 0x11, 0x9f, 0xf2, 0xc, 0xf1,
    0x2f, 0xff, 0xd0, 0xcf, 0x10, 0x2, 0xbf, 0x7c,
    0xf1, 0x0, 0x4, 0xfa, 0xcf, 0x10, 0x0, 0x7f,
    0x9c, 0xf1, 0x69, 0xbf, 0xf2, 0xcf, 0x19, 0xff,
    0xb3, 0x0,

    /* U+00E0 "à" */
    0x1, 0xcf, 0x30, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+00E1 "á" */
    0x0, 0x0, 0x4f, 0xb1, 0x0, 0x5, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+00E2 "â" */
    0x0, 0x1d, 0xfc, 0x10, 0x1, 0xc8, 0x9, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+00E3 "ã" */
    0x0, 0x9e, 0x81, 0xf1, 0x2, 0xe3, 0xbf, 0xa0,
    0x0, 0x10, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+00E4 "ä" */
    0x0, 0xbb, 0xc, 0x90, 0x0, 0x88, 0x8, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+00E5 "å" */
    0x0, 0x9, 0xc8, 0x0, 0x0, 0x39, 0xb, 0x10,
    0x0, 0x9, 0xc8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xbe, 0xfd, 0x60, 0xc, 0xc9, 0x9e, 0xf7,
    0x0, 0x0, 0x2, 0xfc, 0x5, 0xdf, 0xff, 0xfe,
    0x2f, 0xd4, 0x33, 0xff, 0x5f, 0x80, 0x1, 0xff,
    0x1f, 0xe5, 0x5c, 0xff, 0x4, 0xdf, 0xe8, 0xdf,

    /* U+00E6 "æ" */
    0x4, 0xbe, 0xfd, 0x71, 0xae, 0xfd, 0x50, 0x0,
    0xcc, 0x99, 0xef, 0xff, 0xa9, 0xdf, 0x80, 0x0,
    0x0, 0x2, 0xff, 0x60, 0x0, 0xcf, 0x20, 0x4c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xd4,
    0x33, 0xff, 0x33, 0x33, 0x33, 0x4, 0xf8, 0x0,
    0x1f, 0xf4, 0x0, 0x4, 0x0, 0x1f, 0xe7, 0x6d,
    0xfe, 0xf8, 0x69, 0xf8, 0x0, 0x3c, 0xfe, 0xa2,
    0x19, 0xef, 0xe9, 0x10,

    /* U+00E7 "ç" */
    0x0, 0x5c, 0xfe, 0xb2, 0x0, 0x8f, 0xe9, 0xaf,
    0xf1, 0x2f, 0xd1, 0x0, 0x34, 0x6, 0xf8, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x2, 0xfe,
    0x10, 0x4, 0x50, 0x7, 0xfe, 0xab, 0xff, 0x10,
    0x5, 0xcf, 0xfb, 0x20, 0x0, 0x2, 0xf3, 0x0,
    0x0, 0x0, 0x15, 0xf1, 0x0, 0x0, 0x1e, 0xea,
    0x0, 0x0,

    /* U+00E8 "è" */
    0x0, 0xaf, 0x60, 0x0, 0x0, 0x0, 0x6f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+00E9 "é" */
    0x0, 0x0, 0x2e, 0xd2, 0x0, 0x0, 0x2e, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+00EA "ê" */
    0x0, 0xb, 0xff, 0x20, 0x0, 0xa, 0xb0, 0x7c,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+00EB "ë" */
    0x0, 0x7d, 0x9, 0xc0, 0x0, 0x5, 0xa0, 0x69,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+00EC "ì" */
    0x3e, 0xd1, 0x0, 0x1b, 0xc1, 0x0, 0x0, 0x0,
    0xc, 0xf1, 0x0, 0xcf, 0x10, 0xc, 0xf1, 0x0,
    0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf, 0x10, 0xc,
    0xf1, 0x0, 0xcf, 0x10,

    /* U+00ED "í" */
    0x0, 0x9f, 0x70, 0x8, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc,
    0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0,
    0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1,
    0x0,

    /* U+00EE "î" */
    0x2, 0xff, 0x70, 0xb, 0x72, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10,
    0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf,
    0x10,

    /* U+00EF "ï" */
    0xaa, 0x5e, 0x16, 0x73, 0xa0, 0x0, 0x0, 0x0,
    0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf, 0x10, 0xc,
    0xf1, 0x0, 0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf,
    0x10, 0xc, 0xf1, 0x0,

    /* U+00F0 "ð" */
    0x7, 0xef, 0xeb, 0x55, 0x40, 0x6, 0x98, 0xbf,
    0xff, 0x50, 0x0, 0x5b, 0xfc, 0xff, 0x60, 0x0,
    0xc8, 0x20, 0x1f, 0xc0, 0x0, 0x16, 0x75, 0xd,
    0xf0, 0x6, 0xff, 0xff, 0xcd, 0xf2, 0x2f, 0xe4,
    0x2, 0xcf, 0xf1, 0x6f, 0x80, 0x0, 0x3f, 0xe0,
    0x5f, 0xb0, 0x0, 0x6f, 0x90, 0xd, 0xfc, 0x8b,
    0xfd, 0x10, 0x1, 0x9e, 0xfe, 0x91, 0x0,

    /* U+00F1 "ñ" */
    0x1, 0xce, 0x55, 0xc0, 0x0, 0x6b, 0x3d, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0xcf,
    0xe9, 0x0, 0xcf, 0xfc, 0xae, 0xfa, 0xc, 0xf8,
    0x0, 0x1f, 0xf0, 0xcf, 0x20, 0x0, 0xcf, 0x1c,
    0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0, 0xcf,
    0x2c, 0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0,
    0xcf, 0x20,

    /* U+00F2 "ò" */
    0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x4, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+00F3 "ó" */
    0x0, 0x0, 0x1d, 0xe3, 0x0, 0x0, 0x1, 0xdb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+00F4 "ô" */
    0x0, 0x9, 0xff, 0x40, 0x0, 0x0, 0x8c, 0x15,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+00F5 "õ" */
    0x0, 0x4e, 0xb2, 0xa7, 0x0, 0x0, 0xc6, 0x7f,
    0xe1, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+00F6 "ö" */
    0x0, 0x5e, 0x26, 0xe1, 0x0, 0x0, 0x4b, 0x14,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+00F7 "÷" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x7, 0xf5, 0x0,
    0x0, 0x6, 0xf4, 0x0, 0x2, 0x22, 0x22, 0x22,
    0xf, 0xff, 0xff, 0xfe, 0x5, 0x55, 0x55, 0x54,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x7, 0xf5, 0x0,
    0x0, 0x6, 0xf4, 0x0,

    /* U+00F8 "ø" */
    0x0, 0x0, 0x0, 0xb, 0x40, 0x0, 0x5c, 0xfe,
    0xfd, 0x0, 0x8, 0xfe, 0x9c, 0xff, 0x30, 0x2f,
    0xd1, 0xb, 0x9f, 0xc0, 0x6f, 0x80, 0x6a, 0xd,
    0xf0, 0x5f, 0x81, 0xd1, 0xd, 0xf0, 0x2f, 0xed,
    0x50, 0x5f, 0xc0, 0x8, 0xff, 0xab, 0xff, 0x30,
    0x1, 0xfe, 0xfe, 0xb2, 0x0, 0xa, 0x60, 0x0,
    0x0, 0x0,

    /* U+00F9 "ù" */
    0x4, 0xec, 0x0, 0x0, 0x0, 0x1c, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+00FA "ú" */
    0x0, 0x0, 0xaf, 0x60, 0x0, 0xa, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+00FB "û" */
    0x0, 0x5f, 0xf7, 0x0, 0x4, 0xc3, 0x2c, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+00FC "ü" */
    0x2, 0xe5, 0x3e, 0x40, 0x1, 0xb3, 0x2b, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+00FD "ý" */
    0x0, 0x0, 0x9, 0xf7, 0x0, 0x0, 0x0, 0x9d,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0x0, 0x4, 0xf8, 0x6, 0xf8, 0x0, 0xb,
    0xf1, 0x0, 0xee, 0x0, 0x2f, 0xa0, 0x0, 0x8f,
    0x50, 0x9f, 0x30, 0x0, 0x1f, 0xc0, 0xfc, 0x0,
    0x0, 0xa, 0xfa, 0xf5, 0x0, 0x0, 0x3, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xdf, 0x10, 0x0, 0xc, 0xac, 0xf8, 0x0,
    0x0, 0x1a, 0xee, 0x90, 0x0, 0x0,

    /* U+00FE "þ" */
    0xcf, 0x10, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0x0, 0xc, 0xf6, 0xcf,
    0xe9, 0x10, 0xcf, 0xfc, 0x9c, 0xfd, 0xc, 0xfa,
    0x0, 0xa, 0xf7, 0xcf, 0x20, 0x0, 0x3f, 0xac,
    0xf2, 0x0, 0x3, 0xfa, 0xcf, 0xa0, 0x0, 0xbf,
    0x7c, 0xff, 0xc9, 0xdf, 0xd0, 0xcf, 0x6c, 0xfe,
    0x91, 0xc, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x10,
    0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0x0,

    /* U+00FF "ÿ" */
    0x0, 0x1e, 0x62, 0xe5, 0x0, 0x0, 0x1a, 0x41,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0x0, 0x4, 0xf8, 0x6, 0xf8, 0x0, 0xb,
    0xf1, 0x0, 0xee, 0x0, 0x2f, 0xa0, 0x0, 0x8f,
    0x50, 0x9f, 0x30, 0x0, 0x1f, 0xc0, 0xfc, 0x0,
    0x0, 0xa, 0xfa, 0xf5, 0x0, 0x0, 0x3, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xdf, 0x10, 0x0, 0xc, 0xac, 0xf8, 0x0,
    0x0, 0x1a, 0xee, 0x90, 0x0, 0x0,

    /* U+0100 "Ā" */
    0x0, 0x0, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x2, 0x22, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x8, 0xf6, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xfd, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x7f,
    0x60, 0x3f, 0xb0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0xcf, 0x20, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xef, 0x98, 0x88, 0x8f, 0xf2, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x6f, 0x90, 0xd, 0xf2,
    0x0, 0x0, 0x0, 0xef, 0x10,

    /* U+0101 "ā" */
    0x2, 0xff, 0xff, 0xf0, 0x0, 0x22, 0x22, 0x20,
    0x4, 0xbe, 0xfd, 0x60, 0xc, 0xc9, 0x9e, 0xf7,
    0x0, 0x0, 0x2, 0xfc, 0x5, 0xdf, 0xff, 0xfe,
    0x2f, 0xd4, 0x33, 0xff, 0x5f, 0x80, 0x1, 0xff,
    0x1f, 0xe5, 0x5c, 0xff, 0x4, 0xdf, 0xe8, 0xdf,

    /* U+0102 "Ă" */
    0x0, 0x0, 0x72, 0x0, 0x81, 0x0, 0x0, 0x0,
    0xb, 0x90, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0xaf, 0x30, 0x0, 0x0, 0x7, 0xf6, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0xee, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xe, 0xf9, 0x88, 0x88, 0xff, 0x20, 0x5, 0xf9,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0xe, 0xf1,

    /* U+0103 "ă" */
    0x0, 0xf5, 0x6, 0xe0, 0x0, 0x5e, 0xfe, 0x40,
    0x0, 0x0, 0x10, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+0104 "Ą" */
    0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0xaf,
    0x30, 0x0, 0x0, 0x7, 0xf6, 0x3, 0xfb, 0x0,
    0x0, 0x0, 0xee, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0, 0xe, 0xf9,
    0x88, 0x88, 0xff, 0x20, 0x5, 0xf9, 0x0, 0x0,
    0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0, 0x0, 0xf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x3, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xef, 0x60,

    /* U+0105 "ą" */
    0x4, 0xbe, 0xfd, 0x60, 0x0, 0xcc, 0x99, 0xef,
    0x70, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x5d, 0xff,
    0xff, 0xe0, 0x2f, 0xd4, 0x33, 0xff, 0x5, 0xf8,
    0x0, 0x1f, 0xf0, 0x1f, 0xe5, 0x5c, 0xff, 0x0,
    0x4d, 0xfe, 0x8f, 0xf0, 0x0, 0x0, 0x8, 0xc3,
    0x0, 0x0, 0x0, 0xf5, 0x10, 0x0, 0x0, 0x9,
    0xfe, 0x10,

    /* U+0106 "Ć" */
    0x0, 0x0, 0x0, 0x28, 0x60, 0x0, 0x0, 0x0,
    0x2e, 0xc1, 0x0, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfd, 0x91, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf2, 0x9, 0xfc, 0x20, 0x0, 0x48, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xc2, 0x0,
    0x4, 0x80, 0x0, 0xcf, 0xfd, 0xbd, 0xff, 0x20,
    0x0, 0x6c, 0xff, 0xd9, 0x10,

    /* U+0107 "ć" */
    0x0, 0x0, 0x3e, 0xc2, 0x0, 0x0, 0x3e, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xeb, 0x20, 0x8, 0xfe, 0x9a, 0xff, 0x12, 0xfd,
    0x10, 0x3, 0x40, 0x6f, 0x80, 0x0, 0x0, 0x5,
    0xf8, 0x0, 0x0, 0x0, 0x2f, 0xe1, 0x0, 0x45,
    0x0, 0x7f, 0xea, 0xbf, 0xf1, 0x0, 0x5c, 0xff,
    0xb2, 0x0,

    /* U+0108 "Ĉ" */
    0x0, 0x0, 0x7, 0x82, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xe2, 0x0, 0x0, 0x3, 0x71, 0x6, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfd, 0x91, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf2, 0x9, 0xfc, 0x20, 0x0, 0x48, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xc2, 0x0,
    0x4, 0x80, 0x0, 0xcf, 0xfd, 0xbd, 0xff, 0x20,
    0x0, 0x6c, 0xff, 0xd9, 0x10,

    /* U+0109 "ĉ" */
    0x0, 0xb, 0xfe, 0x10, 0x0, 0xa, 0xa0, 0x7c,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xeb, 0x20, 0x8, 0xfe, 0x9a, 0xff, 0x12, 0xfd,
    0x10, 0x3, 0x40, 0x6f, 0x80, 0x0, 0x0, 0x5,
    0xf8, 0x0, 0x0, 0x0, 0x2f, 0xe1, 0x0, 0x45,
    0x0, 0x7f, 0xea, 0xbf, 0xf1, 0x0, 0x5c, 0xff,
    0xb2, 0x0,

    /* U+010A "Ċ" */
    0x0, 0x0, 0xa, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfd, 0x91, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf2, 0x9, 0xfc, 0x20, 0x0, 0x48, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xc2, 0x0,
    0x4, 0x80, 0x0, 0xcf, 0xfd, 0xbd, 0xff, 0x20,
    0x0, 0x6c, 0xff, 0xd9, 0x10,

    /* U+010B "ċ" */
    0x0, 0x3, 0xf6, 0x0, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xeb, 0x20, 0x8, 0xfe, 0x9a, 0xff, 0x12, 0xfd,
    0x10, 0x3, 0x40, 0x6f, 0x80, 0x0, 0x0, 0x5,
    0xf8, 0x0, 0x0, 0x0, 0x2f, 0xe1, 0x0, 0x45,
    0x0, 0x7f, 0xea, 0xbf, 0xf1, 0x0, 0x5c, 0xff,
    0xb2, 0x0,

    /* U+010C "Č" */
    0x0, 0x3, 0x71, 0x5, 0x60, 0x0, 0x0, 0xa,
    0xd9, 0xe2, 0x0, 0x0, 0x0, 0x9, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfd, 0x91, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf2, 0x9, 0xfc, 0x20, 0x0, 0x48, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xc2, 0x0,
    0x4, 0x80, 0x0, 0xcf, 0xfd, 0xbd, 0xff, 0x20,
    0x0, 0x6c, 0xff, 0xd9, 0x10,

    /* U+010D "č" */
    0x0, 0xbc, 0x2a, 0xd1, 0x0, 0x0, 0xbf, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xeb, 0x20, 0x8, 0xfe, 0x9a, 0xff, 0x12, 0xfd,
    0x10, 0x3, 0x40, 0x6f, 0x80, 0x0, 0x0, 0x5,
    0xf8, 0x0, 0x0, 0x0, 0x2f, 0xe1, 0x0, 0x45,
    0x0, 0x7f, 0xea, 0xbf, 0xf1, 0x0, 0x5c, 0xff,
    0xb2, 0x0,

    /* U+010E "Ď" */
    0x0, 0xbd, 0x3a, 0xd1, 0x0, 0x0, 0x0, 0xbf,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xfd, 0x81, 0x0, 0x9f, 0xca,
    0xaa, 0xef, 0xf4, 0x9, 0xf5, 0x0, 0x0, 0x6f,
    0xf1, 0x9f, 0x50, 0x0, 0x0, 0x9f, 0x79, 0xf5,
    0x0, 0x0, 0x4, 0xfa, 0x9f, 0x50, 0x0, 0x0,
    0x4f, 0xa9, 0xf5, 0x0, 0x0, 0x9, 0xf7, 0x9f,
    0x50, 0x0, 0x6, 0xff, 0x19, 0xfc, 0xaa, 0xae,
    0xff, 0x40, 0x9f, 0xff, 0xff, 0xd8, 0x10, 0x0,

    /* U+010F "ď" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9b, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0xab, 0x0, 0x0, 0x0, 0xc,
    0xf1, 0xaa, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0x0, 0x6d, 0xfd, 0x7c, 0xf1, 0x0, 0x9, 0xfe,
    0x9a, 0xff, 0xf1, 0x0, 0x2f, 0xe1, 0x0, 0x4f,
    0xf1, 0x0, 0x6f, 0x80, 0x0, 0xf, 0xf1, 0x0,
    0x6f, 0x80, 0x0, 0xf, 0xf1, 0x0, 0x2f, 0xe2,
    0x0, 0x5f, 0xf1, 0x0, 0x9, 0xfe, 0xab, 0xff,
    0xf1, 0x0, 0x0, 0x6d, 0xfe, 0x7b, 0xf1, 0x0,

    /* U+0110 "Đ" */
    0x7, 0xff, 0xff, 0xfd, 0x92, 0x0, 0x7, 0xfd,
    0xaa, 0xad, 0xff, 0x60, 0x7, 0xf8, 0x0, 0x0,
    0x5f, 0xf3, 0x18, 0xfa, 0x11, 0x0, 0x7, 0xfa,
    0xef, 0xff, 0xff, 0x50, 0x2, 0xfc, 0x4b, 0xfd,
    0x44, 0x10, 0x2, 0xfc, 0x7, 0xf8, 0x0, 0x0,
    0x7, 0xfa, 0x7, 0xf8, 0x0, 0x0, 0x5f, 0xf3,
    0x7, 0xfd, 0xaa, 0xad, 0xff, 0x50, 0x7, 0xff,
    0xff, 0xfd, 0x92, 0x0,

    /* U+0111 "đ" */
    0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x23, 0x3f, 0xf4,
    0x10, 0x6, 0xdf, 0xd7, 0xcf, 0x10, 0x9, 0xfe,
    0x9a, 0xff, 0xf1, 0x2, 0xfe, 0x10, 0x4, 0xff,
    0x10, 0x6f, 0x80, 0x0, 0xf, 0xf1, 0x6, 0xf8,
    0x0, 0x0, 0xff, 0x10, 0x2f, 0xe2, 0x0, 0x5f,
    0xf1, 0x0, 0x9f, 0xea, 0xbf, 0xff, 0x10, 0x0,
    0x6d, 0xfe, 0x7b, 0xf1, 0x0,

    /* U+0112 "Ē" */
    0x5, 0xff, 0xff, 0xd0, 0x0, 0x2, 0x22, 0x21,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xf1, 0x9f, 0xb9, 0x99, 0x99, 0x9, 0xf5,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0x70, 0x9f, 0xd9, 0x99, 0x94,
    0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xfb, 0x99, 0x99, 0x92, 0x9f, 0xff,
    0xff, 0xff, 0x40,

    /* U+0113 "ē" */
    0x0, 0xff, 0xff, 0xf4, 0x0, 0x1, 0x22, 0x22,
    0x0, 0x0, 0x6d, 0xfe, 0xa1, 0x0, 0x9f, 0xc8,
    0xaf, 0xe1, 0x2f, 0xc0, 0x0, 0x4f, 0x86, 0xff,
    0xff, 0xff, 0xfb, 0x6f, 0xa3, 0x33, 0x33, 0x22,
    0xfd, 0x10, 0x1, 0x30, 0x8, 0xfe, 0xaa, 0xee,
    0x0, 0x5, 0xcf, 0xfc, 0x40,

    /* U+0114 "Ĕ" */
    0x2, 0x70, 0x3, 0x60, 0x0, 0x1f, 0x40, 0xb9,
    0x0, 0x0, 0x5e, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb,
    0x99, 0x99, 0x90, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7,
    0x9, 0xfd, 0x99, 0x99, 0x40, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9,
    0x99, 0x99, 0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+0115 "ĕ" */
    0x0, 0xc8, 0x3, 0xf2, 0x0, 0x3, 0xdf, 0xf7,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+0116 "Ė" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x20,
    0x0, 0x0, 0x8, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb,
    0x99, 0x99, 0x90, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7,
    0x9, 0xfd, 0x99, 0x99, 0x40, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9,
    0x99, 0x99, 0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+0117 "ė" */
    0x0, 0x3, 0xe7, 0x0, 0x0, 0x0, 0x4f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+0118 "Ę" */
    0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb, 0x99, 0x99,
    0x90, 0x9f, 0x50, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0x9, 0xfd,
    0x99, 0x99, 0x40, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9, 0x99, 0x99,
    0x29, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4,
    0xe5, 0x0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0,
    0x4, 0xef, 0x60,

    /* U+0119 "ę" */
    0x0, 0x6d, 0xfe, 0xa1, 0x0, 0x9f, 0xc8, 0xaf,
    0xe1, 0x2f, 0xc0, 0x0, 0x4f, 0x86, 0xff, 0xff,
    0xff, 0xfb, 0x6f, 0xa3, 0x33, 0x33, 0x22, 0xfd,
    0x10, 0x1, 0x30, 0x8, 0xfe, 0xaa, 0xee, 0x0,
    0x5, 0xcf, 0xff, 0x40, 0x0, 0x0, 0x1e, 0x40,
    0x0, 0x0, 0x4, 0xf2, 0x10, 0x0, 0x0, 0xb,
    0xfc, 0x0,

    /* U+011A "Ě" */
    0x3, 0x71, 0x5, 0x70, 0x0, 0xc, 0xd8, 0xf5,
    0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb,
    0x99, 0x99, 0x90, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7,
    0x9, 0xfd, 0x99, 0x99, 0x40, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9,
    0x99, 0x99, 0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+011B "ě" */
    0x0, 0xad, 0x29, 0xe2, 0x0, 0x0, 0xaf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+011C "Ĝ" */
    0x0, 0x0, 0x6, 0x82, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xe2, 0x0, 0x0, 0x3, 0x71, 0x6, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0xa2, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf4, 0x9, 0xfc, 0x20, 0x0, 0x38, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x1, 0x4, 0xfa, 0x0, 0x0, 0x6, 0xf6, 0x1f,
    0xf1, 0x0, 0x0, 0x6f, 0x60, 0xaf, 0xc2, 0x0,
    0x9, 0xf6, 0x0, 0xbf, 0xfd, 0xbd, 0xff, 0x70,
    0x0, 0x6c, 0xef, 0xea, 0x30,

    /* U+011D "ĝ" */
    0x0, 0x6, 0xff, 0x60, 0x0, 0x0, 0x5c, 0x22,
    0xc5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x8a, 0xf3, 0x9, 0xfd, 0x89, 0xef,
    0xf3, 0x2f, 0xd0, 0x0, 0x2f, 0xf3, 0x6f, 0x80,
    0x0, 0xf, 0xf3, 0x6f, 0x80, 0x0, 0xf, 0xf3,
    0x2f, 0xe2, 0x0, 0x3f, 0xf3, 0x9, 0xfe, 0x9a,
    0xff, 0xf2, 0x0, 0x6d, 0xfe, 0x8c, 0xf2, 0x1,
    0x0, 0x0, 0x2f, 0xe0, 0xc, 0xfb, 0x9a, 0xff,
    0x70, 0x3, 0xae, 0xff, 0xc5, 0x0,

    /* U+011E "Ğ" */
    0x0, 0x2, 0x70, 0x3, 0x60, 0x0, 0x0, 0x2f,
    0x30, 0xc8, 0x0, 0x0, 0x0, 0x6f, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0xa2, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf4, 0x9, 0xfc, 0x20, 0x0, 0x38, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x1, 0x4, 0xfa, 0x0, 0x0, 0x6, 0xf6, 0x1f,
    0xf1, 0x0, 0x0, 0x6f, 0x60, 0xaf, 0xc2, 0x0,
    0x9, 0xf6, 0x0, 0xbf, 0xfd, 0xbd, 0xff, 0x70,
    0x0, 0x6c, 0xef, 0xea, 0x30,

    /* U+011F "ğ" */
    0x0, 0x7d, 0x0, 0xc7, 0x0, 0x0, 0xa, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x8a, 0xf3, 0x9, 0xfd, 0x89, 0xef,
    0xf3, 0x2f, 0xd0, 0x0, 0x2f, 0xf3, 0x6f, 0x80,
    0x0, 0xf, 0xf3, 0x6f, 0x80, 0x0, 0xf, 0xf3,
    0x2f, 0xe2, 0x0, 0x3f, 0xf3, 0x9, 0xfe, 0x9a,
    0xff, 0xf2, 0x0, 0x6d, 0xfe, 0x8c, 0xf2, 0x1,
    0x0, 0x0, 0x2f, 0xe0, 0xc, 0xfb, 0x9a, 0xff,
    0x70, 0x3, 0xae, 0xff, 0xc5, 0x0,

    /* U+0120 "Ġ" */
    0x0, 0x0, 0x9, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0xa2, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf4, 0x9, 0xfc, 0x20, 0x0, 0x38, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x1, 0x4, 0xfa, 0x0, 0x0, 0x6, 0xf6, 0x1f,
    0xf1, 0x0, 0x0, 0x6f, 0x60, 0xaf, 0xc2, 0x0,
    0x9, 0xf6, 0x0, 0xbf, 0xfd, 0xbd, 0xff, 0x70,
    0x0, 0x6c, 0xef, 0xea, 0x30,

    /* U+0121 "ġ" */
    0x0, 0x0, 0xcd, 0x0, 0x0, 0x0, 0x0, 0xee,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x8a, 0xf3, 0x9, 0xfd, 0x89, 0xef,
    0xf3, 0x2f, 0xd0, 0x0, 0x2f, 0xf3, 0x6f, 0x80,
    0x0, 0xf, 0xf3, 0x6f, 0x80, 0x0, 0xf, 0xf3,
    0x2f, 0xe2, 0x0, 0x3f, 0xf3, 0x9, 0xfe, 0x9a,
    0xff, 0xf2, 0x0, 0x6d, 0xfe, 0x8c, 0xf2, 0x1,
    0x0, 0x0, 0x2f, 0xe0, 0xc, 0xfb, 0x9a, 0xff,
    0x70, 0x3, 0xae, 0xff, 0xc5, 0x0,

    /* U+0122 "Ģ" */
    0x0, 0x5, 0xbe, 0xfe, 0xa2, 0x0, 0xb, 0xff,
    0xdb, 0xdf, 0xf4, 0x9, 0xfc, 0x20, 0x0, 0x38,
    0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x0, 0x1, 0x4, 0xfa, 0x0, 0x0, 0x6,
    0xf6, 0x1f, 0xf1, 0x0, 0x0, 0x6f, 0x60, 0xaf,
    0xc2, 0x0, 0x9, 0xf6, 0x0, 0xbf, 0xfd, 0xbd,
    0xff, 0x70, 0x0, 0x6c, 0xef, 0xea, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x45,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x79, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0,

    /* U+0123 "ģ" */
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xa8,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0x0,
    0x0, 0x56, 0x0, 0x0, 0x0, 0x6d, 0xfe, 0x8a,
    0xf3, 0x9, 0xfd, 0x89, 0xef, 0xf3, 0x2f, 0xd0,
    0x0, 0x2f, 0xf3, 0x6f, 0x80, 0x0, 0xf, 0xf3,
    0x6f, 0x80, 0x0, 0xf, 0xf3, 0x2f, 0xe2, 0x0,
    0x3f, 0xf3, 0x9, 0xfe, 0x9a, 0xff, 0xf2, 0x0,
    0x6d, 0xfe, 0x8c, 0xf2, 0x1, 0x0, 0x0, 0x2f,
    0xe0, 0xc, 0xfb, 0x9a, 0xff, 0x70, 0x3, 0xae,
    0xff, 0xc5, 0x0,

    /* U+0124 "Ĥ" */
    0x0, 0x0, 0x77, 0x10, 0x0, 0x0, 0x9, 0xff,
    0xb0, 0x0, 0x0, 0x6f, 0x43, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x3,
    0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb, 0x9f, 0x50,
    0x0, 0x3, 0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xfb, 0x9f, 0xea, 0xaa,
    0xac, 0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb, 0x9f,
    0x50, 0x0, 0x3, 0xfb, 0x9f, 0x50, 0x0, 0x3,
    0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb,

    /* U+0125 "ĥ" */
    0x3, 0xff, 0x80, 0x0, 0x0, 0x3, 0xf8, 0x4e,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x10, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x6c, 0xfe, 0x90, 0x0, 0xc,
    0xff, 0xca, 0xef, 0xa0, 0x0, 0xcf, 0x80, 0x1,
    0xff, 0x0, 0xc, 0xf2, 0x0, 0xc, 0xf1, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x20, 0xc, 0xf1, 0x0,
    0xc, 0xf2, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x20,
    0xc, 0xf1, 0x0, 0xc, 0xf2,

    /* U+0126 "Ħ" */
    0x18, 0xfa, 0x11, 0x11, 0x2f, 0xf1, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x4b, 0xfd, 0x44,
    0x44, 0x5f, 0xf4, 0x10, 0x7f, 0x80, 0x0, 0x1,
    0xfe, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x7f, 0xea, 0xaa, 0xab, 0xfe, 0x0, 0x7,
    0xf8, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x7f, 0x80,
    0x0, 0x1, 0xfe, 0x0, 0x7, 0xf8, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x7f, 0x80, 0x0, 0x1, 0xfe,
    0x0,

    /* U+0127 "ħ" */
    0xc, 0xf1, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x3f, 0xf5, 0x33, 0x0, 0x0, 0xc,
    0xf6, 0xcf, 0xe9, 0x0, 0xc, 0xff, 0xca, 0xef,
    0xa0, 0xc, 0xf8, 0x0, 0x1f, 0xf0, 0xc, 0xf2,
    0x0, 0xc, 0xf1, 0xc, 0xf1, 0x0, 0xc, 0xf2,
    0xc, 0xf1, 0x0, 0xc, 0xf2, 0xc, 0xf1, 0x0,
    0xc, 0xf2, 0xc, 0xf1, 0x0, 0xc, 0xf2,

    /* U+0128 "Ĩ" */
    0xa, 0xe6, 0x1e, 0x3, 0xe3, 0xcf, 0x90, 0x1,
    0x0, 0x10, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x9f,
    0x50, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x9f, 0x50,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x9, 0xf5, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x9f, 0x50, 0x0,

    /* U+0129 "ĩ" */
    0xa, 0xe5, 0x96, 0x1f, 0x3d, 0xf2, 0x1, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10,
    0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf,
    0x10,

    /* U+012A "Ī" */
    0xbf, 0xff, 0x71, 0x22, 0x21, 0x0, 0x0, 0x0,
    0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f, 0x50, 0x9,
    0xf5, 0x0, 0x9f, 0x50, 0x9, 0xf5, 0x0, 0x9f,
    0x50, 0x9, 0xf5, 0x0, 0x9f, 0x50, 0x9, 0xf5,
    0x0,

    /* U+012B "ī" */
    0xff, 0xff, 0x41, 0x22, 0x20, 0xc, 0xf1, 0x0,
    0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf, 0x10, 0xc,
    0xf1, 0x0, 0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf,
    0x10,

    /* U+012C "Ĭ" */
    0x18, 0x0, 0x27, 0xe, 0x60, 0x9b, 0x4, 0xef,
    0xc2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x9f, 0x50, 0x0, 0x9f, 0x50, 0x0, 0x9f, 0x50,
    0x0, 0x9f, 0x50, 0x0, 0x9f, 0x50, 0x0, 0x9f,
    0x50, 0x0, 0x9f, 0x50, 0x0, 0x9f, 0x50, 0x0,
    0x9f, 0x50,

    /* U+012D "ĭ" */
    0xf, 0x40, 0xe4, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10,
    0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf,
    0x10,

    /* U+012E "Į" */
    0x9f, 0x59, 0xf5, 0x9f, 0x59, 0xf5, 0x9f, 0x59,
    0xf5, 0x9f, 0x59, 0xf5, 0x9f, 0x59, 0xf6, 0x6c,
    0xc, 0x90, 0x6f, 0xa0,

    /* U+012F "į" */
    0xb, 0xe2, 0xe, 0xf3, 0x1, 0x20, 0xc, 0xf1,
    0xc, 0xf1, 0xc, 0xf1, 0xc, 0xf1, 0xc, 0xf1,
    0xc, 0xf1, 0xc, 0xf1, 0xc, 0xf1, 0xa, 0x90,
    0xf, 0x40, 0x9, 0xf6,

    /* U+0130 "İ" */
    0x0, 0x8, 0xf4, 0x7f, 0x30, 0x0, 0x9f, 0x59,
    0xf5, 0x9f, 0x59, 0xf5, 0x9f, 0x59, 0xf5, 0x9f,
    0x59, 0xf5, 0x9f, 0x59, 0xf5,

    /* U+0131 "ı" */
    0xcf, 0x1c, 0xf1, 0xcf, 0x1c, 0xf1, 0xcf, 0x1c,
    0xf1, 0xcf, 0x1c, 0xf1,

    /* U+0132 "Ĳ" */
    0xbf, 0x40, 0x0, 0x7f, 0x8b, 0xf4, 0x0, 0x7,
    0xf8, 0xbf, 0x40, 0x0, 0x7f, 0x8b, 0xf4, 0x0,
    0x7, 0xf8, 0xbf, 0x40, 0x0, 0x7f, 0x87, 0xb2,
    0x0, 0x7, 0xf8, 0x0, 0x0, 0x0, 0x9f, 0x61,
    0x20, 0x0, 0x2f, 0xf1, 0xaf, 0xdb, 0xcf, 0xf8,
    0x1, 0x8d, 0xff, 0xc5, 0x0,

    /* U+0133 "ĳ" */
    0xb, 0xe2, 0x5, 0xf8, 0xe, 0xf3, 0x7, 0xfa,
    0x1, 0x20, 0x0, 0x30, 0xc, 0xf1, 0x5, 0xf8,
    0xc, 0xf1, 0x5, 0xf8, 0xc, 0xf1, 0x5, 0xf8,
    0xc, 0xf1, 0x5, 0xf8, 0xc, 0xf1, 0x5, 0xf8,
    0xc, 0xf1, 0x5, 0xf8, 0xc, 0xf1, 0x5, 0xf8,
    0xc, 0xf1, 0x5, 0xf8, 0x0, 0x0, 0x6, 0xf7,
    0x0, 0x8, 0x9e, 0xf3, 0x0, 0xb, 0xfe, 0x60,

    /* U+0134 "Ĵ" */
    0x0, 0x2, 0x86, 0x0, 0x0, 0x2e, 0xff, 0x90,
    0x0, 0x66, 0x2, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xf9, 0x0, 0x99, 0x9b, 0xf9,
    0x0, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0x5, 0xf8, 0x4, 0x30, 0x9, 0xf6,
    0xe, 0xfb, 0xbf, 0xf1, 0x3, 0xbf, 0xfc, 0x30,

    /* U+0135 "ĵ" */
    0x0, 0x2f, 0xf8, 0x0, 0xb, 0x82, 0xc4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x30, 0x0, 0xb,
    0xf3, 0x0, 0x0, 0xbf, 0x30, 0x0, 0xb, 0xf3,
    0x0, 0x0, 0xbf, 0x30, 0x0, 0xb, 0xf3, 0x0,
    0x0, 0xbf, 0x30, 0x0, 0xb, 0xf3, 0x0, 0x0,
    0xbf, 0x20, 0xa, 0x9f, 0xd0, 0x3, 0xef, 0xc3,
    0x0,

    /* U+0136 "Ķ" */
    0x9f, 0x50, 0x0, 0x1d, 0xf4, 0x9, 0xf5, 0x0,
    0x1d, 0xf4, 0x0, 0x9f, 0x50, 0x1d, 0xf4, 0x0,
    0x9, 0xf5, 0x1d, 0xf4, 0x0, 0x0, 0x9f, 0x7e,
    0xfa, 0x0, 0x0, 0x9, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x9f, 0xf5, 0x5f, 0xe2, 0x0, 0x9, 0xf9,
    0x0, 0x7f, 0xd0, 0x0, 0x9f, 0x50, 0x0, 0x9f,
    0xb0, 0x9, 0xf5, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x20,
    0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0,

    /* U+0137 "ķ" */
    0xcf, 0x10, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0x8f, 0xb0, 0xcf, 0x10, 0x9f, 0xb0, 0xc, 0xf2,
    0xbf, 0xb0, 0x0, 0xcf, 0xdf, 0xf2, 0x0, 0xc,
    0xff, 0xff, 0xc0, 0x0, 0xcf, 0xa0, 0xbf, 0x90,
    0xc, 0xf1, 0x1, 0xdf, 0x60, 0xcf, 0x10, 0x2,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x20, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0xd, 0x30, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,

    /* U+0138 "ĸ" */
    0xcf, 0x10, 0x8, 0xfb, 0xc, 0xf1, 0x9, 0xfb,
    0x0, 0xcf, 0x2b, 0xfb, 0x0, 0xc, 0xfd, 0xff,
    0x20, 0x0, 0xcf, 0xff, 0xfc, 0x0, 0xc, 0xfa,
    0xb, 0xf9, 0x0, 0xcf, 0x10, 0x1d, 0xf6, 0xc,
    0xf1, 0x0, 0x2f, 0xf3,

    /* U+0139 "Ĺ" */
    0x0, 0x77, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0,
    0x7f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0xca, 0xaa, 0xa8, 0x9f, 0xff, 0xff, 0xfd,

    /* U+013A "ĺ" */
    0x0, 0x8f, 0x70, 0x9, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc,
    0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0,
    0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1,
    0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc,
    0xf1, 0x0,

    /* U+013B "Ļ" */
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0xca, 0xaa, 0xa8, 0x9f, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x70, 0x0,
    0x0, 0x6, 0xf2, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0x10, 0x0,

    /* U+013C "ļ" */
    0xcf, 0x1c, 0xf1, 0xcf, 0x1c, 0xf1, 0xcf, 0x1c,
    0xf1, 0xcf, 0x1c, 0xf1, 0xcf, 0x1c, 0xf1, 0xcf,
    0x10, 0x0, 0x36, 0x9, 0xf0, 0x5a, 0x1, 0x0,

    /* U+013D "Ľ" */
    0x0, 0x0, 0x7, 0xf0, 0x9f, 0x50, 0x7, 0xd0,
    0x9f, 0x50, 0x7, 0xb0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0xca, 0xaa, 0xa8,
    0x9f, 0xff, 0xff, 0xfd,

    /* U+013E "ľ" */
    0x0, 0x9, 0xbc, 0xf1, 0xab, 0xcf, 0x1a, 0xac,
    0xf1, 0x0, 0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf,
    0x10, 0xc, 0xf1, 0x0, 0xcf, 0x10, 0xc, 0xf1,
    0x0, 0xcf, 0x10, 0xc, 0xf1, 0x0,

    /* U+013F "Ŀ" */
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x1d, 0xb0,
    0x9f, 0x50, 0x1f, 0xd0, 0x9f, 0x50, 0x1, 0x10,
    0x9f, 0x50, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x9f, 0xca, 0xaa, 0xa8, 0x9f, 0xff, 0xff, 0xfd,

    /* U+0140 "ŀ" */
    0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10,
    0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf,
    0x1c, 0xd0, 0xcf, 0x1c, 0xd0, 0xcf, 0x10, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10,
    0x0,

    /* U+0141 "Ł" */
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x0, 0x7f, 0x80,
    0x0, 0x0, 0x0, 0x7f, 0x9a, 0x90, 0x0, 0x0,
    0x7f, 0xfd, 0x30, 0x0, 0x0, 0xef, 0xf0, 0x0,
    0x0, 0xb, 0xff, 0x80, 0x0, 0x0, 0x4, 0x9f,
    0x80, 0x0, 0x0, 0x0, 0x7f, 0x80, 0x0, 0x0,
    0x0, 0x7f, 0xda, 0xaa, 0xaa, 0x0, 0x7f, 0xff,
    0xff, 0xff,

    /* U+0142 "ł" */
    0x0, 0xaf, 0x40, 0x0, 0xaf, 0x40, 0x0, 0xaf,
    0x40, 0x0, 0xaf, 0x52, 0x0, 0xaf, 0xfa, 0x0,
    0xff, 0xd0, 0x1c, 0xff, 0x40, 0x8, 0xcf, 0x40,
    0x0, 0xaf, 0x40, 0x0, 0xaf, 0x40, 0x0, 0xaf,
    0x40,

    /* U+0143 "Ń" */
    0x0, 0x0, 0x2, 0x86, 0x0, 0x0, 0x0, 0x1e,
    0xd2, 0x0, 0x0, 0x0, 0xcd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x60, 0x0, 0x3,
    0xfb, 0x9f, 0xf4, 0x0, 0x3, 0xfb, 0x9f, 0xfe,
    0x20, 0x3, 0xfb, 0x9f, 0x9f, 0xd0, 0x3, 0xfb,
    0x9f, 0x56, 0xfb, 0x3, 0xfb, 0x9f, 0x50, 0x9f,
    0x83, 0xfb, 0x9f, 0x50, 0xb, 0xf8, 0xfb, 0x9f,
    0x50, 0x1, 0xdf, 0xfb, 0x9f, 0x50, 0x0, 0x2f,
    0xfb, 0x9f, 0x50, 0x0, 0x4, 0xfb,

    /* U+0144 "ń" */
    0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x8e, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0xcf,
    0xe9, 0x0, 0xcf, 0xfc, 0xae, 0xfa, 0xc, 0xf8,
    0x0, 0x1f, 0xf0, 0xcf, 0x20, 0x0, 0xcf, 0x1c,
    0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0, 0xcf,
    0x2c, 0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0,
    0xcf, 0x20,

    /* U+0145 "Ņ" */
    0x9f, 0x60, 0x0, 0x3, 0xfb, 0x9f, 0xf4, 0x0,
    0x3, 0xfb, 0x9f, 0xfe, 0x20, 0x3, 0xfb, 0x9f,
    0x9f, 0xd0, 0x3, 0xfb, 0x9f, 0x56, 0xfb, 0x3,
    0xfb, 0x9f, 0x50, 0x9f, 0x83, 0xfb, 0x9f, 0x50,
    0xb, 0xf8, 0xfb, 0x9f, 0x50, 0x1, 0xdf, 0xfb,
    0x9f, 0x50, 0x0, 0x2f, 0xfb, 0x9f, 0x50, 0x0,
    0x4, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x45, 0x0, 0x0, 0x0, 0x0, 0xce, 0x0,
    0x0, 0x0, 0x0, 0x78, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0,

    /* U+0146 "ņ" */
    0xcf, 0x4c, 0xfe, 0x90, 0xc, 0xff, 0xca, 0xef,
    0xa0, 0xcf, 0x80, 0x1, 0xff, 0xc, 0xf2, 0x0,
    0xc, 0xf1, 0xcf, 0x10, 0x0, 0xcf, 0x2c, 0xf1,
    0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0, 0xcf, 0x2c,
    0xf1, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0x0, 0x0, 0x5a, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0,

    /* U+0147 "Ň" */
    0x0, 0x47, 0x0, 0x65, 0x0, 0x0, 0x1e, 0xba,
    0xf2, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x60, 0x0, 0x3,
    0xfb, 0x9f, 0xf4, 0x0, 0x3, 0xfb, 0x9f, 0xfe,
    0x20, 0x3, 0xfb, 0x9f, 0x9f, 0xd0, 0x3, 0xfb,
    0x9f, 0x56, 0xfb, 0x3, 0xfb, 0x9f, 0x50, 0x9f,
    0x83, 0xfb, 0x9f, 0x50, 0xb, 0xf8, 0xfb, 0x9f,
    0x50, 0x1, 0xdf, 0xfb, 0x9f, 0x50, 0x0, 0x2f,
    0xfb, 0x9f, 0x50, 0x0, 0x4, 0xfb,

    /* U+0148 "ň" */
    0x3, 0xf7, 0x3e, 0x80, 0x0, 0x3, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0xcf,
    0xe9, 0x0, 0xcf, 0xfc, 0xae, 0xfa, 0xc, 0xf8,
    0x0, 0x1f, 0xf0, 0xcf, 0x20, 0x0, 0xcf, 0x1c,
    0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0, 0xcf,
    0x2c, 0xf1, 0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0,
    0xcf, 0x20,

    /* U+0149 "ŉ" */
    0xd, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0x8c, 0xf4, 0xcf, 0xe9, 0x0,
    0x7, 0x1c, 0xff, 0xa8, 0xdf, 0x90, 0x0, 0xc,
    0xf7, 0x0, 0x1f, 0xf0, 0x0, 0xc, 0xf2, 0x0,
    0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1,
    0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc,
    0xf1, 0x0, 0xc, 0xf1, 0x0, 0xc, 0xf1, 0x0,
    0xc, 0xf1,

    /* U+014A "Ŋ" */
    0x9f, 0x70, 0x0, 0x3, 0xfb, 0x9f, 0xf4, 0x0,
    0x3, 0xfb, 0x9f, 0xff, 0x30, 0x3, 0xfb, 0x9f,
    0x9f, 0xe1, 0x3, 0xfb, 0x9f, 0x56, 0xfc, 0x3,
    0xfb, 0x9f, 0x50, 0x9f, 0xb3, 0xfb, 0x9f, 0x50,
    0xb, 0xfc, 0xfb, 0x9f, 0x50, 0x1, 0xdf, 0xfb,
    0x9f, 0x50, 0x0, 0x2f, 0xfb, 0x9f, 0x50, 0x0,
    0x7, 0xfb, 0x0, 0x0, 0x30, 0x6, 0xf9, 0x0,
    0x3, 0xfc, 0xbf, 0xf3, 0x0, 0x0, 0x9e, 0xfc,
    0x40,

    /* U+014B "ŋ" */
    0xcf, 0x4c, 0xfe, 0x90, 0xc, 0xff, 0xca, 0xef,
    0xa0, 0xcf, 0x80, 0x1, 0xff, 0xc, 0xf2, 0x0,
    0xc, 0xf1, 0xcf, 0x10, 0x0, 0xcf, 0x2c, 0xf1,
    0x0, 0xc, 0xf2, 0xcf, 0x10, 0x0, 0xcf, 0x2c,
    0xf1, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x0, 0xcf,
    0x10, 0x0, 0x1a, 0xaf, 0xd0, 0x0, 0x3, 0xef,
    0xc2, 0x0,

    /* U+014C "Ō" */
    0x0, 0x4, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x22, 0x22, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc,
    0x20, 0x0, 0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0,
    0x7, 0xfb, 0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe,
    0x4f, 0xa0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1,
    0x0, 0x0, 0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf4, 0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0,

    /* U+014D "ō" */
    0x0, 0xcf, 0xff, 0xf6, 0x0, 0x0, 0x12, 0x22,
    0x20, 0x0, 0x0, 0x5c, 0xfe, 0xa3, 0x0, 0x8,
    0xfe, 0x9a, 0xff, 0x30, 0x2f, 0xd1, 0x0, 0x4f,
    0xc0, 0x6f, 0x80, 0x0, 0xd, 0xf0, 0x5f, 0x80,
    0x0, 0xd, 0xf0, 0x2f, 0xe1, 0x0, 0x5f, 0xc0,
    0x8, 0xfe, 0xab, 0xff, 0x30, 0x0, 0x5c, 0xfe,
    0xb2, 0x0,

    /* U+014E "Ŏ" */
    0x0, 0x1, 0x70, 0x2, 0x60, 0x0, 0x0, 0x1,
    0xf5, 0xa, 0xa0, 0x0, 0x0, 0x0, 0x5e, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+014F "ŏ" */
    0x0, 0xaa, 0x1, 0xf4, 0x0, 0x0, 0x2c, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+0150 "Ő" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xc3, 0xfa, 0x0, 0x0, 0x0, 0x8e, 0x1b,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+0151 "ő" */
    0x0, 0x6, 0xf5, 0x9f, 0x30, 0x0, 0x1e, 0x93,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+0152 "Œ" */
    0x0, 0x6, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xc, 0xff, 0xba, 0xaf, 0xfb, 0xaa, 0xaa,
    0x90, 0xa, 0xfb, 0x10, 0x0, 0xaf, 0x40, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf5, 0x4, 0xfa, 0x0, 0x0, 0xa, 0xfc,
    0x99, 0x99, 0x30, 0x1f, 0xe0, 0x0, 0x0, 0xaf,
    0x40, 0x0, 0x0, 0x0, 0xaf, 0xb1, 0x0, 0xa,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfb, 0xaa,
    0xff, 0xba, 0xaa, 0xaa, 0x10, 0x0, 0x6b, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf3,

    /* U+0153 "œ" */
    0x0, 0x5c, 0xfe, 0xa2, 0x9, 0xef, 0xd7, 0x0,
    0x8, 0xfe, 0x9a, 0xff, 0xef, 0xb9, 0xdf, 0xa0,
    0x2f, 0xd1, 0x0, 0x4f, 0xf8, 0x0, 0xa, 0xf3,
    0x6f, 0x80, 0x0, 0xf, 0xff, 0xff, 0xff, 0xf6,
    0x5f, 0x80, 0x0, 0xf, 0xf4, 0x33, 0x33, 0x31,
    0x2f, 0xe1, 0x0, 0x5f, 0xf8, 0x0, 0x4, 0x10,
    0x8, 0xfe, 0xab, 0xff, 0xef, 0xd9, 0xcf, 0xa0,
    0x0, 0x5c, 0xfe, 0xa2, 0x8, 0xdf, 0xea, 0x10,

    /* U+0154 "Ŕ" */
    0x0, 0x0, 0x6, 0x82, 0x0, 0x0, 0x0, 0x7f,
    0x80, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfe, 0xc5,
    0x0, 0x9f, 0xfa, 0xab, 0xff, 0x70, 0x9f, 0x50,
    0x0, 0x2f, 0xf0, 0x9f, 0x50, 0x0, 0xd, 0xf2,
    0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f, 0xe9, 0x9a,
    0xff, 0x80, 0x9f, 0xff, 0xff, 0xf6, 0x0, 0x9f,
    0x50, 0x8, 0xf9, 0x0, 0x9f, 0x50, 0x0, 0xcf,
    0x40, 0x9f, 0x50, 0x0, 0x2f, 0xe1,

    /* U+0155 "ŕ" */
    0x0, 0x4f, 0xb1, 0x3, 0xe8, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x4c, 0xd0, 0xcf, 0xfd, 0x90, 0xcf,
    0x90, 0x0, 0xcf, 0x30, 0x0, 0xcf, 0x10, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10,
    0x0,

    /* U+0156 "Ŗ" */
    0x9f, 0xff, 0xfe, 0xc5, 0x0, 0x9f, 0xfa, 0xab,
    0xff, 0x70, 0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f,
    0x50, 0x0, 0xd, 0xf2, 0x9f, 0x50, 0x0, 0x2f,
    0xf0, 0x9f, 0xe9, 0x9a, 0xff, 0x80, 0x9f, 0xff,
    0xff, 0xf6, 0x0, 0x9f, 0x50, 0x8, 0xf9, 0x0,
    0x9f, 0x50, 0x0, 0xcf, 0x40, 0x9f, 0x50, 0x0,
    0x2f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x71, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0,

    /* U+0157 "ŗ" */
    0xcf, 0x4c, 0xdc, 0xff, 0xd9, 0xcf, 0x90, 0xc,
    0xf3, 0x0, 0xcf, 0x10, 0xc, 0xf1, 0x0, 0xcf,
    0x10, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x3, 0x60,
    0x0, 0x9f, 0x0, 0x5, 0xa0, 0x0, 0x10, 0x0,
    0x0,

    /* U+0158 "Ř" */
    0x0, 0x74, 0x2, 0x82, 0x0, 0x0, 0x7f, 0x8e,
    0xa0, 0x0, 0x0, 0xa, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfe, 0xc5,
    0x0, 0x9f, 0xfa, 0xab, 0xff, 0x70, 0x9f, 0x50,
    0x0, 0x2f, 0xf0, 0x9f, 0x50, 0x0, 0xd, 0xf2,
    0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f, 0xe9, 0x9a,
    0xff, 0x80, 0x9f, 0xff, 0xff, 0xf6, 0x0, 0x9f,
    0x50, 0x8, 0xf9, 0x0, 0x9f, 0x50, 0x0, 0xcf,
    0x40, 0x9f, 0x50, 0x0, 0x2f, 0xe1,

    /* U+0159 "ř" */
    0xc, 0xc2, 0xbc, 0x10, 0xb, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x4c, 0xd0, 0xc, 0xff,
    0xd9, 0x0, 0xcf, 0x90, 0x0, 0xc, 0xf3, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0xcf, 0x10, 0x0, 0xc, 0xf1, 0x0, 0x0,

    /* U+015A "Ś" */
    0x0, 0x0, 0x5, 0x84, 0x0, 0x0, 0x6, 0xf8,
    0x0, 0x0, 0x1, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8d, 0xfe, 0xc7, 0x10, 0xcf,
    0xda, 0xad, 0xf3, 0x3f, 0xd0, 0x0, 0x2, 0x2,
    0xfe, 0x20, 0x0, 0x0, 0x7, 0xff, 0xd9, 0x50,
    0x0, 0x2, 0x7b, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x60, 0x0, 0x2, 0xfd, 0x4f, 0xfb,
    0x9a, 0xef, 0x70, 0x5b, 0xef, 0xfc, 0x50,

    /* U+015B "ś" */
    0x0, 0x1, 0xde, 0x30, 0x0, 0x1d, 0xb1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xfd, 0x90,
    0x3f, 0xe8, 0x8b, 0xa0, 0x6f, 0x80, 0x0, 0x0,
    0x2f, 0xfd, 0x96, 0x10, 0x2, 0x7b, 0xef, 0xe1,
    0x1, 0x0, 0x9, 0xf4, 0x6f, 0xb9, 0x9f, 0xf1,
    0x3a, 0xef, 0xfb, 0x30,

    /* U+015C "Ŝ" */
    0x0, 0x2, 0x87, 0x0, 0x0, 0x2, 0xef, 0xfb,
    0x0, 0x0, 0x66, 0x1, 0x73, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8d, 0xfe, 0xc7, 0x10, 0xcf,
    0xda, 0xad, 0xf3, 0x3f, 0xd0, 0x0, 0x2, 0x2,
    0xfe, 0x20, 0x0, 0x0, 0x7, 0xff, 0xd9, 0x50,
    0x0, 0x2, 0x7b, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x60, 0x0, 0x2, 0xfd, 0x4f, 0xfb,
    0x9a, 0xef, 0x70, 0x5b, 0xef, 0xfc, 0x50,

    /* U+015D "ŝ" */
    0x0, 0x9f, 0xf3, 0x0, 0x8, 0xc1, 0x5d, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xfd, 0x90,
    0x3f, 0xe8, 0x8b, 0xa0, 0x6f, 0x80, 0x0, 0x0,
    0x2f, 0xfd, 0x96, 0x10, 0x2, 0x7b, 0xef, 0xe1,
    0x1, 0x0, 0x9, 0xf4, 0x6f, 0xb9, 0x9f, 0xf1,
    0x3a, 0xef, 0xfb, 0x30,

    /* U+015E "Ş" */
    0x0, 0x8d, 0xfe, 0xc7, 0x10, 0xcf, 0xda, 0xad,
    0xf3, 0x3f, 0xd0, 0x0, 0x2, 0x2, 0xfe, 0x20,
    0x0, 0x0, 0x7, 0xff, 0xd9, 0x50, 0x0, 0x2,
    0x7b, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x60, 0x0, 0x2, 0xfd, 0x4f, 0xfb, 0x9a, 0xef,
    0x70, 0x5b, 0xef, 0xfc, 0x50, 0x0, 0x0, 0xe8,
    0x0, 0x0, 0x0, 0x2, 0xe6, 0x0, 0x0, 0xb,
    0xec, 0x20, 0x0,

    /* U+015F "ş" */
    0x4, 0xcf, 0xfd, 0x90, 0x3f, 0xe8, 0x8b, 0xa0,
    0x6f, 0x80, 0x0, 0x0, 0x2f, 0xfd, 0x96, 0x10,
    0x2, 0x7b, 0xef, 0xe1, 0x1, 0x0, 0x9, 0xf4,
    0x6f, 0xb9, 0x9f, 0xf1, 0x3a, 0xef, 0xfb, 0x30,
    0x0, 0xe, 0x70, 0x0, 0x0, 0x12, 0xf5, 0x0,
    0x0, 0xbe, 0xc1, 0x0,

    /* U+0160 "Š" */
    0x0, 0x66, 0x0, 0x73, 0x0, 0x2, 0xe9, 0xcb,
    0x0, 0x0, 0x2, 0xc9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8d, 0xfe, 0xc7, 0x10, 0xcf,
    0xda, 0xad, 0xf3, 0x3f, 0xd0, 0x0, 0x2, 0x2,
    0xfe, 0x20, 0x0, 0x0, 0x7, 0xff, 0xd9, 0x50,
    0x0, 0x2, 0x7b, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x60, 0x0, 0x2, 0xfd, 0x4f, 0xfb,
    0x9a, 0xef, 0x70, 0x5b, 0xef, 0xfc, 0x50,

    /* U+0161 "š" */
    0x8, 0xe3, 0x7f, 0x30, 0x0, 0x8f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xcf, 0xfd, 0x90,
    0x3f, 0xe8, 0x8b, 0xa0, 0x6f, 0x80, 0x0, 0x0,
    0x2f, 0xfd, 0x96, 0x10, 0x2, 0x7b, 0xef, 0xe1,
    0x1, 0x0, 0x9, 0xf4, 0x6f, 0xb9, 0x9f, 0xf1,
    0x3a, 0xef, 0xfb, 0x30,

    /* U+0162 "Ţ" */
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xaa, 0xcf, 0xca,
    0xa9, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0, 0x7f,
    0x70, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0x70, 0x0, 0x0, 0x3, 0xf2,
    0x0, 0x0, 0x0, 0x16, 0xf0, 0x0, 0x0, 0x1e,
    0xe9, 0x0, 0x0,

    /* U+0163 "ţ" */
    0x8, 0xf5, 0x0, 0x0, 0x8f, 0x50, 0x0, 0xdf,
    0xff, 0xf8, 0x6, 0xff, 0xd7, 0x30, 0x8, 0xf5,
    0x0, 0x0, 0x8f, 0x50, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x7f, 0x60, 0x0, 0x4, 0xfe, 0x98, 0x0,
    0x7, 0xff, 0xb0, 0x0, 0xd, 0x70, 0x0, 0x0,
    0x2e, 0x50, 0x0, 0xbe, 0xc1, 0x0,

    /* U+0164 "Ť" */
    0x1, 0x73, 0x3, 0x71, 0x0, 0x8, 0xf7, 0xf9,
    0x0, 0x0, 0xc, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xaa,
    0xcf, 0xca, 0xa9, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7,
    0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x0, 0x7,
    0xf7, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0,

    /* U+0165 "ť" */
    0x0, 0x0, 0x58, 0x0, 0x0, 0x8, 0xc0, 0x7,
    0xe5, 0x8b, 0x0, 0x8f, 0x50, 0x0, 0xdf, 0xff,
    0xf8, 0x6, 0xff, 0xd7, 0x30, 0x8, 0xf5, 0x0,
    0x0, 0x8f, 0x50, 0x0, 0x8, 0xf5, 0x0, 0x0,
    0x7f, 0x60, 0x0, 0x4, 0xfe, 0x98, 0x0, 0x7,
    0xef, 0xb0,

    /* U+0166 "Ŧ" */
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xaa, 0xcf, 0xca,
    0xa9, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x12, 0x9f,
    0x92, 0x10, 0x9, 0xff, 0xff, 0xfa, 0x0, 0x35,
    0xcf, 0xc5, 0x30, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0x70, 0x0,

    /* U+0167 "ŧ" */
    0x7, 0xf7, 0x0, 0x0, 0x7f, 0x70, 0x0, 0xcf,
    0xff, 0xf9, 0x5, 0xef, 0xe7, 0x40, 0x7, 0xf7,
    0x0, 0x9, 0xff, 0xff, 0x60, 0x1a, 0xfa, 0x31,
    0x0, 0x6f, 0x70, 0x0, 0x2, 0xfe, 0x99, 0x0,
    0x6, 0xef, 0xc0,

    /* U+0168 "Ũ" */
    0x0, 0x6e, 0xff, 0xf6, 0x0, 0x0, 0x12, 0x22,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xaf, 0x40, 0x0, 0x6, 0xf8, 0x9f, 0x60, 0x0,
    0x9, 0xf6, 0x5f, 0xd1, 0x0, 0x2f, 0xf2, 0xc,
    0xff, 0xcc, 0xff, 0x90, 0x0, 0x7d, 0xff, 0xc6,
    0x0,

    /* U+0169 "ũ" */
    0x2, 0xdd, 0x46, 0xb0, 0x8, 0x94, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+016A "Ū" */
    0x0, 0xaf, 0xff, 0xf8, 0x0, 0x0, 0x12, 0x22,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xaf, 0x40, 0x0, 0x6, 0xf8, 0x9f, 0x60, 0x0,
    0x9, 0xf6, 0x5f, 0xd1, 0x0, 0x2f, 0xf2, 0xc,
    0xff, 0xcc, 0xff, 0x90, 0x0, 0x7d, 0xff, 0xc6,
    0x0,

    /* U+016B "ū" */
    0x8, 0xff, 0xff, 0xa0, 0x1, 0x22, 0x22, 0x10,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x10, 0x0, 0xff, 0xbf, 0x50, 0x5, 0xff,
    0x5f, 0xfb, 0xbf, 0xff, 0x6, 0xdf, 0xd7, 0xcf,

    /* U+016C "Ŭ" */
    0x0, 0x44, 0x0, 0x53, 0x0, 0x0, 0x6d, 0x11,
    0xe4, 0x0, 0x0, 0xa, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+016D "ŭ" */
    0x6, 0xe1, 0xc, 0x80, 0x0, 0xaf, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+016E "Ů" */
    0x0, 0x2, 0xba, 0x10, 0x0, 0x0, 0xa, 0x24,
    0x80, 0x0, 0x0, 0xb, 0x24, 0x90, 0x0, 0x0,
    0x2, 0xba, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0,
    0x6, 0xf8, 0xaf, 0x40, 0x0, 0x6, 0xf8, 0x9f,
    0x60, 0x0, 0x9, 0xf6, 0x5f, 0xd1, 0x0, 0x2f,
    0xf2, 0xc, 0xff, 0xcc, 0xff, 0x90, 0x0, 0x7d,
    0xff, 0xc6, 0x0,

    /* U+016F "ů" */
    0x0, 0x2b, 0xb3, 0x0, 0x0, 0x93, 0x1b, 0x0,
    0x0, 0x2b, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x10, 0x0, 0xff, 0xbf, 0x50, 0x5, 0xff,
    0x5f, 0xfb, 0xbf, 0xff, 0x6, 0xdf, 0xd7, 0xcf,

    /* U+0170 "Ű" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf7,
    0x8f, 0x40, 0x0, 0xd, 0xa2, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+0171 "ű" */
    0x0, 0x2f, 0x95, 0xf6, 0x0, 0xcc, 0x1e, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+0172 "Ų" */
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0,
    0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf,
    0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xaf, 0x40, 0x0, 0x6, 0xf8, 0x9f, 0x60,
    0x0, 0x9, 0xf6, 0x5f, 0xd1, 0x0, 0x2f, 0xf2,
    0xc, 0xff, 0xcc, 0xff, 0x70, 0x0, 0x7d, 0xff,
    0xb4, 0x0, 0x0, 0x3, 0xe3, 0x0, 0x0, 0x0,
    0x9, 0xc0, 0x10, 0x0, 0x0, 0x3, 0xdf, 0x80,
    0x0,

    /* U+0173 "ų" */
    0xdf, 0x0, 0x0, 0xef, 0xd, 0xf0, 0x0, 0xe,
    0xf0, 0xdf, 0x0, 0x0, 0xef, 0xd, 0xf0, 0x0,
    0xe, 0xf0, 0xdf, 0x10, 0x0, 0xff, 0xb, 0xf5,
    0x0, 0x5f, 0xf0, 0x5f, 0xfb, 0xbf, 0xff, 0x0,
    0x6d, 0xfd, 0x7f, 0xf0, 0x0, 0x0, 0x9, 0xc2,
    0x0, 0x0, 0x0, 0xf6, 0x10, 0x0, 0x0, 0x8,
    0xfe, 0x20,

    /* U+0174 "Ŵ" */
    0x0, 0x0, 0x0, 0x3, 0x85, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdb, 0x9, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0x90, 0x0, 0x6, 0xfb, 0x0,
    0x0, 0x4f, 0x91, 0xfe, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0xa, 0xf3, 0xb, 0xf4, 0x0, 0x2f, 0xff,
    0x60, 0x0, 0xfe, 0x0, 0x6f, 0xa0, 0x7, 0xf5,
    0xfc, 0x0, 0x5f, 0x80, 0x0, 0xff, 0x0, 0xde,
    0xb, 0xf1, 0xb, 0xf3, 0x0, 0xa, 0xf5, 0x3f,
    0x90, 0x6f, 0x71, 0xfd, 0x0, 0x0, 0x5f, 0xa9,
    0xf3, 0x0, 0xfc, 0x6f, 0x70, 0x0, 0x0, 0xff,
    0xfd, 0x0, 0xa, 0xfe, 0xf2, 0x0, 0x0, 0xa,
    0xff, 0x80, 0x0, 0x5f, 0xfc, 0x0, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0xff, 0x70, 0x0,

    /* U+0175 "ŵ" */
    0x0, 0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xee, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x32, 0x0, 0x32, 0x0, 0x0, 0xcf, 0x0, 0x1,
    0xfe, 0x0, 0x2, 0xf8, 0x6f, 0x50, 0x6, 0xff,
    0x40, 0x7, 0xf2, 0xf, 0xb0, 0xc, 0xff, 0xa0,
    0xd, 0xc0, 0xa, 0xf1, 0x2f, 0x8b, 0xf0, 0x3f,
    0x60, 0x4, 0xf6, 0x8f, 0x25, 0xf5, 0x9f, 0x10,
    0x0, 0xec, 0xec, 0x0, 0xec, 0xeb, 0x0, 0x0,
    0x8f, 0xf5, 0x0, 0x9f, 0xf5, 0x0, 0x0, 0x2f,
    0xf0, 0x0, 0x3f, 0xf0, 0x0,

    /* U+0176 "Ŷ" */
    0x0, 0x0, 0x5, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf8, 0x0, 0x0, 0x0, 0x7, 0xf3, 0x4f,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x3f, 0xb0, 0x2, 0xfe,
    0x0, 0x0, 0xdf, 0x10, 0x0, 0x8f, 0x90, 0x8,
    0xf6, 0x0, 0x0, 0xd, 0xf4, 0x3f, 0xc0, 0x0,
    0x0, 0x3, 0xfe, 0xef, 0x20, 0x0, 0x0, 0x0,
    0x9f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0,

    /* U+0177 "ŷ" */
    0x0, 0x4, 0xff, 0x80, 0x0, 0x0, 0x3c, 0x41,
    0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0x0, 0x4, 0xf8, 0x6, 0xf8, 0x0, 0xb,
    0xf1, 0x0, 0xee, 0x0, 0x2f, 0xa0, 0x0, 0x8f,
    0x50, 0x9f, 0x30, 0x0, 0x1f, 0xc0, 0xfc, 0x0,
    0x0, 0xa, 0xfa, 0xf5, 0x0, 0x0, 0x3, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xdf, 0x10, 0x0, 0xc, 0xac, 0xf8, 0x0,
    0x0, 0x1a, 0xee, 0x90, 0x0, 0x0,

    /* U+0178 "Ÿ" */
    0x0, 0x3, 0xf3, 0x4e, 0x20, 0x0, 0x0, 0x2,
    0xb2, 0x3b, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf4, 0x0, 0x0, 0x3f, 0xb0,
    0x3, 0xfd, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x8f,
    0x80, 0x7, 0xf7, 0x0, 0x0, 0xe, 0xf2, 0x2f,
    0xc0, 0x0, 0x0, 0x4, 0xfc, 0xcf, 0x30, 0x0,
    0x0, 0x0, 0xaf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,

    /* U+0179 "Ź" */
    0x0, 0x0, 0x2, 0x86, 0x0, 0x0, 0x0, 0x1d,
    0xe2, 0x0, 0x0, 0x0, 0xbd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf6, 0x2a, 0xaa, 0xaa, 0xcf, 0xf2, 0x0, 0x0,
    0x1, 0xef, 0x50, 0x0, 0x0, 0xc, 0xf7, 0x0,
    0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0, 0x5,
    0xfe, 0x10, 0x0, 0x0, 0x3f, 0xfc, 0xaa, 0xaa,
    0xa5, 0x6f, 0xff, 0xff, 0xff, 0xf9,

    /* U+017A "ź" */
    0x0, 0x0, 0xbf, 0x50, 0x0, 0xa, 0xc2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5,
    0x27, 0x77, 0x9f, 0xe2, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0xa, 0xf6, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x5, 0xfc, 0x0, 0x0, 0x3f, 0xf9, 0x77, 0x73,
    0x7f, 0xff, 0xff, 0xf7,

    /* U+017B "Ż" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0x10, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf6, 0x2a, 0xaa, 0xaa, 0xcf, 0xf2, 0x0, 0x0,
    0x1, 0xef, 0x50, 0x0, 0x0, 0xc, 0xf7, 0x0,
    0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0, 0x5,
    0xfe, 0x10, 0x0, 0x0, 0x3f, 0xfc, 0xaa, 0xaa,
    0xa5, 0x6f, 0xff, 0xff, 0xff, 0xf9,

    /* U+017C "ż" */
    0x0, 0xc, 0xd0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5,
    0x27, 0x77, 0x9f, 0xe2, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0xa, 0xf6, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x5, 0xfc, 0x0, 0x0, 0x3f, 0xf9, 0x77, 0x73,
    0x7f, 0xff, 0xff, 0xf7,

    /* U+017D "Ž" */
    0x0, 0x47, 0x0, 0x66, 0x0, 0x0, 0x1e, 0xca,
    0xf3, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf6, 0x2a, 0xaa, 0xaa, 0xcf, 0xf2, 0x0, 0x0,
    0x1, 0xef, 0x50, 0x0, 0x0, 0xc, 0xf7, 0x0,
    0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0, 0x5,
    0xfe, 0x10, 0x0, 0x0, 0x3f, 0xfc, 0xaa, 0xaa,
    0xa5, 0x6f, 0xff, 0xff, 0xff, 0xf9,

    /* U+017E "ž" */
    0x5, 0xf5, 0x5f, 0x60, 0x0, 0x5f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5,
    0x27, 0x77, 0x9f, 0xe2, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0xa, 0xf6, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x5, 0xfc, 0x0, 0x0, 0x3f, 0xf9, 0x77, 0x73,
    0x7f, 0xff, 0xff, 0xf7,

    /* U+017F "ſ" */
    0x0, 0x4d, 0xfd, 0x0, 0x1f, 0xf8, 0x80, 0x5,
    0xf9, 0x0, 0xa, 0xff, 0x80, 0x0, 0x5d, 0xf8,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x5, 0xf8, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x5, 0xf8, 0x0, 0x0,
    0x5f, 0x80, 0x0, 0x5, 0xf8, 0x0, 0x0,

    /* U+018F "Ə" */
    0x0, 0x17, 0xce, 0xfd, 0x81, 0x0, 0x1, 0xef,
    0xeb, 0xcf, 0xfe, 0x20, 0x0, 0x43, 0x0, 0x1,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf5,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x3f, 0xf8,
    0x88, 0x88, 0x8e, 0xf8, 0xf, 0xf1, 0x0, 0x0,
    0xa, 0xf5, 0x8, 0xfb, 0x10, 0x0, 0x6f, 0xd0,
    0x0, 0xaf, 0xea, 0xad, 0xfe, 0x20, 0x0, 0x5,
    0xcf, 0xfd, 0x81, 0x0,

    /* U+0192 "ƒ" */
    0x0, 0x0, 0x2, 0xbf, 0xe3, 0x0, 0x0, 0xe,
    0xf9, 0x90, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0,
    0xd, 0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xb7,
    0x30, 0x0, 0x0, 0xbf, 0x10, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x3, 0xf9,
    0x0, 0x0, 0x0, 0x5, 0xf8, 0x0, 0x0, 0x0,
    0x8, 0xf5, 0x0, 0x0, 0xa, 0x9f, 0xe0, 0x0,
    0x0, 0x2e, 0xfc, 0x30, 0x0, 0x0,

    /* U+01A0 "Ơ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xac, 0x0, 0x5, 0xbe, 0xff, 0xff, 0xf5,
    0x0, 0xbf, 0xfd, 0xbf, 0xff, 0x60, 0x9, 0xfc,
    0x20, 0x0, 0x5f, 0xf2, 0x1f, 0xf1, 0x0, 0x0,
    0x7, 0xfa, 0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe,
    0x4f, 0xa0, 0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1,
    0x0, 0x0, 0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf4, 0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60,
    0x0, 0x5, 0xbe, 0xfe, 0xa3, 0x0,

    /* U+01A1 "ơ" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x6, 0xd0, 0x0, 0x0, 0x0, 0x8, 0xe0, 0x0,
    0x5c, 0xff, 0xff, 0x70, 0x8, 0xfe, 0x9c, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+01AF "Ư" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf0, 0xbf, 0x40, 0x0, 0x6, 0xff, 0x90,
    0xbf, 0x40, 0x0, 0x6, 0xfb, 0x0, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0x0, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0x0,
    0xaf, 0x40, 0x0, 0x6, 0xf8, 0x0, 0x9f, 0x60,
    0x0, 0x9, 0xf6, 0x0, 0x5f, 0xd1, 0x0, 0x2f,
    0xf2, 0x0, 0xc, 0xff, 0xcc, 0xff, 0x90, 0x0,
    0x0, 0x7d, 0xff, 0xc6, 0x0, 0x0,

    /* U+01B0 "ư" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x1, 0xf3, 0x0, 0x0, 0x0, 0x3, 0xf3, 0xdf,
    0x0, 0x0, 0xff, 0xc0, 0xdf, 0x0, 0x0, 0xff,
    0x0, 0xdf, 0x0, 0x0, 0xef, 0x0, 0xdf, 0x0,
    0x0, 0xef, 0x0, 0xdf, 0x10, 0x0, 0xff, 0x0,
    0xbf, 0x50, 0x5, 0xff, 0x0, 0x5f, 0xfb, 0xbf,
    0xff, 0x0, 0x6, 0xdf, 0xd7, 0xcf, 0x0,

    /* U+01B7 "Ʒ" */
    0x2f, 0xff, 0xff, 0xff, 0x51, 0x99, 0x99, 0xbf,
    0xe2, 0x0, 0x0, 0x2e, 0xe2, 0x0, 0x0, 0x2f,
    0xf2, 0x0, 0x0, 0x9, 0xff, 0xe7, 0x0, 0x0,
    0x35, 0x7e, 0xf6, 0x0, 0x0, 0x0, 0x3f, 0xc0,
    0x60, 0x0, 0x6, 0xfb, 0x6f, 0xeb, 0xac, 0xff,
    0x30, 0x6b, 0xef, 0xeb, 0x30,

    /* U+01C4 "Ǆ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x60,
    0x7, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x9c, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xd8, 0x10, 0x8,
    0xff, 0xff, 0xff, 0xff, 0x29, 0xfc, 0xaa, 0xae,
    0xff, 0x40, 0x5a, 0xaa, 0xaa, 0xff, 0xd0, 0x9f,
    0x50, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0, 0x4f,
    0xe2, 0x9, 0xf5, 0x0, 0x0, 0x9, 0xf7, 0x0,
    0x0, 0x2e, 0xf3, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x4f, 0xa0, 0x0, 0x1d, 0xf5, 0x0, 0x9, 0xf5,
    0x0, 0x0, 0x4, 0xfa, 0x0, 0xc, 0xf7, 0x0,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0x9f, 0x70, 0xb,
    0xf9, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x6f,
    0xf1, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xca,
    0xaa, 0xef, 0xf4, 0x6, 0xff, 0xaa, 0xaa, 0xaa,
    0x39, 0xff, 0xff, 0xfd, 0x81, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xf5,

    /* U+01C5 "ǅ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x90,
    0xbc, 0x9, 0xff, 0xff, 0xfd, 0x81, 0x0, 0x0,
    0x3f, 0xfe, 0x10, 0x9f, 0xca, 0xaa, 0xef, 0xf4,
    0x0, 0x0, 0x36, 0x20, 0x9, 0xf5, 0x0, 0x0,
    0x6f, 0xf1, 0xf, 0xff, 0xff, 0xfb, 0x9f, 0x50,
    0x0, 0x0, 0x9f, 0x70, 0x77, 0x77, 0xff, 0x79,
    0xf5, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0x7f,
    0xa0, 0x9f, 0x50, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0x4f, 0xc0, 0x9, 0xf5, 0x0, 0x0, 0x9, 0xf7,
    0x0, 0x2f, 0xe1, 0x0, 0x9f, 0x50, 0x0, 0x6,
    0xff, 0x10, 0x1d, 0xf3, 0x0, 0x9, 0xfc, 0xaa,
    0xae, 0xff, 0x40, 0xc, 0xff, 0xaa, 0xa8, 0x9f,
    0xff, 0xff, 0xd8, 0x10, 0x0, 0xff, 0xff, 0xff,
    0xe0,

    /* U+01C6 "ǆ" */
    0x0, 0x0, 0x0, 0xc, 0xf1, 0x3, 0xe8, 0x3d,
    0x90, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x3f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0xfd, 0x7c, 0xf1,
    0x2f, 0xff, 0xff, 0xf9, 0x9, 0xfe, 0x9a, 0xff,
    0xf1, 0x19, 0x99, 0x9f, 0xf5, 0x2f, 0xe1, 0x0,
    0x4f, 0xf1, 0x0, 0x0, 0xaf, 0x70, 0x6f, 0x80,
    0x0, 0xf, 0xf1, 0x0, 0x7, 0xfa, 0x0, 0x6f,
    0x80, 0x0, 0xf, 0xf1, 0x0, 0x5f, 0xc0, 0x0,
    0x2f, 0xe2, 0x0, 0x5f, 0xf1, 0x3, 0xfd, 0x10,
    0x0, 0x9, 0xfe, 0xab, 0xff, 0xf1, 0x1e, 0xfc,
    0x99, 0x96, 0x0, 0x6d, 0xfe, 0x7b, 0xf1, 0x3f,
    0xff, 0xff, 0xfb,

    /* U+01C7 "Ǉ" */
    0x9f, 0x50, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x99,
    0xf5, 0x0, 0x0, 0x1, 0x99, 0x9c, 0xf9, 0x9f,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x99, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x9f, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x99, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xf9, 0x9f, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x89, 0xf5, 0x0, 0x0,
    0x3, 0x20, 0x9, 0xf6, 0x9f, 0xca, 0xaa, 0xa9,
    0xef, 0xbb, 0xff, 0x19, 0xff, 0xff, 0xff, 0xd4,
    0xbf, 0xec, 0x30,

    /* U+01C8 "ǈ" */
    0x0, 0x0, 0x0, 0x0, 0xa, 0xe3, 0x9f, 0x50,
    0x0, 0x0, 0xd, 0xf4, 0x9f, 0x50, 0x0, 0x0,
    0x0, 0x20, 0x9f, 0x50, 0x0, 0x0, 0xb, 0xf3,
    0x9f, 0x50, 0x0, 0x0, 0xb, 0xf3, 0x9f, 0x50,
    0x0, 0x0, 0xb, 0xf3, 0x9f, 0x50, 0x0, 0x0,
    0xb, 0xf3, 0x9f, 0x50, 0x0, 0x0, 0xb, 0xf3,
    0x9f, 0x50, 0x0, 0x0, 0xb, 0xf3, 0x9f, 0xca,
    0xaa, 0xa8, 0xb, 0xf3, 0x9f, 0xff, 0xff, 0xfd,
    0xb, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0x0, 0xa, 0x9f, 0xd0, 0x0, 0x0,
    0x0, 0x3e, 0xfc, 0x30,

    /* U+01C9 "ǉ" */
    0xcf, 0x10, 0x5f, 0x8c, 0xf1, 0x7, 0xfa, 0xcf,
    0x10, 0x3, 0xc, 0xf1, 0x5, 0xf8, 0xcf, 0x10,
    0x5f, 0x8c, 0xf1, 0x5, 0xf8, 0xcf, 0x10, 0x5f,
    0x8c, 0xf1, 0x5, 0xf8, 0xcf, 0x10, 0x5f, 0x8c,
    0xf1, 0x5, 0xf8, 0xcf, 0x10, 0x5f, 0x80, 0x0,
    0x6, 0xf7, 0x0, 0x89, 0xef, 0x30, 0xb, 0xfe,
    0x60,

    /* U+01CA "Ǌ" */
    0x9f, 0x60, 0x0, 0x3, 0xfb, 0x0, 0xff, 0xff,
    0xfb, 0x9f, 0xf4, 0x0, 0x3, 0xfb, 0x0, 0x99,
    0x9b, 0xfb, 0x9f, 0xfe, 0x20, 0x3, 0xfb, 0x0,
    0x0, 0x3, 0xfb, 0x9f, 0x9f, 0xd0, 0x3, 0xfb,
    0x0, 0x0, 0x3, 0xfb, 0x9f, 0x56, 0xfb, 0x3,
    0xfb, 0x0, 0x0, 0x3, 0xfb, 0x9f, 0x50, 0x9f,
    0x83, 0xfb, 0x0, 0x0, 0x3, 0xfb, 0x9f, 0x50,
    0xb, 0xf8, 0xfb, 0x0, 0x0, 0x3, 0xfb, 0x9f,
    0x50, 0x1, 0xdf, 0xfb, 0x0, 0x10, 0x5, 0xf9,
    0x9f, 0x50, 0x0, 0x2f, 0xfb, 0xb, 0xe9, 0x8e,
    0xf3, 0x9f, 0x50, 0x0, 0x4, 0xfb, 0x2, 0xbe,
    0xfc, 0x50,

    /* U+01CB "ǋ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x59,
    0xf6, 0x0, 0x0, 0x3f, 0xb0, 0xa, 0xf7, 0x9f,
    0xf4, 0x0, 0x3, 0xfb, 0x0, 0x3, 0x9, 0xff,
    0xe2, 0x0, 0x3f, 0xb0, 0x8, 0xf5, 0x9f, 0x9f,
    0xd0, 0x3, 0xfb, 0x0, 0x8f, 0x59, 0xf5, 0x6f,
    0xb0, 0x3f, 0xb0, 0x8, 0xf5, 0x9f, 0x50, 0x9f,
    0x83, 0xfb, 0x0, 0x8f, 0x59, 0xf5, 0x0, 0xbf,
    0x8f, 0xb0, 0x8, 0xf5, 0x9f, 0x50, 0x1, 0xdf,
    0xfb, 0x0, 0x8f, 0x59, 0xf5, 0x0, 0x2, 0xff,
    0xb0, 0x8, 0xf5, 0x9f, 0x50, 0x0, 0x4, 0xfb,
    0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x99,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfd,
    0x40,

    /* U+01CC "ǌ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xc, 0xf4, 0xcf, 0xe9, 0x0,
    0x6, 0xf7, 0xcf, 0xfc, 0xae, 0xfa, 0x0, 0x6f,
    0x7c, 0xf8, 0x0, 0x1f, 0xf0, 0x6, 0xf7, 0xcf,
    0x20, 0x0, 0xcf, 0x10, 0x6f, 0x7c, 0xf1, 0x0,
    0xc, 0xf2, 0x6, 0xf7, 0xcf, 0x10, 0x0, 0xcf,
    0x20, 0x6f, 0x7c, 0xf1, 0x0, 0xc, 0xf2, 0x6,
    0xf7, 0xcf, 0x10, 0x0, 0xcf, 0x20, 0x6f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x89, 0xef, 0x20, 0x0, 0x0, 0x0,
    0xc, 0xfd, 0x50,

    /* U+01CD "Ǎ" */
    0x0, 0x0, 0x74, 0x2, 0x82, 0x0, 0x0, 0x0,
    0x7, 0xf8, 0xea, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0xaf, 0x30, 0x0, 0x0, 0x7, 0xf6, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0xee, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xe, 0xf9, 0x88, 0x88, 0xff, 0x20, 0x5, 0xf9,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0xe, 0xf1,

    /* U+01CE "ǎ" */
    0x1, 0xdb, 0x2b, 0xc0, 0x0, 0x1d, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+01D3 "Ǔ" */
    0x0, 0x56, 0x0, 0x74, 0x0, 0x0, 0x1d, 0xab,
    0xc0, 0x0, 0x0, 0x1, 0xca, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+01D4 "ǔ" */
    0x4, 0xf6, 0x4e, 0x60, 0x0, 0x4f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+01E4 "Ǥ" */
    0x0, 0x5, 0xbe, 0xfe, 0xa2, 0x0, 0x0, 0xbf,
    0xfd, 0xbd, 0xff, 0x40, 0x9, 0xfc, 0x20, 0x0,
    0x38, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xa0, 0x0, 0x0, 0x2, 0x0, 0x4f, 0xa0,
    0x0, 0x11, 0x8f, 0x81, 0x1f, 0xf1, 0x1, 0xff,
    0xff, 0xfd, 0xa, 0xfc, 0x20, 0x44, 0xdf, 0xb3,
    0x0, 0xbf, 0xfd, 0xbc, 0xff, 0x60, 0x0, 0x6,
    0xce, 0xfe, 0xa3, 0x0,

    /* U+01E5 "ǥ" */
    0x0, 0x7d, 0xfe, 0x8a, 0xf3, 0x0, 0xbf, 0xc6,
    0x7e, 0xff, 0x30, 0x3f, 0xc0, 0x0, 0x1f, 0xf3,
    0x6, 0xf7, 0x0, 0x0, 0xff, 0x30, 0x5f, 0xa0,
    0x0, 0xf, 0xf3, 0x0, 0xef, 0x93, 0x3b, 0xff,
    0x30, 0x2, 0xdf, 0xff, 0xdd, 0xf2, 0x0, 0x0,
    0x24, 0x30, 0xbf, 0x20, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0x20, 0xbd, 0xab, 0xcf, 0xf9, 0x20, 0x3,
    0xae, 0xff, 0xc5, 0x0, 0x0,

    /* U+01E6 "Ǧ" */
    0x0, 0x3, 0x71, 0x5, 0x60, 0x0, 0x0, 0xa,
    0xd9, 0xe2, 0x0, 0x0, 0x0, 0x9, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0xa2, 0x0, 0xb, 0xff, 0xdb, 0xdf,
    0xf4, 0x9, 0xfc, 0x20, 0x0, 0x38, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x1, 0x4, 0xfa, 0x0, 0x0, 0x6, 0xf6, 0x1f,
    0xf1, 0x0, 0x0, 0x6f, 0x60, 0xaf, 0xc2, 0x0,
    0x9, 0xf6, 0x0, 0xbf, 0xfd, 0xbd, 0xff, 0x70,
    0x0, 0x6c, 0xef, 0xea, 0x30,

    /* U+01E7 "ǧ" */
    0x0, 0x5f, 0x55, 0xf6, 0x0, 0x0, 0x5, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xfe, 0x8a, 0xf3, 0x9, 0xfd, 0x89, 0xef,
    0xf3, 0x2f, 0xd0, 0x0, 0x2f, 0xf3, 0x6f, 0x80,
    0x0, 0xf, 0xf3, 0x6f, 0x80, 0x0, 0xf, 0xf3,
    0x2f, 0xe2, 0x0, 0x3f, 0xf3, 0x9, 0xfe, 0x9a,
    0xff, 0xf2, 0x0, 0x6d, 0xfe, 0x8c, 0xf2, 0x1,
    0x0, 0x0, 0x2f, 0xe0, 0xc, 0xfb, 0x9a, 0xff,
    0x70, 0x3, 0xae, 0xff, 0xc5, 0x0,

    /* U+01E8 "Ǩ" */
    0x0, 0x74, 0x2, 0x82, 0x0, 0x0, 0x6, 0xf8,
    0xeb, 0x0, 0x0, 0x0, 0x9, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x50,
    0x0, 0x1d, 0xf4, 0x9, 0xf5, 0x0, 0x1d, 0xf4,
    0x0, 0x9f, 0x50, 0x1d, 0xf4, 0x0, 0x9, 0xf5,
    0x1d, 0xf4, 0x0, 0x0, 0x9f, 0x7e, 0xfa, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x9f,
    0xf5, 0x5f, 0xe2, 0x0, 0x9, 0xf9, 0x0, 0x7f,
    0xd0, 0x0, 0x9f, 0x50, 0x0, 0x9f, 0xb0, 0x9,
    0xf5, 0x0, 0x0, 0xbf, 0x90,

    /* U+01E9 "ǩ" */
    0x3f, 0x84, 0xe8, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x10, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x8, 0xfb, 0x0, 0xc,
    0xf1, 0x9, 0xfb, 0x0, 0x0, 0xcf, 0x2b, 0xfb,
    0x0, 0x0, 0xc, 0xfd, 0xff, 0x20, 0x0, 0x0,
    0xcf, 0xff, 0xfc, 0x0, 0x0, 0xc, 0xfa, 0xb,
    0xf9, 0x0, 0x0, 0xcf, 0x10, 0x1d, 0xf6, 0x0,
    0xc, 0xf1, 0x0, 0x2f, 0xf3,

    /* U+01EA "Ǫ" */
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf4, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0xa, 0xfc, 0x20, 0x0, 0x5f, 0xf3,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x50, 0x0, 0x6,
    0xbf, 0xfe, 0x81, 0x0, 0x0, 0x0, 0x1e, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xfb, 0x0, 0x0,

    /* U+01EB "ǫ" */
    0x0, 0x5c, 0xfe, 0xb3, 0x0, 0x8, 0xfe, 0x9a,
    0xff, 0x40, 0x2f, 0xe1, 0x0, 0x5f, 0xd0, 0x6f,
    0x80, 0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd,
    0xf0, 0x2f, 0xe1, 0x0, 0x5f, 0xb0, 0x8, 0xfe,
    0x9a, 0xfe, 0x20, 0x0, 0x5c, 0xff, 0x81, 0x0,
    0x0, 0x3, 0xd1, 0x0, 0x0, 0x0, 0x9, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0xdf, 0x70, 0x0,

    /* U+01EE "Ǯ" */
    0x0, 0x74, 0x2, 0x71, 0x0, 0x4, 0xf8, 0xe7,
    0x0, 0x0, 0x4, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0x51, 0x99,
    0x99, 0xbf, 0xe2, 0x0, 0x0, 0x2e, 0xe2, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x9, 0xff, 0xe7,
    0x0, 0x0, 0x35, 0x7e, 0xf6, 0x0, 0x0, 0x0,
    0x3f, 0xc0, 0x60, 0x0, 0x6, 0xfb, 0x6f, 0xeb,
    0xac, 0xff, 0x30, 0x6b, 0xef, 0xeb, 0x30,

    /* U+01EF "ǯ" */
    0x1, 0xcb, 0x2b, 0xc1, 0x0, 0x0, 0xcf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xf3, 0x8, 0x99, 0x9a, 0xfe, 0x10, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x2f, 0xd0, 0x0, 0x0, 0x8, 0xff, 0xe6,
    0x0, 0x0, 0x25, 0x7e, 0xf4, 0x0, 0x0, 0x0,
    0x5f, 0x90, 0x51, 0x0, 0x8, 0xf9, 0x1f, 0xfc,
    0xbd, 0xff, 0x20, 0x4a, 0xef, 0xea, 0x20,

    /* U+01FA "Ǻ" */
    0x0, 0x0, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x6b, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x9, 0x30, 0x0, 0x0, 0x0, 0x0, 0xc0, 0x93,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xc8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x6f,
    0xb0, 0x0, 0x0, 0x0, 0xf, 0xd0, 0xaf, 0x30,
    0x0, 0x0, 0x7, 0xf6, 0x3, 0xfb, 0x0, 0x0,
    0x0, 0xee, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0xe, 0xf9, 0x88,
    0x88, 0xff, 0x20, 0x5, 0xf9, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0xdf, 0x20, 0x0, 0x0, 0xe, 0xf1,

    /* U+01FB "ǻ" */
    0x0, 0x0, 0x1, 0xc2, 0x0, 0x9, 0xcf, 0x70,
    0x0, 0x39, 0xb, 0x10, 0x0, 0x9, 0xc8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+01FC "Ǽ" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x77, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfb,
    0x99, 0x99, 0x90, 0x0, 0x0, 0x8, 0xf6, 0xbf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfc, 0xb,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x30,
    0xbf, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x5f, 0xa0,
    0xb, 0xfc, 0x99, 0x99, 0x30, 0x0, 0xf, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x88, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0x2, 0xfd,
    0x0, 0x0, 0xbf, 0xb9, 0x99, 0x99, 0x10, 0xcf,
    0x30, 0x0, 0xb, 0xff, 0xff, 0xff, 0xf3,

    /* U+01FD "ǽ" */
    0x0, 0x0, 0x0, 0xb, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b,
    0xef, 0xd7, 0x1a, 0xef, 0xd5, 0x0, 0xc, 0xc9,
    0x9e, 0xff, 0xfa, 0x9d, 0xf8, 0x0, 0x0, 0x0,
    0x2f, 0xf6, 0x0, 0xc, 0xf2, 0x4, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x42, 0xfd, 0x43, 0x3f,
    0xf3, 0x33, 0x33, 0x30, 0x4f, 0x80, 0x1, 0xff,
    0x40, 0x0, 0x40, 0x1, 0xfe, 0x76, 0xdf, 0xef,
    0x86, 0x9f, 0x80, 0x3, 0xcf, 0xea, 0x21, 0x9e,
    0xfe, 0x91, 0x0,

    /* U+01FE "Ǿ" */
    0x0, 0x0, 0x0, 0x17, 0x70, 0x0, 0x0, 0x0,
    0x1, 0xdd, 0x20, 0x0, 0x0, 0x0, 0x5, 0x70,
    0x0, 0x72, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc0,
    0x0, 0x5, 0xbe, 0xfe, 0xef, 0x20, 0x0, 0xbf,
    0xfd, 0xbf, 0xff, 0x60, 0x9, 0xfc, 0x20, 0xc,
    0xff, 0xf3, 0x1f, 0xf1, 0x0, 0x7d, 0x7, 0xfb,
    0x4f, 0xb0, 0x3, 0xf3, 0x1, 0xfe, 0x4f, 0xa0,
    0xd, 0x70, 0x0, 0xfe, 0x1f, 0xf1, 0xab, 0x0,
    0x6, 0xfb, 0x9, 0xff, 0xf1, 0x0, 0x5f, 0xf4,
    0x0, 0xdf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0xcf,
    0xbe, 0xfe, 0x92, 0x0, 0x8, 0xd0, 0x0, 0x0,
    0x0, 0x0,

    /* U+01FF "ǿ" */
    0x0, 0x0, 0x1d, 0xe3, 0x0, 0x0, 0x1, 0xdb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0x40, 0x0,
    0x5c, 0xfe, 0xfe, 0x0, 0x8, 0xfe, 0x9c, 0xff,
    0x30, 0x2f, 0xd1, 0xa, 0xaf, 0xc0, 0x6f, 0x80,
    0x5b, 0xd, 0xf0, 0x5f, 0x81, 0xd1, 0xd, 0xf0,
    0x2f, 0xec, 0x60, 0x5f, 0xc0, 0x8, 0xff, 0xab,
    0xff, 0x30, 0x1, 0xfe, 0xfe, 0xb2, 0x0, 0xa,
    0x60, 0x0, 0x0, 0x0,

    /* U+0200 "Ȁ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x2d, 0xd0, 0x0, 0x0, 0x0, 0x1, 0xda,
    0x2f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0xaf, 0x30, 0x0, 0x0, 0x7, 0xf6, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0xee, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xe, 0xf9, 0x88, 0x88, 0xff, 0x20, 0x5, 0xf9,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0xe, 0xf1,

    /* U+0201 "ȁ" */
    0xd, 0xd2, 0xfa, 0x0, 0x2, 0xf7, 0x4f, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+0202 "Ȃ" */
    0x0, 0x0, 0x2a, 0xdc, 0x30, 0x0, 0x0, 0x0,
    0xc, 0xa2, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x8, 0xf6, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xfd, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x7f,
    0x60, 0x3f, 0xb0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0xcf, 0x20, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0xef, 0x98, 0x88, 0x8f, 0xf2, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x6f, 0x90, 0xd, 0xf2,
    0x0, 0x0, 0x0, 0xef, 0x10,

    /* U+0203 "ȃ" */
    0x0, 0x4d, 0xfc, 0x30, 0x0, 0xf6, 0x7, 0xe0,
    0x0, 0x10, 0x0, 0x10, 0x4, 0xbe, 0xfd, 0x60,
    0xc, 0xc9, 0x9e, 0xf7, 0x0, 0x0, 0x2, 0xfc,
    0x5, 0xdf, 0xff, 0xfe, 0x2f, 0xd4, 0x33, 0xff,
    0x5f, 0x80, 0x1, 0xff, 0x1f, 0xe5, 0x5c, 0xff,
    0x4, 0xdf, 0xe8, 0xdf,

    /* U+0204 "Ȅ" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xeb, 0x4f, 0x80,
    0x0, 0x4, 0xf4, 0x7f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xfb,
    0x99, 0x99, 0x90, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7,
    0x9, 0xfd, 0x99, 0x99, 0x40, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xb9,
    0x99, 0x99, 0x29, 0xff, 0xff, 0xff, 0xf4,

    /* U+0205 "ȅ" */
    0xa, 0xf2, 0xdd, 0x0, 0x0, 0xd, 0xa2, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+0206 "Ȇ" */
    0x0, 0x5c, 0xda, 0x10, 0x0, 0x2f, 0x52, 0xba,
    0x0, 0x0, 0x20, 0x1, 0x20, 0x9, 0xff, 0xff,
    0xff, 0xf1, 0x9f, 0xb9, 0x99, 0x99, 0x9, 0xf5,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0x70, 0x9f, 0xd9, 0x99, 0x94,
    0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x9, 0xfb, 0x99, 0x99, 0x92, 0x9f, 0xff,
    0xff, 0xff, 0x40,

    /* U+0207 "ȇ" */
    0x0, 0x2c, 0xfd, 0x50, 0x0, 0xc, 0x90, 0x4f,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xea, 0x10, 0x9, 0xfc, 0x8a, 0xfe, 0x12, 0xfc,
    0x0, 0x4, 0xf8, 0x6f, 0xff, 0xff, 0xff, 0xb6,
    0xfa, 0x33, 0x33, 0x32, 0x2f, 0xd1, 0x0, 0x13,
    0x0, 0x8f, 0xea, 0xae, 0xe0, 0x0, 0x5c, 0xff,
    0xc4, 0x0,

    /* U+0208 "Ȉ" */
    0x0, 0x0, 0x0, 0x1, 0xdc, 0x3f, 0xa0, 0x3,
    0xf6, 0x5f, 0x30, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf5, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x9, 0xf5,
    0x0, 0x0, 0x9f, 0x50, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x9f, 0x50, 0x0, 0x9, 0xf5, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x9f,
    0x50,

    /* U+0209 "ȉ" */
    0x3f, 0x95, 0xf6, 0x0, 0x6f, 0x39, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0xc,
    0xf1, 0x0, 0x0, 0xcf, 0x10, 0x0, 0xc, 0xf1,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0xc, 0xf1, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0xc, 0xf1, 0x0,

    /* U+020A "Ȋ" */
    0x3, 0xcd, 0xa2, 0xf, 0x62, 0xac, 0x3, 0x0,
    0x2, 0x0, 0x9f, 0x50, 0x0, 0x9f, 0x50, 0x0,
    0x9f, 0x50, 0x0, 0x9f, 0x50, 0x0, 0x9f, 0x50,
    0x0, 0x9f, 0x50, 0x0, 0x9f, 0x50, 0x0, 0x9f,
    0x50, 0x0, 0x9f, 0x50, 0x0, 0x9f, 0x50,

    /* U+020B "ȋ" */
    0x4, 0xdf, 0x90, 0xe, 0x51, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10,
    0x0, 0xcf, 0x10, 0x0, 0xcf, 0x10, 0x0, 0xcf,
    0x10,

    /* U+020C "Ȍ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xb3, 0xf9, 0x0, 0x0, 0x0, 0x3, 0xf5, 0x6f,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+020D "ȍ" */
    0x8, 0xf3, 0xbf, 0x10, 0x0, 0x0, 0xbc, 0x1d,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+020E "Ȏ" */
    0x0, 0x0, 0x2b, 0xd9, 0x0, 0x0, 0x0, 0x0,
    0xe8, 0x2c, 0x80, 0x0, 0x0, 0x2, 0xe0, 0x5,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x9, 0xfc, 0x20, 0x0,
    0x5f, 0xf3, 0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb,
    0x4f, 0xb0, 0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0,
    0x6, 0xfb, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4,
    0x0, 0xbf, 0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0,

    /* U+020F "ȏ" */
    0x0, 0x1b, 0xfe, 0x70, 0x0, 0x0, 0xab, 0x13,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a, 0xff,
    0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f, 0x80,
    0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd, 0xf0,
    0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe, 0xab,
    0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+0210 "Ȑ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0xdd,
    0x0, 0x0, 0x1, 0xda, 0x2f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfe, 0xc5,
    0x0, 0x9f, 0xfa, 0xab, 0xff, 0x70, 0x9f, 0x50,
    0x0, 0x2f, 0xf0, 0x9f, 0x50, 0x0, 0xd, 0xf2,
    0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f, 0xe9, 0x9a,
    0xff, 0x80, 0x9f, 0xff, 0xff, 0xf6, 0x0, 0x9f,
    0x50, 0x8, 0xf9, 0x0, 0x9f, 0x50, 0x0, 0xcf,
    0x40, 0x9f, 0x50, 0x0, 0x2f, 0xe1,

    /* U+0211 "ȑ" */
    0xc, 0xe2, 0xec, 0x0, 0x1e, 0x83, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf4, 0xcd, 0x0, 0xcf,
    0xfd, 0x90, 0xc, 0xf9, 0x0, 0x0, 0xcf, 0x30,
    0x0, 0xc, 0xf1, 0x0, 0x0, 0xcf, 0x10, 0x0,
    0xc, 0xf1, 0x0, 0x0, 0xcf, 0x10, 0x0,

    /* U+0212 "Ȓ" */
    0x0, 0x2a, 0xdb, 0x30, 0x0, 0x0, 0xc9, 0x27,
    0xf0, 0x0, 0x0, 0x20, 0x0, 0x30, 0x0, 0x9f,
    0xff, 0xfe, 0xc5, 0x0, 0x9f, 0xfa, 0xab, 0xff,
    0x70, 0x9f, 0x50, 0x0, 0x2f, 0xf0, 0x9f, 0x50,
    0x0, 0xd, 0xf2, 0x9f, 0x50, 0x0, 0x2f, 0xf0,
    0x9f, 0xe9, 0x9a, 0xff, 0x80, 0x9f, 0xff, 0xff,
    0xf6, 0x0, 0x9f, 0x50, 0x8, 0xf9, 0x0, 0x9f,
    0x50, 0x0, 0xcf, 0x40, 0x9f, 0x50, 0x0, 0x2f,
    0xe1,

    /* U+0213 "ȓ" */
    0x3, 0xcf, 0xd4, 0x0, 0xe7, 0x6, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x4c, 0xd0, 0xc, 0xff,
    0xd9, 0x0, 0xcf, 0x90, 0x0, 0xc, 0xf3, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0xcf, 0x10, 0x0, 0xc, 0xf1, 0x0, 0x0,

    /* U+0214 "Ȕ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf6, 0x9f,
    0x30, 0x0, 0x0, 0x9e, 0x1c, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+0215 "ȕ" */
    0x4f, 0x77, 0xf4, 0x0, 0x8, 0xf1, 0xad, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+0216 "Ȗ" */
    0x0, 0x6, 0xdc, 0x50, 0x0, 0x0, 0x5e, 0x34,
    0xf3, 0x0, 0x0, 0x77, 0x0, 0x85, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x6,
    0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8, 0xbf, 0x40,
    0x0, 0x6, 0xf8, 0xbf, 0x40, 0x0, 0x6, 0xf8,
    0xbf, 0x40, 0x0, 0x6, 0xf8, 0xaf, 0x40, 0x0,
    0x6, 0xf8, 0x9f, 0x60, 0x0, 0x9, 0xf6, 0x5f,
    0xd1, 0x0, 0x2f, 0xf2, 0xc, 0xff, 0xcc, 0xff,
    0x90, 0x0, 0x7d, 0xff, 0xc6, 0x0,

    /* U+0217 "ȗ" */
    0x0, 0x9e, 0xfa, 0x0, 0x6, 0xe2, 0x1c, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x0, 0x0, 0xef,
    0xdf, 0x0, 0x0, 0xef, 0xdf, 0x10, 0x0, 0xff,
    0xbf, 0x50, 0x5, 0xff, 0x5f, 0xfb, 0xbf, 0xff,
    0x6, 0xdf, 0xd7, 0xcf,

    /* U+0218 "Ș" */
    0x0, 0x8d, 0xfe, 0xc7, 0x10, 0xcf, 0xda, 0xad,
    0xf3, 0x3f, 0xd0, 0x0, 0x2, 0x2, 0xfe, 0x20,
    0x0, 0x0, 0x7, 0xff, 0xd9, 0x50, 0x0, 0x2,
    0x7b, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x60, 0x0, 0x2, 0xfd, 0x4f, 0xfb, 0x9a, 0xef,
    0x70, 0x5b, 0xef, 0xfc, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x30, 0x0, 0x0, 0x0,
    0xfb, 0x0, 0x0, 0x0, 0xa, 0x50, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0,

    /* U+0219 "ș" */
    0x4, 0xcf, 0xfd, 0x90, 0x3f, 0xe8, 0x8b, 0xa0,
    0x6f, 0x80, 0x0, 0x0, 0x2f, 0xfd, 0x96, 0x10,
    0x2, 0x7b, 0xef, 0xe1, 0x1, 0x0, 0x9, 0xf4,
    0x6f, 0xb9, 0x9f, 0xf1, 0x3a, 0xef, 0xfb, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0xf, 0xa0, 0x0, 0x0, 0xb, 0x40, 0x0,
    0x0, 0x2, 0x0, 0x0,

    /* U+021A "Ț" */
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xaa, 0xcf, 0xca,
    0xa9, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0, 0x7f,
    0x70, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x7f, 0x70, 0x0, 0x0, 0x7, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0x10, 0x0, 0x0, 0x3,
    0xf5, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0,

    /* U+021B "ț" */
    0x4, 0x82, 0x0, 0x0, 0x8f, 0x50, 0x0, 0xdf,
    0xff, 0xf8, 0x6, 0xff, 0xd7, 0x30, 0x8, 0xf5,
    0x0, 0x0, 0x8f, 0x50, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x7f, 0x60, 0x0, 0x4, 0xfe, 0x98, 0x0,
    0x7, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x63, 0x0, 0x0, 0xf, 0xa0, 0x0, 0x0, 0xb5,
    0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+021E "Ȟ" */
    0x0, 0x47, 0x0, 0x65, 0x0, 0x0, 0x1e, 0xba,
    0xf2, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x3,
    0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb, 0x9f, 0x50,
    0x0, 0x3, 0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb,
    0x9f, 0xff, 0xff, 0xff, 0xfb, 0x9f, 0xea, 0xaa,
    0xac, 0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb, 0x9f,
    0x50, 0x0, 0x3, 0xfb, 0x9f, 0x50, 0x0, 0x3,
    0xfb, 0x9f, 0x50, 0x0, 0x3, 0xfb,

    /* U+021F "ȟ" */
    0x3f, 0x84, 0xe8, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x10, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x6c, 0xfe, 0x90, 0x0, 0xc,
    0xff, 0xca, 0xef, 0xa0, 0x0, 0xcf, 0x80, 0x1,
    0xff, 0x0, 0xc, 0xf2, 0x0, 0xc, 0xf1, 0x0,
    0xcf, 0x10, 0x0, 0xcf, 0x20, 0xc, 0xf1, 0x0,
    0xc, 0xf2, 0x0, 0xcf, 0x10, 0x0, 0xcf, 0x20,
    0xc, 0xf1, 0x0, 0xc, 0xf2,

    /* U+022A "Ȫ" */
    0x0, 0x4, 0xee, 0xee, 0xd0, 0x0, 0x0, 0x0,
    0x11, 0x11, 0x10, 0x0, 0x0, 0x0, 0xb8, 0xd,
    0x60, 0x0, 0x0, 0x0, 0x75, 0x9, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf, 0xfd, 0xbe,
    0xff, 0x60, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf3,
    0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb, 0x4f, 0xb0,
    0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0, 0x6, 0xfb,
    0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5, 0xbe, 0xfe,
    0x92, 0x0,

    /* U+022B "ȫ" */
    0x0, 0xbe, 0xee, 0xe5, 0x0, 0x0, 0x11, 0x11,
    0x10, 0x0, 0x0, 0x4e, 0x26, 0xd0, 0x0, 0x0,
    0x29, 0x4, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe,
    0x9a, 0xff, 0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0,
    0x6f, 0x80, 0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0,
    0xd, 0xf0, 0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8,
    0xfe, 0xab, 0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2,
    0x0,

    /* U+022C "Ȭ" */
    0x0, 0x4, 0xee, 0xee, 0xd0, 0x0, 0x0, 0x0,
    0x11, 0x11, 0x10, 0x0, 0x0, 0x0, 0xbe, 0x63,
    0xe0, 0x0, 0x0, 0x4, 0xd1, 0xaf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf, 0xfd, 0xbe,
    0xff, 0x60, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf3,
    0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb, 0x4f, 0xb0,
    0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0, 0x6, 0xfb,
    0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5, 0xbe, 0xfe,
    0x92, 0x0,

    /* U+022D "ȭ" */
    0x0, 0xbe, 0xee, 0xe5, 0x0, 0x0, 0x11, 0x11,
    0x10, 0x0, 0x0, 0x4e, 0xc2, 0xa6, 0x0, 0x0,
    0xc5, 0x5d, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe,
    0x9a, 0xff, 0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0,
    0x6f, 0x80, 0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0,
    0xd, 0xf0, 0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8,
    0xfe, 0xab, 0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2,
    0x0,

    /* U+0230 "Ȱ" */
    0x0, 0x4, 0xee, 0xee, 0xd0, 0x0, 0x0, 0x0,
    0x11, 0x11, 0x10, 0x0, 0x0, 0x0, 0x5, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0x92, 0x0, 0x0, 0xbf, 0xfd, 0xbe,
    0xff, 0x60, 0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf3,
    0x1f, 0xf1, 0x0, 0x0, 0x7, 0xfb, 0x4f, 0xb0,
    0x0, 0x0, 0x1, 0xfe, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0xfe, 0x1f, 0xf1, 0x0, 0x0, 0x6, 0xfb,
    0x9, 0xfc, 0x20, 0x0, 0x5f, 0xf4, 0x0, 0xbf,
    0xfd, 0xbe, 0xff, 0x60, 0x0, 0x5, 0xbe, 0xfe,
    0x92, 0x0,

    /* U+0231 "ȱ" */
    0x0, 0xbe, 0xee, 0xe5, 0x0, 0x0, 0x11, 0x11,
    0x10, 0x0, 0x0, 0x0, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x95, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe,
    0x9a, 0xff, 0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0,
    0x6f, 0x80, 0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0,
    0xd, 0xf0, 0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8,
    0xfe, 0xab, 0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2,
    0x0,

    /* U+0232 "Ȳ" */
    0x0, 0x9, 0xff, 0xff, 0x80, 0x0, 0x0, 0x1,
    0x22, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf4, 0x0, 0x0, 0x3f, 0xb0,
    0x3, 0xfd, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x8f,
    0x80, 0x7, 0xf7, 0x0, 0x0, 0xe, 0xf2, 0x2f,
    0xc0, 0x0, 0x0, 0x4, 0xfc, 0xcf, 0x30, 0x0,
    0x0, 0x0, 0xaf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,

    /* U+0233 "ȳ" */
    0x0, 0x7f, 0xff, 0xfb, 0x0, 0x0, 0x2, 0x22,
    0x21, 0x0, 0xd, 0xf1, 0x0, 0x4, 0xf8, 0x6,
    0xf8, 0x0, 0xb, 0xf1, 0x0, 0xee, 0x0, 0x2f,
    0xa0, 0x0, 0x8f, 0x50, 0x9f, 0x30, 0x0, 0x1f,
    0xc0, 0xfc, 0x0, 0x0, 0xa, 0xfa, 0xf5, 0x0,
    0x0, 0x3, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0xc,
    0xac, 0xf8, 0x0, 0x0, 0x1a, 0xee, 0x90, 0x0,
    0x0,

    /* U+0237 "ȷ" */
    0x0, 0xb, 0xf3, 0x0, 0xb, 0xf3, 0x0, 0xb,
    0xf3, 0x0, 0xb, 0xf3, 0x0, 0xb, 0xf3, 0x0,
    0xb, 0xf3, 0x0, 0xb, 0xf3, 0x0, 0xb, 0xf3,
    0x0, 0xb, 0xf2, 0xa, 0x9f, 0xd0, 0x3e, 0xfc,
    0x30,

    /* U+041B "Л" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0x70, 0x5, 0xfd,
    0xaa, 0xae, 0xf7, 0x0, 0x6f, 0x50, 0x0, 0x7f,
    0x70, 0x6, 0xf5, 0x0, 0x7, 0xf7, 0x0, 0x7f,
    0x40, 0x0, 0x7f, 0x70, 0x8, 0xf3, 0x0, 0x7,
    0xf7, 0x0, 0xbf, 0x10, 0x0, 0x7f, 0x70, 0x1f,
    0xe0, 0x0, 0x7, 0xf7, 0x8e, 0xf8, 0x0, 0x0,
    0x7f, 0x7b, 0xfa, 0x0, 0x0, 0x7, 0xf7,

    /* U+0430 "а" */
    0x5, 0xbe, 0xfd, 0x60, 0xc, 0xc9, 0x9e, 0xf6,
    0x0, 0x0, 0x3, 0xfc, 0x5, 0xdf, 0xff, 0xfe,
    0x2f, 0xd4, 0x33, 0xfe, 0x5f, 0x70, 0x1, 0xfe,
    0x2f, 0xe5, 0x5d, 0xfe, 0x5, 0xdf, 0xe8, 0xee,

    /* U+0431 "б" */
    0x0, 0x0, 0x0, 0x14, 0x60, 0x0, 0x3, 0x9e,
    0xff, 0xe0, 0x0, 0x8f, 0xfd, 0x96, 0x20, 0x5,
    0xfc, 0x20, 0x0, 0x0, 0xc, 0xf2, 0x68, 0x71,
    0x0, 0xf, 0xde, 0xff, 0xff, 0x50, 0x2f, 0xfb,
    0x10, 0x6f, 0xf1, 0x2f, 0xf0, 0x0, 0x9, 0xf5,
    0xf, 0xe0, 0x0, 0x8, 0xf6, 0xc, 0xf5, 0x0,
    0x1d, 0xf3, 0x3, 0xff, 0x98, 0xdf, 0x90, 0x0,
    0x3b, 0xff, 0xc6, 0x0,

    /* U+0432 "в" */
    0xbf, 0xff, 0xfd, 0x60, 0xbf, 0x65, 0x6d, 0xf3,
    0xbf, 0x0, 0x1b, 0xf1, 0xbf, 0xff, 0xff, 0x70,
    0xbf, 0x33, 0x3b, 0xf4, 0xbf, 0x0, 0x4, 0xf7,
    0xbf, 0x55, 0x6c, 0xf4, 0xbf, 0xff, 0xfd, 0x70,

    /* U+0433 "г" */
    0xbf, 0xff, 0xff, 0x6b, 0xfa, 0x99, 0x93, 0xbf,
    0x20, 0x0, 0xb, 0xf2, 0x0, 0x0, 0xbf, 0x20,
    0x0, 0xb, 0xf2, 0x0, 0x0, 0xbf, 0x20, 0x0,
    0xb, 0xf2, 0x0, 0x0,

    /* U+0434 "д" */
    0x0, 0xff, 0xff, 0xff, 0xa0, 0x0, 0xfd, 0x99,
    0xaf, 0xa0, 0x1, 0xfa, 0x0, 0x4f, 0xa0, 0x2,
    0xf9, 0x0, 0x4f, 0xa0, 0x4, 0xf7, 0x0, 0x4f,
    0xa0, 0x8, 0xf4, 0x0, 0x4f, 0xa0, 0x8f, 0xf9,
    0x99, 0xdf, 0xf7, 0xdf, 0xff, 0xff, 0xff, 0xfd,
    0xde, 0x0, 0x0, 0x0, 0xfd, 0xde, 0x0, 0x0,
    0x0, 0xfd,

    /* U+0435 "е" */
    0x0, 0x6d, 0xfe, 0xa1, 0x0, 0x9f, 0xc8, 0xaf,
    0xe1, 0x2f, 0xc0, 0x0, 0x4f, 0x86, 0xff, 0xff,
    0xff, 0xfb, 0x6f, 0xa3, 0x33, 0x33, 0x22, 0xfd,
    0x10, 0x1, 0x30, 0x8, 0xfe, 0xaa, 0xee, 0x0,
    0x5, 0xcf, 0xfc, 0x40,

    /* U+0436 "ж" */
    0x5f, 0x90, 0x5, 0xf8, 0x0, 0x7f, 0x80, 0xc,
    0xf2, 0x5, 0xf8, 0x1, 0xee, 0x0, 0x3, 0xfb,
    0x5, 0xf8, 0x9, 0xf5, 0x0, 0x0, 0x9f, 0xde,
    0xff, 0xbf, 0xd0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x3, 0xfe, 0x15, 0xf8, 0xc,
    0xf6, 0x0, 0xd, 0xf5, 0x5, 0xf8, 0x2, 0xfe,
    0x10, 0x8f, 0xa0, 0x5, 0xf8, 0x0, 0x8f, 0xb0,

    /* U+0437 "з" */
    0x19, 0xdf, 0xeb, 0x50, 0x1d, 0xa8, 0x9f, 0xf3,
    0x0, 0x0, 0x1b, 0xf3, 0x0, 0x6f, 0xff, 0x80,
    0x0, 0x13, 0x4b, 0xf5, 0x1, 0x0, 0x6, 0xf9,
    0x6f, 0xb9, 0xaf, 0xf4, 0x29, 0xdf, 0xfb, 0x40,

    /* U+0438 "и" */
    0xbf, 0x20, 0x0, 0xcf, 0x4b, 0xf2, 0x0, 0xbf,
    0xf4, 0xbf, 0x20, 0x8f, 0xff, 0x4b, 0xf2, 0x6f,
    0xba, 0xf4, 0xbf, 0x6f, 0xd1, 0x9f, 0x4b, 0xff,
    0xe2, 0x9, 0xf4, 0xbf, 0xf4, 0x0, 0x9f, 0x4b,
    0xf6, 0x0, 0x9, 0xf4,

    /* U+0439 "й" */
    0x4, 0xf1, 0xc, 0x90, 0x0, 0xa, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x10, 0x0, 0xb, 0xf2, 0x0,
    0xc, 0xf4, 0xbf, 0x20, 0xb, 0xff, 0x4b, 0xf2,
    0x8, 0xff, 0xf4, 0xbf, 0x26, 0xfb, 0xaf, 0x4b,
    0xf6, 0xfd, 0x19, 0xf4, 0xbf, 0xfe, 0x20, 0x9f,
    0x4b, 0xff, 0x40, 0x9, 0xf4, 0xbf, 0x60, 0x0,
    0x9f, 0x40,

    /* U+043A "к" */
    0xbf, 0x20, 0x5, 0xfa, 0xb, 0xf2, 0x1, 0xed,
    0x0, 0xbf, 0x20, 0xbf, 0x30, 0xb, 0xfb, 0xff,
    0x70, 0x0, 0xbf, 0xff, 0xf7, 0x0, 0xb, 0xf2,
    0x1e, 0xf4, 0x0, 0xbf, 0x20, 0x3f, 0xe1, 0xb,
    0xf2, 0x0, 0x6f, 0xc0,

    /* U+043B "л" */
    0x0, 0xff, 0xff, 0xff, 0x80, 0xf, 0xf9, 0x9d,
    0xf8, 0x1, 0xf9, 0x0, 0x5f, 0x80, 0x2f, 0x80,
    0x5, 0xf8, 0x3, 0xf7, 0x0, 0x5f, 0x80, 0x6f,
    0x40, 0x5, 0xf8, 0x7e, 0xf0, 0x0, 0x5f, 0x8d,
    0xe6, 0x0, 0x5, 0xf8,

    /* U+043C "м" */
    0xbf, 0x80, 0x0, 0x3, 0xfd, 0xbf, 0xf1, 0x0,
    0xd, 0xfd, 0xbf, 0xfa, 0x0, 0x6f, 0xfd, 0xbf,
    0x8f, 0x31, 0xfa, 0xdd, 0xbf, 0xe, 0xca, 0xf1,
    0xdd, 0xbf, 0x5, 0xff, 0x70, 0xdd, 0xbf, 0x0,
    0xcd, 0x0, 0xdd, 0xbf, 0x0, 0x11, 0x0, 0xdd,

    /* U+043D "н" */
    0xbf, 0x20, 0x0, 0xdf, 0xb, 0xf2, 0x0, 0xd,
    0xf0, 0xbf, 0x20, 0x0, 0xdf, 0xb, 0xfa, 0x99,
    0x9e, 0xf0, 0xbf, 0xff, 0xff, 0xff, 0xb, 0xf2,
    0x0, 0xd, 0xf0, 0xbf, 0x20, 0x0, 0xdf, 0xb,
    0xf2, 0x0, 0xd, 0xf0,

    /* U+043E "о" */
    0x0, 0x5c, 0xfe, 0xa3, 0x0, 0x8, 0xfe, 0x9a,
    0xff, 0x30, 0x2f, 0xd1, 0x0, 0x4f, 0xc0, 0x6f,
    0x80, 0x0, 0xd, 0xf0, 0x5f, 0x80, 0x0, 0xd,
    0xf0, 0x2f, 0xe1, 0x0, 0x5f, 0xc0, 0x8, 0xfe,
    0xab, 0xff, 0x30, 0x0, 0x5c, 0xfe, 0xb2, 0x0,

    /* U+043F "п" */
    0xbf, 0xff, 0xff, 0xff, 0xbf, 0xb9, 0x99, 0xff,
    0xbf, 0x20, 0x0, 0xef, 0xbf, 0x20, 0x0, 0xef,
    0xbf, 0x20, 0x0, 0xef, 0xbf, 0x20, 0x0, 0xef,
    0xbf, 0x20, 0x0, 0xef, 0xbf, 0x20, 0x0, 0xef,

    /* U+0440 "р" */
    0xbf, 0x5c, 0xfe, 0x91, 0xb, 0xff, 0xc9, 0xcf,
    0xd1, 0xbf, 0x90, 0x0, 0x9f, 0x7b, 0xf2, 0x0,
    0x3, 0xfb, 0xbf, 0x30, 0x0, 0x3f, 0xbb, 0xfb,
    0x0, 0xa, 0xf7, 0xbf, 0xfc, 0x9d, 0xfd, 0xb,
    0xf6, 0xcf, 0xe9, 0x10, 0xbf, 0x20, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x0,
    0x0, 0x0,

    /* U+0441 "с" */
    0x0, 0x5c, 0xfe, 0xb2, 0x0, 0x8f, 0xe9, 0xaf,
    0xf1, 0x2f, 0xd1, 0x0, 0x34, 0x6, 0xf8, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x2, 0xfe,
    0x10, 0x4, 0x50, 0x7, 0xfe, 0xab, 0xff, 0x10,
    0x5, 0xcf, 0xfb, 0x20,

    /* U+0442 "т" */
    0xef, 0xff, 0xff, 0xf9, 0x89, 0xaf, 0xf9, 0x95,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0x0, 0x1f, 0xc0, 0x0,

    /* U+0443 "у" */
    0xc, 0xf2, 0x0, 0x3, 0xf9, 0x5, 0xf9, 0x0,
    0xa, 0xf2, 0x0, 0xef, 0x10, 0x1f, 0xb0, 0x0,
    0x7f, 0x70, 0x7f, 0x40, 0x0, 0x1f, 0xd0, 0xed,
    0x0, 0x0, 0x9, 0xfa, 0xf6, 0x0, 0x0, 0x2,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xcf, 0x20, 0x0, 0xb, 0xab, 0xf9,
    0x0, 0x0, 0xa, 0xee, 0x90, 0x0, 0x0,

    /* U+0445 "х" */
    0x6f, 0xa0, 0x3, 0xfd, 0x0, 0xaf, 0x60, 0xdf,
    0x20, 0x0, 0xdf, 0xdf, 0x50, 0x0, 0x2, 0xff,
    0xa0, 0x0, 0x0, 0x3f, 0xfb, 0x0, 0x0, 0x1e,
    0xeb, 0xf7, 0x0, 0xc, 0xf4, 0xd, 0xf3, 0x8,
    0xf8, 0x0, 0x2f, 0xe1,

    /* U+0447 "ч" */
    0x7f, 0x70, 0x0, 0xdf, 0x17, 0xf7, 0x0, 0xd,
    0xf1, 0x6f, 0x70, 0x0, 0xdf, 0x14, 0xfd, 0x42,
    0x5f, 0xf1, 0x9, 0xff, 0xff, 0xff, 0x10, 0x2,
    0x43, 0xd, 0xf1, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0xd, 0xf1,

    /* U+0449 "щ" */
    0xbf, 0x20, 0xc, 0xf1, 0x0, 0xdf, 0x10, 0xbf,
    0x20, 0xc, 0xf1, 0x0, 0xdf, 0x10, 0xbf, 0x20,
    0xc, 0xf1, 0x0, 0xdf, 0x10, 0xbf, 0x20, 0xc,
    0xf1, 0x0, 0xdf, 0x10, 0xbf, 0x20, 0xc, 0xf1,
    0x0, 0xdf, 0x10, 0xbf, 0x20, 0xc, 0xf1, 0x0,
    0xdf, 0x10, 0xbf, 0xb9, 0x9f, 0xfa, 0x99, 0xff,
    0xa2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x81,

    /* U+044B "ы" */
    0xbf, 0x20, 0x0, 0x0, 0xaf, 0x3b, 0xf2, 0x0,
    0x0, 0xa, 0xf3, 0xbf, 0x20, 0x0, 0x0, 0xaf,
    0x3b, 0xff, 0xff, 0xd5, 0xa, 0xf3, 0xbf, 0x53,
    0x5e, 0xf1, 0xaf, 0x3b, 0xf2, 0x0, 0xaf, 0x4a,
    0xf3, 0xbf, 0x65, 0x7f, 0xf1, 0xaf, 0x3b, 0xff,
    0xfe, 0xb3, 0xa, 0xf3,

    /* U+044C "ь" */
    0xbf, 0x20, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0xbf, 0xff, 0xfd, 0x60,
    0xbf, 0x53, 0x5d, 0xf3, 0xbf, 0x20, 0x8, 0xf6,
    0xbf, 0x75, 0x7e, 0xf2, 0xbf, 0xff, 0xfc, 0x40,

    /* U+044E "ю" */
    0xbf, 0x20, 0x7, 0xdf, 0xe9, 0x10, 0xbf, 0x20,
    0xbf, 0xc8, 0xbf, 0xd0, 0xbf, 0x24, 0xfb, 0x0,
    0x8, 0xf7, 0xbf, 0xff, 0xf5, 0x0, 0x2, 0xfb,
    0xbf, 0xbf, 0xf5, 0x0, 0x2, 0xfa, 0xbf, 0x24,
    0xfb, 0x0, 0x9, 0xf6, 0xbf, 0x20, 0xaf, 0xc7,
    0xbf, 0xd0, 0xbf, 0x20, 0x7, 0xdf, 0xe9, 0x0,

    /* U+05D0 "א" */
    0x1a, 0x0, 0x3, 0x91, 0x4, 0xf4, 0x0, 0x6f,
    0xf4, 0x1f, 0xf3, 0x3, 0xff, 0x60, 0x5f, 0xf3,
    0x8, 0x40, 0x4, 0xef, 0xe4, 0xb0, 0x0, 0xd1,
    0x6f, 0xf8, 0x0, 0xf, 0x20, 0x7f, 0xe2, 0x0,
    0xf9, 0x0, 0x7f, 0xe0, 0xc, 0xe0, 0x0, 0x8f,
    0x35, 0xff, 0x0, 0x0, 0xb1,

    /* U+05D1 "ב" */
    0x18, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xfe, 0x40,
    0x2f, 0xff, 0xff, 0xc0, 0x1, 0x11, 0x15, 0xe0,
    0x0, 0x0, 0x0, 0xd0, 0x0, 0x0, 0x0, 0xd0,
    0x0, 0x0, 0x0, 0xd0, 0x1, 0x11, 0x12, 0xc1,
    0x3f, 0xff, 0xff, 0xfa, 0x7f, 0xff, 0xff, 0xfa,

    /* U+05D4 "ה" */
    0x8, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xf1, 0x1f, 0xff, 0xff, 0xff, 0x10, 0x12, 0x22,
    0x22, 0xb0, 0x0, 0x0, 0x0, 0x1c, 0x0, 0xc2,
    0x0, 0x2, 0xc0, 0xe, 0x20, 0x0, 0x2d, 0x0,
    0xe2, 0x0, 0x2, 0xe0, 0xe, 0x20, 0x0, 0x2e,
    0x0, 0xc1, 0x0, 0x2, 0xb0,

    /* U+05D5 "ו" */
    0x45, 0x0, 0x7f, 0xf7, 0x5f, 0xf9, 0x1, 0x6a,
    0x0, 0x4a, 0x0, 0x4b, 0x0, 0x4b, 0x0, 0x4c,
    0x0, 0x4c, 0x0, 0x49,

    /* U+05D9 "י" */
    0x45, 0x0, 0x7, 0xff, 0xe1, 0x5f, 0xff, 0x40,
    0x12, 0xf2, 0x0, 0x3c, 0x0, 0x1d, 0x20, 0x0,
    0x10, 0x0,

    /* U+05DC "ל" */
    0x96, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0xc,
    0x0, 0x0, 0x3, 0xa0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0x85, 0xff, 0xff, 0xfe, 0x1, 0x11, 0x13,
    0xe0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x7, 0xa0,
    0x0, 0x8, 0xb0, 0x0, 0x8, 0xb0, 0x0, 0x1,
    0xf0, 0x0, 0x0, 0x1c, 0x0, 0x0,

    /* U+05DD "ם" */
    0x8, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xe3,
    0x1f, 0xff, 0xff, 0xfa, 0x9, 0x61, 0x11, 0x7c,
    0x1c, 0x0, 0x0, 0x1c, 0x3b, 0x0, 0x0, 0x1c,
    0x3b, 0x0, 0x0, 0x1c, 0x3b, 0x11, 0x11, 0x5c,
    0x3f, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xff, 0xf8,

    /* U+05E0 "נ" */
    0x7, 0x20, 0x0, 0xaf, 0xf5, 0x9, 0xff, 0xb0,
    0x1, 0x5b, 0x0, 0x2, 0xb0, 0x0, 0x2b, 0x0,
    0x2, 0xb0, 0x11, 0x5b, 0x5f, 0xff, 0xa9, 0xff,
    0xf4,

    /* U+05E1 "ס" */
    0x18, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x30, 0x2f, 0xff, 0xff, 0xfb, 0x0, 0x84, 0x11,
    0x28, 0xf0, 0xb, 0x0, 0x0, 0xe, 0x1, 0xc0,
    0x0, 0x0, 0xd0, 0x1e, 0x0, 0x0, 0xe, 0x0,
    0xf9, 0x10, 0x2a, 0xb0, 0x8, 0xff, 0xff, 0xf3,
    0x0, 0x8, 0xef, 0xd4, 0x0,

    /* U+05E2 "ע" */
    0x67, 0x10, 0x2a, 0x10, 0x9f, 0xf4, 0x5f, 0xf7,
    0x6f, 0xf5, 0x2b, 0xe8, 0x6, 0x50, 0x0, 0xa4,
    0x5, 0x90, 0x2, 0xe0, 0x1, 0xf1, 0xb, 0x80,
    0x0, 0xaa, 0xae, 0x10, 0x0, 0x8f, 0xf4, 0x0,
    0x4d, 0xff, 0x60, 0x0, 0x9f, 0xd3, 0x0, 0x0,
    0x98, 0x0, 0x0, 0x0,

    /* U+05E3 "ף" */
    0x72, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x90,
    0x9f, 0xff, 0xff, 0xf2, 0xc, 0x31, 0x12, 0xd4,
    0xc, 0x0, 0x0, 0xa4, 0x3e, 0xe5, 0x0, 0xa5,
    0x49, 0x85, 0x0, 0xa5, 0x0, 0x0, 0x0, 0xa5,
    0x0, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x0, 0xa6,
    0x0, 0x0, 0x0, 0xa6, 0x0, 0x0, 0x0, 0xa6,
    0x0, 0x0, 0x0, 0xa6, 0x0, 0x0, 0x0, 0x94,

    /* U+05E4 "פ" */
    0x9, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xb1,
    0xf, 0xff, 0xff, 0xf9, 0x3, 0xa1, 0x12, 0x9c,
    0x9, 0x85, 0x0, 0x1c, 0xc, 0xef, 0x0, 0x1c,
    0x2, 0x2, 0x0, 0x1c, 0x1, 0x11, 0x12, 0x9b,
    0xf, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0xb1,

    /* U+05E6 "צ" */
    0x26, 0x0, 0x9, 0x0, 0x5f, 0xf4, 0x2f, 0xf7,
    0x4f, 0xf7, 0x1f, 0xfa, 0x1, 0x77, 0x5, 0xa2,
    0x0, 0x5c, 0x1b, 0x0, 0x0, 0xc, 0xeb, 0x0,
    0x0, 0x0, 0xaf, 0x50, 0x1, 0x11, 0x1d, 0xe0,
    0x6f, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xa0,

    /* U+05E7 "ק" */
    0x80, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf6, 0xdf,
    0xff, 0xff, 0xd0, 0x11, 0x11, 0x4d, 0x10, 0x0,
    0x0, 0xdb, 0x10, 0x0, 0x2c, 0xc1, 0x0, 0x2d,
    0x5c, 0x20, 0x4d, 0x40, 0xc2, 0xd, 0x40, 0xc,
    0x20, 0xc2, 0x0, 0xc3, 0x0, 0x0, 0xc, 0x30,
    0x0, 0x0, 0xc4, 0x0, 0x0, 0xb, 0x20, 0x0,
    0x0,

    /* U+05E8 "ר" */
    0x81, 0x0, 0x0, 0xd, 0xff, 0xff, 0x90, 0xbf,
    0xff, 0xff, 0x30, 0x11, 0x12, 0xc5, 0x0, 0x0,
    0x9, 0x50, 0x0, 0x0, 0x96, 0x0, 0x0, 0x9,
    0x60, 0x0, 0x0, 0x97, 0x0, 0x0, 0x9, 0x70,
    0x0, 0x0, 0x85,

    /* U+05E9 "ש" */
    0x4, 0x0, 0x31, 0x0, 0x40, 0x4, 0xea, 0x29,
    0xd9, 0xf, 0xb3, 0x4f, 0xf7, 0xaf, 0xf2, 0xff,
    0x80, 0x8a, 0x21, 0xc8, 0x3, 0xa6, 0xa, 0x30,
    0xc, 0x0, 0x9, 0x30, 0xc2, 0x0, 0xd0, 0x0,
    0xd0, 0xb, 0x30, 0x2a, 0x0, 0x5c, 0x0, 0xa7,
    0x16, 0x81, 0x5e, 0x70, 0x7, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x5f, 0xff, 0xff, 0xd2, 0x0,

    /* U+090F "ए" */
    0x9f, 0xff, 0xff, 0xff, 0xf9, 0x16, 0xff, 0x66,
    0xff, 0x76, 0x0, 0xdc, 0x0, 0x8f, 0x10, 0x0,
    0xdc, 0x0, 0x8f, 0x10, 0x0, 0xdc, 0x0, 0x9f,
    0x0, 0x0, 0xdc, 0x0, 0xed, 0x0, 0x0, 0x9e,
    0x0, 0x55, 0x0, 0x0, 0x1c, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x3,
    0xe9, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x20, 0x0,
    0x0, 0x0, 0xaf, 0x20, 0x0, 0x0, 0x0, 0x16,
    0x0,

    /* U+0915 "क" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x41, 0x66,
    0x66, 0x6f, 0xf6, 0x66, 0x64, 0x0, 0x2, 0x10,
    0xfa, 0x0, 0x0, 0x0, 0xb, 0xff, 0xbf, 0xa0,
    0x0, 0x0, 0x6, 0xf9, 0x35, 0xff, 0xff, 0x70,
    0x0, 0x6d, 0x0, 0x1f, 0xf8, 0x3d, 0x30, 0x1,
    0xd3, 0x4d, 0xfc, 0x0, 0xe5, 0x0, 0x3, 0xdf,
    0xdf, 0xa0, 0x9f, 0x20, 0x0, 0x0, 0x10, 0xfa,
    0x4, 0x80, 0x0, 0x0, 0x0, 0x6, 0x90, 0x0,
    0x0,

    /* U+0924 "त" */
    0x9f, 0xff, 0xff, 0xff, 0xfc, 0x1, 0x66, 0x66,
    0x6c, 0xfa, 0x60, 0x0, 0x0, 0x21, 0x5f, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xf4, 0x0, 0x1, 0xfe,
    0x64, 0xcf, 0x40, 0x0, 0x4f, 0x70, 0x5, 0xf4,
    0x0, 0x2, 0xf6, 0x0, 0x5f, 0x40, 0x0, 0xc,
    0xa0, 0x5, 0xf4, 0x0, 0x0, 0x3f, 0x40, 0x5f,
    0x40, 0x0, 0x0, 0x4d, 0x1, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0925 "थ" */
    0x9, 0xfc, 0x2c, 0xff, 0xf8, 0x1f, 0xa7, 0xd2,
    0xef, 0x75, 0xe, 0x71, 0xf2, 0x8f, 0x10, 0x3,
    0xde, 0xf1, 0x8f, 0x10, 0x6, 0xef, 0x70, 0x8f,
    0x10, 0x2f, 0xd3, 0x0, 0xff, 0x10, 0x8, 0xb3,
    0x4c, 0xff, 0x10, 0x0, 0x8f, 0xff, 0xdf, 0x10,
    0x0, 0x0, 0x20, 0x8f, 0x10, 0x0, 0x0, 0x0,
    0x3c, 0x10,

    /* U+092A "प" */
    0x9f, 0xff, 0xff, 0xff, 0xf4, 0x17, 0xfe, 0x66,
    0xff, 0x64, 0x1, 0xf8, 0x0, 0xcc, 0x0, 0x1,
    0xf8, 0x0, 0xcc, 0x0, 0x0, 0xf8, 0x0, 0xdc,
    0x0, 0x0, 0x9c, 0x38, 0xfc, 0x0, 0x0, 0x9,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x11, 0xcc, 0x0,
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x4a, 0x0,

    /* U+092F "य" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x21, 0x66, 0xfb,
    0x66, 0xff, 0x63, 0x0, 0x7, 0xd0, 0xf, 0xa0,
    0x0, 0x1, 0xdf, 0x0, 0xfa, 0x0, 0x3, 0xef,
    0x90, 0xf, 0xa0, 0x0, 0x1f, 0xa0, 0x2, 0xfa,
    0x0, 0x0, 0x7b, 0x36, 0xef, 0xa0, 0x0, 0x0,
    0x7f, 0xfe, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x69, 0x0,

    /* U+0930 "र" */
    0x9f, 0xff, 0xff, 0xf5, 0x16, 0x66, 0xfd, 0x64,
    0x0, 0x0, 0xca, 0x0, 0x0, 0x0, 0xdb, 0x0,
    0x1, 0xed, 0xf9, 0x0, 0x1, 0xff, 0xd1, 0x0,
    0x0, 0xab, 0x0, 0x0, 0x0, 0x1e, 0x80, 0x0,
    0x0, 0x3, 0xfb, 0x20, 0x0, 0x0, 0x4f, 0x40,
    0x0, 0x0, 0x1, 0x0,

    /* U+0932 "ल" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x1, 0x66,
    0x66, 0x66, 0x6c, 0xfa, 0x60, 0x0, 0x1, 0x10,
    0x0, 0x6f, 0x30, 0x0, 0x8, 0xff, 0xb9, 0xff,
    0xf3, 0x0, 0x2, 0xfd, 0x4e, 0xf7, 0x6f, 0x30,
    0x0, 0x3f, 0x60, 0xbe, 0x5, 0xf3, 0x0, 0x0,
    0xe7, 0x7, 0xb0, 0x5f, 0x30, 0x0, 0x6, 0xd0,
    0x0, 0x5, 0xf3, 0x0, 0x0, 0xa, 0x90, 0x0,
    0x5f, 0x30, 0x0, 0x0, 0xa, 0x70, 0x1, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0938 "स" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x16, 0x66,
    0xfd, 0x66, 0xff, 0x65, 0x0, 0x0, 0xca, 0x0,
    0xaf, 0x0, 0x0, 0x0, 0xdb, 0x0, 0xaf, 0x0,
    0x1, 0xee, 0xff, 0x23, 0xff, 0x0, 0x1, 0xff,
    0xfd, 0xff, 0xff, 0x0, 0x0, 0xac, 0x0, 0x1,
    0xaf, 0x0, 0x0, 0x1e, 0x80, 0x0, 0xaf, 0x0,
    0x0, 0x3, 0xfb, 0x20, 0xaf, 0x0, 0x0, 0x0,
    0x4f, 0x40, 0x3c, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0,

    /* U+0939 "ह" */
    0x9f, 0xff, 0xff, 0xff, 0xa0, 0x16, 0x66, 0x6f,
    0xf6, 0x60, 0x0, 0x0, 0x1a, 0xf0, 0x0, 0x0,
    0x9f, 0xff, 0xf0, 0x0, 0x0, 0xf9, 0x32, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0x80, 0x0, 0x0, 0xbf,
    0x72, 0xa7, 0x0, 0x0, 0xea, 0x0, 0x7c, 0x0,
    0x0, 0xd9, 0x0, 0xea, 0x0, 0x0, 0x6e, 0x10,
    0x42, 0x0, 0x0, 0x9, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x49, 0x0, 0x0,

    /* U+093E "ा" */
    0x9f, 0xff, 0xf3, 0x16, 0xff, 0x64, 0x0, 0xdc,
    0x0, 0x0, 0xdc, 0x0, 0x0, 0xdc, 0x0, 0x0,
    0xdc, 0x0, 0x0, 0xdc, 0x0, 0x0, 0xdc, 0x0,
    0x0, 0xdc, 0x0, 0x0, 0x5a, 0x0,

    /* U+093F "ि" */
    0x0, 0x6d, 0xfe, 0x92, 0x0, 0x0, 0x3f, 0xe9,
    0x8b, 0xf8, 0x0, 0x4, 0xf2, 0x0, 0x1, 0xaa,
    0x0, 0xc, 0x10, 0x0, 0x0, 0x97, 0xbf, 0xff,
    0xf2, 0x0, 0x0, 0x1, 0x6f, 0xf6, 0x30, 0x0,
    0x0, 0x0, 0xdc, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xdc, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdc, 0x0, 0x0, 0x0, 0x0, 0xd, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xdc, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xb0, 0x0, 0x0, 0x0,

    /* U+0940 "ी" */
    0x8, 0xfd, 0x40, 0x0, 0x2, 0xfe, 0xaf, 0x40,
    0x0, 0x1f, 0x40, 0x3d, 0x0, 0x0, 0x87, 0x0,
    0x94, 0x0, 0x0, 0xb, 0xff, 0xff, 0x20, 0x0,
    0x16, 0xff, 0x63, 0x0, 0x0, 0xd, 0xc0, 0x0,
    0x0, 0x0, 0xdc, 0x0, 0x0, 0x0, 0xd, 0xc0,
    0x0, 0x0, 0x0, 0xdc, 0x0, 0x0, 0x0, 0xd,
    0xc0, 0x0, 0x0, 0x0, 0xdc, 0x0, 0x0, 0x0,
    0xd, 0xc0, 0x0, 0x0, 0x0, 0x8b, 0x0,

    /* U+0947 "े" */
    0x15, 0x30, 0x0, 0x5f, 0xfb, 0x0, 0x8, 0x6c,
    0x90, 0x0, 0x0, 0xd2, 0x0, 0x0, 0x59, 0x0,
    0x0, 0x1,

    /* U+094D "्" */
    0x2, 0x20, 0x0, 0xe, 0xfb, 0x0, 0x3, 0x4a,
    0xb0, 0x0, 0x0, 0x97, 0x0, 0x0, 0x5,

    /* U+0964 "।" */
    0x11, 0x3, 0xe2, 0x3f, 0x43, 0xf4, 0x3f, 0x43,
    0xf4, 0x3f, 0x43, 0xf4, 0x3f, 0x40, 0xb4, 0x0,
    0x0,

    /* U+3002 "。" */
    0x1, 0x9b, 0x50, 0xd, 0x74, 0xd5, 0x2c, 0x0,
    0x59, 0xe, 0x30, 0xa7, 0x4, 0xde, 0x90,

    /* U+5021 "倡" */
    0x0, 0x4, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x99, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x5f,
    0x29, 0xc1, 0x11, 0x18, 0xe0, 0x0, 0xca, 0x9,
    0xff, 0xff, 0xff, 0xe0, 0x5, 0xf6, 0x9, 0xc1,
    0x11, 0x18, 0xe0, 0x1e, 0xf6, 0x9, 0xd4, 0x44,
    0x4a, 0xe0, 0x9f, 0xf6, 0x7, 0xcc, 0xcc, 0xcc,
    0xb0, 0x35, 0xe6, 0x14, 0x44, 0x44, 0x44, 0x42,
    0x0, 0xe6, 0x3f, 0xee, 0xee, 0xee, 0xf9, 0x0,
    0xe6, 0x3f, 0x30, 0x0, 0x0, 0xd9, 0x0, 0xe6,
    0x3f, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xe6, 0x3f,
    0x31, 0x11, 0x11, 0xd9, 0x0, 0xe6, 0x3f, 0xdc,
    0xcc, 0xcc, 0xf9, 0x0, 0xe6, 0x3f, 0x75, 0x55,
    0x55, 0xd8,

    /* U+5145 "充" */
    0x0, 0x0, 0x0, 0x48, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x4,
    0x44, 0x44, 0x4d, 0xa4, 0x44, 0x44, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1, 0x11,
    0x8f, 0x61, 0x15, 0x51, 0x11, 0x0, 0x0, 0x4f,
    0x80, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x4f, 0xb1,
    0x23, 0x34, 0xdf, 0x50, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x66, 0x5f, 0x81,
    0x9f, 0x0, 0xb5, 0x0, 0x0, 0x4, 0xf4, 0x8,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x8f,
    0x0, 0x7, 0x10, 0x0, 0x5f, 0x90, 0x8, 0xf0,
    0x0, 0xf5, 0x3, 0x9f, 0xc0, 0x0, 0x8f, 0x65,
    0x7f, 0x30, 0xdf, 0x80, 0x0, 0x2, 0xdf, 0xff,
    0xa0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+548C "和" */
    0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0, 0x3,
    0x69, 0xdf, 0xe2, 0x22, 0x22, 0x21, 0xa, 0xcb,
    0xf4, 0x0, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xf1,
    0x0, 0xf5, 0x22, 0xe7, 0x2, 0x26, 0xf4, 0x21,
    0xf4, 0x0, 0xe7, 0x1f, 0xff, 0xff, 0xf8, 0xf4,
    0x0, 0xe7, 0x1, 0x1d, 0xf5, 0x11, 0xf4, 0x0,
    0xe7, 0x0, 0x4f, 0xfe, 0x20, 0xf4, 0x0, 0xe7,
    0x0, 0xcc, 0xfa, 0xd1, 0xf4, 0x0, 0xe7, 0x6,
    0xf5, 0xf2, 0xd4, 0xf4, 0x0, 0xe7, 0x2f, 0x74,
    0xf1, 0x10, 0xf4, 0x0, 0xe7, 0x2b, 0x4, 0xf1,
    0x0, 0xff, 0xff, 0xf7, 0x0, 0x4, 0xf1, 0x0,
    0xf7, 0x44, 0xf7, 0x0, 0x4, 0xf1, 0x0, 0x72,
    0x0, 0x53,

    /* U+5BF9 "对" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe7, 0x0, 0xc,
    0xdd, 0xde, 0x90, 0x0, 0xe, 0x70, 0x0, 0x89,
    0x99, 0xea, 0x11, 0x11, 0xe8, 0x10, 0x0, 0x0,
    0xe, 0x7e, 0xff, 0xff, 0xff, 0x20, 0x14, 0x1,
    0xf5, 0x33, 0x33, 0xe9, 0x30, 0x6, 0xf5, 0x5f,
    0x11, 0x20, 0xe, 0x70, 0x0, 0x7, 0xfd, 0xc0,
    0x8d, 0x0, 0xe7, 0x0, 0x0, 0x8, 0xf9, 0x1,
    0xe7, 0xe, 0x70, 0x0, 0x0, 0x8f, 0xf3, 0x8,
    0xc0, 0xe7, 0x0, 0x0, 0x2f, 0x8c, 0xe0, 0x10,
    0xe, 0x70, 0x0, 0x1d, 0xd0, 0x2d, 0x10, 0x0,
    0xe7, 0x0, 0x1d, 0xe3, 0x0, 0x0, 0x17, 0x7f,
    0x60, 0x0, 0xa3, 0x0, 0x0, 0x0, 0xef, 0xc1,
    0x0,

    /* U+5BFC "导" */
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0xf, 0x83, 0x33, 0x33, 0x34, 0xf5, 0x0, 0x0,
    0xfa, 0x55, 0x55, 0x55, 0x5f, 0x50, 0x0, 0xf,
    0xed, 0xdd, 0xdd, 0xdd, 0xd4, 0x0, 0x0, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x11,
    0x11, 0x16, 0x91, 0x0, 0x0, 0x55, 0x55, 0x55,
    0x55, 0xaf, 0x55, 0x50, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0xad, 0x20, 0x0,
    0x8e, 0x0, 0x0, 0x0, 0x0, 0xbe, 0x20, 0x8,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x93, 0x33, 0xae,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfe, 0x70,
    0x0, 0x0,

    /* U+5F00 "开" */
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x35, 0x5c, 0xc5, 0x55, 0x9f, 0x55, 0x30, 0x0,
    0x0, 0xbb, 0x0, 0x6, 0xf0, 0x0, 0x0, 0x0,
    0xb, 0xb0, 0x0, 0x6f, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0x0, 0x6, 0xf0, 0x0, 0x0, 0x55, 0x5c,
    0xd5, 0x55, 0xaf, 0x55, 0x51, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0xe, 0x80,
    0x0, 0x6f, 0x0, 0x0, 0x0, 0x3, 0xf5, 0x0,
    0x6, 0xf0, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x6,
    0xf0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x6f,
    0x0, 0x0, 0xa, 0x90, 0x0, 0x0, 0x6, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+60C5 "情" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x50, 0x0, 0x8, 0xd0, 0x0, 0x0, 0x0,
    0xe5, 0x2d, 0xdd, 0xef, 0xdd, 0xd8, 0x0, 0xe,
    0xa4, 0x44, 0x4a, 0xe4, 0x44, 0x20, 0x2a, 0xeb,
    0xab, 0xdd, 0xff, 0xdd, 0xd3, 0x4, 0xbe, 0x6b,
    0x11, 0x19, 0xd1, 0x11, 0x0, 0x7a, 0xe5, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x1a, 0x7e, 0x51, 0x23,
    0x33, 0x33, 0x33, 0x20, 0x32, 0xe5, 0x9, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xe, 0x50, 0x9b, 0x0,
    0x0, 0x5f, 0x0, 0x0, 0xe5, 0x9, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xe, 0x50, 0x9b, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0xe5, 0x9, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0xe, 0x50, 0x9b, 0x0, 0x2, 0x7f,
    0x0, 0x0, 0xe5, 0x9, 0xb0, 0x0, 0xff, 0x90,
    0x0,

    /* U+6280 "技" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xf1, 0x0, 0x0, 0xe7, 0x0, 0x0, 0x0,
    0x3f, 0x10, 0x0, 0xe, 0x70, 0x0, 0x0, 0x3,
    0xf1, 0x4, 0x44, 0xf9, 0x44, 0x30, 0x6, 0x8f,
    0x76, 0xff, 0xff, 0xff, 0xfe, 0x1, 0xde, 0xfe,
    0x92, 0x22, 0xe8, 0x22, 0x20, 0x0, 0x3f, 0x10,
    0x0, 0xe, 0x70, 0x0, 0x0, 0x3, 0xf1, 0xd,
    0xff, 0xff, 0xff, 0x80, 0x1, 0x7f, 0xdb, 0x7f,
    0x44, 0x47, 0xf3, 0x1, 0xff, 0xf8, 0x30, 0xe7,
    0x0, 0xcc, 0x0, 0x4, 0x4f, 0x10, 0x6, 0xf2,
    0x8f, 0x30, 0x0, 0x3, 0xf1, 0x0, 0xb, 0xef,
    0x50, 0x0, 0x0, 0x3f, 0x10, 0x3, 0xcf, 0xf6,
    0x0, 0x0, 0x37, 0xf1, 0x5b, 0xfd, 0x49, 0xfd,
    0x71, 0xb, 0xfb, 0x8, 0xc5, 0x0, 0x3, 0xae,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+672F "术" */
    0x0, 0x0, 0x0, 0x3f, 0x44, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0x7f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0x40, 0x6f, 0x70, 0x0, 0x0,
    0x0, 0x3, 0xf4, 0x0, 0x50, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x66, 0x66,
    0x6e, 0xff, 0x76, 0x66, 0x50, 0x0, 0x0, 0x5,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x2, 0xed,
    0xf7, 0xf7, 0x0, 0x0, 0x0, 0x1, 0xdc, 0x4f,
    0x45, 0xf6, 0x0, 0x0, 0x2, 0xde, 0x13, 0xf4,
    0x9, 0xf7, 0x0, 0x6, 0xfd, 0x20, 0x3f, 0x40,
    0xa, 0xfa, 0x2, 0xfa, 0x0, 0x3, 0xf4, 0x0,
    0x9, 0xe2, 0x2, 0x0, 0x0, 0x3f, 0x40, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x3, 0xf4, 0x0, 0x0,
    0x0,

    /* U+6E90 "源" */
    0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xc2, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x3d, 0x3f, 0x83, 0x38, 0xb5, 0x33, 0x0, 0x0,
    0x0, 0xf6, 0x0, 0xae, 0x10, 0x0, 0x4, 0x0,
    0xf, 0x6d, 0xff, 0xff, 0xfe, 0x2, 0xfd, 0x40,
    0xf6, 0xd5, 0x0, 0x5, 0xe0, 0x1, 0xa7, 0xf,
    0x5d, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x1, 0xf4,
    0xd5, 0x0, 0x5, 0xe0, 0x0, 0x15, 0x3f, 0x2d,
    0xff, 0xff, 0xfe, 0x0, 0x7, 0xe5, 0xf0, 0x21,
    0x6f, 0x13, 0x20, 0x0, 0xe8, 0x9c, 0xd, 0x85,
    0xf0, 0xd8, 0x0, 0x6f, 0x2e, 0x86, 0xf1, 0x5f,
    0x4, 0xf1, 0xd, 0xa6, 0xf2, 0xc6, 0x38, 0xf0,
    0xb, 0x40, 0x63, 0x58, 0x0, 0xc, 0xf9, 0x0,
    0x0,

    /* U+6EE1 "满" */
    0x1, 0x60, 0x0, 0x2f, 0x10, 0x9b, 0x0, 0x0,
    0x5f, 0xc8, 0xde, 0xfd, 0xdf, 0xfd, 0xd1, 0x0,
    0x2b, 0x45, 0x7f, 0x65, 0xbc, 0x55, 0x0, 0x0,
    0x0, 0x1, 0x50, 0x3, 0x40, 0x0, 0x2, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x33, 0xfa, 0x2,
    0x33, 0x9b, 0x3d, 0x63, 0x30, 0x5, 0xf8, 0x14,
    0x4a, 0xb4, 0xe7, 0x43, 0x0, 0x3, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x3, 0x4f, 0xa,
    0x70, 0xf1, 0x6d, 0x0, 0x3, 0xf7, 0xf0, 0xed,
    0x5f, 0x86, 0xd0, 0x0, 0xbc, 0x4f, 0x6e, 0x9d,
    0xcf, 0x9d, 0x0, 0x4f, 0x44, 0xfb, 0x52, 0xf3,
    0x58, 0xd0, 0xd, 0xc0, 0x4f, 0x10, 0x3, 0x0,
    0x7d, 0x0, 0x53, 0x4, 0xf0, 0x0, 0x0, 0xce,
    0x80,

    /* U+70ED "热" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd6, 0x0, 0xc, 0x80, 0x0, 0x0, 0x0,
    0xd, 0x60, 0x0, 0xc8, 0x0, 0x0, 0x0, 0x55,
    0xe9, 0x52, 0x4d, 0xa4, 0x42, 0x0, 0xf, 0xff,
    0xff, 0x9e, 0xff, 0xef, 0x90, 0x0, 0x0, 0xd6,
    0x0, 0xe, 0x60, 0xb8, 0x0, 0x0, 0xe, 0xbb,
    0x43, 0xf3, 0xb, 0x80, 0x2, 0xcf, 0xfd, 0x88,
    0xff, 0x20, 0xb8, 0x0, 0x8, 0x4e, 0x60, 0xa,
    0xfe, 0x5a, 0x81, 0x0, 0x0, 0xe6, 0x5, 0xf4,
    0x7a, 0x8b, 0xa7, 0x6, 0xdf, 0x43, 0xf8, 0x0,
    0x3, 0xff, 0x40, 0x17, 0x40, 0x4, 0x0, 0x0,
    0x5, 0x40, 0x0, 0xda, 0xb, 0x60, 0xb8, 0x9,
    0xe1, 0x0, 0x8f, 0x20, 0xc9, 0x8, 0xe0, 0xe,
    0xb0, 0x1e, 0x70, 0xa, 0x90, 0x3d, 0x20, 0x5e,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7A0B "程" */
    0x0, 0x0, 0x22, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x58, 0xcf, 0xd1, 0xff, 0xff, 0xff, 0x60, 0x2d,
    0xaf, 0x80, 0xf, 0x63, 0x33, 0xf6, 0x0, 0x0,
    0xe6, 0x0, 0xf3, 0x0, 0xe, 0x60, 0x14, 0x4f,
    0x94, 0x1f, 0x63, 0x33, 0xf6, 0x3, 0xff, 0xff,
    0xf3, 0xff, 0xff, 0xff, 0x60, 0x0, 0x5f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x59,
    0xff, 0xff, 0xff, 0xe0, 0x4, 0xdf, 0xaf, 0x42,
    0x28, 0xe2, 0x22, 0x1, 0xe6, 0xe6, 0x51, 0x33,
    0x9f, 0x33, 0x20, 0x6d, 0xe, 0x60, 0x3f, 0xff,
    0xff, 0xf8, 0x0, 0x30, 0xe6, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0xe, 0x60, 0x33, 0x39, 0xf3,
    0x33, 0x0, 0x0, 0xe6, 0x3f, 0xff, 0xff, 0xff,
    0xf3,

    /* U+7F16 "编" */
    0x0, 0x7, 0x10, 0x0, 0x8, 0x30, 0x0, 0x0,
    0x4f, 0x20, 0x0, 0xe, 0xc0, 0x0, 0x0, 0xab,
    0x0, 0xdf, 0xff, 0xff, 0xfc, 0x1, 0xf4, 0x30,
    0xd7, 0x11, 0x11, 0x9c, 0x9, 0xc0, 0xf5, 0xd9,
    0x44, 0x44, 0xac, 0x4f, 0xbb, 0xc0, 0xde, 0xdd,
    0xdd, 0xda, 0x2a, 0xaf, 0x20, 0xe8, 0x22, 0x22,
    0x22, 0x0, 0xb8, 0x0, 0xef, 0xff, 0xff, 0xff,
    0x8, 0xf9, 0xc1, 0xff, 0x3d, 0xe, 0x1f, 0x3f,
    0xea, 0x63, 0xfd, 0x5e, 0x2e, 0x3f, 0x3, 0x1,
    0x66, 0xeb, 0xff, 0xff, 0xff, 0x4, 0xaf, 0xcc,
    0xab, 0x3d, 0xe, 0x1f, 0x4f, 0xa3, 0x1f, 0x4b,
    0x3d, 0xe, 0x1f, 0x1, 0x0, 0x1a, 0xb, 0x3a,
    0xa, 0xab, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8005 "者" */
    0x0, 0x0, 0x2, 0xf3, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x0, 0x2f, 0x30, 0x0, 0x6f, 0x30, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xcf, 0x70, 0x0, 0x3,
    0x44, 0x6f, 0x64, 0x7f, 0x90, 0x0, 0x0, 0x0,
    0x2, 0xf3, 0x4f, 0xa0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x4, 0x44, 0x48,
    0xff, 0x74, 0x44, 0x44, 0x0, 0x0, 0x2a, 0xfe,
    0x53, 0x33, 0x31, 0x0, 0x4, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x3, 0xfb, 0x6f, 0x40, 0x0,
    0x1, 0xf5, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x1f, 0x52, 0x22, 0x23,
    0xf5, 0x0, 0x0, 0x1, 0xf7, 0x33, 0x33, 0x4f,
    0x50, 0x0, 0x0, 0x1f, 0xfe, 0xee, 0xee, 0xe5,
    0x0,

    /* U+FB7C "ﭼ" */
    0x0, 0x9f, 0xd6, 0x0, 0x0, 0x0, 0x1e, 0xac,
    0xfd, 0x50, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfc,
    0x0, 0x89, 0xad, 0xfb, 0x54, 0x20, 0x1f, 0xfe,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x51, 0x90,
    0x0, 0x0, 0x0, 0x4e, 0x7c, 0x10, 0x0, 0x0,
    0x0, 0x4e, 0x10, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0,

    /* U+FBFE "ﯾ" */
    0x0, 0x0, 0x90, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0xe6, 0x0, 0x0, 0x87, 0x19, 0x9b, 0xf3, 0x1f,
    0xeb, 0x50, 0x0, 0x0, 0x0, 0x1, 0x82, 0xc2,
    0x4, 0xe3, 0xb1, 0x0, 0x0, 0x0,

    /* U+FBFF "ﯿ" */
    0x0, 0x0, 0x95, 0x0, 0x0, 0x2f, 0x10, 0x19,
    0xae, 0xfc, 0x61, 0xfe, 0xa4, 0xc9, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x39, 0x60, 0x0, 0xd6, 0x85,
    0x0, 0x0, 0x0, 0x0,

    /* U+FE8B "ﺋ" */
    0x0, 0x8a, 0x0, 0xc, 0x50, 0x0, 0xca, 0x0,
    0x1, 0x0, 0x0, 0xc, 0x0, 0x4, 0xf3, 0x0,
    0xe, 0x60, 0x0, 0x87, 0x19, 0xbf, 0x31, 0xeb,
    0x50,

    /* U+FE8D "ﺍ" */
    0x0, 0x9, 0xb0, 0xcf, 0xb, 0xa0, 0xab, 0x9,
    0xb0, 0x8b, 0x7, 0xb0, 0x6a, 0x5, 0xa0, 0x48,
    0x0,

    /* U+FE8E "ﺎ" */
    0x0, 0x0, 0x59, 0x0, 0xcb, 0x0, 0xbc, 0x0,
    0xac, 0x0, 0x8c, 0x0, 0x6d, 0x0, 0x5d, 0x0,
    0x3e, 0x0, 0xf, 0xb3, 0x5, 0xe6,

    /* U+FE90 "ﺐ" */
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0x0, 0x0, 0xc, 0x20, 0xe4, 0x0, 0x0, 0x0,
    0x7d, 0x0, 0x9f, 0xc9, 0x9a, 0xcf, 0xff, 0xc5,
    0x8, 0xdf, 0xfe, 0xb7, 0x13, 0xd8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0,

    /* U+FE91 "ﺑ" */
    0x0, 0x9, 0x0, 0x4, 0xf2, 0x0, 0xf, 0x50,
    0x0, 0x87, 0x19, 0xbf, 0x31, 0xec, 0x50, 0x0,
    0x0, 0x0, 0x3, 0x70, 0x0, 0xbd, 0x0, 0x0,
    0x10,

    /* U+FE92 "ﺒ" */
    0x0, 0x9, 0x50, 0x0, 0x2f, 0x0, 0x19, 0xef,
    0xc5, 0x1f, 0xb4, 0xc9, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x2, 0x0,

    /* U+FE96 "ﺖ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x2b, 0x40, 0x0, 0x0, 0x0, 0x2e, 0x4a, 0x20,
    0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0x0, 0x0, 0xc, 0x20, 0xe4, 0x0,
    0x0, 0x0, 0x7d, 0x0, 0x9f, 0xc9, 0x9a, 0xcf,
    0xff, 0xc5, 0x8, 0xdf, 0xfe, 0xb7, 0x13, 0xd8,

    /* U+FE97 "ﺗ" */
    0x2, 0x23, 0x60, 0xb, 0xb9, 0xb0, 0x1, 0x10,
    0x0, 0x0, 0x9, 0x0, 0x0, 0x4f, 0x20, 0x0,
    0xf, 0x50, 0x0, 0x8, 0x70, 0x19, 0xbf, 0x30,
    0x1e, 0xc5, 0x0,

    /* U+FE98 "ﺘ" */
    0x0, 0x0, 0x0, 0x0, 0xa, 0x5b, 0x90, 0x0,
    0xa4, 0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x95, 0x0, 0x0, 0x2f, 0x10, 0x19, 0xae, 0xfc,
    0x61, 0xfe, 0xa4, 0xc9,

    /* U+FEA0 "ﺠ" */
    0x0, 0x39, 0x71, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x61, 0x3c, 0xfe,
    0x97, 0x0, 0x0, 0x0, 0x5, 0xef, 0xfb, 0x0,
    0x19, 0x9c, 0xff, 0x87, 0xf9, 0x91, 0x1f, 0xfe,
    0xa2, 0x0, 0x9e, 0xf2, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x60, 0x0, 0x0,

    /* U+FEA6 "ﺦ" */
    0x0, 0x2, 0x30, 0x0, 0x0, 0x0, 0xc, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0x30, 0x0, 0x0, 0x28,
    0x83, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xb4, 0x10,
    0x0, 0x81, 0x19, 0xff, 0xff, 0x10, 0x0, 0x6e,
    0xdb, 0xc6, 0x0, 0x7, 0xd4, 0x0, 0xea, 0x97,
    0x1c, 0x0, 0x0, 0x3b, 0xfc, 0x65, 0x0, 0x0,
    0x0, 0x0, 0x66, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0x51, 0x1, 0x43, 0x0, 0x8, 0xff, 0xff, 0xc3,
    0x0, 0x0, 0x37, 0x73, 0x0, 0x0,

    /* U+FEA9 "ﺩ" */
    0x0, 0x0, 0x20, 0x0, 0xa, 0xb0, 0x0, 0x8,
    0xf1, 0x0, 0x1, 0xe7, 0x0, 0x0, 0x6b, 0x6d,
    0xa9, 0xc9, 0x3e, 0xfe, 0xa2,

    /* U+FEAA "ﺪ" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x5, 0xb0, 0x0,
    0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0x0, 0x8d, 0x0, 0x6d, 0xa9, 0xcf, 0xd4,
    0x3e, 0xfd, 0x95, 0xc7,

    /* U+FEAD "ﺭ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0x10, 0x0,
    0x3, 0xf6, 0x0, 0x0, 0xc, 0xb0, 0x0, 0x0,
    0x4f, 0x0, 0x0, 0x0, 0xc0, 0x0, 0x0, 0x3c,
    0x1, 0x33, 0x6e, 0x60, 0x2d, 0xff, 0x90, 0x0,
    0x4, 0x20, 0x0,

    /* U+FEAE "ﺮ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd2, 0x0,
    0x0, 0x1, 0xf7, 0x0, 0x0, 0x0, 0xac, 0x0,
    0x0, 0x0, 0x2f, 0xa1, 0x0, 0x0, 0xc, 0xe3,
    0x0, 0x0, 0x4b, 0x0, 0x13, 0x26, 0xf5, 0x0,
    0x2d, 0xff, 0x80, 0x0, 0x0, 0x42, 0x0, 0x0,

    /* U+FEB0 "ﺰ" */
    0x0, 0x2, 0x20, 0x0, 0x0, 0xd, 0xd0, 0x0,
    0x0, 0x3, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd2, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x0, 0xac, 0x0, 0x0, 0x0, 0x2f, 0xa1,
    0x0, 0x0, 0xc, 0xe3, 0x0, 0x0, 0x4b, 0x0,
    0x13, 0x26, 0xf5, 0x0, 0x2d, 0xff, 0x80, 0x0,
    0x0, 0x42, 0x0, 0x0,

    /* U+FEB3 "ﺳ" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xe1, 0x0, 0x5, 0x10, 0x86, 0x2f,
    0x40, 0x3, 0xd0, 0x1f, 0x10, 0xb7, 0x1b, 0xfd,
    0xac, 0xff, 0xcc, 0x71, 0xea, 0xdf, 0xe7, 0x9d,
    0xf2,

    /* U+FEB4 "ﺴ" */
    0x0, 0x5, 0x10, 0x86, 0xc, 0x20, 0x0, 0x3d,
    0x1, 0xf1, 0x5c, 0x0, 0x1b, 0xfd, 0xac, 0xfa,
    0xef, 0xc5, 0x1e, 0xad, 0xfe, 0xcf, 0xc4, 0xd8,

    /* U+FEB7 "ﺷ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0x10, 0x0, 0x0, 0x0, 0x1a, 0x7d, 0x20,
    0x0, 0x0, 0x3, 0xb1, 0x80, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0x10, 0x0, 0x51, 0x8, 0x62,
    0xf4, 0x0, 0x3d, 0x1, 0xf1, 0xb, 0x71, 0xbf,
    0xda, 0xcf, 0xfc, 0xc7, 0x1e, 0xad, 0xfe, 0x79,
    0xdf, 0x20,

    /* U+FEBF "ﺿ" */
    0x0, 0x0, 0x0, 0x67, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17,
    0x81, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xe3,
    0x0, 0x0, 0x40, 0x4e, 0x61, 0x2b, 0xe0, 0x0,
    0x3d, 0x2d, 0x20, 0x0, 0x5d, 0x1, 0xbf, 0xff,
    0xca, 0xbc, 0xff, 0x50, 0x1e, 0x86, 0xbe, 0xff,
    0xd9, 0x20, 0x0,

    /* U+FECB "ﻋ" */
    0x0, 0xb, 0xf9, 0x0, 0x0, 0x8, 0xc8, 0xa5,
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x1, 0x62, 0x19, 0xef, 0xcd, 0xff, 0x21, 0xff,
    0xec, 0x96, 0x20,

    /* U+FECC "ﻌ" */
    0x0, 0x7c, 0xfc, 0x20, 0x6, 0xfb, 0x9e, 0xd0,
    0x3, 0xc8, 0x7, 0xe0, 0x0, 0x1e, 0x8e, 0x60,
    0x19, 0x9d, 0xff, 0xa7, 0x1f, 0xd8, 0x4a, 0xdc,

    /* U+FECD "ﻍ" */
    0x0, 0x70, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x0,
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x17, 0x40,
    0x0, 0x0, 0x1e, 0xff, 0x80, 0x0, 0x8, 0x71,
    0x2, 0x0, 0x0, 0xb8, 0x0, 0x6, 0x10, 0x7,
    0xfa, 0x8f, 0xf1, 0x0, 0x9, 0xfe, 0x81, 0x0,
    0x0, 0xb9, 0x0, 0x0, 0x0, 0x2a, 0x0, 0x0,
    0x0, 0x4, 0x70, 0x0, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0x0, 0x0, 0xbe, 0xa9, 0x9b, 0xd1, 0x0,
    0x8e, 0xfd, 0x70, 0x0,

    /* U+FED3 "ﻓ" */
    0x0, 0x3, 0x60, 0x0, 0x0, 0xcd, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x68, 0x0, 0x0, 0x6f,
    0xfb, 0x0, 0xb, 0x24, 0xf3, 0x0, 0xc0, 0xc,
    0x70, 0x7, 0xff, 0xb9, 0x9, 0x9b, 0xbd, 0x71,
    0xfe, 0xda, 0x50,

    /* U+FED4 "ﻔ" */
    0x0, 0x3, 0xa0, 0x0, 0x0, 0x7, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x92, 0x0,
    0x0, 0x7f, 0xfc, 0x0, 0x0, 0xf6, 0x2f, 0x10,
    0x2, 0xc0, 0xb, 0x20, 0x19, 0xfd, 0xdf, 0x92,
    0x1f, 0xea, 0xad, 0xf4,

    /* U+FED6 "ﻖ" */
    0x0, 0x1, 0x11, 0x40, 0x0, 0x0, 0xbc, 0x9c,
    0x0, 0x0, 0x1, 0x20, 0x10, 0x0, 0x0, 0x5,
    0x81, 0x0, 0x0, 0x5, 0xff, 0xc0, 0x0, 0x0,
    0xb2, 0x5f, 0x40, 0x0, 0xc, 0x0, 0xba, 0x0,
    0x0, 0xec, 0x9c, 0xe6, 0xa0, 0x3, 0xbe, 0xff,
    0xcc, 0x0, 0x0, 0x1, 0xb0, 0xe2, 0x0, 0x4,
    0xd4, 0xa, 0xfa, 0xad, 0xf8, 0x0, 0x1a, 0xfe,
    0xa3, 0x0, 0x0,

    /* U+FED7 "ﻗ" */
    0x0, 0x0, 0x2, 0x0, 0xa, 0xaa, 0xd0, 0x0,
    0x34, 0x12, 0x0, 0x0, 0x68, 0x0, 0x0, 0x6f,
    0xfb, 0x0, 0xb, 0x24, 0xf3, 0x0, 0xc0, 0xc,
    0x70, 0x7, 0xff, 0xb9, 0x9, 0x9b, 0xbd, 0x71,
    0xfe, 0xda, 0x50,

    /* U+FED8 "ﻘ" */
    0x0, 0x2, 0x5, 0x0, 0x0, 0x8e, 0x7e, 0x0,
    0x0, 0x13, 0x1, 0x0, 0x0, 0x4, 0x92, 0x0,
    0x0, 0x7f, 0xfc, 0x0, 0x0, 0xf6, 0x2f, 0x10,
    0x2, 0xc0, 0xb, 0x20, 0x19, 0xfd, 0xdf, 0x92,
    0x1f, 0xea, 0xad, 0xf4,

    /* U+FEDF "ﻟ" */
    0x0, 0x0, 0x1, 0xb1, 0x5, 0xf2, 0x2, 0xf2,
    0x0, 0xf3, 0x0, 0xd4, 0x0, 0xb4, 0x0, 0x95,
    0x0, 0x75, 0x1a, 0xe3, 0x2f, 0xa0,

    /* U+FEE0 "ﻠ" */
    0x0, 0x0, 0x0, 0x1a, 0x20, 0x4, 0xf2, 0x0,
    0x2f, 0x20, 0x1, 0xf2, 0x0, 0xf, 0x20, 0x0,
    0xd2, 0x0, 0xc, 0x20, 0x0, 0x92, 0x1, 0x9e,
    0xd8, 0x1f, 0xab, 0xe0,

    /* U+FEE1 "ﻡ" */
    0x0, 0x4e, 0x90, 0x0, 0x1e, 0xaf, 0x60, 0x3,
    0x40, 0x8f, 0x30, 0x3, 0x57, 0xfc, 0xb, 0xff,
    0xff, 0x71, 0xd4, 0x10, 0x0, 0x1e, 0x0, 0x0,
    0x0, 0xf1, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0,
    0xd6, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0,

    /* U+FEE3 "ﻣ" */
    0x0, 0x0, 0x57, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x0, 0xe, 0x48, 0xf0, 0x0, 0x7b, 0x2, 0xf3,
    0x1c, 0xff, 0xda, 0xf3, 0x1e, 0x67, 0xce, 0xc0,

    /* U+FEE4 "ﻤ" */
    0x0, 0x1, 0x60, 0x0, 0x0, 0x2e, 0xf7, 0x0,
    0x0, 0xbb, 0x5e, 0x0, 0x1, 0xe0, 0xf, 0x50,
    0x1b, 0xfe, 0xad, 0xf4, 0x1e, 0x9a, 0xed, 0xb6,

    /* U+FEE5 "ﻥ" */
    0x0, 0x15, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0x0, 0x13, 0x0, 0x50, 0x0, 0x0, 0x8, 0xc0,
    0x0, 0x0, 0x9, 0xf0, 0xb0, 0x0, 0x3, 0xf1,
    0xc0, 0x0, 0x0, 0xb2, 0xe2, 0x0, 0x5, 0xd0,
    0xae, 0xaa, 0xdf, 0x40, 0x1b, 0xfe, 0xa2, 0x0,

    /* U+FEE6 "ﻦ" */
    0x0, 0x26, 0x0, 0x0, 0x0, 0xa, 0xe0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x50, 0x0, 0x0, 0x0,
    0x9c, 0x0, 0x10, 0x0, 0x9, 0xfa, 0x1b, 0x0,
    0x0, 0x3f, 0xf2, 0xc0, 0x0, 0x0, 0xb2, 0xe,
    0x20, 0x0, 0x5d, 0x0, 0xae, 0xaa, 0xef, 0x40,
    0x1, 0xbf, 0xea, 0x20, 0x0,

    /* U+FEE7 "ﻧ" */
    0x0, 0x0, 0x0, 0x7, 0xa0, 0x0, 0x9a, 0x0,
    0x0, 0x0, 0x0, 0x9, 0x0, 0x4, 0xf2, 0x0,
    0xf, 0x50, 0x0, 0x87, 0x19, 0xbf, 0x31, 0xec,
    0x50,

    /* U+FEE8 "ﻨ" */
    0x0, 0x3, 0x0, 0x0, 0x9f, 0x10, 0x0, 0x15,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x50, 0x0,
    0x2f, 0x0, 0x19, 0xef, 0xc5, 0x1f, 0xb4, 0xc9,

    /* U+FEEA "ﻪ" */
    0x0, 0x4, 0x40, 0x0, 0x1, 0xf6, 0x0, 0x6,
    0xff, 0x60, 0x5, 0xc3, 0x97, 0x0, 0xd8, 0x16,
    0x70, 0x9, 0xff, 0xde, 0x91, 0x2, 0x62, 0x9f,
    0x20,

    /* U+FEEB "ﻫ" */
    0x0, 0x9, 0x70, 0x0, 0x0, 0x3f, 0xfd, 0x20,
    0x0, 0xe9, 0xee, 0xe1, 0x3, 0x90, 0x86, 0xd8,
    0x1, 0xe6, 0xd2, 0x4d, 0x1d, 0xff, 0xfb, 0x8c,
    0x1e, 0xb4, 0x7c, 0xe6,

    /* U+FEED "ﻭ" */
    0x0, 0x6, 0x70, 0x0, 0x6, 0xff, 0x90, 0x0,
    0xc2, 0x5f, 0x10, 0xa, 0x0, 0xb6, 0x0, 0xfa,
    0x8d, 0x80, 0x6, 0xfd, 0x96, 0x0, 0x0, 0xc,
    0x31, 0x42, 0x4c, 0xb0, 0x1c, 0xff, 0xc1, 0x0,
    0x3, 0x30, 0x0,

    /* U+FEEE "ﻮ" */
    0x0, 0x6, 0x70, 0x0, 0x0, 0x6f, 0xf9, 0x0,
    0x0, 0xc2, 0x5f, 0x10, 0x0, 0xb0, 0xb, 0x50,
    0x0, 0xfb, 0x8b, 0xc3, 0x0, 0x4c, 0xff, 0xf7,
    0x0, 0x0, 0xc, 0x30, 0x14, 0x24, 0xcc, 0x0,
    0x1c, 0xff, 0xc1, 0x0, 0x0, 0x33, 0x0, 0x0,

    /* U+FEF1 "ﻱ" */
    0x0, 0x0, 0x2, 0x83, 0x0, 0x0, 0x2f, 0xfc,
    0x0, 0x0, 0xda, 0x12, 0x0, 0x4, 0xb0, 0x0,
    0x10, 0x6, 0xa3, 0x0, 0xa0, 0x2, 0xff, 0xf7,
    0xc0, 0x0, 0x27, 0x9c, 0xe2, 0x0, 0x3, 0xc7,
    0xaf, 0xaa, 0xdf, 0x80, 0x1b, 0xfe, 0xa3, 0x0,
    0x0, 0x10, 0x40, 0x0, 0x9, 0xd8, 0xe0, 0x0,
    0x2, 0x40, 0x20, 0x0,

    /* U+FEF2 "ﻲ" */
    0x10, 0x0, 0x1, 0xca, 0x95, 0xb0, 0x0, 0x7,
    0xfe, 0xf9, 0xc0, 0x0, 0x0, 0x8e, 0x60, 0xe4,
    0x0, 0x0, 0x6, 0xc0, 0x9f, 0xb9, 0xac, 0xfd,
    0x30, 0x9, 0xef, 0xea, 0x50, 0x0, 0x0, 0x20,
    0x50, 0x0, 0x0, 0x4, 0xf5, 0xf3, 0x0, 0x0,
    0x0, 0x50, 0x20, 0x0, 0x0,

    /* U+FEF4 "ﻴ" */
    0x0, 0x0, 0x95, 0x0, 0x0, 0x2f, 0x10, 0x19,
    0xae, 0xfc, 0x61, 0xfe, 0xa4, 0xc9, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x39, 0x60, 0x0, 0xd6, 0x85,
    0x0, 0x0, 0x0, 0x0,

    /* U+FEF8 "ﻸ" */
    0x59, 0x0, 0x0, 0x0, 0x0, 0x9c, 0x10, 0x0,
    0x5a, 0x0, 0x23, 0x0, 0x0, 0xbc, 0x0, 0x6f,
    0x50, 0x0, 0xbc, 0x0, 0x2f, 0xf1, 0x0, 0xdc,
    0x0, 0x0, 0xda, 0x0, 0xdc, 0x0, 0x0, 0x1e,
    0x23, 0xbc, 0x0, 0x0, 0x6, 0x9a, 0x8c, 0x0,
    0x0, 0x0, 0xcf, 0x4b, 0x0, 0x0, 0x17, 0xfe,
    0xb, 0xb5, 0x1c, 0xff, 0xc3, 0x3, 0xd9,

    /* U+FEFC "ﻼ" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0x0, 0x3, 0x0, 0x0, 0xbc, 0x0, 0x6f,
    0x50, 0x0, 0xbc, 0x0, 0x2f, 0xf1, 0x0, 0xdc,
    0x0, 0x0, 0xda, 0x0, 0xdc, 0x0, 0x0, 0x1e,
    0x23, 0xbc, 0x0, 0x0, 0x6, 0x9a, 0x8c, 0x0,
    0x0, 0x0, 0xcf, 0x4b, 0x0, 0x0, 0x17, 0xfe,
    0xb, 0xb5, 0x1c, 0xff, 0xc3, 0x3, 0xd9
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 66, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 67, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20, .adv_w = 99, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 35, .adv_w = 171, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 90, .adv_w = 151, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 158, .adv_w = 206, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 223, .adv_w = 169, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 284, .adv_w = 53, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 292, .adv_w = 83, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 320, .adv_w = 84, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 348, .adv_w = 100, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 366, .adv_w = 142, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 394, .adv_w = 59, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 404, .adv_w = 92, .box_w = 5, .box_h = 2, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 409, .adv_w = 59, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 415, .adv_w = 89, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 471, .adv_w = 162, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 521, .adv_w = 91, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 546, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 591, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 636, .adv_w = 163, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 691, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 736, .adv_w = 150, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 786, .adv_w = 146, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 831, .adv_w = 156, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 881, .adv_w = 150, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 926, .adv_w = 59, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 942, .adv_w = 59, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 962, .adv_w = 142, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 994, .adv_w = 142, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1018, .adv_w = 142, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1050, .adv_w = 139, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1090, .adv_w = 248, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1188, .adv_w = 180, .box_w = 13, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1253, .adv_w = 183, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1303, .adv_w = 174, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1358, .adv_w = 198, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1413, .adv_w = 161, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1458, .adv_w = 153, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1503, .adv_w = 185, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1558, .adv_w = 194, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1608, .adv_w = 77, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1623, .adv_w = 126, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1663, .adv_w = 175, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1718, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1758, .adv_w = 229, .box_w = 12, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1818, .adv_w = 194, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1868, .adv_w = 202, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1928, .adv_w = 174, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1978, .adv_w = 202, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2063, .adv_w = 175, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2113, .adv_w = 151, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2158, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2203, .adv_w = 189, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2253, .adv_w = 175, .box_w = 13, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2318, .adv_w = 275, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2403, .adv_w = 166, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2458, .adv_w = 159, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2518, .adv_w = 159, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2568, .adv_w = 84, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2596, .adv_w = 89, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2652, .adv_w = 84, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2680, .adv_w = 142, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 2704, .adv_w = 120, .box_w = 8, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2712, .adv_w = 144, .box_w = 5, .box_h = 2, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 2717, .adv_w = 146, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2749, .adv_w = 165, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2799, .adv_w = 139, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2835, .adv_w = 165, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2890, .adv_w = 149, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2926, .adv_w = 89, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2965, .adv_w = 167, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3020, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3070, .adv_w = 69, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3092, .adv_w = 71, .box_w = 6, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 3134, .adv_w = 153, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3184, .adv_w = 69, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3201, .adv_w = 253, .box_w = 14, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3257, .adv_w = 164, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3293, .adv_w = 155, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3333, .adv_w = 165, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3383, .adv_w = 165, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3438, .adv_w = 101, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3458, .adv_w = 124, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3490, .adv_w = 102, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3525, .adv_w = 163, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3557, .adv_w = 139, .box_w = 10, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3597, .adv_w = 221, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3653, .adv_w = 138, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3689, .adv_w = 139, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 3744, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3776, .adv_w = 89, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3818, .adv_w = 73, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3839, .adv_w = 89, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3874, .adv_w = 142, .box_w = 9, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 3892, .adv_w = 66, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3892, .adv_w = 67, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3912, .adv_w = 139, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3966, .adv_w = 158, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4016, .adv_w = 168, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4071, .adv_w = 173, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4131, .adv_w = 73, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4152, .adv_w = 123, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4204, .adv_w = 144, .box_w = 5, .box_h = 2, .ofs_x = 2, .ofs_y = 9},
    {.bitmap_index = 4209, .adv_w = 191, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4275, .adv_w = 98, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 4290, .adv_w = 128, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4314, .adv_w = 142, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 4334, .adv_w = 92, .box_w = 5, .box_h = 2, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4339, .adv_w = 191, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4405, .adv_w = 144, .box_w = 7, .box_h = 2, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 4412, .adv_w = 101, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 4427, .adv_w = 142, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4467, .adv_w = 103, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 4485, .adv_w = 103, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 4503, .adv_w = 144, .box_w = 5, .box_h = 2, .ofs_x = 3, .ofs_y = 9},
    {.bitmap_index = 4508, .adv_w = 165, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4558, .adv_w = 160, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4617, .adv_w = 68, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4623, .adv_w = 144, .box_w = 5, .box_h = 4, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 4633, .adv_w = 103, .box_w = 5, .box_h = 6, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 4648, .adv_w = 101, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 4666, .adv_w = 128, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4690, .adv_w = 249, .box_w = 15, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4773, .adv_w = 249, .box_w = 14, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4850, .adv_w = 249, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4938, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4988, .adv_w = 180, .box_w = 13, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5079, .adv_w = 180, .box_w = 13, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5170, .adv_w = 180, .box_w = 13, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5261, .adv_w = 180, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5346, .adv_w = 180, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5431, .adv_w = 180, .box_w = 13, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5529, .adv_w = 255, .box_w = 17, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5614, .adv_w = 174, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5686, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5749, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5812, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5875, .adv_w = 161, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5934, .adv_w = 77, .box_w = 5, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5969, .adv_w = 77, .box_w = 5, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6004, .adv_w = 77, .box_w = 7, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6053, .adv_w = 77, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6086, .adv_w = 201, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6146, .adv_w = 194, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6211, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6295, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6379, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6463, .adv_w = 202, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6541, .adv_w = 202, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6619, .adv_w = 142, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 6644, .adv_w = 202, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6722, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6792, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6862, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6932, .adv_w = 189, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6997, .adv_w = 159, .box_w = 12, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7081, .adv_w = 174, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7131, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7181, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7225, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7269, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7313, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7357, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7401, .adv_w = 146, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7449, .adv_w = 238, .box_w = 15, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7509, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7559, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7609, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7659, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7709, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7759, .adv_w = 69, .box_w = 5, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7787, .adv_w = 69, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7820, .adv_w = 69, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7853, .adv_w = 69, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7881, .adv_w = 156, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7936, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7986, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8041, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8096, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8151, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8206, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8261, .adv_w = 142, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 8297, .adv_w = 155, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8347, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8391, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8435, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8479, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8523, .adv_w = 139, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8593, .adv_w = 165, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8656, .adv_w = 139, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8726, .adv_w = 180, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8811, .adv_w = 146, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8851, .adv_w = 180, .box_w = 13, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8942, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8986, .adv_w = 180, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 9071, .adv_w = 146, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9121, .adv_w = 174, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9198, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9248, .adv_w = 174, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9325, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9375, .adv_w = 174, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9452, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9502, .adv_w = 174, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9579, .adv_w = 139, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9629, .adv_w = 198, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9701, .adv_w = 165, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9773, .adv_w = 201, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9833, .adv_w = 165, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9894, .adv_w = 161, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9953, .adv_w = 149, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9998, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10061, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10111, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10174, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10224, .adv_w = 161, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 10283, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10333, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10396, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10446, .adv_w = 185, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10523, .adv_w = 167, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10593, .adv_w = 185, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10670, .adv_w = 167, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10740, .adv_w = 185, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10817, .adv_w = 167, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10887, .adv_w = 185, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 10970, .adv_w = 167, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11045, .adv_w = 194, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11115, .adv_w = 164, .box_w = 11, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11192, .adv_w = 199, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11257, .adv_w = 164, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11312, .adv_w = 77, .box_w = 7, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11358, .adv_w = 69, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11391, .adv_w = 77, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11424, .adv_w = 69, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11449, .adv_w = 77, .box_w = 6, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11491, .adv_w = 69, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11524, .adv_w = 77, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11544, .adv_w = 69, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11572, .adv_w = 77, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11593, .adv_w = 69, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11605, .adv_w = 173, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11650, .adv_w = 140, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11706, .adv_w = 126, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11762, .adv_w = 71, .box_w = 7, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 11811, .adv_w = 175, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 11894, .adv_w = 153, .box_w = 9, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 11966, .adv_w = 150, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12002, .adv_w = 144, .box_w = 8, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12058, .adv_w = 69, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12100, .adv_w = 144, .box_w = 8, .box_h = 15, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 12160, .adv_w = 69, .box_w = 3, .box_h = 16, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 12184, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12228, .adv_w = 69, .box_w = 5, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12258, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12298, .adv_w = 91, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12331, .adv_w = 146, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12381, .adv_w = 74, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12414, .adv_w = 194, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12484, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12534, .adv_w = 194, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 12609, .adv_w = 164, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 12668, .adv_w = 194, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12738, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12788, .adv_w = 180, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12854, .adv_w = 194, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 12919, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 12969, .adv_w = 202, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13047, .adv_w = 155, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13097, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13181, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13236, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13320, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13375, .adv_w = 271, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13460, .adv_w = 256, .box_w = 16, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13524, .adv_w = 175, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13594, .adv_w = 101, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13627, .adv_w = 175, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 13702, .adv_w = 101, .box_w = 5, .box_h = 13, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 13735, .adv_w = 175, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13805, .adv_w = 101, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13844, .adv_w = 151, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13907, .adv_w = 124, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13951, .adv_w = 151, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14014, .adv_w = 124, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14058, .adv_w = 151, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14117, .adv_w = 124, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14161, .adv_w = 151, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14224, .adv_w = 124, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14268, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14327, .adv_w = 102, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14373, .adv_w = 144, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14436, .adv_w = 102, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14478, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14523, .adv_w = 104, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14558, .adv_w = 189, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14623, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14667, .adv_w = 189, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14732, .adv_w = 163, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14772, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14842, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14886, .adv_w = 189, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14961, .adv_w = 163, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15009, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15079, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15123, .adv_w = 189, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15188, .adv_w = 163, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15238, .adv_w = 275, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15357, .adv_w = 221, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15434, .adv_w = 159, .box_w = 12, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 15518, .adv_w = 139, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 15588, .adv_w = 159, .box_w = 12, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 15666, .adv_w = 159, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15736, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15780, .adv_w = 159, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15850, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15894, .adv_w = 159, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15964, .adv_w = 128, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16008, .adv_w = 77, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16047, .adv_w = 196, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16107, .adv_w = 111, .box_w = 10, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 16177, .adv_w = 202, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16255, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16310, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16388, .adv_w = 166, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16443, .adv_w = 151, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16488, .adv_w = 348, .box_w = 21, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16635, .adv_w = 326, .box_w = 19, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16740, .adv_w = 293, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16839, .adv_w = 270, .box_w = 15, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16914, .adv_w = 215, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 16998, .adv_w = 140, .box_w = 7, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 17047, .adv_w = 321, .box_w = 18, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17137, .adv_w = 265, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 17242, .adv_w = 235, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 17333, .adv_w = 180, .box_w = 13, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17424, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17468, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17538, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17582, .adv_w = 194, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17642, .adv_w = 169, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17703, .adv_w = 185, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17780, .adv_w = 167, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17850, .adv_w = 175, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17927, .adv_w = 153, .box_w = 11, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18004, .adv_w = 202, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18082, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18137, .adv_w = 151, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18200, .adv_w = 130, .box_w = 9, .box_h = 14, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 18263, .adv_w = 180, .box_w = 13, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18367, .adv_w = 146, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18419, .adv_w = 255, .box_w = 17, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18538, .adv_w = 238, .box_w = 15, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18621, .adv_w = 202, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 18711, .adv_w = 155, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 18771, .adv_w = 180, .box_w = 13, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18862, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18906, .adv_w = 180, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18991, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19035, .adv_w = 161, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 19098, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19148, .adv_w = 161, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 19207, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19257, .adv_w = 77, .box_w = 7, .box_h = 14, .ofs_x = -2, .ofs_y = 0},
    {.bitmap_index = 19306, .adv_w = 69, .box_w = 7, .box_h = 11, .ofs_x = -2, .ofs_y = 0},
    {.bitmap_index = 19345, .adv_w = 77, .box_w = 6, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 19384, .adv_w = 69, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 19417, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19501, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19556, .adv_w = 202, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19640, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19695, .adv_w = 175, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 19765, .adv_w = 101, .box_w = 7, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 19804, .adv_w = 175, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 19869, .adv_w = 101, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19908, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 19978, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 20022, .adv_w = 189, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 20092, .adv_w = 163, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 20136, .adv_w = 151, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 20204, .adv_w = 124, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 20256, .adv_w = 144, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 20324, .adv_w = 102, .box_w = 7, .box_h = 15, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 20377, .adv_w = 194, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 20447, .adv_w = 164, .box_w = 11, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 20524, .adv_w = 202, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20614, .adv_w = 155, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20679, .adv_w = 202, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20769, .adv_w = 155, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20834, .adv_w = 202, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20924, .adv_w = 155, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20989, .adv_w = 159, .box_w = 12, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 21067, .adv_w = 139, .box_w = 10, .box_h = 13, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 21132, .adv_w = 71, .box_w = 6, .box_h = 11, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 21165, .adv_w = 191, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21220, .adv_w = 143, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21252, .adv_w = 160, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21312, .adv_w = 149, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21344, .adv_w = 120, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21372, .adv_w = 164, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21422, .adv_w = 153, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21458, .adv_w = 211, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21514, .adv_w = 133, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21546, .adv_w = 168, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21582, .adv_w = 168, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21632, .adv_w = 148, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21668, .adv_w = 156, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21704, .adv_w = 193, .box_w = 10, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21744, .adv_w = 165, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21780, .adv_w = 155, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21820, .adv_w = 163, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21852, .adv_w = 166, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 21902, .adv_w = 139, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21938, .adv_w = 123, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21970, .adv_w = 142, .box_w = 10, .box_h = 11, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 22025, .adv_w = 135, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22061, .adv_w = 149, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22097, .adv_w = 229, .box_w = 14, .box_h = 10, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 22167, .adv_w = 200, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22211, .adv_w = 141, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22243, .adv_w = 213, .box_w = 12, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 22291, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22336, .adv_w = 132, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22376, .adv_w = 146, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22421, .adv_w = 77, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22441, .adv_w = 79, .box_w = 5, .box_h = 7, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 22459, .adv_w = 122, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22505, .adv_w = 141, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22545, .adv_w = 90, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22570, .adv_w = 141, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22615, .adv_w = 129, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 22659, .adv_w = 133, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 22715, .adv_w = 139, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22755, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22795, .adv_w = 137, .box_w = 7, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 22844, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22879, .adv_w = 178, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22934, .adv_w = 131, .box_w = 10, .box_h = 13, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 22999, .adv_w = 174, .box_w = 13, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 23064, .adv_w = 134, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 23125, .adv_w = 147, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23175, .adv_w = 127, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 23225, .adv_w = 140, .box_w = 11, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 23280, .adv_w = 96, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 23324, .adv_w = 166, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 23396, .adv_w = 162, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 23462, .adv_w = 116, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 23522, .adv_w = 62, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 23552, .adv_w = 62, .box_w = 11, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 23629, .adv_w = 62, .box_w = 9, .box_h = 14, .ofs_x = -4, .ofs_y = 0},
    {.bitmap_index = 23692, .adv_w = 0, .box_w = 6, .box_h = 6, .ofs_x = -6, .ofs_y = 9},
    {.bitmap_index = 23710, .adv_w = 0, .box_w = 6, .box_h = 5, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 23725, .adv_w = 91, .box_w = 3, .box_h = 11, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 23742, .adv_w = 240, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 23757, .adv_w = 240, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 23855, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 23968, .adv_w = 240, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24066, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24171, .adv_w = 240, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24269, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 24374, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24487, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 24607, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24712, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24817, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24922, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 25042, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 25147, .adv_w = 240, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 25252, .adv_w = 240, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 25357, .adv_w = 155, .box_w = 11, .box_h = 9, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25407, .adv_w = 82, .box_w = 6, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25437, .adv_w = 86, .box_w = 7, .box_h = 8, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25465, .adv_w = 68, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 25490, .adv_w = 65, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 25507, .adv_w = 66, .box_w = 4, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 25529, .adv_w = 196, .box_w = 12, .box_h = 9, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 25583, .adv_w = 66, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25608, .adv_w = 70, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25632, .adv_w = 196, .box_w = 12, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 25680, .adv_w = 66, .box_w = 6, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 25707, .adv_w = 86, .box_w = 7, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 25735, .adv_w = 158, .box_w = 12, .box_h = 9, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 25789, .adv_w = 169, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 25859, .adv_w = 103, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 25880, .adv_w = 115, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 25908, .adv_w = 91, .box_w = 7, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25943, .adv_w = 96, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25983, .adv_w = 96, .box_w = 8, .box_h = 13, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 26035, .adv_w = 165, .box_w = 11, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26068, .adv_w = 165, .box_w = 12, .box_h = 4, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26092, .adv_w = 165, .box_w = 11, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26142, .adv_w = 190, .box_w = 13, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26201, .adv_w = 123, .box_w = 9, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26228, .adv_w = 105, .box_w = 8, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26252, .adv_w = 145, .box_w = 9, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26320, .adv_w = 102, .box_w = 7, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26355, .adv_w = 97, .box_w = 8, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26391, .adv_w = 152, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26450, .adv_w = 102, .box_w = 7, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26485, .adv_w = 97, .box_w = 8, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26521, .adv_w = 52, .box_w = 4, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26543, .adv_w = 59, .box_w = 5, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26571, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 26610, .adv_w = 112, .box_w = 8, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26634, .adv_w = 99, .box_w = 8, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26658, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26698, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26743, .adv_w = 66, .box_w = 5, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26768, .adv_w = 70, .box_w = 6, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26792, .adv_w = 111, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 26817, .adv_w = 123, .box_w = 8, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 26845, .adv_w = 117, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 26880, .adv_w = 115, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 26920, .adv_w = 151, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 26972, .adv_w = 165, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 27017, .adv_w = 86, .box_w = 7, .box_h = 8, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 27045, .adv_w = 150, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 27100, .adv_w = 150, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_2[] = {
    0x0, 0x3, 0x11, 0x12, 0x20, 0x21, 0x28, 0x35,
    0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
    0x3e, 0x3f, 0x44, 0x45, 0x55, 0x56, 0x57, 0x58,
    0x59, 0x5a, 0x5b, 0x5c, 0x5f, 0x60
};

static const uint16_t unicode_list_4[] = {
    0x0, 0x1, 0xc, 0xd, 0xe, 0xf, 0x12, 0x13,
    0x14, 0x15, 0x19, 0x1fd
};

static const uint16_t unicode_list_6[] = {
    0x0, 0x2, 0x4, 0x6, 0x7, 0x9, 0x18b, 0x18c,
    0x18f, 0x190, 0x194, 0x197, 0x198, 0x19b, 0x19c, 0x19d,
    0x19e, 0x19f, 0x1a1, 0x1a2, 0x1a3, 0x1a4, 0x4ca, 0x4d0,
    0x4df, 0x4e0, 0x4e5, 0x4ea, 0x4eb, 0x4ed, 0x4f3, 0x4f4,
    0x4f9, 0x4fa, 0x4fb, 0x502, 0x508, 0x51f, 0x2bbd, 0x4bdc,
    0x4d00, 0x5047, 0x57b4, 0x57b7, 0x5abb, 0x5c80, 0x5e3b, 0x62ea,
    0x6a4b, 0x6a9c, 0x6ca8, 0x75c6, 0x7ad1, 0x7bc0, 0xf737, 0xf7b9,
    0xf7ba, 0xfa46, 0xfa48, 0xfa49, 0xfa4b, 0xfa4c, 0xfa4d, 0xfa51,
    0xfa52, 0xfa53, 0xfa5b, 0xfa61, 0xfa64, 0xfa65, 0xfa68, 0xfa69,
    0xfa6b, 0xfa6e, 0xfa6f, 0xfa72, 0xfa7a, 0xfa86, 0xfa87, 0xfa88,
    0xfa8e, 0xfa8f, 0xfa91, 0xfa92, 0xfa93, 0xfa9a, 0xfa9b, 0xfa9c,
    0xfa9e, 0xfa9f, 0xfaa0, 0xfaa1, 0xfaa2, 0xfaa3, 0xfaa5, 0xfaa6,
    0xfaa8, 0xfaa9, 0xfaac, 0xfaad, 0xfaaf, 0xfab3, 0xfab7
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 160, .range_length = 224, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 399, .range_length = 97, .glyph_id_start = 320,
        .unicode_list = unicode_list_2, .glyph_id_ofs_list = NULL, .list_length = 30, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    },
    {
        .range_start = 506, .range_length = 34, .glyph_id_start = 350,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 542, .range_length = 510, .glyph_id_start = 384,
        .unicode_list = unicode_list_4, .glyph_id_ofs_list = NULL, .list_length = 12, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    },
    {
        .range_start = 1072, .range_length = 20, .glyph_id_start = 396,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1093, .range_length = 64184, .glyph_id_start = 416,
        .unicode_list = unicode_list_6, .glyph_id_ofs_list = NULL, .list_length = 103, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    0, 61, 47, 62, 63, 64, 0, 65,
    0, 22, 8, 66, 0, 9, 22, 0,
    67, 0, 0, 0, 0, 51, 29, 9,
    0, 0, 8, 9, 0, 0, 0, 68,
    23, 23, 23, 23, 23, 23, 26, 25,
    26, 26, 26, 26, 29, 29, 29, 29,
    22, 29, 22, 22, 22, 22, 22, 0,
    22, 30, 30, 30, 30, 39, 69, 46,
    45, 45, 45, 45, 45, 45, 49, 47,
    49, 49, 49, 49, 51, 51, 70, 51,
    71, 45, 46, 46, 46, 46, 46, 9,
    46, 51, 51, 51, 51, 58, 46, 58,
    23, 45, 23, 45, 23, 72, 25, 47,
    25, 47, 25, 47, 25, 47, 22, 73,
    22, 48, 26, 49, 26, 49, 26, 49,
    26, 74, 26, 49, 28, 51, 28, 51,
    28, 51, 28, 51, 29, 45, 29, 45,
    29, 51, 29, 51, 29, 51, 29, 75,
    29, 51, 30, 51, 30, 51, 31, 53,
    53, 32, 48, 32, 48, 32, 73, 32,
    48, 32, 48, 29, 45, 29, 45, 29,
    45, 45, 29, 45, 22, 46, 22, 46,
    22, 46, 26, 49, 35, 55, 35, 55,
    35, 55, 3, 56, 3, 56, 3, 56,
    3, 56, 36, 57, 36, 76, 36, 57,
    30, 51, 30, 51, 30, 51, 30, 51,
    30, 51, 30, 77, 37, 58, 39, 58,
    39, 40, 60, 40, 60, 40, 60, 0,
    22, 78, 79, 80, 81, 82, 0, 40,
    60, 60, 30, 51, 51, 30, 51, 45,
    23, 45, 30, 51, 28, 0, 28, 51,
    31, 53, 22, 46, 24, 0, 23, 45,
    26, 49, 22, 46, 23, 45, 23, 45,
    26, 49, 26, 49, 29, 83, 29, 84,
    22, 46, 22, 46, 35, 55, 35, 55,
    30, 51, 30, 51, 3, 56, 36, 85,
    29, 45, 22, 46, 22, 46, 22, 46,
    39, 58, 51, 29, 51, 46, 0, 86,
    51, 49, 59, 0, 51, 51, 59, 51,
    51, 51, 46, 51, 46, 47, 86, 58,
    59, 51, 51, 51, 87, 46, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 40,
    39, 38, 41, 42, 38, 38, 43, 43,
    39, 43, 39, 43, 44, 45, 46, 47,
    47, 48, 49, 50, 0, 0, 35, 9,
    0, 51, 39, 52, 53, 54, 0, 55,
    0, 23, 8, 9, 9, 9, 23, 0,
    56, 0, 0, 0, 0, 43, 57, 9,
    0, 0, 8, 58, 0, 0, 0, 59,
    24, 24, 24, 24, 24, 24, 24, 23,
    25, 25, 25, 25, 25, 25, 25, 25,
    25, 25, 23, 23, 23, 23, 23, 0,
    23, 28, 28, 28, 28, 31, 25, 38,
    37, 37, 37, 37, 37, 37, 37, 39,
    39, 39, 39, 39, 60, 43, 61, 62,
    63, 43, 39, 39, 39, 39, 39, 9,
    39, 46, 46, 46, 46, 47, 38, 47,
    24, 37, 24, 37, 24, 37, 23, 39,
    23, 39, 23, 39, 23, 39, 25, 39,
    25, 39, 25, 39, 25, 39, 25, 39,
    25, 39, 25, 39, 23, 39, 23, 39,
    23, 39, 23, 39, 25, 38, 25, 64,
    25, 65, 25, 66, 25, 67, 25, 68,
    25, 43, 25, 68, 26, 69, 25, 38,
    43, 25, 38, 25, 38, 25, 38, 25,
    38, 25, 38, 25, 43, 25, 43, 25,
    43, 43, 25, 43, 23, 39, 23, 39,
    23, 39, 23, 39, 25, 43, 25, 43,
    25, 43, 3, 44, 3, 44, 3, 44,
    3, 44, 27, 45, 27, 45, 27, 45,
    28, 46, 28, 46, 28, 46, 28, 46,
    28, 46, 28, 46, 29, 47, 31, 47,
    31, 32, 50, 32, 50, 32, 50, 0,
    23, 70, 23, 39, 28, 46, 71, 25,
    25, 39, 25, 25, 38, 25, 25, 43,
    24, 37, 28, 46, 23, 39, 23, 39,
    25, 38, 23, 39, 71, 72, 24, 37,
    24, 37, 23, 39, 24, 37, 24, 37,
    25, 39, 25, 39, 25, 73, 25, 74,
    23, 39, 23, 39, 25, 75, 25, 76,
    28, 46, 28, 46, 3, 44, 27, 45,
    25, 38, 23, 39, 23, 39, 23, 39,
    31, 47, 77, 78, 37, 23, 43, 43,
    79, 39, 48, 72, 43, 43, 43, 79,
    43, 43, 39, 43, 43, 39, 80, 47,
    48, 46, 43, 43, 43, 43, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 0, 0, 0, 0, 0, 0, 2,
    0, 0, 0, 0, 2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 6, 5, 0, 2, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 11, 0, 6, -5, 0, 0, 5,
    0, -13, -14, 1, 11, 5, 4, -10,
    1, 11, 0, 10, 2, 7, 0, -10,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 14, 2, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 5, 0, 5,
    10, 0, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, -12, 0, 0,
    0, -7, 0, 0, 0, 0, 0, -5,
    4, 5, 0, 0, -2, 0, -1, 2,
    0, -2, 0, -2, -1, -5, 0, 0,
    0, 0, -2, 0, 0, -3, -4, 0,
    0, -2, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    -2, 0, 0, 0, 0, 0, 0, -4,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -4, 0, -3,
    0, -6, 0, -30, 0, 0, -5, -12,
    5, 7, 0, 0, -5, 2, 2, 8,
    5, -4, 5, 0, 0, -13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, 0, 2, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, -11, 0, -10, -1, 0, 0, -7,
    0, 0, 8, 0, -7, -2, 0, 0,
    0, -4, 0, 0, -2, -17, 0, 2,
    0, 7, -6, 0, -5, 0, -10, 2,
    0, -19, -2, 7, 2, 0, 0, 0,
    0, 0, 0, 0, 2, 0, -4, 0,
    -4, 0, -2, 0, 0, 0, 0, -11,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 8, 0, 2, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 10, 2, 0, 0, 0, 0, 0,
    0, 23, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 23, 21, -3, -5,
    0, 0, 0, 0, 23, -3, -4, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -8, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 5, 2, 7, -2, 0, 0, 5,
    -2, -8, -32, 1, 6, 5, 0, -3,
    0, 7, 0, 7, 0, 7, 0, -23,
    0, -3, 7, 0, 8, -2, 5, 2,
    0, 0, 0, -2, 0, 0, -4, 0,
    0, 0, 19, 0, 7, 0, 10, 4,
    10, 4, 0, 0, 1, 0, 0, 7,
    8, 4, -19, 19, 19, 19, -4, 0,
    7, 19, 19, 19, 0, -1, 0, 0,
    19, 7, 19, 19, 0, 0, 0, 0,
    0, 0, -4, -8, 0, 0, 0, -2,
    0, -2, 0, 1, -5, -4, -5, 1,
    0, -2, 0, 0, 0, -10, 1, -5,
    0, -5, -8, 0, -6, -5, -8, 0,
    0, -16, 0, 0, 0, 0, 2, 0,
    0, 0, 0, 0, 2, 0, -4, -6,
    -4, -3, 0, 0, 1, -6, 2, 0,
    -20, -2, 0, 0, 0, 0, 2, 0,
    0, 0, 0, 0, 0, 0, -2, -3,
    0, 0, 0, 0, 0, -5, -2, -4,
    0, -13, 2, -16, 0, 0, 0, -8,
    -2, 0, 23, -3, -4, 2, 2, -1,
    0, -4, 2, 0, 0, -13, -5, 8,
    0, 13, -8, -2, -9, 0, -9, 4,
    0, -23, 0, 2, 2, 0, -2, 0,
    0, 2, 0, 0, -1, -2, -8, 0,
    -8, 0, 0, 0, -2, -7, 0, -16,
    -33, 0, 2, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 2, 5, 5, -11,
    0, 14, -5, 0, -8, 0, 8, 0,
    -17, -23, -17, -5, 7, 0, 0, -16,
    0, 2, -6, 0, -4, 0, -5, -10,
    0, -2, 7, 0, 7, 0, 7, 0,
    0, 5, 7, -29, -16, 0, -16, 0,
    2, 0, -16, -16, -6, -16, -7, -13,
    -7, -16, -16, -2, 0, 10, -5, 11,
    0, -11, -18, -16, -16, -16, -11, 0,
    -16, -16, -16, -16, 0, 0, 0, 0,
    -16, -16, -16, -16, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 1,
    1, -3, -5, 0, 0, 0, -2, 0,
    0, -1, 0, 0, 0, -5, 0, -2,
    0, -6, -5, 0, -6, -8, -8, -4,
    0, -5, 0, -5, 0, 0, 0, 0,
    0, -2, 0, 0, 2, 0, 1, -2,
    1, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 2, -1, 0, 0, 0,
    -1, 2, 2, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, -1, 0, -3, 0, -4, 0,
    0, -1, 0, 7, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, -1, 0,
    -2, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, -2, -4,
    -2, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, -7, -1, -7, 5, 0, 0, -5,
    2, 5, 6, 0, -6, 0, -2, 0,
    0, -11, 2, -1, 2, -13, 2, 0,
    0, 0, -12, 0, -13, -2, -21, -1,
    0, -12, 0, 5, 7, 0, 4, 0,
    0, 0, 0, 0, 0, 0, -5, -4,
    -5, 0, 0, 2, 2, -2, 6, -7,
    -5, 0, 2, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -1, -1, 0,
    -1, -3, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, -2, -3, -2, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    -2, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, -5, 2, 0, 0, -2,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, -2, 0, 0, 0, 0, -3, -4,
    -3, 0, 0, 0, 2, 0, 5, -4,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 7, -1, 0, -7, 0, 0, 6,
    -12, -12, -10, -5, 2, 0, -2, -16,
    -4, 0, -4, 0, -5, 4, -4, -15,
    0, -6, 0, 0, 1, 0, 2, -1,
    0, 2, 1, -7, -9, 0, -12, 0,
    0, -5, -5, -7, -2, -6, 0, -4,
    0, -6, -6, -3, -5, 4, -2, 2,
    6, -4, -14, -5, -5, -5, -12, 0,
    -5, -5, -5, -5, -5, -5, 0, 0,
    -5, -5, -5, -5, -5, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 1,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, -1,
    0, 0, -2, 0, -4, -5, -5, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -2, 2, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, -2, 0, 2,
    0, 8, -2, 0, -6, -2, -9, 0,
    0, -5, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 1, -2,
    1, 0, 0, 0, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, -4, 0, 0, 2,
    0, -12, -7, 0, 0, 0, -4, -12,
    0, 0, -2, 2, 0, -5, 0, -11,
    0, -7, 0, 0, -4, -5, -4, -2,
    -3, 0, 0, -4, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    2, 0, -1, 0, 0, -4, 0, 2,
    0, 2, -25, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 2, 0,
    1, -5, -5, 0, -2, -2, -3, 0,
    0, 0, 0, 0, 0, -7, 0, -2,
    0, -4, -2, 0, -5, -6, -7, -2,
    0, -5, 1, -7, 0, 0, 0, 0,
    0, 19, 0, 0, 1, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 19, 0, -5, -4,
    0, 0, 0, 0, 19, -5, -4, -2,
    0, -10, 0, 0, 0, 0, 0, -23,
    -5, 8, 7, -2, -10, 0, 2, -4,
    0, -12, -1, -3, 2, -17, -2, 4,
    0, 4, -8, -4, -9, -8, -10, 0,
    0, -14, 0, 13, 0, 0, -1, 0,
    0, 0, 0, -1, -1, -2, -6, -8,
    -6, 0, 2, 0, 0, 0, 0, -23,
    -19, 2, 5, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -1, -2, -4, 0,
    0, -5, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 5,
    0, 3, 0, -5, 2, -1, 0, -5,
    -2, 0, -3, -2, -2, 0, -4, -4,
    0, 0, -2, 0, -1, -4, -2, 0,
    0, -2, 0, 2, -1, 0, -5, 0,
    0, 0, 0, -5, 0, -4, 0, -4,
    0, -4, -2, 0, 0, 0, -1, 2,
    1, 0, -2, 0, 0, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -4, -5, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 2, 0, -3, 0, -1, -2, -6,
    -1, -1, -1, 0, -1, -2, 0, 0,
    0, 0, 0, 0, -2, -2, -2, 0,
    0, 0, 0, 2, -1, 0, -1, 0,
    0, 0, 0, -1, -2, -1, -2, -2,
    -2, -2, -1, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 10, 0, 0, -6, 0, -1, 5,
    0, -2, -10, -3, 4, 0, 0, -11,
    -4, 2, -4, 2, 0, 0, -2, -7,
    0, -4, 1, 0, 0, -4, 0, 0,
    0, 2, 2, -5, -4, 0, -4, 0,
    0, -4, -2, -2, 0, -4, 1, -4,
    1, -4, -2, 0, 0, 0, -1, 7,
    2, -3, -11, -2, 5, 2, -4, 0,
    2, 2, 2, -2, 4, 0, -2, -6,
    12, 2, -2, -2, -4, -9, -8, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, -3, 0, 0, -2, -2, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, -4,
    0, 0, 0, 0, -1, -5, -4, -4,
    0, 0, -4, 0, -5, 0, 0, 0,
    -8, 0, 1, -5, 5, 0, -1, -11,
    0, 0, -5, -2, 0, -10, -6, -7,
    0, 0, -11, -2, -10, -10, -12, 0,
    -5, 0, 2, 16, -3, 0, -6, 0,
    0, 0, -2, -4, -6, -4, -9, -10,
    -9, -6, 0, 0, 0, 0, 0, -2,
    -2, 0, 0, -2, -2, -2, -6, 0,
    -2, -2, -2, -2, 0, 0, -6, -6,
    -2, -2, -2, -2, 0, 0, 0, -11,
    0, 0, -1, 0, 0, 0, 0, -17,
    -1, 7, 5, -5, -8, 0, 0, -6,
    0, -12, -1, -2, 5, -22, -3, 0,
    0, 0, -16, -2, -12, -2, -18, 0,
    0, -17, 0, 13, 0, 0, -1, 0,
    0, 0, 0, 0, -1, -1, -9, -1,
    -9, 0, 0, 0, 0, 0, 0, -16,
    -19, 1, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, -2, 0,
    0, -7, -11, 0, 0, -1, -4, -7,
    -2, 0, -2, 0, 0, 0, 0, -11,
    -2, -8, -7, -2, -4, -6, -2, -4,
    0, -5, -2, -8, -4, 0, -3, 0,
    0, -2, -4, 0, 1, 0, -1, -8,
    -1, 0, 0, 0, 0, 0, 0, 5,
    0, 0, -12, -4, -4, -4, -3, 0,
    -4, -4, -4, -4, -2, 0, -4, -5,
    -4, -4, -4, -4, -2, -10, -10, -2,
    0, -4, 0, 0, 0, 0, 2, 0,
    1, -5, 11, 0, -2, -2, -3, 0,
    0, 0, 0, 0, 0, -7, 0, -2,
    0, -4, -2, 0, -5, -6, -7, -2,
    0, -5, 2, 10, 0, 0, 0, 0,
    0, 19, 0, 0, 1, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 19, 0, -5, -4,
    0, 0, 0, 0, 19, -5, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, -5,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, -2, -2, 0, 0, -5, -2, 0,
    0, -5, 0, 4, -1, 0, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    5, 2, -2, 0, -7, -3, 0, 7,
    -8, -8, -5, -5, 10, 5, 2, -20,
    -2, 5, -2, 0, -2, 4, -2, -8,
    0, -2, 2, -3, -2, -7, -2, 0,
    0, 7, 5, 0, -7, 0, -13, 0,
    0, 9, -3, -9, 0, -3, -8, -8,
    -8, -2, -5, 0, 0, 0, 0, 10,
    7, -4, -13, 2, 10, 5, -13, 0,
    7, 7, 6, -3, 9, 0, 0, -9,
    18, 10, 2, -3, 9, -9, -16, -4,
    2, 0, -4, 0, -6, 0, 2, 8,
    -6, -9, -10, -6, 7, 0, 0, -18,
    -2, 2, -4, -2, -6, 0, -5, -9,
    -4, -4, -2, 0, 0, -6, -6, -2,
    0, 7, 6, -2, -13, 0, -13, 0,
    -2, -5, -8, -14, 0, -8, -4, -8,
    -4, -7, -7, 0, 0, 0, 0, 6,
    5, -4, -17, 5, 10, 6, -13, 0,
    7, 6, 11, -4, 8, 0, -2, -11,
    17, 5, 4, -6, -5, -6, -10, -5,
    0, 0, -3, 0, -5, -2, 0, -2,
    -5, 0, 4, -8, 2, 0, 0, -13,
    0, -2, -5, -4, -2, -7, -6, -8,
    -6, 0, -7, -2, -6, -4, -7, -2,
    0, 0, 0, 11, -4, 0, -7, 0,
    0, 0, -2, -5, -6, -6, -7, -10,
    -7, -4, -2, 0, 0, 0, 0, -5,
    0, -2, 0, -2, -2, -2, -7, 0,
    -2, -2, -2, -2, 0, 0, -12, -9,
    -2, -2, -2, -2, 0, -2, -2, -13,
    5, 0, -4, 0, -12, -2, 2, 5,
    -8, -9, -5, -8, 8, -2, 1, -23,
    -5, 5, -5, -4, -9, 0, -7, -10,
    -2, -2, -2, -2, -6, -7, 0, 0,
    0, 7, 7, -1, -16, 0, -14, 0,
    -4, 7, -9, -17, -5, -8, -10, -12,
    -10, -8, -10, 0, 0, 0, -5, 5,
    5, -5, -19, 8, 8, 7, -14, 0,
    7, 7, 10, -4, 7, 0, 0, -13,
    -9, -9, -9, -9, 7, -8, -14, -6,
    0, 0, 0, 0, -2, 0, 0, 2,
    -2, 5, 1, -4, 5, 0, 0, -6,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 2, 7, 0, 0, -2, 0,
    0, 0, 0, 0, -1, -1, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 2, 0,
    -1, 0, 10, 0, 5, 0, 0, -4,
    0, 5, 0, 0, 0, 2, 0, 0,
    0, 0, 5, 0, 6, 0, 7, 0,
    0, 7, 0, 7, -2, 0, 0, 0,
    0, 24, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 24, 22, -2, -5,
    0, 0, 0, 0, 24, -4, -5, -1,
    0, -14, 0, -2, 4, 0, 7, -32,
    0, 23, 2, -5, -5, 2, 2, -1,
    0, -12, 0, 0, 12, -14, -5, 7,
    0, 7, -5, -2, -10, 5, -5, 0,
    0, -17, 10, 34, 0, 0, 0, 0,
    0, 29, 0, 0, 0, 0, 5, 0,
    5, 0, 10, 0, 0, 0, 7, -14,
    -7, 2, 7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 29, 31, 0, 0,
    0, 0, 0, 0, 29, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -4, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    0, 0, 0, 0, 0, -2, -3, 0,
    0, -1, 5, -6, 0, 0, 0, -2,
    0, 2, 30, -5, -2, 7, 6, -6,
    2, 0, 0, 2, 2, -4, -7, 13,
    7, 19, 0, -2, -2, 11, -1, 5,
    0, -31, 7, 0, -2, 0, -6, 0,
    0, 26, 0, 2, -5, -6, -4, 8,
    5, 5, 4, 4, -1, -2, 6, 0,
    -15, 2, 0, 0, 0, 0, -6, 0,
    0, 0, 0, 0, 26, 24, 0, 0,
    0, 0, 0, 0, 26, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, -6,
    0, 0, 0, 0, -5, -1, 0, 0,
    0, -5, 0, -2, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, -4,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, -4, 0, -6, 0, 0, 0, -4,
    2, -2, 0, 0, -6, -2, -6, 0,
    0, -6, 0, -2, 0, -11, 0, -4,
    0, 0, -18, -4, -10, -4, -9, 0,
    0, -16, 0, -6, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -4,
    -4, -2, 0, 0, 0, 0, 0, -4,
    -6, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -6, -2,
    0, 0, 0, 0, 0, -1, -1, -5,
    0, 0, 0, 0, -5, 0, -5, 4,
    -4, 5, 0, -1, -6, -1, -4, -4,
    0, -2, -1, -1, 2, -6, 0, 0,
    0, 0, -19, -2, -5, 0, -7, 0,
    -1, -11, -2, 0, 0, -1, -2, 0,
    -1, 0, 0, 0, 2, 0, -2, -4,
    -2, -1, 0, 0, 0, 0, -2, 4,
    0, 0, -1, 0, 0, 0, -2, -1,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, -3, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -1, 0, 0, 0, -5,
    2, 0, 0, 0, -6, -2, -5, 0,
    0, -7, 0, -2, 0, -11, 0, 0,
    0, 0, -23, 0, -5, -9, -12, 0,
    0, -16, 0, -2, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -4,
    -2, -1, 0, 0, 0, 0, 0, -4,
    -5, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -3, -2, -5,
    0, 0, 0, 4, -4, 0, 8, 11,
    -2, -2, -7, 2, 11, 4, 5, -6,
    2, 10, 2, 6, 5, 6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 15, 11, -5, -2, 1, -2, 1,
    1, 11, 0, 0, 0, 0, 2, 0,
    2, 0, 0, 0, 0, 0, 0, 8,
    10, 0, -4, 16, 12, 14, -2, 7,
    14, 12, 19, 0, 11, 0, 0, 0,
    27, 10, 11, 0, 11, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -6, 2, 0, -2, 2, 5, 2, -7,
    0, 0, -2, 2, 0, 2, 0, 0,
    0, 0, -4, 0, -2, -1, -5, 0,
    -2, -10, 0, 14, -2, 0, -5, 0,
    0, 0, -1, -4, 0, -2, -7, -5,
    -7, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -1, -1, -5, 0,
    -1, -1, -1, -1, 0, 0, 0, -6,
    -1, -1, -1, -1, 0, 0, 2, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 12, 0, -4, -2,
    0, 0, 0, 0, 12, 0, 0, 0,
    0, 0, -3, 0, -7, -2, -2, 7,
    -2, -2, -10, 0, 1, 0, -1, -6,
    0, 5, 0, 2, 0, 2, -5, -10,
    -2, 0, -7, -4, -6, -10, -9, 0,
    -3, -5, -2, -4, -10, -1, -2, 0,
    -1, 0, -1, 0, 4, 0, 4, -1,
    4, 0, 0, 0, 0, 0, 2, 8,
    7, 0, -8, -1, -1, -1, -2, -1,
    -1, -1, -1, -1, 0, 0, -4, -4,
    -1, -1, -1, -1, 0, -9, -9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -2, -2, 0,
    0, -6, 0, -1, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    -2, 0, 0, 0, 0, 0, 0, -3,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, -4,
    -2, 2, 0, -4, -4, -1, 0, -6,
    -1, -5, -2, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 7, 0, 0, -4, 0,
    0, 0, 0, 0, -3, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, -2, -1,
    -5, 0, 0, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -6, 0, 0, 10,
    -4, -8, -8, 1, 4, 4, 0, -7,
    1, 4, 1, 7, 1, 8, -1, -6,
    0, 0, -7, 0, 0, -7, -6, 0,
    0, -5, 0, -4, -4, 0, -4, 0,
    0, -4, 0, -2, 4, 0, -2, -7,
    -2, -2, 0, 0, 0, 0, 0, 8,
    7, 0, -8, 0, 0, 0, -4, 0,
    0, 0, 0, 0, -4, 0, 0, -3,
    0, 0, 0, 0, -4, -6, -8, 0,
    0, 0, -2, 0, -5, 0, 0, 4,
    -6, 0, 2, -2, 2, 1, 0, -8,
    0, -1, 0, 0, -2, 4, -2, 0,
    0, 0, -8, -2, -5, 0, -7, 0,
    0, -11, 0, 8, -2, 0, -4, 0,
    0, 4, 0, -2, 0, -2, -7, 0,
    -7, -2, 0, 0, 0, 0, 0, 2,
    0, 0, 0, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 4, 0, -2, -6,
    0, 0, 0, 0, 4, 0, 0, -2,
    0, 0, 0, 0, -1, 0, 0, 2,
    -3, 0, 0, 0, -2, -1, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 5, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 10, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, 2, -5, 0, -7, -2, -10, 0,
    0, -13, 0, 4, 0, 0, 0, 0,
    0, 17, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 17, 24, 0, 0,
    0, 0, 0, 0, 17, 0, 0, 0,
    0, 0, -2, 0, -8, 0, 0, 5,
    -7, 0, 3, -8, 4, -2, -2, -7,
    -4, 1, -8, -5, -7, 0, -3, -11,
    0, -5, 0, 0, 0, -2, 2, 0,
    0, 4, 0, 4, -10, 0, -7, 0,
    0, -6, -5, -10, -5, -6, -5, -6,
    -5, -10, -6, 0, 0, 0, -2, 0,
    2, -4, -5, -5, 7, 2, -7, 0,
    4, 2, 6, -5, 7, 0, 0, 0,
    -5, -5, -5, -5, -6, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 1,
    1, -2, 0, 0, 0, -2, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    4, 5, 0, 0, 0, 0, 5, 0,
    -6, -7, -7, -3, 7, 0, 2, -2,
    0, 6, -2, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 5, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, -10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    5, 0, 5, 0, 0, 0, 0, 4,
    0, 1, 2, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 1, 1, 1, 1, 0,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 0, 0, 0,
    1, 1, 1, 1, 1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    -2, 0, 2, 0, 0, 0, 0, 1,
    1, 0, 0, 0, 0, -2, 0, 2,
    0, 7, -4, 0, -4, -2, -5, 0,
    0, -13, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 5, 5, 6, 0, 0, 0, 7,
    0, -16, -13, 0, 11, 7, 4, -10,
    1, 10, 0, 8, 0, 5, 2, -23,
    0, -2, 10, 0, 6, -5, 5, 0,
    0, 13, 0, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 5, 0, 8, 2,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -14, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, -14, -4, 0, 0, -20,
    -10, 0, 10, -12, -11, 0, 0, -11,
    0, -11, -11, 0, 0, -32, -13, 5,
    0, 0, -14, -8, -17, 2, -19, 0,
    0, -22, 2, 12, -4, 0, -8, 0,
    0, 17, -2, -1, -2, -12, -12, 2,
    -6, 0, 0, 2, -4, -12, 0, -14,
    -22, -2, -5, -2, -2, -2, -8, 0,
    -2, -2, -2, -2, 17, 22, 0, 0,
    -2, -2, -2, -2, 17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, -7, 0, -5, -6, -5, 0,
    -2, 0, 0, 0, 0, -7, 0, -7,
    0, -10, -6, 0, -2, -7, -7, -4,
    0, -10, 0, -7, -2, 0, 0, 0,
    0, -2, 0, 0, 1, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 19,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 10, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, -4, 0, 1, 0, 0, -1, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 5,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, -6,
    0, 0, 0, 0, -5, -1, 0, 0,
    0, -5, 0, -2, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 5, 0, 0, 0, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, -4,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 22, 0, 0,
    23, 22, 2, 0, 15, 2, 2, 2,
    2, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 2, 2, 0, 22,
    2, 2, 2, 2, 0, 0, 0, 0,
    2, 2, 2, 2, 0, 0, 0, 0,
    0, -5, 0, -1, 0, 0, 0, -5,
    2, 0, 0, 0, -6, -2, -5, 0,
    0, -7, 0, -2, 0, -11, 0, 0,
    0, 0, -23, 0, -5, -9, -12, 0,
    0, -16, 0, -2, -4, 0, 0, 0,
    0, 7, 0, 0, 0, 0, -2, -4,
    -2, -1, 0, 0, 0, 0, 0, -4,
    -5, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 7, 0, 0, -2,
    0, 0, 0, 0, 7, -3, -2, -5,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 17, 0, -4, -2,
    0, 0, 0, 0, 17, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, -4,
    -2, 2, 0, -4, -4, -1, 0, -6,
    -1, -5, -2, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 7, 0, 0, -4, 0,
    0, 4, 5, 0, -3, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, -2, -1,
    -5, 0, 0, 5, 5, 5, -4, 0,
    5, 5, 5, 5, 4, 0, 0, 0,
    5, 5, 5, 5, 4, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 11, 0, -4, -2,
    0, 0, 0, 0, 11, 0, 0, 0,
    2, 5, 0, 0, 0, 0, 6, 8,
    -7, -14, -2, 0, 12, 2, 5, -8,
    0, 8, 0, 0, -5, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 17, 8, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 2, 0,
    1, -5, -5, 0, -2, -2, -3, 0,
    0, 0, 0, 0, 0, -7, 0, -2,
    0, -4, -2, 0, -5, -6, -7, -2,
    0, 4, 1, -7, 0, 0, 0, 0,
    0, 19, 0, 0, 1, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 19, 0, -5, -4,
    0, 0, 0, 0, 19, -5, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 10,
    2, -2, 0, 0, 2, 1, -3, 0,
    0, 5, 0, 0, 0, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 4, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    -2, -2, 0, 0, 0, 0, 0, 7,
    7, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    6, 0, 0, 0, -2, 0, 0, 7,
    -2, -3, -4, -2, 10, 0, 5, -2,
    0, 7, -2, 0, -2, 5, 0, -4,
    0, 0, 0, 0, 0, -1, -1, 0,
    0, 23, 10, -4, -2, 3, -2, 0,
    3, 4, -2, -2, 0, -2, -2, -2,
    -2, -2, -2, 0, 0, 0, 0, 4,
    4, 0, -4, -2, -2, -2, -2, 3,
    -2, -2, -2, -2, 4, 0, 0, 0,
    -2, -2, -2, -2, 4, 0, 0, 0,
    7, 0, 0, 0, 0, 0, 5, 16,
    -5, -5, -5, -1, 10, 9, 5, -5,
    -1, 12, 1, 7, 0, 19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 7, -5, -3, 1, -5, 0,
    1, -1, -2, -2, 5, -2, -2, -2,
    -2, -2, -1, 0, 0, 0, 4, 12,
    11, -1, -5, -2, -2, -2, -5, 1,
    -2, -2, -2, -2, -1, 0, 0, 0,
    -2, -2, -2, -2, -1, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 10,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 7,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -18, -2, -2, -9, -11, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, -4,
    -2, 2, 0, -4, -4, -1, 0, -6,
    -1, -5, -2, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 7, 0, 0, -4, 0,
    0, 5, 0, 0, -3, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, -2, -1,
    -5, 0, 0, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    -4, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    0, 0, 0, 0, -2, -9, -5, 0,
    0, 0, 0, 0, 0, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -10, -9, 2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -11, 0, 0,
    0, 0, -26, 0, -9, -7, -8, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -8, -9,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -2, -3, -12
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 87,
    .right_class_cnt     = 80,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 7,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t font_multilang_small = {
#else
lv_font_t font_multilang_small = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 23,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if FONT_MULTILANG_SMALL*/
