/*******************************************************************************
 * Size: 14 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 14 --font Inter-SemiBold.ttf -r 0x20-0x7F --font ../../../scripts/built_in_font/DejaVuSans.ttf --range 0xFE99,0xFE97,0xFE8D,0xFEBF,0xFE83,0xFE95,0xFE8E,0xFEF3,0xFE8B,0xFEBC,0xFE87,0xFEB3,0xFE98,0xFECB,0xFEDF,0xFED3,0xFEEF,0xFED8,0xFEB1,0xFEAD,0x627,0xFEA9,0xFEF9,0xFED0,0x0644,0xFECF,0xFE94,0x0639,0xFECC,0x0646,0x0648,0x0645,0xFEE3,0xFEE4,0xFEE2,0x0631,0x0633,0x0628,0xFE91,0x0637,0x064A,0x062D,0x0641,0x0636,0x0642,0x0649,0x0638,0x0625,0x0623,0x0621,0xFEE0,0xFEDE,0xFECA,0xFEE8,0xFEE6,0xFEAE,0xFEB4,0xFEB2,0xFE92,0xFE90,0xFEC4,0xFEC2,0xFEF4,0xFEF2,0xFEAA,0xFEDB,0xFEDC,0xFEA0,0xFEA3,0xFEA4,0xFEA2,0xFEF7,0xFEC0,0xFEF0,0xFEE1,0xFEEE,0xFEC9 --font ../../../scripts/built_in_font/SimSun.woff --symbols 语語言標題月日电池今日距离天的速度时间设置蓝牙灯亮度音量最大限度光照强统计三平均高时速简体中文。 --format lvgl -o font_ebike_inter_14.c --force-fast-kern-format
 ******************************************************************************/

#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "../../../lvgl.h"
#endif

#ifndef FONT_EBIKE_INTER_14
    #define FONT_EBIKE_INTER_14 1
#endif

#if FONT_EBIKE_INTER_14

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xcf, 0x1c, 0xf1, 0xbf, 0x1b, 0xf0, 0xbf, 0xa,
    0xf0, 0xaf, 0x4, 0x60, 0x12, 0xd, 0xf2, 0xae,
    0x10,

    /* U+0022 "\"" */
    0x3f, 0x2a, 0xb3, 0xf2, 0xab, 0x3f, 0x2a, 0xb2,
    0xe2, 0x9a,

    /* U+0023 "#" */
    0x0, 0x3f, 0x20, 0xac, 0x0, 0x5, 0xf0, 0xc,
    0x90, 0x0, 0x8d, 0x0, 0xf7, 0x1, 0xff, 0xff,
    0xff, 0xfb, 0x17, 0xec, 0x79, 0xf8, 0x40, 0xf,
    0x60, 0x6f, 0x0, 0x13, 0xf5, 0x29, 0xd2, 0xb,
    0xff, 0xff, 0xff, 0xf2, 0x4a, 0xf5, 0x5e, 0xa5,
    0x0, 0x9c, 0x0, 0xf6, 0x0, 0xb, 0xa0, 0x2f,
    0x30, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x0, 0x7d, 0xfe, 0x91, 0x0, 0x9f, 0xdf,
    0xcf, 0xc0, 0xf, 0xe0, 0xd0, 0xaf, 0x31, 0xfe,
    0xd, 0x0, 0x10, 0xb, 0xfe, 0xf3, 0x0, 0x0,
    0x19, 0xff, 0xfe, 0x40, 0x0, 0x0, 0xd9, 0xff,
    0x21, 0x42, 0xd, 0x7, 0xf6, 0x3f, 0xb0, 0xd0,
    0x9f, 0x50, 0xcf, 0xdf, 0xcf, 0xe0, 0x1, 0x9e,
    0xfe, 0xa2, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x0,

    /* U+0025 "%" */
    0x2d, 0xfa, 0x0, 0x9, 0xa0, 0xab, 0x3f, 0x50,
    0x3e, 0x10, 0xd7, 0xd, 0x70, 0xd6, 0x0, 0xba,
    0x1f, 0x67, 0xc0, 0x0, 0x3f, 0xfc, 0x3f, 0x20,
    0x0, 0x0, 0x20, 0xc7, 0x0, 0x0, 0x0, 0x6,
    0xd1, 0xbf, 0xb1, 0x0, 0x1e, 0x38, 0xd3, 0xd8,
    0x0, 0xa9, 0xa, 0xa0, 0xaa, 0x5, 0xe0, 0x8,
    0xd3, 0xd8, 0xe, 0x40, 0x1, 0xcf, 0xc1,

    /* U+0026 "&" */
    0x0, 0x7e, 0xfa, 0x0, 0x0, 0x4, 0xfc, 0xaf,
    0x80, 0x0, 0x8, 0xf2, 0xd, 0xb0, 0x0, 0x6,
    0xf5, 0x5f, 0x80, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x2, 0xdf, 0xe0, 0x1, 0x10, 0xd, 0xfa,
    0xf9, 0xf, 0x80, 0x5f, 0x80, 0xaf, 0xaf, 0x60,
    0x5f, 0x80, 0xc, 0xfe, 0x0, 0xe, 0xfa, 0x9f,
    0xfe, 0x10, 0x2, 0xbf, 0xfb, 0x6f, 0xc0,

    /* U+0027 "'" */
    0x3f, 0x23, 0xf2, 0x3f, 0x22, 0xe2,

    /* U+0028 "(" */
    0x1, 0xeb, 0x0, 0x8f, 0x30, 0xe, 0xd0, 0x4,
    0xf9, 0x0, 0x7f, 0x60, 0x9, 0xf4, 0x0, 0x9f,
    0x40, 0x9, 0xf4, 0x0, 0x7f, 0x60, 0x4, 0xf9,
    0x0, 0xf, 0xd0, 0x0, 0x9f, 0x30, 0x1, 0xea,
    0x0, 0x0, 0x10,

    /* U+0029 ")" */
    0x3f, 0x70, 0x0, 0xbf, 0x10, 0x5, 0xf7, 0x0,
    0x1f, 0xc0, 0x0, 0xef, 0x0, 0xc, 0xf1, 0x0,
    0xbf, 0x20, 0xc, 0xf1, 0x0, 0xef, 0x0, 0x1f,
    0xc0, 0x5, 0xf7, 0x0, 0xbf, 0x10, 0x2f, 0x80,
    0x0, 0x10, 0x0,

    /* U+002A "*" */
    0x0, 0xc6, 0x0, 0xa7, 0xb7, 0xb4, 0x4c, 0xff,
    0x91, 0x5d, 0xff, 0xa2, 0x96, 0xb6, 0xa4, 0x0,
    0xb5, 0x0,

    /* U+002B "+" */
    0x0, 0x1f, 0x70, 0x0, 0x0, 0x1f, 0x70, 0x0,
    0x0, 0x1f, 0x70, 0x0, 0xbf, 0xff, 0xff, 0xf2,
    0x79, 0xaf, 0xc9, 0x91, 0x0, 0x1f, 0x70, 0x0,
    0x0, 0x1f, 0x70, 0x0,

    /* U+002C "," */
    0x0, 0x0, 0x9, 0xf0, 0xb, 0xc0, 0xd, 0x80,
    0x1f, 0x30,

    /* U+002D "-" */
    0x8, 0x88, 0x84, 0xf, 0xff, 0xf8,

    /* U+002E "." */
    0x1, 0x10, 0xe, 0xf0, 0xc, 0xd0,

    /* U+002F "/" */
    0x0, 0x4, 0x70, 0x0, 0xc, 0xd0, 0x0, 0xf,
    0x80, 0x0, 0x4f, 0x40, 0x0, 0x8f, 0x0, 0x0,
    0xdc, 0x0, 0x1, 0xf8, 0x0, 0x5, 0xf3, 0x0,
    0x9, 0xf0, 0x0, 0xd, 0xb0, 0x0, 0x2f, 0x70,
    0x0, 0x6f, 0x30, 0x0, 0xae, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x4d, 0xfe, 0x80, 0x0, 0x4f, 0xfa, 0xdf,
    0x90, 0xc, 0xf3, 0x0, 0xdf, 0x10, 0xfd, 0x0,
    0x7, 0xf6, 0x3f, 0xa0, 0x0, 0x5f, 0x83, 0xf9,
    0x0, 0x4, 0xf9, 0x3f, 0xa0, 0x0, 0x5f, 0x80,
    0xfd, 0x0, 0x7, 0xf6, 0xc, 0xf4, 0x0, 0xdf,
    0x10, 0x3f, 0xfa, 0xdf, 0x80, 0x0, 0x4c, 0xfe,
    0x80, 0x0,

    /* U+0031 "1" */
    0x0, 0x7f, 0xf0, 0x1c, 0xff, 0xf0, 0x3f, 0x6c,
    0xf0, 0x12, 0xc, 0xf0, 0x0, 0xc, 0xf0, 0x0,
    0xc, 0xf0, 0x0, 0xc, 0xf0, 0x0, 0xc, 0xf0,
    0x0, 0xc, 0xf0, 0x0, 0xc, 0xf0, 0x0, 0xc,
    0xf0,

    /* U+0032 "2" */
    0x0, 0x8e, 0xfd, 0x50, 0xa, 0xfc, 0x9e, 0xf4,
    0xf, 0xc0, 0x3, 0xfa, 0x6, 0x30, 0x1, 0xfb,
    0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x3f, 0xd0,
    0x0, 0x3, 0xfe, 0x20, 0x0, 0x2e, 0xf3, 0x0,
    0x2, 0xef, 0x40, 0x0, 0xd, 0xfd, 0xaa, 0xa8,
    0x1f, 0xff, 0xff, 0xfe,

    /* U+0033 "3" */
    0x0, 0x6d, 0xfe, 0x70, 0x0, 0x7f, 0xd9, 0xdf,
    0x70, 0xe, 0xf0, 0x0, 0xfd, 0x0, 0x11, 0x0,
    0xe, 0xe0, 0x0, 0x1, 0x28, 0xf7, 0x0, 0x0,
    0x8f, 0xfa, 0x0, 0x0, 0x2, 0x59, 0xfc, 0x0,
    0x0, 0x0, 0xa, 0xf3, 0x1f, 0xc0, 0x0, 0xcf,
    0x30, 0xbf, 0xd9, 0xcf, 0xd0, 0x0, 0x8e, 0xfe,
    0x91, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x3f, 0xf5, 0x0, 0x0, 0xd, 0xff,
    0x50, 0x0, 0x6, 0xfb, 0xf5, 0x0, 0x0, 0xec,
    0x6f, 0x50, 0x0, 0x8f, 0x36, 0xf5, 0x0, 0x2f,
    0xa0, 0x6f, 0x50, 0xb, 0xf2, 0x6, 0xf5, 0x3,
    0xfe, 0x99, 0xcf, 0xc6, 0x4f, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x6f, 0x50, 0x0, 0x0, 0x6,
    0xf5, 0x0,

    /* U+0035 "5" */
    0x6, 0xff, 0xff, 0xf9, 0x0, 0x7f, 0xa9, 0x99,
    0x50, 0x9, 0xf1, 0x0, 0x0, 0x0, 0xaf, 0x1,
    0x10, 0x0, 0xc, 0xfc, 0xff, 0xa0, 0x0, 0xae,
    0x85, 0xbf, 0x90, 0x0, 0x0, 0x0, 0xef, 0x0,
    0x0, 0x0, 0xb, 0xf1, 0xf, 0xd0, 0x1, 0xee,
    0x0, 0x9f, 0xc9, 0xdf, 0x70, 0x0, 0x8e, 0xfd,
    0x60, 0x0,

    /* U+0036 "6" */
    0x0, 0x3c, 0xfe, 0x80, 0x0, 0x2f, 0xfb, 0xdf,
    0xa0, 0xb, 0xf3, 0x0, 0xdf, 0x10, 0xfc, 0x0,
    0x10, 0x10, 0x2f, 0x99, 0xff, 0xc2, 0x3, 0xff,
    0xc5, 0x9f, 0xd0, 0x3f, 0xe0, 0x0, 0xbf, 0x31,
    0xfc, 0x0, 0x8, 0xf4, 0xd, 0xf1, 0x0, 0xcf,
    0x20, 0x5f, 0xe9, 0xcf, 0xa0, 0x0, 0x5d, 0xfe,
    0x80, 0x0,

    /* U+0037 "7" */
    0x6f, 0xff, 0xff, 0xf8, 0x4a, 0xaa, 0xac, 0xf7,
    0x0, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x0, 0xbf, 0x30, 0x0, 0x3, 0xfb, 0x0,
    0x0, 0xa, 0xf4, 0x0, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x9f, 0x50, 0x0, 0x1, 0xfd, 0x0, 0x0,
    0x7, 0xf6, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x6d, 0xfe, 0x70, 0x0, 0x7f, 0xd8, 0xcf,
    0x70, 0xd, 0xf1, 0x0, 0xfd, 0x0, 0xdf, 0x0,
    0xe, 0xd0, 0x7, 0xf8, 0x28, 0xf7, 0x0, 0xa,
    0xff, 0xfb, 0x0, 0xa, 0xf9, 0x59, 0xfb, 0x2,
    0xfb, 0x0, 0xb, 0xf3, 0x3f, 0xc0, 0x0, 0xbf,
    0x40, 0xcf, 0xb7, 0xbf, 0xd0, 0x1, 0x9e, 0xfe,
    0x91, 0x0,

    /* U+0039 "9" */
    0x0, 0x7e, 0xfd, 0x60, 0x0, 0x8f, 0xd9, 0xef,
    0x60, 0xf, 0xd0, 0x1, 0xfe, 0x3, 0xf9, 0x0,
    0xb, 0xf3, 0x2f, 0xb0, 0x0, 0xdf, 0x40, 0xdf,
    0x72, 0x9f, 0xf5, 0x3, 0xef, 0xfd, 0x9f, 0x30,
    0x0, 0x43, 0xa, 0xf1, 0xf, 0xd0, 0x2, 0xfc,
    0x0, 0x9f, 0xda, 0xff, 0x30, 0x0, 0x8e, 0xfc,
    0x40, 0x0,

    /* U+003A ":" */
    0xc, 0xd0, 0xe, 0xf0, 0x1, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0xe, 0xf0, 0xc, 0xd0,

    /* U+003B ";" */
    0x9, 0xe1, 0xc, 0xf3, 0x1, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf0, 0xa, 0xc0,
    0xd, 0x80, 0x1f, 0x30, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd1,
    0x0, 0x7, 0xef, 0xe1, 0x17, 0xef, 0xc5, 0x0,
    0xaf, 0xc4, 0x0, 0x0, 0x7f, 0xf9, 0x20, 0x0,
    0x2, 0x9f, 0xfb, 0x40, 0x0, 0x1, 0x9f, 0xf1,
    0x0, 0x0, 0x1, 0x81,

    /* U+003D "=" */
    0x9f, 0xff, 0xff, 0xf0, 0x59, 0x99, 0x99, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x59, 0x99, 0x99, 0x90,
    0x9f, 0xff, 0xff, 0xf0,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0xa9, 0x20, 0x0, 0x0,
    0x8f, 0xfa, 0x20, 0x0, 0x3, 0xaf, 0xfb, 0x30,
    0x0, 0x1, 0x8f, 0xf1, 0x0, 0x6, 0xdf, 0xd1,
    0x18, 0xff, 0xc5, 0x0, 0xbf, 0xc4, 0x0, 0x0,
    0x73, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x3, 0xcf, 0xe9, 0x1, 0xff, 0x9c, 0xf9, 0x6f,
    0x70, 0xf, 0xe0, 0x0, 0x1, 0xfe, 0x0, 0x1,
    0xcf, 0x70, 0x0, 0xdf, 0x70, 0x0, 0x4f, 0x80,
    0x0, 0x4, 0xc3, 0x0, 0x0, 0x3, 0x0, 0x0,
    0x7, 0xf8, 0x0, 0x0, 0x5f, 0x50, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x7c, 0xff, 0xea, 0x30, 0x0, 0x0,
    0x2d, 0xfb, 0x77, 0x9e, 0xf7, 0x0, 0x0, 0xde,
    0x30, 0x0, 0x0, 0x9f, 0x40, 0x7, 0xf4, 0x6,
    0xba, 0x6a, 0x1c, 0xd0, 0xd, 0xc0, 0x8f, 0xee,
    0xff, 0x15, 0xf2, 0x1f, 0x70, 0xfb, 0x0, 0xaf,
    0x12, 0xf5, 0x3f, 0x53, 0xf4, 0x0, 0x5f, 0x11,
    0xf6, 0x3f, 0x53, 0xf4, 0x0, 0x5f, 0x12, 0xf5,
    0x1f, 0x71, 0xf8, 0x0, 0x8f, 0x14, 0xf2, 0xe,
    0xb0, 0xbf, 0x88, 0xff, 0x9c, 0xd0, 0x9, 0xf3,
    0x1b, 0xfd, 0x5b, 0xfd, 0x30, 0x1, 0xee, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xfb, 0x87,
    0x8c, 0x50, 0x0, 0x0, 0x1, 0x8c, 0xff, 0xeb,
    0x40, 0x0,

    /* U+0041 "A" */
    0x0, 0x3, 0xff, 0x50, 0x0, 0x0, 0x9, 0xff,
    0xb0, 0x0, 0x0, 0xe, 0xfd, 0xf0, 0x0, 0x0,
    0x3f, 0xa8, 0xf5, 0x0, 0x0, 0x8f, 0x53, 0xfa,
    0x0, 0x0, 0xdf, 0x10, 0xef, 0x0, 0x3, 0xfb,
    0x0, 0x9f, 0x50, 0x8, 0xff, 0xff, 0xff, 0xa0,
    0xd, 0xf8, 0x88, 0x8f, 0xf0, 0x2f, 0xc0, 0x0,
    0xa, 0xf4, 0x7f, 0x70, 0x0, 0x5, 0xfa,

    /* U+0042 "B" */
    0xff, 0xff, 0xfb, 0x20, 0xfe, 0x99, 0xcf, 0xd0,
    0xfd, 0x0, 0xc, 0xf2, 0xfd, 0x0, 0xb, 0xf2,
    0xfd, 0x11, 0x5f, 0xb0, 0xff, 0xff, 0xfd, 0x20,
    0xfe, 0x44, 0x6e, 0xf2, 0xfd, 0x0, 0x6, 0xf8,
    0xfd, 0x0, 0x6, 0xf8, 0xff, 0x99, 0xaf, 0xf3,
    0xff, 0xff, 0xfd, 0x50,

    /* U+0043 "C" */
    0x0, 0x19, 0xef, 0xd8, 0x0, 0x1, 0xdf, 0xeb,
    0xef, 0xc0, 0x9, 0xf9, 0x0, 0xb, 0xf6, 0xf,
    0xe0, 0x0, 0x2, 0xb7, 0x2f, 0xa0, 0x0, 0x0,
    0x0, 0x3f, 0x90, 0x0, 0x0, 0x0, 0x2f, 0xa0,
    0x0, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x2, 0xa6,
    0x9, 0xf9, 0x0, 0xb, 0xf5, 0x1, 0xdf, 0xeb,
    0xef, 0xb0, 0x0, 0x19, 0xef, 0xe8, 0x0,

    /* U+0044 "D" */
    0xff, 0xff, 0xea, 0x20, 0xf, 0xfa, 0xad, 0xfe,
    0x20, 0xfd, 0x0, 0x6, 0xfc, 0xf, 0xd0, 0x0,
    0xc, 0xf2, 0xfd, 0x0, 0x0, 0x8f, 0x5f, 0xd0,
    0x0, 0x7, 0xf6, 0xfd, 0x0, 0x0, 0x8f, 0x5f,
    0xd0, 0x0, 0xc, 0xf2, 0xfd, 0x0, 0x6, 0xfc,
    0xf, 0xfa, 0xad, 0xfe, 0x20, 0xff, 0xff, 0xe9,
    0x20, 0x0,

    /* U+0045 "E" */
    0xff, 0xff, 0xff, 0xaf, 0xf9, 0x99, 0x95, 0xfd,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf4, 0xff, 0x99, 0x99,
    0x2f, 0xd0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0xf,
    0xf9, 0x99, 0x96, 0xff, 0xff, 0xff, 0xa0,

    /* U+0046 "F" */
    0xff, 0xff, 0xff, 0x8f, 0xf9, 0x99, 0x95, 0xfd,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf1, 0xff, 0x99, 0x99,
    0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0xf,
    0xd0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x19, 0xef, 0xd7, 0x0, 0x1, 0xdf, 0xeb,
    0xef, 0xb0, 0x9, 0xf9, 0x0, 0xc, 0xf5, 0xf,
    0xe0, 0x0, 0x3, 0xa6, 0x2f, 0xa0, 0x0, 0x0,
    0x0, 0x3f, 0x90, 0x2, 0x88, 0x86, 0x2f, 0xa0,
    0x5, 0xff, 0xfc, 0xf, 0xe0, 0x0, 0x1, 0xfb,
    0x9, 0xf9, 0x0, 0x9, 0xf7, 0x1, 0xdf, 0xeb,
    0xef, 0xd0, 0x0, 0x19, 0xef, 0xe8, 0x0,

    /* U+0048 "H" */
    0xfd, 0x0, 0x0, 0x7f, 0x6f, 0xd0, 0x0, 0x7,
    0xf6, 0xfd, 0x0, 0x0, 0x7f, 0x6f, 0xd0, 0x0,
    0x7, 0xf6, 0xfd, 0x0, 0x0, 0x7f, 0x6f, 0xff,
    0xff, 0xff, 0xf6, 0xff, 0x99, 0x99, 0xcf, 0x6f,
    0xd0, 0x0, 0x7, 0xf6, 0xfd, 0x0, 0x0, 0x7f,
    0x6f, 0xd0, 0x0, 0x7, 0xf6, 0xfd, 0x0, 0x0,
    0x7f, 0x60,

    /* U+0049 "I" */
    0xfd, 0xfd, 0xfd, 0xfd, 0xfd, 0xfd, 0xfd, 0xfd,
    0xfd, 0xfd, 0xfd,

    /* U+004A "J" */
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0xf,
    0xd0, 0x0, 0x0, 0xfd, 0x8f, 0x50, 0x2f, 0xb3,
    0xfe, 0x9d, 0xf5, 0x5, 0xdf, 0xe6, 0x0,

    /* U+004B "K" */
    0xfd, 0x0, 0x7, 0xfc, 0xf, 0xd0, 0x4, 0xfe,
    0x10, 0xfd, 0x1, 0xef, 0x30, 0xf, 0xd0, 0xcf,
    0x60, 0x0, 0xfd, 0x9f, 0x90, 0x0, 0xf, 0xff,
    0xfa, 0x0, 0x0, 0xff, 0xcd, 0xf4, 0x0, 0xf,
    0xf1, 0x3f, 0xe0, 0x0, 0xfd, 0x0, 0x9f, 0x90,
    0xf, 0xd0, 0x0, 0xef, 0x40, 0xfd, 0x0, 0x4,
    0xfd, 0x0,

    /* U+004C "L" */
    0xfd, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0xfd,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0xf, 0xd0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0xf,
    0xf9, 0x99, 0x93, 0xff, 0xff, 0xff, 0x50,

    /* U+004D "M" */
    0xff, 0x70, 0x0, 0x0, 0xcf, 0xbf, 0xfd, 0x0,
    0x0, 0x2f, 0xfb, 0xff, 0xf3, 0x0, 0x8, 0xff,
    0xbf, 0xef, 0x90, 0x0, 0xee, 0xfb, 0xfc, 0xbf,
    0x0, 0x4f, 0x8f, 0xbf, 0xc5, 0xf5, 0xa, 0xf2,
    0xfb, 0xfc, 0xf, 0xb1, 0xfa, 0x1f, 0xbf, 0xc0,
    0x9f, 0x8f, 0x41, 0xfb, 0xfc, 0x3, 0xff, 0xe0,
    0x1f, 0xbf, 0xc0, 0xd, 0xf9, 0x1, 0xfb, 0xfc,
    0x0, 0x8f, 0x30, 0x1f, 0xb0,

    /* U+004E "N" */
    0xfe, 0x10, 0x0, 0x7f, 0x6f, 0xf9, 0x0, 0x7,
    0xf6, 0xff, 0xf4, 0x0, 0x7f, 0x6f, 0xef, 0xd0,
    0x7, 0xf6, 0xfd, 0x6f, 0x80, 0x7f, 0x6f, 0xd0,
    0xcf, 0x37, 0xf6, 0xfd, 0x2, 0xfc, 0x7f, 0x6f,
    0xd0, 0x8, 0xfe, 0xf6, 0xfd, 0x0, 0xd, 0xff,
    0x6f, 0xd0, 0x0, 0x3f, 0xf6, 0xfd, 0x0, 0x0,
    0x9f, 0x60,

    /* U+004F "O" */
    0x0, 0x19, 0xef, 0xd8, 0x0, 0x0, 0x1d, 0xfe,
    0xbe, 0xfc, 0x0, 0x9, 0xf9, 0x0, 0xb, 0xf7,
    0x0, 0xfe, 0x0, 0x0, 0x1f, 0xd0, 0x2f, 0xa0,
    0x0, 0x0, 0xdf, 0x3, 0xf9, 0x0, 0x0, 0xc,
    0xf1, 0x2f, 0xa0, 0x0, 0x0, 0xdf, 0x0, 0xfe,
    0x0, 0x0, 0x1f, 0xd0, 0x9, 0xf9, 0x0, 0xb,
    0xf7, 0x0, 0x1d, 0xfe, 0xbe, 0xfc, 0x0, 0x0,
    0x19, 0xef, 0xd8, 0x0, 0x0,

    /* U+0050 "P" */
    0xff, 0xff, 0xfa, 0x10, 0xff, 0x99, 0xdf, 0xc0,
    0xfd, 0x0, 0xc, 0xf3, 0xfd, 0x0, 0x8, 0xf6,
    0xfd, 0x0, 0x9, 0xf5, 0xfd, 0x0, 0x3f, 0xf1,
    0xff, 0xff, 0xff, 0x70, 0xff, 0x99, 0x83, 0x0,
    0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x19, 0xef, 0xd8, 0x0, 0x0, 0x1d, 0xfe,
    0xbe, 0xfc, 0x0, 0x9, 0xf9, 0x0, 0xb, 0xf7,
    0x0, 0xfe, 0x0, 0x0, 0x1f, 0xd0, 0x2f, 0xa0,
    0x0, 0x0, 0xdf, 0x3, 0xf9, 0x0, 0x0, 0xc,
    0xf1, 0x2f, 0xa0, 0x0, 0x0, 0xdf, 0x0, 0xfe,
    0x0, 0xa8, 0x1f, 0xd0, 0x9, 0xf9, 0x5, 0xfd,
    0xf7, 0x0, 0x1d, 0xfe, 0xbf, 0xfc, 0x0, 0x0,
    0x19, 0xef, 0xde, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x80,

    /* U+0052 "R" */
    0xff, 0xff, 0xfa, 0x10, 0xff, 0x99, 0xcf, 0xd0,
    0xfd, 0x0, 0xb, 0xf4, 0xfd, 0x0, 0x7, 0xf6,
    0xfd, 0x0, 0xb, 0xf4, 0xff, 0x99, 0xcf, 0xd0,
    0xff, 0xff, 0xfd, 0x20, 0xfd, 0x1, 0xfe, 0x0,
    0xfd, 0x0, 0x8f, 0x80, 0xfd, 0x0, 0x1f, 0xf1,
    0xfd, 0x0, 0x8, 0xf8,

    /* U+0053 "S" */
    0x0, 0x7d, 0xfe, 0x91, 0x0, 0x9f, 0xda, 0xcf,
    0xc0, 0xf, 0xe0, 0x0, 0xaf, 0x31, 0xfe, 0x0,
    0x0, 0x10, 0xb, 0xfe, 0x83, 0x0, 0x0, 0x19,
    0xff, 0xfe, 0x40, 0x0, 0x0, 0x49, 0xff, 0x21,
    0x42, 0x0, 0x7, 0xf6, 0x3f, 0xb0, 0x0, 0x9f,
    0x50, 0xcf, 0xda, 0xcf, 0xe0, 0x1, 0x9e, 0xfe,
    0xa2, 0x0,

    /* U+0054 "T" */
    0x7f, 0xff, 0xff, 0xff, 0xb4, 0x99, 0xbf, 0xc9,
    0x96, 0x0, 0x4, 0xf8, 0x0, 0x0, 0x0, 0x4f,
    0x80, 0x0, 0x0, 0x4, 0xf8, 0x0, 0x0, 0x0,
    0x4f, 0x80, 0x0, 0x0, 0x4, 0xf8, 0x0, 0x0,
    0x0, 0x4f, 0x80, 0x0, 0x0, 0x4, 0xf8, 0x0,
    0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x4, 0xf8,
    0x0, 0x0,

    /* U+0055 "U" */
    0xfd, 0x0, 0x0, 0x9f, 0x4f, 0xd0, 0x0, 0x9,
    0xf4, 0xfd, 0x0, 0x0, 0x9f, 0x4f, 0xd0, 0x0,
    0x9, 0xf4, 0xfd, 0x0, 0x0, 0x9f, 0x4f, 0xd0,
    0x0, 0x9, 0xf4, 0xfd, 0x0, 0x0, 0x9f, 0x4f,
    0xe0, 0x0, 0xa, 0xf3, 0xcf, 0x60, 0x2, 0xff,
    0x3, 0xff, 0xcb, 0xff, 0x70, 0x3, 0xbf, 0xfd,
    0x50, 0x0,

    /* U+0056 "V" */
    0x7f, 0x80, 0x0, 0x6, 0xfa, 0x2f, 0xd0, 0x0,
    0xb, 0xf4, 0xd, 0xf2, 0x0, 0xf, 0xf0, 0x8,
    0xf6, 0x0, 0x4f, 0xa0, 0x3, 0xfb, 0x0, 0x9f,
    0x50, 0x0, 0xdf, 0x10, 0xef, 0x0, 0x0, 0x8f,
    0x53, 0xfa, 0x0, 0x0, 0x3f, 0xa8, 0xf5, 0x0,
    0x0, 0xe, 0xfd, 0xf0, 0x0, 0x0, 0x9, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0,

    /* U+0057 "W" */
    0x9f, 0x50, 0x0, 0xff, 0x10, 0x4, 0xfb, 0x5f,
    0x90, 0x3, 0xff, 0x50, 0x7, 0xf7, 0x1f, 0xd0,
    0x7, 0xff, 0x80, 0xb, 0xf2, 0xd, 0xf0, 0xb,
    0xfd, 0xc0, 0xf, 0xe0, 0x8, 0xf4, 0xf, 0xb9,
    0xf0, 0x2f, 0xa0, 0x4, 0xf8, 0x3f, 0x65, 0xf4,
    0x6f, 0x60, 0x0, 0xfb, 0x6f, 0x21, 0xf8, 0xaf,
    0x20, 0x0, 0xcf, 0xae, 0x0, 0xcc, 0xdd, 0x0,
    0x0, 0x8f, 0xfa, 0x0, 0x8f, 0xf9, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0xf, 0xf1, 0x0,

    /* U+0058 "X" */
    0x4f, 0xd0, 0x0, 0x2f, 0xe0, 0xb, 0xf6, 0x0,
    0xbf, 0x50, 0x2, 0xfe, 0x4, 0xfc, 0x0, 0x0,
    0x8f, 0x8d, 0xf3, 0x0, 0x0, 0xe, 0xff, 0xa0,
    0x0, 0x0, 0x8, 0xff, 0x30, 0x0, 0x0, 0xe,
    0xff, 0xa0, 0x0, 0x0, 0x9f, 0x8d, 0xf3, 0x0,
    0x2, 0xfe, 0x4, 0xfc, 0x0, 0xb, 0xf5, 0x0,
    0xbf, 0x60, 0x5f, 0xc0, 0x0, 0x2f, 0xe0,

    /* U+0059 "Y" */
    0x6f, 0xa0, 0x0, 0xc, 0xf4, 0xd, 0xf2, 0x0,
    0x4f, 0xc0, 0x5, 0xfa, 0x0, 0xcf, 0x30, 0x0,
    0xcf, 0x35, 0xfa, 0x0, 0x0, 0x4f, 0xbd, 0xf2,
    0x0, 0x0, 0xb, 0xff, 0x90, 0x0, 0x0, 0x3,
    0xff, 0x10, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,

    /* U+005A "Z" */
    0x3f, 0xff, 0xff, 0xff, 0x52, 0x99, 0x99, 0xaf,
    0xf2, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x3,
    0xfd, 0x0, 0x0, 0x0, 0xcf, 0x30, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0,
    0xb, 0xf5, 0x0, 0x0, 0x5, 0xfa, 0x0, 0x0,
    0x1, 0xef, 0xb9, 0x99, 0x93, 0x3f, 0xff, 0xff,
    0xff, 0x50,

    /* U+005B "[" */
    0x7f, 0xff, 0x27, 0xf9, 0x60, 0x7f, 0x40, 0x7,
    0xf4, 0x0, 0x7f, 0x40, 0x7, 0xf4, 0x0, 0x7f,
    0x40, 0x7, 0xf4, 0x0, 0x7f, 0x40, 0x7, 0xf4,
    0x0, 0x7f, 0x40, 0x7, 0xf5, 0x10, 0x7f, 0xff,
    0x22, 0x44, 0x40,

    /* U+005C "\\" */
    0x56, 0x0, 0x0, 0x8f, 0x10, 0x0, 0x4f, 0x50,
    0x0, 0xf, 0x90, 0x0, 0xb, 0xd0, 0x0, 0x7,
    0xf1, 0x0, 0x3, 0xf6, 0x0, 0x0, 0xea, 0x0,
    0x0, 0xae, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x2f,
    0x60, 0x0, 0xe, 0xa0, 0x0, 0xa, 0xe0,

    /* U+005D "]" */
    0xaf, 0xff, 0x46, 0xdf, 0x0, 0xcf, 0x0, 0xcf,
    0x0, 0xcf, 0x0, 0xcf, 0x0, 0xcf, 0x0, 0xcf,
    0x0, 0xcf, 0x0, 0xcf, 0x0, 0xcf, 0x11, 0xcf,
    0xaf, 0xff, 0x34, 0x44,

    /* U+005E "^" */
    0x0, 0x59, 0x20, 0x0, 0xe, 0xfa, 0x0, 0x6,
    0xf5, 0xf2, 0x0, 0xe8, 0xc, 0xa0, 0x4c, 0x10,
    0x4c, 0x10,

    /* U+005F "_" */
    0x2, 0x22, 0x22, 0x21, 0xf, 0xff, 0xff, 0xf9,
    0x5, 0x55, 0x55, 0x52,

    /* U+0060 "`" */
    0x44, 0x6, 0xf3, 0xc, 0xa0,

    /* U+0061 "a" */
    0x2, 0xbf, 0xfb, 0x20, 0xd, 0xf8, 0x8f, 0xe0,
    0x4, 0x30, 0xa, 0xf2, 0x1, 0x7a, 0xcf, 0xf2,
    0x2f, 0xe9, 0x6b, 0xf2, 0x6f, 0x50, 0xc, 0xf2,
    0x4f, 0xc6, 0x9f, 0xf2, 0x8, 0xef, 0xa9, 0xf2,

    /* U+0062 "b" */
    0xfc, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0xfc, 0x6e, 0xf9, 0x0,
    0xfe, 0xd8, 0xdf, 0x90, 0xff, 0x10, 0xe, 0xf0,
    0xfd, 0x0, 0xa, 0xf3, 0xfd, 0x0, 0xa, 0xf3,
    0xff, 0x20, 0xe, 0xf0, 0xfe, 0xd8, 0xdf, 0x90,
    0xfc, 0x6e, 0xf9, 0x0,

    /* U+0063 "c" */
    0x0, 0x9e, 0xfc, 0x40, 0xb, 0xfb, 0x8e, 0xf2,
    0x2f, 0xc0, 0x4, 0xd6, 0x6f, 0x70, 0x0, 0x0,
    0x6f, 0x70, 0x0, 0x0, 0x2f, 0xc0, 0x4, 0xd6,
    0xb, 0xfb, 0x9e, 0xf2, 0x0, 0x9e, 0xfc, 0x40,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd,
    0x0, 0x0, 0x0, 0xfd, 0x1, 0xbf, 0xe4, 0xfd,
    0xc, 0xfb, 0x9d, 0xfd, 0x3f, 0xc0, 0x4, 0xfd,
    0x5f, 0x80, 0x0, 0xfd, 0x5f, 0x80, 0x0, 0xfd,
    0x3f, 0xc0, 0x4, 0xfd, 0xc, 0xfb, 0x9d, 0xfd,
    0x1, 0xbf, 0xe4, 0xfd,

    /* U+0065 "e" */
    0x0, 0x9e, 0xfc, 0x30, 0xa, 0xfa, 0x7d, 0xf2,
    0x2f, 0xa0, 0x2, 0xf8, 0x6f, 0xff, 0xff, 0xfb,
    0x6f, 0x83, 0x33, 0x32, 0x3f, 0xa0, 0x1, 0x73,
    0xb, 0xfa, 0x7d, 0xf4, 0x0, 0x9e, 0xfd, 0x50,

    /* U+0066 "f" */
    0x0, 0x27, 0x60, 0x3, 0xff, 0xf0, 0x8, 0xf6,
    0x10, 0x9, 0xf3, 0x0, 0xbf, 0xff, 0xc0, 0x5c,
    0xf9, 0x50, 0x9, 0xf3, 0x0, 0x9, 0xf3, 0x0,
    0x9, 0xf3, 0x0, 0x9, 0xf3, 0x0, 0x9, 0xf3,
    0x0, 0x9, 0xf3, 0x0,

    /* U+0067 "g" */
    0x1, 0xbf, 0xe4, 0xfd, 0xc, 0xfb, 0x9d, 0xfd,
    0x3f, 0xc0, 0x4, 0xfd, 0x5f, 0x80, 0x0, 0xfd,
    0x5f, 0x70, 0x0, 0xfd, 0x3f, 0xc0, 0x3, 0xfd,
    0xd, 0xfa, 0x8d, 0xfd, 0x1, 0xbf, 0xe5, 0xfd,
    0x1, 0x20, 0x0, 0xfc, 0xe, 0xf8, 0x6c, 0xf7,
    0x2, 0xbf, 0xfd, 0x70,

    /* U+0068 "h" */
    0x1f, 0xb0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0x1f, 0xb7, 0xef, 0x90,
    0x1f, 0xed, 0x9e, 0xf6, 0x1f, 0xe0, 0x4, 0xfa,
    0x1f, 0xb0, 0x2, 0xfa, 0x1f, 0xb0, 0x2, 0xfa,
    0x1f, 0xb0, 0x2, 0xfa, 0x1f, 0xb0, 0x2, 0xfa,
    0x1f, 0xb0, 0x2, 0xfa,

    /* U+0069 "i" */
    0x6, 0x32, 0xfd, 0x6, 0x40, 0x0, 0x1f, 0xb1,
    0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f,
    0xb1, 0xfb,

    /* U+006A "j" */
    0x0, 0x63, 0x2, 0xfd, 0x0, 0x74, 0x0, 0x0,
    0x1, 0xfb, 0x1, 0xfb, 0x1, 0xfb, 0x1, 0xfb,
    0x1, 0xfb, 0x1, 0xfb, 0x1, 0xfb, 0x1, 0xfb,
    0x1, 0xfb, 0x2b, 0xf8, 0x4f, 0xb1,

    /* U+006B "k" */
    0x1f, 0xb0, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0x1f, 0xb0, 0xc, 0xf4,
    0x1f, 0xb0, 0xaf, 0x60, 0x1f, 0xb8, 0xf9, 0x0,
    0x1f, 0xff, 0xe0, 0x0, 0x1f, 0xfe, 0xf6, 0x0,
    0x1f, 0xc1, 0xef, 0x20, 0x1f, 0xb0, 0x5f, 0xc0,
    0x1f, 0xb0, 0xa, 0xf8,

    /* U+006C "l" */
    0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1,
    0xfb, 0x1f, 0xb1, 0xfb, 0x1f, 0xb1, 0xfb, 0x1f,
    0xb0,

    /* U+006D "m" */
    0x1f, 0xa9, 0xfe, 0x55, 0xef, 0xa0, 0x1f, 0xfa,
    0x8f, 0xfd, 0x8d, 0xf7, 0x1f, 0xd0, 0xa, 0xf4,
    0x3, 0xfa, 0x1f, 0xb0, 0x9, 0xf2, 0x2, 0xfa,
    0x1f, 0xb0, 0x9, 0xf2, 0x2, 0xfa, 0x1f, 0xb0,
    0x9, 0xf2, 0x2, 0xfa, 0x1f, 0xb0, 0x9, 0xf2,
    0x2, 0xfa, 0x1f, 0xb0, 0x9, 0xf2, 0x2, 0xfa,

    /* U+006E "n" */
    0x1f, 0xa8, 0xff, 0x80, 0x1f, 0xec, 0x8e, 0xf5,
    0x1f, 0xd0, 0x4, 0xf9, 0x1f, 0xb0, 0x2, 0xfa,
    0x1f, 0xb0, 0x2, 0xfa, 0x1f, 0xb0, 0x2, 0xfa,
    0x1f, 0xb0, 0x2, 0xfa, 0x1f, 0xb0, 0x2, 0xfa,

    /* U+006F "o" */
    0x0, 0x9e, 0xfc, 0x40, 0xb, 0xfa, 0x8e, 0xf3,
    0x2f, 0xc0, 0x3, 0xfb, 0x6f, 0x70, 0x0, 0xfe,
    0x6f, 0x70, 0x0, 0xfe, 0x2f, 0xc0, 0x4, 0xfb,
    0xb, 0xfb, 0x8e, 0xf3, 0x0, 0x9e, 0xfc, 0x40,

    /* U+0070 "p" */
    0x1f, 0xb8, 0xfe, 0x90, 0x1, 0xff, 0xc7, 0xcf,
    0x80, 0x1f, 0xe0, 0x0, 0xff, 0x1, 0xfb, 0x0,
    0xb, 0xf1, 0x1f, 0xb0, 0x0, 0xcf, 0x11, 0xff,
    0x10, 0x1f, 0xf0, 0x1f, 0xed, 0x8d, 0xf8, 0x1,
    0xfb, 0x7f, 0xf9, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x1, 0xfb, 0x0, 0x0, 0x0, 0x1f, 0xb0, 0x0,
    0x0, 0x0,

    /* U+0071 "q" */
    0x1, 0xbf, 0xe4, 0xfd, 0xc, 0xfb, 0x9d, 0xfd,
    0x3f, 0xc0, 0x4, 0xfd, 0x5f, 0x80, 0x0, 0xfd,
    0x5f, 0x80, 0x0, 0xfd, 0x3f, 0xc0, 0x4, 0xfd,
    0xc, 0xfb, 0x9d, 0xfd, 0x1, 0xbf, 0xe4, 0xfd,
    0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd,
    0x0, 0x0, 0x0, 0xfd,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x1f, 0xaa, 0xf4, 0x1f, 0xfd,
    0x92, 0x1f, 0xe0, 0x0, 0x1f, 0xb0, 0x0, 0x1f,
    0xb0, 0x0, 0x1f, 0xb0, 0x0, 0x1f, 0xb0, 0x0,
    0x1f, 0xb0, 0x0,

    /* U+0073 "s" */
    0x3, 0xcf, 0xfa, 0x10, 0xf, 0xe7, 0x8f, 0xb0,
    0x2f, 0xb0, 0x4, 0x30, 0xc, 0xfe, 0xa6, 0x0,
    0x0, 0x6a, 0xdf, 0xc0, 0x15, 0x20, 0xd, 0xf1,
    0x2f, 0xe7, 0x8f, 0xd0, 0x4, 0xcf, 0xfb, 0x20,

    /* U+0074 "t" */
    0x9, 0xf3, 0x0, 0x9f, 0x30, 0xaf, 0xff, 0xb4,
    0xcf, 0x95, 0x9, 0xf3, 0x0, 0x9f, 0x30, 0x9,
    0xf3, 0x0, 0x9f, 0x40, 0x7, 0xfc, 0x60, 0xa,
    0xfb,

    /* U+0075 "u" */
    0x1f, 0xb0, 0x3, 0xf9, 0x1f, 0xb0, 0x3, 0xf9,
    0x1f, 0xb0, 0x3, 0xf9, 0x1f, 0xb0, 0x3, 0xf9,
    0x1f, 0xb0, 0x3, 0xf9, 0xf, 0xd0, 0x6, 0xf9,
    0xc, 0xfb, 0x9e, 0xf9, 0x2, 0xcf, 0xc5, 0xf9,

    /* U+0076 "v" */
    0x8f, 0x60, 0x5, 0xf9, 0x2f, 0xa0, 0x9, 0xf3,
    0xd, 0xf0, 0xe, 0xe0, 0x8, 0xf4, 0x3f, 0x90,
    0x2, 0xf9, 0x8f, 0x30, 0x0, 0xdd, 0xce, 0x0,
    0x0, 0x7f, 0xf8, 0x0, 0x0, 0x2f, 0xf3, 0x0,

    /* U+0077 "w" */
    0x8f, 0x40, 0x2f, 0xe0, 0x9, 0xf4, 0x4f, 0x80,
    0x6f, 0xf2, 0xd, 0xf0, 0xf, 0xc0, 0xae, 0xf6,
    0xf, 0xb0, 0xb, 0xf0, 0xe9, 0xda, 0x4f, 0x70,
    0x7, 0xf6, 0xf4, 0x9e, 0x8f, 0x20, 0x2, 0xfd,
    0xf0, 0x5f, 0xee, 0x0, 0x0, 0xef, 0xc0, 0x1f,
    0xfa, 0x0, 0x0, 0xaf, 0x80, 0xc, 0xf5, 0x0,

    /* U+0078 "x" */
    0x3f, 0xa0, 0xc, 0xf2, 0xa, 0xf2, 0x4f, 0x80,
    0x2, 0xfb, 0xde, 0x0, 0x0, 0x8f, 0xf6, 0x0,
    0x0, 0x8f, 0xf6, 0x0, 0x2, 0xfb, 0xde, 0x10,
    0xb, 0xf3, 0x4f, 0x90, 0x4f, 0x90, 0xb, 0xf2,

    /* U+0079 "y" */
    0x8f, 0x60, 0x5, 0xf9, 0x2f, 0xa0, 0x9, 0xf3,
    0xd, 0xf0, 0xe, 0xe0, 0x8, 0xf4, 0x3f, 0x80,
    0x2, 0xf8, 0x7f, 0x30, 0x0, 0xdd, 0xcd, 0x0,
    0x0, 0x7f, 0xf8, 0x0, 0x0, 0x2f, 0xf3, 0x0,
    0x0, 0x1f, 0xd0, 0x0, 0x7, 0xcf, 0x70, 0x0,
    0xe, 0xf9, 0x0, 0x0,

    /* U+007A "z" */
    0xf, 0xff, 0xff, 0xf0, 0x9, 0x99, 0xcf, 0xc0,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0xa, 0xf5, 0x0,
    0x0, 0x6f, 0x90, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0xd, 0xfb, 0x99, 0x90, 0x2f, 0xff, 0xff, 0xf1,

    /* U+007B "{" */
    0x0, 0x2c, 0xf2, 0x0, 0xcf, 0x80, 0x0, 0xfb,
    0x0, 0x0, 0xfb, 0x0, 0x0, 0xfa, 0x0, 0x5,
    0xf7, 0x0, 0x9f, 0x80, 0x0, 0x6e, 0xf6, 0x0,
    0x2, 0xfa, 0x0, 0x0, 0xfb, 0x0, 0x0, 0xfb,
    0x0, 0x0, 0xee, 0x30, 0x0, 0x6f, 0xf2, 0x0,
    0x1, 0x40,

    /* U+007C "|" */
    0x28, 0x25, 0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55,
    0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f,
    0x55, 0xf5, 0x5f, 0x55, 0xf5, 0x5f, 0x55, 0xf5,
    0x5f, 0x55, 0xf5,

    /* U+007D "}" */
    0x9e, 0x80, 0x0, 0x4c, 0xf4, 0x0, 0x3, 0xf8,
    0x0, 0x3, 0xf8, 0x0, 0x2, 0xf8, 0x0, 0x0,
    0xec, 0x10, 0x0, 0x2d, 0xf1, 0x0, 0xef, 0xc1,
    0x2, 0xfa, 0x0, 0x3, 0xf8, 0x0, 0x3, 0xf8,
    0x0, 0x19, 0xf6, 0x0, 0xaf, 0xd1, 0x0, 0x23,
    0x0, 0x0,

    /* U+007E "~" */
    0x3d, 0xf8, 0x2, 0xc3, 0xbf, 0xaf, 0xcc, 0xf2,
    0xa7, 0x4, 0xdf, 0x80,

    /* U+0621 "ء" */
    0x0, 0x0, 0x1, 0xbf, 0xd0, 0xad, 0x44, 0xd,
    0x70, 0x0, 0x9e, 0x77, 0x42, 0xbf, 0xf4, 0xec,
    0x60, 0x0, 0x0, 0x0,

    /* U+0623 "أ" */
    0xa, 0x90, 0x29, 0x20, 0x29, 0x60, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90,

    /* U+0625 "إ" */
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xa, 0xa0,
    0x38, 0x0, 0x2e, 0xb0, 0x0, 0x0,

    /* U+0627 "ا" */
    0xb9, 0xb9, 0xb9, 0xb9, 0xb9, 0xb9, 0xb9, 0xb9,
    0xb9, 0xb9, 0xb9,

    /* U+0628 "ب" */
    0x6, 0x30, 0x0, 0x0, 0x0, 0x18, 0x0, 0xf3,
    0x0, 0x0, 0x0, 0x2, 0xf1, 0xf, 0x40, 0x0,
    0x0, 0x0, 0xae, 0x0, 0xae, 0x74, 0x34, 0x79,
    0xee, 0x30, 0x0, 0x7c, 0xff, 0xec, 0x95, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0,

    /* U+062D "ح" */
    0x9c, 0xef, 0xed, 0x60, 0x8, 0x58, 0xfd, 0x84,
    0x0, 0x6, 0xf5, 0x0, 0x0, 0x2, 0xf4, 0x0,
    0x0, 0x0, 0x8c, 0x0, 0x0, 0x0, 0x9, 0xa0,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0xeb, 0x30, 0x2, 0x80, 0x2, 0xaf, 0xff, 0xfb,
    0x0, 0x0, 0x1, 0x20, 0x0,

    /* U+0631 "ر" */
    0x0, 0x0, 0x6, 0x80, 0x0, 0x0, 0x5e, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0xab, 0x0, 0x0,
    0x5f, 0x40, 0x25, 0xbf, 0x80, 0x9f, 0xf9, 0x30,
    0x1, 0x10, 0x0, 0x0,

    /* U+0633 "س" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0x0, 0x0, 0x0, 0x7, 0x20, 0x7, 0x30, 0x5e,
    0x0, 0x0, 0x0, 0xb, 0x70, 0xe, 0x60, 0x5e,
    0x3, 0x60, 0x0, 0x8, 0xc0, 0x1f, 0x90, 0x6d,
    0xc, 0x70, 0x0, 0x7, 0xfa, 0xbe, 0xf8, 0xd9,
    0xf, 0x30, 0x0, 0x9, 0xde, 0xe3, 0x9f, 0xb1,
    0x1f, 0x30, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0,
    0xe, 0xa2, 0x14, 0xdd, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0636 "ض" */
    0x0, 0x0, 0x0, 0x0, 0x4a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xde, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdd, 0x53, 0xcb,
    0x2, 0x40, 0x0, 0xd, 0x6d, 0xb0, 0x0, 0x9d,
    0xc, 0x80, 0x0, 0xb, 0xfe, 0x44, 0x6c, 0xf6,
    0xf, 0x30, 0x0, 0xc, 0xdf, 0xff, 0xda, 0x30,
    0x1f, 0x30, 0x0, 0x1f, 0x50, 0x0, 0x0, 0x0,
    0xe, 0xa2, 0x14, 0xce, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0637 "ط" */
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xc0, 0x7,
    0xdf, 0xc3, 0x0, 0x8, 0xc1, 0xce, 0x63, 0xad,
    0x0, 0x8, 0xcc, 0xc1, 0x0, 0x7f, 0x4, 0x4a,
    0xff, 0x54, 0x6b, 0xf8, 0xf, 0xff, 0xff, 0xff,
    0xea, 0x40,

    /* U+0638 "ظ" */
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xc0, 0x4a, 0x0, 0x0, 0x0, 0x8,
    0xc0, 0x1, 0x0, 0x0, 0x0, 0x8, 0xc0, 0x7,
    0xdf, 0xc3, 0x0, 0x8, 0xc1, 0xce, 0x63, 0xad,
    0x0, 0x8, 0xcc, 0xc1, 0x0, 0x7f, 0x4, 0x4a,
    0xff, 0x54, 0x6b, 0xf8, 0xf, 0xff, 0xff, 0xff,
    0xea, 0x40,

    /* U+0639 "ع" */
    0x0, 0x3b, 0xe6, 0x0, 0x0, 0x2f, 0x84, 0x10,
    0x0, 0x8, 0xb0, 0x0, 0x10, 0x0, 0x7e, 0x69,
    0xef, 0x0, 0x0, 0xcf, 0xd7, 0x30, 0x0, 0x7f,
    0x60, 0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0x2,
    0xf1, 0x0, 0x0, 0x0, 0x1f, 0x20, 0x0, 0x0,
    0x0, 0xad, 0x40, 0x2, 0x72, 0x0, 0x9e, 0xff,
    0xfc, 0x10, 0x0, 0x1, 0x20, 0x0,

    /* U+0641 "ف" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x86, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xef, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x97, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x41, 0xf5, 0xd, 0x50, 0x0, 0x0, 0x9,
    0xff, 0xf3, 0x1f, 0x30, 0x0, 0x0, 0x0, 0x16,
    0xf1, 0xd, 0xc4, 0x10, 0x1, 0x36, 0xbf, 0x70,
    0x2, 0xaf, 0xff, 0xff, 0xfd, 0x93, 0x0, 0x0,
    0x0, 0x23, 0x22, 0x0, 0x0, 0x0,

    /* U+0642 "ق" */
    0x0, 0x0, 0x2, 0xc7, 0x80, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xb0, 0x0, 0x0, 0x9, 0xd5,
    0xd7, 0x0, 0x0, 0xb, 0xb0, 0xbb, 0x4, 0x30,
    0x3, 0xef, 0xec, 0xf, 0x30, 0x0, 0x1, 0x8b,
    0x3f, 0x0, 0x0, 0x0, 0xe6, 0x3f, 0x0, 0x0,
    0xb, 0xc0, 0xe, 0x91, 0x26, 0xdd, 0x10, 0x4,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x3, 0x20, 0x0,
    0x0,

    /* U+0644 "ل" */
    0x0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0x0,
    0x5e, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x0,
    0x0, 0x5e, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x0, 0x5e, 0x0, 0x0, 0x0, 0x5, 0xe0,
    0x0, 0x0, 0x0, 0x5e, 0x1, 0x10, 0x0, 0x6,
    0xe0, 0xd6, 0x0, 0x0, 0x8c, 0xf, 0x40, 0x0,
    0xd, 0x90, 0xcb, 0x21, 0x5c, 0xd1, 0x2, 0xdf,
    0xff, 0x91, 0x0, 0x0, 0x12, 0x0, 0x0,

    /* U+0645 "م" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x0, 0x1f, 0x95, 0xe8, 0x1, 0x9f, 0x40, 0xc9,
    0xb, 0xeb, 0xff, 0xe3, 0xf, 0x50, 0x12, 0x0,
    0xf, 0x50, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0,
    0xf, 0x50, 0x0, 0x0,

    /* U+0646 "ن" */
    0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x10, 0x5,
    0x50, 0x0, 0x0, 0x0, 0x6e, 0x7, 0x40, 0x0,
    0x2, 0xf2, 0xf5, 0x0, 0x0, 0x1f, 0x3f, 0x40,
    0x0, 0x3, 0xf2, 0xe6, 0x0, 0x0, 0x9d, 0x8,
    0xe4, 0x11, 0x7f, 0x40, 0x9, 0xff, 0xfd, 0x40,
    0x0, 0x0, 0x32, 0x0, 0x0,

    /* U+0648 "و" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xa0, 0x0,
    0xbc, 0x5e, 0x70, 0xb, 0xc5, 0xba, 0x0, 0x2b,
    0xff, 0xa0, 0x0, 0x1, 0xe6, 0x1, 0x26, 0xec,
    0x9, 0xff, 0xe8, 0x0, 0x12, 0x10, 0x0, 0x0,

    /* U+0649 "ى" */
    0x0, 0x0, 0x4, 0xce, 0xc3, 0x0, 0x0, 0x0,
    0xf8, 0x28, 0xc0, 0x0, 0x0, 0xe, 0xb4, 0x0,
    0x0, 0x74, 0x0, 0x2a, 0xfd, 0x10, 0xf, 0x30,
    0x0, 0x0, 0xd8, 0x0, 0xf4, 0x0, 0x0, 0x5f,
    0x60, 0x8, 0xfa, 0x9b, 0xef, 0x80, 0x0, 0x4,
    0x99, 0x85, 0x0, 0x0,

    /* U+064A "ي" */
    0x0, 0x0, 0x4, 0xce, 0xc3, 0x0, 0x0, 0x0,
    0xf8, 0x28, 0xc0, 0x0, 0x0, 0xe, 0xb4, 0x0,
    0x0, 0x84, 0x0, 0x19, 0xed, 0x20, 0xf, 0x30,
    0x0, 0x0, 0xd9, 0x0, 0xf5, 0x0, 0x0, 0x6f,
    0x60, 0x7, 0xfb, 0xac, 0xfe, 0x70, 0x0, 0x2,
    0x78, 0x63, 0x0, 0x0, 0x0, 0x8, 0x6c, 0x20,
    0x0, 0x0, 0x0, 0x10, 0x10, 0x0, 0x0,

    /* U+3002 "。" */
    0x0, 0x0, 0x67, 0x70, 0x70, 0x70, 0x77, 0x70,
    0x0, 0x0,

    /* U+4E09 "三" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x65, 0x55, 0x55, 0x55, 0x6d, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x90, 0x0, 0x0, 0x26, 0x55, 0x55, 0x55, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x16, 0x55,
    0x55, 0x55, 0x55, 0x58, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+4E2D "中" */
    0x0, 0x0, 0x41, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xa3, 0x0, 0x0,
    0x8, 0x55, 0x5b, 0x75, 0x55, 0xa2, 0xc0, 0x0,
    0xa3, 0x0, 0xc, 0xc, 0x0, 0xa, 0x30, 0x0,
    0xc0, 0xc0, 0x0, 0xa3, 0x0, 0xc, 0xd, 0x55,
    0x5b, 0x75, 0x55, 0xd0, 0xb0, 0x0, 0xa3, 0x0,
    0xa, 0x0, 0x0, 0xa, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xa3, 0x0, 0x0, 0x0, 0x0, 0xa, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xa3, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+4EAE "亮" */
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x68, 0x0, 0x0, 0x0, 0x3, 0x65,
    0x55, 0x57, 0x55, 0x59, 0x80, 0x0, 0x1, 0x75,
    0x55, 0x57, 0x50, 0x0, 0x0, 0x1, 0xa0, 0x0,
    0x7, 0x50, 0x0, 0x0, 0x2, 0xc5, 0x55, 0x5a,
    0x50, 0x0, 0x0, 0x21, 0x30, 0x0, 0x2, 0x0,
    0x10, 0x3, 0x95, 0x55, 0x55, 0x55, 0x57, 0xd1,
    0xc, 0x30, 0x20, 0x0, 0x30, 0x6, 0x0, 0x0,
    0x0, 0x69, 0x55, 0xc3, 0x0, 0x0, 0x0, 0x0,
    0x84, 0x0, 0xb1, 0x0, 0x30, 0x0, 0x0, 0xb0,
    0x0, 0xb1, 0x0, 0x52, 0x0, 0x38, 0x30, 0x0,
    0x7c, 0x99, 0xd4, 0x4, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+4ECA "今" */
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0x76, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x0, 0x90, 0x0, 0x0, 0x0, 0x1, 0xc2, 0x0,
    0x3b, 0x10, 0x0, 0x0, 0x1a, 0x11, 0x92, 0x3,
    0xd7, 0x10, 0x2, 0x81, 0x0, 0x3b, 0x0, 0x1b,
    0xd2, 0x14, 0x0, 0x0, 0x1, 0x0, 0x20, 0x0,
    0x0, 0x17, 0x55, 0x55, 0x58, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x0,
    0x0, 0x0,

    /* U+4F53 "体" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xb0, 0x0, 0xc2, 0x0, 0x0, 0x0, 0x9,
    0x40, 0x0, 0xc0, 0x0, 0x0, 0x0, 0xb, 0x0,
    0x0, 0xc0, 0x2, 0x50, 0x0, 0x69, 0x16, 0x5b,
    0xf8, 0x55, 0x50, 0x0, 0xac, 0x0, 0xc, 0xc5,
    0x0, 0x0, 0x6, 0x1b, 0x0, 0x57, 0xc1, 0x60,
    0x0, 0x13, 0xb, 0x0, 0xa0, 0xc0, 0x91, 0x0,
    0x0, 0xb, 0x7, 0x30, 0xc0, 0x2c, 0x0, 0x0,
    0xb, 0x25, 0x0, 0xc0, 0x5a, 0xc2, 0x0, 0xb,
    0x30, 0x75, 0xd5, 0x53, 0x30, 0x0, 0xb, 0x0,
    0x0, 0xc0, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0,
    0xc0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x20,
    0x0, 0x0,

    /* U+5149 "光" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4a, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x0, 0x48, 0x0, 0x91, 0x0, 0x0, 0x2, 0xc0,
    0x48, 0x4, 0xb0, 0x0, 0x0, 0x0, 0xa4, 0x48,
    0xa, 0x10, 0x0, 0x0, 0x0, 0x20, 0x48, 0x33,
    0x2, 0x40, 0x6, 0x55, 0x5c, 0x56, 0xc5, 0x56,
    0x60, 0x0, 0x0, 0x1c, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x29, 0x0, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x76, 0x0, 0xb0, 0x0, 0x30, 0x0, 0x0,
    0xb0, 0x0, 0xb0, 0x0, 0x60, 0x0, 0x8, 0x50,
    0x0, 0xc0, 0x0, 0xa0, 0x1, 0x73, 0x0, 0x0,
    0xab, 0xaa, 0xd2, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5747 "均" */
    0x0, 0x2, 0x0, 0x2, 0x10, 0x0, 0x0, 0x0,
    0xe, 0x0, 0x7, 0x90, 0x0, 0x0, 0x0, 0xc,
    0x0, 0xb, 0x10, 0x0, 0x10, 0x0, 0xc, 0x0,
    0x2b, 0x55, 0x55, 0xd1, 0x5, 0x5d, 0x86, 0x81,
    0x0, 0x0, 0xc0, 0x0, 0xc, 0x2, 0x53, 0x60,
    0x0, 0xc0, 0x0, 0xc, 0x2, 0x0, 0xb6, 0x0,
    0xc0, 0x0, 0xc, 0x0, 0x0, 0x35, 0x0, 0xb0,
    0x0, 0xc, 0x2, 0x10, 0x5, 0x41, 0xb0, 0x0,
    0x2e, 0x83, 0x6, 0x91, 0x2, 0xa0, 0x1d, 0xc3,
    0x2, 0xe6, 0x0, 0x4, 0x80, 0x3, 0x0, 0x0,
    0x20, 0x22, 0x1a, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xea, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0,

    /* U+5927 "大" */
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0x2, 0x10, 0x6, 0x55, 0x55, 0xe5,
    0x55, 0x59, 0x80, 0x0, 0x0, 0x0, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x87, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x31, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0x0, 0x73, 0x0, 0x0, 0x0, 0x0,
    0xb2, 0x0, 0xa, 0x40, 0x0, 0x0, 0x1a, 0x30,
    0x0, 0x1, 0xda, 0x30, 0x5, 0x70, 0x0, 0x0,
    0x0, 0xa, 0x70, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5929 "天" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x45, 0x55, 0x55, 0x55, 0xd4, 0x0, 0x0, 0x10,
    0x0, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0x1b, 0x10, 0x6, 0x55, 0x56, 0xc8, 0x55, 0x55,
    0x30, 0x0, 0x0, 0x6, 0x75, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0x21, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0x0, 0x82, 0x0, 0x0, 0x0, 0x0,
    0xb1, 0x0, 0x1c, 0x30, 0x0, 0x0, 0x9, 0x10,
    0x0, 0x3, 0xe8, 0x20, 0x3, 0x70, 0x0, 0x0,
    0x0, 0x2c, 0xa1, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5E73 "平" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x55, 0x55, 0x55, 0x55, 0x8b, 0x0, 0x0, 0x10,
    0x0, 0xa2, 0x0, 0x30, 0x0, 0x0, 0x9, 0x0,
    0xa2, 0x4, 0xd1, 0x0, 0x0, 0x6, 0xb0, 0xa2,
    0x9, 0x10, 0x0, 0x0, 0x0, 0xb0, 0xa2, 0x42,
    0x0, 0x0, 0x36, 0x55, 0x55, 0xc7, 0x65, 0x5a,
    0x90, 0x0, 0x0, 0x0, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0x1, 0x0, 0x0, 0xb5,
    0x55, 0x58, 0x55, 0x59, 0x70, 0x0, 0xc0, 0x9,
    0x30, 0xa, 0x20, 0x0, 0x0, 0xc4, 0x5b, 0x65,
    0x5d, 0x5b, 0x40, 0x0, 0xc0, 0x9, 0x20, 0xb,
    0x0, 0x0, 0x0, 0xc0, 0x9, 0x65, 0x5c, 0x0,
    0x0, 0x0, 0xc0, 0x4, 0x0, 0x3, 0x20, 0x0,
    0x0, 0xa0, 0x47, 0x55, 0x59, 0xb0, 0x0, 0x2,
    0x70, 0x0, 0x70, 0x2c, 0x0, 0x0, 0x6, 0x20,
    0x0, 0x29, 0xc1, 0x0, 0x0, 0x7, 0x0, 0x0,
    0x6a, 0xb6, 0x10, 0x0, 0x23, 0x2, 0x67, 0x30,
    0x7, 0xcd, 0x80, 0x10, 0x22, 0x0, 0x0, 0x0,
    0x1, 0x0,

    /* U+5F3A "强" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x55, 0x92, 0x48, 0x55, 0x5c, 0x0, 0x0, 0x0,
    0xb0, 0x47, 0x0, 0xb, 0x0, 0x0, 0x0, 0xb0,
    0x49, 0x44, 0x4c, 0x0, 0x3, 0x65, 0xd0, 0x34,
    0xb, 0x5, 0x0, 0x6, 0x50, 0x50, 0x0, 0xb,
    0x0, 0x0, 0x7, 0x30, 0x0, 0xb5, 0x5c, 0x58,
    0x80, 0xb, 0x65, 0xb1, 0xb0, 0xb, 0x4, 0x60,
    0x0, 0x0, 0xc0, 0xc5, 0x5c, 0x58, 0x70, 0x0,
    0x0, 0xb0, 0x70, 0xb, 0x2, 0x10, 0x0, 0x3,
    0x80, 0x0, 0xb, 0x6, 0x30, 0x2, 0x39, 0x41,
    0x24, 0x5d, 0x77, 0xd1, 0x0, 0x99, 0xa, 0xa6,
    0x31, 0x0, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+6587 "文" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x48, 0x0, 0x0, 0x10, 0x5, 0x55, 0x65,
    0x55, 0x57, 0x5a, 0xa0, 0x0, 0x0, 0x50, 0x0,
    0x2a, 0x0, 0x0, 0x0, 0x0, 0x51, 0x0, 0x66,
    0x0, 0x0, 0x0, 0x0, 0x7, 0x0, 0xb1, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x2, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x8a, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x8a, 0x70, 0x0, 0x0, 0x0, 0x1, 0x84,
    0x0, 0x7d, 0x84, 0x10, 0x3, 0x55, 0x0, 0x0,
    0x1, 0x8e, 0x80, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+65E5 "日" */
    0x95, 0x55, 0x55, 0x5a, 0x2c, 0x0, 0x0, 0x0,
    0xc0, 0xc0, 0x0, 0x0, 0xc, 0xc, 0x0, 0x0,
    0x0, 0xc0, 0xc0, 0x0, 0x0, 0xc, 0xd, 0x55,
    0x55, 0x55, 0xd0, 0xc0, 0x0, 0x0, 0xc, 0xc,
    0x0, 0x0, 0x0, 0xc0, 0xc0, 0x0, 0x0, 0xc,
    0xc, 0x0, 0x0, 0x0, 0xc0, 0xd5, 0x55, 0x55,
    0x5d, 0xa, 0x0, 0x0, 0x0, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+65F6 "时" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0x0, 0x85, 0x5a, 0x20,
    0x0, 0xc, 0x0, 0xc, 0x0, 0xc0, 0x0, 0x0,
    0xc0, 0x50, 0xc0, 0xc, 0x46, 0x55, 0x5d, 0x57,
    0x1c, 0x0, 0xc0, 0x0, 0x0, 0xc0, 0x0, 0xc5,
    0x5d, 0x7, 0x40, 0xc, 0x0, 0xc, 0x0, 0xc0,
    0xe, 0x10, 0xc0, 0x0, 0xc0, 0xc, 0x0, 0x60,
    0xc, 0x0, 0xc, 0x0, 0xc0, 0x0, 0x0, 0xc0,
    0x0, 0xc5, 0x5d, 0x0, 0x0, 0xc, 0x0, 0x9,
    0x0, 0x30, 0x0, 0x11, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0,

    /* U+6700 "最" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x6, 0x85, 0x55, 0x55, 0xd2, 0x0, 0x0, 0x6,
    0x95, 0x55, 0x55, 0xd0, 0x0, 0x0, 0x6, 0x60,
    0x0, 0x0, 0xc0, 0x0, 0x0, 0x6, 0x95, 0x55,
    0x55, 0xd0, 0x0, 0x4, 0x56, 0x55, 0x55, 0x55,
    0x58, 0xd1, 0x0, 0x29, 0x0, 0xb0, 0x0, 0x3,
    0x0, 0x0, 0x2b, 0x55, 0xc4, 0x85, 0x5d, 0x30,
    0x0, 0x2b, 0x55, 0xc0, 0x50, 0x39, 0x0, 0x0,
    0x29, 0x0, 0xb0, 0x28, 0xb1, 0x0, 0x0, 0x2b,
    0x67, 0xd5, 0x1a, 0x90, 0x0, 0xa, 0xc7, 0x30,
    0xb0, 0x56, 0x99, 0x10, 0x0, 0x0, 0x0, 0xb5,
    0x30, 0x6, 0xb2, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0,

    /* U+6708 "月" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa5,
    0x55, 0x55, 0xc1, 0x0, 0xc, 0x0, 0x0, 0xc,
    0x0, 0x0, 0xc0, 0x0, 0x0, 0xc0, 0x0, 0xd,
    0x55, 0x55, 0x5c, 0x0, 0x0, 0xc0, 0x0, 0x0,
    0xc0, 0x0, 0xc, 0x0, 0x0, 0xc, 0x0, 0x0,
    0xd5, 0x55, 0x55, 0xc0, 0x0, 0x2a, 0x0, 0x0,
    0xc, 0x0, 0x5, 0x70, 0x0, 0x0, 0xc0, 0x0,
    0xa1, 0x0, 0x0, 0xc, 0x0, 0x37, 0x0, 0x1,
    0x11, 0xd0, 0x26, 0x0, 0x0, 0x5, 0xe9, 0x2,
    0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+6A19 "標" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0x3, 0x65, 0x75, 0x75, 0xa1, 0x0, 0xa,
    0x0, 0x0, 0x90, 0x90, 0x0, 0x5, 0x5c, 0x86,
    0xa5, 0xb5, 0xb5, 0xa0, 0x0, 0x3b, 0x0, 0xa0,
    0x90, 0x92, 0x80, 0x0, 0x7d, 0x60, 0xc5, 0xb5,
    0xb6, 0x80, 0x0, 0xaa, 0x94, 0x70, 0x0, 0x2,
    0x20, 0x3, 0x5a, 0x10, 0x55, 0x55, 0x58, 0x20,
    0x6, 0xa, 0x4, 0x55, 0x55, 0x55, 0x85, 0x12,
    0xa, 0x1, 0x23, 0xb, 0x10, 0x0, 0x0, 0xb,
    0x0, 0xb5, 0xb, 0x38, 0x0, 0x0, 0xb, 0x8,
    0x20, 0xb, 0x4, 0xb0, 0x0, 0xb, 0x40, 0x5,
    0xb8, 0x0, 0x40, 0x0, 0x1, 0x0, 0x0, 0x10,
    0x0, 0x0,

    /* U+6C60 "池" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa2, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x0, 0x49,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x31,
    0xa0, 0xb0, 0x2, 0x0, 0x9, 0x10, 0x41, 0xa0,
    0xb4, 0x4c, 0x30, 0x3, 0xb1, 0x44, 0xb4, 0xc0,
    0xa, 0x0, 0x0, 0x26, 0x22, 0xa0, 0xb0, 0xb,
    0x0, 0x0, 0x7, 0x1, 0xa0, 0xb0, 0xb, 0x0,
    0x0, 0x35, 0x1, 0xa0, 0xb2, 0x4c, 0x0, 0x6,
    0xe1, 0x1, 0xa0, 0xb0, 0x76, 0x20, 0x0, 0xd0,
    0x1, 0xa0, 0x70, 0x0, 0x60, 0x0, 0xe0, 0x1,
    0xb0, 0x0, 0x0, 0xb4, 0x0, 0xa0, 0x0, 0x8a,
    0xaa, 0xaa, 0x91,

    /* U+706F "灯" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0xc,
    0x1, 0x65, 0x59, 0x57, 0x80, 0x0, 0xc, 0x1a,
    0x0, 0xd, 0x0, 0x0, 0x5, 0xc, 0x83, 0x0,
    0xd, 0x0, 0x0, 0xa, 0xc, 0x10, 0x0, 0xd,
    0x0, 0x0, 0x37, 0xb, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x0, 0xb, 0x0, 0x0, 0xd, 0x0, 0x0,
    0x0, 0x1c, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0,
    0x37, 0x92, 0x0, 0xd, 0x0, 0x0, 0x0, 0x81,
    0x2a, 0x0, 0xd, 0x0, 0x0, 0x2, 0x60, 0x2,
    0x0, 0xd, 0x0, 0x0, 0x6, 0x0, 0x0, 0x6,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0,

    /* U+7167 "照" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x75, 0x5a, 0x26, 0x95, 0x5b, 0x40, 0x1, 0xa0,
    0xb, 0x0, 0xc0, 0xb, 0x10, 0x1, 0xa0, 0xb,
    0x5, 0x61, 0x1c, 0x0, 0x1, 0xb3, 0x3b, 0xa,
    0x2, 0xc5, 0x0, 0x1, 0xa2, 0x2b, 0x67, 0x55,
    0x59, 0x10, 0x1, 0xa0, 0xb, 0xc, 0x0, 0xc,
    0x0, 0x1, 0xa0, 0xb, 0xc, 0x0, 0xc, 0x0,
    0x1, 0xb5, 0x5b, 0xc, 0x55, 0x5c, 0x0, 0x1,
    0x50, 0x2, 0x1, 0x0, 0x10, 0x0, 0x0, 0x70,
    0x45, 0x3, 0x70, 0x1a, 0x0, 0x1, 0xd0, 0xe,
    0x0, 0xc3, 0x7, 0xa0, 0x8, 0x70, 0x7, 0x0,
    0x51, 0x1, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+7259 "牙" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x55, 0x55, 0x55, 0x55, 0x5c, 0x70, 0x0, 0x13,
    0x10, 0x0, 0xc0, 0x0, 0x0, 0x0, 0xa, 0x50,
    0x0, 0xc0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0xc0, 0x0, 0x30, 0x0, 0x99, 0x55, 0x59, 0xd5,
    0x58, 0xa1, 0x0, 0x0, 0x0, 0x4b, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc1, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0x40, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xa4, 0x0, 0xc0, 0x0, 0x0, 0x0, 0x9,
    0x30, 0x0, 0xc0, 0x0, 0x0, 0x3, 0x71, 0x1,
    0x12, 0xc0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x5e,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0,

    /* U+7535 "电" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc0, 0x0,
    0x0, 0x0, 0xa5, 0x55, 0xd5, 0x55, 0xb4, 0x0,
    0xc0, 0x0, 0xc0, 0x0, 0xc0, 0x0, 0xc0, 0x0,
    0xc0, 0x0, 0xc0, 0x0, 0xc5, 0x55, 0xd5, 0x55,
    0xd0, 0x0, 0xc0, 0x0, 0xc0, 0x0, 0xc0, 0x0,
    0xc0, 0x0, 0xc0, 0x0, 0xc0, 0x0, 0xd5, 0x55,
    0xd5, 0x55, 0xa0, 0x10, 0x30, 0x0, 0xc0, 0x0,
    0x0, 0x50, 0x0, 0x0, 0xc0, 0x0, 0x0, 0xa0,
    0x0, 0x0, 0x8b, 0xaa, 0xab, 0xd0,

    /* U+7684 "的" */
    0x0, 0x11, 0x0, 0x1, 0x10, 0x0, 0x0, 0x5,
    0x90, 0x0, 0x6c, 0x0, 0x0, 0x0, 0x70, 0x0,
    0xb, 0x30, 0x0, 0x1, 0xa7, 0x5b, 0x42, 0xd5,
    0x56, 0xb0, 0x1b, 0x0, 0xb0, 0x92, 0x0, 0x3a,
    0x0, 0xb0, 0xb, 0x35, 0x0, 0x3, 0x90, 0xb,
    0x0, 0xb2, 0x25, 0x0, 0x39, 0x0, 0xc5, 0x5c,
    0x0, 0xa3, 0x4, 0x90, 0xb, 0x0, 0xb0, 0x5,
    0x40, 0x48, 0x0, 0xb0, 0xb, 0x0, 0x0, 0x5,
    0x70, 0xb, 0x0, 0xb0, 0x0, 0x0, 0x66, 0x1,
    0xc5, 0x5c, 0x0, 0x14, 0x2b, 0x40, 0x19, 0x0,
    0x70, 0x0, 0x2d, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+79BB "离" */
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4b, 0x0, 0x0, 0x30, 0x6, 0x55,
    0x55, 0x56, 0x55, 0x56, 0x70, 0x0, 0xb, 0x24,
    0x33, 0xb0, 0xa1, 0x0, 0x0, 0xb, 0x0, 0x8d,
    0x50, 0xb0, 0x0, 0x0, 0xb, 0x25, 0x20, 0xa4,
    0xc0, 0x0, 0x0, 0xc, 0x55, 0x66, 0x55, 0xc0,
    0x0, 0x0, 0x20, 0x0, 0x87, 0x0, 0x23, 0x0,
    0x0, 0xd5, 0x57, 0xa5, 0x55, 0x5b, 0x30, 0x0,
    0xb0, 0x8, 0x1, 0x82, 0x9, 0x10, 0x0, 0xb0,
    0xca, 0x75, 0x6d, 0x9, 0x10, 0x0, 0xb0, 0x20,
    0x0, 0x3, 0x9, 0x10, 0x0, 0xc0, 0x0, 0x0,
    0x3, 0xad, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x1, 0x0,

    /* U+7B80 "简" */
    0x0, 0x2, 0x0, 0x0, 0x20, 0x0, 0x0, 0x2,
    0xc0, 0x2, 0x1c, 0x10, 0x1, 0x0, 0xa6, 0x95,
    0x79, 0x78, 0x56, 0x60, 0x52, 0x9, 0x23, 0x30,
    0x46, 0x0, 0x1, 0x8, 0x51, 0x65, 0x55, 0x59,
    0x10, 0xb, 0x29, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0xb0, 0x9, 0x55, 0xb1, 0xb, 0x0, 0xb, 0x0,
    0xb0, 0xb, 0x0, 0xb0, 0x0, 0xb0, 0xc, 0x55,
    0xb0, 0xb, 0x0, 0xb, 0x0, 0xb0, 0xb, 0x0,
    0xb0, 0x0, 0xb0, 0xc, 0x55, 0xc0, 0xb, 0x0,
    0xc, 0x0, 0x60, 0x3, 0x0, 0xb0, 0x0, 0xc0,
    0x0, 0x0, 0x5, 0xbb, 0x0, 0x3, 0x0, 0x0,
    0x0, 0x1, 0x0,

    /* U+7EDF "统" */
    0x0, 0x22, 0x0, 0x4, 0x0, 0x0, 0x0, 0x9,
    0x70, 0x0, 0x2c, 0x0, 0x0, 0x1, 0xb0, 0x5,
    0x55, 0xa5, 0x6c, 0x10, 0x81, 0xa, 0x20, 0x77,
    0x0, 0x0, 0x57, 0x39, 0x70, 0x19, 0x2, 0x0,
    0x7, 0x65, 0x80, 0x8, 0x0, 0x3a, 0x20, 0x1,
    0x90, 0xb, 0xaa, 0x6a, 0x7c, 0x1, 0xa2, 0x34,
    0x34, 0xa0, 0xb0, 0x30, 0x6d, 0x72, 0x0, 0x39,
    0xb, 0x0, 0x0, 0x0, 0x0, 0x5, 0x70, 0xb0,
    0x0, 0x14, 0x76, 0x40, 0x93, 0xb, 0x4, 0x8,
    0x81, 0x0, 0x3a, 0x0, 0xb0, 0x61, 0x0, 0x0,
    0x57, 0x0, 0xb, 0xbc, 0x40, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0,

    /* U+7F6E "置" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0xb6, 0x6b, 0x66, 0xa6, 0x6e, 0x0, 0x0, 0xc0,
    0xc, 0x0, 0xb0, 0xd, 0x0, 0x0, 0xc6, 0x6b,
    0x76, 0xa6, 0x6d, 0x0, 0x0, 0x20, 0x0, 0xa3,
    0x0, 0x7, 0x0, 0x2, 0x76, 0x66, 0xd6, 0x66,
    0x66, 0x10, 0x0, 0xa, 0x66, 0x96, 0x69, 0xa0,
    0x0, 0x0, 0xb, 0x10, 0x0, 0x4, 0x80, 0x0,
    0x0, 0xb, 0x66, 0x66, 0x68, 0x80, 0x0, 0x0,
    0xb, 0x76, 0x66, 0x69, 0x80, 0x0, 0x0, 0xb,
    0x10, 0x0, 0x4, 0x80, 0x0, 0x0, 0xb, 0x76,
    0x66, 0x69, 0x80, 0x0, 0x26, 0x6d, 0x76, 0x66,
    0x69, 0xb7, 0xd2, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x30, 0x0, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xb1, 0x0, 0xe2, 0x2, 0x20, 0x6, 0x55,
    0xc5, 0x55, 0xe5, 0x58, 0x80, 0x0, 0x0, 0x70,
    0x3, 0x70, 0x0, 0x0, 0x0, 0x51, 0x83, 0xc,
    0x30, 0x26, 0x0, 0x0, 0xb1, 0x92, 0x2a, 0x55,
    0x55, 0x0, 0x0, 0xb0, 0x92, 0x71, 0x39, 0x30,
    0x0, 0x0, 0xb0, 0x94, 0x30, 0x1, 0xd0, 0x0,
    0x0, 0x21, 0x40, 0x0, 0x0, 0x60, 0x0, 0x0,
    0x2b, 0x5c, 0x5c, 0x65, 0xd0, 0x0, 0x0, 0x1a,
    0xb, 0xa, 0x10, 0xc0, 0x0, 0x0, 0x1a, 0xb,
    0xa, 0x10, 0xc0, 0x30, 0x16, 0x57, 0x58, 0x58,
    0x55, 0x86, 0x90,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x35, 0x0, 0x5, 0x20, 0x65, 0x55, 0x55, 0x55,
    0x55, 0x54, 0x0, 0x0, 0x0, 0x0, 0x2, 0x70,
    0x0, 0x0, 0x65, 0x55, 0x55, 0x55, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x65,
    0x55, 0x55, 0x56, 0x10, 0x0, 0x3, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x0, 0xc5, 0x55, 0x55, 0x6c,
    0x0, 0x0, 0xc, 0x0, 0x0, 0x2, 0xa0, 0x0,
    0x0, 0xc5, 0x55, 0x55, 0x7a, 0x0, 0x0, 0xc,
    0x0, 0x0, 0x2, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+8A9E "語" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x74, 0x0, 0x22, 0x22, 0x28, 0x0, 0x0, 0x1a,
    0x13, 0x33, 0xb3, 0x33, 0x0, 0x16, 0x55, 0x54,
    0x12, 0xb2, 0x52, 0x0, 0x3, 0x55, 0x80, 0x3,
    0xa2, 0xa3, 0x0, 0x1, 0x0, 0x0, 0x1, 0x80,
    0xa2, 0x0, 0x0, 0x0, 0x41, 0x35, 0x93, 0xc5,
    0xa0, 0x4, 0x55, 0x51, 0x42, 0x22, 0x22, 0x20,
    0x6, 0x55, 0x81, 0x1a, 0x44, 0x4d, 0x0, 0xb,
    0x0, 0xb0, 0xb, 0x0, 0xc, 0x0, 0xb, 0x0,
    0xb0, 0xb, 0x0, 0xc, 0x0, 0xb, 0x55, 0xc0,
    0xd, 0x55, 0x5c, 0x0, 0xa, 0x0, 0x80, 0x1a,
    0x0, 0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+8BA1 "计" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x46, 0x0, 0x0, 0xb3, 0x0, 0x0, 0x0, 0xb,
    0x40, 0x0, 0xb2, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb2, 0x0, 0x0, 0x16, 0x5c, 0x16, 0x55, 0xc6,
    0x56, 0xb0, 0x0, 0xb, 0x0, 0x0, 0xb2, 0x0,
    0x0, 0x0, 0xb, 0x0, 0x0, 0xb2, 0x0, 0x0,
    0x0, 0xb, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x0,
    0xb, 0x3, 0x0, 0xb2, 0x0, 0x0, 0x0, 0xb,
    0x82, 0x0, 0xb2, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0xb2, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0,

    /* U+8BBE "设" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x65, 0x0, 0x86, 0x55, 0xd0, 0x0, 0x0, 0xd,
    0x0, 0x92, 0x0, 0xd0, 0x0, 0x0, 0x1, 0x0,
    0xb1, 0x0, 0xd0, 0x0, 0x0, 0x1, 0x0, 0xb0,
    0x0, 0xe6, 0x71, 0x6, 0x5e, 0x18, 0x20, 0x0,
    0x36, 0x51, 0x0, 0xc, 0x43, 0x75, 0x55, 0x8b,
    0x0, 0x0, 0xc, 0x0, 0x13, 0x0, 0xa4, 0x0,
    0x0, 0xc, 0x1, 0x8, 0x2, 0xc0, 0x0, 0x0,
    0xc, 0x24, 0x4, 0x6b, 0x30, 0x0, 0x0, 0xe,
    0x80, 0x0, 0xdc, 0x0, 0x0, 0x0, 0x1c, 0x0,
    0x2b, 0x56, 0xc6, 0x10, 0x0, 0x0, 0x37, 0x70,
    0x0, 0x2a, 0xc2, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+8BED "语" */
    0x3, 0x50, 0x5, 0x55, 0x55, 0x6b, 0x0, 0x0,
    0xc1, 0x1, 0xb, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0xb, 0x0, 0x50, 0x0, 0x0, 0x0, 0x3,
    0x5c, 0x55, 0xd0, 0x0, 0x35, 0xa3, 0x0, 0x19,
    0x0, 0xc0, 0x0, 0x0, 0xa1, 0x55, 0x79, 0x55,
    0xb6, 0xa0, 0x0, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa1, 0x1, 0x95, 0x55, 0x78, 0x0,
    0x0, 0xa1, 0x31, 0xa0, 0x0, 0x47, 0x0, 0x0,
    0xa9, 0x31, 0xa0, 0x0, 0x47, 0x0, 0x0, 0xa4,
    0x1, 0xc5, 0x55, 0x88, 0x0, 0x0, 0x0, 0x1,
    0x90, 0x0, 0x35, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+8DDD "距" */
    0x5, 0x55, 0x59, 0x7, 0x55, 0x59, 0x40, 0x7,
    0x30, 0xa, 0xb, 0x0, 0x0, 0x0, 0x7, 0x30,
    0xa, 0xb, 0x0, 0x0, 0x0, 0x7, 0x30, 0xa,
    0xc, 0x33, 0x37, 0x0, 0x7, 0x7b, 0x57, 0xb,
    0x11, 0x1c, 0x0, 0x1, 0xa, 0x0, 0xb, 0x0,
    0xb, 0x0, 0xa, 0x1a, 0x59, 0xb, 0x0, 0xb,
    0x0, 0xa, 0xa, 0x0, 0xc, 0x55, 0x5c, 0x0,
    0xa, 0xa, 0x0, 0xb, 0x0, 0x2, 0x0, 0xa,
    0xa, 0x55, 0x1b, 0x0, 0x0, 0x0, 0x2c, 0xa8,
    0x20, 0xb, 0x0, 0x1, 0x50, 0x37, 0x0, 0x0,
    0x29, 0x55, 0x55, 0x50,

    /* U+901F "速" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x0, 0xc2,
    0x35, 0x55, 0xd5, 0x57, 0x90, 0x0, 0x63, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x55,
    0xc5, 0x5c, 0x0, 0x3, 0x38, 0x9, 0x10, 0xb0,
    0xb, 0x0, 0x3, 0x1b, 0x9, 0x65, 0xc5, 0x5b,
    0x0, 0x0, 0xb, 0x6, 0x6, 0xf0, 0x5, 0x0,
    0x0, 0xb, 0x0, 0x39, 0xb6, 0x71, 0x0, 0x0,
    0xb, 0x2, 0x80, 0xb0, 0x3d, 0x30, 0x0, 0x4b,
    0x33, 0x0, 0xc0, 0x2, 0x40, 0xa, 0x60, 0x84,
    0x0, 0x90, 0x0, 0x0, 0x4, 0x0, 0x5, 0xab,
    0xcd, 0xde, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0xb, 0x55, 0x55, 0x55, 0xd0, 0x0, 0x0, 0xb,
    0x55, 0x55, 0x55, 0xb0, 0x0, 0x0, 0xb, 0x55,
    0x55, 0x55, 0xb0, 0x0, 0x0, 0x8, 0x0, 0x0,
    0x0, 0x40, 0x50, 0x17, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x72, 0x0, 0xa, 0x55, 0x57, 0x55, 0xb0,
    0x0, 0x0, 0xb, 0x0, 0x19, 0x0, 0xb0, 0x0,
    0x0, 0xc, 0x55, 0x6b, 0x55, 0xc0, 0x0, 0x0,
    0xd, 0x55, 0x6b, 0x55, 0xb0, 0x0, 0x0, 0x2,
    0x0, 0x19, 0x0, 0x34, 0x0, 0x0, 0x56, 0x55,
    0x6b, 0x55, 0x54, 0x0, 0x5, 0x55, 0x55, 0x6b,
    0x55, 0x56, 0xc1, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+95F4 "间" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x10, 0x0, 0x0, 0x0, 0x40, 0x15, 0x76, 0x16,
    0x55, 0x55, 0x6c, 0x1, 0xc0, 0x0, 0x0, 0x0,
    0x2, 0xa0, 0x1b, 0x0, 0xa5, 0x5b, 0x60, 0x2a,
    0x1, 0xb0, 0xb, 0x0, 0xb0, 0x2, 0xa0, 0x1b,
    0x0, 0xb0, 0xb, 0x0, 0x2a, 0x1, 0xb0, 0xc,
    0x55, 0xc0, 0x2, 0xa0, 0x1b, 0x0, 0xb0, 0xb,
    0x0, 0x2a, 0x1, 0xb0, 0xd, 0x55, 0xc0, 0x2,
    0xa0, 0x1b, 0x0, 0x70, 0x5, 0x0, 0x2a, 0x1,
    0xb0, 0x0, 0x0, 0x1, 0x23, 0xa0, 0x1a, 0x0,
    0x0, 0x0, 0x5, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+9650 "限" */
    0x75, 0x5a, 0x8, 0x55, 0x5a, 0x40, 0xa, 0x12,
    0x90, 0xb0, 0x0, 0xa1, 0x0, 0xa1, 0x62, 0xb,
    0x0, 0xa, 0x10, 0xa, 0x16, 0x0, 0xd5, 0x55,
    0xc1, 0x0, 0xa1, 0x50, 0xb, 0x0, 0xa, 0x20,
    0xa, 0x12, 0x50, 0xd6, 0x55, 0xc2, 0x0, 0xa1,
    0xa, 0xb, 0x14, 0x2, 0x90, 0xa, 0x10, 0xb0,
    0xb0, 0x71, 0xa5, 0x0, 0xa5, 0xbd, 0xb, 0x7,
    0x70, 0x0, 0xa, 0x14, 0x10, 0xb0, 0x1b, 0x20,
    0x0, 0xa1, 0x0, 0xd, 0x82, 0x2d, 0x93, 0xa,
    0x10, 0x1, 0xc1, 0x0, 0x1a, 0x40, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+97F3 "音" */
    0x0, 0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x65,
    0x55, 0x87, 0x55, 0x98, 0x0, 0x0, 0x0, 0x53,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x0,
    0x84, 0x0, 0x0, 0x0, 0x0, 0x6, 0x0, 0x70,
    0x5, 0x70, 0x26, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x50, 0x0, 0x7, 0x55, 0x55, 0x56, 0xa0, 0x0,
    0x0, 0x9, 0x20, 0x0, 0x2, 0x90, 0x0, 0x0,
    0x9, 0x65, 0x55, 0x56, 0x90, 0x0, 0x0, 0x9,
    0x20, 0x0, 0x2, 0x90, 0x0, 0x0, 0xa, 0x65,
    0x55, 0x56, 0xa0, 0x0, 0x0, 0x9, 0x20, 0x0,
    0x2, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+984C "題" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x75, 0x5b, 0x36, 0x55, 0x57, 0x90, 0x4, 0x50,
    0xa, 0x0, 0x9, 0x0, 0x0, 0x4, 0x85, 0x5a,
    0x7, 0x67, 0x5b, 0x0, 0x5, 0x51, 0x1a, 0xa,
    0x0, 0xa, 0x0, 0x4, 0x64, 0x46, 0xa, 0x55,
    0x5a, 0x0, 0x5, 0x55, 0x57, 0x7a, 0x55, 0x5a,
    0x0, 0x3, 0xa, 0x0, 0xa, 0x0, 0xa, 0x0,
    0x8, 0x3a, 0x3, 0x1a, 0x55, 0x58, 0x0, 0x9,
    0x1a, 0x55, 0x21, 0xc0, 0x44, 0x0, 0x9, 0x5a,
    0x0, 0x8, 0x20, 0xa, 0x40, 0x7, 0xb, 0x30,
    0x30, 0x0, 0x1, 0x20, 0x41, 0x0, 0x7b, 0xbb,
    0xcc, 0xde, 0x80, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+9AD8 "高" */
    0x0, 0x0, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa2, 0x0, 0x0, 0x10, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x69, 0x0, 0x3, 0x65, 0x55, 0x59,
    0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0x4, 0x95, 0x55, 0x5c, 0x0, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x50, 0x10, 0x0, 0xc5, 0x55,
    0x55, 0x55, 0x5d, 0x30, 0xb, 0x6, 0x55, 0x59,
    0x30, 0xc0, 0x0, 0xb0, 0x91, 0x0, 0x91, 0xc,
    0x0, 0xb, 0x9, 0x65, 0x5b, 0x20, 0xc0, 0x0,
    0xb0, 0x70, 0x0, 0x61, 0xc, 0x0, 0xb, 0x0,
    0x0, 0x0, 0x39, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+FE83 "ﺃ" */
    0xa, 0xa0, 0x38, 0x0, 0x2f, 0xc0, 0x1, 0x0,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90,

    /* U+FE87 "ﺇ" */
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xb, 0x90,
    0xb, 0x90, 0xb, 0x90, 0xb, 0x90, 0xa, 0xa0,
    0x38, 0x0, 0x2e, 0xb0, 0x0, 0x0,

    /* U+FE8B "ﺋ" */
    0x0, 0x9a, 0x0, 0x19, 0x0, 0x1, 0xec, 0x10,
    0x1, 0x0, 0x0, 0x22, 0x0, 0x9, 0xa0, 0x0,
    0x9a, 0x0, 0x5e, 0x70, 0x2f, 0xb0, 0x0,

    /* U+FE8D "ﺍ" */
    0xb9, 0xb9, 0xb9, 0xb9, 0xb9, 0xb9, 0xb9, 0xb9,
    0xb9, 0xb9, 0xb9,

    /* U+FE8E "ﺎ" */
    0xb9, 0x0, 0xb9, 0x0, 0xb9, 0x0, 0xb9, 0x0,
    0xb9, 0x0, 0xb9, 0x0, 0xb9, 0x0, 0xb9, 0x0,
    0xa9, 0x0, 0x7e, 0x51, 0x1b, 0xf6,

    /* U+FE90 "ﺐ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0x0, 0xe,
    0x50, 0x0, 0x0, 0x0, 0x1f, 0x20, 0x1f, 0x30,
    0x0, 0x0, 0x1, 0xbf, 0x30, 0xc, 0xe7, 0x44,
    0x57, 0xaf, 0xdd, 0xc4, 0x0, 0x8d, 0xff, 0xec,
    0x95, 0x2, 0xde, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+FE91 "ﺑ" */
    0x0, 0x9a, 0x0, 0x9a, 0x5, 0xe7, 0x2f, 0xc1,
    0x0, 0x0, 0x0, 0x68, 0x0, 0x0,

    /* U+FE92 "ﺒ" */
    0x0, 0x9a, 0x0, 0x0, 0x9b, 0x0, 0x5, 0xef,
    0x51, 0x2f, 0xbb, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x68, 0x0, 0x0, 0x0, 0x0,

    /* U+FE94 "ﺔ" */
    0x0, 0x86, 0xc1, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x4, 0xd6, 0x0, 0x3, 0xdb, 0xd8, 0x0,
    0xd, 0x70, 0x9b, 0x0, 0xe, 0xa7, 0xdf, 0x62,
    0x1, 0x77, 0x47, 0xfa,

    /* U+FE95 "ﺕ" */
    0x0, 0x0, 0x3b, 0x77, 0x0, 0x0, 0x0, 0x84,
    0x0, 0x10, 0x0, 0x2, 0xf0, 0xf, 0x30, 0x0,
    0x0, 0x0, 0x3f, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0x1c, 0xc0, 0xa, 0xe8, 0x43, 0x57, 0xbf, 0xc1,
    0x0, 0x6, 0xcf, 0xfe, 0xc8, 0x40, 0x0,

    /* U+FE97 "ﺗ" */
    0x4, 0xa8, 0x50, 0x1, 0x10, 0x0, 0x11, 0x0,
    0x9, 0xa0, 0x0, 0x9a, 0x0, 0x5e, 0x70, 0x2f,
    0xb1, 0x0,

    /* U+FE98 "ﺘ" */
    0x4, 0xa8, 0x50, 0x0, 0x11, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x9a, 0x0, 0x0, 0x9b, 0x0, 0x5,
    0xef, 0x51, 0x2f, 0xbb, 0xf5,

    /* U+FE99 "ﺙ" */
    0x0, 0x0, 0x5, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xc8, 0x70, 0x0, 0x0, 0x8, 0x40, 0x1,
    0x0, 0x0, 0x2f, 0x0, 0xf3, 0x0, 0x0, 0x0,
    0x3, 0xf0, 0xf, 0x40, 0x0, 0x0, 0x1, 0xcc,
    0x0, 0xae, 0x84, 0x35, 0x7b, 0xfc, 0x10, 0x0,
    0x6c, 0xff, 0xec, 0x84, 0x0, 0x0,

    /* U+FEA0 "ﺠ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe,
    0xc8, 0x30, 0x0, 0x0, 0x34, 0x68, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x3e, 0xf5, 0x0, 0x0, 0x0,
    0x3f, 0xef, 0x10, 0x0, 0x45, 0xaf, 0x90, 0xdd,
    0x50, 0x2f, 0xeb, 0x50, 0x2, 0xcf, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEA2 "ﺢ" */
    0x9d, 0xef, 0xed, 0x60, 0x8, 0x58, 0xff, 0xd4,
    0x0, 0x6, 0xf5, 0x2f, 0x0, 0x2, 0xf4, 0x0,
    0xb7, 0x0, 0x8c, 0x0, 0x3, 0xf7, 0x9, 0xa0,
    0x0, 0x5, 0xe2, 0x6d, 0x0, 0x0, 0x0, 0x0,
    0xeb, 0x30, 0x2, 0x70, 0x2, 0xaf, 0xff, 0xfb,
    0x0, 0x0, 0x1, 0x20, 0x0,

    /* U+FEA3 "ﺣ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0xc8,
    0x30, 0x0, 0x34, 0x69, 0xdf, 0x90, 0x0, 0x0,
    0x3d, 0xe5, 0x0, 0x0, 0x3f, 0xb1, 0x0, 0x45,
    0xaf, 0x90, 0x0, 0x2f, 0xea, 0x40, 0x0, 0x0,

    /* U+FEA4 "ﺤ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfe,
    0xc8, 0x30, 0x0, 0x0, 0x34, 0x68, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x3e, 0xf5, 0x0, 0x0, 0x0,
    0x3f, 0xef, 0x10, 0x0, 0x45, 0xaf, 0x90, 0xdd,
    0x50, 0x2f, 0xeb, 0x50, 0x2, 0xcf, 0x20,

    /* U+FEA9 "ﺩ" */
    0x0, 0x6e, 0x30, 0x0, 0x8, 0xd0, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0xe5, 0x15, 0x4b, 0xf2, 0x1e,
    0xfc, 0x40,

    /* U+FEAA "ﺪ" */
    0x0, 0x6e, 0x20, 0x0, 0x0, 0x8, 0xd0, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0xe7, 0x0,
    0x4, 0x4b, 0xfe, 0x52, 0x2f, 0xfc, 0x38, 0xf7,

    /* U+FEAD "ﺭ" */
    0x0, 0x0, 0x6, 0x80, 0x0, 0x0, 0x5e, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0xab, 0x0, 0x0,
    0x5f, 0x40, 0x25, 0xbf, 0x80, 0x9f, 0xf9, 0x30,
    0x1, 0x10, 0x0, 0x0,

    /* U+FEAE "ﺮ" */
    0x0, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x0, 0x5e,
    0x0, 0x0, 0x0, 0x6, 0xf9, 0x30, 0x0, 0x0,
    0xae, 0xed, 0x0, 0x0, 0x6f, 0x50, 0x0, 0x25,
    0xbf, 0x90, 0x0, 0x9f, 0xfa, 0x30, 0x0, 0x1,
    0x10, 0x0, 0x0, 0x0,

    /* U+FEB1 "ﺱ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0x0, 0x0, 0x0, 0x7, 0x20, 0x7, 0x30, 0x5e,
    0x0, 0x0, 0x0, 0xb, 0x70, 0xe, 0x60, 0x5e,
    0x3, 0x60, 0x0, 0x8, 0xc0, 0x1f, 0x90, 0x6d,
    0xc, 0x70, 0x0, 0x7, 0xfa, 0xbe, 0xf8, 0xd9,
    0xf, 0x30, 0x0, 0x9, 0xde, 0xe3, 0x9f, 0xb1,
    0x1f, 0x30, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0,
    0xe, 0xa2, 0x14, 0xdd, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+FEB2 "ﺲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x60, 0xe, 0x60,
    0x5e, 0x0, 0x3, 0x60, 0x0, 0x9, 0xa0, 0xf,
    0x70, 0x6f, 0x0, 0xc, 0x70, 0x0, 0x7, 0xf6,
    0x9f, 0xe5, 0xcf, 0x93, 0xf, 0x30, 0x0, 0x8,
    0xef, 0xe4, 0xaf, 0xc8, 0xef, 0x1f, 0x30, 0x0,
    0x1e, 0x80, 0x0, 0x0, 0x0, 0x0, 0xe, 0xa2,
    0x14, 0xce, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xef, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+FEB3 "ﺳ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb9, 0x0, 0x1f,
    0x20, 0x4f, 0x0, 0xb9, 0x0, 0x2f, 0x40, 0x6f,
    0x20, 0xb8, 0x4, 0xbf, 0xc5, 0xdf, 0xb5, 0xf4,
    0x2f, 0xd5, 0xcf, 0xb3, 0xdf, 0x80,

    /* U+FEB4 "ﺴ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x54, 0x0, 0x0,
    0x8, 0x10, 0x18, 0x0, 0xb9, 0x0, 0x0, 0x1f,
    0x20, 0x4f, 0x0, 0xb9, 0x0, 0x0, 0x4f, 0x50,
    0x7f, 0x30, 0xcb, 0x0, 0x17, 0xdf, 0xe8, 0xfe,
    0xd9, 0xff, 0x94, 0x2f, 0xd2, 0xbf, 0xb2, 0xcf,
    0x88, 0xfa,

    /* U+FEBC "ﺼ" */
    0x0, 0x0, 0x0, 0x8, 0xef, 0xb2, 0x0, 0x0,
    0x8, 0x12, 0xdd, 0x53, 0xcb, 0x0, 0x0, 0x2f,
    0x6e, 0xa0, 0x0, 0x9d, 0x0, 0x4, 0xbf, 0xfe,
    0x45, 0x7c, 0xfd, 0x51, 0x2f, 0xd4, 0xbf, 0xff,
    0xda, 0x5c, 0xf4,

    /* U+FEBF "ﺿ" */
    0x0, 0x0, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xef, 0xb2, 0x0, 0x8, 0x12, 0xdd, 0x53, 0xcb,
    0x0, 0x2f, 0x6e, 0xa0, 0x0, 0x9c, 0x4, 0xbf,
    0xfe, 0x45, 0x7c, 0xf5, 0x2f, 0xd4, 0xbf, 0xff,
    0xd9, 0x30,

    /* U+FEC0 "ﻀ" */
    0x0, 0x0, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xef, 0xb2, 0x0, 0x0, 0x8, 0x12,
    0xdd, 0x53, 0xcb, 0x0, 0x0, 0x2f, 0x6e, 0xa0,
    0x0, 0x9d, 0x0, 0x4, 0xbf, 0xfe, 0x45, 0x7c,
    0xfd, 0x51, 0x2f, 0xd4, 0xbf, 0xff, 0xda, 0x5c,
    0xf4,

    /* U+FEC2 "ﻂ" */
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xc0, 0x7, 0xdf, 0xc3,
    0x0, 0x0, 0x8, 0xc1, 0xce, 0x63, 0xad, 0x0,
    0x0, 0x8, 0xcc, 0xc1, 0x0, 0x7f, 0x0, 0x4,
    0x4a, 0xff, 0x54, 0x6b, 0xfe, 0x51, 0xf, 0xff,
    0xff, 0xff, 0xea, 0x5b, 0xf6,

    /* U+FEC4 "ﻄ" */
    0x0, 0x5f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x6c, 0xfd, 0x50, 0x0, 0x5, 0xf0,
    0xaf, 0x73, 0x8f, 0x10, 0x0, 0x5f, 0xae, 0x20,
    0x4, 0xf2, 0x0, 0x47, 0xff, 0x64, 0x6a, 0xff,
    0x62, 0x2f, 0xff, 0xff, 0xfe, 0xb5, 0x9f, 0xa0,

    /* U+FEC9 "ﻉ" */
    0x0, 0x3b, 0xe6, 0x0, 0x0, 0x2f, 0x84, 0x10,
    0x0, 0x8, 0xb0, 0x0, 0x10, 0x0, 0x7e, 0x69,
    0xef, 0x0, 0x0, 0xcf, 0xd7, 0x30, 0x0, 0x7f,
    0x60, 0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0x2,
    0xf1, 0x0, 0x0, 0x0, 0x1f, 0x20, 0x0, 0x0,
    0x0, 0xad, 0x40, 0x2, 0x72, 0x0, 0x9e, 0xff,
    0xfc, 0x10, 0x0, 0x1, 0x20, 0x0,

    /* U+FECA "ﻊ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xfd, 0x50,
    0x0, 0xb, 0xf4, 0xec, 0x0, 0x0, 0x3f, 0xec,
    0x20, 0x0, 0xb, 0xec, 0xe7, 0x42, 0x2, 0xf3,
    0x7, 0xdf, 0x90, 0x1f, 0x20, 0x0, 0x0, 0x0,
    0xcc, 0x30, 0x1, 0x62, 0x1, 0xaf, 0xff, 0xfc,
    0x10, 0x0, 0x1, 0x20, 0x0,

    /* U+FECB "ﻋ" */
    0x0, 0x4, 0xbe, 0x60, 0x0, 0x2f, 0x84, 0x10,
    0x0, 0x8b, 0x0, 0x0, 0x0, 0x7c, 0x0, 0x5,
    0x0, 0x1d, 0xba, 0xec, 0x4, 0x6c, 0xfb, 0x40,
    0x2f, 0xd8, 0x20, 0x0,

    /* U+FECC "ﻌ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xfd, 0x40,
    0x0, 0xdd, 0x4f, 0x90, 0x0, 0x3f, 0xed, 0x10,
    0x4, 0x7f, 0xed, 0x53, 0x2f, 0xd6, 0x8, 0xee,

    /* U+FECF "ﻏ" */
    0x0, 0x6, 0x80, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x4, 0xbe, 0x60, 0x0, 0x2f, 0x84, 0x10,
    0x0, 0x8b, 0x0, 0x0, 0x0, 0x7c, 0x0, 0x5,
    0x0, 0x1d, 0xba, 0xec, 0x4, 0x6c, 0xfb, 0x40,
    0x2f, 0xd8, 0x20, 0x0,

    /* U+FED0 "ﻐ" */
    0x0, 0x2, 0xc0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xfd, 0x40,
    0x0, 0xdd, 0x4f, 0x90, 0x0, 0x3f, 0xed, 0x10,
    0x4, 0x7f, 0xed, 0x53, 0x2f, 0xd6, 0x8, 0xee,

    /* U+FED3 "ﻓ" */
    0x0, 0x1, 0xd0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x2c, 0xfa, 0x0, 0xa, 0xc5, 0xe7, 0x0, 0xba,
    0xb, 0xa0, 0x4, 0xff, 0xfa, 0x0, 0x0, 0x1e,
    0x70, 0x44, 0x5b, 0xf1, 0x2f, 0xff, 0xc4, 0x0,

    /* U+FED8 "ﻘ" */
    0x0, 0xc, 0x4c, 0x0, 0x0, 0x0, 0x10, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xc1, 0x0, 0x0, 0x7e, 0x5e, 0x90, 0x0, 0x8,
    0xd0, 0xb9, 0x0, 0x4, 0x6f, 0xdf, 0x74, 0x2,
    0xff, 0xda, 0xdf, 0xf3,

    /* U+FEDB "ﻛ" */
    0x0, 0x0, 0x0, 0x25, 0x0, 0x0, 0x5b, 0xf8,
    0x0, 0x6e, 0xe8, 0x20, 0x4, 0xf6, 0x0, 0x0,
    0x4, 0xf2, 0x0, 0x0, 0x0, 0xad, 0x10, 0x0,
    0x0, 0xd, 0xb0, 0x0, 0x0, 0x2, 0xe6, 0x0,
    0x0, 0x0, 0xaa, 0x0, 0x4, 0x46, 0xf7, 0x0,
    0x2f, 0xff, 0xa0, 0x0,

    /* U+FEDC "ﻜ" */
    0x0, 0x0, 0x0, 0x25, 0x0, 0x0, 0x5, 0xbf,
    0x80, 0x0, 0x6e, 0xe8, 0x20, 0x0, 0x4f, 0x60,
    0x0, 0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0xa,
    0xd1, 0x0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0,
    0x0, 0x2e, 0x90, 0x0, 0x0, 0x0, 0xaf, 0x60,
    0x0, 0x44, 0x6f, 0xcf, 0x73, 0x2f, 0xff, 0xa0,
    0x7e, 0xe0,

    /* U+FEDE "ﻞ" */
    0x0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x5e, 0x0, 0x1, 0x10, 0x0, 0x6,
    0xf0, 0x0, 0xd6, 0x0, 0x0, 0x8f, 0x73, 0xf,
    0x40, 0x0, 0xd, 0xdf, 0xb0, 0xcb, 0x21, 0x5c,
    0xd1, 0x0, 0x2, 0xdf, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0, 0x0,

    /* U+FEDF "ﻟ" */
    0x0, 0x5f, 0x0, 0x5f, 0x0, 0x5f, 0x0, 0x5f,
    0x0, 0x5f, 0x0, 0x5f, 0x0, 0x5f, 0x0, 0x5f,
    0x0, 0x5e, 0x4, 0xcc, 0x2f, 0xd3,

    /* U+FEE0 "ﻠ" */
    0x0, 0x5f, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x5f,
    0x0, 0x0, 0x5f, 0x0, 0x0, 0x5f, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x5f, 0x0,
    0x0, 0x5f, 0x0, 0x4, 0xcf, 0x83, 0x2f, 0xd9,
    0xec,

    /* U+FEE1 "ﻡ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x0, 0x1f, 0x95, 0xe8, 0x1, 0x9f, 0x40, 0xc9,
    0xb, 0xeb, 0xff, 0xe3, 0xf, 0x50, 0x12, 0x0,
    0xf, 0x50, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0,
    0xf, 0x50, 0x0, 0x0,

    /* U+FEE2 "ﻢ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0x70, 0x0, 0x0, 0xc, 0xa5, 0xf4, 0x0, 0x0,
    0x1f, 0x20, 0xba, 0x0, 0x0, 0x6f, 0x84, 0xef,
    0x72, 0x9, 0xc8, 0xdf, 0xed, 0xf7, 0xf, 0x50,
    0x0, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0x0,
    0xf, 0x50, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,
    0x0, 0x0,

    /* U+FEE3 "ﻣ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x80,
    0x0, 0x1f, 0x77, 0xf3, 0x0, 0x4e, 0x0, 0xf5,
    0x4, 0xcf, 0x66, 0xf3, 0x2f, 0xdb, 0xef, 0x80,

    /* U+FEE4 "ﻤ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x50, 0x0, 0x0, 0x1f, 0x88, 0xf2, 0x0, 0x0,
    0x4e, 0x0, 0xf6, 0x0, 0x4, 0xcf, 0x65, 0xfd,
    0x40, 0x2f, 0xdb, 0xef, 0xce, 0xf3,

    /* U+FEE6 "ﻦ" */
    0x0, 0xd, 0x0, 0x10, 0x0, 0x0, 0x1, 0x0,
    0x7b, 0x0, 0x21, 0x0, 0x0, 0x2f, 0x20, 0xd6,
    0x0, 0x0, 0x1f, 0xb3, 0xf4, 0x0, 0x0, 0x3f,
    0xec, 0xe5, 0x0, 0x0, 0x8e, 0x0, 0x9d, 0x41,
    0x27, 0xf6, 0x0, 0x9, 0xff, 0xfd, 0x50, 0x0,
    0x0, 0x2, 0x20, 0x0, 0x0,

    /* U+FEE8 "ﻨ" */
    0x0, 0x68, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x9a, 0x0, 0x0, 0x9b, 0x0, 0x5,
    0xef, 0x51, 0x2f, 0xbb, 0xf5,

    /* U+FEEE "ﻮ" */
    0x0, 0x3, 0x72, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0xca, 0xd, 0x80, 0x0, 0xa, 0xd5,
    0xbb, 0x41, 0x0, 0x1b, 0xff, 0xff, 0x60, 0x0,
    0x1, 0xe6, 0x0, 0x1, 0x26, 0xeb, 0x0, 0x9,
    0xff, 0xe7, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0,
    0x0,

    /* U+FEEF "ﻯ" */
    0x0, 0x0, 0x4, 0xce, 0xc3, 0x0, 0x0, 0x0,
    0xf8, 0x28, 0xc0, 0x0, 0x0, 0xe, 0xb4, 0x0,
    0x0, 0x74, 0x0, 0x2a, 0xfd, 0x10, 0xf, 0x30,
    0x0, 0x0, 0xd8, 0x0, 0xf4, 0x0, 0x0, 0x5f,
    0x60, 0x8, 0xfa, 0x9b, 0xef, 0x80, 0x0, 0x4,
    0x99, 0x85, 0x0, 0x0,

    /* U+FEF0 "ﻰ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xed, 0x10, 0xe, 0x50, 0x0, 0xc,
    0xaa, 0xa0, 0x1f, 0x30, 0x0, 0xc, 0xe3, 0xf6,
    0xf, 0x40, 0x0, 0x2, 0xf5, 0x5c, 0xb, 0xd4,
    0x1, 0x28, 0xf2, 0x0, 0x1, 0xaf, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0x0, 0x22, 0x10, 0x0, 0x0,

    /* U+FEF2 "ﻲ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xed, 0x10, 0xe, 0x50, 0x0, 0xc,
    0xaa, 0xa0, 0x1f, 0x30, 0x0, 0xc, 0xe3, 0xf6,
    0xf, 0x40, 0x0, 0x2, 0xf5, 0x5c, 0xa, 0xd4,
    0x1, 0x28, 0xe1, 0x0, 0x0, 0x8e, 0xff, 0xe9,
    0x10, 0x0, 0x0, 0x8, 0x7d, 0x20, 0x0, 0x0,
    0x0, 0x1, 0x1, 0x0, 0x0, 0x0,

    /* U+FEF3 "ﻳ" */
    0x0, 0x9a, 0x0, 0x9, 0xa0, 0x5, 0xe7, 0x2,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x4a, 0x85, 0x0,
    0x10, 0x0,

    /* U+FEF4 "ﻴ" */
    0x0, 0x9a, 0x0, 0x0, 0x9b, 0x0, 0x5, 0xef,
    0x51, 0x2f, 0xbb, 0xf5, 0x0, 0x0, 0x0, 0x4,
    0xa8, 0x50, 0x0, 0x10, 0x0,

    /* U+FEF7 "ﻷ" */
    0x9, 0xa0, 0x0, 0x0, 0x29, 0x0, 0x0, 0x0,
    0x1e, 0xc1, 0x0, 0x0, 0x1, 0x0, 0x0, 0xb9,
    0x3, 0xe0, 0x0, 0xb9, 0x0, 0xc7, 0x0, 0xb9,
    0x0, 0x5d, 0x0, 0xb9, 0x0, 0xe, 0x40, 0xb9,
    0x0, 0x7, 0xb0, 0xb8, 0x0, 0x1, 0xf2, 0xd6,
    0x0, 0x0, 0x9c, 0xf1, 0x0, 0x0, 0x3f, 0xa0,
    0x0, 0x65, 0xdd, 0x0, 0x0, 0xef, 0x90, 0x0,

    /* U+FEF9 "ﻹ" */
    0x0, 0x0, 0xb, 0x93, 0xe0, 0x0, 0xb9, 0xc,
    0x70, 0xb, 0x90, 0x5d, 0x0, 0xb9, 0x0, 0xe4,
    0xb, 0x90, 0x7, 0xb0, 0xb8, 0x0, 0x1f, 0x2d,
    0x60, 0x0, 0x9c, 0xf1, 0x0, 0x3, 0xfa, 0x0,
    0x65, 0xdd, 0x0, 0xe, 0xf9, 0x0, 0x6, 0xa3,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0xb, 0xd5, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 56, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 69, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17, .adv_w = 89, .box_w = 5, .box_h = 4, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 27, .adv_w = 144, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 77, .adv_w = 145, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 145, .adv_w = 189, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 200, .adv_w = 148, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 255, .adv_w = 48, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 261, .adv_w = 88, .box_w = 5, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 296, .adv_w = 88, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 331, .adv_w = 122, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 349, .adv_w = 151, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 377, .adv_w = 66, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 387, .adv_w = 104, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 393, .adv_w = 65, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 399, .adv_w = 85, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 438, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 488, .adv_w = 108, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 521, .adv_w = 139, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 565, .adv_w = 146, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 615, .adv_w = 149, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 665, .adv_w = 142, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 715, .adv_w = 145, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 765, .adv_w = 131, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 809, .adv_w = 145, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 859, .adv_w = 145, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 909, .adv_w = 65, .box_w = 4, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 925, .adv_w = 66, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 947, .adv_w = 151, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 983, .adv_w = 151, .box_w = 8, .box_h = 5, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1003, .adv_w = 151, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1039, .adv_w = 122, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1078, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1176, .adv_w = 162, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1231, .adv_w = 147, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1275, .adv_w = 167, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1330, .adv_w = 162, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1380, .adv_w = 136, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1419, .adv_w = 131, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1458, .adv_w = 169, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1513, .adv_w = 167, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1563, .adv_w = 62, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1574, .adv_w = 126, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1613, .adv_w = 152, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1663, .adv_w = 127, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1702, .adv_w = 203, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1763, .adv_w = 166, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1813, .adv_w = 174, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1874, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1918, .adv_w = 174, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1984, .adv_w = 146, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2028, .adv_w = 145, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2078, .adv_w = 148, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2128, .adv_w = 164, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2178, .adv_w = 162, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2233, .adv_w = 226, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2310, .adv_w = 155, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2365, .adv_w = 158, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2420, .adv_w = 146, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2470, .adv_w = 88, .box_w = 5, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2505, .adv_w = 85, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2544, .adv_w = 88, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2572, .adv_w = 108, .box_w = 7, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 2590, .adv_w = 105, .box_w = 8, .box_h = 3, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 2602, .adv_w = 111, .box_w = 3, .box_h = 3, .ofs_x = 2, .ofs_y = 9},
    {.bitmap_index = 2607, .adv_w = 129, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2639, .adv_w = 141, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2683, .adv_w = 129, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2715, .adv_w = 141, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2759, .adv_w = 133, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2791, .adv_w = 84, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2827, .adv_w = 140, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2871, .adv_w = 137, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2915, .adv_w = 58, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2933, .adv_w = 58, .box_w = 4, .box_h = 15, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2963, .adv_w = 127, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3007, .adv_w = 58, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3024, .adv_w = 201, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3072, .adv_w = 137, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3104, .adv_w = 136, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3136, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3186, .adv_w = 140, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3230, .adv_w = 89, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3257, .adv_w = 123, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3289, .adv_w = 85, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3314, .adv_w = 136, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3346, .adv_w = 129, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3378, .adv_w = 188, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3426, .adv_w = 126, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3458, .adv_w = 129, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3502, .adv_w = 126, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3534, .adv_w = 88, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3576, .adv_w = 80, .box_w = 3, .box_h = 18, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3603, .adv_w = 88, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3645, .adv_w = 151, .box_w = 8, .box_h = 3, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 3657, .adv_w = 105, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3677, .adv_w = 62, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3705, .adv_w = 62, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3735, .adv_w = 62, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3746, .adv_w = 211, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3798, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3843, .adv_w = 108, .box_w = 7, .box_h = 8, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 3871, .adv_w = 273, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3951, .adv_w = 271, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4039, .adv_w = 207, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4105, .adv_w = 207, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4171, .adv_w = 134, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4225, .adv_w = 232, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4295, .adv_w = 174, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4360, .adv_w = 163, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4423, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4459, .adv_w = 165, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4504, .adv_w = 108, .box_w = 7, .box_h = 9, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 4536, .adv_w = 175, .box_w = 11, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4580, .adv_w = 175, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4635, .adv_w = 224, .box_w = 4, .box_h = 5, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4645, .adv_w = 224, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4729, .adv_w = 224, .box_w = 11, .box_h = 14, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 4806, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4904, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5002, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5100, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5198, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5296, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5394, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5492, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5590, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5688, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5786, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5884, .adv_w = 224, .box_w = 9, .box_h = 13, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 5943, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6034, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6132, .adv_w = 224, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6209, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6307, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6398, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6496, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6594, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6692, .adv_w = 224, .box_w = 12, .box_h = 13, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 6770, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6861, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6959, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7050, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7141, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7239, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7330, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7421, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7519, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7617, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7715, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7806, .adv_w = 224, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7890, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7988, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8086, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 8177, .adv_w = 224, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 8262, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8360, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8458, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 8549, .adv_w = 62, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8579, .adv_w = 62, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8609, .adv_w = 62, .box_w = 5, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8632, .adv_w = 62, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8643, .adv_w = 68, .box_w = 4, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8665, .adv_w = 220, .box_w = 14, .box_h = 8, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8721, .adv_w = 62, .box_w = 4, .box_h = 7, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8735, .adv_w = 68, .box_w = 6, .box_h = 7, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8756, .adv_w = 120, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8784, .adv_w = 211, .box_w = 13, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8823, .adv_w = 62, .box_w = 5, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8841, .adv_w = 68, .box_w = 6, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8862, .adv_w = 211, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8908, .adv_w = 145, .box_w = 11, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8963, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 9008, .adv_w = 138, .box_w = 9, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9040, .adv_w = 145, .box_w = 11, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9079, .adv_w = 100, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9097, .adv_w = 118, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9121, .adv_w = 108, .box_w = 7, .box_h = 8, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 9149, .adv_w = 124, .box_w = 9, .box_h = 8, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 9185, .adv_w = 273, .box_w = 16, .box_h = 10, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9265, .adv_w = 286, .box_w = 18, .box_h = 9, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9346, .adv_w = 188, .box_w = 12, .box_h = 5, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9376, .adv_w = 200, .box_w = 14, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9418, .adv_w = 194, .box_w = 14, .box_h = 5, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9453, .adv_w = 190, .box_w = 12, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9495, .adv_w = 194, .box_w = 14, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9544, .adv_w = 213, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9621, .adv_w = 184, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9693, .adv_w = 134, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9747, .adv_w = 119, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9792, .adv_w = 134, .box_w = 8, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9820, .adv_w = 108, .box_w = 8, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9844, .adv_w = 117, .box_w = 8, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9880, .adv_w = 108, .box_w = 8, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9912, .adv_w = 107, .box_w = 7, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9944, .adv_w = 113, .box_w = 9, .box_h = 8, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9980, .adv_w = 107, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10024, .adv_w = 124, .box_w = 9, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10074, .adv_w = 170, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10151, .adv_w = 68, .box_w = 4, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10173, .adv_w = 74, .box_w = 6, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10206, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10242, .adv_w = 149, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10292, .adv_w = 120, .box_w = 8, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10316, .adv_w = 130, .box_w = 10, .box_h = 6, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10346, .adv_w = 171, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 10391, .adv_w = 68, .box_w = 6, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10412, .adv_w = 116, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 10453, .adv_w = 175, .box_w = 11, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10497, .adv_w = 187, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10545, .adv_w = 187, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10599, .adv_w = 62, .box_w = 5, .box_h = 7, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10617, .adv_w = 68, .box_w = 6, .box_h = 7, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10638, .adv_w = 128, .box_w = 8, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10694, .adv_w = 128, .box_w = 7, .box_h = 15, .ofs_x = 0, .ofs_y = -4}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x2, 0x4, 0x6, 0x7, 0xc, 0x10, 0x12,
    0x15, 0x16, 0x17, 0x18, 0x20, 0x21, 0x23, 0x24,
    0x25, 0x27, 0x28, 0x29, 0x29e1, 0x47e8, 0x480c, 0x488d,
    0x48a9, 0x4932, 0x4b28, 0x5126, 0x5306, 0x5308, 0x5852, 0x5885,
    0x5919, 0x5f66, 0x5fc4, 0x5fd5, 0x60df, 0x60e7, 0x63f8, 0x663f,
    0x6a4e, 0x6b46, 0x6c38, 0x6f14, 0x7063, 0x739a, 0x755f, 0x78be,
    0x794d, 0x7ebc, 0x83df, 0x847d, 0x8580, 0x859d, 0x85cc, 0x87bc,
    0x89fe, 0x8bae, 0x8fd3, 0x902f, 0x91d2, 0x922b, 0x94b7, 0xf862,
    0xf866, 0xf86a, 0xf86c, 0xf86d, 0xf86f, 0xf870, 0xf871, 0xf873,
    0xf874, 0xf876, 0xf877, 0xf878, 0xf87f, 0xf881, 0xf882, 0xf883,
    0xf888, 0xf889, 0xf88c, 0xf88d, 0xf890, 0xf891, 0xf892, 0xf893,
    0xf89b, 0xf89e, 0xf89f, 0xf8a1, 0xf8a3, 0xf8a8, 0xf8a9, 0xf8aa,
    0xf8ab, 0xf8ae, 0xf8af, 0xf8b2, 0xf8b7, 0xf8ba, 0xf8bb, 0xf8bd,
    0xf8be, 0xf8bf, 0xf8c0, 0xf8c1, 0xf8c2, 0xf8c3, 0xf8c5, 0xf8c7,
    0xf8cd, 0xf8ce, 0xf8cf, 0xf8d1, 0xf8d2, 0xf8d3, 0xf8d6, 0xf8d8
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 1569, .range_length = 63705, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 120, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
    /*Store all the custom data of the font*/
    static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_ebike_inter_14 = {
#else
lv_font_t font_ebike_inter_14 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 19,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_EBIKE_INTER_14*/

