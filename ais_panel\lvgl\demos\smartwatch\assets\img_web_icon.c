#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: web.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_web_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x9F,0x07,0x5F,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,
0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x9F,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x07,0x5F,0x0F,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x07,0x7F,0x0F,0xDF,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5F,0x07,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x5F,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,
0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x5F,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x3F,0x0F,0x5F,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x1F,0x1F,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x3F,0x17,0xDF,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x26,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0xDF,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0xFF,0x26,0x00,0x00,0x1F,0x17,0xFF,0x07,0xFF,0x16,0x1F,0x17,
0x1F,0x17,0xDF,0x16,0xFF,0x07,0x1F,0x17,0x00,0x00,0x1F,0x1F,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x17,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0x00,0x00,0x1F,0x17,0xFF,0x16,0x1F,0x17,0x00,0x00,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0x00,0x00,0x1F,0x17,0x1F,0x17,0x1F,0x17,0x00,0x00,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0xFF,0x16,0x1F,0x17,0x00,0x00,
0x00,0x00,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0x00,0x00,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x26,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x26,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x16,0x00,0x00,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x1E,0xFF,0x16,0x00,0x00,0xDF,0x16,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xFF,0x26,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xFF,0x07,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,
0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xFF,0x07,0xFF,0x1E,0xDF,0x1E,0xDF,0x1E,0xFF,0x1E,0x1F,0x1F,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xBF,0x16,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xFF,0x07,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0x00,0x00,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0x00,0x00,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0x00,0x00,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,
0xBF,0x1E,0xBF,0x1E,0xBF,0x1E,0xBF,0x1E,0xBF,0x1E,0xBF,0x1E,0xDF,0x26,0xFF,0x07,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xBF,0x1E,0x00,0x00,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0x00,0x00,0xBF,0x1E,0xDF,0x1E,0xDF,0x1E,0xDF,0x1E,0xFF,0x07,0xFF,0x26,0xBF,0x1E,0xBF,0x1E,0xBF,0x1E,0xBF,0x1E,0xBF,0x1E,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0x00,0x00,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0x9F,0x26,0x00,0x00,0xBF,0x26,0xBF,0x26,0xBF,0x26,
0xBF,0x26,0xBF,0x26,0xBF,0x26,0x00,0x00,0x9F,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0x00,0x00,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0xBF,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x00,0x00,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0xFF,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0xFF,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x00,0x00,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,
0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x00,0x00,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x7F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x7F,0x26,0x7F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x00,0x00,0x7F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x9F,0x26,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x00,0x00,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x26,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,
0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x26,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x00,0x00,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x00,0x00,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x5F,0x2E,0xFF,0x25,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0xFF,0x25,0x5F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x00,0x00,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,0x7F,0x2E,
0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x00,0x00,0x7F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x00,0x00,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x00,0x00,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x7F,0x2E,0x00,0x00,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x2E,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0xFF,0x45,0x5F,0x55,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x00,0x00,0x3F,0x36,0x5F,0x36,0x5F,0x36,
0x5F,0x36,0x5F,0x36,0x3F,0x36,0x00,0x00,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x55,0xFF,0x45,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x5F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x00,0x00,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x00,0x00,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x00,0x00,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x00,0x00,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,0x3F,0x36,
0x3F,0x3E,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x3F,0x3E,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0xFF,0x07,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0xFF,0x07,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x3F,0x3E,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0x1F,0x36,0xDF,0x2D,0x00,0x00,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x00,0x00,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0xFF,0x35,0xFF,0x45,0x1F,0x3E,0x1F,0x3E,
0x1F,0x3E,0x1F,0x3E,0xFF,0x45,0x1F,0x3E,0x1F,0x3E,0x1F,0x36,0xFF,0x35,0x00,0x00,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x1F,0x3E,0x00,0x00,0x00,0x00,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0x00,0x00,0x1F,0x3E,0xFF,0x3D,0xFF,0x3D,0x00,0x00,0x1F,0x3E,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0x00,0x00,0xFF,0x3D,0x1F,0x3E,0xFF,0x3D,0x00,0x00,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0x1F,0x3E,0x00,0x00,
0x00,0x00,0x00,0x00,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0x3F,0x3E,0x00,0x00,0xFF,0x3D,0xFF,0x07,0xDF,0x45,0xFF,0x3D,0xFF,0x3D,0xBF,0x35,0xFF,0x07,0xFF,0x3D,0x00,0x00,0x3F,0x3E,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0xFF,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBF,0x4D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xFF,0x45,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x45,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0xDF,0x3D,0x5F,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0xDF,0x45,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xDF,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,
0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0xBF,0x45,0x9F,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBF,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x5F,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x9F,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5F,0x55,0x7F,0x4D,0x7F,0x4D,0x7F,0x4D,0x7F,0x4D,0x7F,0x4D,
0x7F,0x4D,0x7F,0x4D,0x7F,0x4D,0x7F,0x4D,0x7F,0x4D,0xDF,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0D,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xC9,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x19,0x97,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9B,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x82,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x88,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x24,0xD2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC5,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0xE9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x24,0xE9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0xAA,0x7E,0x64,0x64,0x7F,0xAB,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x17,0x00,0x00,0x00,
0x00,0x00,0x09,0xD2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x5F,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x60,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC5,0x07,0x00,0x00,0x00,0x00,0x82,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9C,0x08,0x00,0x37,0x01,0x0F,0xC5,0xC4,0x0E,0x01,0x37,0x00,0x09,0x9E,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x19,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0x7C,0x00,0x22,0xC4,0x93,0x00,0x96,0xFF,0xFF,0x94,0x00,0x93,0xC3,0x21,0x00,0x7C,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x23,0x00,
0x00,0x97,0xFF,0xFF,0xFF,0xFF,0xFF,0x9D,0x00,0x3B,0xF1,0xFF,0x34,0x10,0xF8,0xFF,0xFF,0xF8,0x10,0x35,0xFF,0xF0,0x3A,0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x0D,0xF5,0xFF,0xFF,0xFF,0xFF,0xE1,0x08,0x27,0xEF,0xFF,0xE5,0x01,0x53,0xFF,0xFF,0xFF,0xFF,0x53,0x01,0xE6,0xFF,0xEE,0x25,0x09,0xE2,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0x60,0x01,0xBE,0xFF,0xFF,0xAD,0x00,0x99,0xFF,0xFF,0xFF,0xFF,0x99,0x00,0xAE,0xFF,0xFF,0xBD,0x00,0x61,0xFF,0xFF,0xFF,0xFF,0xFF,0x56,
0x9B,0xFF,0xFF,0xFF,0xFF,0xF2,0x07,0x03,0x22,0x22,0x22,0x13,0x00,0x1A,0x22,0x22,0x22,0x22,0x1A,0x00,0x13,0x22,0x22,0x22,0x03,0x08,0xF3,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xAB,0x00,0x32,0x44,0x44,0x44,0x17,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x00,0x17,0x44,0x44,0x44,0x32,0x00,0xAD,0xFF,0xFF,0xFF,0xFF,0xC8,0xEB,0xFF,0xFF,0xFF,0xFF,0x7E,0x00,0xCF,0xFF,0xFF,0xFF,0x4E,0x08,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x08,0x4E,0xFF,0xFF,0xFF,0xCF,0x00,0x7F,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0x64,0x00,0xEF,0xFF,0xFF,0xFF,0x43,0x15,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x14,0x44,0xFF,0xFF,0xFF,0xEF,0x00,0x65,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0x65,0x00,0xEF,0xFF,0xFF,0xFF,0x43,0x15,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x14,0x44,0xFF,0xFF,0xFF,0xEF,0x00,0x66,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0x7E,0x00,0xCF,0xFF,0xFF,0xFF,0x4E,0x08,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x08,0x4E,0xFF,0xFF,0xFF,0xCF,0x00,0x7F,0xFF,0xFF,0xFF,0xFF,0xE9,
0xC9,0xFF,0xFF,0xFF,0xFF,0xAB,0x00,0x32,0x44,0x44,0x44,0x17,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x00,0x17,0x44,0x44,0x44,0x32,0x00,0xAD,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xF3,0x08,0x03,0x22,0x22,0x22,0x13,0x00,0x1A,0x22,0x22,0x22,0x22,0x1A,0x00,0x13,0x22,0x22,0x22,0x03,0x08,0xF3,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0x60,0x00,0xBE,0xFF,0xFF,0xAE,0x00,0x99,0xFF,0xFF,0xFF,0xFF,0x99,0x00,0xAE,0xFF,0xFF,0xBD,0x00,0x61,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0D,0xF5,0xFF,0xFF,0xFF,0xFF,0xE2,0x09,0x26,0xEE,0xFF,0xE5,0x01,0x53,0xFF,0xFF,0xFF,0xFF,0x53,0x01,0xE7,0xFF,0xED,0x25,0x09,0xE3,0xFF,0xFF,0xFF,0xFF,0xF3,0x0B,0x00,0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0x9E,0x00,0x3A,0xF0,0xFF,0x34,0x10,0xF8,0xFF,0xFF,0xF8,0x10,0x35,0xFF,0xEF,0x38,0x00,0x9E,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0x00,0x00,0x24,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0x7C,0x00,0x21,0xC2,0x93,0x00,0x95,0xFF,0xFF,0x93,0x00,0x93,0xC1,0x20,0x00,0x7C,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x21,0x00,
0x00,0x00,0x88,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9D,0x09,0x00,0x36,0x01,0x0F,0xC4,0xC3,0x0E,0x01,0x36,0x00,0x09,0x9E,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x85,0x00,0x00,0x00,0x00,0x07,0xC5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x61,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x61,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC3,0x06,0x00,0x00,0x00,0x00,0x00,0x17,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0xAC,0x7F,0x65,0x65,0x7F,0xAC,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE0,0x16,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2C,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x2B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x14,0xC0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC5,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x87,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x85,0x06,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x22,0x9A,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x99,0x21,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x56,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x55,0x0B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_web_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_web_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_web_icon_data};

