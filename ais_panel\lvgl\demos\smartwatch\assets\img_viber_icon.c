#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: viber.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_viber_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0x69,0xF5,0x71,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0xD6,0x71,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x71,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0xF5,0x69,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD5,0x71,0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x13,0x62,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x71,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x13,0x62,0xF5,0x71,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x77,0x93,0x7B,0xC5,0x5C,0xD6,0x1E,0xEF,0x9F,0xF7,0xDF,0xFF,0xDF,0xFF,0x9F,0xF7,0x1E,0xEF,0x5C,0xD6,0x7B,0xC5,0x57,0x93,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xD5,0x71,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x76,0x7A,0x5C,0xD6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3E,0xEF,0xDD,0xE6,0x7D,0xDE,
0x7D,0xDE,0xDD,0xE6,0x3E,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5C,0xD6,0x76,0x7A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x71,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x55,0x72,0x1E,0xEF,0xFF,0xFF,0x1C,0xD6,0x57,0x8B,0x35,0x72,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x35,0x72,0x57,0x8B,0x3C,0xD6,0xFF,0xFF,0x1E,0xEF,0x55,0x7A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0x00,0x00,
0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xDC,0xCD,0xFF,0xFF,0x3A,0xBD,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x55,0x72,0xB6,0x82,0x15,0x72,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x72,0x3B,0xBD,0xFF,0xFF,0xDC,0xCD,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x00,0x00,0xB4,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xB6,0x82,0xFF,0xFF,0x9D,0xDE,0x15,0x72,0xF5,0x69,0x55,0x72,0x1A,0xBD,0xB6,0x7A,0xF5,0x69,0xB6,0x82,
0x99,0xAC,0x7B,0xC5,0x79,0xAC,0x15,0x72,0xF5,0x69,0xF5,0x69,0x15,0x72,0x9D,0xDE,0xFF,0xFF,0xB6,0x82,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xD6,0x71,0xF5,0x71,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x19,0xA4,0xFF,0xFF,0x79,0xAC,0xF5,0x69,0xF5,0x69,0xDD,0xE6,0xFF,0xFF,0x9D,0xDE,0x15,0x72,0xF5,0x69,0x1A,0xBD,0x5B,0xC5,0xF8,0x9B,0x1A,0xBD,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x79,0xAC,0xFF,0xFF,0x19,0xA4,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x1A,0xBD,0xFF,0xFF,0x57,0x93,0xF5,0x69,0xF5,0x69,0x7E,0xF7,0xFF,0xFF,0xFF,0xFF,0x39,0xA4,0xF5,0x69,0xB8,0x9B,0x59,0xA4,0xBB,0xC5,0x79,0xAC,0x77,0x93,0xF5,0x69,0xF5,0x69,0x77,0x93,0xFF,0xFF,0x1A,0xBD,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x71,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x9B,0xC5,0xFF,0xFF,0xB6,0x82,0xF5,0x69,0xF5,0x69,0x1A,0xBD,0xFF,0xFF,0x5E,0xEF,0xB6,0x82,0xF5,0x69,
0xF5,0x69,0x99,0xAC,0x18,0x9C,0x79,0xAC,0x3B,0xBD,0xF5,0x69,0xF5,0x69,0xD6,0x82,0xFF,0xFF,0x9B,0xC5,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xFC,0xCD,0xFF,0xFF,0x76,0x7A,0xF5,0x69,0xF5,0x69,0x76,0x7A,0x9F,0xF7,0x9D,0xDE,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x55,0x72,0x96,0x7A,0x39,0xA4,0x3B,0xBD,0xF5,0x69,0xF5,0x69,0x76,0x7A,0xFF,0xFF,0xFC,0xCD,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xDC,0xCD,0xFF,0xFF,0x96,0x7A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x59,0xA4,0xFF,0xFF,0x98,0x93,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x96,0x7A,0xF5,0x69,0xF5,0x69,0x96,0x7A,0xFF,0xFF,0xBB,0xCD,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x7B,0xC5,0xFF,0xFF,0xF6,0x82,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xBB,0xCD,0xBF,0xF7,0x98,0x93,
0xF5,0x69,0x77,0x93,0x99,0xAC,0x15,0x72,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF7,0x82,0xFF,0xFF,0x7B,0xC5,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xBA,0xAC,0xFF,0xFF,0xD8,0x9B,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x72,0xBB,0xC5,0xFF,0xFF,0xDD,0xE6,0xDF,0xFF,0xFF,0xFF,0x9D,0xDE,0x96,0x7A,0xF5,0x69,0xF5,0x69,0xD8,0x9B,0xFF,0xFF,0x99,0xAC,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xB8,0x93,0xFF,0xFF,0x3A,0xBD,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF8,0x9B,0x9F,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x39,0xA4,0xF5,0x69,0xF5,0x69,0x3B,0xBD,0xFF,0xFF,0x98,0x93,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x35,0x72,0x7F,0xF7,0x9F,0xF7,0xB6,0x82,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0x55,0x72,0xBA,0xB4,0x3E,0xEF,0xBB,0xC5,0x15,0x72,0xF5,0x69,0xD6,0x82,0xBF,0xF7,0x5E,0xEF,0x15,0x72,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x39,0xA4,0xFF,0xFF,0x3E,0xEF,0xF7,0x82,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x17,0x8B,0x3E,0xEF,0xFF,0xFF,0x18,0xA4,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xFA,0xB4,0xFF,0xFF,0xDF,0xFF,0xFA,0xB4,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF6,0x82,0x37,0x8B,0x98,0x93,0x18,0xA4,0xDA,0xB4,0xFC,0xCD,0xDF,0xFF,0xFF,0xFF,0xDA,0xB4,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0x00,0x00,0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xB8,0x9B,0xDD,0xE6,0x5D,0xD6,0xF5,0x69,0xF5,0x69,0x39,0xA4,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDD,0xE6,0xB8,0x93,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x00,0x00,0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x3C,0xD6,0xF5,0x69,0x18,0xA4,0x3C,0xD6,0x3A,0xBD,0x3A,0xBD,0xFA,0xB4,0x99,0xAC,0xB8,0x9B,0xD6,0x82,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x71,0x00,0x00,
0x00,0x00,0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x3C,0xD6,0xF8,0x9B,0xFC,0xCD,0x15,0x72,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x13,0x62,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x7D,0xDE,0xDC,0xCD,0x15,0x72,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x56,0x6A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x72,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x71,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x56,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x56,0x6A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x71,0xF5,0x71,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0x6A,0xF5,0x69,0x15,0x6A,0xF5,0x69,0xF5,0x69,0xF5,0x69,
0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0x15,0x6A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x1B,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_viber_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_viber_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_viber_icon_data};

