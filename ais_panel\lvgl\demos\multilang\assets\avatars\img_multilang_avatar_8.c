#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_8
    #define LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_8
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_8 uint8_t
img_multilang_avatar_8_map[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc3, 0xc3, 0xc3, 0x11, 0xc2, 0xc2, 0xc7, 0x37, 0xc6, 0xc6, 0xc8, 0x5e, 0xc5, 0xc7, 0xc7, 0x86, 0xc5, 0xc6, 0xc8, 0xa8, 0xc4, 0xc5, 0xc6, 0xbf, 0xc4, 0xc7, 0xc8, 0xd2, 0xc6, 0xc8, 0xc9, 0xe5, 0xc6, 0xc8, 0xc9, 0xee, 0xc1, 0xc3, 0xc5, 0xf2, 0xbb, 0xbd, 0xbe, 0xff, 0xb3, 0xb5, 0xb5, 0xff, 0xa9, 0xab, 0xab, 0xf2, 0x9d, 0x9f, 0x9f, 0xee, 0x8f, 0x91, 0x91, 0xe5, 0x7f, 0x80, 0x80, 0xd2, 0x71, 0x74, 0x71, 0xbf, 0x5f, 0x62, 0x5e, 0xa8, 0x53, 0x53, 0x51, 0x86, 0x54, 0x54, 0x4e, 0x5e, 0x53, 0x58, 0x4e, 0x37, 0x5a, 0x5a, 0x4b, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0xc1, 0xc1, 0x19, 0xbd, 0xc3, 0xc3, 0x55, 0xbd, 0xc0, 0xc2, 0x8f, 0xbc, 0xc0, 0xc2, 0xc1, 0xbe, 0xc1, 0xc2, 0xe9, 0xbf, 0xc3, 0xc4, 0xff, 0xc2, 0xc6, 0xc7, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc5, 0xc7, 0xc8, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc2, 0xc4, 0xc5, 0xff, 0xbb, 0xbd, 0xbe, 0xff, 0xb3, 0xb5, 0xb5, 0xff, 0xaa, 0xac, 0xac, 0xff, 0x9e, 0xa0, 0xa0, 0xff, 0x90, 0x92, 0x92, 0xff, 0x7f, 0x81, 0x81, 0xff, 0x71, 0x74, 0x71, 0xff, 0x60, 0x63, 0x5e, 0xff, 0x53, 0x54, 0x52, 0xff, 0x55, 0x56, 0x52, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x59, 0x5c, 0x52, 0xff, 0x57, 0x5a, 0x52, 0xe9, 0x54, 0x58, 0x4f, 0xc1, 0x55, 0x57, 0x4e, 0x8f, 0x57, 0x5a, 0x51, 0x55, 0x5b, 0x5b, 0x51, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0xda, 0xda, 0x07, 0xc8, 0xc8, 0xcc, 0x46, 0xc3, 0xc5, 0xc7, 0x8e, 0xc1, 0xc2, 0xc2, 0xd7, 0xbf, 0xc1, 0xc2, 0xff, 0xbe, 0xc3, 0xc4, 0xff, 0xbe, 0xc2, 0xc3, 0xff, 0xc0, 0xc4, 0xc5, 0xff, 0xbf, 0xc3, 0xc4, 0xff, 0xbf, 0xc4, 0xc5, 0xff, 0xc3, 0xc7, 0xc8, 0xff, 0xc7, 0xc9, 0xca, 0xff, 0xc7, 0xc9, 0xca, 0xff, 0xc7, 0xc9, 0xca, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc7, 0xc9, 0xca, 0xff, 0xc9, 0xcb, 0xcc, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc1, 0xc3, 0xc4, 0xff, 0xbb, 0xbd, 0xbe, 0xff, 0xb3, 0xb5, 0xb5, 0xff, 0xaa, 0xac, 0xac, 0xff, 0x9e, 0xa0, 0xa0, 0xff, 0x90, 0x92, 0x92, 0xff, 0x7f, 0x81, 0x81, 0xff, 0x71, 0x74, 0x71, 0xff, 0x60, 0x64, 0x5e, 0xff, 0x54, 0x55, 0x54, 0xff, 0x56, 0x57, 0x52, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x5a, 0x5d, 0x53, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x55, 0x58, 0x50, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x5b, 0x5e, 0x55, 0xff, 0x56, 0x5a, 0x50, 0xd7, 0x56, 0x59, 0x50, 0x8e, 0x53, 0x57, 0x4c, 0x46, 0x48, 0x48, 0x48, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0xcc, 0x05, 0xd4, 0xd4, 0xd4, 0x48, 0xd2, 0xd2, 0xd2, 0x9f, 0xcd, 0xcd, 0xcc, 0xef, 0xca, 0xcb, 0xcb, 0xff, 0xc5, 0xc7, 0xc8, 0xff, 0xc1, 0xc3, 0xc4, 0xff, 0xc2, 0xc4, 0xc5, 0xff, 0xc3, 0xc6, 0xc7, 0xff, 0xc3, 0xc5, 0xc6, 0xff, 0xc3, 0xc5, 0xc6, 0xff, 0xc0, 0xc4, 0xc5, 0xff, 0xc2, 0xc6, 0xc7, 0xff, 0xc4, 0xc8, 0xc9, 0xff, 0xc5, 0xc9, 0xca, 0xff, 0xc5, 0xc9, 0xca, 0xff, 0xc5, 0xc9, 0xca, 0xff, 0xc7, 0xca, 0xcb, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc9, 0xcb, 0xcc, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc2, 0xc4, 0xc5, 0xff, 0xba, 0xbc, 0xbd, 0xff, 0xb1, 0xb3, 0xb4, 0xff, 0xa9, 0xab, 0xab, 0xff, 0x9e, 0xa0, 0xa0, 0xff, 0x8f, 0x91, 0x90, 0xff, 0x7f, 0x83, 0x7e, 0xff, 0x70, 0x74, 0x70, 0xff, 0x5e, 0x61, 0x5c, 0xff, 0x54, 0x54, 0x51, 0xff, 0x54, 0x55, 0x51, 0xff, 0x55, 0x57, 0x52, 0xff, 0x5a, 0x5b, 0x57, 0xff, 0x58, 0x5a, 0x53, 0xff, 0x54, 0x57, 0x4c, 0xff, 0x56, 0x59, 0x4f, 0xff, 0x5a, 0x5d, 0x55, 0xff, 0x5b, 0x5e, 0x55, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x58, 0x5b, 0x53, 0xef, 0x56, 0x59, 0x51, 0x9f, 0x51, 0x55, 0x4d, 0x48, 0x66, 0x66, 0x33, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0xda, 0xda, 0x23, 0xd7, 0xd7, 0xd7, 0x86, 0xd4, 0xd4, 0xd4, 0xe5, 0xd6, 0xd6, 0xd6, 0xff, 0xd4, 0xd4, 0xd4, 0xff, 0xd0, 0xd0, 0xd0, 0xff, 0xcc, 0xcd, 0xcd, 0xff, 0xc6, 0xc8, 0xc9, 0xff, 0xc4, 0xc6, 0xc7, 0xff, 0xc7, 0xc9, 0xca, 0xff, 0xc9, 0xcb, 0xcc, 0xff, 0xc8, 0xc9, 0xca, 0xff, 0xc5, 0xc7, 0xc8, 0xff, 0xc1, 0xc5, 0xc6, 0xff, 0xc2, 0xc6, 0xc7, 0xff, 0xc5, 0xc9, 0xca, 0xff, 0xc5, 0xca, 0xcb, 0xff, 0xc5, 0xca, 0xcb, 0xff, 0xc5, 0xca, 0xcb, 0xff, 0xc7, 0xca, 0xcb, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc9, 0xcb, 0xcc, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc2, 0xc4, 0xc5, 0xff, 0xba, 0xbc, 0xbd, 0xff, 0xb1, 0xb3, 0xb4, 0xff, 0xaa, 0xac, 0xab, 0xff, 0x9e, 0xa0, 0xa1, 0xff, 0x8f, 0x92, 0x91, 0xff, 0x7f, 0x84, 0x7e, 0xff, 0x71, 0x75, 0x70, 0xff, 0x5e, 0x61, 0x5c, 0xff, 0x54, 0x54, 0x50, 0xff, 0x54, 0x55, 0x51, 0xff, 0x56, 0x57, 0x53, 0xff, 0x5a, 0x5b, 0x58, 0xff, 0x58, 0x5a, 0x55, 0xff, 0x55, 0x59, 0x4d, 0xff, 0x57, 0x5a, 0x50, 0xff, 0x5a, 0x5d, 0x55, 0xff, 0x5b, 0x5e, 0x55, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x52, 0x54, 0x4b, 0xe5, 0x53, 0x57, 0x4e, 0x86, 0x57, 0x57, 0x50, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe3, 0xdf, 0xdf, 0x41, 0xdf, 0xde, 0xdd, 0xb4, 0xda, 0xda, 0xd9, 0xfe, 0xd7, 0xd7, 0xd7, 0xff, 0xd5, 0xd4, 0xd5, 0xff, 0xd7, 0xd7, 0xd7, 0xff, 0xd7, 0xd7, 0xd7, 0xff, 0xd3, 0xd2, 0xd2, 0xff, 0xce, 0xcf, 0xcf, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xcc, 0xcd, 0xce, 0xff, 0xcb, 0xcd, 0xce, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc5, 0xc6, 0xc7, 0xff, 0xc2, 0xc5, 0xc7, 0xff, 0xc3, 0xc7, 0xc8, 0xff, 0xc6, 0xca, 0xcb, 0xff, 0xc7, 0xcb, 0xcd, 0xff, 0xc7, 0xcb, 0xcc, 0xff, 0xc6, 0xcb, 0xcc, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xc9, 0xca, 0xcb, 0xff, 0xc9, 0xcb, 0xcb, 0xff, 0xcb, 0xcd, 0xcd, 0xff, 0xc4, 0xc6, 0xc7, 0xff, 0xb9, 0xbb, 0xbc, 0xff, 0xb2, 0xb3, 0xb4, 0xff, 0xac, 0xad, 0xac, 0xff, 0x9f, 0xa1, 0xa2, 0xff, 0x90, 0x92, 0x92, 0xff, 0x80, 0x85, 0x7f, 0xff, 0x71, 0x76, 0x71, 0xff, 0x5e, 0x62, 0x5e, 0xff, 0x55, 0x55, 0x51, 0xff, 0x56, 0x56, 0x52, 0xff, 0x56, 0x57, 0x54, 0xff, 0x5a, 0x5b, 0x58, 0xff, 0x5a, 0x5b, 0x55, 0xff, 0x57, 0x5b, 0x4f, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x5a, 0x5d, 0x54, 0xff, 0x5b, 0x5e, 0x55, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x54, 0x57, 0x4e, 0xfe, 0x50, 0x53, 0x4b, 0xb4, 0x4e, 0x52, 0x4a, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe7, 0xe4, 0xe4, 0x4c, 0xe4, 0xe3, 0xe2, 0xcb, 0xe3, 0xe1, 0xe0, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xda, 0xda, 0xd9, 0xff, 0xd8, 0xd8, 0xd8, 0xff, 0xd6, 0xd7, 0xd6, 0xff, 0xd7, 0xd6, 0xd7, 0xff, 0xd6, 0xd5, 0xd7, 0xff, 0xd0, 0xd2, 0xd3, 0xff, 0xcc, 0xd0, 0xd1, 0xff, 0xca, 0xce, 0xcf, 0xff, 0xc9, 0xcd, 0xce, 0xff, 0xca, 0xcd, 0xce, 0xff, 0xc8, 0xcb, 0xcc, 0xff, 0xc5, 0xc8, 0xc9, 0xff, 0xc4, 0xc8, 0xc9, 0xff, 0xc4, 0xc9, 0xc8, 0xff, 0xc2, 0xc7, 0xc6, 0xff, 0xc5, 0xca, 0xc9, 0xff, 0xc6, 0xcd, 0xca, 0xff, 0xc8, 0xcb, 0xca, 0xff, 0xca, 0xc8, 0xca, 0xff, 0xcb, 0xcb, 0xcc, 0xff, 0xc9, 0xcb, 0xcd, 0xff, 0xce, 0xcf, 0xd5, 0xff, 0xce, 0xcf, 0xd0, 0xff, 0xc7, 0xcb, 0xc6, 0xff, 0xc0, 0xc1, 0xc1, 0xff, 0xb2, 0xb8, 0xb4, 0xff, 0xaa, 0xb0, 0xae, 0xff, 0xa4, 0xa3, 0xa5, 0xff, 0x92, 0x96, 0x91, 0xff, 0x82, 0x84, 0x80, 0xff, 0x75, 0x77, 0x71, 0xff, 0x61, 0x65, 0x5d, 0xff, 0x54, 0x57, 0x54, 0xff, 0x54, 0x57, 0x52, 0xff, 0x57, 0x5a, 0x53, 0xff, 0x59, 0x5d, 0x57, 0xff, 0x58, 0x5b, 0x55, 0xff, 0x58, 0x5c, 0x55, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x58, 0x5c, 0x53, 0xff, 0x59, 0x5e, 0x55, 0xff, 0x57, 0x5b, 0x52, 0xff, 0x55, 0x5a, 0x51, 0xff, 0x53, 0x57, 0x4e, 0xff, 0x58, 0x5c, 0x53, 0xff, 0x58, 0x5a, 0x51, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x51, 0x54, 0x4b, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x4d, 0x50, 0x47, 0xcb, 0x4f, 0x52, 0x48, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe9, 0xe9, 0xe5, 0x46, 0xe8, 0xe5, 0xe5, 0xc9, 0xe6, 0xe4, 0xe3, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xda, 0xd9, 0xd9, 0xff, 0xd8, 0xd8, 0xd9, 0xff, 0xd8, 0xd8, 0xd7, 0xff, 0xd7, 0xd6, 0xd7, 0xff, 0xd4, 0xd3, 0xd5, 0xff, 0xcd, 0xd1, 0xd2, 0xff, 0xca, 0xcf, 0xd1, 0xff, 0xca, 0xce, 0xcf, 0xff, 0xc6, 0xcb, 0xcc, 0xff, 0xc6, 0xca, 0xcb, 0xff, 0xc4, 0xc8, 0xc9, 0xff, 0xc1, 0xc6, 0xc7, 0xff, 0xc2, 0xc7, 0xc8, 0xff, 0xc3, 0xc7, 0xc7, 0xff, 0xc4, 0xc9, 0xc8, 0xff, 0xc4, 0xc9, 0xc9, 0xff, 0xc4, 0xcb, 0xc7, 0xff, 0xc9, 0xcd, 0xcc, 0xff, 0xcf, 0xcd, 0xd1, 0xff, 0xca, 0xcd, 0xd1, 0xff, 0xc7, 0xcd, 0xd4, 0xff, 0xc7, 0xca, 0xd5, 0xff, 0x99, 0x9a, 0xaa, 0xff, 0x94, 0x96, 0xa5, 0xff, 0xbe, 0xc2, 0xc7, 0xff, 0xb0, 0xbb, 0xbb, 0xff, 0xa3, 0xaa, 0xae, 0xff, 0xa2, 0xa3, 0xa5, 0xff, 0x93, 0x99, 0x93, 0xff, 0x84, 0x85, 0x84, 0xff, 0x75, 0x76, 0x71, 0xff, 0x66, 0x69, 0x62, 0xff, 0x59, 0x5d, 0x5b, 0xff, 0x56, 0x5a, 0x55, 0xff, 0x57, 0x5d, 0x53, 0xff, 0x5c, 0x60, 0x5a, 0xff, 0x5a, 0x5e, 0x59, 0xff, 0x5a, 0x5e, 0x59, 0xff, 0x58, 0x5c, 0x55, 0xff, 0x59, 0x5e, 0x54, 0xff, 0x5c, 0x61, 0x58, 0xff, 0x57, 0x5c, 0x53, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x55, 0x5a, 0x51, 0xff, 0x5a, 0x5e, 0x55, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x56, 0x59, 0x50, 0xff, 0x56, 0x59, 0x50, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x50, 0x53, 0x4a, 0xff, 0x49, 0x4f, 0x44, 0xc9, 0x45, 0x4c, 0x45, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xe9, 0xe9, 0x24, 0xec, 0xe8, 0xe8, 0xb4, 0xea, 0xe8, 0xe7, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xda, 0xd9, 0xd9, 0xff, 0xd8, 0xd8, 0xd9, 0xff, 0xd8, 0xd8, 0xd7, 0xff, 0xd6, 0xd5, 0xd6, 0xff, 0xd3, 0xd1, 0xd4, 0xff, 0xcc, 0xd0, 0xd0, 0xff, 0xc9, 0xce, 0xcf, 0xff, 0xc7, 0xcc, 0xcd, 0xff, 0xc4, 0xc8, 0xc8, 0xff, 0xc5, 0xc8, 0xc8, 0xff, 0xc8, 0xcc, 0xcc, 0xff, 0xcc, 0xd0, 0xcf, 0xff, 0xca, 0xce, 0xce, 0xff, 0xc5, 0xc8, 0xca, 0xff, 0xc8, 0xcb, 0xce, 0xff, 0xcc, 0xce, 0xd0, 0xff, 0xca, 0xce, 0xcd, 0xff, 0xc5, 0xcb, 0xcd, 0xff, 0xc2, 0xc9, 0xcd, 0xff, 0xc3, 0xcb, 0xd4, 0xff, 0x8e, 0x97, 0xa4, 0xff, 0x4d, 0x55, 0x64, 0xff, 0x59, 0x5f, 0x76, 0xff, 0x5d, 0x62, 0x79, 0xff, 0x8c, 0x93, 0xa1, 0xff, 0x98, 0xa1, 0xb2, 0xff, 0x9a, 0x9e, 0xab, 0xff, 0x9e, 0xa1, 0x9f, 0xff, 0x91, 0x97, 0x97, 0xff, 0x83, 0x86, 0x86, 0xff, 0x72, 0x77, 0x73, 0xff, 0x63, 0x68, 0x64, 0xff, 0x5b, 0x5e, 0x5d, 0xff, 0x59, 0x5d, 0x58, 0xff, 0x5a, 0x5f, 0x55, 0xff, 0x5c, 0x60, 0x58, 0xff, 0x59, 0x5d, 0x56, 0xff, 0x56, 0x5a, 0x55, 0xff, 0x57, 0x5c, 0x53, 0xff, 0x59, 0x5f, 0x53, 0xff, 0x5b, 0x61, 0x58, 0xff, 0x58, 0x5d, 0x53, 0xff, 0x55, 0x5a, 0x50, 0xff, 0x55, 0x5b, 0x52, 0xff, 0x5b, 0x5f, 0x56, 0xff, 0x5b, 0x5d, 0x54, 0xff, 0x56, 0x59, 0x50, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x51, 0x53, 0x4a, 0xff, 0x50, 0x52, 0x49, 0xff, 0x51, 0x54, 0x4b, 0xff, 0x4d, 0x53, 0x48, 0xff, 0x49, 0x4e, 0x46, 0xff, 0x46, 0x4a, 0x46, 0xb5, 0x46, 0x4d, 0x46, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe2, 0xe2, 0xe2, 0x09, 0xef, 0xec, 0xea, 0x87, 0xee, 0xeb, 0xea, 0xf9, 0xec, 0xea, 0xe9, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xe5, 0xe3, 0xe3, 0xff, 0xe2, 0xe0, 0xe0, 0xff, 0xde, 0xdc, 0xdc, 0xff, 0xda, 0xd9, 0xd9, 0xff, 0xd8, 0xd8, 0xd8, 0xff, 0xd8, 0xd8, 0xd8, 0xff, 0xd5, 0xd5, 0xd6, 0xff, 0xd1, 0xd3, 0xd4, 0xff, 0xcd, 0xcf, 0xd1, 0xff, 0xcb, 0xce, 0xcf, 0xff, 0xcb, 0xcc, 0xcc, 0xff, 0xcd, 0xd0, 0xd1, 0xff, 0xd4, 0xd7, 0xd9, 0xff, 0xd1, 0xd6, 0xd6, 0xff, 0xc0, 0xc4, 0xc6, 0xff, 0xc2, 0xc5, 0xc9, 0xff, 0xc4, 0xc6, 0xce, 0xff, 0xb8, 0xbb, 0xc3, 0xff, 0xc2, 0xc4, 0xcc, 0xff, 0xce, 0xcf, 0xd4, 0xff, 0xca, 0xcb, 0xd0, 0xff, 0xa0, 0xab, 0xb3, 0xff, 0x87, 0x96, 0xa5, 0xff, 0x67, 0x74, 0x84, 0xff, 0x30, 0x39, 0x4c, 0xff, 0x43, 0x4d, 0x5c, 0xff, 0x4d, 0x57, 0x65, 0xff, 0x54, 0x5e, 0x70, 0xff, 0x59, 0x63, 0x7b, 0xff, 0x73, 0x7a, 0x8d, 0xff, 0x9e, 0xa8, 0xac, 0xff, 0x96, 0xa1, 0xa4, 0xff, 0x80, 0x86, 0x8e, 0xff, 0x7c, 0x80, 0x86, 0xff, 0x6d, 0x72, 0x6c, 0xff, 0x5f, 0x63, 0x5b, 0xff, 0x60, 0x64, 0x5d, 0xff, 0x65, 0x69, 0x61, 0xff, 0x5f, 0x64, 0x5d, 0xff, 0x57, 0x5b, 0x55, 0xff, 0x59, 0x5a, 0x55, 0xff, 0x5a, 0x5b, 0x56, 0xff, 0x5e, 0x5f, 0x59, 0xff, 0x5f, 0x62, 0x55, 0xff, 0x5b, 0x60, 0x57, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x54, 0x58, 0x4f, 0xff, 0x59, 0x5d, 0x55, 0xff, 0x58, 0x5c, 0x52, 0xff, 0x54, 0x58, 0x4f, 0xff, 0x54, 0x58, 0x4f, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x56, 0x5a, 0x51, 0xff, 0x52, 0x56, 0x4d, 0xff, 0x50, 0x54, 0x4b, 0xff, 0x50, 0x54, 0x4b, 0xff, 0x50, 0x55, 0x4b, 0xff, 0x4d, 0x53, 0x48, 0xff, 0x4a, 0x4f, 0x47, 0xff, 0x47, 0x4a, 0x46, 0xff, 0x49, 0x4d, 0x48, 0xf9, 0x49, 0x4d, 0x49, 0x87, 0x38, 0x55, 0x38, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xef, 0xeb, 0xeb, 0x40, 0xed, 0xeb, 0xe9, 0xda, 0xed, 0xec, 0xeb, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xec, 0xea, 0xe9, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xe5, 0xe3, 0xe3, 0xff, 0xe2, 0xe0, 0xe0, 0xff, 0xde, 0xdc, 0xdc, 0xff, 0xda, 0xd9, 0xd9, 0xff, 0xd8, 0xd8, 0xd8, 0xff, 0xd8, 0xd7, 0xd7, 0xff, 0xd6, 0xd7, 0xd8, 0xff, 0xd3, 0xd6, 0xd7, 0xff, 0xd3, 0xd4, 0xd4, 0xff, 0xce, 0xcf, 0xd2, 0xff, 0xc8, 0xcd, 0xd3, 0xff, 0xbc, 0xc3, 0xcc, 0xff, 0xb1, 0xba, 0xc6, 0xff, 0xa2, 0xad, 0xb7, 0xff, 0x74, 0x7d, 0x8b, 0xff, 0x79, 0x81, 0x92, 0xff, 0x8e, 0x96, 0xa8, 0xff, 0x78, 0x81, 0x93, 0xff, 0x72, 0x7b, 0x8e, 0xff, 0xa7, 0xa9, 0xb6, 0xff, 0xd7, 0xd7, 0xde, 0xff, 0x87, 0x92, 0x9e, 0xff, 0x46, 0x54, 0x6a, 0xff, 0x66, 0x70, 0x85, 0xff, 0x5d, 0x63, 0x7a, 0xff, 0x3f, 0x43, 0x54, 0xff, 0x46, 0x4d, 0x5a, 0xff, 0x41, 0x4a, 0x5c, 0xff, 0x32, 0x3b, 0x50, 0xff, 0x4b, 0x55, 0x69, 0xff, 0x6c, 0x79, 0x86, 0xff, 0x7f, 0x8c, 0x95, 0xff, 0x72, 0x7d, 0x91, 0xff, 0x7c, 0x83, 0x95, 0xff, 0x79, 0x81, 0x84, 0xff, 0x70, 0x79, 0x7c, 0xff, 0x6b, 0x72, 0x7b, 0xff, 0x71, 0x77, 0x84, 0xff, 0x6b, 0x70, 0x75, 0xff, 0x58, 0x5c, 0x57, 0xff, 0x55, 0x59, 0x49, 0xff, 0x5c, 0x63, 0x5b, 0xff, 0x67, 0x6d, 0x76, 0xff, 0x65, 0x6a, 0x71, 0xff, 0x5b, 0x61, 0x62, 0xff, 0x55, 0x5b, 0x54, 0xff, 0x59, 0x5b, 0x52, 0xff, 0x5a, 0x5d, 0x57, 0xff, 0x53, 0x59, 0x50, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x54, 0x59, 0x50, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x55, 0x5b, 0x52, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x50, 0x56, 0x4d, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x4d, 0x53, 0x48, 0xff, 0x4b, 0x4f, 0x47, 0xff, 0x47, 0x4b, 0x47, 0xff, 0x4a, 0x4e, 0x49, 0xff, 0x49, 0x4d, 0x48, 0xff, 0x46, 0x4a, 0x46, 0xda, 0x46, 0x46, 0x42, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xf0, 0xee, 0xed, 0x8e, 0xed, 0xeb, 0xea, 0xfe, 0xed, 0xeb, 0xea, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xec, 0xea, 0xe9, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xe5, 0xe3, 0xe3, 0xff, 0xe2, 0xe0, 0xe0, 0xff, 0xde, 0xdc, 0xdc, 0xff, 0xda, 0xd9, 0xd9, 0xff, 0xd8, 0xd8, 0xd8, 0xff, 0xd6, 0xd6, 0xd6, 0xff, 0xdb, 0xdc, 0xdc, 0xff, 0xdd, 0xde, 0xdf, 0xff, 0xd8, 0xd8, 0xd7, 0xff, 0xd2, 0xd5, 0xd8, 0xff, 0xcf, 0xd8, 0xe1, 0xff, 0xa7, 0xaf, 0xbb, 0xff, 0x60, 0x6c, 0x7c, 0xff, 0x49, 0x55, 0x67, 0xff, 0x47, 0x51, 0x67, 0xff, 0x46, 0x50, 0x68, 0xff, 0x43, 0x4d, 0x62, 0xff, 0x4b, 0x55, 0x6a, 0xff, 0x26, 0x30, 0x47, 0xff, 0x66, 0x71, 0x7f, 0xff, 0xc4, 0xca, 0xd0, 0xff, 0x6d, 0x75, 0x85, 0xff, 0x36, 0x40, 0x54, 0xff, 0x44, 0x4c, 0x5d, 0xff, 0x3d, 0x41, 0x55, 0xff, 0x3a, 0x3b, 0x4a, 0xff, 0x2f, 0x32, 0x3d, 0xff, 0x32, 0x39, 0x48, 0xff, 0x29, 0x31, 0x42, 0xff, 0x4d, 0x54, 0x63, 0xff, 0x5f, 0x66, 0x74, 0xff, 0x4b, 0x51, 0x62, 0xff, 0x2d, 0x33, 0x44, 0xff, 0x29, 0x34, 0x41, 0xff, 0x43, 0x4f, 0x61, 0xff, 0x3a, 0x45, 0x57, 0xff, 0x38, 0x42, 0x56, 0xff, 0x4c, 0x54, 0x6d, 0xff, 0x5b, 0x5d, 0x71, 0xff, 0x6e, 0x6f, 0x76, 0xff, 0x6a, 0x6e, 0x69, 0xff, 0x62, 0x6d, 0x6f, 0xff, 0x4c, 0x58, 0x68, 0xff, 0x45, 0x4f, 0x64, 0xff, 0x5b, 0x63, 0x6a, 0xff, 0x56, 0x5c, 0x52, 0xff, 0x59, 0x5c, 0x52, 0xff, 0x5d, 0x60, 0x59, 0xff, 0x55, 0x5b, 0x51, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x54, 0x5a, 0x51, 0xff, 0x57, 0x5d, 0x53, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x51, 0x57, 0x4e, 0xff, 0x51, 0x57, 0x4e, 0xff, 0x51, 0x57, 0x4e, 0xff, 0x51, 0x57, 0x4e, 0xff, 0x4f, 0x55, 0x49, 0xff, 0x4b, 0x50, 0x48, 0xff, 0x48, 0x4c, 0x48, 0xff, 0x4b, 0x4f, 0x4a, 0xff, 0x4b, 0x4f, 0x4a, 0xff, 0x48, 0x4c, 0x47, 0xff, 0x47, 0x49, 0x44, 0xfe, 0x46, 0x47, 0x42, 0x8e, 0x48, 0x48, 0x48, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf2, 0xf2, 0xf2, 0x29, 0xf1, 0xf1, 0xf1, 0xd1, 0xf1, 0xee, 0xed, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xec, 0xea, 0xe9, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xe5, 0xe3, 0xe3, 0xff, 0xe2, 0xe0, 0xe0, 0xff, 0xde, 0xdc, 0xdc, 0xff, 0xda, 0xd9, 0xd8, 0xff, 0xd7, 0xd7, 0xd7, 0xff, 0xd7, 0xd6, 0xd7, 0xff, 0xd8, 0xd9, 0xda, 0xff, 0xde, 0xe2, 0xe2, 0xff, 0xe0, 0xe3, 0xe3, 0xff, 0xe1, 0xe3, 0xe6, 0xff, 0xd2, 0xd6, 0xdf, 0xff, 0xb1, 0xb9, 0xc1, 0xff, 0x4a, 0x51, 0x5a, 0xff, 0x24, 0x2c, 0x39, 0xff, 0x42, 0x49, 0x59, 0xff, 0x36, 0x3c, 0x4d, 0xff, 0x24, 0x2b, 0x3d, 0xff, 0x2d, 0x33, 0x46, 0xff, 0x16, 0x1e, 0x2e, 0xff, 0x4a, 0x56, 0x66, 0xff, 0x8a, 0x91, 0xa0, 0xff, 0x47, 0x4d, 0x61, 0xff, 0x25, 0x2d, 0x3c, 0xff, 0x28, 0x2f, 0x39, 0xff, 0x38, 0x3c, 0x48, 0xff, 0x21, 0x23, 0x2d, 0xff, 0x1f, 0x22, 0x2b, 0xff, 0x3c, 0x41, 0x4e, 0xff, 0x18, 0x1d, 0x2b, 0xff, 0x12, 0x16, 0x24, 0xff, 0x48, 0x4d, 0x5c, 0xff, 0x4f, 0x50, 0x63, 0xff, 0x20, 0x1f, 0x2b, 0xff, 0x00, 0x00, 0x05, 0xff, 0x1e, 0x23, 0x33, 0xff, 0x19, 0x21, 0x2d, 0xff, 0x1d, 0x27, 0x33, 0xff, 0x25, 0x2e, 0x3c, 0xff, 0x50, 0x55, 0x6a, 0xff, 0x7b, 0x7c, 0x96, 0xff, 0x75, 0x75, 0x8c, 0xff, 0x44, 0x49, 0x5a, 0xff, 0x27, 0x2d, 0x3d, 0xff, 0x4b, 0x53, 0x5a, 0xff, 0x5e, 0x63, 0x61, 0xff, 0x5a, 0x5c, 0x56, 0xff, 0x5c, 0x5d, 0x56, 0xff, 0x5d, 0x5e, 0x59, 0xff, 0x55, 0x5a, 0x51, 0xff, 0x54, 0x59, 0x4f, 0xff, 0x56, 0x5b, 0x51, 0xff, 0x59, 0x5d, 0x55, 0xff, 0x59, 0x5d, 0x54, 0xff, 0x54, 0x58, 0x4f, 0xff, 0x52, 0x56, 0x4d, 0xff, 0x53, 0x57, 0x4e, 0xff, 0x53, 0x57, 0x4e, 0xff, 0x50, 0x56, 0x4a, 0xff, 0x4c, 0x51, 0x49, 0xff, 0x49, 0x4c, 0x49, 0xff, 0x4c, 0x50, 0x4b, 0xff, 0x4c, 0x50, 0x4b, 0xff, 0x48, 0x4d, 0x48, 0xff, 0x49, 0x4a, 0x46, 0xff, 0x48, 0x4a, 0x45, 0xff, 0x4c, 0x52, 0x4c, 0xd1, 0x57, 0x5d, 0x57, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf6, 0xf6, 0xf6, 0x56, 0xf4, 0xf4, 0xf4, 0xf3, 0xf1, 0xf1, 0xf1, 0xff, 0xf1, 0xee, 0xed, 0xff, 0xef, 0xed, 0xec, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xef, 0xed, 0xec, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xe2, 0xe0, 0xe0, 0xff, 0xdf, 0xdd, 0xdd, 0xff, 0xdb, 0xdb, 0xda, 0xff, 0xd8, 0xd8, 0xd8, 0xff, 0xdb, 0xd8, 0xdd, 0xff, 0xa8, 0xab, 0xb1, 0xff, 0x93, 0x9d, 0xa5, 0xff, 0xcd, 0xd4, 0xde, 0xff, 0xc1, 0xc7, 0xd4, 0xff, 0x8f, 0x99, 0xad, 0xff, 0x87, 0x94, 0xa6, 0xff, 0x3d, 0x45, 0x4d, 0xff, 0x09, 0x0c, 0x15, 0xff, 0x1b, 0x20, 0x29, 0xff, 0x29, 0x2f, 0x38, 0xff, 0x37, 0x3d, 0x4d, 0xff, 0x2e, 0x33, 0x47, 0xff, 0x10, 0x18, 0x23, 0xff, 0x48, 0x4f, 0x5e, 0xff, 0x69, 0x6f, 0x82, 0xff, 0x2f, 0x35, 0x47, 0xff, 0x0d, 0x10, 0x1f, 0xff, 0x0c, 0x0e, 0x1b, 0xff, 0x35, 0x37, 0x46, 0xff, 0x35, 0x3b, 0x44, 0xff, 0x22, 0x28, 0x2e, 0xff, 0x34, 0x37, 0x44, 0xff, 0x54, 0x56, 0x65, 0xff, 0x1d, 0x1f, 0x2d, 0xff, 0x0c, 0x0f, 0x1c, 0xff, 0x2b, 0x2d, 0x3d, 0xff, 0x2f, 0x2e, 0x3d, 0xff, 0x0e, 0x0f, 0x14, 0xff, 0x1b, 0x1a, 0x24, 0xff, 0x24, 0x27, 0x36, 0xff, 0x3b, 0x43, 0x4f, 0xff, 0x3b, 0x46, 0x51, 0xff, 0x4d, 0x55, 0x67, 0xff, 0x47, 0x4b, 0x69, 0xff, 0x2c, 0x34, 0x4c, 0xff, 0x11, 0x19, 0x28, 0xff, 0x28, 0x2b, 0x3b, 0xff, 0x64, 0x67, 0x6e, 0xff, 0x64, 0x68, 0x60, 0xff, 0x5a, 0x5c, 0x51, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x5d, 0x5f, 0x58, 0xff, 0x5b, 0x5d, 0x56, 0xff, 0x56, 0x5a, 0x4e, 0xff, 0x58, 0x5b, 0x50, 0xff, 0x5c, 0x5e, 0x56, 0xff, 0x5c, 0x5e, 0x55, 0xff, 0x56, 0x59, 0x50, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x55, 0x57, 0x4f, 0xff, 0x55, 0x58, 0x4e, 0xff, 0x50, 0x56, 0x4b, 0xff, 0x4d, 0x52, 0x4a, 0xff, 0x4a, 0x4d, 0x49, 0xff, 0x4d, 0x51, 0x4c, 0xff, 0x4c, 0x50, 0x4b, 0xff, 0x48, 0x4d, 0x48, 0xff, 0x4a, 0x4b, 0x47, 0xff, 0x49, 0x4b, 0x47, 0xff, 0x50, 0x56, 0x50, 0xff, 0x58, 0x5f, 0x5c, 0xf3, 0x61, 0x6a, 0x6d, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xf7, 0xf7, 0xf7, 0x8a, 0xf6, 0xf6, 0xf6, 0xfe, 0xf7, 0xf7, 0xf8, 0xff, 0xf4, 0xf3, 0xf3, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf0, 0xee, 0xed, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xf0, 0xee, 0xed, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xec, 0xea, 0xe9, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xe5, 0xe3, 0xe3, 0xff, 0xe2, 0xe0, 0xe0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdc, 0xdd, 0xdf, 0xff, 0xe3, 0xe6, 0xe9, 0xff, 0xef, 0xee, 0xf3, 0xff, 0x98, 0x9b, 0xa5, 0xff, 0x29, 0x31, 0x3e, 0xff, 0x5a, 0x5f, 0x71, 0xff, 0x75, 0x7b, 0x8d, 0xff, 0x54, 0x61, 0x75, 0xff, 0x45, 0x56, 0x6b, 0xff, 0x19, 0x23, 0x2e, 0xff, 0x08, 0x09, 0x11, 0xff, 0x06, 0x09, 0x11, 0xff, 0x23, 0x26, 0x2e, 0xff, 0x44, 0x48, 0x57, 0xff, 0x2d, 0x30, 0x42, 0xff, 0x06, 0x0a, 0x0f, 0xff, 0x36, 0x3c, 0x45, 0xff, 0x46, 0x4b, 0x56, 0xff, 0x25, 0x2a, 0x34, 0xff, 0x1f, 0x24, 0x2e, 0xff, 0x0d, 0x13, 0x1c, 0xff, 0x14, 0x1a, 0x23, 0xff, 0x36, 0x3d, 0x44, 0xff, 0x1f, 0x26, 0x2d, 0xff, 0x26, 0x29, 0x37, 0xff, 0x4b, 0x4d, 0x5c, 0xff, 0x4a, 0x4d, 0x5b, 0xff, 0x12, 0x15, 0x23, 0xff, 0x14, 0x15, 0x25, 0xff, 0x25, 0x25, 0x30, 0xff, 0x10, 0x13, 0x15, 0xff, 0x0f, 0x11, 0x1a, 0xff, 0x13, 0x13, 0x23, 0xff, 0x34, 0x37, 0x45, 0xff, 0x4d, 0x54, 0x62, 0xff, 0x32, 0x37, 0x47, 0xff, 0x1e, 0x24, 0x31, 0xff, 0x25, 0x2a, 0x3a, 0xff, 0x18, 0x21, 0x2f, 0xff, 0x2f, 0x37, 0x48, 0xff, 0x5a, 0x5f, 0x6f, 0xff, 0x5d, 0x64, 0x62, 0xff, 0x57, 0x60, 0x57, 0xff, 0x55, 0x5f, 0x57, 0xff, 0x5a, 0x5f, 0x58, 0xff, 0x5d, 0x5e, 0x54, 0xff, 0x58, 0x5c, 0x4f, 0xff, 0x59, 0x5d, 0x51, 0xff, 0x5d, 0x5f, 0x57, 0xff, 0x5d, 0x60, 0x57, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x56, 0x58, 0x50, 0xff, 0x56, 0x59, 0x4f, 0xff, 0x50, 0x56, 0x4b, 0xff, 0x4d, 0x52, 0x4a, 0xff, 0x4a, 0x4d, 0x49, 0xff, 0x4d, 0x51, 0x4c, 0xff, 0x4c, 0x50, 0x4b, 0xff, 0x48, 0x4d, 0x48, 0xff, 0x4a, 0x4b, 0x47, 0xff, 0x49, 0x4b, 0x47, 0xff, 0x4f, 0x55, 0x4f, 0xff, 0x59, 0x61, 0x5d, 0xff, 0x64, 0x6d, 0x70, 0xfe, 0x6a, 0x75, 0x7c, 0x8b, 0x55, 0x55, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0b, 0xfa, 0xfa, 0xfa, 0xae, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf5, 0xf5, 0xf4, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xef, 0xed, 0xec, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xec, 0xea, 0xe9, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xe4, 0xe2, 0xe3, 0xff, 0xe3, 0xe0, 0xe1, 0xff, 0xe1, 0xdc, 0xdb, 0xff, 0xda, 0xd9, 0xdc, 0xff, 0xc6, 0xca, 0xd1, 0xff, 0xce, 0xd1, 0xdd, 0xff, 0xbc, 0xc1, 0xcc, 0xff, 0x4e, 0x53, 0x5d, 0xff, 0x1c, 0x1f, 0x2b, 0xff, 0x28, 0x2b, 0x38, 0xff, 0x48, 0x4f, 0x5d, 0xff, 0x26, 0x2e, 0x3c, 0xff, 0x11, 0x16, 0x1c, 0xff, 0x11, 0x13, 0x16, 0xff, 0x06, 0x09, 0x0d, 0xff, 0x1d, 0x1f, 0x23, 0xff, 0x34, 0x36, 0x3f, 0xff, 0x18, 0x19, 0x25, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x11, 0x16, 0xff, 0x37, 0x39, 0x41, 0xff, 0x24, 0x25, 0x2d, 0xff, 0x1f, 0x23, 0x2a, 0xff, 0x0f, 0x13, 0x1b, 0xff, 0x00, 0x02, 0x0a, 0xff, 0x23, 0x27, 0x2f, 0xff, 0x32, 0x37, 0x40, 0xff, 0x38, 0x3d, 0x49, 0xff, 0x1e, 0x22, 0x30, 0xff, 0x24, 0x28, 0x35, 0xff, 0x36, 0x3b, 0x48, 0xff, 0x2f, 0x31, 0x40, 0xff, 0x2c, 0x2d, 0x35, 0xff, 0x0d, 0x13, 0x11, 0xff, 0x16, 0x19, 0x1c, 0xff, 0x11, 0x10, 0x1d, 0xff, 0x1b, 0x1c, 0x29, 0xff, 0x3e, 0x41, 0x4f, 0xff, 0x20, 0x23, 0x30, 0xff, 0x1e, 0x24, 0x27, 0xff, 0x2d, 0x2f, 0x3a, 0xff, 0x1a, 0x25, 0x31, 0xff, 0x28, 0x32, 0x43, 0xff, 0x4b, 0x50, 0x64, 0xff, 0x4f, 0x57, 0x60, 0xff, 0x5e, 0x66, 0x6b, 0xff, 0x6a, 0x73, 0x7e, 0xff, 0x67, 0x6c, 0x71, 0xff, 0x5d, 0x60, 0x58, 0xff, 0x56, 0x5b, 0x4f, 0xff, 0x58, 0x5c, 0x51, 0xff, 0x5e, 0x60, 0x58, 0xff, 0x5d, 0x60, 0x57, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x56, 0x59, 0x50, 0xff, 0x56, 0x59, 0x50, 0xff, 0x50, 0x55, 0x4b, 0xff, 0x4c, 0x51, 0x4a, 0xff, 0x4a, 0x4d, 0x48, 0xff, 0x4c, 0x51, 0x4b, 0xff, 0x4c, 0x50, 0x4a, 0xff, 0x48, 0x4d, 0x48, 0xff, 0x4a, 0x4b, 0x47, 0xff, 0x49, 0x4b, 0x47, 0xff, 0x4f, 0x55, 0x4f, 0xff, 0x59, 0x60, 0x5d, 0xff, 0x63, 0x6c, 0x6f, 0xff, 0x67, 0x72, 0x79, 0xff, 0x6d, 0x77, 0x83, 0xaf, 0x73, 0x73, 0x8b, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x16, 0xfc, 0xfc, 0xfc, 0xc9, 0xfb, 0xfb, 0xfb, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf4, 0xf4, 0xf3, 0xff, 0xf3, 0xf0, 0xef, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xeb, 0xe9, 0xe7, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xe6, 0xe2, 0xe6, 0xff, 0xe4, 0xe0, 0xe3, 0xff, 0xea, 0xe4, 0xe4, 0xff, 0xdc, 0xd9, 0xdc, 0xff, 0xa0, 0xa3, 0xac, 0xff, 0x89, 0x91, 0xa4, 0xff, 0x83, 0x8e, 0x9f, 0xff, 0x67, 0x70, 0x7e, 0xff, 0x4c, 0x52, 0x5b, 0xff, 0x29, 0x2f, 0x38, 0xff, 0x29, 0x2e, 0x3b, 0xff, 0x21, 0x21, 0x2b, 0xff, 0x17, 0x18, 0x1e, 0xff, 0x09, 0x0c, 0x0b, 0xff, 0x00, 0x01, 0x01, 0xff, 0x13, 0x14, 0x15, 0xff, 0x20, 0x21, 0x22, 0xff, 0x0f, 0x11, 0x10, 0xff, 0x06, 0x08, 0x09, 0xff, 0x0b, 0x0c, 0x0f, 0xff, 0x1a, 0x19, 0x20, 0xff, 0x17, 0x15, 0x20, 0xff, 0x0e, 0x0c, 0x16, 0xff, 0x0e, 0x0d, 0x15, 0xff, 0x09, 0x07, 0x0f, 0xff, 0x0e, 0x0e, 0x17, 0xff, 0x2d, 0x2f, 0x39, 0xff, 0x39, 0x3f, 0x48, 0xff, 0x1b, 0x23, 0x2c, 0xff, 0x00, 0x03, 0x0d, 0xff, 0x15, 0x1c, 0x26, 0xff, 0x36, 0x3a, 0x45, 0xff, 0x32, 0x34, 0x3d, 0xff, 0x0f, 0x14, 0x18, 0xff, 0x0b, 0x10, 0x10, 0xff, 0x0f, 0x12, 0x16, 0xff, 0x18, 0x1c, 0x23, 0xff, 0x2b, 0x30, 0x3a, 0xff, 0x13, 0x15, 0x1f, 0xff, 0x0e, 0x11, 0x16, 0xff, 0x1c, 0x1e, 0x26, 0xff, 0x15, 0x1c, 0x27, 0xff, 0x2e, 0x36, 0x44, 0xff, 0x59, 0x60, 0x71, 0xff, 0x6b, 0x74, 0x81, 0xff, 0x6c, 0x74, 0x80, 0xff, 0x5d, 0x64, 0x78, 0xff, 0x5d, 0x62, 0x74, 0xff, 0x61, 0x67, 0x69, 0xff, 0x58, 0x5e, 0x56, 0xff, 0x58, 0x5b, 0x54, 0xff, 0x5e, 0x60, 0x58, 0xff, 0x5c, 0x5f, 0x56, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x58, 0x5b, 0x51, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x51, 0x55, 0x4f, 0xff, 0x4e, 0x52, 0x4c, 0xff, 0x4b, 0x4f, 0x49, 0xff, 0x4d, 0x53, 0x49, 0xff, 0x4d, 0x52, 0x49, 0xff, 0x4a, 0x4e, 0x49, 0xff, 0x4a, 0x4b, 0x47, 0xff, 0x49, 0x4b, 0x47, 0xff, 0x4f, 0x55, 0x4f, 0xff, 0x59, 0x61, 0x5d, 0xff, 0x63, 0x6c, 0x6f, 0xff, 0x68, 0x73, 0x7a, 0xff, 0x6e, 0x77, 0x83, 0xff, 0x72, 0x7c, 0x8c, 0xc9, 0x73, 0x7f, 0x96, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x1a, 0xfb, 0xfb, 0xfb, 0xd6, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf5, 0xf4, 0xf4, 0xff, 0xf4, 0xf1, 0xf0, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf3, 0xf1, 0xf0, 0xff, 0xf3, 0xf1, 0xf0, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xef, 0xed, 0xec, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xec, 0xea, 0xe9, 0xff, 0xe8, 0xe6, 0xe6, 0xff, 0xe7, 0xe5, 0xe5, 0xff, 0xe6, 0xe6, 0xea, 0xff, 0xe8, 0xeb, 0xf2, 0xff, 0xd7, 0xdc, 0xe7, 0xff, 0x99, 0x9f, 0xaf, 0xff, 0x65, 0x6d, 0x7f, 0xff, 0x4d, 0x55, 0x67, 0xff, 0x49, 0x4f, 0x5e, 0xff, 0x53, 0x55, 0x60, 0xff, 0x45, 0x49, 0x54, 0xff, 0x34, 0x39, 0x47, 0xff, 0x2e, 0x2f, 0x3a, 0xff, 0x15, 0x17, 0x1d, 0xff, 0x03, 0x04, 0x04, 0xff, 0x07, 0x08, 0x09, 0xff, 0x17, 0x19, 0x1a, 0xff, 0x16, 0x18, 0x19, 0xff, 0x06, 0x08, 0x09, 0xff, 0x05, 0x07, 0x08, 0xff, 0x03, 0x05, 0x05, 0xff, 0x00, 0x00, 0x02, 0xff, 0x0f, 0x0e, 0x16, 0xff, 0x0e, 0x0e, 0x14, 0xff, 0x09, 0x0a, 0x0d, 0xff, 0x0b, 0x0c, 0x0f, 0xff, 0x00, 0x00, 0x04, 0xff, 0x15, 0x16, 0x1c, 0xff, 0x21, 0x25, 0x2a, 0xff, 0x13, 0x18, 0x1e, 0xff, 0x04, 0x08, 0x11, 0xff, 0x0a, 0x0e, 0x16, 0xff, 0x21, 0x25, 0x2c, 0xff, 0x28, 0x2a, 0x33, 0xff, 0x38, 0x3a, 0x44, 0xff, 0x22, 0x24, 0x2e, 0xff, 0x24, 0x27, 0x2e, 0xff, 0x1a, 0x20, 0x2b, 0xff, 0x11, 0x1a, 0x27, 0xff, 0x12, 0x15, 0x1f, 0xff, 0x0e, 0x12, 0x17, 0xff, 0x14, 0x16, 0x1e, 0xff, 0x2f, 0x36, 0x42, 0xff, 0x4d, 0x55, 0x64, 0xff, 0x52, 0x59, 0x6a, 0xff, 0x63, 0x6b, 0x7a, 0xff, 0x5a, 0x62, 0x71, 0xff, 0x30, 0x39, 0x47, 0xff, 0x38, 0x3e, 0x4f, 0xff, 0x62, 0x66, 0x6d, 0xff, 0x64, 0x69, 0x65, 0xff, 0x5c, 0x5e, 0x59, 0xff, 0x5e, 0x61, 0x58, 0xff, 0x5d, 0x60, 0x57, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x53, 0x57, 0x52, 0xff, 0x51, 0x55, 0x4f, 0xff, 0x4d, 0x51, 0x4c, 0xff, 0x50, 0x55, 0x4b, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x4d, 0x51, 0x4c, 0xff, 0x4b, 0x4c, 0x48, 0xff, 0x49, 0x4b, 0x46, 0xff, 0x4f, 0x55, 0x4f, 0xff, 0x58, 0x60, 0x5c, 0xff, 0x62, 0x6b, 0x6e, 0xff, 0x69, 0x74, 0x7b, 0xff, 0x6e, 0x78, 0x84, 0xff, 0x70, 0x7a, 0x8b, 0xff, 0x73, 0x7e, 0x94, 0xd6, 0x75, 0x7f, 0x9c, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x20, 0xfb, 0xfb, 0xfb, 0xde, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf5, 0xf4, 0xf4, 0xff, 0xf5, 0xf2, 0xf1, 0xff, 0xf3, 0xf1, 0xf0, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf0, 0xee, 0xed, 0xff, 0xef, 0xed, 0xec, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xe9, 0xe7, 0xe5, 0xff, 0xea, 0xeb, 0xe9, 0xff, 0xd7, 0xdd, 0xe1, 0xff, 0xa8, 0xb3, 0xbf, 0xff, 0x9a, 0xa5, 0xb7, 0xff, 0x7b, 0x83, 0x96, 0xff, 0x41, 0x49, 0x59, 0xff, 0x37, 0x3d, 0x4c, 0xff, 0x2c, 0x2d, 0x3b, 0xff, 0x2a, 0x2a, 0x34, 0xff, 0x27, 0x29, 0x34, 0xff, 0x37, 0x3d, 0x4a, 0xff, 0x35, 0x37, 0x42, 0xff, 0x25, 0x26, 0x2d, 0xff, 0x04, 0x05, 0x06, 0xff, 0x06, 0x08, 0x08, 0xff, 0x0a, 0x0c, 0x0d, 0xff, 0x07, 0x09, 0x0a, 0xff, 0x06, 0x08, 0x09, 0xff, 0x02, 0x03, 0x04, 0xff, 0x02, 0x04, 0x03, 0xff, 0x01, 0x03, 0x03, 0xff, 0x0b, 0x0c, 0x10, 0xff, 0x15, 0x16, 0x18, 0xff, 0x04, 0x07, 0x07, 0xff, 0x09, 0x0c, 0x0b, 0xff, 0x13, 0x12, 0x15, 0xff, 0x12, 0x0f, 0x13, 0xff, 0x12, 0x14, 0x17, 0xff, 0x0a, 0x0d, 0x11, 0xff, 0x04, 0x06, 0x0c, 0xff, 0x0a, 0x0c, 0x11, 0xff, 0x23, 0x26, 0x2c, 0xff, 0x25, 0x29, 0x31, 0xff, 0x2f, 0x30, 0x3d, 0xff, 0x35, 0x36, 0x46, 0xff, 0x3a, 0x3f, 0x4a, 0xff, 0x34, 0x3d, 0x4b, 0xff, 0x3d, 0x46, 0x58, 0xff, 0x31, 0x34, 0x40, 0xff, 0x13, 0x16, 0x1b, 0xff, 0x1f, 0x21, 0x2a, 0xff, 0x28, 0x2f, 0x3a, 0xff, 0x1f, 0x27, 0x36, 0xff, 0x3a, 0x41, 0x51, 0xff, 0x49, 0x4e, 0x62, 0xff, 0x3f, 0x45, 0x57, 0xff, 0x27, 0x30, 0x39, 0xff, 0x2f, 0x35, 0x44, 0xff, 0x54, 0x56, 0x61, 0xff, 0x61, 0x65, 0x63, 0xff, 0x5c, 0x5f, 0x5a, 0xff, 0x5e, 0x62, 0x57, 0xff, 0x5d, 0x61, 0x56, 0xff, 0x5b, 0x5d, 0x55, 0xff, 0x5a, 0x5d, 0x54, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x52, 0x56, 0x50, 0xff, 0x4f, 0x53, 0x4d, 0xff, 0x4c, 0x50, 0x4a, 0xff, 0x4f, 0x54, 0x4a, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x4b, 0x4f, 0x4b, 0xff, 0x4a, 0x4c, 0x48, 0xff, 0x49, 0x4b, 0x46, 0xff, 0x50, 0x55, 0x4f, 0xff, 0x58, 0x5f, 0x5c, 0xff, 0x62, 0x6b, 0x6e, 0xff, 0x6a, 0x74, 0x7c, 0xff, 0x70, 0x7a, 0x85, 0xff, 0x72, 0x7b, 0x8d, 0xff, 0x75, 0x80, 0x97, 0xff, 0x77, 0x84, 0x9e, 0xde, 0x77, 0x87, 0x9f, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x1c, 0xfb, 0xfb, 0xfb, 0xdb, 0xfb, 0xfb, 0xfb, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf5, 0xf5, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xed, 0xeb, 0xe9, 0xff, 0xe4, 0xea, 0xe3, 0xff, 0x96, 0x9e, 0xa5, 0xff, 0x51, 0x5b, 0x6d, 0xff, 0x5a, 0x66, 0x76, 0xff, 0x3e, 0x46, 0x54, 0xff, 0x27, 0x2e, 0x38, 0xff, 0x2b, 0x2d, 0x34, 0xff, 0x14, 0x13, 0x19, 0xff, 0x0c, 0x0d, 0x12, 0xff, 0x1c, 0x1c, 0x24, 0xff, 0x38, 0x3a, 0x46, 0xff, 0x3b, 0x3d, 0x49, 0xff, 0x2b, 0x2d, 0x37, 0xff, 0x17, 0x18, 0x1c, 0xff, 0x05, 0x07, 0x07, 0xff, 0x02, 0x04, 0x03, 0xff, 0x03, 0x06, 0x06, 0xff, 0x06, 0x08, 0x09, 0xff, 0x03, 0x04, 0x05, 0xff, 0x06, 0x06, 0x06, 0xff, 0x06, 0x06, 0x05, 0xff, 0x08, 0x08, 0x08, 0xff, 0x0f, 0x0f, 0x0f, 0xff, 0x0b, 0x0b, 0x0c, 0xff, 0x08, 0x08, 0x08, 0xff, 0x14, 0x12, 0x14, 0xff, 0x0e, 0x0d, 0x0f, 0xff, 0x11, 0x10, 0x12, 0xff, 0x0f, 0x0d, 0x0f, 0xff, 0x03, 0x02, 0x04, 0xff, 0x0b, 0x0c, 0x11, 0xff, 0x2a, 0x2e, 0x36, 0xff, 0x18, 0x1c, 0x27, 0xff, 0x06, 0x09, 0x14, 0xff, 0x17, 0x1a, 0x26, 0xff, 0x1d, 0x23, 0x30, 0xff, 0x2f, 0x37, 0x46, 0xff, 0x37, 0x40, 0x53, 0xff, 0x40, 0x42, 0x51, 0xff, 0x1a, 0x1b, 0x26, 0xff, 0x20, 0x23, 0x2b, 0xff, 0x19, 0x1c, 0x29, 0xff, 0x12, 0x15, 0x24, 0xff, 0x2f, 0x32, 0x40, 0xff, 0x3a, 0x3c, 0x4e, 0xff, 0x2c, 0x2e, 0x3f, 0xff, 0x1f, 0x24, 0x2e, 0xff, 0x20, 0x23, 0x2f, 0xff, 0x44, 0x45, 0x51, 0xff, 0x63, 0x69, 0x67, 0xff, 0x5d, 0x64, 0x5d, 0xff, 0x5d, 0x66, 0x58, 0xff, 0x5d, 0x64, 0x51, 0xff, 0x5b, 0x5e, 0x53, 0xff, 0x59, 0x5f, 0x53, 0xff, 0x58, 0x5f, 0x52, 0xff, 0x56, 0x5d, 0x50, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x4e, 0x53, 0x4a, 0xff, 0x4d, 0x52, 0x49, 0xff, 0x50, 0x55, 0x4b, 0xff, 0x4d, 0x52, 0x49, 0xff, 0x48, 0x4d, 0x48, 0xff, 0x4a, 0x4b, 0x47, 0xff, 0x4a, 0x4c, 0x48, 0xff, 0x4f, 0x55, 0x4e, 0xff, 0x57, 0x5e, 0x5b, 0xff, 0x64, 0x6c, 0x71, 0xff, 0x6b, 0x74, 0x7e, 0xff, 0x71, 0x79, 0x88, 0xff, 0x72, 0x7c, 0x8f, 0xff, 0x75, 0x80, 0x99, 0xff, 0x79, 0x85, 0xa1, 0xff, 0x79, 0x87, 0xa6, 0xdc, 0x76, 0x7f, 0xa3, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x15, 0xfa, 0xfa, 0xfa, 0xd6, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf5, 0xf5, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xee, 0xeb, 0xea, 0xff, 0xee, 0xeb, 0xea, 0xff, 0xe9, 0xee, 0xe9, 0xff, 0x94, 0x9b, 0xa6, 0xff, 0x41, 0x4b, 0x5f, 0xff, 0x43, 0x4e, 0x5e, 0xff, 0x35, 0x3d, 0x4a, 0xff, 0x13, 0x19, 0x23, 0xff, 0x15, 0x17, 0x1e, 0xff, 0x18, 0x17, 0x1c, 0xff, 0x09, 0x09, 0x0e, 0xff, 0x22, 0x23, 0x2b, 0xff, 0x34, 0x37, 0x42, 0xff, 0x29, 0x2c, 0x37, 0xff, 0x2d, 0x2f, 0x3a, 0xff, 0x45, 0x44, 0x4b, 0xff, 0x22, 0x23, 0x27, 0xff, 0x11, 0x13, 0x14, 0xff, 0x01, 0x03, 0x04, 0xff, 0x05, 0x07, 0x08, 0xff, 0x04, 0x05, 0x07, 0xff, 0x08, 0x08, 0x08, 0xff, 0x0e, 0x0e, 0x0e, 0xff, 0x05, 0x05, 0x04, 0xff, 0x06, 0x06, 0x06, 0xff, 0x0b, 0x0b, 0x0b, 0xff, 0x09, 0x0a, 0x09, 0xff, 0x08, 0x08, 0x09, 0xff, 0x0e, 0x0d, 0x0f, 0xff, 0x0d, 0x0c, 0x0e, 0xff, 0x06, 0x05, 0x06, 0xff, 0x04, 0x03, 0x05, 0xff, 0x0b, 0x0d, 0x12, 0xff, 0x25, 0x27, 0x30, 0xff, 0x1d, 0x1e, 0x28, 0xff, 0x0a, 0x0b, 0x15, 0xff, 0x15, 0x16, 0x1f, 0xff, 0x1c, 0x1c, 0x25, 0xff, 0x20, 0x24, 0x2f, 0xff, 0x14, 0x19, 0x27, 0xff, 0x30, 0x31, 0x40, 0xff, 0x30, 0x32, 0x3e, 0xff, 0x2b, 0x2e, 0x36, 0xff, 0x12, 0x16, 0x20, 0xff, 0x15, 0x19, 0x24, 0xff, 0x24, 0x28, 0x33, 0xff, 0x1b, 0x1f, 0x2a, 0xff, 0x15, 0x19, 0x23, 0xff, 0x0b, 0x0f, 0x19, 0xff, 0x16, 0x19, 0x25, 0xff, 0x48, 0x49, 0x54, 0xff, 0x61, 0x66, 0x69, 0xff, 0x60, 0x67, 0x65, 0xff, 0x6c, 0x74, 0x70, 0xff, 0x66, 0x6d, 0x62, 0xff, 0x59, 0x5f, 0x52, 0xff, 0x55, 0x5c, 0x4f, 0xff, 0x57, 0x5e, 0x51, 0xff, 0x57, 0x5f, 0x52, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x4f, 0x54, 0x4a, 0xff, 0x51, 0x56, 0x4c, 0xff, 0x4d, 0x52, 0x4a, 0xff, 0x49, 0x4e, 0x49, 0xff, 0x4c, 0x4d, 0x49, 0xff, 0x4c, 0x4e, 0x49, 0xff, 0x50, 0x56, 0x4f, 0xff, 0x58, 0x5f, 0x5d, 0xff, 0x65, 0x6d, 0x72, 0xff, 0x6a, 0x74, 0x7e, 0xff, 0x70, 0x78, 0x87, 0xff, 0x72, 0x7b, 0x8f, 0xff, 0x75, 0x80, 0x99, 0xff, 0x79, 0x85, 0xa1, 0xff, 0x7a, 0x88, 0xa7, 0xff, 0x77, 0x83, 0xa6, 0xd7, 0x6d, 0x79, 0x9d, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0a, 0xfb, 0xfb, 0xfb, 0xc8, 0xfa, 0xfa, 0xfa, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf5, 0xf5, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xed, 0xea, 0xe9, 0xff, 0xf0, 0xee, 0xec, 0xff, 0xec, 0xf0, 0xed, 0xff, 0xa1, 0xa6, 0xb4, 0xff, 0x4f, 0x56, 0x6f, 0xff, 0x3b, 0x45, 0x56, 0xff, 0x34, 0x3c, 0x48, 0xff, 0x21, 0x27, 0x32, 0xff, 0x0c, 0x0e, 0x16, 0xff, 0x0b, 0x0a, 0x0f, 0xff, 0x0c, 0x0b, 0x10, 0xff, 0x20, 0x21, 0x29, 0xff, 0x2c, 0x2e, 0x3a, 0xff, 0x12, 0x15, 0x21, 0xff, 0x1a, 0x1b, 0x27, 0xff, 0x29, 0x27, 0x31, 0xff, 0x2c, 0x2c, 0x33, 0xff, 0x35, 0x36, 0x39, 0xff, 0x16, 0x18, 0x19, 0xff, 0x05, 0x06, 0x07, 0xff, 0x01, 0x02, 0x04, 0xff, 0x0c, 0x0c, 0x0c, 0xff, 0x04, 0x04, 0x05, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x04, 0x04, 0x04, 0xff, 0x09, 0x0a, 0x09, 0xff, 0x08, 0x08, 0x09, 0xff, 0x0d, 0x0b, 0x0e, 0xff, 0x07, 0x06, 0x08, 0xff, 0x05, 0x04, 0x06, 0xff, 0x07, 0x06, 0x08, 0xff, 0x04, 0x06, 0x0c, 0xff, 0x0d, 0x10, 0x18, 0xff, 0x26, 0x25, 0x2c, 0xff, 0x1e, 0x1e, 0x25, 0xff, 0x0e, 0x0e, 0x13, 0xff, 0x12, 0x0f, 0x12, 0xff, 0x0d, 0x0d, 0x13, 0xff, 0x07, 0x08, 0x11, 0xff, 0x25, 0x26, 0x34, 0xff, 0x3f, 0x41, 0x4d, 0xff, 0x33, 0x36, 0x3e, 0xff, 0x22, 0x28, 0x2f, 0xff, 0x11, 0x17, 0x1e, 0xff, 0x19, 0x1f, 0x26, 0xff, 0x1c, 0x24, 0x27, 0xff, 0x14, 0x1a, 0x1f, 0xff, 0x07, 0x0c, 0x17, 0xff, 0x20, 0x23, 0x2f, 0xff, 0x49, 0x4b, 0x55, 0xff, 0x61, 0x63, 0x6b, 0xff, 0x6c, 0x70, 0x7a, 0xff, 0x6b, 0x6e, 0x82, 0xff, 0x6a, 0x6c, 0x7a, 0xff, 0x61, 0x68, 0x60, 0xff, 0x59, 0x60, 0x51, 0xff, 0x5a, 0x60, 0x53, 0xff, 0x58, 0x5f, 0x52, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x52, 0x57, 0x4d, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x4a, 0x4f, 0x4a, 0xff, 0x4c, 0x4e, 0x4a, 0xff, 0x4d, 0x4e, 0x4a, 0xff, 0x51, 0x56, 0x50, 0xff, 0x59, 0x61, 0x5e, 0xff, 0x65, 0x6e, 0x73, 0xff, 0x69, 0x72, 0x7c, 0xff, 0x6e, 0x77, 0x86, 0xff, 0x72, 0x7b, 0x8f, 0xff, 0x75, 0x80, 0x99, 0xff, 0x79, 0x85, 0xa1, 0xff, 0x7a, 0x88, 0xa7, 0xff, 0x78, 0x84, 0xa6, 0xff, 0x6e, 0x7a, 0x9f, 0xc8, 0x66, 0x7f, 0x99, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xf9, 0xf9, 0xf9, 0xb1, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf5, 0xf5, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf7, 0xf5, 0xf4, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf4, 0xf1, 0xf1, 0xff, 0xf0, 0xef, 0xed, 0xff, 0xef, 0xee, 0xeb, 0xff, 0xf2, 0xec, 0xed, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xbe, 0xc2, 0xc8, 0xff, 0x86, 0x8a, 0x9a, 0xff, 0x4e, 0x55, 0x6f, 0xff, 0x34, 0x3b, 0x4c, 0xff, 0x13, 0x19, 0x22, 0xff, 0x15, 0x1c, 0x27, 0xff, 0x21, 0x25, 0x2f, 0xff, 0x0a, 0x0c, 0x11, 0xff, 0x09, 0x09, 0x0b, 0xff, 0x16, 0x15, 0x19, 0xff, 0x19, 0x16, 0x20, 0xff, 0x16, 0x13, 0x1d, 0xff, 0x0c, 0x0a, 0x12, 0xff, 0x08, 0x07, 0x0a, 0xff, 0x1a, 0x19, 0x1c, 0xff, 0x1b, 0x1a, 0x1d, 0xff, 0x2c, 0x2d, 0x31, 0xff, 0x25, 0x26, 0x2a, 0xff, 0x03, 0x03, 0x07, 0xff, 0x07, 0x06, 0x09, 0xff, 0x05, 0x04, 0x05, 0xff, 0x03, 0x02, 0x04, 0xff, 0x03, 0x02, 0x04, 0xff, 0x0a, 0x09, 0x0a, 0xff, 0x0a, 0x09, 0x0b, 0xff, 0x03, 0x02, 0x03, 0xff, 0x06, 0x06, 0x07, 0xff, 0x0a, 0x0a, 0x0a, 0xff, 0x08, 0x07, 0x08, 0xff, 0x06, 0x05, 0x08, 0xff, 0x05, 0x04, 0x07, 0xff, 0x07, 0x08, 0x0a, 0xff, 0x0f, 0x11, 0x12, 0xff, 0x19, 0x1b, 0x1c, 0xff, 0x0e, 0x0f, 0x11, 0xff, 0x03, 0x03, 0x06, 0xff, 0x06, 0x06, 0x0a, 0xff, 0x03, 0x04, 0x08, 0xff, 0x1e, 0x1d, 0x27, 0xff, 0x37, 0x36, 0x41, 0xff, 0x28, 0x27, 0x32, 0xff, 0x14, 0x18, 0x21, 0xff, 0x0d, 0x13, 0x1c, 0xff, 0x16, 0x1a, 0x24, 0xff, 0x24, 0x2b, 0x33, 0xff, 0x26, 0x2d, 0x35, 0xff, 0x09, 0x0c, 0x16, 0xff, 0x16, 0x18, 0x22, 0xff, 0x41, 0x44, 0x4e, 0xff, 0x63, 0x68, 0x75, 0xff, 0x63, 0x6a, 0x7a, 0xff, 0x48, 0x4d, 0x63, 0xff, 0x58, 0x5b, 0x6c, 0xff, 0x63, 0x68, 0x66, 0xff, 0x5a, 0x5f, 0x54, 0xff, 0x5a, 0x5f, 0x55, 0xff, 0x58, 0x5e, 0x54, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x51, 0x56, 0x4c, 0xff, 0x4d, 0x52, 0x49, 0xff, 0x4b, 0x4f, 0x4b, 0xff, 0x49, 0x4b, 0x47, 0xff, 0x4b, 0x4d, 0x48, 0xff, 0x51, 0x57, 0x51, 0xff, 0x5a, 0x61, 0x5f, 0xff, 0x65, 0x6d, 0x72, 0xff, 0x6a, 0x73, 0x7d, 0xff, 0x6c, 0x77, 0x85, 0xff, 0x6f, 0x7c, 0x8d, 0xff, 0x77, 0x81, 0x98, 0xff, 0x7b, 0x85, 0xa0, 0xff, 0x78, 0x87, 0xa4, 0xff, 0x77, 0x83, 0xa6, 0xff, 0x71, 0x7c, 0xa4, 0xff, 0x6d, 0x79, 0x9e, 0xb1, 0x55, 0x55, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf7, 0xf7, 0xf7, 0x8d, 0xf9, 0xf9, 0xf9, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf5, 0xf5, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf7, 0xf5, 0xf4, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf3, 0xf1, 0xf0, 0xff, 0xf4, 0xf3, 0xf1, 0xff, 0xf0, 0xee, 0xec, 0xff, 0xfb, 0xf5, 0xf6, 0xff, 0xd3, 0xd6, 0xd8, 0xff, 0x6d, 0x74, 0x7f, 0xff, 0x4b, 0x52, 0x61, 0xff, 0x51, 0x58, 0x6c, 0xff, 0x3c, 0x40, 0x4e, 0xff, 0x0e, 0x11, 0x1a, 0xff, 0x0f, 0x13, 0x1f, 0xff, 0x21, 0x25, 0x2f, 0xff, 0x08, 0x0a, 0x11, 0xff, 0x06, 0x06, 0x08, 0xff, 0x05, 0x03, 0x06, 0xff, 0x08, 0x06, 0x0b, 0xff, 0x10, 0x0d, 0x13, 0xff, 0x0a, 0x08, 0x0c, 0xff, 0x0c, 0x0b, 0x0c, 0xff, 0x0f, 0x0e, 0x0f, 0xff, 0x0b, 0x0a, 0x0c, 0xff, 0x0c, 0x0d, 0x10, 0xff, 0x20, 0x22, 0x24, 0xff, 0x24, 0x26, 0x29, 0xff, 0x28, 0x28, 0x2a, 0xff, 0x13, 0x12, 0x14, 0xff, 0x02, 0x01, 0x03, 0xff, 0x0a, 0x09, 0x0b, 0xff, 0x0b, 0x0a, 0x0c, 0xff, 0x04, 0x03, 0x06, 0xff, 0x00, 0x00, 0x00, 0xff, 0x04, 0x04, 0x03, 0xff, 0x0d, 0x0e, 0x0d, 0xff, 0x09, 0x09, 0x09, 0xff, 0x04, 0x03, 0x05, 0xff, 0x07, 0x05, 0x06, 0xff, 0x0a, 0x0a, 0x0b, 0xff, 0x07, 0x0a, 0x0a, 0xff, 0x07, 0x0a, 0x0a, 0xff, 0x0d, 0x0f, 0x10, 0xff, 0x01, 0x02, 0x06, 0xff, 0x07, 0x08, 0x0b, 0xff, 0x0a, 0x0b, 0x0f, 0xff, 0x0b, 0x0a, 0x12, 0xff, 0x19, 0x18, 0x20, 0xff, 0x1a, 0x18, 0x21, 0xff, 0x0e, 0x10, 0x17, 0xff, 0x14, 0x18, 0x1f, 0xff, 0x11, 0x13, 0x1a, 0xff, 0x05, 0x0b, 0x12, 0xff, 0x1c, 0x22, 0x29, 0xff, 0x16, 0x17, 0x1e, 0xff, 0x05, 0x06, 0x0e, 0xff, 0x2f, 0x33, 0x3e, 0xff, 0x58, 0x5e, 0x6d, 0xff, 0x48, 0x50, 0x5f, 0xff, 0x33, 0x38, 0x4b, 0xff, 0x4f, 0x53, 0x61, 0xff, 0x5c, 0x62, 0x5f, 0xff, 0x56, 0x5b, 0x51, 0xff, 0x56, 0x5a, 0x52, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x51, 0x56, 0x4c, 0xff, 0x4d, 0x52, 0x49, 0xff, 0x4b, 0x4f, 0x4b, 0xff, 0x49, 0x4a, 0x46, 0xff, 0x4b, 0x4d, 0x48, 0xff, 0x51, 0x57, 0x51, 0xff, 0x5a, 0x61, 0x5f, 0xff, 0x65, 0x6d, 0x72, 0xff, 0x6b, 0x74, 0x7f, 0xff, 0x6e, 0x79, 0x87, 0xff, 0x71, 0x7f, 0x8f, 0xff, 0x79, 0x83, 0x9a, 0xff, 0x7c, 0x85, 0xa0, 0xff, 0x79, 0x88, 0xa4, 0xff, 0x77, 0x83, 0xa7, 0xff, 0x73, 0x7d, 0xa6, 0xff, 0x6e, 0x79, 0xa0, 0xff, 0x6c, 0x77, 0x9d, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf6, 0xf6, 0xf6, 0x55, 0xf7, 0xf7, 0xf7, 0xfe, 0xf9, 0xf9, 0xf9, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf5, 0xf5, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xf2, 0xf0, 0xee, 0xff, 0xfe, 0xf7, 0xf8, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0x53, 0x60, 0x6a, 0xff, 0x1c, 0x25, 0x34, 0xff, 0x28, 0x2d, 0x39, 0xff, 0x2d, 0x2e, 0x3a, 0xff, 0x14, 0x14, 0x20, 0xff, 0x19, 0x18, 0x26, 0xff, 0x1e, 0x20, 0x2b, 0xff, 0x11, 0x13, 0x19, 0xff, 0x07, 0x06, 0x0a, 0xff, 0x02, 0x00, 0x02, 0xff, 0x01, 0x01, 0x03, 0xff, 0x08, 0x08, 0x0a, 0xff, 0x0a, 0x09, 0x0b, 0xff, 0x06, 0x04, 0x07, 0xff, 0x06, 0x04, 0x07, 0xff, 0x09, 0x08, 0x0a, 0xff, 0x00, 0x03, 0x02, 0xff, 0x17, 0x1a, 0x19, 0xff, 0x17, 0x1a, 0x19, 0xff, 0x13, 0x12, 0x13, 0xff, 0x19, 0x18, 0x1a, 0xff, 0x11, 0x10, 0x12, 0xff, 0x07, 0x06, 0x08, 0xff, 0x05, 0x04, 0x06, 0xff, 0x05, 0x05, 0x06, 0xff, 0x04, 0x04, 0x04, 0xff, 0x04, 0x03, 0x03, 0xff, 0x08, 0x08, 0x07, 0xff, 0x06, 0x05, 0x06, 0xff, 0x03, 0x02, 0x04, 0xff, 0x05, 0x03, 0x04, 0xff, 0x02, 0x02, 0x03, 0xff, 0x02, 0x04, 0x04, 0xff, 0x03, 0x05, 0x05, 0xff, 0x0a, 0x0b, 0x0c, 0xff, 0x09, 0x0a, 0x0e, 0xff, 0x06, 0x07, 0x0b, 0xff, 0x04, 0x05, 0x08, 0xff, 0x06, 0x07, 0x0a, 0xff, 0x0c, 0x0d, 0x11, 0xff, 0x09, 0x0a, 0x0e, 0xff, 0x0e, 0x0f, 0x13, 0xff, 0x0e, 0x0f, 0x12, 0xff, 0x0a, 0x0b, 0x0e, 0xff, 0x09, 0x0c, 0x10, 0xff, 0x13, 0x17, 0x1b, 0xff, 0x1f, 0x1e, 0x20, 0xff, 0x12, 0x11, 0x18, 0xff, 0x2a, 0x2e, 0x3a, 0xff, 0x46, 0x4b, 0x59, 0xff, 0x49, 0x50, 0x60, 0xff, 0x49, 0x4e, 0x62, 0xff, 0x57, 0x5a, 0x69, 0xff, 0x5a, 0x60, 0x5d, 0xff, 0x56, 0x5b, 0x51, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x51, 0x56, 0x4c, 0xff, 0x4d, 0x52, 0x49, 0xff, 0x4b, 0x4f, 0x4b, 0xff, 0x49, 0x4a, 0x46, 0xff, 0x4b, 0x4d, 0x48, 0xff, 0x51, 0x57, 0x51, 0xff, 0x59, 0x61, 0x5e, 0xff, 0x65, 0x6d, 0x73, 0xff, 0x6d, 0x76, 0x80, 0xff, 0x71, 0x7c, 0x8a, 0xff, 0x75, 0x82, 0x92, 0xff, 0x7b, 0x84, 0x9b, 0xff, 0x7c, 0x86, 0xa1, 0xff, 0x7a, 0x89, 0xa5, 0xff, 0x78, 0x84, 0xa8, 0xff, 0x74, 0x7e, 0xa7, 0xff, 0x6f, 0x7a, 0xa1, 0xff, 0x6f, 0x7a, 0x9f, 0xfe, 0x6f, 0x7b, 0xa2, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf2, 0xf2, 0xf2, 0x29, 0xf6, 0xf6, 0xf6, 0xf3, 0xf8, 0xf8, 0xf8, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf5, 0xf6, 0xf6, 0xff, 0xf5, 0xf5, 0xf4, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf3, 0xf1, 0xef, 0xff, 0xd7, 0xd4, 0xd4, 0xff, 0xde, 0xdd, 0xdd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xe5, 0xec, 0xff, 0x48, 0x50, 0x60, 0xff, 0x02, 0x0a, 0x12, 0xff, 0x0d, 0x10, 0x14, 0xff, 0x1d, 0x1c, 0x25, 0xff, 0x18, 0x17, 0x22, 0xff, 0x11, 0x10, 0x18, 0xff, 0x19, 0x1a, 0x21, 0xff, 0x15, 0x16, 0x1b, 0xff, 0x09, 0x0a, 0x0b, 0xff, 0x07, 0x07, 0x08, 0xff, 0x04, 0x03, 0x04, 0xff, 0x03, 0x03, 0x03, 0xff, 0x07, 0x07, 0x07, 0xff, 0x06, 0x06, 0x07, 0xff, 0x07, 0x06, 0x07, 0xff, 0x06, 0x05, 0x06, 0xff, 0x0c, 0x0b, 0x0d, 0xff, 0x0c, 0x0c, 0x0d, 0xff, 0x07, 0x06, 0x08, 0xff, 0x03, 0x02, 0x04, 0xff, 0x08, 0x06, 0x08, 0xff, 0x0f, 0x0c, 0x0c, 0xff, 0x07, 0x04, 0x04, 0xff, 0x09, 0x07, 0x07, 0xff, 0x13, 0x10, 0x10, 0xff, 0x07, 0x06, 0x06, 0xff, 0x00, 0x00, 0x00, 0xff, 0x04, 0x04, 0x04, 0xff, 0x04, 0x04, 0x03, 0xff, 0x06, 0x05, 0x06, 0xff, 0x04, 0x02, 0x05, 0xff, 0x00, 0x00, 0x02, 0xff, 0x05, 0x07, 0x09, 0xff, 0x0b, 0x0d, 0x0f, 0xff, 0x0c, 0x0e, 0x10, 0xff, 0x1b, 0x1a, 0x1d, 0xff, 0x0b, 0x0b, 0x0e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x07, 0x06, 0x07, 0xff, 0x05, 0x06, 0x06, 0xff, 0x00, 0x01, 0x01, 0xff, 0x07, 0x0c, 0x0e, 0xff, 0x11, 0x14, 0x18, 0xff, 0x0b, 0x0c, 0x10, 0xff, 0x09, 0x0a, 0x0c, 0xff, 0x12, 0x14, 0x17, 0xff, 0x20, 0x1e, 0x24, 0xff, 0x20, 0x1f, 0x28, 0xff, 0x28, 0x2a, 0x37, 0xff, 0x37, 0x3a, 0x4a, 0xff, 0x36, 0x3c, 0x4d, 0xff, 0x36, 0x3a, 0x4c, 0xff, 0x4b, 0x4e, 0x5a, 0xff, 0x5b, 0x60, 0x5c, 0xff, 0x58, 0x5d, 0x53, 0xff, 0x59, 0x5d, 0x54, 0xff, 0x58, 0x5d, 0x54, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x51, 0x57, 0x4d, 0xff, 0x52, 0x56, 0x4e, 0xff, 0x4e, 0x52, 0x4b, 0xff, 0x4c, 0x50, 0x4b, 0xff, 0x49, 0x4c, 0x44, 0xff, 0x4b, 0x4f, 0x46, 0xff, 0x52, 0x58, 0x51, 0xff, 0x59, 0x62, 0x5e, 0xff, 0x66, 0x6e, 0x74, 0xff, 0x6e, 0x76, 0x81, 0xff, 0x72, 0x7e, 0x8b, 0xff, 0x72, 0x83, 0x92, 0xff, 0x7b, 0x87, 0x9d, 0xff, 0x7d, 0x88, 0xa4, 0xff, 0x7b, 0x89, 0xa6, 0xff, 0x79, 0x85, 0xa7, 0xff, 0x73, 0x7f, 0xa3, 0xff, 0x72, 0x7e, 0xa2, 0xff, 0x71, 0x7c, 0xa1, 0xff, 0x70, 0x7d, 0xa0, 0xf4, 0x73, 0x79, 0x9d, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xf5, 0xf5, 0xf5, 0xd1, 0xf7, 0xf7, 0xf7, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf4, 0xf5, 0xf3, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf7, 0xf5, 0xf4, 0xff, 0xf3, 0xf1, 0xf0, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xdf, 0xdc, 0xdc, 0xff, 0xb4, 0xb4, 0xb5, 0xff, 0xa4, 0xa7, 0xa6, 0xff, 0x73, 0x73, 0x7f, 0xff, 0x3d, 0x41, 0x55, 0xff, 0x06, 0x0d, 0x12, 0xff, 0x06, 0x08, 0x0a, 0xff, 0x21, 0x1f, 0x27, 0xff, 0x18, 0x17, 0x1f, 0xff, 0x0c, 0x0d, 0x0f, 0xff, 0x0c, 0x0c, 0x10, 0xff, 0x0c, 0x0d, 0x10, 0xff, 0x09, 0x0d, 0x0d, 0xff, 0x07, 0x09, 0x09, 0xff, 0x09, 0x09, 0x09, 0xff, 0x04, 0x04, 0x03, 0xff, 0x01, 0x01, 0x01, 0xff, 0x02, 0x02, 0x02, 0xff, 0x07, 0x08, 0x07, 0xff, 0x05, 0x05, 0x05, 0xff, 0x08, 0x07, 0x09, 0xff, 0x09, 0x08, 0x0a, 0xff, 0x07, 0x06, 0x08, 0xff, 0x09, 0x09, 0x0c, 0xff, 0x06, 0x06, 0x0a, 0xff, 0x02, 0x03, 0x08, 0xff, 0x0e, 0x0e, 0x15, 0xff, 0x10, 0x0f, 0x18, 0xff, 0x0d, 0x0b, 0x14, 0xff, 0x04, 0x07, 0x0b, 0xff, 0x02, 0x09, 0x08, 0xff, 0x06, 0x0b, 0x0c, 0xff, 0x09, 0x0e, 0x0f, 0xff, 0x06, 0x0b, 0x0d, 0xff, 0x07, 0x0a, 0x10, 0xff, 0x0d, 0x10, 0x1b, 0xff, 0x18, 0x1e, 0x2c, 0xff, 0x18, 0x1e, 0x2c, 0xff, 0x0f, 0x15, 0x22, 0xff, 0x0e, 0x14, 0x1d, 0xff, 0x1a, 0x1f, 0x28, 0xff, 0x09, 0x0e, 0x18, 0xff, 0x04, 0x04, 0x0c, 0xff, 0x08, 0x09, 0x0c, 0xff, 0x02, 0x05, 0x04, 0xff, 0x05, 0x0c, 0x0d, 0xff, 0x15, 0x1b, 0x1f, 0xff, 0x17, 0x18, 0x1d, 0xff, 0x0d, 0x0e, 0x10, 0xff, 0x10, 0x12, 0x13, 0xff, 0x1d, 0x1c, 0x24, 0xff, 0x18, 0x18, 0x21, 0xff, 0x0e, 0x0f, 0x1a, 0xff, 0x26, 0x29, 0x37, 0xff, 0x35, 0x3b, 0x49, 0xff, 0x31, 0x35, 0x44, 0xff, 0x4b, 0x4d, 0x56, 0xff, 0x5e, 0x63, 0x5d, 0xff, 0x57, 0x5c, 0x52, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x58, 0x5d, 0x54, 0xff, 0x53, 0x58, 0x4f, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x52, 0x56, 0x4f, 0xff, 0x4e, 0x52, 0x4c, 0xff, 0x4c, 0x50, 0x4b, 0xff, 0x49, 0x4e, 0x43, 0xff, 0x4d, 0x51, 0x46, 0xff, 0x53, 0x59, 0x53, 0xff, 0x5b, 0x62, 0x60, 0xff, 0x66, 0x6e, 0x74, 0xff, 0x6d, 0x75, 0x80, 0xff, 0x70, 0x7c, 0x8a, 0xff, 0x71, 0x82, 0x92, 0xff, 0x7a, 0x86, 0x9d, 0xff, 0x7e, 0x89, 0xa5, 0xff, 0x7e, 0x8d, 0xaa, 0xff, 0x7c, 0x89, 0xa9, 0xff, 0x76, 0x82, 0xa4, 0xff, 0x73, 0x7f, 0xa3, 0xff, 0x71, 0x7d, 0xa1, 0xff, 0x71, 0x7e, 0xa1, 0xff, 0x71, 0x7b, 0x9f, 0xd1, 0x6d, 0x6d, 0x91, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf4, 0xf4, 0xf4, 0x8e, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf4, 0xf5, 0xf5, 0xff, 0xf4, 0xf3, 0xf3, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf5, 0xf3, 0xf3, 0xff, 0xc8, 0xc7, 0xc8, 0xff, 0x89, 0x8c, 0x8a, 0xff, 0x5c, 0x5e, 0x6a, 0xff, 0x41, 0x47, 0x5b, 0xff, 0x15, 0x1d, 0x22, 0xff, 0x0a, 0x0b, 0x0e, 0xff, 0x17, 0x15, 0x1c, 0xff, 0x0a, 0x0b, 0x0f, 0xff, 0x0d, 0x10, 0x0d, 0xff, 0x04, 0x05, 0x08, 0xff, 0x06, 0x06, 0x0c, 0xff, 0x06, 0x09, 0x09, 0xff, 0x01, 0x03, 0x02, 0xff, 0x09, 0x09, 0x09, 0xff, 0x0b, 0x0a, 0x0a, 0xff, 0x04, 0x03, 0x03, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x02, 0x01, 0xff, 0x06, 0x06, 0x05, 0xff, 0x06, 0x05, 0x07, 0xff, 0x05, 0x03, 0x04, 0xff, 0x08, 0x06, 0x05, 0xff, 0x08, 0x08, 0x0a, 0xff, 0x03, 0x07, 0x0f, 0xff, 0x07, 0x11, 0x1d, 0xff, 0x21, 0x28, 0x3b, 0xff, 0x21, 0x25, 0x3a, 0xff, 0x0f, 0x13, 0x28, 0xff, 0x1e, 0x25, 0x37, 0xff, 0x19, 0x21, 0x31, 0xff, 0x01, 0x09, 0x19, 0xff, 0x12, 0x19, 0x2b, 0xff, 0x1a, 0x21, 0x35, 0xff, 0x24, 0x28, 0x3f, 0xff, 0x41, 0x48, 0x65, 0xff, 0x40, 0x4d, 0x6f, 0xff, 0x32, 0x3f, 0x5f, 0xff, 0x25, 0x31, 0x51, 0xff, 0x1d, 0x26, 0x48, 0xff, 0x29, 0x32, 0x53, 0xff, 0x28, 0x31, 0x53, 0xff, 0x1b, 0x29, 0x46, 0xff, 0x17, 0x23, 0x37, 0xff, 0x06, 0x0e, 0x19, 0xff, 0x00, 0x04, 0x04, 0xff, 0x0a, 0x0f, 0x0f, 0xff, 0x1a, 0x1c, 0x21, 0xff, 0x16, 0x19, 0x19, 0xff, 0x13, 0x15, 0x15, 0xff, 0x1a, 0x19, 0x22, 0xff, 0x1a, 0x1b, 0x24, 0xff, 0x07, 0x0c, 0x12, 0xff, 0x15, 0x1b, 0x26, 0xff, 0x2f, 0x37, 0x42, 0xff, 0x38, 0x3e, 0x4a, 0xff, 0x4e, 0x51, 0x57, 0xff, 0x5a, 0x61, 0x58, 0xff, 0x55, 0x5b, 0x51, 0xff, 0x58, 0x5e, 0x55, 0xff, 0x55, 0x5b, 0x52, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x51, 0x56, 0x4f, 0xff, 0x4e, 0x52, 0x4c, 0xff, 0x4c, 0x50, 0x4b, 0xff, 0x4b, 0x4f, 0x45, 0xff, 0x4e, 0x52, 0x48, 0xff, 0x54, 0x5a, 0x54, 0xff, 0x5c, 0x63, 0x61, 0xff, 0x66, 0x6e, 0x74, 0xff, 0x6a, 0x72, 0x7d, 0xff, 0x6f, 0x7b, 0x89, 0xff, 0x72, 0x82, 0x92, 0xff, 0x7a, 0x86, 0x9d, 0xff, 0x7e, 0x89, 0xa5, 0xff, 0x7f, 0x8d, 0xaa, 0xff, 0x7d, 0x89, 0xaa, 0xff, 0x76, 0x82, 0xa5, 0xff, 0x72, 0x7e, 0xa2, 0xff, 0x71, 0x7d, 0xa1, 0xff, 0x71, 0x7e, 0xa2, 0xff, 0x73, 0x7b, 0xa0, 0xff, 0x6e, 0x77, 0x9c, 0x8f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf7, 0xf7, 0xf7, 0x40, 0xf4, 0xf4, 0xf4, 0xfe, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf2, 0xf2, 0xf1, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf3, 0xf1, 0xf0, 0xff, 0xf4, 0xf2, 0xf2, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xdf, 0xdb, 0xe3, 0xff, 0x70, 0x74, 0x80, 0xff, 0x13, 0x1d, 0x24, 0xff, 0x06, 0x09, 0x0c, 0xff, 0x0a, 0x0a, 0x0d, 0xff, 0x06, 0x08, 0x09, 0xff, 0x0b, 0x0d, 0x0c, 0xff, 0x05, 0x08, 0x0a, 0xff, 0x00, 0x04, 0x04, 0xff, 0x01, 0x05, 0x03, 0xff, 0x00, 0x01, 0x00, 0xff, 0x06, 0x05, 0x06, 0xff, 0x09, 0x0a, 0x09, 0xff, 0x04, 0x04, 0x02, 0xff, 0x05, 0x02, 0x02, 0xff, 0x04, 0x04, 0x03, 0xff, 0x06, 0x07, 0x03, 0xff, 0x0b, 0x0a, 0x07, 0xff, 0x05, 0x05, 0x06, 0xff, 0x00, 0x00, 0x06, 0xff, 0x04, 0x07, 0x0e, 0xff, 0x16, 0x1c, 0x2a, 0xff, 0x32, 0x3c, 0x52, 0xff, 0x45, 0x4f, 0x6b, 0xff, 0x4d, 0x56, 0x74, 0xff, 0x52, 0x5a, 0x77, 0xff, 0x66, 0x70, 0x8e, 0xff, 0x70, 0x7c, 0x9a, 0xff, 0x56, 0x61, 0x7f, 0xff, 0x4e, 0x5a, 0x7a, 0xff, 0x60, 0x6b, 0x92, 0xff, 0x62, 0x6b, 0x92, 0xff, 0x69, 0x75, 0x9d, 0xff, 0x69, 0x7a, 0xa4, 0xff, 0x6d, 0x7d, 0xa9, 0xff, 0x6b, 0x7a, 0xa8, 0xff, 0x6a, 0x77, 0xa8, 0xff, 0x5b, 0x69, 0x9b, 0xff, 0x55, 0x64, 0x98, 0xff, 0x5c, 0x6f, 0x9e, 0xff, 0x52, 0x65, 0x8a, 0xff, 0x2e, 0x3f, 0x5c, 0xff, 0x0d, 0x19, 0x27, 0xff, 0x01, 0x07, 0x08, 0xff, 0x15, 0x0e, 0x14, 0xff, 0x17, 0x13, 0x1a, 0xff, 0x15, 0x17, 0x1b, 0xff, 0x11, 0x12, 0x17, 0xff, 0x11, 0x12, 0x15, 0xff, 0x0a, 0x0b, 0x0f, 0xff, 0x15, 0x18, 0x1f, 0xff, 0x1d, 0x21, 0x29, 0xff, 0x3a, 0x3f, 0x48, 0xff, 0x5a, 0x61, 0x66, 0xff, 0x59, 0x5f, 0x57, 0xff, 0x55, 0x59, 0x4e, 0xff, 0x59, 0x5e, 0x53, 0xff, 0x56, 0x5a, 0x50, 0xff, 0x51, 0x55, 0x4c, 0xff, 0x4f, 0x53, 0x4a, 0xff, 0x4f, 0x53, 0x4a, 0xff, 0x51, 0x56, 0x4e, 0xff, 0x4d, 0x52, 0x4a, 0xff, 0x4b, 0x4f, 0x48, 0xff, 0x4b, 0x4f, 0x45, 0xff, 0x4e, 0x52, 0x49, 0xff, 0x55, 0x5a, 0x54, 0xff, 0x5c, 0x64, 0x61, 0xff, 0x65, 0x6e, 0x73, 0xff, 0x69, 0x72, 0x7c, 0xff, 0x6e, 0x7a, 0x88, 0xff, 0x73, 0x83, 0x93, 0xff, 0x7c, 0x88, 0x9f, 0xff, 0x7f, 0x8a, 0xa6, 0xff, 0x7e, 0x8c, 0xa9, 0xff, 0x7b, 0x87, 0xa8, 0xff, 0x76, 0x81, 0xa4, 0xff, 0x73, 0x7f, 0xa3, 0xff, 0x71, 0x7d, 0xa1, 0xff, 0x72, 0x7e, 0xa2, 0xff, 0x70, 0x7b, 0xa0, 0xff, 0x6e, 0x78, 0x9b, 0xfe, 0x6d, 0x75, 0x9c, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x09, 0xf4, 0xf4, 0xf4, 0xdb, 0xf3, 0xf3, 0xf3, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf1, 0xf0, 0xf0, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf3, 0xf1, 0xf0, 0xff, 0xec, 0xec, 0xec, 0xff, 0xf1, 0xf0, 0xf2, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x93, 0x96, 0x9b, 0xff, 0x1c, 0x26, 0x32, 0xff, 0x08, 0x0b, 0x10, 0xff, 0x07, 0x06, 0x08, 0xff, 0x00, 0x02, 0x02, 0xff, 0x00, 0x03, 0x04, 0xff, 0x08, 0x07, 0x09, 0xff, 0x07, 0x06, 0x00, 0xff, 0x03, 0x03, 0x00, 0xff, 0x05, 0x02, 0x00, 0xff, 0x08, 0x05, 0x05, 0xff, 0x06, 0x08, 0x05, 0xff, 0x01, 0x03, 0x04, 0xff, 0x07, 0x08, 0x12, 0xff, 0x05, 0x09, 0x13, 0xff, 0x00, 0x03, 0x0b, 0xff, 0x01, 0x0a, 0x15, 0xff, 0x0b, 0x15, 0x26, 0xff, 0x13, 0x1e, 0x36, 0xff, 0x1e, 0x2a, 0x42, 0xff, 0x42, 0x4d, 0x69, 0xff, 0x69, 0x75, 0x99, 0xff, 0x84, 0x8f, 0xb0, 0xff, 0x92, 0x9c, 0xbb, 0xff, 0xa3, 0xae, 0xcd, 0xff, 0xaf, 0xba, 0xd9, 0xff, 0xb6, 0xc3, 0xe1, 0xff, 0xb2, 0xbd, 0xdb, 0xff, 0xa6, 0xb3, 0xd4, 0xff, 0xa3, 0xb2, 0xd7, 0xff, 0xa1, 0xaf, 0xd2, 0xff, 0x9b, 0xaa, 0xce, 0xff, 0x9b, 0xab, 0xd3, 0xff, 0x92, 0xa0, 0xcd, 0xff, 0x91, 0x9f, 0xce, 0xff, 0x8f, 0x9f, 0xce, 0xff, 0x83, 0x96, 0xc8, 0xff, 0x78, 0x8e, 0xc4, 0xff, 0x7c, 0x8d, 0xc5, 0xff, 0x71, 0x81, 0xb5, 0xff, 0x69, 0x7a, 0xa9, 0xff, 0x5c, 0x6b, 0x96, 0xff, 0x27, 0x30, 0x43, 0xff, 0x0b, 0x07, 0x08, 0xff, 0x17, 0x12, 0x19, 0xff, 0x14, 0x14, 0x1d, 0xff, 0x0c, 0x0f, 0x10, 0xff, 0x04, 0x05, 0x04, 0xff, 0x0d, 0x0a, 0x0d, 0xff, 0x19, 0x1b, 0x20, 0xff, 0x1f, 0x1f, 0x26, 0xff, 0x2b, 0x2e, 0x37, 0xff, 0x50, 0x5a, 0x60, 0xff, 0x62, 0x67, 0x61, 0xff, 0x59, 0x5c, 0x4f, 0xff, 0x59, 0x5d, 0x50, 0xff, 0x5a, 0x5e, 0x52, 0xff, 0x55, 0x57, 0x4e, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x53, 0x55, 0x4c, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x4d, 0x52, 0x49, 0xff, 0x4a, 0x50, 0x47, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x55, 0x5a, 0x54, 0xff, 0x5d, 0x64, 0x62, 0xff, 0x65, 0x6e, 0x73, 0xff, 0x6c, 0x74, 0x7f, 0xff, 0x6f, 0x7b, 0x88, 0xff, 0x72, 0x82, 0x92, 0xff, 0x79, 0x85, 0x9c, 0xff, 0x7d, 0x88, 0xa4, 0xff, 0x7d, 0x8c, 0xa9, 0xff, 0x7a, 0x87, 0xa7, 0xff, 0x75, 0x81, 0xa4, 0xff, 0x73, 0x7f, 0xa3, 0xff, 0x71, 0x7d, 0xa1, 0xff, 0x72, 0x7d, 0xa1, 0xff, 0x6e, 0x7a, 0x9e, 0xff, 0x6b, 0x78, 0x9c, 0xff, 0x6c, 0x77, 0x9c, 0xdb, 0x71, 0x71, 0x8d, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf1, 0xf1, 0xf1, 0x88, 0xf0, 0xf0, 0xf0, 0xff, 0xef, 0xef, 0xef, 0xff, 0xef, 0xef, 0xef, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf3, 0xf2, 0xf2, 0xff, 0xf3, 0xf2, 0xf2, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xef, 0xef, 0xef, 0xff, 0xee, 0xee, 0xee, 0xff, 0xee, 0xee, 0xee, 0xff, 0xef, 0xf0, 0xf0, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf8, 0xf5, 0xf4, 0xff, 0xe7, 0xe4, 0xe5, 0xff, 0xc2, 0xc4, 0xc5, 0xff, 0xc5, 0xcd, 0xcf, 0xff, 0x93, 0x97, 0x9f, 0xff, 0x36, 0x3d, 0x4b, 0xff, 0x12, 0x14, 0x1e, 0xff, 0x04, 0x03, 0x07, 0xff, 0x00, 0x02, 0x01, 0xff, 0x01, 0x03, 0x03, 0xff, 0x08, 0x06, 0x0b, 0xff, 0x06, 0x03, 0x07, 0xff, 0x07, 0x05, 0x06, 0xff, 0x07, 0x04, 0x06, 0xff, 0x00, 0x00, 0x03, 0xff, 0x00, 0x03, 0x0e, 0xff, 0x0a, 0x17, 0x29, 0xff, 0x17, 0x24, 0x3d, 0xff, 0x1c, 0x2a, 0x49, 0xff, 0x1a, 0x29, 0x4e, 0xff, 0x1b, 0x30, 0x55, 0xff, 0x4b, 0x5d, 0x83, 0xff, 0x7b, 0x8a, 0xb1, 0xff, 0x72, 0x85, 0xab, 0xff, 0x7a, 0x8c, 0xb2, 0xff, 0xa3, 0xb0, 0xd7, 0xff, 0xb5, 0xbe, 0xde, 0xff, 0xb8, 0xc1, 0xdd, 0xff, 0xb6, 0xc0, 0xde, 0xff, 0xbb, 0xc5, 0xe3, 0xff, 0xbc, 0xc4, 0xe3, 0xff, 0xbf, 0xc7, 0xe5, 0xff, 0xbc, 0xc5, 0xe5, 0xff, 0xb6, 0xc0, 0xe2, 0xff, 0xb6, 0xbe, 0xe0, 0xff, 0xb7, 0xc2, 0xe4, 0xff, 0xb5, 0xc3, 0xe8, 0xff, 0xa9, 0xb4, 0xdb, 0xff, 0x9f, 0xab, 0xd6, 0xff, 0x99, 0xaa, 0xdc, 0xff, 0x8a, 0x9f, 0xd2, 0xff, 0x7d, 0x92, 0xca, 0xff, 0x76, 0x8d, 0xc4, 0xff, 0x6c, 0x81, 0xb7, 0xff, 0x65, 0x77, 0xad, 0xff, 0x6a, 0x81, 0xb4, 0xff, 0x4b, 0x5c, 0x84, 0xff, 0x0a, 0x14, 0x1e, 0xff, 0x09, 0x0a, 0x0c, 0xff, 0x07, 0x05, 0x0c, 0xff, 0x08, 0x0b, 0x0b, 0xff, 0x0a, 0x0a, 0x0a, 0xff, 0x0d, 0x0b, 0x0e, 0xff, 0x11, 0x12, 0x1a, 0xff, 0x20, 0x21, 0x2a, 0xff, 0x2d, 0x31, 0x3b, 0xff, 0x48, 0x52, 0x58, 0xff, 0x5f, 0x64, 0x5d, 0xff, 0x5b, 0x5f, 0x51, 0xff, 0x5b, 0x5f, 0x53, 0xff, 0x5b, 0x5f, 0x52, 0xff, 0x56, 0x59, 0x50, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x54, 0x56, 0x4d, 0xff, 0x54, 0x59, 0x50, 0xff, 0x50, 0x56, 0x4d, 0xff, 0x4d, 0x53, 0x4a, 0xff, 0x4d, 0x51, 0x46, 0xff, 0x50, 0x54, 0x4a, 0xff, 0x57, 0x5c, 0x56, 0xff, 0x5d, 0x65, 0x63, 0xff, 0x67, 0x6f, 0x74, 0xff, 0x6f, 0x77, 0x82, 0xff, 0x72, 0x7f, 0x8c, 0xff, 0x73, 0x84, 0x94, 0xff, 0x7a, 0x87, 0x9d, 0xff, 0x7c, 0x87, 0xa3, 0xff, 0x7b, 0x8a, 0xa7, 0xff, 0x78, 0x85, 0xa6, 0xff, 0x74, 0x80, 0xa3, 0xff, 0x72, 0x7e, 0xa2, 0xff, 0x70, 0x7c, 0xa0, 0xff, 0x71, 0x7d, 0xa1, 0xff, 0x6e, 0x7a, 0x9e, 0xff, 0x6a, 0x76, 0x9a, 0xff, 0x6a, 0x77, 0x9b, 0xff, 0x6c, 0x78, 0x99, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf1, 0xf1, 0xf1, 0x26, 0xee, 0xee, 0xee, 0xfa, 0xee, 0xee, 0xee, 0xff, 0xee, 0xee, 0xee, 0xff, 0xed, 0xed, 0xed, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf0, 0xf1, 0xf1, 0xff, 0xef, 0xf1, 0xf1, 0xff, 0xef, 0xf0, 0xf0, 0xff, 0xef, 0xef, 0xef, 0xff, 0xee, 0xee, 0xee, 0xff, 0xed, 0xed, 0xed, 0xff, 0xed, 0xed, 0xed, 0xff, 0xed, 0xed, 0xed, 0xff, 0xee, 0xee, 0xee, 0xff, 0xef, 0xef, 0xee, 0xff, 0xf1, 0xee, 0xee, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf2, 0xf0, 0xef, 0xff, 0xf4, 0xf1, 0xf0, 0xff, 0xf9, 0xf7, 0xf8, 0xff, 0xac, 0xb3, 0xb2, 0xff, 0x4c, 0x59, 0x5c, 0xff, 0x5d, 0x64, 0x72, 0xff, 0x47, 0x4b, 0x60, 0xff, 0x1c, 0x1c, 0x27, 0xff, 0x0c, 0x09, 0x0c, 0xff, 0x04, 0x08, 0x01, 0xff, 0x00, 0x04, 0x01, 0xff, 0x04, 0x09, 0x10, 0xff, 0x02, 0x09, 0x12, 0xff, 0x09, 0x0f, 0x1d, 0xff, 0x0d, 0x13, 0x25, 0xff, 0x0d, 0x16, 0x2b, 0xff, 0x11, 0x21, 0x3b, 0xff, 0x23, 0x36, 0x56, 0xff, 0x3a, 0x4a, 0x74, 0xff, 0x4b, 0x5b, 0x8c, 0xff, 0x4e, 0x61, 0x94, 0xff, 0x6b, 0x7e, 0xaa, 0xff, 0xa0, 0xaf, 0xd4, 0xff, 0xb1, 0xba, 0xdf, 0xff, 0xa1, 0xad, 0xd4, 0xff, 0xa0, 0xad, 0xd3, 0xff, 0xa5, 0xae, 0xd6, 0xff, 0xad, 0xb8, 0xd8, 0xff, 0xb4, 0xbf, 0xdc, 0xff, 0xb4, 0xbf, 0xdc, 0xff, 0xb7, 0xc0, 0xdc, 0xff, 0xbb, 0xc3, 0xdf, 0xff, 0xbc, 0xc4, 0xdf, 0xff, 0xbe, 0xc7, 0xe3, 0xff, 0xbb, 0xc5, 0xe2, 0xff, 0xb7, 0xc0, 0xdd, 0xff, 0xb5, 0xbf, 0xdc, 0xff, 0xb4, 0xbf, 0xe0, 0xff, 0xb0, 0xb9, 0xde, 0xff, 0xa4, 0xaf, 0xd8, 0xff, 0x94, 0xa8, 0xdb, 0xff, 0x87, 0x9d, 0xd5, 0xff, 0x7d, 0x97, 0xce, 0xff, 0x71, 0x8f, 0xc0, 0xff, 0x68, 0x83, 0xb4, 0xff, 0x64, 0x7b, 0xaf, 0xff, 0x5f, 0x7a, 0xac, 0xff, 0x50, 0x67, 0x96, 0xff, 0x20, 0x2f, 0x4a, 0xff, 0x0b, 0x0f, 0x18, 0xff, 0x09, 0x08, 0x0f, 0xff, 0x0c, 0x0f, 0x12, 0xff, 0x11, 0x12, 0x12, 0xff, 0x0b, 0x0b, 0x0c, 0xff, 0x00, 0x01, 0x05, 0xff, 0x13, 0x15, 0x1b, 0xff, 0x34, 0x37, 0x3f, 0xff, 0x4a, 0x50, 0x58, 0xff, 0x59, 0x5e, 0x5a, 0xff, 0x59, 0x5c, 0x50, 0xff, 0x59, 0x5c, 0x51, 0xff, 0x5a, 0x5d, 0x52, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x55, 0x59, 0x50, 0xff, 0x52, 0x56, 0x4d, 0xff, 0x4f, 0x53, 0x4a, 0xff, 0x4d, 0x51, 0x46, 0xff, 0x50, 0x54, 0x4b, 0xff, 0x57, 0x5c, 0x56, 0xff, 0x5d, 0x65, 0x63, 0xff, 0x67, 0x70, 0x75, 0xff, 0x70, 0x79, 0x83, 0xff, 0x75, 0x7f, 0x8d, 0xff, 0x75, 0x82, 0x93, 0xff, 0x7a, 0x85, 0x9c, 0xff, 0x7b, 0x88, 0xa3, 0xff, 0x7d, 0x8b, 0xa8, 0xff, 0x7a, 0x85, 0xa7, 0xff, 0x74, 0x7f, 0xa1, 0xff, 0x70, 0x7c, 0x9f, 0xff, 0x6f, 0x7b, 0x9e, 0xff, 0x6f, 0x7b, 0x9e, 0xff, 0x6c, 0x78, 0x9c, 0xff, 0x6a, 0x76, 0x9a, 0xff, 0x6a, 0x76, 0x9a, 0xff, 0x6e, 0x78, 0x9b, 0xfa, 0x6b, 0x78, 0x9a, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xed, 0xed, 0xed, 0xb3, 0xec, 0xec, 0xec, 0xff, 0xec, 0xec, 0xec, 0xff, 0xec, 0xec, 0xec, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xee, 0xee, 0xee, 0xff, 0xee, 0xee, 0xee, 0xff, 0xeb, 0xed, 0xed, 0xff, 0xe9, 0xec, 0xec, 0xff, 0xe8, 0xeb, 0xeb, 0xff, 0xea, 0xea, 0xea, 0xff, 0xed, 0xec, 0xec, 0xff, 0xec, 0xec, 0xec, 0xff, 0xec, 0xec, 0xec, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xed, 0xed, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf4, 0xf2, 0xf2, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf3, 0xf0, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbb, 0xc2, 0xc1, 0xff, 0x36, 0x40, 0x48, 0xff, 0x1f, 0x28, 0x3a, 0xff, 0x22, 0x23, 0x38, 0xff, 0x1c, 0x1a, 0x23, 0xff, 0x0a, 0x07, 0x0c, 0xff, 0x02, 0x06, 0x02, 0xff, 0x0e, 0x16, 0x1b, 0xff, 0x20, 0x2c, 0x3e, 0xff, 0x20, 0x32, 0x46, 0xff, 0x28, 0x37, 0x59, 0xff, 0x2e, 0x3e, 0x65, 0xff, 0x38, 0x4d, 0x74, 0xff, 0x3f, 0x55, 0x7e, 0xff, 0x45, 0x5a, 0x84, 0xff, 0x56, 0x66, 0x99, 0xff, 0x67, 0x77, 0xaf, 0xff, 0x6d, 0x7e, 0xb0, 0xff, 0x95, 0xa2, 0xce, 0xff, 0xb2, 0xbe, 0xdf, 0xff, 0xa9, 0xb1, 0xd4, 0xff, 0xa2, 0xab, 0xd1, 0xff, 0xa1, 0xad, 0xd5, 0xff, 0x92, 0xa1, 0xce, 0xff, 0x99, 0xaa, 0xd2, 0xff, 0xa9, 0xb8, 0xdb, 0xff, 0xb5, 0xc1, 0xe1, 0xff, 0xb6, 0xc1, 0xde, 0xff, 0xb6, 0xc0, 0xdd, 0xff, 0xb4, 0xbf, 0xdc, 0xff, 0xba, 0xc5, 0xe0, 0xff, 0xbc, 0xc7, 0xe1, 0xff, 0xba, 0xc6, 0xdf, 0xff, 0xb7, 0xc2, 0xde, 0xff, 0xab, 0xb6, 0xd7, 0xff, 0xaa, 0xb4, 0xdc, 0xff, 0x9e, 0xac, 0xd8, 0xff, 0x84, 0x99, 0xcc, 0xff, 0x79, 0x90, 0xcd, 0xff, 0x73, 0x92, 0xc9, 0xff, 0x6f, 0x90, 0xc3, 0xff, 0x67, 0x84, 0xb7, 0xff, 0x62, 0x7c, 0xb0, 0xff, 0x64, 0x78, 0xb1, 0xff, 0x52, 0x68, 0x9a, 0xff, 0x35, 0x45, 0x62, 0xff, 0x32, 0x3a, 0x46, 0xff, 0x21, 0x25, 0x2e, 0xff, 0x0c, 0x0f, 0x17, 0xff, 0x11, 0x13, 0x16, 0xff, 0x10, 0x12, 0x12, 0xff, 0x07, 0x08, 0x09, 0xff, 0x0b, 0x0d, 0x0d, 0xff, 0x1f, 0x21, 0x28, 0xff, 0x3c, 0x3b, 0x49, 0xff, 0x53, 0x56, 0x58, 0xff, 0x57, 0x5a, 0x50, 0xff, 0x57, 0x5a, 0x50, 0xff, 0x58, 0x5b, 0x53, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x51, 0x54, 0x4b, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x4d, 0x50, 0x45, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x57, 0x5d, 0x57, 0xff, 0x5e, 0x66, 0x63, 0xff, 0x68, 0x70, 0x75, 0xff, 0x6e, 0x78, 0x82, 0xff, 0x75, 0x7d, 0x8c, 0xff, 0x75, 0x7e, 0x8f, 0xff, 0x77, 0x82, 0x99, 0xff, 0x7b, 0x87, 0xa2, 0xff, 0x7e, 0x89, 0xa7, 0xff, 0x7b, 0x85, 0xa6, 0xff, 0x73, 0x7d, 0x9f, 0xff, 0x6d, 0x79, 0x9b, 0xff, 0x6d, 0x79, 0x9a, 0xff, 0x6c, 0x78, 0x9a, 0xff, 0x69, 0x75, 0x99, 0xff, 0x69, 0x75, 0x99, 0xff, 0x69, 0x75, 0x99, 0xff, 0x6e, 0x77, 0x9b, 0xff, 0x6b, 0x75, 0x97, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xf0, 0xf0, 0x44, 0xee, 0xee, 0xee, 0xff, 0xea, 0xea, 0xea, 0xff, 0xea, 0xea, 0xea, 0xff, 0xe9, 0xe8, 0xe8, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xe9, 0xe9, 0xe8, 0xff, 0xea, 0xea, 0xea, 0xff, 0xea, 0xe9, 0xe9, 0xff, 0xea, 0xe9, 0xe9, 0xff, 0xe9, 0xea, 0xea, 0xff, 0xe7, 0xe9, 0xe9, 0xff, 0xe6, 0xe8, 0xe8, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xea, 0xea, 0xea, 0xff, 0xed, 0xeb, 0xeb, 0xff, 0xf0, 0xed, 0xed, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf3, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf4, 0xf2, 0xf2, 0xff, 0xfd, 0xfa, 0xfb, 0xff, 0xd1, 0xd4, 0xd3, 0xff, 0x4a, 0x4f, 0x5b, 0xff, 0x0f, 0x12, 0x22, 0xff, 0x0b, 0x0f, 0x16, 0xff, 0x08, 0x09, 0x11, 0xff, 0x04, 0x07, 0x14, 0xff, 0x2e, 0x35, 0x48, 0xff, 0x4b, 0x52, 0x72, 0xff, 0x49, 0x56, 0x80, 0xff, 0x45, 0x59, 0x87, 0xff, 0x4e, 0x63, 0x95, 0xff, 0x4e, 0x64, 0x96, 0xff, 0x4d, 0x63, 0x97, 0xff, 0x4e, 0x66, 0x9d, 0xff, 0x52, 0x6d, 0xa3, 0xff, 0x62, 0x7b, 0xae, 0xff, 0x78, 0x8a, 0xbf, 0xff, 0x8f, 0x9e, 0xca, 0xff, 0xa2, 0xaf, 0xd6, 0xff, 0xaa, 0xba, 0xdc, 0xff, 0xa0, 0xaf, 0xd9, 0xff, 0x9a, 0xab, 0xd7, 0xff, 0x92, 0xa7, 0xd3, 0xff, 0x86, 0x9e, 0xce, 0xff, 0x8d, 0xa1, 0xd0, 0xff, 0x98, 0xa9, 0xd4, 0xff, 0xa1, 0xb0, 0xd9, 0xff, 0xa8, 0xb5, 0xdd, 0xff, 0xac, 0xb7, 0xdf, 0xff, 0xab, 0xb7, 0xe0, 0xff, 0xb1, 0xbe, 0xe2, 0xff, 0xb5, 0xc1, 0xe1, 0xff, 0xb6, 0xc2, 0xe2, 0xff, 0xb7, 0xc2, 0xe5, 0xff, 0xa7, 0xb2, 0xda, 0xff, 0xa0, 0xac, 0xda, 0xff, 0x91, 0x9f, 0xd1, 0xff, 0x77, 0x8d, 0xc3, 0xff, 0x6f, 0x88, 0xc8, 0xff, 0x6b, 0x8c, 0xc5, 0xff, 0x6e, 0x8d, 0xc3, 0xff, 0x67, 0x81, 0xb8, 0xff, 0x61, 0x79, 0xb0, 0xff, 0x61, 0x76, 0xb0, 0xff, 0x5c, 0x71, 0xa7, 0xff, 0x40, 0x4d, 0x6d, 0xff, 0x27, 0x2e, 0x3c, 0xff, 0x1d, 0x25, 0x2f, 0xff, 0x1b, 0x20, 0x29, 0xff, 0x17, 0x17, 0x21, 0xff, 0x0f, 0x0d, 0x17, 0xff, 0x0a, 0x0b, 0x0e, 0xff, 0x08, 0x0b, 0x09, 0xff, 0x13, 0x15, 0x1c, 0xff, 0x2b, 0x2c, 0x39, 0xff, 0x4a, 0x4e, 0x4e, 0xff, 0x58, 0x5b, 0x50, 0xff, 0x5a, 0x5d, 0x53, 0xff, 0x59, 0x5b, 0x52, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x56, 0x59, 0x50, 0xff, 0x56, 0x59, 0x50, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x50, 0x53, 0x4a, 0xff, 0x4e, 0x51, 0x46, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x59, 0x5e, 0x58, 0xff, 0x5f, 0x66, 0x64, 0xff, 0x67, 0x70, 0x75, 0xff, 0x6c, 0x76, 0x80, 0xff, 0x73, 0x7c, 0x8a, 0xff, 0x75, 0x7e, 0x8f, 0xff, 0x77, 0x82, 0x99, 0xff, 0x7a, 0x86, 0xa1, 0xff, 0x7b, 0x86, 0xa4, 0xff, 0x78, 0x82, 0xa3, 0xff, 0x6f, 0x7a, 0x9c, 0xff, 0x6a, 0x76, 0x98, 0xff, 0x6a, 0x77, 0x98, 0xff, 0x69, 0x75, 0x97, 0xff, 0x68, 0x74, 0x98, 0xff, 0x68, 0x74, 0x98, 0xff, 0x67, 0x73, 0x98, 0xff, 0x6a, 0x75, 0x97, 0xff, 0x6a, 0x73, 0x94, 0xff, 0x69, 0x70, 0x92, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xee, 0xef, 0xef, 0xc8, 0xec, 0xed, 0xed, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xe6, 0xe7, 0xe7, 0xff, 0xe4, 0xe5, 0xe5, 0xff, 0xe3, 0xe4, 0xe4, 0xff, 0xe4, 0xe4, 0xe5, 0xff, 0xe6, 0xe7, 0xe7, 0xff, 0xe6, 0xe7, 0xe7, 0xff, 0xe6, 0xe7, 0xe7, 0xff, 0xe5, 0xe6, 0xe7, 0xff, 0xe4, 0xe6, 0xe6, 0xff, 0xe3, 0xe5, 0xe5, 0xff, 0xe3, 0xe4, 0xe4, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe3, 0xe4, 0xe4, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xee, 0xed, 0xed, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf2, 0xef, 0xf0, 0xff, 0xf6, 0xf1, 0xf2, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0x79, 0x7c, 0x84, 0xff, 0x1b, 0x1d, 0x27, 0xff, 0x13, 0x17, 0x17, 0xff, 0x01, 0x04, 0x0a, 0xff, 0x32, 0x3a, 0x4d, 0xff, 0x6f, 0x7b, 0x9d, 0xff, 0x6e, 0x7a, 0xa9, 0xff, 0x60, 0x6f, 0xa4, 0xff, 0x61, 0x72, 0xaa, 0xff, 0x60, 0x73, 0xa9, 0xff, 0x5e, 0x73, 0xa8, 0xff, 0x5d, 0x71, 0xa7, 0xff, 0x5d, 0x71, 0xaa, 0xff, 0x64, 0x7c, 0xb5, 0xff, 0x6c, 0x89, 0xc0, 0xff, 0x84, 0x9b, 0xcd, 0xff, 0xa1, 0xb4, 0xda, 0xff, 0xac, 0xb8, 0xe0, 0xff, 0xa5, 0xb3, 0xde, 0xff, 0x9b, 0xab, 0xde, 0xff, 0x86, 0x9f, 0xd2, 0xff, 0x79, 0x94, 0xc7, 0xff, 0x75, 0x93, 0xc7, 0xff, 0x76, 0x8f, 0xc4, 0xff, 0x86, 0x9a, 0xcd, 0xff, 0x94, 0xa5, 0xd7, 0xff, 0x99, 0xaa, 0xd9, 0xff, 0x9b, 0xad, 0xdb, 0xff, 0x9c, 0xaf, 0xde, 0xff, 0xa7, 0xb7, 0xe3, 0xff, 0xac, 0xb9, 0xe0, 0xff, 0xa6, 0xb4, 0xdb, 0xff, 0xa1, 0xae, 0xd6, 0xff, 0x98, 0xa5, 0xd3, 0xff, 0x90, 0x9e, 0xd0, 0xff, 0x82, 0x93, 0xc8, 0xff, 0x6b, 0x84, 0xbc, 0xff, 0x63, 0x7f, 0xbf, 0xff, 0x63, 0x84, 0xc2, 0xff, 0x66, 0x84, 0xbe, 0xff, 0x65, 0x7f, 0xb9, 0xff, 0x67, 0x7d, 0xb9, 0xff, 0x66, 0x7c, 0xb8, 0xff, 0x5f, 0x73, 0xa9, 0xff, 0x41, 0x4d, 0x70, 0xff, 0x10, 0x19, 0x2c, 0xff, 0x08, 0x11, 0x1e, 0xff, 0x30, 0x36, 0x42, 0xff, 0x29, 0x27, 0x37, 0xff, 0x1d, 0x18, 0x28, 0xff, 0x14, 0x16, 0x18, 0xff, 0x04, 0x08, 0x06, 0xff, 0x09, 0x0a, 0x13, 0xff, 0x24, 0x25, 0x30, 0xff, 0x4a, 0x4c, 0x4e, 0xff, 0x5b, 0x5e, 0x56, 0xff, 0x5d, 0x60, 0x59, 0xff, 0x5a, 0x5d, 0x55, 0xff, 0x56, 0x59, 0x50, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x56, 0x59, 0x50, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x4f, 0x53, 0x47, 0xff, 0x53, 0x57, 0x4d, 0xff, 0x59, 0x5f, 0x58, 0xff, 0x60, 0x67, 0x65, 0xff, 0x67, 0x6f, 0x75, 0xff, 0x6b, 0x74, 0x7f, 0xff, 0x71, 0x7a, 0x88, 0xff, 0x73, 0x7e, 0x8e, 0xff, 0x76, 0x82, 0x99, 0xff, 0x78, 0x85, 0xa0, 0xff, 0x78, 0x83, 0xa1, 0xff, 0x75, 0x7f, 0xa0, 0xff, 0x6e, 0x78, 0x9a, 0xff, 0x68, 0x75, 0x96, 0xff, 0x67, 0x73, 0x95, 0xff, 0x66, 0x72, 0x94, 0xff, 0x65, 0x71, 0x95, 0xff, 0x66, 0x72, 0x95, 0xff, 0x66, 0x72, 0x95, 0xff, 0x67, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf1, 0xf1, 0xf1, 0x4d, 0xee, 0xef, 0xef, 0xff, 0xe8, 0xea, 0xea, 0xff, 0xe3, 0xe6, 0xe6, 0xff, 0xe2, 0xe4, 0xe4, 0xff, 0xe1, 0xe4, 0xe5, 0xff, 0xdf, 0xe2, 0xe3, 0xff, 0xdf, 0xe1, 0xe3, 0xff, 0xdf, 0xe3, 0xe4, 0xff, 0xe1, 0xe5, 0xe6, 0xff, 0xe1, 0xe5, 0xe6, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xe1, 0xe3, 0xe3, 0xff, 0xe0, 0xe3, 0xe3, 0xff, 0xe1, 0xe3, 0xe3, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xe9, 0xea, 0xea, 0xff, 0xeb, 0xec, 0xec, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf3, 0xf0, 0xf1, 0xff, 0xf5, 0xf2, 0xf2, 0xff, 0xf7, 0xf6, 0xf6, 0xff, 0x84, 0x87, 0x86, 0xff, 0x23, 0x29, 0x2c, 0xff, 0x0b, 0x0c, 0x10, 0xff, 0x0e, 0x10, 0x18, 0xff, 0x5d, 0x68, 0x83, 0xff, 0x7d, 0x8d, 0xb4, 0xff, 0x6d, 0x7f, 0xac, 0xff, 0x60, 0x75, 0xaa, 0xff, 0x62, 0x78, 0xb0, 0xff, 0x63, 0x78, 0xaf, 0xff, 0x64, 0x7a, 0xb1, 0xff, 0x68, 0x7f, 0xb9, 0xff, 0x6d, 0x83, 0xbc, 0xff, 0x6a, 0x82, 0xbc, 0xff, 0x6d, 0x88, 0xc1, 0xff, 0x7d, 0x98, 0xc8, 0xff, 0x8d, 0xa6, 0xce, 0xff, 0x97, 0xaa, 0xd8, 0xff, 0x8d, 0x9e, 0xd9, 0xff, 0x84, 0x99, 0xd3, 0xff, 0x73, 0x8e, 0xcc, 0xff, 0x68, 0x85, 0xc2, 0xff, 0x6b, 0x8a, 0xc1, 0xff, 0x6f, 0x8a, 0xc3, 0xff, 0x76, 0x8f, 0xc4, 0xff, 0x86, 0x9a, 0xce, 0xff, 0x8c, 0xa2, 0xd4, 0xff, 0x8a, 0xa2, 0xd1, 0xff, 0x8b, 0xa4, 0xd4, 0xff, 0x95, 0xaa, 0xda, 0xff, 0xa1, 0xaf, 0xde, 0xff, 0x9f, 0xb0, 0xdb, 0xff, 0x91, 0xa2, 0xcd, 0xff, 0x8b, 0x9b, 0xcc, 0xff, 0x85, 0x98, 0xcb, 0xff, 0x7b, 0x91, 0xc7, 0xff, 0x6b, 0x87, 0xc0, 0xff, 0x63, 0x80, 0xbd, 0xff, 0x61, 0x7f, 0xc2, 0xff, 0x5e, 0x7b, 0xba, 0xff, 0x5e, 0x79, 0xb7, 0xff, 0x60, 0x77, 0xb7, 0xff, 0x63, 0x7d, 0xb8, 0xff, 0x5c, 0x73, 0xa4, 0xff, 0x49, 0x59, 0x7f, 0xff, 0x3c, 0x48, 0x64, 0xff, 0x29, 0x30, 0x46, 0xff, 0x25, 0x29, 0x3c, 0xff, 0x1e, 0x1e, 0x2d, 0xff, 0x25, 0x25, 0x2f, 0xff, 0x22, 0x25, 0x2c, 0xff, 0x0e, 0x10, 0x17, 0xff, 0x09, 0x07, 0x12, 0xff, 0x27, 0x27, 0x2d, 0xff, 0x51, 0x52, 0x55, 0xff, 0x5a, 0x5d, 0x5b, 0xff, 0x59, 0x5d, 0x58, 0xff, 0x58, 0x5c, 0x55, 0xff, 0x56, 0x59, 0x50, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x56, 0x59, 0x50, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x50, 0x53, 0x48, 0xff, 0x52, 0x56, 0x4d, 0xff, 0x59, 0x5e, 0x58, 0xff, 0x61, 0x69, 0x66, 0xff, 0x69, 0x71, 0x76, 0xff, 0x6b, 0x74, 0x7e, 0xff, 0x6c, 0x77, 0x85, 0xff, 0x6c, 0x7a, 0x8a, 0xff, 0x71, 0x7e, 0x95, 0xff, 0x75, 0x82, 0x9d, 0xff, 0x76, 0x82, 0x9f, 0xff, 0x72, 0x7d, 0x9d, 0xff, 0x6f, 0x79, 0x9b, 0xff, 0x69, 0x75, 0x97, 0xff, 0x64, 0x70, 0x92, 0xff, 0x62, 0x6e, 0x90, 0xff, 0x62, 0x6e, 0x90, 0xff, 0x64, 0x70, 0x91, 0xff, 0x65, 0x70, 0x92, 0xff, 0x66, 0x71, 0x93, 0xff, 0x67, 0x71, 0x93, 0xff, 0x69, 0x73, 0x95, 0xff, 0x66, 0x70, 0x95, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xee, 0xee, 0xee, 0xcd, 0xec, 0xec, 0xec, 0xff, 0xe7, 0xe9, 0xe9, 0xff, 0xe2, 0xe4, 0xe4, 0xff, 0xe0, 0xe2, 0xe2, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdd, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdb, 0xdf, 0xe0, 0xff, 0xdd, 0xe1, 0xe2, 0xff, 0xdc, 0xe1, 0xe2, 0xff, 0xde, 0xe1, 0xe2, 0xff, 0xe0, 0xe1, 0xe2, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xdc, 0xde, 0xde, 0xff, 0xdc, 0xdf, 0xdf, 0xff, 0xdf, 0xe0, 0xe0, 0xff, 0xe1, 0xe1, 0xe1, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xed, 0xed, 0xed, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf5, 0xf2, 0xf2, 0xff, 0xf5, 0xf1, 0xf2, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xaf, 0xb2, 0xb2, 0xff, 0x2d, 0x33, 0x35, 0xff, 0x00, 0x02, 0x06, 0xff, 0x22, 0x2a, 0x38, 0xff, 0x6b, 0x78, 0x99, 0xff, 0x7a, 0x89, 0xb5, 0xff, 0x6a, 0x7b, 0xae, 0xff, 0x5e, 0x74, 0xae, 0xff, 0x5b, 0x72, 0xac, 0xff, 0x67, 0x7e, 0xb6, 0xff, 0x6c, 0x85, 0xbe, 0xff, 0x72, 0x8b, 0xc8, 0xff, 0x74, 0x8d, 0xca, 0xff, 0x69, 0x83, 0xbf, 0xff, 0x6a, 0x85, 0xbe, 0xff, 0x75, 0x8b, 0xc3, 0xff, 0x7e, 0x93, 0xc8, 0xff, 0x7c, 0x98, 0xca, 0xff, 0x6c, 0x87, 0xc6, 0xff, 0x67, 0x85, 0xc3, 0xff, 0x6a, 0x86, 0xc5, 0xff, 0x6d, 0x88, 0xc7, 0xff, 0x70, 0x8d, 0xc9, 0xff, 0x78, 0x92, 0xcb, 0xff, 0x71, 0x89, 0xbe, 0xff, 0x77, 0x8c, 0xbf, 0xff, 0x81, 0x96, 0xc9, 0xff, 0x87, 0x9e, 0xd0, 0xff, 0x87, 0x9f, 0xd2, 0xff, 0x8b, 0x9f, 0xd3, 0xff, 0x96, 0xa6, 0xd8, 0xff, 0x97, 0xaa, 0xd7, 0xff, 0x8a, 0x9e, 0xca, 0xff, 0x85, 0x99, 0xc9, 0xff, 0x7c, 0x92, 0xc5, 0xff, 0x77, 0x8e, 0xc5, 0xff, 0x72, 0x8e, 0xc6, 0xff, 0x6b, 0x89, 0xc5, 0xff, 0x61, 0x7f, 0xc1, 0xff, 0x5b, 0x78, 0xb6, 0xff, 0x5c, 0x77, 0xb4, 0xff, 0x5d, 0x74, 0xb3, 0xff, 0x5e, 0x76, 0xb1, 0xff, 0x5f, 0x77, 0xaa, 0xff, 0x59, 0x70, 0x9b, 0xff, 0x51, 0x61, 0x87, 0xff, 0x47, 0x51, 0x6f, 0xff, 0x26, 0x2e, 0x44, 0xff, 0x03, 0x0b, 0x15, 0xff, 0x12, 0x19, 0x1f, 0xff, 0x1f, 0x20, 0x2e, 0xff, 0x1b, 0x19, 0x27, 0xff, 0x13, 0x12, 0x18, 0xff, 0x31, 0x32, 0x36, 0xff, 0x56, 0x59, 0x58, 0xff, 0x57, 0x5b, 0x56, 0xff, 0x56, 0x5c, 0x53, 0xff, 0x55, 0x5c, 0x50, 0xff, 0x55, 0x57, 0x4e, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x50, 0x53, 0x4a, 0xff, 0x4f, 0x52, 0x48, 0xff, 0x53, 0x57, 0x4d, 0xff, 0x59, 0x5e, 0x58, 0xff, 0x61, 0x69, 0x66, 0xff, 0x69, 0x71, 0x77, 0xff, 0x6b, 0x74, 0x7e, 0xff, 0x6d, 0x78, 0x85, 0xff, 0x6d, 0x7b, 0x8b, 0xff, 0x70, 0x7d, 0x94, 0xff, 0x73, 0x81, 0x9c, 0xff, 0x74, 0x80, 0x9d, 0xff, 0x6f, 0x79, 0x9a, 0xff, 0x6a, 0x74, 0x96, 0xff, 0x64, 0x70, 0x92, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x62, 0x6e, 0x90, 0xff, 0x66, 0x70, 0x92, 0xff, 0x66, 0x70, 0x92, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xef, 0xef, 0xef, 0x40, 0xee, 0xed, 0xed, 0xff, 0xea, 0xeb, 0xeb, 0xff, 0xe5, 0xe7, 0xe7, 0xff, 0xe0, 0xe2, 0xe2, 0xff, 0xdf, 0xe1, 0xe1, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xdb, 0xdf, 0xe0, 0xff, 0xdd, 0xe1, 0xe2, 0xff, 0xdd, 0xe1, 0xe2, 0xff, 0xde, 0xe1, 0xe2, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdf, 0xe1, 0xe3, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xdc, 0xff, 0xda, 0xdc, 0xdc, 0xff, 0xdd, 0xde, 0xde, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xe2, 0xe2, 0xe2, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xea, 0xea, 0xea, 0xff, 0xed, 0xed, 0xed, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf4, 0xf1, 0xf2, 0xff, 0xf5, 0xf2, 0xf3, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xdc, 0xdf, 0xde, 0xff, 0x4d, 0x50, 0x51, 0xff, 0x01, 0x03, 0x0a, 0xff, 0x3b, 0x48, 0x5e, 0xff, 0x6e, 0x7e, 0xa6, 0xff, 0x6b, 0x7c, 0xac, 0xff, 0x64, 0x77, 0xae, 0xff, 0x60, 0x77, 0xb4, 0xff, 0x5b, 0x76, 0xb3, 0xff, 0x61, 0x7b, 0xb7, 0xff, 0x65, 0x80, 0xbc, 0xff, 0x6d, 0x8a, 0xc7, 0xff, 0x6a, 0x86, 0xc3, 0xff, 0x5f, 0x7b, 0xb7, 0xff, 0x63, 0x7d, 0xb6, 0xff, 0x65, 0x7a, 0xb8, 0xff, 0x68, 0x7b, 0xb8, 0xff, 0x6c, 0x85, 0xbd, 0xff, 0x68, 0x81, 0xc3, 0xff, 0x61, 0x7c, 0xc0, 0xff, 0x5e, 0x79, 0xbc, 0xff, 0x67, 0x82, 0xc4, 0xff, 0x70, 0x8c, 0xc9, 0xff, 0x79, 0x94, 0xcc, 0xff, 0x81, 0x9a, 0xcd, 0xff, 0x87, 0x9d, 0xcf, 0xff, 0x83, 0x98, 0xcc, 0xff, 0x84, 0x9a, 0xcf, 0xff, 0x8a, 0xa1, 0xd6, 0xff, 0x91, 0xa6, 0xdb, 0xff, 0x98, 0xab, 0xde, 0xff, 0x96, 0xaa, 0xda, 0xff, 0x89, 0x9e, 0xcd, 0xff, 0x7f, 0x96, 0xc5, 0xff, 0x7c, 0x96, 0xc7, 0xff, 0x7c, 0x97, 0xcc, 0xff, 0x76, 0x92, 0xc9, 0xff, 0x6d, 0x8b, 0xc7, 0xff, 0x63, 0x81, 0xc2, 0xff, 0x5b, 0x79, 0xb6, 0xff, 0x5b, 0x77, 0xb4, 0xff, 0x5e, 0x75, 0xb4, 0xff, 0x61, 0x78, 0xb1, 0xff, 0x5e, 0x76, 0xaa, 0xff, 0x57, 0x71, 0xa1, 0xff, 0x51, 0x64, 0x92, 0xff, 0x54, 0x61, 0x86, 0xff, 0x3f, 0x4c, 0x63, 0xff, 0x30, 0x3d, 0x4f, 0xff, 0x31, 0x3a, 0x4c, 0xff, 0x2d, 0x30, 0x3c, 0xff, 0x23, 0x23, 0x29, 0xff, 0x18, 0x19, 0x1c, 0xff, 0x3e, 0x41, 0x41, 0xff, 0x58, 0x5c, 0x58, 0xff, 0x53, 0x58, 0x50, 0xff, 0x55, 0x5b, 0x50, 0xff, 0x54, 0x5b, 0x4e, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x56, 0x59, 0x50, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x4f, 0x53, 0x49, 0xff, 0x4f, 0x52, 0x48, 0xff, 0x53, 0x57, 0x4d, 0xff, 0x5a, 0x5f, 0x59, 0xff, 0x5f, 0x67, 0x65, 0xff, 0x66, 0x6f, 0x74, 0xff, 0x6d, 0x76, 0x80, 0xff, 0x70, 0x7b, 0x89, 0xff, 0x6f, 0x7d, 0x8d, 0xff, 0x72, 0x7f, 0x95, 0xff, 0x74, 0x81, 0x9c, 0xff, 0x72, 0x7e, 0x9b, 0xff, 0x6c, 0x77, 0x97, 0xff, 0x66, 0x70, 0x92, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x66, 0x6f, 0x91, 0xff, 0x66, 0x70, 0x92, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x73, 0x93, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xef, 0xef, 0xef, 0xb3, 0xee, 0xed, 0xed, 0xff, 0xe9, 0xe9, 0xea, 0xff, 0xe3, 0xe5, 0xe6, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd4, 0xd9, 0xda, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xda, 0xde, 0xdf, 0xff, 0xdc, 0xe0, 0xe1, 0xff, 0xdc, 0xe0, 0xe1, 0xff, 0xde, 0xe1, 0xe2, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xde, 0xff, 0xdb, 0xdc, 0xdc, 0xff, 0xde, 0xde, 0xde, 0xff, 0xe2, 0xe2, 0xe2, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xea, 0xea, 0xea, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf1, 0xf0, 0xf0, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xfa, 0xf8, 0xf8, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0x4f, 0x54, 0x51, 0xff, 0x0d, 0x12, 0x1f, 0xff, 0x45, 0x52, 0x74, 0xff, 0x64, 0x76, 0xa2, 0xff, 0x5e, 0x71, 0xa3, 0xff, 0x59, 0x71, 0xa8, 0xff, 0x53, 0x6e, 0xa7, 0xff, 0x51, 0x6e, 0xa9, 0xff, 0x55, 0x70, 0xb0, 0xff, 0x5f, 0x7b, 0xbb, 0xff, 0x5e, 0x7c, 0xb8, 0xff, 0x5f, 0x7a, 0xb3, 0xff, 0x63, 0x7c, 0xb4, 0xff, 0x5c, 0x73, 0xab, 0xff, 0x61, 0x78, 0xb0, 0xff, 0x61, 0x77, 0xb0, 0xff, 0x64, 0x7c, 0xb6, 0xff, 0x6a, 0x84, 0xc2, 0xff, 0x6b, 0x87, 0xc8, 0xff, 0x62, 0x7e, 0xc0, 0xff, 0x61, 0x7d, 0xba, 0xff, 0x75, 0x90, 0xc7, 0xff, 0x87, 0xa2, 0xd5, 0xff, 0x8e, 0xa7, 0xd8, 0xff, 0x91, 0xa7, 0xda, 0xff, 0x8f, 0xa4, 0xd8, 0xff, 0x8d, 0xa3, 0xd6, 0xff, 0x8f, 0xa7, 0xd6, 0xff, 0x98, 0xaf, 0xdb, 0xff, 0x9b, 0xb0, 0xde, 0xff, 0x98, 0xab, 0xdb, 0xff, 0x8f, 0xa3, 0xd6, 0xff, 0x88, 0x9e, 0xd1, 0xff, 0x85, 0x9f, 0xd0, 0xff, 0x82, 0x9f, 0xd0, 0xff, 0x73, 0x8e, 0xc3, 0xff, 0x62, 0x7c, 0xbb, 0xff, 0x62, 0x80, 0xbc, 0xff, 0x5f, 0x7e, 0xb8, 0xff, 0x57, 0x73, 0xaf, 0xff, 0x5a, 0x73, 0xaf, 0xff, 0x66, 0x7e, 0xb8, 0xff, 0x60, 0x77, 0xad, 0xff, 0x53, 0x6b, 0x9b, 0xff, 0x5c, 0x6f, 0x9b, 0xff, 0x55, 0x63, 0x89, 0xff, 0x44, 0x52, 0x71, 0xff, 0x4d, 0x5a, 0x78, 0xff, 0x52, 0x5b, 0x77, 0xff, 0x50, 0x57, 0x68, 0xff, 0x25, 0x2a, 0x34, 0xff, 0x0d, 0x13, 0x16, 0xff, 0x3d, 0x41, 0x3d, 0xff, 0x5b, 0x60, 0x55, 0xff, 0x55, 0x5a, 0x51, 0xff, 0x57, 0x5c, 0x53, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x56, 0x59, 0x50, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x50, 0x52, 0x4a, 0xff, 0x4f, 0x53, 0x48, 0xff, 0x54, 0x58, 0x4f, 0xff, 0x5a, 0x5f, 0x59, 0xff, 0x60, 0x67, 0x65, 0xff, 0x66, 0x6f, 0x74, 0xff, 0x6d, 0x75, 0x80, 0xff, 0x6f, 0x79, 0x87, 0xff, 0x6c, 0x7a, 0x8a, 0xff, 0x70, 0x7e, 0x94, 0xff, 0x73, 0x80, 0x9b, 0xff, 0x71, 0x7c, 0x9a, 0xff, 0x6b, 0x76, 0x96, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x62, 0x6e, 0x90, 0xff, 0x66, 0x70, 0x92, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x66, 0x70, 0x92, 0xff, 0x67, 0x71, 0x94, 0xb3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf7, 0xf7, 0xf7, 0x22, 0xef, 0xef, 0xef, 0xfe, 0xec, 0xec, 0xec, 0xff, 0xe6, 0xe7, 0xe8, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xdd, 0xe0, 0xe1, 0xff, 0xd9, 0xdb, 0xdd, 0xff, 0xd3, 0xd7, 0xd8, 0xff, 0xd0, 0xd5, 0xd6, 0xff, 0xd3, 0xd7, 0xd8, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xdb, 0xdc, 0xdd, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd6, 0xd9, 0xda, 0xff, 0xd8, 0xd9, 0xda, 0xff, 0xdc, 0xdd, 0xdc, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf4, 0xf2, 0xf2, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf7, 0xf5, 0xf5, 0xff, 0xe8, 0xe5, 0xe4, 0xff, 0x59, 0x5f, 0x5b, 0xff, 0x0e, 0x13, 0x20, 0xff, 0x50, 0x5d, 0x7f, 0xff, 0x59, 0x6b, 0x98, 0xff, 0x54, 0x66, 0x97, 0xff, 0x54, 0x6c, 0xa2, 0xff, 0x4a, 0x66, 0x9e, 0xff, 0x4c, 0x6a, 0xa4, 0xff, 0x51, 0x6c, 0xad, 0xff, 0x56, 0x71, 0xb1, 0xff, 0x59, 0x77, 0xb3, 0xff, 0x59, 0x75, 0xae, 0xff, 0x64, 0x7c, 0xb3, 0xff, 0x5b, 0x72, 0xaa, 0xff, 0x5c, 0x73, 0xab, 0xff, 0x63, 0x7a, 0xb1, 0xff, 0x61, 0x79, 0xb3, 0xff, 0x65, 0x81, 0xbe, 0xff, 0x6a, 0x86, 0xc7, 0xff, 0x6d, 0x89, 0xc8, 0xff, 0x71, 0x8b, 0xc4, 0xff, 0x87, 0x9f, 0xd3, 0xff, 0x95, 0xae, 0xdd, 0xff, 0x96, 0xae, 0xde, 0xff, 0x93, 0xa7, 0xd8, 0xff, 0x92, 0xa4, 0xd6, 0xff, 0x99, 0xae, 0xde, 0xff, 0x9d, 0xb2, 0xdf, 0xff, 0xa3, 0xb9, 0xe2, 0xff, 0xa8, 0xbc, 0xe4, 0xff, 0xa4, 0xb6, 0xe3, 0xff, 0x9e, 0xb0, 0xe0, 0xff, 0x9d, 0xb0, 0xe2, 0xff, 0x98, 0xae, 0xde, 0xff, 0x90, 0xa9, 0xd8, 0xff, 0x7b, 0x95, 0xcb, 0xff, 0x64, 0x7d, 0xbd, 0xff, 0x5d, 0x7b, 0xb7, 0xff, 0x5b, 0x7b, 0xb4, 0xff, 0x56, 0x72, 0xae, 0xff, 0x5a, 0x72, 0xae, 0xff, 0x60, 0x78, 0xb5, 0xff, 0x61, 0x78, 0xb1, 0xff, 0x5b, 0x71, 0xa4, 0xff, 0x59, 0x6b, 0x98, 0xff, 0x54, 0x64, 0x8d, 0xff, 0x50, 0x60, 0x83, 0xff, 0x53, 0x61, 0x81, 0xff, 0x4a, 0x56, 0x73, 0xff, 0x43, 0x4d, 0x60, 0xff, 0x35, 0x3b, 0x48, 0xff, 0x1c, 0x20, 0x28, 0xff, 0x35, 0x39, 0x39, 0xff, 0x5a, 0x5e, 0x55, 0xff, 0x56, 0x5c, 0x52, 0xff, 0x58, 0x5d, 0x54, 0xff, 0x57, 0x5c, 0x53, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x57, 0x5a, 0x51, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x51, 0x53, 0x4b, 0xff, 0x4f, 0x53, 0x48, 0xff, 0x53, 0x57, 0x4e, 0xff, 0x58, 0x5e, 0x58, 0xff, 0x60, 0x67, 0x65, 0xff, 0x66, 0x6e, 0x73, 0xff, 0x6b, 0x74, 0x7e, 0xff, 0x6e, 0x79, 0x87, 0xff, 0x6d, 0x7b, 0x8b, 0xff, 0x6f, 0x7c, 0x93, 0xff, 0x71, 0x7f, 0x9a, 0xff, 0x70, 0x7c, 0x99, 0xff, 0x6b, 0x75, 0x96, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x62, 0x6e, 0x90, 0xff, 0x66, 0x70, 0x92, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x66, 0x70, 0x92, 0xff, 0x68, 0x72, 0x93, 0xfe, 0x69, 0x70, 0x96, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf5, 0xf5, 0xf5, 0x86, 0xf1, 0xf1, 0xf1, 0xff, 0xeb, 0xea, 0xea, 0xff, 0xe4, 0xe5, 0xe5, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd3, 0xd7, 0xd8, 0xff, 0xd0, 0xd5, 0xd6, 0xff, 0xd3, 0xd7, 0xd8, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd6, 0xda, 0xdb, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xda, 0xdb, 0xdc, 0xff, 0xd7, 0xd9, 0xda, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd6, 0xd8, 0xda, 0xff, 0xd7, 0xd8, 0xd9, 0xff, 0xdb, 0xda, 0xda, 0xff, 0xde, 0xde, 0xde, 0xff, 0xe2, 0xe2, 0xe2, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xed, 0xed, 0xed, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf4, 0xf2, 0xf2, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf4, 0xf1, 0xf1, 0xff, 0xf8, 0xf6, 0xf5, 0xff, 0x74, 0x79, 0x76, 0xff, 0x0e, 0x13, 0x20, 0xff, 0x4f, 0x5c, 0x7d, 0xff, 0x53, 0x65, 0x91, 0xff, 0x4c, 0x5f, 0x8f, 0xff, 0x4a, 0x62, 0x98, 0xff, 0x49, 0x65, 0x9e, 0xff, 0x4d, 0x6b, 0xa6, 0xff, 0x51, 0x6c, 0xab, 0xff, 0x52, 0x6d, 0xad, 0xff, 0x5f, 0x7e, 0xba, 0xff, 0x61, 0x7d, 0xb7, 0xff, 0x5b, 0x73, 0xac, 0xff, 0x63, 0x7a, 0xb3, 0xff, 0x5f, 0x76, 0xaf, 0xff, 0x5f, 0x76, 0xaf, 0xff, 0x60, 0x79, 0xb2, 0xff, 0x65, 0x81, 0xbe, 0xff, 0x6c, 0x88, 0xc9, 0xff, 0x74, 0x8d, 0xc9, 0xff, 0x82, 0x99, 0xcf, 0xff, 0x97, 0xae, 0xdf, 0xff, 0x9a, 0xb1, 0xde, 0xff, 0x9c, 0xb2, 0xdf, 0xff, 0xa3, 0xb3, 0xe1, 0xff, 0x9f, 0xaf, 0xde, 0xff, 0xa4, 0xb5, 0xe3, 0xff, 0xa8, 0xba, 0xe5, 0xff, 0xaf, 0xc2, 0xe9, 0xff, 0xb3, 0xc4, 0xeb, 0xff, 0xad, 0xbd, 0xe8, 0xff, 0xa9, 0xb8, 0xe7, 0xff, 0xab, 0xbc, 0xeb, 0xff, 0xa5, 0xba, 0xe7, 0xff, 0x97, 0xaf, 0xdd, 0xff, 0x86, 0xa0, 0xd6, 0xff, 0x6d, 0x88, 0xc7, 0xff, 0x5a, 0x78, 0xb4, 0xff, 0x58, 0x78, 0xb2, 0xff, 0x5e, 0x7a, 0xb7, 0xff, 0x5b, 0x74, 0xb1, 0xff, 0x57, 0x6e, 0xad, 0xff, 0x5f, 0x74, 0xb1, 0xff, 0x5c, 0x71, 0xa7, 0xff, 0x54, 0x68, 0x98, 0xff, 0x5b, 0x6d, 0x97, 0xff, 0x53, 0x66, 0x8b, 0xff, 0x4f, 0x60, 0x83, 0xff, 0x47, 0x55, 0x74, 0xff, 0x42, 0x4e, 0x64, 0xff, 0x3f, 0x46, 0x56, 0xff, 0x2c, 0x2d, 0x38, 0xff, 0x35, 0x37, 0x3a, 0xff, 0x55, 0x59, 0x53, 0xff, 0x53, 0x59, 0x4f, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x54, 0x59, 0x50, 0xff, 0x50, 0x54, 0x4b, 0xff, 0x52, 0x54, 0x4b, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x51, 0x54, 0x4b, 0xff, 0x4e, 0x51, 0x48, 0xff, 0x4d, 0x51, 0x46, 0xff, 0x52, 0x56, 0x4d, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x5f, 0x65, 0x64, 0xff, 0x64, 0x6d, 0x72, 0xff, 0x6a, 0x73, 0x7d, 0xff, 0x6e, 0x79, 0x87, 0xff, 0x6d, 0x7b, 0x8b, 0xff, 0x6e, 0x7b, 0x91, 0xff, 0x6f, 0x7d, 0x97, 0xff, 0x6f, 0x7b, 0x98, 0xff, 0x6a, 0x74, 0x94, 0xff, 0x64, 0x6e, 0x90, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x62, 0x6e, 0x90, 0xff, 0x66, 0x70, 0x92, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x66, 0x70, 0x92, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x04, 0xf3, 0xf3, 0xf3, 0xe7, 0xf2, 0xf2, 0xf2, 0xff, 0xec, 0xec, 0xec, 0xff, 0xe5, 0xe6, 0xe5, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xd8, 0xdb, 0xdc, 0xff, 0xd2, 0xd6, 0xd7, 0xff, 0xcf, 0xd3, 0xd4, 0xff, 0xd2, 0xd6, 0xd7, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd6, 0xda, 0xdb, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd9, 0xdc, 0xdd, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd8, 0xd9, 0xda, 0xff, 0xd9, 0xd9, 0xd9, 0xff, 0xde, 0xde, 0xde, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0x98, 0x98, 0x96, 0xff, 0x21, 0x26, 0x33, 0xff, 0x52, 0x62, 0x82, 0xff, 0x54, 0x66, 0x91, 0xff, 0x4a, 0x5e, 0x8c, 0xff, 0x4d, 0x66, 0x9a, 0xff, 0x49, 0x66, 0x9e, 0xff, 0x47, 0x65, 0x9e, 0xff, 0x52, 0x6e, 0xab, 0xff, 0x5f, 0x7b, 0xb9, 0xff, 0x69, 0x85, 0xc0, 0xff, 0x75, 0x8e, 0xc7, 0xff, 0x79, 0x90, 0xc8, 0xff, 0x7e, 0x96, 0xcb, 0xff, 0x7b, 0x95, 0xc6, 0xff, 0x70, 0x88, 0xbf, 0xff, 0x6a, 0x84, 0xbc, 0xff, 0x69, 0x85, 0xc0, 0xff, 0x70, 0x8e, 0xcb, 0xff, 0x7d, 0x95, 0xd3, 0xff, 0x84, 0x99, 0xd4, 0xff, 0x92, 0xa5, 0xde, 0xff, 0xa1, 0xb3, 0xe3, 0xff, 0xae, 0xbd, 0xe8, 0xff, 0xad, 0xbb, 0xe3, 0xff, 0xaf, 0xbc, 0xe4, 0xff, 0xb0, 0xbd, 0xe6, 0xff, 0xb3, 0xc0, 0xe8, 0xff, 0xb5, 0xc4, 0xec, 0xff, 0xb1, 0xc1, 0xe9, 0xff, 0xad, 0xbd, 0xe4, 0xff, 0xad, 0xbd, 0xe4, 0xff, 0xac, 0xbd, 0xe5, 0xff, 0x9e, 0xb4, 0xe3, 0xff, 0x8f, 0xab, 0xdf, 0xff, 0x7d, 0x9b, 0xd1, 0xff, 0x68, 0x85, 0xbe, 0xff, 0x5e, 0x7b, 0xb6, 0xff, 0x5b, 0x77, 0xb5, 0xff, 0x57, 0x73, 0xb0, 0xff, 0x57, 0x74, 0xad, 0xff, 0x5a, 0x71, 0xad, 0xff, 0x5c, 0x71, 0xac, 0xff, 0x59, 0x70, 0xa9, 0xff, 0x57, 0x6d, 0xa1, 0xff, 0x5a, 0x6c, 0x9a, 0xff, 0x56, 0x68, 0x91, 0xff, 0x50, 0x60, 0x83, 0xff, 0x52, 0x5f, 0x7c, 0xff, 0x52, 0x5e, 0x7a, 0xff, 0x43, 0x4d, 0x65, 0xff, 0x30, 0x33, 0x3d, 0xff, 0x3f, 0x3d, 0x3c, 0xff, 0x57, 0x59, 0x55, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x53, 0x59, 0x4f, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x4e, 0x53, 0x4a, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x4a, 0x50, 0x47, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x5f, 0x67, 0x63, 0xff, 0x65, 0x6e, 0x72, 0xff, 0x6a, 0x72, 0x7e, 0xff, 0x6c, 0x76, 0x87, 0xff, 0x6a, 0x77, 0x8a, 0xff, 0x6b, 0x79, 0x8e, 0xff, 0x6d, 0x7b, 0x94, 0xff, 0x6e, 0x7a, 0x98, 0xff, 0x68, 0x73, 0x94, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x62, 0x6f, 0x91, 0xff, 0x66, 0x70, 0x92, 0xff, 0x66, 0x70, 0x92, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x66, 0x70, 0x92, 0xe7, 0x7f, 0x7f, 0x7f, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf4, 0xf4, 0xf4, 0x49, 0xf3, 0xf3, 0xf3, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xec, 0xeb, 0xeb, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd2, 0xd6, 0xd7, 0xff, 0xd1, 0xd5, 0xd6, 0xff, 0xd3, 0xd7, 0xd8, 0xff, 0xd6, 0xda, 0xdb, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd7, 0xdc, 0xdd, 0xff, 0xda, 0xdd, 0xde, 0xff, 0xdb, 0xdc, 0xdd, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd9, 0xda, 0xda, 0xff, 0xda, 0xda, 0xd9, 0xff, 0xdf, 0xdf, 0xdf, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xef, 0xed, 0xed, 0xff, 0xfa, 0xf8, 0xf7, 0xff, 0xba, 0xb9, 0xb8, 0xff, 0x3a, 0x41, 0x4d, 0xff, 0x4f, 0x5e, 0x7f, 0xff, 0x57, 0x69, 0x94, 0xff, 0x4b, 0x60, 0x8d, 0xff, 0x4a, 0x62, 0x95, 0xff, 0x49, 0x65, 0x9c, 0xff, 0x4d, 0x6b, 0xa3, 0xff, 0x59, 0x76, 0xb3, 0xff, 0x6a, 0x84, 0xc1, 0xff, 0x7c, 0x90, 0xc9, 0xff, 0x8c, 0x9f, 0xd6, 0xff, 0x98, 0xaa, 0xde, 0xff, 0x9d, 0xb2, 0xdd, 0xff, 0x9c, 0xb1, 0xd8, 0xff, 0x91, 0xa5, 0xd4, 0xff, 0x7d, 0x96, 0xce, 0xff, 0x73, 0x8f, 0xc9, 0xff, 0x72, 0x90, 0xcc, 0xff, 0x76, 0x92, 0xd1, 0xff, 0x78, 0x92, 0xd0, 0xff, 0x7a, 0x92, 0xce, 0xff, 0x8a, 0xa1, 0xd6, 0xff, 0xa7, 0xbb, 0xec, 0xff, 0xaa, 0xbb, 0xeb, 0xff, 0xaa, 0xbd, 0xe7, 0xff, 0xaa, 0xbf, 0xe6, 0xff, 0xa7, 0xbb, 0xe3, 0xff, 0xa5, 0xba, 0xe2, 0xff, 0xa9, 0xbe, 0xe6, 0xff, 0xa4, 0xb9, 0xe1, 0xff, 0xa1, 0xb6, 0xde, 0xff, 0xa1, 0xb6, 0xe1, 0xff, 0x93, 0xab, 0xd9, 0xff, 0x87, 0xa2, 0xd4, 0xff, 0x7d, 0x9a, 0xcd, 0xff, 0x70, 0x8c, 0xc2, 0xff, 0x68, 0x82, 0xbc, 0xff, 0x63, 0x7c, 0xba, 0xff, 0x58, 0x73, 0xac, 0xff, 0x50, 0x6d, 0xa2, 0xff, 0x55, 0x6c, 0xa5, 0xff, 0x57, 0x6e, 0xa6, 0xff, 0x53, 0x6c, 0xa2, 0xff, 0x53, 0x6a, 0x9e, 0xff, 0x5a, 0x6f, 0x9d, 0xff, 0x55, 0x68, 0x92, 0xff, 0x4c, 0x5e, 0x81, 0xff, 0x51, 0x5f, 0x7f, 0xff, 0x54, 0x60, 0x7f, 0xff, 0x54, 0x60, 0x7c, 0xff, 0x38, 0x3e, 0x4b, 0xff, 0x43, 0x44, 0x40, 0xff, 0x58, 0x5e, 0x54, 0xff, 0x54, 0x59, 0x50, 0xff, 0x57, 0x5b, 0x52, 0xff, 0x55, 0x5a, 0x51, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x4a, 0x50, 0x47, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x5f, 0x67, 0x63, 0xff, 0x67, 0x6f, 0x73, 0xff, 0x6b, 0x73, 0x7f, 0xff, 0x6c, 0x76, 0x88, 0xff, 0x6a, 0x77, 0x8a, 0xff, 0x6c, 0x7a, 0x8f, 0xff, 0x6e, 0x7c, 0x94, 0xff, 0x6e, 0x7a, 0x98, 0xff, 0x68, 0x73, 0x94, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x62, 0x6f, 0x91, 0xff, 0x66, 0x70, 0x92, 0xff, 0x66, 0x70, 0x92, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0xff, 0x67, 0x71, 0x94, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf5, 0xf5, 0xf5, 0x9f, 0xf2, 0xf2, 0xf2, 0xff, 0xee, 0xee, 0xee, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xda, 0xdd, 0xde, 0xff, 0xd4, 0xd8, 0xd9, 0xff, 0xd1, 0xd6, 0xd7, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd8, 0xdc, 0xdd, 0xff, 0xdb, 0xde, 0xdf, 0xff, 0xdc, 0xdd, 0xde, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xda, 0xdb, 0xdb, 0xff, 0xdb, 0xdb, 0xdb, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xed, 0xeb, 0xeb, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xd6, 0xd5, 0xd3, 0xff, 0x56, 0x5c, 0x68, 0xff, 0x49, 0x59, 0x79, 0xff, 0x52, 0x63, 0x8f, 0xff, 0x46, 0x5a, 0x87, 0xff, 0x46, 0x5f, 0x93, 0xff, 0x4f, 0x6c, 0xa3, 0xff, 0x58, 0x76, 0xae, 0xff, 0x61, 0x7d, 0xba, 0xff, 0x70, 0x8d, 0xc7, 0xff, 0x80, 0x9b, 0xd0, 0xff, 0x93, 0xab, 0xdd, 0xff, 0x9b, 0xb2, 0xe2, 0xff, 0xa0, 0xb9, 0xe4, 0xff, 0xa6, 0xc0, 0xe5, 0xff, 0x9d, 0xb5, 0xe3, 0xff, 0x87, 0x9f, 0xd7, 0xff, 0x77, 0x93, 0xcd, 0xff, 0x77, 0x94, 0xd0, 0xff, 0x6c, 0x8e, 0xcf, 0xff, 0x6c, 0x8c, 0xcc, 0xff, 0x70, 0x8e, 0xca, 0xff, 0x78, 0x96, 0xd1, 0xff, 0x84, 0x9f, 0xda, 0xff, 0x94, 0xac, 0xe4, 0xff, 0x93, 0xac, 0xe2, 0xff, 0x8e, 0xa9, 0xde, 0xff, 0x8c, 0xa7, 0xdd, 0xff, 0x8c, 0xa6, 0xdc, 0xff, 0x8c, 0xa3, 0xd9, 0xff, 0x8b, 0xa2, 0xdb, 0xff, 0x8e, 0xa6, 0xdb, 0xff, 0x97, 0xaf, 0xdd, 0xff, 0x95, 0xad, 0xda, 0xff, 0x92, 0xa9, 0xd6, 0xff, 0x87, 0xa2, 0xd3, 0xff, 0x81, 0x9b, 0xd0, 0xff, 0x7a, 0x93, 0xcb, 0xff, 0x74, 0x8b, 0xc5, 0xff, 0x65, 0x7e, 0xb5, 0xff, 0x53, 0x6c, 0x9f, 0xff, 0x51, 0x69, 0x9d, 0xff, 0x53, 0x6b, 0xa0, 0xff, 0x4f, 0x68, 0x99, 0xff, 0x4d, 0x66, 0x97, 0xff, 0x51, 0x6b, 0x98, 0xff, 0x4f, 0x68, 0x90, 0xff, 0x4d, 0x63, 0x87, 0xff, 0x54, 0x65, 0x88, 0xff, 0x56, 0x65, 0x85, 0xff, 0x54, 0x60, 0x81, 0xff, 0x46, 0x4c, 0x5d, 0xff, 0x49, 0x50, 0x48, 0xff, 0x53, 0x5e, 0x4f, 0xff, 0x54, 0x59, 0x51, 0xff, 0x56, 0x5b, 0x52, 0xff, 0x55, 0x5a, 0x51, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x4a, 0x50, 0x47, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x60, 0x68, 0x64, 0xff, 0x68, 0x71, 0x74, 0xff, 0x6c, 0x75, 0x80, 0xff, 0x6e, 0x78, 0x8a, 0xff, 0x6c, 0x7a, 0x8c, 0xff, 0x6e, 0x7c, 0x91, 0xff, 0x6f, 0x7d, 0x96, 0xff, 0x6e, 0x7a, 0x97, 0xff, 0x68, 0x73, 0x93, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x62, 0x6f, 0x91, 0xff, 0x66, 0x70, 0x92, 0xff, 0x66, 0x70, 0x92, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0xff, 0x66, 0x70, 0x93, 0x9f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xf3, 0xf3, 0xf3, 0xef, 0xf1, 0xf1, 0xf1, 0xff, 0xee, 0xee, 0xee, 0xff, 0xeb, 0xeb, 0xea, 0xff, 0xe6, 0xe6, 0xe7, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd1, 0xd5, 0xd6, 0xff, 0xd3, 0xd7, 0xd8, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xda, 0xde, 0xdf, 0xff, 0xdb, 0xdf, 0xe0, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xdd, 0xde, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdb, 0xdd, 0xdd, 0xff, 0xdd, 0xdd, 0xdc, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xec, 0xea, 0xea, 0xff, 0xf6, 0xf2, 0xf3, 0xff, 0xdd, 0xdd, 0xd7, 0xff, 0x5a, 0x66, 0x72, 0xff, 0x48, 0x59, 0x7f, 0xff, 0x4b, 0x5c, 0x85, 0xff, 0x45, 0x59, 0x87, 0xff, 0x51, 0x6a, 0x9d, 0xff, 0x55, 0x72, 0xa9, 0xff, 0x5e, 0x7c, 0xb4, 0xff, 0x69, 0x85, 0xc2, 0xff, 0x70, 0x8e, 0xc8, 0xff, 0x76, 0x95, 0xc9, 0xff, 0x78, 0x95, 0xcd, 0xff, 0x7b, 0x97, 0xcf, 0xff, 0x85, 0xa5, 0xd9, 0xff, 0x8d, 0xac, 0xdf, 0xff, 0x88, 0xa7, 0xdd, 0xff, 0x7b, 0x97, 0xd3, 0xff, 0x6a, 0x88, 0xc6, 0xff, 0x68, 0x88, 0xc9, 0xff, 0x6b, 0x8e, 0xd1, 0xff, 0x6c, 0x8c, 0xce, 0xff, 0x68, 0x85, 0xc4, 0xff, 0x6d, 0x8a, 0xca, 0xff, 0x6e, 0x8c, 0xcd, 0xff, 0x74, 0x91, 0xd1, 0xff, 0x77, 0x97, 0xd4, 0xff, 0x76, 0x95, 0xd1, 0xff, 0x7c, 0x94, 0xd4, 0xff, 0x7d, 0x94, 0xd6, 0xff, 0x74, 0x8d, 0xcb, 0xff, 0x79, 0x91, 0xcc, 0xff, 0x88, 0x9f, 0xd4, 0xff, 0x97, 0xad, 0xd9, 0xff, 0x9d, 0xb2, 0xdf, 0xff, 0x9c, 0xb2, 0xde, 0xff, 0x96, 0xaf, 0xdd, 0xff, 0x92, 0xa9, 0xdc, 0xff, 0x89, 0x9f, 0xd7, 0xff, 0x7c, 0x96, 0xd2, 0xff, 0x74, 0x8d, 0xc6, 0xff, 0x64, 0x7a, 0xb0, 0xff, 0x4e, 0x69, 0x9b, 0xff, 0x54, 0x6f, 0xa0, 0xff, 0x57, 0x70, 0xa2, 0xff, 0x4f, 0x67, 0x97, 0xff, 0x4f, 0x66, 0x95, 0xff, 0x53, 0x68, 0x93, 0xff, 0x55, 0x68, 0x91, 0xff, 0x55, 0x68, 0x8f, 0xff, 0x55, 0x66, 0x8a, 0xff, 0x52, 0x64, 0x88, 0xff, 0x4d, 0x5a, 0x71, 0xff, 0x4c, 0x50, 0x4e, 0xff, 0x54, 0x5a, 0x49, 0xff, 0x53, 0x57, 0x4d, 0xff, 0x55, 0x59, 0x50, 0xff, 0x53, 0x57, 0x4e, 0xff, 0x50, 0x53, 0x4a, 0xff, 0x4f, 0x53, 0x4a, 0xff, 0x51, 0x54, 0x4b, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x4e, 0x53, 0x4a, 0xff, 0x4b, 0x50, 0x47, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x56, 0x5d, 0x57, 0xff, 0x60, 0x68, 0x65, 0xff, 0x69, 0x72, 0x76, 0xff, 0x6d, 0x76, 0x81, 0xff, 0x6f, 0x78, 0x87, 0xff, 0x6d, 0x78, 0x89, 0xff, 0x6e, 0x7a, 0x90, 0xff, 0x6f, 0x7b, 0x96, 0xff, 0x6d, 0x79, 0x97, 0xff, 0x67, 0x72, 0x93, 0xff, 0x62, 0x6d, 0x8f, 0xff, 0x5d, 0x69, 0x8b, 0xff, 0x5c, 0x69, 0x8b, 0xff, 0x5e, 0x6a, 0x8c, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x61, 0x6d, 0x8f, 0xff, 0x62, 0x6f, 0x91, 0xff, 0x65, 0x70, 0x92, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x68, 0x72, 0x94, 0xff, 0x68, 0x72, 0x94, 0xff, 0x69, 0x73, 0x95, 0xff, 0x67, 0x71, 0x94, 0xef, 0x6d, 0x6d, 0x91, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf4, 0xf4, 0xf4, 0x46, 0xf1, 0xf1, 0xf1, 0xff, 0xef, 0xef, 0xef, 0xff, 0xec, 0xec, 0xec, 0xff, 0xe9, 0xe8, 0xe8, 0xff, 0xe4, 0xe4, 0xe5, 0xff, 0xdf, 0xe2, 0xe3, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xda, 0xdd, 0xde, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd1, 0xd5, 0xd6, 0xff, 0xd2, 0xd6, 0xd7, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd8, 0xdc, 0xdd, 0xff, 0xd9, 0xde, 0xdf, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdb, 0xdd, 0xdd, 0xff, 0xdd, 0xdd, 0xdc, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xef, 0xed, 0xed, 0xff, 0xeb, 0xe9, 0xe9, 0xff, 0xf2, 0xef, 0xef, 0xff, 0xd8, 0xd9, 0xd1, 0xff, 0x5c, 0x6b, 0x76, 0xff, 0x48, 0x59, 0x83, 0xff, 0x49, 0x5b, 0x82, 0xff, 0x4d, 0x61, 0x8f, 0xff, 0x56, 0x6e, 0xa2, 0xff, 0x55, 0x72, 0xa9, 0xff, 0x5a, 0x78, 0xb1, 0xff, 0x60, 0x7c, 0xb8, 0xff, 0x5c, 0x7a, 0xb7, 0xff, 0x59, 0x79, 0xb7, 0xff, 0x55, 0x73, 0xb5, 0xff, 0x56, 0x74, 0xb8, 0xff, 0x5e, 0x7d, 0xc3, 0xff, 0x62, 0x82, 0xc8, 0xff, 0x6a, 0x89, 0xcd, 0xff, 0x6c, 0x89, 0xc5, 0xff, 0x60, 0x7f, 0xbc, 0xff, 0x5c, 0x7c, 0xbd, 0xff, 0x62, 0x82, 0xc5, 0xff, 0x67, 0x84, 0xc5, 0xff, 0x61, 0x7d, 0xbb, 0xff, 0x62, 0x7d, 0xb9, 0xff, 0x66, 0x81, 0xbf, 0xff, 0x6a, 0x85, 0xc4, 0xff, 0x6a, 0x88, 0xc2, 0xff, 0x6e, 0x89, 0xc1, 0xff, 0x74, 0x86, 0xbf, 0xff, 0x63, 0x74, 0xad, 0xff, 0x61, 0x77, 0xac, 0xff, 0x6a, 0x7f, 0xab, 0xff, 0x77, 0x89, 0xb1, 0xff, 0x7a, 0x87, 0xb3, 0xff, 0x7b, 0x8a, 0xb6, 0xff, 0x7f, 0x8e, 0xb9, 0xff, 0x7b, 0x8d, 0xb4, 0xff, 0x70, 0x82, 0xaa, 0xff, 0x6b, 0x7c, 0xa7, 0xff, 0x72, 0x7f, 0xa7, 0xff, 0x7a, 0x8e, 0xbb, 0xff, 0x78, 0x92, 0xca, 0xff, 0x61, 0x77, 0xb5, 0xff, 0x56, 0x6b, 0xa5, 0xff, 0x5d, 0x77, 0xa8, 0xff, 0x54, 0x6d, 0x9c, 0xff, 0x4d, 0x61, 0x91, 0xff, 0x4f, 0x60, 0x8e, 0xff, 0x55, 0x68, 0x93, 0xff, 0x54, 0x69, 0x90, 0xff, 0x58, 0x6a, 0x90, 0xff, 0x5d, 0x71, 0x96, 0xff, 0x50, 0x5f, 0x7b, 0xff, 0x4d, 0x4e, 0x52, 0xff, 0x57, 0x59, 0x4a, 0xff, 0x54, 0x56, 0x4c, 0xff, 0x57, 0x59, 0x51, 0xff, 0x54, 0x56, 0x4d, 0xff, 0x4e, 0x51, 0x48, 0xff, 0x4e, 0x51, 0x48, 0xff, 0x50, 0x52, 0x49, 0xff, 0x51, 0x55, 0x4c, 0xff, 0x4b, 0x50, 0x47, 0xff, 0x48, 0x4d, 0x44, 0xff, 0x4a, 0x4e, 0x43, 0xff, 0x51, 0x55, 0x4b, 0xff, 0x55, 0x5b, 0x55, 0xff, 0x5d, 0x65, 0x63, 0xff, 0x66, 0x6e, 0x74, 0xff, 0x69, 0x72, 0x7c, 0xff, 0x6d, 0x77, 0x84, 0xff, 0x6e, 0x78, 0x89, 0xff, 0x6e, 0x79, 0x90, 0xff, 0x6e, 0x7a, 0x96, 0xff, 0x6c, 0x78, 0x95, 0xff, 0x66, 0x71, 0x92, 0xff, 0x61, 0x6c, 0x8e, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x5b, 0x68, 0x8a, 0xff, 0x5e, 0x6a, 0x8c, 0xff, 0x5e, 0x6a, 0x8c, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x62, 0x6e, 0x90, 0xff, 0x64, 0x70, 0x92, 0xff, 0x67, 0x70, 0x92, 0xff, 0x67, 0x71, 0x93, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x72, 0x94, 0xff, 0x68, 0x72, 0x94, 0xff, 0x67, 0x71, 0x93, 0xff, 0x66, 0x70, 0x91, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xf0, 0xf0, 0x8d, 0xef, 0xef, 0xef, 0xff, 0xed, 0xed, 0xed, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe1, 0xe2, 0xe2, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdc, 0xdd, 0xde, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd1, 0xd5, 0xd6, 0xff, 0xd2, 0xd6, 0xd6, 0xff, 0xd4, 0xd8, 0xd9, 0xff, 0xd6, 0xdb, 0xdb, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xdb, 0xdc, 0xdd, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdb, 0xdd, 0xdd, 0xff, 0xdd, 0xdd, 0xdb, 0xff, 0xdf, 0xdf, 0xdf, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xec, 0xea, 0xea, 0xff, 0xf2, 0xee, 0xef, 0xff, 0xde, 0xdf, 0xd8, 0xff, 0x69, 0x77, 0x83, 0xff, 0x42, 0x55, 0x7d, 0xff, 0x4c, 0x5e, 0x85, 0xff, 0x54, 0x68, 0x96, 0xff, 0x51, 0x69, 0x9d, 0xff, 0x50, 0x6c, 0xa4, 0xff, 0x55, 0x74, 0xad, 0xff, 0x53, 0x70, 0xae, 0xff, 0x4a, 0x66, 0xa5, 0xff, 0x48, 0x61, 0x9f, 0xff, 0x42, 0x5b, 0x9d, 0xff, 0x42, 0x5b, 0x9f, 0xff, 0x45, 0x62, 0xa4, 0xff, 0x4c, 0x69, 0xaa, 0xff, 0x57, 0x72, 0xb2, 0xff, 0x5e, 0x78, 0xb0, 0xff, 0x59, 0x75, 0xaf, 0xff, 0x53, 0x71, 0xae, 0xff, 0x52, 0x70, 0xb1, 0xff, 0x5c, 0x79, 0xb8, 0xff, 0x58, 0x72, 0xae, 0xff, 0x55, 0x6b, 0xa3, 0xff, 0x60, 0x76, 0xad, 0xff, 0x5e, 0x75, 0xae, 0xff, 0x56, 0x6b, 0xa3, 0xff, 0x5e, 0x70, 0xa3, 0xff, 0x58, 0x67, 0x95, 0xff, 0x43, 0x50, 0x79, 0xff, 0x48, 0x54, 0x7a, 0xff, 0x48, 0x54, 0x73, 0xff, 0x4d, 0x58, 0x74, 0xff, 0x4b, 0x57, 0x75, 0xff, 0x49, 0x56, 0x73, 0xff, 0x51, 0x5c, 0x79, 0xff, 0x4b, 0x52, 0x70, 0xff, 0x47, 0x51, 0x6a, 0xff, 0x40, 0x4a, 0x5d, 0xff, 0x47, 0x4a, 0x5d, 0xff, 0x5d, 0x66, 0x84, 0xff, 0x72, 0x86, 0xaf, 0xff, 0x79, 0x8a, 0xbc, 0xff, 0x68, 0x7b, 0xaf, 0xff, 0x58, 0x73, 0xa5, 0xff, 0x56, 0x6f, 0xa0, 0xff, 0x53, 0x68, 0x97, 0xff, 0x49, 0x5b, 0x88, 0xff, 0x4d, 0x5f, 0x89, 0xff, 0x52, 0x67, 0x8e, 0xff, 0x59, 0x6c, 0x90, 0xff, 0x5d, 0x70, 0x97, 0xff, 0x5a, 0x66, 0x86, 0xff, 0x55, 0x54, 0x5a, 0xff, 0x54, 0x57, 0x4a, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x55, 0x58, 0x4f, 0xff, 0x51, 0x54, 0x4b, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x4e, 0x51, 0x48, 0xff, 0x50, 0x54, 0x4b, 0xff, 0x4a, 0x50, 0x47, 0xff, 0x47, 0x4c, 0x43, 0xff, 0x4b, 0x4f, 0x44, 0xff, 0x52, 0x56, 0x4c, 0xff, 0x56, 0x5c, 0x55, 0xff, 0x5d, 0x64, 0x62, 0xff, 0x64, 0x6c, 0x71, 0xff, 0x68, 0x71, 0x7b, 0xff, 0x6c, 0x76, 0x83, 0xff, 0x6d, 0x77, 0x87, 0xff, 0x6d, 0x78, 0x8f, 0xff, 0x6e, 0x7a, 0x95, 0xff, 0x6c, 0x77, 0x95, 0xff, 0x65, 0x70, 0x91, 0xff, 0x61, 0x6b, 0x8d, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x5e, 0x6a, 0x8c, 0xff, 0x5d, 0x69, 0x8b, 0xff, 0x5f, 0x6b, 0x8d, 0xff, 0x61, 0x6e, 0x90, 0xff, 0x63, 0x6e, 0x90, 0xff, 0x66, 0x6f, 0x91, 0xff, 0x66, 0x70, 0x92, 0xff, 0x66, 0x70, 0x92, 0xff, 0x66, 0x70, 0x92, 0xff, 0x67, 0x71, 0x93, 0xff, 0x67, 0x71, 0x93, 0xff, 0x68, 0x70, 0x94, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xf0, 0xf0, 0xd7, 0xef, 0xef, 0xef, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xe2, 0xe2, 0xe2, 0xff, 0xdf, 0xe0, 0xe0, 0xff, 0xde, 0xe0, 0xe2, 0xff, 0xdc, 0xdd, 0xde, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd5, 0xd8, 0xdb, 0xff, 0xd1, 0xd4, 0xd8, 0xff, 0xd3, 0xd7, 0xd9, 0xff, 0xd7, 0xda, 0xdd, 0xff, 0xda, 0xdd, 0xe0, 0xff, 0xdb, 0xde, 0xe1, 0xff, 0xdb, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xde, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xdd, 0xdd, 0xff, 0xdf, 0xde, 0xde, 0xff, 0xe2, 0xe2, 0xe2, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xed, 0xed, 0xed, 0xff, 0xef, 0xee, 0xee, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xef, 0xed, 0xed, 0xff, 0xec, 0xea, 0xea, 0xff, 0xf1, 0xee, 0xee, 0xff, 0xe4, 0xe6, 0xde, 0xff, 0x6d, 0x7a, 0x86, 0xff, 0x42, 0x4e, 0x7a, 0xff, 0x57, 0x66, 0x90, 0xff, 0x5f, 0x72, 0xa3, 0xff, 0x5f, 0x78, 0xac, 0xff, 0x5b, 0x7a, 0xb1, 0xff, 0x5d, 0x7a, 0xb5, 0xff, 0x56, 0x72, 0xac, 0xff, 0x4b, 0x67, 0x98, 0xff, 0x44, 0x5c, 0x8b, 0xff, 0x3a, 0x4c, 0x82, 0xff, 0x36, 0x46, 0x7b, 0xff, 0x38, 0x4e, 0x7c, 0xff, 0x3e, 0x55, 0x87, 0xff, 0x40, 0x57, 0x8d, 0xff, 0x41, 0x5b, 0x92, 0xff, 0x44, 0x5e, 0x97, 0xff, 0x41, 0x5c, 0x97, 0xff, 0x4b, 0x64, 0x9f, 0xff, 0x51, 0x69, 0xa2, 0xff, 0x4f, 0x67, 0xa0, 0xff, 0x49, 0x5e, 0x95, 0xff, 0x4f, 0x62, 0x96, 0xff, 0x4d, 0x61, 0x90, 0xff, 0x37, 0x49, 0x7a, 0xff, 0x38, 0x46, 0x74, 0xff, 0x3a, 0x40, 0x63, 0xff, 0x36, 0x3c, 0x59, 0xff, 0x2c, 0x33, 0x4d, 0xff, 0x25, 0x28, 0x3b, 0xff, 0x2d, 0x31, 0x3d, 0xff, 0x29, 0x31, 0x3d, 0xff, 0x22, 0x2a, 0x37, 0xff, 0x1b, 0x21, 0x31, 0xff, 0x25, 0x27, 0x3d, 0xff, 0x2d, 0x31, 0x46, 0xff, 0x2a, 0x30, 0x43, 0xff, 0x26, 0x2b, 0x45, 0xff, 0x27, 0x2f, 0x49, 0xff, 0x35, 0x3f, 0x58, 0xff, 0x50, 0x60, 0x78, 0xff, 0x6c, 0x83, 0xa5, 0xff, 0x58, 0x76, 0xa8, 0xff, 0x56, 0x70, 0xa6, 0xff, 0x5d, 0x71, 0xa3, 0xff, 0x4c, 0x62, 0x8e, 0xff, 0x4a, 0x5d, 0x88, 0xff, 0x53, 0x63, 0x8c, 0xff, 0x59, 0x69, 0x8c, 0xff, 0x54, 0x63, 0x84, 0xff, 0x51, 0x5f, 0x7b, 0xff, 0x51, 0x57, 0x61, 0xff, 0x56, 0x58, 0x4e, 0xff, 0x57, 0x5b, 0x4e, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x55, 0x58, 0x50, 0xff, 0x53, 0x56, 0x4d, 0xff, 0x51, 0x54, 0x4a, 0xff, 0x52, 0x55, 0x4c, 0xff, 0x51, 0x55, 0x4c, 0xff, 0x4b, 0x50, 0x47, 0xff, 0x4a, 0x4f, 0x46, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x58, 0x5e, 0x57, 0xff, 0x5e, 0x65, 0x63, 0xff, 0x65, 0x6e, 0x72, 0xff, 0x69, 0x71, 0x7d, 0xff, 0x6b, 0x74, 0x84, 0xff, 0x69, 0x75, 0x87, 0xff, 0x6b, 0x78, 0x8d, 0xff, 0x6b, 0x78, 0x91, 0xff, 0x69, 0x75, 0x93, 0xff, 0x64, 0x6f, 0x90, 0xff, 0x60, 0x6b, 0x8d, 0xff, 0x5d, 0x69, 0x8b, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x5d, 0x69, 0x8b, 0xff, 0x5e, 0x6a, 0x8c, 0xff, 0x60, 0x6c, 0x8e, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x64, 0x6e, 0x90, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x68, 0x72, 0x94, 0xff, 0x66, 0x70, 0x92, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x64, 0x6f, 0x91, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xeb, 0xeb, 0xeb, 0x1a, 0xf0, 0xf0, 0xf0, 0xff, 0xed, 0xed, 0xed, 0xff, 0xea, 0xea, 0xea, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xde, 0xde, 0xdf, 0xff, 0xdd, 0xdf, 0xe1, 0xff, 0xdb, 0xde, 0xde, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd3, 0xd6, 0xdb, 0xff, 0xd0, 0xd2, 0xd8, 0xff, 0xd3, 0xd6, 0xda, 0xff, 0xd7, 0xda, 0xde, 0xff, 0xda, 0xdd, 0xe1, 0xff, 0xdb, 0xde, 0xe3, 0xff, 0xdd, 0xdf, 0xe1, 0xff, 0xde, 0xdf, 0xe0, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdc, 0xdd, 0xde, 0xff, 0xe1, 0xe0, 0xe0, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf1, 0xf0, 0xf0, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xf0, 0xee, 0xee, 0xff, 0xec, 0xea, 0xea, 0xff, 0xee, 0xeb, 0xeb, 0xff, 0xe0, 0xe3, 0xdb, 0xff, 0x71, 0x7e, 0x88, 0xff, 0x4e, 0x57, 0x80, 0xff, 0x6c, 0x7a, 0xa4, 0xff, 0x75, 0x89, 0xbb, 0xff, 0x70, 0x8b, 0xbd, 0xff, 0x67, 0x83, 0xb3, 0xff, 0x61, 0x76, 0xa9, 0xff, 0x50, 0x65, 0x91, 0xff, 0x40, 0x58, 0x77, 0xff, 0x31, 0x48, 0x69, 0xff, 0x2b, 0x3b, 0x62, 0xff, 0x2f, 0x3d, 0x64, 0xff, 0x29, 0x3c, 0x60, 0xff, 0x27, 0x3a, 0x67, 0xff, 0x2d, 0x42, 0x74, 0xff, 0x2c, 0x46, 0x77, 0xff, 0x2c, 0x45, 0x7a, 0xff, 0x33, 0x4a, 0x82, 0xff, 0x40, 0x54, 0x8a, 0xff, 0x44, 0x59, 0x8e, 0xff, 0x43, 0x58, 0x8e, 0xff, 0x37, 0x4c, 0x83, 0xff, 0x36, 0x4a, 0x7c, 0xff, 0x37, 0x4a, 0x75, 0xff, 0x2e, 0x40, 0x67, 0xff, 0x27, 0x36, 0x59, 0xff, 0x1e, 0x25, 0x3e, 0xff, 0x1d, 0x23, 0x3a, 0xff, 0x14, 0x1d, 0x32, 0xff, 0x08, 0x0f, 0x1c, 0xff, 0x0a, 0x11, 0x1d, 0xff, 0x0a, 0x12, 0x24, 0xff, 0x11, 0x17, 0x2c, 0xff, 0x18, 0x1e, 0x37, 0xff, 0x26, 0x33, 0x4d, 0xff, 0x2d, 0x3d, 0x5a, 0xff, 0x37, 0x48, 0x6b, 0xff, 0x36, 0x4b, 0x6f, 0xff, 0x2a, 0x3a, 0x5c, 0xff, 0x25, 0x2b, 0x4a, 0xff, 0x2a, 0x33, 0x4e, 0xff, 0x55, 0x65, 0x87, 0xff, 0x6c, 0x82, 0xb2, 0xff, 0x65, 0x7d, 0xb5, 0xff, 0x63, 0x78, 0xad, 0xff, 0x54, 0x6c, 0x97, 0xff, 0x51, 0x64, 0x8d, 0xff, 0x5b, 0x6a, 0x91, 0xff, 0x5b, 0x69, 0x8b, 0xff, 0x50, 0x5c, 0x78, 0xff, 0x4e, 0x5c, 0x76, 0xff, 0x4f, 0x5b, 0x67, 0xff, 0x58, 0x5a, 0x51, 0xff, 0x57, 0x5b, 0x4d, 0xff, 0x58, 0x5b, 0x52, 0xff, 0x54, 0x56, 0x50, 0xff, 0x51, 0x53, 0x4b, 0xff, 0x4e, 0x51, 0x48, 0xff, 0x51, 0x54, 0x4b, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x4a, 0x4f, 0x46, 0xff, 0x4a, 0x4f, 0x46, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x5e, 0x65, 0x62, 0xff, 0x65, 0x6e, 0x72, 0xff, 0x69, 0x71, 0x7d, 0xff, 0x68, 0x73, 0x84, 0xff, 0x67, 0x75, 0x88, 0xff, 0x6a, 0x78, 0x8d, 0xff, 0x69, 0x78, 0x90, 0xff, 0x69, 0x74, 0x92, 0xff, 0x64, 0x6f, 0x8f, 0xff, 0x5f, 0x69, 0x8c, 0xff, 0x5d, 0x69, 0x8b, 0xff, 0x5b, 0x67, 0x89, 0xff, 0x5a, 0x66, 0x88, 0xff, 0x5b, 0x67, 0x89, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x5e, 0x6a, 0x8c, 0xff, 0x62, 0x6d, 0x8f, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x64, 0x6e, 0x90, 0xff, 0x69, 0x73, 0x95, 0xff, 0x65, 0x6f, 0x91, 0xff, 0x63, 0x6d, 0x90, 0xff, 0x63, 0x6e, 0x90, 0xff, 0x5e, 0x71, 0x8d, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xed, 0xed, 0xed, 0x58, 0xec, 0xec, 0xec, 0xff, 0xea, 0xea, 0xea, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xe2, 0xe1, 0xe1, 0xff, 0xde, 0xdd, 0xdd, 0xff, 0xdd, 0xdd, 0xdd, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd8, 0xda, 0xdc, 0xff, 0xd5, 0xd8, 0xdc, 0xff, 0xd1, 0xd4, 0xd9, 0xff, 0xd4, 0xd6, 0xdb, 0xff, 0xd7, 0xda, 0xde, 0xff, 0xda, 0xdd, 0xe1, 0xff, 0xdb, 0xde, 0xe2, 0xff, 0xdc, 0xdf, 0xe0, 0xff, 0xdd, 0xdf, 0xdf, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xdb, 0xde, 0xdf, 0xff, 0xdd, 0xde, 0xde, 0xff, 0xe1, 0xe1, 0xe1, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xed, 0xed, 0xed, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf3, 0xf0, 0xf0, 0xff, 0xf1, 0xef, 0xef, 0xff, 0xee, 0xeb, 0xeb, 0xff, 0xef, 0xeb, 0xec, 0xff, 0xe5, 0xe6, 0xe1, 0xff, 0x81, 0x90, 0x98, 0xff, 0x61, 0x70, 0x92, 0xff, 0x76, 0x85, 0xa4, 0xff, 0x67, 0x74, 0x97, 0xff, 0x51, 0x5f, 0x7e, 0xff, 0x41, 0x50, 0x6c, 0xff, 0x39, 0x44, 0x66, 0xff, 0x2e, 0x39, 0x52, 0xff, 0x28, 0x33, 0x43, 0xff, 0x25, 0x2d, 0x42, 0xff, 0x24, 0x2d, 0x46, 0xff, 0x21, 0x2d, 0x49, 0xff, 0x15, 0x25, 0x44, 0xff, 0x16, 0x27, 0x4a, 0xff, 0x23, 0x35, 0x5d, 0xff, 0x21, 0x34, 0x62, 0xff, 0x22, 0x35, 0x65, 0xff, 0x2b, 0x3c, 0x70, 0xff, 0x2b, 0x3e, 0x73, 0xff, 0x2f, 0x43, 0x76, 0xff, 0x32, 0x45, 0x78, 0xff, 0x2e, 0x44, 0x78, 0xff, 0x29, 0x41, 0x6f, 0xff, 0x25, 0x3b, 0x63, 0xff, 0x24, 0x37, 0x59, 0xff, 0x19, 0x2a, 0x4a, 0xff, 0x12, 0x21, 0x3d, 0xff, 0x13, 0x1f, 0x3b, 0xff, 0x15, 0x21, 0x40, 0xff, 0x12, 0x23, 0x40, 0xff, 0x0f, 0x22, 0x42, 0xff, 0x19, 0x2a, 0x52, 0xff, 0x2b, 0x3a, 0x66, 0xff, 0x3d, 0x4c, 0x7a, 0xff, 0x46, 0x5c, 0x86, 0xff, 0x3f, 0x56, 0x84, 0xff, 0x3b, 0x55, 0x86, 0xff, 0x3b, 0x59, 0x89, 0xff, 0x43, 0x5c, 0x8a, 0xff, 0x41, 0x55, 0x80, 0xff, 0x34, 0x44, 0x69, 0xff, 0x3e, 0x4e, 0x77, 0xff, 0x6a, 0x79, 0xaa, 0xff, 0x69, 0x7e, 0xb5, 0xff, 0x63, 0x7a, 0xae, 0xff, 0x5d, 0x74, 0xa0, 0xff, 0x54, 0x69, 0x8f, 0xff, 0x58, 0x69, 0x8b, 0xff, 0x53, 0x63, 0x80, 0xff, 0x46, 0x55, 0x70, 0xff, 0x4d, 0x59, 0x77, 0xff, 0x57, 0x5e, 0x6e, 0xff, 0x58, 0x58, 0x53, 0xff, 0x51, 0x53, 0x45, 0xff, 0x54, 0x57, 0x4a, 0xff, 0x54, 0x55, 0x4d, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x4d, 0x4f, 0x47, 0xff, 0x50, 0x52, 0x48, 0xff, 0x4f, 0x55, 0x4c, 0xff, 0x49, 0x50, 0x47, 0xff, 0x4a, 0x4f, 0x46, 0xff, 0x4c, 0x50, 0x45, 0xff, 0x54, 0x57, 0x4e, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x5e, 0x65, 0x62, 0xff, 0x65, 0x6e, 0x72, 0xff, 0x69, 0x71, 0x7d, 0xff, 0x69, 0x73, 0x84, 0xff, 0x68, 0x75, 0x88, 0xff, 0x6a, 0x79, 0x8d, 0xff, 0x6a, 0x79, 0x91, 0xff, 0x6b, 0x76, 0x94, 0xff, 0x64, 0x6e, 0x8f, 0xff, 0x5e, 0x68, 0x8b, 0xff, 0x5b, 0x68, 0x8a, 0xff, 0x59, 0x65, 0x87, 0xff, 0x59, 0x65, 0x87, 0xff, 0x58, 0x65, 0x87, 0xff, 0x5a, 0x66, 0x88, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x61, 0x6c, 0x8e, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x64, 0x6e, 0x90, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x67, 0x71, 0x93, 0xff, 0x64, 0x6e, 0x90, 0xff, 0x62, 0x6c, 0x8f, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x5f, 0x6b, 0x8b, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xeb, 0xeb, 0xeb, 0x90, 0xeb, 0xeb, 0xeb, 0xff, 0xe9, 0xe8, 0xe8, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xde, 0xde, 0xde, 0xff, 0xdb, 0xdc, 0xdc, 0xff, 0xdb, 0xdc, 0xdd, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdb, 0xdd, 0xdd, 0xff, 0xd8, 0xdb, 0xdc, 0xff, 0xd6, 0xda, 0xdc, 0xff, 0xd6, 0xd9, 0xdc, 0xff, 0xd8, 0xdb, 0xde, 0xff, 0xda, 0xdd, 0xe1, 0xff, 0xdd, 0xe0, 0xe4, 0xff, 0xde, 0xe2, 0xe5, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe3, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdf, 0xe0, 0xe0, 0xff, 0xe2, 0xe1, 0xe1, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf3, 0xf2, 0xf2, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf3, 0xf1, 0xf1, 0xff, 0xf3, 0xf2, 0xf2, 0xff, 0xf0, 0xef, 0xef, 0xff, 0xed, 0xeb, 0xec, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xe9, 0xea, 0xe6, 0xff, 0x9b, 0xa3, 0xae, 0xff, 0x76, 0x84, 0x9d, 0xff, 0x62, 0x6c, 0x7f, 0xff, 0x3c, 0x40, 0x52, 0xff, 0x2d, 0x31, 0x3d, 0xff, 0x1e, 0x22, 0x30, 0xff, 0x15, 0x1b, 0x2e, 0xff, 0x14, 0x1a, 0x27, 0xff, 0x16, 0x18, 0x22, 0xff, 0x12, 0x13, 0x20, 0xff, 0x10, 0x18, 0x25, 0xff, 0x0d, 0x19, 0x28, 0xff, 0x0f, 0x1b, 0x33, 0xff, 0x14, 0x21, 0x40, 0xff, 0x18, 0x26, 0x4b, 0xff, 0x18, 0x29, 0x50, 0xff, 0x22, 0x32, 0x5b, 0xff, 0x29, 0x38, 0x64, 0xff, 0x26, 0x38, 0x67, 0xff, 0x29, 0x3b, 0x6f, 0xff, 0x2c, 0x40, 0x73, 0xff, 0x31, 0x45, 0x76, 0xff, 0x2f, 0x42, 0x70, 0xff, 0x23, 0x36, 0x5f, 0xff, 0x1b, 0x2d, 0x53, 0xff, 0x18, 0x29, 0x50, 0xff, 0x1a, 0x2a, 0x50, 0xff, 0x19, 0x25, 0x4e, 0xff, 0x1d, 0x28, 0x56, 0xff, 0x20, 0x31, 0x5f, 0xff, 0x1c, 0x30, 0x5f, 0xff, 0x29, 0x3d, 0x6c, 0xff, 0x3f, 0x53, 0x84, 0xff, 0x4c, 0x5f, 0x92, 0xff, 0x4f, 0x63, 0x98, 0xff, 0x4c, 0x60, 0x97, 0xff, 0x3d, 0x52, 0x8a, 0xff, 0x33, 0x49, 0x83, 0xff, 0x32, 0x4c, 0x83, 0xff, 0x35, 0x52, 0x85, 0xff, 0x40, 0x58, 0x85, 0xff, 0x39, 0x50, 0x7b, 0xff, 0x54, 0x6c, 0x98, 0xff, 0x6b, 0x83, 0xb4, 0xff, 0x68, 0x7e, 0xb1, 0xff, 0x63, 0x7a, 0xa9, 0xff, 0x56, 0x69, 0x93, 0xff, 0x4e, 0x5f, 0x7f, 0xff, 0x49, 0x59, 0x72, 0xff, 0x46, 0x52, 0x6f, 0xff, 0x4f, 0x5a, 0x7d, 0xff, 0x55, 0x60, 0x70, 0xff, 0x55, 0x57, 0x48, 0xff, 0x57, 0x5c, 0x47, 0xff, 0x5c, 0x61, 0x58, 0xff, 0x54, 0x55, 0x4c, 0xff, 0x4f, 0x50, 0x47, 0xff, 0x4c, 0x53, 0x4a, 0xff, 0x4d, 0x54, 0x4b, 0xff, 0x51, 0x55, 0x4c, 0xff, 0x4c, 0x50, 0x47, 0xff, 0x4a, 0x4f, 0x46, 0xff, 0x4b, 0x4f, 0x44, 0xff, 0x53, 0x56, 0x4e, 0xff, 0x58, 0x5d, 0x57, 0xff, 0x5f, 0x66, 0x63, 0xff, 0x65, 0x6e, 0x72, 0xff, 0x69, 0x71, 0x7d, 0xff, 0x69, 0x73, 0x84, 0xff, 0x69, 0x74, 0x88, 0xff, 0x6a, 0x76, 0x8d, 0xff, 0x6b, 0x79, 0x92, 0xff, 0x6c, 0x77, 0x95, 0xff, 0x64, 0x6e, 0x8f, 0xff, 0x5d, 0x67, 0x8a, 0xff, 0x5a, 0x65, 0x87, 0xff, 0x5a, 0x65, 0x87, 0xff, 0x5a, 0x65, 0x87, 0xff, 0x59, 0x64, 0x86, 0xff, 0x5a, 0x66, 0x88, 0xff, 0x5c, 0x67, 0x89, 0xff, 0x5f, 0x6a, 0x8c, 0xff, 0x60, 0x6a, 0x8c, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x66, 0x70, 0x92, 0xff, 0x63, 0x6d, 0x8f, 0xff, 0x62, 0x6c, 0x8f, 0xff, 0x62, 0x6d, 0x8f, 0xff, 0x5f, 0x6c, 0x8a, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xea, 0xea, 0xea, 0xc6, 0xea, 0xea, 0xea, 0xff, 0xe8, 0xe7, 0xe7, 0xff, 0xde, 0xdf, 0xdf, 0xff, 0xd7, 0xda, 0xda, 0xff, 0xd7, 0xd9, 0xd9, 0xff, 0xd9, 0xdb, 0xdb, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xd9, 0xdc, 0xdd, 0xff, 0xd5, 0xd9, 0xda, 0xff, 0xd7, 0xdb, 0xdb, 0xff, 0xda, 0xde, 0xde, 0xff, 0xdb, 0xdf, 0xe2, 0xff, 0xdd, 0xe0, 0xe5, 0xff, 0xdf, 0xe2, 0xe6, 0xff, 0xe2, 0xe3, 0xe6, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xdf, 0xe1, 0xe2, 0xff, 0xe0, 0xe1, 0xe2, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xea, 0xea, 0xea, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf2, 0xf3, 0xf3, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf0, 0xf1, 0xf1, 0xff, 0xeb, 0xeb, 0xec, 0xff, 0xea, 0xea, 0xe9, 0xff, 0xed, 0xef, 0xeb, 0xff, 0xb6, 0xb7, 0xc2, 0xff, 0x5a, 0x62, 0x74, 0xff, 0x24, 0x2d, 0x3f, 0xff, 0x17, 0x1d, 0x2f, 0xff, 0x13, 0x19, 0x2b, 0xff, 0x13, 0x19, 0x2b, 0xff, 0x12, 0x19, 0x2a, 0xff, 0x0d, 0x15, 0x27, 0xff, 0x0f, 0x15, 0x29, 0xff, 0x13, 0x19, 0x2c, 0xff, 0x10, 0x1b, 0x2c, 0xff, 0x0d, 0x1b, 0x2c, 0xff, 0x10, 0x1b, 0x35, 0xff, 0x13, 0x1d, 0x3e, 0xff, 0x0e, 0x1a, 0x40, 0xff, 0x0e, 0x22, 0x43, 0xff, 0x17, 0x2a, 0x4d, 0xff, 0x1f, 0x2f, 0x56, 0xff, 0x28, 0x39, 0x64, 0xff, 0x2e, 0x3f, 0x75, 0xff, 0x34, 0x4a, 0x7c, 0xff, 0x35, 0x48, 0x78, 0xff, 0x32, 0x42, 0x72, 0xff, 0x27, 0x38, 0x66, 0xff, 0x19, 0x2d, 0x58, 0xff, 0x17, 0x2a, 0x55, 0xff, 0x1c, 0x2c, 0x56, 0xff, 0x1a, 0x28, 0x58, 0xff, 0x1b, 0x2b, 0x5f, 0xff, 0x1f, 0x2f, 0x60, 0xff, 0x1e, 0x2f, 0x5d, 0xff, 0x23, 0x32, 0x5f, 0xff, 0x30, 0x3f, 0x6c, 0xff, 0x3f, 0x4e, 0x7c, 0xff, 0x39, 0x4d, 0x7e, 0xff, 0x2f, 0x42, 0x73, 0xff, 0x2b, 0x3e, 0x6e, 0xff, 0x33, 0x47, 0x79, 0xff, 0x31, 0x45, 0x78, 0xff, 0x32, 0x46, 0x79, 0xff, 0x38, 0x4a, 0x7d, 0xff, 0x42, 0x57, 0x89, 0xff, 0x55, 0x6e, 0x9e, 0xff, 0x69, 0x83, 0xb1, 0xff, 0x66, 0x7d, 0xac, 0xff, 0x66, 0x7b, 0xad, 0xff, 0x5b, 0x6c, 0x9a, 0xff, 0x4e, 0x5d, 0x7d, 0xff, 0x4a, 0x58, 0x70, 0xff, 0x4b, 0x54, 0x73, 0xff, 0x54, 0x61, 0x83, 0xff, 0x47, 0x56, 0x67, 0xff, 0x6a, 0x73, 0x71, 0xff, 0xaf, 0xb9, 0xca, 0xff, 0x97, 0xa4, 0xc2, 0xff, 0x62, 0x6c, 0x77, 0xff, 0x51, 0x53, 0x44, 0xff, 0x51, 0x56, 0x45, 0xff, 0x53, 0x55, 0x4f, 0xff, 0x55, 0x57, 0x50, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x4d, 0x50, 0x47, 0xff, 0x4d, 0x50, 0x45, 0xff, 0x54, 0x58, 0x4f, 0xff, 0x5a, 0x5f, 0x5a, 0xff, 0x61, 0x69, 0x66, 0xff, 0x65, 0x6d, 0x71, 0xff, 0x67, 0x71, 0x7c, 0xff, 0x6a, 0x72, 0x84, 0xff, 0x6c, 0x74, 0x8a, 0xff, 0x6b, 0x76, 0x8e, 0xff, 0x6d, 0x79, 0x94, 0xff, 0x6b, 0x76, 0x94, 0xff, 0x64, 0x6e, 0x8f, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5c, 0x65, 0x87, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5e, 0x67, 0x89, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x61, 0x6b, 0x8d, 0xff, 0x61, 0x6b, 0x8d, 0xff, 0x64, 0x6e, 0x90, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x63, 0x6d, 0x90, 0xff, 0x62, 0x6d, 0x8e, 0xff, 0x5e, 0x69, 0x88, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0xff, 0xff, 0xff, 0x01, 0xe8, 0xe8, 0xe8, 0xf2, 0xe8, 0xe8, 0xe8, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xd9, 0xda, 0xda, 0xff, 0xd4, 0xd6, 0xd6, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xdc, 0xde, 0xe0, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xd9, 0xdc, 0xdd, 0xff, 0xd6, 0xda, 0xdb, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xdb, 0xdf, 0xe0, 0xff, 0xdd, 0xe0, 0xe4, 0xff, 0xde, 0xe1, 0xe5, 0xff, 0xe0, 0xe3, 0xe8, 0xff, 0xe3, 0xe5, 0xe7, 0xff, 0xe2, 0xe4, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe2, 0xe3, 0xe4, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xec, 0xec, 0xec, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xed, 0xed, 0xed, 0xff, 0xeb, 0xea, 0xeb, 0xff, 0xf1, 0xf2, 0xef, 0xff, 0xc0, 0xc8, 0xc7, 0xff, 0x30, 0x39, 0x48, 0xff, 0x06, 0x0f, 0x29, 0xff, 0x1d, 0x27, 0x46, 0xff, 0x1e, 0x2c, 0x50, 0xff, 0x20, 0x31, 0x55, 0xff, 0x22, 0x32, 0x55, 0xff, 0x22, 0x32, 0x54, 0xff, 0x1e, 0x2e, 0x51, 0xff, 0x1d, 0x2d, 0x51, 0xff, 0x1c, 0x2a, 0x4e, 0xff, 0x19, 0x25, 0x48, 0xff, 0x15, 0x1f, 0x42, 0xff, 0x11, 0x1f, 0x42, 0xff, 0x0d, 0x1f, 0x42, 0xff, 0x0c, 0x1f, 0x42, 0xff, 0x0d, 0x20, 0x47, 0xff, 0x14, 0x25, 0x4d, 0xff, 0x1e, 0x30, 0x5b, 0xff, 0x34, 0x45, 0x7b, 0xff, 0x45, 0x5a, 0x8c, 0xff, 0x3b, 0x50, 0x82, 0xff, 0x2e, 0x41, 0x74, 0xff, 0x24, 0x38, 0x67, 0xff, 0x17, 0x2e, 0x5b, 0xff, 0x14, 0x2a, 0x58, 0xff, 0x1c, 0x2d, 0x5c, 0xff, 0x1b, 0x2c, 0x5c, 0xff, 0x13, 0x24, 0x52, 0xff, 0x0f, 0x1d, 0x44, 0xff, 0x10, 0x1b, 0x3b, 0xff, 0x19, 0x22, 0x45, 0xff, 0x29, 0x33, 0x58, 0xff, 0x3d, 0x49, 0x6f, 0xff, 0x38, 0x4c, 0x73, 0xff, 0x2b, 0x3f, 0x64, 0xff, 0x22, 0x35, 0x5a, 0xff, 0x1d, 0x30, 0x52, 0xff, 0x21, 0x32, 0x56, 0xff, 0x2f, 0x3c, 0x67, 0xff, 0x34, 0x44, 0x6f, 0xff, 0x3a, 0x4c, 0x7b, 0xff, 0x4a, 0x5d, 0x92, 0xff, 0x5e, 0x76, 0xa9, 0xff, 0x60, 0x78, 0xa7, 0xff, 0x62, 0x78, 0xa9, 0xff, 0x61, 0x72, 0x9f, 0xff, 0x52, 0x61, 0x81, 0xff, 0x48, 0x58, 0x70, 0xff, 0x4a, 0x55, 0x75, 0xff, 0x44, 0x4e, 0x6f, 0xff, 0x43, 0x4d, 0x6c, 0xff, 0x91, 0xa0, 0xcc, 0xff, 0xa7, 0xb7, 0xf8, 0xff, 0x4a, 0x5e, 0xa6, 0xff, 0x58, 0x6c, 0x9e, 0xff, 0x63, 0x6e, 0x73, 0xff, 0x55, 0x53, 0x40, 0xff, 0x5d, 0x59, 0x4d, 0xff, 0x55, 0x57, 0x50, 0xff, 0x4f, 0x52, 0x49, 0xff, 0x4d, 0x50, 0x47, 0xff, 0x4e, 0x51, 0x47, 0xff, 0x55, 0x59, 0x50, 0xff, 0x5b, 0x61, 0x5c, 0xff, 0x61, 0x69, 0x64, 0xff, 0x64, 0x6c, 0x6f, 0xff, 0x67, 0x70, 0x7b, 0xff, 0x6a, 0x71, 0x84, 0xff, 0x6b, 0x74, 0x89, 0xff, 0x6b, 0x77, 0x8d, 0xff, 0x6b, 0x77, 0x92, 0xff, 0x68, 0x73, 0x91, 0xff, 0x63, 0x6d, 0x8e, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x60, 0x6a, 0x8c, 0xff, 0x61, 0x6b, 0x8d, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x61, 0x6b, 0x8d, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x61, 0x6c, 0x8d, 0xff, 0x5d, 0x69, 0x87, 0xf2, 0x00, 0x00, 0xff, 0x01,
    0xe7, 0xe7, 0xe7, 0x21, 0xe6, 0xe6, 0xe6, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xd8, 0xd9, 0xd9, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd7, 0xd9, 0xd9, 0xff, 0xda, 0xdc, 0xdc, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd7, 0xdb, 0xdd, 0xff, 0xd8, 0xdb, 0xdd, 0xff, 0xdb, 0xde, 0xe0, 0xff, 0xdd, 0xe0, 0xe4, 0xff, 0xde, 0xe2, 0xe6, 0xff, 0xe0, 0xe4, 0xe8, 0xff, 0xe2, 0xe5, 0xe6, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe2, 0xe4, 0xe4, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xea, 0xea, 0xea, 0xff, 0xed, 0xed, 0xed, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xed, 0xed, 0xed, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xf3, 0xf3, 0xf2, 0xff, 0xc5, 0xcb, 0xc7, 0xff, 0x30, 0x3d, 0x47, 0xff, 0x18, 0x25, 0x44, 0xff, 0x25, 0x34, 0x5d, 0xff, 0x1d, 0x34, 0x63, 0xff, 0x21, 0x37, 0x66, 0xff, 0x29, 0x3c, 0x6a, 0xff, 0x29, 0x3f, 0x6e, 0xff, 0x27, 0x3f, 0x6d, 0xff, 0x21, 0x39, 0x67, 0xff, 0x21, 0x31, 0x61, 0xff, 0x21, 0x2b, 0x5a, 0xff, 0x1a, 0x28, 0x4f, 0xff, 0x10, 0x23, 0x47, 0xff, 0x0b, 0x20, 0x43, 0xff, 0x0a, 0x1d, 0x41, 0xff, 0x0e, 0x1d, 0x41, 0xff, 0x11, 0x1e, 0x45, 0xff, 0x1c, 0x2c, 0x58, 0xff, 0x3e, 0x51, 0x85, 0xff, 0x5a, 0x70, 0xa4, 0xff, 0x53, 0x69, 0x9e, 0xff, 0x3d, 0x53, 0x8a, 0xff, 0x26, 0x3e, 0x6e, 0xff, 0x1a, 0x31, 0x5f, 0xff, 0x14, 0x2b, 0x58, 0xff, 0x15, 0x2a, 0x59, 0xff, 0x17, 0x24, 0x55, 0xff, 0x15, 0x1c, 0x42, 0xff, 0x0b, 0x10, 0x26, 0xff, 0x0d, 0x11, 0x21, 0xff, 0x0f, 0x16, 0x28, 0xff, 0x14, 0x18, 0x25, 0xff, 0x23, 0x27, 0x33, 0xff, 0x16, 0x1f, 0x31, 0xff, 0x1c, 0x26, 0x3c, 0xff, 0x1f, 0x2a, 0x3f, 0xff, 0x22, 0x2c, 0x3d, 0xff, 0x1c, 0x24, 0x3e, 0xff, 0x1e, 0x28, 0x4a, 0xff, 0x2a, 0x3b, 0x5c, 0xff, 0x2f, 0x42, 0x66, 0xff, 0x3b, 0x4e, 0x7b, 0xff, 0x58, 0x6f, 0x9f, 0xff, 0x62, 0x79, 0xa8, 0xff, 0x5f, 0x74, 0xa5, 0xff, 0x63, 0x75, 0xa2, 0xff, 0x59, 0x68, 0x88, 0xff, 0x4b, 0x58, 0x70, 0xff, 0x41, 0x4c, 0x66, 0xff, 0x34, 0x3d, 0x58, 0xff, 0x67, 0x75, 0xa4, 0xff, 0x83, 0x9c, 0xdc, 0xff, 0x44, 0x59, 0x9b, 0xff, 0x00, 0x12, 0x61, 0xff, 0x29, 0x3d, 0x8a, 0xff, 0x62, 0x72, 0x99, 0xff, 0x57, 0x58, 0x4a, 0xff, 0x58, 0x5a, 0x46, 0xff, 0x50, 0x54, 0x4e, 0xff, 0x4f, 0x51, 0x48, 0xff, 0x4b, 0x4e, 0x45, 0xff, 0x4a, 0x4d, 0x43, 0xff, 0x51, 0x55, 0x4c, 0xff, 0x57, 0x5d, 0x57, 0xff, 0x5f, 0x66, 0x63, 0xff, 0x62, 0x6b, 0x6f, 0xff, 0x65, 0x6e, 0x7a, 0xff, 0x67, 0x6f, 0x81, 0xff, 0x67, 0x71, 0x86, 0xff, 0x68, 0x73, 0x8b, 0xff, 0x68, 0x75, 0x90, 0xff, 0x67, 0x73, 0x90, 0xff, 0x62, 0x6d, 0x8e, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x60, 0x6a, 0x8c, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x61, 0x6b, 0x8d, 0xff, 0x61, 0x6b, 0x8e, 0xff, 0x60, 0x6a, 0x8c, 0xff, 0x5e, 0x69, 0x88, 0xff, 0x5c, 0x6c, 0x83, 0x21,
    0xea, 0xea, 0xea, 0x4b, 0xe6, 0xe6, 0xe6, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xe1, 0xe0, 0xe0, 0xff, 0xda, 0xdb, 0xdb, 0xff, 0xd6, 0xd8, 0xd8, 0xff, 0xd7, 0xd9, 0xd9, 0xff, 0xda, 0xdc, 0xdc, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd7, 0xda, 0xde, 0xff, 0xd7, 0xda, 0xde, 0xff, 0xd9, 0xdc, 0xe0, 0xff, 0xdc, 0xdf, 0xe3, 0xff, 0xde, 0xe2, 0xe6, 0xff, 0xdf, 0xe3, 0xe7, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe2, 0xe3, 0xe3, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xed, 0xed, 0xed, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xed, 0xed, 0xed, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xef, 0xf0, 0xee, 0xff, 0xcb, 0xce, 0xcf, 0xff, 0x40, 0x50, 0x5c, 0xff, 0x10, 0x24, 0x45, 0xff, 0x19, 0x2b, 0x55, 0xff, 0x1b, 0x32, 0x5f, 0xff, 0x26, 0x37, 0x63, 0xff, 0x2c, 0x37, 0x60, 0xff, 0x2a, 0x3c, 0x67, 0xff, 0x2a, 0x41, 0x6a, 0xff, 0x32, 0x49, 0x6d, 0xff, 0x35, 0x42, 0x68, 0xff, 0x28, 0x30, 0x56, 0xff, 0x15, 0x24, 0x45, 0xff, 0x13, 0x24, 0x46, 0xff, 0x0e, 0x21, 0x46, 0xff, 0x10, 0x21, 0x3f, 0xff, 0x10, 0x1a, 0x33, 0xff, 0x10, 0x1c, 0x3c, 0xff, 0x28, 0x3b, 0x69, 0xff, 0x58, 0x6d, 0xa0, 0xff, 0x7b, 0x93, 0xc8, 0xff, 0x78, 0x8e, 0xc6, 0xff, 0x54, 0x6e, 0xaa, 0xff, 0x33, 0x4f, 0x84, 0xff, 0x2b, 0x40, 0x6d, 0xff, 0x20, 0x36, 0x5d, 0xff, 0x1f, 0x34, 0x5a, 0xff, 0x1d, 0x2b, 0x54, 0xff, 0x0b, 0x0c, 0x26, 0xff, 0x0e, 0x12, 0x23, 0xff, 0x39, 0x3f, 0x55, 0xff, 0x12, 0x13, 0x20, 0xff, 0x03, 0x00, 0x00, 0xff, 0x13, 0x0c, 0x07, 0xff, 0x00, 0x00, 0x00, 0xff, 0x16, 0x15, 0x24, 0xff, 0x08, 0x08, 0x18, 0xff, 0x08, 0x07, 0x12, 0xff, 0x14, 0x19, 0x34, 0xff, 0x11, 0x21, 0x40, 0xff, 0x1a, 0x29, 0x4a, 0xff, 0x2c, 0x3b, 0x63, 0xff, 0x37, 0x4b, 0x79, 0xff, 0x4b, 0x60, 0x90, 0xff, 0x5f, 0x72, 0xa2, 0xff, 0x61, 0x75, 0xa6, 0xff, 0x61, 0x76, 0xa5, 0xff, 0x60, 0x6e, 0x91, 0xff, 0x50, 0x5a, 0x73, 0xff, 0x31, 0x3c, 0x52, 0xff, 0x42, 0x4b, 0x6d, 0xff, 0x6d, 0x84, 0xc0, 0xff, 0x50, 0x6f, 0xb1, 0xff, 0x2b, 0x41, 0x7f, 0xff, 0x19, 0x2a, 0x71, 0xff, 0x12, 0x25, 0x76, 0xff, 0x4e, 0x5b, 0x97, 0xff, 0x60, 0x62, 0x5e, 0xff, 0x50, 0x55, 0x3e, 0xff, 0x51, 0x53, 0x4e, 0xff, 0x50, 0x53, 0x4a, 0xff, 0x4a, 0x4d, 0x44, 0xff, 0x48, 0x4c, 0x41, 0xff, 0x4f, 0x53, 0x4a, 0xff, 0x55, 0x5b, 0x54, 0xff, 0x5e, 0x64, 0x65, 0xff, 0x62, 0x69, 0x72, 0xff, 0x64, 0x6c, 0x78, 0xff, 0x64, 0x6d, 0x7f, 0xff, 0x63, 0x6f, 0x84, 0xff, 0x67, 0x74, 0x8c, 0xff, 0x69, 0x77, 0x91, 0xff, 0x68, 0x74, 0x91, 0xff, 0x63, 0x6e, 0x8f, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x58, 0x62, 0x84, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x60, 0x6a, 0x8c, 0xff, 0x61, 0x6b, 0x8e, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5e, 0x69, 0x88, 0xff, 0x5b, 0x69, 0x84, 0x4b,
    0xe9, 0xe9, 0xe9, 0x6b, 0xe6, 0xe6, 0xe6, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xe1, 0xe0, 0xe0, 0xff, 0xda, 0xdb, 0xdb, 0xff, 0xd6, 0xd8, 0xd8, 0xff, 0xd7, 0xd9, 0xd9, 0xff, 0xda, 0xdc, 0xdc, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd7, 0xda, 0xde, 0xff, 0xd7, 0xda, 0xde, 0xff, 0xd9, 0xdc, 0xe0, 0xff, 0xdc, 0xdf, 0xe3, 0xff, 0xde, 0xe2, 0xe6, 0xff, 0xdf, 0xe3, 0xe7, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe0, 0xe2, 0xe4, 0xff, 0xe2, 0xe3, 0xe4, 0xff, 0xe8, 0xe8, 0xe9, 0xff, 0xed, 0xed, 0xed, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xed, 0xed, 0xed, 0xff, 0xea, 0xeb, 0xea, 0xff, 0xec, 0xed, 0xed, 0xff, 0xd9, 0xda, 0xde, 0xff, 0x4f, 0x5e, 0x6f, 0xff, 0x11, 0x22, 0x42, 0xff, 0x21, 0x2d, 0x50, 0xff, 0x21, 0x2d, 0x4e, 0xff, 0x1e, 0x28, 0x48, 0xff, 0x23, 0x2a, 0x48, 0xff, 0x21, 0x29, 0x43, 0xff, 0x22, 0x2b, 0x3e, 0xff, 0x25, 0x2c, 0x3a, 0xff, 0x2b, 0x30, 0x3f, 0xff, 0x1b, 0x1e, 0x2e, 0xff, 0x0c, 0x0f, 0x22, 0xff, 0x10, 0x18, 0x33, 0xff, 0x16, 0x25, 0x45, 0xff, 0x0d, 0x1e, 0x37, 0xff, 0x09, 0x16, 0x2e, 0xff, 0x1a, 0x2d, 0x52, 0xff, 0x47, 0x5f, 0x92, 0xff, 0x7a, 0x8f, 0xc4, 0xff, 0x98, 0xac, 0xdc, 0xff, 0x91, 0xa8, 0xdd, 0xff, 0x65, 0x82, 0xc2, 0xff, 0x45, 0x5f, 0xa0, 0xff, 0x36, 0x48, 0x80, 0xff, 0x41, 0x54, 0x7d, 0xff, 0x49, 0x5e, 0x81, 0xff, 0x17, 0x2b, 0x4a, 0xff, 0x03, 0x0e, 0x1f, 0xff, 0x1b, 0x23, 0x3c, 0xff, 0x43, 0x4a, 0x6e, 0xff, 0x24, 0x2a, 0x43, 0xff, 0x19, 0x1f, 0x30, 0xff, 0x2e, 0x34, 0x44, 0xff, 0x36, 0x3f, 0x4f, 0xff, 0x34, 0x3b, 0x5b, 0xff, 0x27, 0x30, 0x51, 0xff, 0x20, 0x2a, 0x48, 0xff, 0x29, 0x36, 0x5e, 0xff, 0x38, 0x4b, 0x72, 0xff, 0x32, 0x41, 0x68, 0xff, 0x3c, 0x4a, 0x79, 0xff, 0x44, 0x59, 0x8d, 0xff, 0x4d, 0x63, 0x98, 0xff, 0x56, 0x6b, 0x9e, 0xff, 0x60, 0x75, 0xaa, 0xff, 0x62, 0x78, 0xab, 0xff, 0x5d, 0x6e, 0x93, 0xff, 0x4a, 0x55, 0x6d, 0xff, 0x32, 0x43, 0x61, 0xff, 0x52, 0x67, 0x9c, 0xff, 0x4c, 0x62, 0xa5, 0xff, 0x42, 0x5c, 0x9d, 0xff, 0x4f, 0x68, 0xa5, 0xff, 0x33, 0x49, 0x8e, 0xff, 0x07, 0x1c, 0x6c, 0xff, 0x3c, 0x46, 0x9e, 0xff, 0x63, 0x6a, 0x80, 0xff, 0x53, 0x58, 0x40, 0xff, 0x56, 0x57, 0x4d, 0xff, 0x4f, 0x53, 0x4a, 0xff, 0x4c, 0x4e, 0x45, 0xff, 0x4b, 0x4e, 0x43, 0xff, 0x50, 0x54, 0x4b, 0xff, 0x56, 0x5c, 0x55, 0xff, 0x5f, 0x65, 0x66, 0xff, 0x63, 0x6a, 0x73, 0xff, 0x65, 0x6e, 0x79, 0xff, 0x65, 0x6e, 0x80, 0xff, 0x64, 0x6f, 0x84, 0xff, 0x67, 0x74, 0x8c, 0xff, 0x69, 0x77, 0x91, 0xff, 0x68, 0x74, 0x91, 0xff, 0x63, 0x6e, 0x8f, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x58, 0x62, 0x84, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x61, 0x6b, 0x8d, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x60, 0x6a, 0x8d, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x68, 0x87, 0xff, 0x5c, 0x66, 0x83, 0x6b,
    0xe8, 0xe8, 0xe8, 0x8b, 0xe6, 0xe6, 0xe6, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xe1, 0xe0, 0xe0, 0xff, 0xda, 0xdb, 0xdb, 0xff, 0xd6, 0xd8, 0xd8, 0xff, 0xd7, 0xd9, 0xd9, 0xff, 0xda, 0xdc, 0xdc, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xd8, 0xdb, 0xde, 0xff, 0xd7, 0xdb, 0xde, 0xff, 0xd9, 0xdc, 0xe0, 0xff, 0xdc, 0xdf, 0xe3, 0xff, 0xde, 0xe2, 0xe5, 0xff, 0xdf, 0xe3, 0xe7, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe0, 0xe2, 0xe4, 0xff, 0xe3, 0xe4, 0xe4, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xed, 0xed, 0xed, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf1, 0xf2, 0xf2, 0xff, 0xed, 0xed, 0xec, 0xff, 0xec, 0xea, 0xea, 0xff, 0xf6, 0xf4, 0xf5, 0xff, 0xe2, 0xe4, 0xe8, 0xff, 0x51, 0x5e, 0x73, 0xff, 0x17, 0x25, 0x45, 0xff, 0x22, 0x29, 0x44, 0xff, 0x1d, 0x20, 0x35, 0xff, 0x19, 0x1b, 0x2f, 0xff, 0x19, 0x1a, 0x2d, 0xff, 0x23, 0x23, 0x2d, 0xff, 0x0a, 0x09, 0x08, 0xff, 0x07, 0x06, 0x02, 0xff, 0x10, 0x10, 0x0e, 0xff, 0x06, 0x08, 0x08, 0xff, 0x28, 0x28, 0x32, 0xff, 0x06, 0x0c, 0x1f, 0xff, 0x11, 0x1d, 0x39, 0xff, 0x16, 0x23, 0x42, 0xff, 0x05, 0x14, 0x34, 0xff, 0x34, 0x4c, 0x7b, 0xff, 0x6f, 0x89, 0xc1, 0xff, 0x92, 0xa9, 0xdc, 0xff, 0xa4, 0xb6, 0xe4, 0xff, 0x97, 0xaf, 0xe5, 0xff, 0x76, 0x96, 0xda, 0xff, 0x53, 0x70, 0xb8, 0xff, 0x3e, 0x52, 0x93, 0xff, 0x46, 0x59, 0x8a, 0xff, 0x55, 0x67, 0x90, 0xff, 0x3a, 0x4c, 0x76, 0xff, 0x2e, 0x3f, 0x65, 0xff, 0x28, 0x34, 0x55, 0xff, 0x1b, 0x27, 0x52, 0xff, 0x22, 0x31, 0x5e, 0xff, 0x2c, 0x40, 0x68, 0xff, 0x40, 0x55, 0x7c, 0xff, 0x50, 0x62, 0x80, 0xff, 0x39, 0x47, 0x71, 0xff, 0x2d, 0x3f, 0x6b, 0xff, 0x37, 0x4b, 0x75, 0xff, 0x41, 0x53, 0x84, 0xff, 0x5f, 0x73, 0xa1, 0xff, 0x69, 0x7b, 0xa9, 0xff, 0x65, 0x79, 0xad, 0xff, 0x69, 0x80, 0xb9, 0xff, 0x66, 0x7d, 0xb4, 0xff, 0x59, 0x70, 0xa4, 0xff, 0x5b, 0x72, 0xa8, 0xff, 0x61, 0x79, 0xad, 0xff, 0x5b, 0x6d, 0x95, 0xff, 0x4b, 0x58, 0x72, 0xff, 0x4a, 0x60, 0x85, 0xff, 0x49, 0x68, 0xa2, 0xff, 0x41, 0x5c, 0x96, 0xff, 0x5c, 0x79, 0xb0, 0xff, 0x70, 0x8c, 0xca, 0xff, 0x4f, 0x6a, 0xae, 0xff, 0x10, 0x28, 0x75, 0xff, 0x26, 0x35, 0x93, 0xff, 0x5a, 0x69, 0x90, 0xff, 0x5b, 0x5c, 0x51, 0xff, 0x59, 0x58, 0x50, 0xff, 0x4d, 0x52, 0x48, 0xff, 0x4c, 0x4f, 0x46, 0xff, 0x4c, 0x4f, 0x44, 0xff, 0x51, 0x55, 0x4b, 0xff, 0x57, 0x5d, 0x56, 0xff, 0x5f, 0x66, 0x66, 0xff, 0x64, 0x6b, 0x73, 0xff, 0x66, 0x6f, 0x7a, 0xff, 0x65, 0x6e, 0x80, 0xff, 0x64, 0x6f, 0x84, 0xff, 0x67, 0x74, 0x8c, 0xff, 0x69, 0x77, 0x91, 0xff, 0x68, 0x74, 0x91, 0xff, 0x63, 0x6e, 0x8f, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x58, 0x62, 0x84, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x60, 0x6a, 0x8c, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5f, 0x69, 0x8c, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x67, 0x86, 0xff, 0x5b, 0x66, 0x84, 0x8b,
    0xe5, 0xe8, 0xe8, 0xaa, 0xe3, 0xe5, 0xe5, 0xff, 0xe2, 0xe4, 0xe4, 0xff, 0xde, 0xe0, 0xe0, 0xff, 0xd9, 0xdb, 0xdb, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xd9, 0xdb, 0xda, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xda, 0xda, 0xdf, 0xff, 0xd9, 0xd8, 0xe0, 0xff, 0xd5, 0xd8, 0xdd, 0xff, 0xd4, 0xd7, 0xdd, 0xff, 0xd6, 0xd9, 0xdf, 0xff, 0xda, 0xde, 0xe0, 0xff, 0xdd, 0xe2, 0xe2, 0xff, 0xdf, 0xe3, 0xe3, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe2, 0xe5, 0xe6, 0xff, 0xe3, 0xe5, 0xe5, 0xff, 0xe8, 0xe8, 0xe7, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf4, 0xf4, 0xf5, 0xff, 0xef, 0xf1, 0xf2, 0xff, 0xef, 0xee, 0xe9, 0xff, 0xf1, 0xe7, 0xe8, 0xff, 0xe5, 0xe5, 0xf0, 0xff, 0xc4, 0xcf, 0xde, 0xff, 0x47, 0x56, 0x70, 0xff, 0x16, 0x22, 0x40, 0xff, 0x14, 0x1b, 0x2a, 0xff, 0x0e, 0x10, 0x14, 0xff, 0x02, 0x00, 0x09, 0xff, 0x05, 0x06, 0x12, 0xff, 0x33, 0x36, 0x44, 0xff, 0x1f, 0x26, 0x31, 0xff, 0x17, 0x20, 0x29, 0xff, 0x20, 0x27, 0x35, 0xff, 0x2d, 0x33, 0x49, 0xff, 0x3a, 0x41, 0x64, 0xff, 0x20, 0x29, 0x4e, 0xff, 0x19, 0x24, 0x4a, 0xff, 0x17, 0x25, 0x47, 0xff, 0x14, 0x29, 0x4f, 0xff, 0x5e, 0x76, 0xa8, 0xff, 0x8c, 0xa5, 0xde, 0xff, 0xa9, 0xbb, 0xeb, 0xff, 0xb1, 0xc3, 0xf2, 0xff, 0xa0, 0xb9, 0xee, 0xff, 0x82, 0xa4, 0xe4, 0xff, 0x62, 0x84, 0xcc, 0xff, 0x45, 0x66, 0xa6, 0xff, 0x3e, 0x5a, 0x93, 0xff, 0x55, 0x67, 0x9c, 0xff, 0x57, 0x66, 0x94, 0xff, 0x38, 0x49, 0x71, 0xff, 0x21, 0x30, 0x57, 0xff, 0x1c, 0x27, 0x4d, 0xff, 0x1a, 0x24, 0x4c, 0xff, 0x12, 0x23, 0x4b, 0xff, 0x12, 0x25, 0x4c, 0xff, 0x19, 0x27, 0x4e, 0xff, 0x20, 0x32, 0x57, 0xff, 0x28, 0x3d, 0x63, 0xff, 0x41, 0x55, 0x82, 0xff, 0x5b, 0x71, 0xa1, 0xff, 0x6c, 0x83, 0xb4, 0xff, 0x6e, 0x88, 0xbd, 0xff, 0x6c, 0x8c, 0xc8, 0xff, 0x76, 0x95, 0xcd, 0xff, 0x70, 0x87, 0xbc, 0xff, 0x67, 0x7d, 0xb1, 0xff, 0x5c, 0x75, 0xa5, 0xff, 0x5e, 0x73, 0xa6, 0xff, 0x60, 0x6f, 0x99, 0xff, 0x50, 0x62, 0x82, 0xff, 0x4d, 0x60, 0x95, 0xff, 0x45, 0x58, 0x93, 0xff, 0x42, 0x60, 0x8f, 0xff, 0x68, 0x86, 0xbd, 0xff, 0x70, 0x8c, 0xd2, 0xff, 0x5f, 0x81, 0xca, 0xff, 0x2e, 0x46, 0x8e, 0xff, 0x16, 0x29, 0x81, 0xff, 0x49, 0x5c, 0x86, 0xff, 0x59, 0x5c, 0x4f, 0xff, 0x51, 0x55, 0x4a, 0xff, 0x4a, 0x50, 0x47, 0xff, 0x49, 0x4e, 0x46, 0xff, 0x4c, 0x50, 0x46, 0xff, 0x53, 0x57, 0x4e, 0xff, 0x58, 0x5e, 0x57, 0xff, 0x5e, 0x66, 0x64, 0xff, 0x64, 0x6c, 0x71, 0xff, 0x66, 0x6f, 0x79, 0xff, 0x65, 0x6d, 0x7d, 0xff, 0x65, 0x6f, 0x82, 0xff, 0x68, 0x74, 0x89, 0xff, 0x6a, 0x77, 0x90, 0xff, 0x68, 0x73, 0x91, 0xff, 0x62, 0x6d, 0x8d, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x58, 0x62, 0x84, 0xff, 0x58, 0x62, 0x84, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x67, 0x86, 0xff, 0x5a, 0x66, 0x84, 0xab,
    0xe5, 0xe8, 0xe8, 0xbe, 0xe3, 0xe5, 0xe5, 0xff, 0xe2, 0xe4, 0xe4, 0xff, 0xde, 0xe0, 0xe0, 0xff, 0xd9, 0xdb, 0xdb, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xd8, 0xda, 0xd9, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd9, 0xda, 0xde, 0xff, 0xd8, 0xd7, 0xdf, 0xff, 0xd7, 0xd6, 0xe0, 0xff, 0xd3, 0xd4, 0xde, 0xff, 0xd3, 0xd4, 0xde, 0xff, 0xd5, 0xd6, 0xe0, 0xff, 0xd8, 0xdc, 0xe0, 0xff, 0xdc, 0xdf, 0xe3, 0xff, 0xdd, 0xe0, 0xe4, 0xff, 0xdf, 0xe0, 0xe2, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe2, 0xe5, 0xe6, 0xff, 0xe3, 0xe5, 0xe5, 0xff, 0xe8, 0xe8, 0xe7, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xf4, 0xf4, 0xf3, 0xff, 0xf4, 0xf4, 0xf5, 0xff, 0xf2, 0xf1, 0xf3, 0xff, 0xef, 0xf1, 0xeb, 0xff, 0xea, 0xe9, 0xe7, 0xff, 0x95, 0x9e, 0xb9, 0xff, 0x82, 0x90, 0xb7, 0xff, 0x46, 0x5a, 0x75, 0xff, 0x02, 0x11, 0x22, 0xff, 0x06, 0x12, 0x1d, 0xff, 0x04, 0x0c, 0x1a, 0xff, 0x0e, 0x17, 0x2c, 0xff, 0x18, 0x29, 0x42, 0xff, 0x1d, 0x2c, 0x51, 0xff, 0x21, 0x30, 0x5b, 0xff, 0x2c, 0x3a, 0x62, 0xff, 0x2f, 0x3e, 0x65, 0xff, 0x30, 0x3d, 0x64, 0xff, 0x2c, 0x39, 0x5c, 0xff, 0x2c, 0x3b, 0x63, 0xff, 0x25, 0x36, 0x5d, 0xff, 0x1a, 0x30, 0x53, 0xff, 0x40, 0x53, 0x89, 0xff, 0x7c, 0x90, 0xcd, 0xff, 0xa8, 0xbb, 0xec, 0xff, 0xc5, 0xd1, 0xf7, 0xff, 0xc4, 0xcf, 0xf6, 0xff, 0xb2, 0xc5, 0xef, 0xff, 0x8d, 0xaa, 0xe2, 0xff, 0x6f, 0x90, 0xd2, 0xff, 0x5d, 0x7b, 0xc5, 0xff, 0x4e, 0x67, 0xb4, 0xff, 0x52, 0x6b, 0xab, 0xff, 0x5c, 0x70, 0xa7, 0xff, 0x52, 0x63, 0x93, 0xff, 0x3d, 0x4f, 0x79, 0xff, 0x31, 0x40, 0x64, 0xff, 0x2f, 0x3b, 0x5c, 0xff, 0x2c, 0x39, 0x5a, 0xff, 0x29, 0x36, 0x58, 0xff, 0x30, 0x3b, 0x62, 0xff, 0x33, 0x42, 0x66, 0xff, 0x3f, 0x50, 0x75, 0xff, 0x52, 0x68, 0x95, 0xff, 0x67, 0x7f, 0xb1, 0xff, 0x70, 0x89, 0xbe, 0xff, 0x68, 0x82, 0xba, 0xff, 0x5c, 0x7b, 0xba, 0xff, 0x5d, 0x7a, 0xb6, 0xff, 0x66, 0x7c, 0xb2, 0xff, 0x6d, 0x83, 0xb6, 0xff, 0x64, 0x7c, 0xac, 0xff, 0x5e, 0x73, 0xa5, 0xff, 0x64, 0x73, 0x9d, 0xff, 0x56, 0x67, 0x88, 0xff, 0x45, 0x56, 0x88, 0xff, 0x3f, 0x50, 0x86, 0xff, 0x32, 0x4b, 0x7d, 0xff, 0x3a, 0x54, 0x91, 0xff, 0x4b, 0x67, 0xae, 0xff, 0x5d, 0x7f, 0xc9, 0xff, 0x52, 0x6a, 0xaf, 0xff, 0x20, 0x36, 0x87, 0xff, 0x47, 0x57, 0x86, 0xff, 0x5a, 0x5c, 0x50, 0xff, 0x4d, 0x52, 0x46, 0xff, 0x4b, 0x50, 0x47, 0xff, 0x4a, 0x50, 0x47, 0xff, 0x4e, 0x53, 0x48, 0xff, 0x56, 0x5a, 0x51, 0xff, 0x5b, 0x60, 0x5a, 0xff, 0x61, 0x68, 0x66, 0xff, 0x65, 0x6e, 0x73, 0xff, 0x67, 0x70, 0x7a, 0xff, 0x66, 0x6f, 0x7f, 0xff, 0x66, 0x70, 0x83, 0xff, 0x68, 0x74, 0x88, 0xff, 0x69, 0x76, 0x8f, 0xff, 0x67, 0x72, 0x90, 0xff, 0x61, 0x6b, 0x8c, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x58, 0x62, 0x84, 0xff, 0x58, 0x62, 0x84, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x67, 0x86, 0xff, 0x5b, 0x67, 0x83, 0xbe,
    0xe5, 0xe8, 0xe8, 0xd3, 0xe3, 0xe5, 0xe5, 0xff, 0xe2, 0xe4, 0xe4, 0xff, 0xde, 0xe0, 0xe0, 0xff, 0xd9, 0xdb, 0xdb, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd7, 0xd6, 0xde, 0xff, 0xd6, 0xd5, 0xdf, 0xff, 0xd5, 0xd3, 0xdf, 0xff, 0xd2, 0xd2, 0xde, 0xff, 0xd1, 0xd1, 0xde, 0xff, 0xd3, 0xd3, 0xdf, 0xff, 0xd7, 0xd9, 0xe1, 0xff, 0xda, 0xdd, 0xe4, 0xff, 0xdb, 0xdd, 0xe6, 0xff, 0xdc, 0xde, 0xe1, 0xff, 0xdf, 0xe1, 0xe1, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xe2, 0xe5, 0xe6, 0xff, 0xe3, 0xe5, 0xe5, 0xff, 0xe8, 0xe8, 0xe7, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf4, 0xf3, 0xf4, 0xff, 0xf2, 0xef, 0xf1, 0xff, 0xeb, 0xef, 0xea, 0xff, 0xef, 0xf7, 0xf4, 0xff, 0x70, 0x7e, 0x91, 0xff, 0x58, 0x61, 0x8c, 0xff, 0x4f, 0x59, 0x79, 0xff, 0x0b, 0x17, 0x2c, 0xff, 0x30, 0x3d, 0x54, 0xff, 0x2f, 0x39, 0x5c, 0xff, 0x24, 0x2e, 0x59, 0xff, 0x1f, 0x30, 0x56, 0xff, 0x1a, 0x2f, 0x54, 0xff, 0x15, 0x27, 0x51, 0xff, 0x14, 0x22, 0x50, 0xff, 0x10, 0x21, 0x45, 0xff, 0x11, 0x23, 0x45, 0xff, 0x23, 0x33, 0x59, 0xff, 0x32, 0x44, 0x6c, 0xff, 0x2f, 0x45, 0x6d, 0xff, 0x3a, 0x52, 0x88, 0xff, 0x63, 0x74, 0xbc, 0xff, 0x93, 0xa8, 0xe1, 0xff, 0xc1, 0xcf, 0xf7, 0xff, 0xcf, 0xd4, 0xf4, 0xff, 0xcb, 0xd0, 0xf1, 0xff, 0xbf, 0xcb, 0xed, 0xff, 0x9a, 0xb1, 0xe4, 0xff, 0x79, 0x96, 0xd9, 0xff, 0x6f, 0x8e, 0xd1, 0xff, 0x63, 0x84, 0xc7, 0xff, 0x55, 0x72, 0xbb, 0xff, 0x5b, 0x75, 0xc0, 0xff, 0x66, 0x7f, 0xc4, 0xff, 0x64, 0x7f, 0xb8, 0xff, 0x57, 0x70, 0xa2, 0xff, 0x4d, 0x61, 0x94, 0xff, 0x4b, 0x59, 0x8e, 0xff, 0x4d, 0x58, 0x8d, 0xff, 0x50, 0x60, 0x92, 0xff, 0x51, 0x65, 0x97, 0xff, 0x53, 0x69, 0x9a, 0xff, 0x61, 0x78, 0xab, 0xff, 0x78, 0x91, 0xc6, 0xff, 0x80, 0x9a, 0xd3, 0xff, 0x79, 0x92, 0xce, 0xff, 0x6b, 0x88, 0xc9, 0xff, 0x5b, 0x76, 0xb6, 0xff, 0x5b, 0x71, 0xa7, 0xff, 0x63, 0x79, 0xab, 0xff, 0x65, 0x7d, 0xac, 0xff, 0x5d, 0x72, 0xa4, 0xff, 0x5e, 0x6e, 0x97, 0xff, 0x5a, 0x6c, 0x8d, 0xff, 0x55, 0x66, 0x95, 0xff, 0x3e, 0x4c, 0x7e, 0xff, 0x1c, 0x30, 0x65, 0xff, 0x12, 0x29, 0x69, 0xff, 0x2b, 0x48, 0x8d, 0xff, 0x3e, 0x5e, 0xa9, 0xff, 0x5d, 0x76, 0xb9, 0xff, 0x42, 0x59, 0xa4, 0xff, 0x4c, 0x5a, 0x8e, 0xff, 0x5b, 0x5b, 0x54, 0xff, 0x51, 0x57, 0x49, 0xff, 0x4e, 0x53, 0x4a, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x53, 0x57, 0x4c, 0xff, 0x59, 0x5d, 0x54, 0xff, 0x5e, 0x64, 0x5e, 0xff, 0x63, 0x6a, 0x68, 0xff, 0x67, 0x6f, 0x74, 0xff, 0x6a, 0x72, 0x7c, 0xff, 0x68, 0x70, 0x80, 0xff, 0x67, 0x70, 0x83, 0xff, 0x66, 0x72, 0x88, 0xff, 0x68, 0x74, 0x8d, 0xff, 0x66, 0x72, 0x8f, 0xff, 0x60, 0x6b, 0x8c, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x58, 0x62, 0x84, 0xff, 0x58, 0x62, 0x84, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5d, 0x67, 0x89, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x67, 0x86, 0xff, 0x5a, 0x66, 0x83, 0xd3,
    0xe3, 0xe4, 0xe5, 0xe7, 0xe2, 0xe4, 0xe5, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd7, 0xd9, 0xd9, 0xff, 0xd7, 0xd9, 0xda, 0xff, 0xd6, 0xd6, 0xdc, 0xff, 0xd7, 0xd5, 0xdf, 0xff, 0xd3, 0xd4, 0xe0, 0xff, 0xcf, 0xd1, 0xdf, 0xff, 0xce, 0xcf, 0xdb, 0xff, 0xcd, 0xcf, 0xdb, 0xff, 0xd0, 0xd1, 0xdd, 0xff, 0xd2, 0xd5, 0xe0, 0xff, 0xd5, 0xd8, 0xe0, 0xff, 0xd7, 0xda, 0xe0, 0xff, 0xd7, 0xe0, 0xe0, 0xff, 0xd9, 0xe1, 0xe0, 0xff, 0xdc, 0xe0, 0xe1, 0xff, 0xde, 0xe0, 0xe4, 0xff, 0xe2, 0xe2, 0xe6, 0xff, 0xe3, 0xe6, 0xe5, 0xff, 0xe4, 0xe5, 0xe5, 0xff, 0xe8, 0xe8, 0xe7, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xf3, 0xf3, 0xf4, 0xff, 0xf1, 0xf2, 0xf2, 0xff, 0xee, 0xf0, 0xef, 0xff, 0xef, 0xec, 0xed, 0xff, 0xef, 0xf5, 0xf5, 0xff, 0x7c, 0x86, 0x9a, 0xff, 0x4e, 0x53, 0x7c, 0xff, 0x6b, 0x74, 0x94, 0xff, 0x5a, 0x66, 0x86, 0xff, 0x51, 0x5e, 0x7f, 0xff, 0x32, 0x3d, 0x64, 0xff, 0x1d, 0x29, 0x50, 0xff, 0x12, 0x1f, 0x42, 0xff, 0x0a, 0x19, 0x3b, 0xff, 0x0a, 0x16, 0x38, 0xff, 0x12, 0x1b, 0x3f, 0xff, 0x20, 0x2c, 0x50, 0xff, 0x2e, 0x3e, 0x67, 0xff, 0x36, 0x4a, 0x7f, 0xff, 0x3d, 0x50, 0x84, 0xff, 0x3b, 0x50, 0x83, 0xff, 0x4a, 0x65, 0xa1, 0xff, 0x91, 0xa7, 0xe4, 0xff, 0xcc, 0xd0, 0xf9, 0xff, 0xd0, 0xd4, 0xf3, 0xff, 0xcf, 0xd6, 0xf6, 0xff, 0xcd, 0xd3, 0xf2, 0xff, 0xc3, 0xd0, 0xf4, 0xff, 0xb3, 0xc4, 0xf0, 0xff, 0x8e, 0xa7, 0xe3, 0xff, 0x70, 0x93, 0xd4, 0xff, 0x67, 0x89, 0xc8, 0xff, 0x5f, 0x7a, 0xc1, 0xff, 0x64, 0x7d, 0xc9, 0xff, 0x72, 0x8f, 0xd1, 0xff, 0x83, 0x9d, 0xd9, 0xff, 0x87, 0x9f, 0xdd, 0xff, 0x7f, 0x98, 0xd1, 0xff, 0x73, 0x87, 0xc3, 0xff, 0x68, 0x79, 0xb7, 0xff, 0x68, 0x7c, 0xb5, 0xff, 0x75, 0x89, 0xc0, 0xff, 0x7d, 0x94, 0xc8, 0xff, 0x8a, 0xa1, 0xd4, 0xff, 0x9b, 0xb0, 0xe1, 0xff, 0x98, 0xb1, 0xe8, 0xff, 0x88, 0xa6, 0xe2, 0xff, 0x7b, 0x98, 0xd9, 0xff, 0x67, 0x85, 0xc5, 0xff, 0x5a, 0x78, 0xad, 0xff, 0x5f, 0x74, 0xa6, 0xff, 0x64, 0x7a, 0xa8, 0xff, 0x5d, 0x72, 0xa3, 0xff, 0x58, 0x68, 0x90, 0xff, 0x59, 0x6e, 0x8e, 0xff, 0x74, 0x8c, 0xbb, 0xff, 0x5b, 0x70, 0xa7, 0xff, 0x23, 0x34, 0x73, 0xff, 0x15, 0x28, 0x6d, 0xff, 0x17, 0x31, 0x77, 0xff, 0x24, 0x3d, 0x86, 0xff, 0x4a, 0x66, 0xa4, 0xff, 0x56, 0x6d, 0xb4, 0xff, 0x58, 0x66, 0x97, 0xff, 0x5a, 0x5e, 0x56, 0xff, 0x56, 0x58, 0x4c, 0xff, 0x51, 0x57, 0x4e, 0xff, 0x52, 0x57, 0x4e, 0xff, 0x56, 0x5a, 0x4f, 0xff, 0x5d, 0x61, 0x57, 0xff, 0x60, 0x66, 0x60, 0xff, 0x66, 0x6d, 0x6b, 0xff, 0x69, 0x71, 0x77, 0xff, 0x6a, 0x73, 0x7d, 0xff, 0x6a, 0x74, 0x81, 0xff, 0x69, 0x72, 0x84, 0xff, 0x67, 0x72, 0x89, 0xff, 0x67, 0x73, 0x8e, 0xff, 0x66, 0x71, 0x8f, 0xff, 0x60, 0x6a, 0x8b, 0xff, 0x5b, 0x65, 0x88, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x58, 0x62, 0x84, 0xff, 0x58, 0x62, 0x84, 0xff, 0x58, 0x62, 0x84, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5e, 0x68, 0x8b, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x68, 0x86, 0xff, 0x59, 0x66, 0x82, 0xe6,
    0xe3, 0xe5, 0xe5, 0xf0, 0xe3, 0xe5, 0xe6, 0xff, 0xe3, 0xe5, 0xe6, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xd5, 0xd8, 0xd7, 0xff, 0xd5, 0xd7, 0xd9, 0xff, 0xd5, 0xd4, 0xdb, 0xff, 0xd5, 0xd4, 0xde, 0xff, 0xd2, 0xd3, 0xdf, 0xff, 0xcc, 0xcf, 0xdd, 0xff, 0xcc, 0xce, 0xd9, 0xff, 0xcc, 0xce, 0xd9, 0xff, 0xcf, 0xd1, 0xdc, 0xff, 0xd0, 0xd1, 0xdf, 0xff, 0xd2, 0xd3, 0xdf, 0xff, 0xd4, 0xd6, 0xde, 0xff, 0xd3, 0xdb, 0xde, 0xff, 0xd5, 0xde, 0xdf, 0xff, 0xda, 0xde, 0xe1, 0xff, 0xdd, 0xde, 0xe3, 0xff, 0xe2, 0xe2, 0xe6, 0xff, 0xe3, 0xe5, 0xe5, 0xff, 0xe4, 0xe5, 0xe4, 0xff, 0xe7, 0xe6, 0xe6, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xee, 0xf0, 0xef, 0xff, 0xeb, 0xed, 0xec, 0xff, 0xee, 0xe9, 0xeb, 0xff, 0xec, 0xf0, 0xf0, 0xff, 0x77, 0x82, 0x99, 0xff, 0x45, 0x4e, 0x76, 0xff, 0x8a, 0x98, 0xb6, 0xff, 0x64, 0x74, 0x99, 0xff, 0x3d, 0x4d, 0x73, 0xff, 0x33, 0x42, 0x6c, 0xff, 0x38, 0x44, 0x6e, 0xff, 0x37, 0x3f, 0x69, 0xff, 0x32, 0x3a, 0x65, 0xff, 0x2e, 0x3a, 0x5f, 0xff, 0x33, 0x48, 0x6a, 0xff, 0x4a, 0x5a, 0x8d, 0xff, 0x58, 0x6a, 0xa8, 0xff, 0x4e, 0x6b, 0xa7, 0xff, 0x41, 0x5e, 0x99, 0xff, 0x3f, 0x5a, 0x97, 0xff, 0x79, 0x8b, 0xc5, 0xff, 0xc4, 0xd1, 0xf8, 0xff, 0xdd, 0xe2, 0xf3, 0xff, 0xd6, 0xdd, 0xf2, 0xff, 0xd6, 0xdf, 0xf9, 0xff, 0xd1, 0xda, 0xf1, 0xff, 0xc9, 0xd3, 0xf4, 0xff, 0xc9, 0xd7, 0xfb, 0xff, 0xa2, 0xc2, 0xed, 0xff, 0x79, 0x9f, 0xdb, 0xff, 0x66, 0x82, 0xc6, 0xff, 0x61, 0x79, 0xc0, 0xff, 0x61, 0x79, 0xbe, 0xff, 0x77, 0x91, 0xc9, 0xff, 0x8b, 0xa2, 0xd6, 0xff, 0x94, 0xa9, 0xe3, 0xff, 0x99, 0xb1, 0xeb, 0xff, 0xa0, 0xb5, 0xef, 0xff, 0x9c, 0xb0, 0xe6, 0xff, 0x98, 0xae, 0xdd, 0xff, 0x98, 0xb0, 0xdb, 0xff, 0xa0, 0xb9, 0xe2, 0xff, 0xa5, 0xbf, 0xe9, 0xff, 0xa8, 0xbe, 0xe5, 0xff, 0x9b, 0xb6, 0xe2, 0xff, 0x82, 0xa5, 0xdd, 0xff, 0x75, 0x95, 0xd6, 0xff, 0x6a, 0x8c, 0xca, 0xff, 0x60, 0x82, 0xb9, 0xff, 0x63, 0x79, 0xae, 0xff, 0x61, 0x77, 0xa9, 0xff, 0x5c, 0x72, 0xa3, 0xff, 0x54, 0x62, 0x8a, 0xff, 0x5b, 0x6d, 0x91, 0xff, 0x77, 0x94, 0xc9, 0xff, 0x74, 0x9c, 0xd8, 0xff, 0x4a, 0x61, 0xa2, 0xff, 0x2c, 0x3c, 0x80, 0xff, 0x13, 0x2b, 0x72, 0xff, 0x16, 0x2d, 0x75, 0xff, 0x37, 0x53, 0x91, 0xff, 0x55, 0x6c, 0xb3, 0xff, 0x5e, 0x6d, 0x9d, 0xff, 0x58, 0x5d, 0x54, 0xff, 0x53, 0x55, 0x49, 0xff, 0x51, 0x57, 0x4e, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x55, 0x59, 0x4e, 0xff, 0x5d, 0x60, 0x57, 0xff, 0x60, 0x65, 0x5f, 0xff, 0x66, 0x6d, 0x6b, 0xff, 0x6a, 0x73, 0x78, 0xff, 0x6c, 0x75, 0x7f, 0xff, 0x6d, 0x76, 0x84, 0xff, 0x6c, 0x76, 0x86, 0xff, 0x6a, 0x75, 0x8c, 0xff, 0x68, 0x74, 0x90, 0xff, 0x65, 0x70, 0x8e, 0xff, 0x5f, 0x6a, 0x8b, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x59, 0x63, 0x85, 0xff, 0x57, 0x61, 0x83, 0xff, 0x57, 0x61, 0x83, 0xff, 0x58, 0x62, 0x84, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5e, 0x68, 0x8b, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x68, 0x86, 0xff, 0x5a, 0x66, 0x82, 0xf0,
    0xe2, 0xe4, 0xe5, 0xf3, 0xe5, 0xe7, 0xe8, 0xff, 0xe4, 0xe6, 0xe7, 0xff, 0xdd, 0xdf, 0xe1, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xd3, 0xd5, 0xd5, 0xff, 0xd3, 0xd5, 0xd6, 0xff, 0xd2, 0xd2, 0xd9, 0xff, 0xd2, 0xd1, 0xdc, 0xff, 0xcf, 0xd0, 0xdc, 0xff, 0xca, 0xcd, 0xdb, 0xff, 0xcb, 0xcd, 0xd7, 0xff, 0xcb, 0xcd, 0xd7, 0xff, 0xcd, 0xcf, 0xda, 0xff, 0xce, 0xce, 0xdf, 0xff, 0xd0, 0xd0, 0xdf, 0xff, 0xd2, 0xd3, 0xde, 0xff, 0xcf, 0xd6, 0xdd, 0xff, 0xd2, 0xd9, 0xde, 0xff, 0xd9, 0xdb, 0xe2, 0xff, 0xdd, 0xdd, 0xe4, 0xff, 0xe1, 0xe2, 0xe4, 0xff, 0xe2, 0xe4, 0xe3, 0xff, 0xe3, 0xe4, 0xe3, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xea, 0xea, 0xea, 0xff, 0xed, 0xec, 0xed, 0xff, 0xee, 0xee, 0xee, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xee, 0xee, 0xee, 0xff, 0xee, 0xed, 0xee, 0xff, 0xec, 0xee, 0xed, 0xff, 0xe7, 0xe9, 0xe8, 0xff, 0xea, 0xe6, 0xe8, 0xff, 0xeb, 0xf0, 0xf0, 0xff, 0x6a, 0x77, 0x8d, 0xff, 0x49, 0x55, 0x7e, 0xff, 0x7c, 0x8d, 0xaa, 0xff, 0x4f, 0x62, 0x8a, 0xff, 0x5f, 0x72, 0x9f, 0xff, 0x72, 0x84, 0xb2, 0xff, 0x7c, 0x8d, 0xbe, 0xff, 0x75, 0x87, 0xb8, 0xff, 0x6d, 0x7f, 0xae, 0xff, 0x66, 0x7b, 0xab, 0xff, 0x65, 0x81, 0xb6, 0xff, 0x6f, 0x8b, 0xc8, 0xff, 0x66, 0x82, 0xc3, 0xff, 0x48, 0x61, 0x9f, 0xff, 0x3c, 0x53, 0x91, 0xff, 0x56, 0x71, 0xb0, 0xff, 0x8b, 0xa5, 0xe2, 0xff, 0xb7, 0xc7, 0xee, 0xff, 0xbd, 0xd1, 0xf0, 0xff, 0xb1, 0xc1, 0xe6, 0xff, 0xb6, 0xc2, 0xe8, 0xff, 0xbd, 0xcb, 0xf0, 0xff, 0xbc, 0xcc, 0xf4, 0xff, 0xc0, 0xcf, 0xf4, 0xff, 0xb8, 0xc9, 0xf3, 0xff, 0x95, 0xb0, 0xec, 0xff, 0x7a, 0x96, 0xda, 0xff, 0x75, 0x8b, 0xd2, 0xff, 0x6a, 0x7d, 0xc6, 0xff, 0x62, 0x75, 0xb4, 0xff, 0x68, 0x7a, 0xb3, 0xff, 0x74, 0x85, 0xc6, 0xff, 0x86, 0x9d, 0xdd, 0xff, 0x9b, 0xaf, 0xe7, 0xff, 0xa4, 0xb9, 0xe9, 0xff, 0xa3, 0xb9, 0xe8, 0xff, 0xa6, 0xbc, 0xe8, 0xff, 0xa8, 0xc1, 0xeb, 0xff, 0xa9, 0xc1, 0xed, 0xff, 0xad, 0xc2, 0xea, 0xff, 0xa0, 0xbb, 0xe8, 0xff, 0x84, 0xaa, 0xe2, 0xff, 0x74, 0x98, 0xd8, 0xff, 0x6c, 0x92, 0xce, 0xff, 0x6b, 0x8e, 0xc6, 0xff, 0x6a, 0x82, 0xba, 0xff, 0x5f, 0x77, 0xae, 0xff, 0x5c, 0x73, 0xa5, 0xff, 0x50, 0x5f, 0x85, 0xff, 0x5b, 0x6a, 0x95, 0xff, 0x66, 0x7f, 0xbf, 0xff, 0x69, 0x91, 0xd5, 0xff, 0x59, 0x7e, 0xbd, 0xff, 0x4c, 0x6d, 0xab, 0xff, 0x2c, 0x44, 0x8b, 0xff, 0x15, 0x2d, 0x76, 0xff, 0x3d, 0x59, 0x97, 0xff, 0x57, 0x6d, 0xb6, 0xff, 0x5d, 0x6d, 0x9e, 0xff, 0x59, 0x5d, 0x55, 0xff, 0x53, 0x54, 0x48, 0xff, 0x50, 0x56, 0x4c, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x53, 0x57, 0x4d, 0xff, 0x5b, 0x5f, 0x55, 0xff, 0x5e, 0x64, 0x5e, 0xff, 0x64, 0x6b, 0x69, 0xff, 0x68, 0x70, 0x75, 0xff, 0x6b, 0x75, 0x7f, 0xff, 0x6d, 0x76, 0x84, 0xff, 0x6b, 0x75, 0x86, 0xff, 0x6a, 0x76, 0x8c, 0xff, 0x68, 0x74, 0x90, 0xff, 0x64, 0x6f, 0x8e, 0xff, 0x5e, 0x69, 0x8a, 0xff, 0x59, 0x63, 0x86, 0xff, 0x58, 0x62, 0x85, 0xff, 0x56, 0x60, 0x82, 0xff, 0x56, 0x60, 0x82, 0xff, 0x58, 0x62, 0x84, 0xff, 0x59, 0x63, 0x85, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5a, 0x64, 0x86, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5b, 0x65, 0x87, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5e, 0x68, 0x8b, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x68, 0x86, 0xff, 0x5a, 0x65, 0x83, 0xf3,
    0xe3, 0xe5, 0xe6, 0xff, 0xe3, 0xe5, 0xe6, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xd7, 0xd9, 0xdb, 0xff, 0xd3, 0xd5, 0xd5, 0xff, 0xd2, 0xd4, 0xd5, 0xff, 0xd0, 0xd1, 0xd7, 0xff, 0xd1, 0xd2, 0xdb, 0xff, 0xcd, 0xcf, 0xda, 0xff, 0xc7, 0xcb, 0xd9, 0xff, 0xc3, 0xca, 0xd7, 0xff, 0xc2, 0xc8, 0xd6, 0xff, 0xc6, 0xcc, 0xda, 0xff, 0xc7, 0xcc, 0xde, 0xff, 0xc8, 0xce, 0xde, 0xff, 0xc9, 0xd0, 0xdb, 0xff, 0xcc, 0xd1, 0xdd, 0xff, 0xd0, 0xd4, 0xe0, 0xff, 0xd4, 0xd9, 0xe1, 0xff, 0xd6, 0xdc, 0xe2, 0xff, 0xda, 0xe0, 0xe3, 0xff, 0xdf, 0xe1, 0xe5, 0xff, 0xde, 0xe1, 0xe5, 0xff, 0xe1, 0xe3, 0xe5, 0xff, 0xe7, 0xe8, 0xe8, 0xff, 0xec, 0xeb, 0xeb, 0xff, 0xed, 0xed, 0xed, 0xff, 0xef, 0xef, 0xef, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xef, 0xef, 0xef, 0xff, 0xec, 0xec, 0xec, 0xff, 0xe9, 0xea, 0xeb, 0xff, 0xe6, 0xe7, 0xe8, 0xff, 0xe8, 0xe5, 0xe3, 0xff, 0xe8, 0xeb, 0xed, 0xff, 0x7c, 0x88, 0x9b, 0xff, 0x53, 0x60, 0x83, 0xff, 0x73, 0x85, 0xa4, 0xff, 0x57, 0x6c, 0x91, 0xff, 0x6a, 0x82, 0xb5, 0xff, 0x7d, 0x95, 0xc9, 0xff, 0x8c, 0xa3, 0xd8, 0xff, 0x94, 0xaa, 0xe1, 0xff, 0x8a, 0xa2, 0xd5, 0xff, 0x7e, 0x9d, 0xd2, 0xff, 0x7c, 0x99, 0xd6, 0xff, 0x66, 0x83, 0xc1, 0xff, 0x38, 0x50, 0x8e, 0xff, 0x3c, 0x49, 0x82, 0xff, 0x6a, 0x7c, 0xb3, 0xff, 0x74, 0x90, 0xce, 0xff, 0x6c, 0x90, 0xd4, 0xff, 0x7a, 0x97, 0xd9, 0xff, 0x7f, 0x98, 0xdc, 0xff, 0x79, 0x93, 0xd4, 0xff, 0x78, 0x8f, 0xcf, 0xff, 0x8c, 0xa3, 0xd9, 0xff, 0xa3, 0xb6, 0xeb, 0xff, 0xa3, 0xb3, 0xeb, 0xff, 0xa7, 0xb5, 0xec, 0xff, 0x9b, 0xb2, 0xee, 0xff, 0x94, 0xb2, 0xed, 0xff, 0x98, 0xb3, 0xf0, 0xff, 0x88, 0x9f, 0xe4, 0xff, 0x74, 0x8a, 0xcb, 0xff, 0x68, 0x7c, 0xb8, 0xff, 0x49, 0x59, 0x99, 0xff, 0x52, 0x64, 0xa7, 0xff, 0x72, 0x86, 0xc3, 0xff, 0x82, 0x9b, 0xd3, 0xff, 0x8c, 0xa7, 0xde, 0xff, 0x98, 0xb0, 0xe5, 0xff, 0xa3, 0xb7, 0xe9, 0xff, 0xa0, 0xb9, 0xeb, 0xff, 0x9d, 0xb9, 0xeb, 0xff, 0x9b, 0xb8, 0xed, 0xff, 0x8d, 0xb0, 0xea, 0xff, 0x7d, 0xa7, 0xe2, 0xff, 0x73, 0x9c, 0xd6, 0xff, 0x72, 0x94, 0xcf, 0xff, 0x6b, 0x88, 0xc2, 0xff, 0x61, 0x7c, 0xb1, 0xff, 0x61, 0x79, 0xa7, 0xff, 0x55, 0x65, 0x87, 0xff, 0x5d, 0x6a, 0x95, 0xff, 0x62, 0x7a, 0xb9, 0xff, 0x5a, 0x79, 0xbd, 0xff, 0x4b, 0x70, 0xb0, 0xff, 0x61, 0x86, 0xc6, 0xff, 0x3c, 0x55, 0x99, 0xff, 0x34, 0x4d, 0x90, 0xff, 0x66, 0x87, 0xc3, 0xff, 0x58, 0x76, 0xb9, 0xff, 0x5e, 0x6b, 0x93, 0xff, 0x51, 0x57, 0x4e, 0xff, 0x52, 0x57, 0x4c, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x51, 0x56, 0x4d, 0xff, 0x55, 0x59, 0x4f, 0xff, 0x5b, 0x5f, 0x55, 0xff, 0x5e, 0x63, 0x5d, 0xff, 0x62, 0x6a, 0x67, 0xff, 0x66, 0x6f, 0x74, 0xff, 0x6a, 0x73, 0x7d, 0xff, 0x69, 0x73, 0x81, 0xff, 0x68, 0x72, 0x83, 0xff, 0x66, 0x72, 0x89, 0xff, 0x66, 0x71, 0x8c, 0xff, 0x64, 0x70, 0x8d, 0xff, 0x5e, 0x69, 0x8b, 0xff, 0x59, 0x63, 0x88, 0xff, 0x57, 0x60, 0x82, 0xff, 0x54, 0x5f, 0x81, 0xff, 0x51, 0x5d, 0x7f, 0xff, 0x53, 0x5e, 0x83, 0xff, 0x55, 0x5f, 0x85, 0xff, 0x56, 0x60, 0x86, 0xff, 0x53, 0x61, 0x83, 0xff, 0x57, 0x63, 0x84, 0xff, 0x5e, 0x65, 0x87, 0xff, 0x5d, 0x64, 0x89, 0xff, 0x5f, 0x68, 0x8e, 0xff, 0x5c, 0x68, 0x8e, 0xff, 0x5b, 0x67, 0x8a, 0xff, 0x5b, 0x64, 0x86, 0xff, 0x5c, 0x66, 0x87, 0xff, 0x5b, 0x65, 0x86, 0xff,
    0xe0, 0xe2, 0xe3, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xdd, 0xdf, 0xdf, 0xff, 0xd7, 0xd9, 0xda, 0xff, 0xd4, 0xd6, 0xd6, 0xff, 0xd4, 0xd5, 0xd7, 0xff, 0xd2, 0xd3, 0xd8, 0xff, 0xd2, 0xd4, 0xdd, 0xff, 0xcd, 0xd0, 0xdd, 0xff, 0xc6, 0xcc, 0xda, 0xff, 0xbf, 0xc7, 0xd7, 0xff, 0xbd, 0xc5, 0xd5, 0xff, 0xc2, 0xc9, 0xd9, 0xff, 0xc6, 0xce, 0xdf, 0xff, 0xc7, 0xcf, 0xdf, 0xff, 0xc7, 0xd0, 0xdc, 0xff, 0xcc, 0xd0, 0xde, 0xff, 0xd0, 0xd2, 0xe0, 0xff, 0xd1, 0xd7, 0xdf, 0xff, 0xd1, 0xdb, 0xe1, 0xff, 0xd6, 0xdd, 0xe4, 0xff, 0xdc, 0xde, 0xe7, 0xff, 0xdb, 0xde, 0xe6, 0xff, 0xde, 0xe2, 0xe5, 0xff, 0xe5, 0xe5, 0xe6, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xea, 0xea, 0xea, 0xff, 0xec, 0xec, 0xec, 0xff, 0xee, 0xee, 0xee, 0xff, 0xed, 0xed, 0xed, 0xff, 0xea, 0xea, 0xe9, 0xff, 0xe6, 0xe7, 0xe8, 0xff, 0xe2, 0xe3, 0xe5, 0xff, 0xe4, 0xe2, 0xdf, 0xff, 0xe2, 0xe5, 0xe8, 0xff, 0x8a, 0x95, 0xa8, 0xff, 0x56, 0x63, 0x83, 0xff, 0x76, 0x88, 0xa8, 0xff, 0x5b, 0x71, 0x96, 0xff, 0x5a, 0x74, 0xaa, 0xff, 0x6f, 0x8a, 0xc5, 0xff, 0x8a, 0xa4, 0xd7, 0xff, 0x91, 0xa8, 0xda, 0xff, 0x82, 0x98, 0xd6, 0xff, 0x6d, 0x89, 0xcf, 0xff, 0x5f, 0x74, 0xbd, 0xff, 0x3b, 0x50, 0x8c, 0xff, 0x21, 0x33, 0x66, 0xff, 0x68, 0x7a, 0xb0, 0xff, 0x8a, 0x9f, 0xe1, 0xff, 0x63, 0x78, 0xc2, 0xff, 0x4c, 0x68, 0xb4, 0xff, 0x4f, 0x6b, 0xb9, 0xff, 0x55, 0x6e, 0xbc, 0xff, 0x53, 0x6f, 0xb8, 0xff, 0x57, 0x72, 0xbd, 0xff, 0x6f, 0x8b, 0xcb, 0xff, 0x80, 0x9c, 0xd8, 0xff, 0x76, 0x92, 0xd2, 0xff, 0x69, 0x8c, 0xca, 0xff, 0x72, 0x90, 0xce, 0xff, 0x8e, 0xaa, 0xe3, 0xff, 0x9a, 0xb6, 0xf0, 0xff, 0x91, 0xae, 0xf5, 0xff, 0x7f, 0x9e, 0xe7, 0xff, 0x68, 0x82, 0xc3, 0xff, 0x4d, 0x60, 0x99, 0xff, 0x2f, 0x3f, 0x78, 0xff, 0x3d, 0x4d, 0x93, 0xff, 0x58, 0x69, 0xb7, 0xff, 0x6f, 0x86, 0xc8, 0xff, 0x7e, 0x94, 0xd3, 0xff, 0x8e, 0xa2, 0xdf, 0xff, 0x92, 0xae, 0xe4, 0xff, 0x8a, 0xa9, 0xe1, 0xff, 0x87, 0xa6, 0xe1, 0xff, 0x83, 0xa4, 0xe1, 0xff, 0x79, 0xa4, 0xde, 0xff, 0x77, 0x9e, 0xda, 0xff, 0x74, 0x94, 0xd2, 0xff, 0x6f, 0x8f, 0xc7, 0xff, 0x69, 0x84, 0xb7, 0xff, 0x60, 0x79, 0xa5, 0xff, 0x57, 0x67, 0x87, 0xff, 0x61, 0x6d, 0x97, 0xff, 0x64, 0x7d, 0xbb, 0xff, 0x5a, 0x78, 0xba, 0xff, 0x57, 0x78, 0xb9, 0xff, 0x67, 0x89, 0xca, 0xff, 0x5d, 0x7a, 0xb9, 0xff, 0x6f, 0x8c, 0xc8, 0xff, 0x60, 0x81, 0xbb, 0xff, 0x4f, 0x69, 0xa8, 0xff, 0x63, 0x6d, 0x86, 0xff, 0x4e, 0x55, 0x47, 0xff, 0x52, 0x56, 0x4d, 0xff, 0x51, 0x56, 0x4c, 0xff, 0x50, 0x56, 0x4d, 0xff, 0x55, 0x59, 0x4f, 0xff, 0x5b, 0x5f, 0x56, 0xff, 0x5e, 0x63, 0x5d, 0xff, 0x62, 0x6a, 0x67, 0xff, 0x65, 0x6e, 0x73, 0xff, 0x67, 0x70, 0x7a, 0xff, 0x68, 0x72, 0x80, 0xff, 0x68, 0x72, 0x83, 0xff, 0x66, 0x72, 0x88, 0xff, 0x65, 0x71, 0x8d, 0xff, 0x61, 0x6c, 0x8a, 0xff, 0x5c, 0x66, 0x85, 0xff, 0x59, 0x63, 0x84, 0xff, 0x55, 0x63, 0x7f, 0xff, 0x58, 0x65, 0x80, 0xff, 0x5a, 0x66, 0x82, 0xff, 0x5f, 0x6a, 0x88, 0xff, 0x62, 0x6d, 0x8d, 0xff, 0x62, 0x6c, 0x8c, 0xff, 0x5f, 0x6a, 0x87, 0xff, 0x60, 0x69, 0x86, 0xff, 0x63, 0x69, 0x85, 0xff, 0x5f, 0x64, 0x86, 0xff, 0x60, 0x68, 0x8d, 0xff, 0x5e, 0x6a, 0x8e, 0xff, 0x5d, 0x69, 0x8b, 0xff, 0x5b, 0x64, 0x86, 0xff, 0x5c, 0x66, 0x89, 0xff, 0x5b, 0x65, 0x88, 0xff,
    0xdc, 0xde, 0xdf, 0xf3, 0xdd, 0xdf, 0xe0, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xd8, 0xda, 0xdc, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd5, 0xd7, 0xd8, 0xff, 0xd5, 0xd5, 0xdc, 0xff, 0xd5, 0xd6, 0xdf, 0xff, 0xd0, 0xd3, 0xdf, 0xff, 0xc8, 0xce, 0xdc, 0xff, 0xc3, 0xca, 0xd9, 0xff, 0xc1, 0xc9, 0xd8, 0xff, 0xc6, 0xcd, 0xdb, 0xff, 0xc8, 0xcf, 0xe0, 0xff, 0xc9, 0xd0, 0xe0, 0xff, 0xca, 0xd2, 0xde, 0xff, 0xce, 0xd2, 0xdf, 0xff, 0xd1, 0xd2, 0xdf, 0xff, 0xd0, 0xd5, 0xdc, 0xff, 0xd0, 0xd7, 0xdf, 0xff, 0xd5, 0xd9, 0xe5, 0xff, 0xda, 0xd9, 0xe6, 0xff, 0xdb, 0xdc, 0xe5, 0xff, 0xde, 0xe1, 0xe3, 0xff, 0xe2, 0xe3, 0xe3, 0xff, 0xe6, 0xe5, 0xe4, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xea, 0xea, 0xea, 0xff, 0xeb, 0xeb, 0xeb, 0xff, 0xea, 0xea, 0xea, 0xff, 0xe8, 0xe7, 0xe7, 0xff, 0xe4, 0xe4, 0xe5, 0xff, 0xdf, 0xe0, 0xe1, 0xff, 0xe1, 0xde, 0xdb, 0xff, 0xdd, 0xe0, 0xe3, 0xff, 0x8c, 0x98, 0xac, 0xff, 0x51, 0x5e, 0x80, 0xff, 0x72, 0x83, 0xa3, 0xff, 0x53, 0x6a, 0x90, 0xff, 0x5e, 0x7b, 0xb4, 0xff, 0x78, 0x94, 0xd0, 0xff, 0x7e, 0x99, 0xd2, 0xff, 0x6f, 0x86, 0xc3, 0xff, 0x5d, 0x76, 0xb9, 0xff, 0x4b, 0x6b, 0xaf, 0xff, 0x3d, 0x53, 0x93, 0xff, 0x25, 0x33, 0x66, 0xff, 0x39, 0x44, 0x6f, 0xff, 0x6f, 0x89, 0xc5, 0xff, 0x56, 0x77, 0xbf, 0xff, 0x3a, 0x4e, 0x88, 0xff, 0x2f, 0x3d, 0x77, 0xff, 0x31, 0x46, 0x82, 0xff, 0x3a, 0x54, 0x8c, 0xff, 0x43, 0x57, 0x95, 0xff, 0x4c, 0x5e, 0xa7, 0xff, 0x57, 0x71, 0xb4, 0xff, 0x56, 0x6d, 0xab, 0xff, 0x49, 0x5f, 0x9d, 0xff, 0x3d, 0x59, 0x97, 0xff, 0x49, 0x66, 0xaa, 0xff, 0x61, 0x7f, 0xc0, 0xff, 0x6c, 0x87, 0xc9, 0xff, 0x6a, 0x88, 0xd7, 0xff, 0x66, 0x87, 0xd3, 0xff, 0x58, 0x6d, 0xab, 0xff, 0x4e, 0x5d, 0x8f, 0xff, 0x36, 0x48, 0x76, 0xff, 0x1f, 0x30, 0x6d, 0xff, 0x35, 0x44, 0x8a, 0xff, 0x51, 0x61, 0xa6, 0xff, 0x62, 0x78, 0xb7, 0xff, 0x6f, 0x8c, 0xc6, 0xff, 0x7c, 0x99, 0xd4, 0xff, 0x7b, 0x99, 0xd7, 0xff, 0x79, 0x99, 0xda, 0xff, 0x75, 0x96, 0xd7, 0xff, 0x6c, 0x96, 0xd5, 0xff, 0x71, 0x97, 0xd8, 0xff, 0x72, 0x91, 0xd0, 0xff, 0x6e, 0x8d, 0xc5, 0xff, 0x6b, 0x86, 0xba, 0xff, 0x5f, 0x79, 0xa5, 0xff, 0x57, 0x67, 0x88, 0xff, 0x5c, 0x67, 0x92, 0xff, 0x56, 0x6f, 0xad, 0xff, 0x58, 0x78, 0xbb, 0xff, 0x67, 0x88, 0xc9, 0xff, 0x6c, 0x92, 0xd1, 0xff, 0x97, 0xbb, 0xf7, 0xff, 0x7b, 0x99, 0xd3, 0xff, 0x36, 0x51, 0x8a, 0xff, 0x59, 0x67, 0xa7, 0xff, 0x60, 0x68, 0x78, 0xff, 0x4f, 0x56, 0x43, 0xff, 0x51, 0x55, 0x4d, 0xff, 0x50, 0x55, 0x4c, 0xff, 0x4f, 0x54, 0x4c, 0xff, 0x55, 0x59, 0x4f, 0xff, 0x5b, 0x5f, 0x56, 0xff, 0x5e, 0x64, 0x5e, 0xff, 0x62, 0x6a, 0x68, 0xff, 0x61, 0x6a, 0x6f, 0xff, 0x63, 0x6d, 0x77, 0xff, 0x69, 0x72, 0x80, 0xff, 0x69, 0x73, 0x84, 0xff, 0x66, 0x71, 0x88, 0xff, 0x66, 0x71, 0x8d, 0xff, 0x63, 0x6b, 0x89, 0xff, 0x5b, 0x64, 0x80, 0xff, 0x5b, 0x68, 0x80, 0xff, 0x60, 0x6c, 0x80, 0xff, 0x6d, 0x72, 0x86, 0xff, 0x7c, 0x78, 0x8e, 0xff, 0x82, 0x80, 0x98, 0xff, 0x87, 0x86, 0x9f, 0xff, 0x86, 0x84, 0x9c, 0xff, 0x7f, 0x7d, 0x97, 0xff, 0x77, 0x79, 0x93, 0xff, 0x6e, 0x75, 0x8c, 0xff, 0x67, 0x6c, 0x8a, 0xff, 0x64, 0x69, 0x8c, 0xff, 0x5f, 0x67, 0x88, 0xff, 0x5f, 0x69, 0x8a, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x5c, 0x65, 0x88, 0xf3,
    0xdb, 0xde, 0xdf, 0xf0, 0xdc, 0xde, 0xdf, 0xff, 0xdb, 0xdd, 0xdd, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xd4, 0xd6, 0xd7, 0xff, 0xd4, 0xd6, 0xd6, 0xff, 0xd5, 0xd7, 0xd6, 0xff, 0xd5, 0xd7, 0xda, 0xff, 0xd5, 0xd6, 0xe0, 0xff, 0xd2, 0xd4, 0xe0, 0xff, 0xcb, 0xd0, 0xdf, 0xff, 0xc4, 0xcc, 0xdc, 0xff, 0xc3, 0xca, 0xdb, 0xff, 0xc6, 0xcd, 0xdd, 0xff, 0xc9, 0xcf, 0xdf, 0xff, 0xc8, 0xd0, 0xdf, 0xff, 0xc7, 0xd1, 0xde, 0xff, 0xc9, 0xd1, 0xdd, 0xff, 0xcc, 0xd2, 0xdd, 0xff, 0xd0, 0xd4, 0xde, 0xff, 0xd2, 0xd6, 0xdf, 0xff, 0xd4, 0xd9, 0xe2, 0xff, 0xd8, 0xdb, 0xe5, 0xff, 0xd8, 0xdd, 0xe5, 0xff, 0xdc, 0xe1, 0xe6, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xe4, 0xe5, 0xe5, 0xff, 0xe5, 0xe6, 0xe7, 0xff, 0xe6, 0xe7, 0xe8, 0xff, 0xe7, 0xe8, 0xe9, 0xff, 0xe6, 0xe8, 0xe8, 0xff, 0xe4, 0xe6, 0xe6, 0xff, 0xe1, 0xe3, 0xe3, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdb, 0xdb, 0xda, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0x98, 0xa1, 0xaf, 0xff, 0x54, 0x61, 0x7c, 0xff, 0x71, 0x82, 0xa2, 0xff, 0x66, 0x7d, 0xa8, 0xff, 0x66, 0x86, 0xbd, 0xff, 0x64, 0x81, 0xbe, 0xff, 0x60, 0x7a, 0xbd, 0xff, 0x51, 0x69, 0xae, 0xff, 0x47, 0x5f, 0xa0, 0xff, 0x39, 0x54, 0x8c, 0xff, 0x2f, 0x47, 0x74, 0xff, 0x1a, 0x28, 0x50, 0xff, 0x33, 0x3f, 0x6b, 0xff, 0x40, 0x5d, 0x9a, 0xff, 0x25, 0x44, 0x7f, 0xff, 0x28, 0x41, 0x6f, 0xff, 0x1f, 0x2e, 0x55, 0xff, 0x22, 0x2e, 0x54, 0xff, 0x2a, 0x3a, 0x63, 0xff, 0x32, 0x42, 0x76, 0xff, 0x3d, 0x4c, 0x89, 0xff, 0x38, 0x4e, 0x89, 0xff, 0x33, 0x45, 0x7a, 0xff, 0x33, 0x41, 0x74, 0xff, 0x2e, 0x3d, 0x6d, 0xff, 0x2d, 0x42, 0x76, 0xff, 0x32, 0x4d, 0x85, 0xff, 0x42, 0x58, 0x95, 0xff, 0x4b, 0x60, 0xa8, 0xff, 0x4d, 0x64, 0xa6, 0xff, 0x3c, 0x4d, 0x7f, 0xff, 0x38, 0x44, 0x6e, 0xff, 0x3c, 0x47, 0x75, 0xff, 0x1e, 0x2d, 0x5c, 0xff, 0x1e, 0x31, 0x63, 0xff, 0x2d, 0x3f, 0x76, 0xff, 0x40, 0x56, 0x8f, 0xff, 0x54, 0x6c, 0xa7, 0xff, 0x61, 0x7d, 0xbc, 0xff, 0x67, 0x84, 0xc7, 0xff, 0x63, 0x83, 0xc7, 0xff, 0x64, 0x88, 0xc9, 0xff, 0x61, 0x89, 0xc7, 0xff, 0x64, 0x8b, 0xca, 0xff, 0x65, 0x88, 0xc6, 0xff, 0x60, 0x7e, 0xb9, 0xff, 0x69, 0x83, 0xba, 0xff, 0x65, 0x7b, 0xaa, 0xff, 0x58, 0x68, 0x88, 0xff, 0x58, 0x6b, 0x94, 0xff, 0x47, 0x5d, 0xa0, 0xff, 0x3f, 0x5c, 0x9c, 0xff, 0x6a, 0x8a, 0xc4, 0xff, 0x85, 0xa1, 0xe3, 0xff, 0x7a, 0x9d, 0xd6, 0xff, 0x5b, 0x78, 0xa7, 0xff, 0x62, 0x73, 0xaf, 0xff, 0x6c, 0x71, 0xb1, 0xff, 0x4b, 0x55, 0x55, 0xff, 0x4c, 0x55, 0x41, 0xff, 0x4f, 0x53, 0x4d, 0xff, 0x4f, 0x53, 0x4a, 0xff, 0x4f, 0x54, 0x4b, 0xff, 0x54, 0x58, 0x4d, 0xff, 0x5b, 0x5e, 0x55, 0xff, 0x5f, 0x64, 0x5e, 0xff, 0x62, 0x69, 0x67, 0xff, 0x62, 0x6a, 0x6f, 0xff, 0x63, 0x6c, 0x76, 0xff, 0x64, 0x6d, 0x7b, 0xff, 0x62, 0x6c, 0x7d, 0xff, 0x62, 0x6d, 0x83, 0xff, 0x62, 0x6e, 0x8c, 0xff, 0x61, 0x69, 0x8c, 0xff, 0x5e, 0x64, 0x7f, 0xff, 0x67, 0x6b, 0x7e, 0xff, 0x79, 0x76, 0x83, 0xff, 0x8a, 0x7e, 0x8d, 0xff, 0x95, 0x87, 0x96, 0xff, 0x9e, 0x8f, 0x9d, 0xff, 0xa5, 0x96, 0xa6, 0xff, 0xa0, 0x95, 0xa8, 0xff, 0x9b, 0x93, 0xa8, 0xff, 0x91, 0x8d, 0xa3, 0xff, 0x81, 0x84, 0x9a, 0xff, 0x73, 0x76, 0x91, 0xff, 0x69, 0x6c, 0x8b, 0xff, 0x5d, 0x66, 0x83, 0xff, 0x5c, 0x67, 0x87, 0xff, 0x5c, 0x66, 0x89, 0xff, 0x5f, 0x69, 0x8b, 0xff, 0x5d, 0x68, 0x8a, 0xf0,
    0xdc, 0xde, 0xde, 0xe7, 0xdc, 0xde, 0xde, 0xff, 0xd9, 0xdb, 0xdb, 0xff, 0xd4, 0xd6, 0xd6, 0xff, 0xd3, 0xd5, 0xd5, 0xff, 0xd2, 0xd4, 0xd4, 0xff, 0xd4, 0xd6, 0xd4, 0xff, 0xd4, 0xd6, 0xd7, 0xff, 0xd5, 0xd7, 0xdf, 0xff, 0xd2, 0xd5, 0xe0, 0xff, 0xcb, 0xd1, 0xdf, 0xff, 0xc8, 0xce, 0xdf, 0xff, 0xc7, 0xcd, 0xde, 0xff, 0xc7, 0xce, 0xdf, 0xff, 0xcb, 0xcf, 0xde, 0xff, 0xc8, 0xcf, 0xde, 0xff, 0xc4, 0xd0, 0xde, 0xff, 0xc4, 0xd0, 0xdd, 0xff, 0xc6, 0xcf, 0xdc, 0xff, 0xcb, 0xd0, 0xdf, 0xff, 0xcf, 0xd4, 0xdf, 0xff, 0xd2, 0xd9, 0xe0, 0xff, 0xd4, 0xdc, 0xe4, 0xff, 0xd5, 0xdd, 0xe4, 0xff, 0xdb, 0xe0, 0xe6, 0xff, 0xe0, 0xe3, 0xe5, 0xff, 0xe1, 0xe3, 0xe3, 0xff, 0xe0, 0xe3, 0xe4, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xe2, 0xe5, 0xe6, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xdf, 0xe1, 0xe3, 0xff, 0xdc, 0xde, 0xe0, 0xff, 0xd7, 0xd9, 0xda, 0xff, 0xd4, 0xd5, 0xd7, 0xff, 0xd7, 0xd9, 0xd7, 0xff, 0x9e, 0xa4, 0xae, 0xff, 0x65, 0x71, 0x88, 0xff, 0x78, 0x89, 0xaa, 0xff, 0x68, 0x7b, 0xaf, 0xff, 0x5b, 0x76, 0xb1, 0xff, 0x4c, 0x68, 0xa8, 0xff, 0x48, 0x5d, 0xa0, 0xff, 0x3f, 0x55, 0x92, 0xff, 0x3d, 0x4f, 0x8c, 0xff, 0x38, 0x4a, 0x80, 0xff, 0x27, 0x3c, 0x67, 0xff, 0x10, 0x20, 0x46, 0xff, 0x0e, 0x1e, 0x42, 0xff, 0x22, 0x37, 0x5c, 0xff, 0x28, 0x39, 0x5b, 0xff, 0x16, 0x26, 0x46, 0xff, 0x08, 0x0f, 0x1c, 0xff, 0x17, 0x1b, 0x2c, 0xff, 0x26, 0x33, 0x54, 0xff, 0x29, 0x39, 0x64, 0xff, 0x2d, 0x3a, 0x6e, 0xff, 0x30, 0x41, 0x75, 0xff, 0x35, 0x47, 0x6d, 0xff, 0x1e, 0x2b, 0x46, 0xff, 0x09, 0x0b, 0x1d, 0xff, 0x09, 0x0a, 0x15, 0xff, 0x0d, 0x10, 0x27, 0xff, 0x1e, 0x22, 0x4b, 0xff, 0x2f, 0x3b, 0x64, 0xff, 0x28, 0x38, 0x5d, 0xff, 0x2d, 0x39, 0x62, 0xff, 0x46, 0x4f, 0x7d, 0xff, 0x3d, 0x46, 0x77, 0xff, 0x1f, 0x2c, 0x5b, 0xff, 0x17, 0x27, 0x54, 0xff, 0x1f, 0x31, 0x60, 0xff, 0x2e, 0x42, 0x76, 0xff, 0x41, 0x53, 0x90, 0xff, 0x44, 0x5c, 0x9b, 0xff, 0x4e, 0x68, 0xa8, 0xff, 0x56, 0x72, 0xb5, 0xff, 0x54, 0x75, 0xb4, 0xff, 0x4c, 0x6e, 0xab, 0xff, 0x50, 0x73, 0xb0, 0xff, 0x58, 0x79, 0xb7, 0xff, 0x5e, 0x7b, 0xb6, 0xff, 0x64, 0x7e, 0xb5, 0xff, 0x66, 0x79, 0xa7, 0xff, 0x5a, 0x68, 0x89, 0xff, 0x57, 0x6b, 0x93, 0xff, 0x43, 0x59, 0x95, 0xff, 0x3b, 0x57, 0x95, 0xff, 0x56, 0x79, 0xb9, 0xff, 0x6e, 0x88, 0xd1, 0xff, 0x63, 0x7e, 0xb9, 0xff, 0x6f, 0x8a, 0xc3, 0xff, 0x73, 0x88, 0xca, 0xff, 0x58, 0x63, 0x7b, 0xff, 0x46, 0x50, 0x3e, 0xff, 0x4d, 0x54, 0x49, 0xff, 0x4e, 0x52, 0x4c, 0xff, 0x4d, 0x51, 0x48, 0xff, 0x4d, 0x53, 0x4a, 0xff, 0x51, 0x55, 0x4b, 0xff, 0x59, 0x5c, 0x53, 0xff, 0x5c, 0x62, 0x5c, 0xff, 0x60, 0x67, 0x65, 0xff, 0x61, 0x6a, 0x6f, 0xff, 0x61, 0x6a, 0x75, 0xff, 0x60, 0x69, 0x77, 0xff, 0x61, 0x6b, 0x7c, 0xff, 0x62, 0x6d, 0x83, 0xff, 0x60, 0x6e, 0x8b, 0xff, 0x5e, 0x68, 0x8a, 0xff, 0x64, 0x67, 0x7f, 0xff, 0x73, 0x70, 0x7f, 0xff, 0x8c, 0x83, 0x8d, 0xff, 0xa2, 0x8f, 0x9b, 0xff, 0xa2, 0x94, 0x9f, 0xff, 0xab, 0x9b, 0xa4, 0xff, 0xb3, 0xa2, 0xad, 0xff, 0xb0, 0xa3, 0xb1, 0xff, 0xae, 0xa2, 0xb3, 0xff, 0xa4, 0x9b, 0xaf, 0xff, 0x94, 0x90, 0xa6, 0xff, 0x83, 0x83, 0x9b, 0xff, 0x72, 0x75, 0x90, 0xff, 0x5f, 0x68, 0x84, 0xff, 0x59, 0x64, 0x84, 0xff, 0x59, 0x63, 0x86, 0xff, 0x5c, 0x66, 0x88, 0xff, 0x5c, 0x66, 0x88, 0xe6,
    0xda, 0xdd, 0xdd, 0xd3, 0xdc, 0xde, 0xde, 0xff, 0xd9, 0xdb, 0xdb, 0xff, 0xd1, 0xd3, 0xd3, 0xff, 0xd0, 0xd2, 0xd2, 0xff, 0xd2, 0xd4, 0xd4, 0xff, 0xd3, 0xd5, 0xd4, 0xff, 0xd3, 0xd6, 0xd6, 0xff, 0xd5, 0xd8, 0xdc, 0xff, 0xd2, 0xd7, 0xdd, 0xff, 0xcb, 0xd3, 0xdc, 0xff, 0xc8, 0xd0, 0xde, 0xff, 0xc7, 0xd0, 0xdd, 0xff, 0xc8, 0xd0, 0xdd, 0xff, 0xcc, 0xd1, 0xe0, 0xff, 0xca, 0xd1, 0xe0, 0xff, 0xc7, 0xd3, 0xe0, 0xff, 0xc3, 0xd1, 0xe0, 0xff, 0xc3, 0xce, 0xde, 0xff, 0xc8, 0xcd, 0xdf, 0xff, 0xc9, 0xd1, 0xdf, 0xff, 0xcc, 0xd6, 0xe1, 0xff, 0xce, 0xd8, 0xe5, 0xff, 0xd2, 0xda, 0xe5, 0xff, 0xd9, 0xde, 0xe3, 0xff, 0xdc, 0xde, 0xe1, 0xff, 0xdc, 0xde, 0xde, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xd3, 0xd5, 0xd6, 0xff, 0xcf, 0xd0, 0xd1, 0xff, 0xd3, 0xd4, 0xd4, 0xff, 0xa4, 0xab, 0xb8, 0xff, 0x6f, 0x7a, 0x94, 0xff, 0x71, 0x82, 0xa3, 0xff, 0x56, 0x68, 0x9a, 0xff, 0x42, 0x5c, 0x93, 0xff, 0x37, 0x55, 0x88, 0xff, 0x33, 0x47, 0x7f, 0xff, 0x32, 0x41, 0x7b, 0xff, 0x35, 0x43, 0x7a, 0xff, 0x31, 0x40, 0x72, 0xff, 0x1d, 0x2f, 0x59, 0xff, 0x12, 0x1f, 0x44, 0xff, 0x0f, 0x19, 0x37, 0xff, 0x11, 0x1c, 0x36, 0xff, 0x11, 0x19, 0x31, 0xff, 0x08, 0x0e, 0x25, 0xff, 0x08, 0x07, 0x19, 0xff, 0x00, 0x00, 0x0d, 0xff, 0x0e, 0x17, 0x2e, 0xff, 0x2e, 0x38, 0x57, 0xff, 0x32, 0x38, 0x62, 0xff, 0x36, 0x40, 0x68, 0xff, 0x29, 0x32, 0x54, 0xff, 0x16, 0x1c, 0x3d, 0xff, 0x28, 0x2c, 0x49, 0xff, 0x26, 0x2a, 0x47, 0xff, 0x1a, 0x1d, 0x3b, 0xff, 0x13, 0x13, 0x2f, 0xff, 0x0d, 0x12, 0x2c, 0xff, 0x2b, 0x37, 0x59, 0xff, 0x6b, 0x74, 0xa5, 0xff, 0x63, 0x6c, 0xa7, 0xff, 0x3f, 0x4d, 0x82, 0xff, 0x26, 0x33, 0x60, 0xff, 0x17, 0x22, 0x4b, 0xff, 0x19, 0x2b, 0x55, 0xff, 0x23, 0x37, 0x67, 0xff, 0x2f, 0x42, 0x79, 0xff, 0x36, 0x4a, 0x84, 0xff, 0x40, 0x55, 0x91, 0xff, 0x46, 0x5c, 0x99, 0xff, 0x42, 0x5d, 0x99, 0xff, 0x43, 0x61, 0x9b, 0xff, 0x46, 0x62, 0x9d, 0xff, 0x47, 0x65, 0xa1, 0xff, 0x53, 0x70, 0xa8, 0xff, 0x5d, 0x76, 0xa7, 0xff, 0x68, 0x7a, 0xa3, 0xff, 0x64, 0x6c, 0x92, 0xff, 0x59, 0x67, 0x93, 0xff, 0x38, 0x51, 0x7a, 0xff, 0x46, 0x5f, 0x95, 0xff, 0x62, 0x82, 0xce, 0xff, 0x69, 0x89, 0xd3, 0xff, 0x71, 0x8f, 0xc9, 0xff, 0x77, 0x8c, 0xc9, 0xff, 0x5a, 0x6f, 0x92, 0xff, 0x44, 0x54, 0x3f, 0xff, 0x4d, 0x4d, 0x40, 0xff, 0x52, 0x55, 0x4d, 0xff, 0x4e, 0x54, 0x4b, 0xff, 0x4b, 0x50, 0x46, 0xff, 0x4a, 0x4f, 0x46, 0xff, 0x4f, 0x54, 0x48, 0xff, 0x56, 0x5b, 0x51, 0xff, 0x58, 0x5e, 0x57, 0xff, 0x5c, 0x63, 0x61, 0xff, 0x5e, 0x67, 0x6c, 0xff, 0x5e, 0x67, 0x72, 0xff, 0x5e, 0x68, 0x75, 0xff, 0x5e, 0x68, 0x79, 0xff, 0x5d, 0x69, 0x81, 0xff, 0x5e, 0x6a, 0x87, 0xff, 0x61, 0x69, 0x83, 0xff, 0x6a, 0x6c, 0x7f, 0xff, 0x7b, 0x7b, 0x85, 0xff, 0x98, 0x8f, 0x96, 0xff, 0xac, 0x99, 0xa3, 0xff, 0xac, 0x9d, 0xa6, 0xff, 0xb3, 0xa6, 0xb3, 0xff, 0xc0, 0xb0, 0xbb, 0xff, 0xc4, 0xb2, 0xb7, 0xff, 0xc1, 0xae, 0xbb, 0xff, 0xb4, 0xa7, 0xba, 0xff, 0xa6, 0x9e, 0xb1, 0xff, 0x93, 0x8f, 0xa4, 0xff, 0x79, 0x7d, 0x95, 0xff, 0x63, 0x6a, 0x85, 0xff, 0x58, 0x62, 0x81, 0xff, 0x54, 0x5e, 0x81, 0xff, 0x58, 0x62, 0x84, 0xff, 0x59, 0x63, 0x84, 0xd3,
    0xdc, 0xdd, 0xdd, 0xbe, 0xdb, 0xdd, 0xdd, 0xff, 0xd6, 0xd8, 0xd8, 0xff, 0xcf, 0xd1, 0xd1, 0xff, 0xce, 0xd0, 0xd0, 0xff, 0xd1, 0xd3, 0xd3, 0xff, 0xd2, 0xd4, 0xd4, 0xff, 0xd4, 0xd6, 0xd6, 0xff, 0xd6, 0xd8, 0xda, 0xff, 0xd2, 0xd7, 0xdb, 0xff, 0xcb, 0xd2, 0xda, 0xff, 0xc8, 0xce, 0xd9, 0xff, 0xc6, 0xcd, 0xd8, 0xff, 0xc8, 0xd0, 0xdc, 0xff, 0xcd, 0xd2, 0xe2, 0xff, 0xcb, 0xd1, 0xe2, 0xff, 0xc7, 0xd1, 0xe2, 0xff, 0xc1, 0xcf, 0xdf, 0xff, 0xbe, 0xcc, 0xdc, 0xff, 0xc1, 0xcb, 0xdd, 0xff, 0xc5, 0xcf, 0xde, 0xff, 0xca, 0xd4, 0xe1, 0xff, 0xcd, 0xd6, 0xe3, 0xff, 0xd2, 0xd9, 0xe3, 0xff, 0xd8, 0xdc, 0xe3, 0xff, 0xda, 0xdc, 0xe0, 0xff, 0xdb, 0xdc, 0xdd, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xd2, 0xd4, 0xd5, 0xff, 0xcf, 0xd1, 0xd2, 0xff, 0xcc, 0xce, 0xce, 0xff, 0xd1, 0xd2, 0xd1, 0xff, 0xad, 0xb3, 0xbe, 0xff, 0x75, 0x84, 0x9c, 0xff, 0x64, 0x78, 0x9b, 0xff, 0x40, 0x51, 0x80, 0xff, 0x32, 0x48, 0x77, 0xff, 0x29, 0x45, 0x6e, 0xff, 0x26, 0x3d, 0x6b, 0xff, 0x29, 0x3a, 0x6f, 0xff, 0x2d, 0x3b, 0x6e, 0xff, 0x27, 0x38, 0x66, 0xff, 0x13, 0x27, 0x4c, 0xff, 0x14, 0x21, 0x44, 0xff, 0x24, 0x2c, 0x50, 0xff, 0x1c, 0x26, 0x48, 0xff, 0x09, 0x11, 0x2d, 0xff, 0x09, 0x10, 0x2b, 0xff, 0x11, 0x18, 0x3d, 0xff, 0x0f, 0x16, 0x30, 0xff, 0x09, 0x0f, 0x25, 0xff, 0x15, 0x1b, 0x33, 0xff, 0x1f, 0x25, 0x44, 0xff, 0x1c, 0x25, 0x44, 0xff, 0x20, 0x27, 0x4a, 0xff, 0x2d, 0x34, 0x5f, 0xff, 0x34, 0x3f, 0x70, 0xff, 0x28, 0x37, 0x6b, 0xff, 0x25, 0x34, 0x61, 0xff, 0x2d, 0x36, 0x5a, 0xff, 0x3b, 0x43, 0x6c, 0xff, 0x58, 0x63, 0x99, 0xff, 0x5d, 0x6a, 0xa4, 0xff, 0x4b, 0x5b, 0x96, 0xff, 0x49, 0x58, 0x8d, 0xff, 0x3b, 0x46, 0x77, 0xff, 0x1a, 0x25, 0x4c, 0xff, 0x0b, 0x19, 0x3e, 0xff, 0x1b, 0x29, 0x53, 0xff, 0x24, 0x38, 0x66, 0xff, 0x2c, 0x3f, 0x70, 0xff, 0x33, 0x47, 0x7a, 0xff, 0x37, 0x4e, 0x85, 0xff, 0x39, 0x50, 0x8a, 0xff, 0x41, 0x58, 0x92, 0xff, 0x47, 0x5e, 0x98, 0xff, 0x45, 0x5e, 0x95, 0xff, 0x49, 0x61, 0x95, 0xff, 0x57, 0x6c, 0xa0, 0xff, 0x69, 0x79, 0xa2, 0xff, 0x64, 0x6f, 0x94, 0xff, 0x58, 0x6a, 0x8f, 0xff, 0x3a, 0x4e, 0x60, 0xff, 0x30, 0x3e, 0x62, 0xff, 0x48, 0x5d, 0xa5, 0xff, 0x58, 0x70, 0xb8, 0xff, 0x5d, 0x79, 0xaf, 0xff, 0x60, 0x6b, 0x89, 0xff, 0x4f, 0x52, 0x4c, 0xff, 0x48, 0x4d, 0x3c, 0xff, 0x4e, 0x4e, 0x4a, 0xff, 0x4f, 0x52, 0x4c, 0xff, 0x4c, 0x52, 0x49, 0xff, 0x49, 0x4e, 0x47, 0xff, 0x48, 0x4d, 0x45, 0xff, 0x4e, 0x52, 0x49, 0xff, 0x56, 0x59, 0x53, 0xff, 0x56, 0x5b, 0x57, 0xff, 0x59, 0x60, 0x5f, 0xff, 0x5c, 0x64, 0x68, 0xff, 0x5d, 0x64, 0x6f, 0xff, 0x5d, 0x63, 0x72, 0xff, 0x5b, 0x64, 0x75, 0xff, 0x5b, 0x67, 0x7c, 0xff, 0x5a, 0x66, 0x80, 0xff, 0x5f, 0x68, 0x7d, 0xff, 0x73, 0x73, 0x7e, 0xff, 0x8c, 0x83, 0x8b, 0xff, 0x9f, 0x91, 0x9a, 0xff, 0xab, 0x97, 0xa2, 0xff, 0xb8, 0xa3, 0xae, 0xff, 0xc3, 0xb3, 0xbe, 0xff, 0xcb, 0xba, 0xc2, 0xff, 0xce, 0xbb, 0xbf, 0xff, 0xcb, 0xb7, 0xc2, 0xff, 0xc3, 0xb0, 0xc0, 0xff, 0xb5, 0xa7, 0xba, 0xff, 0xa2, 0x9a, 0xb0, 0xff, 0x8b, 0x87, 0x9b, 0xff, 0x6c, 0x6f, 0x83, 0xff, 0x57, 0x61, 0x7e, 0xff, 0x52, 0x5d, 0x7f, 0xff, 0x54, 0x5f, 0x80, 0xff, 0x54, 0x5d, 0x7f, 0xbe,
    0xdc, 0xde, 0xde, 0xaa, 0xd9, 0xdb, 0xdb, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xcf, 0xd1, 0xd1, 0xff, 0xce, 0xd0, 0xd0, 0xff, 0xd1, 0xd3, 0xd3, 0xff, 0xd2, 0xd4, 0xd4, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd9, 0xda, 0xdb, 0xff, 0xd3, 0xd6, 0xdb, 0xff, 0xcd, 0xd2, 0xd9, 0xff, 0xcc, 0xcf, 0xd6, 0xff, 0xc8, 0xcd, 0xd7, 0xff, 0xc8, 0xd0, 0xdd, 0xff, 0xcc, 0xd2, 0xe0, 0xff, 0xc8, 0xcf, 0xe0, 0xff, 0xc3, 0xcc, 0xe0, 0xff, 0xbf, 0xcb, 0xdd, 0xff, 0xbc, 0xc9, 0xd8, 0xff, 0xbb, 0xc9, 0xda, 0xff, 0xc1, 0xce, 0xdd, 0xff, 0xc8, 0xd2, 0xe0, 0xff, 0xcd, 0xd6, 0xdf, 0xff, 0xd1, 0xd7, 0xe0, 0xff, 0xd5, 0xd8, 0xe2, 0xff, 0xd7, 0xda, 0xdf, 0xff, 0xda, 0xdb, 0xdd, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xd7, 0xd9, 0xda, 0xff, 0xd5, 0xd7, 0xd8, 0xff, 0xd0, 0xd2, 0xd3, 0xff, 0xcf, 0xd1, 0xd2, 0xff, 0xcd, 0xcf, 0xcf, 0xff, 0xcc, 0xce, 0xce, 0xff, 0xcf, 0xd0, 0xce, 0xff, 0xbc, 0xc0, 0xc4, 0xff, 0x82, 0x95, 0xa5, 0xff, 0x55, 0x6b, 0x8a, 0xff, 0x31, 0x41, 0x6c, 0xff, 0x2a, 0x38, 0x66, 0xff, 0x27, 0x3a, 0x65, 0xff, 0x21, 0x38, 0x63, 0xff, 0x24, 0x39, 0x66, 0xff, 0x2c, 0x3c, 0x6a, 0xff, 0x22, 0x33, 0x60, 0xff, 0x0e, 0x23, 0x45, 0xff, 0x16, 0x28, 0x48, 0xff, 0x1f, 0x2b, 0x56, 0xff, 0x23, 0x2e, 0x59, 0xff, 0x19, 0x24, 0x47, 0xff, 0x0b, 0x15, 0x33, 0xff, 0x0c, 0x15, 0x30, 0xff, 0x0f, 0x1a, 0x34, 0xff, 0x11, 0x1d, 0x32, 0xff, 0x0b, 0x14, 0x2e, 0xff, 0x07, 0x12, 0x2e, 0xff, 0x11, 0x1e, 0x3c, 0xff, 0x19, 0x24, 0x47, 0xff, 0x1c, 0x25, 0x4b, 0xff, 0x20, 0x27, 0x50, 0xff, 0x2f, 0x36, 0x5c, 0xff, 0x36, 0x3f, 0x64, 0xff, 0x39, 0x45, 0x70, 0xff, 0x43, 0x52, 0x84, 0xff, 0x4a, 0x58, 0x8e, 0xff, 0x44, 0x55, 0x8b, 0xff, 0x4b, 0x5f, 0x95, 0xff, 0x51, 0x62, 0x98, 0xff, 0x45, 0x52, 0x89, 0xff, 0x2c, 0x3c, 0x65, 0xff, 0x0d, 0x18, 0x3d, 0xff, 0x0d, 0x15, 0x3a, 0xff, 0x15, 0x27, 0x4b, 0xff, 0x22, 0x34, 0x59, 0xff, 0x27, 0x39, 0x65, 0xff, 0x2d, 0x46, 0x77, 0xff, 0x3a, 0x4e, 0x84, 0xff, 0x3c, 0x4e, 0x86, 0xff, 0x3f, 0x51, 0x8a, 0xff, 0x47, 0x58, 0x8c, 0xff, 0x4c, 0x5d, 0x8e, 0xff, 0x57, 0x69, 0x9f, 0xff, 0x67, 0x77, 0xa2, 0xff, 0x5b, 0x6e, 0x90, 0xff, 0x50, 0x66, 0x82, 0xff, 0x5a, 0x64, 0x67, 0xff, 0x4f, 0x55, 0x5c, 0xff, 0x42, 0x4e, 0x76, 0xff, 0x57, 0x61, 0x8a, 0xff, 0x5f, 0x66, 0x81, 0xff, 0x55, 0x58, 0x5d, 0xff, 0x50, 0x4e, 0x45, 0xff, 0x47, 0x49, 0x45, 0xff, 0x46, 0x4b, 0x46, 0xff, 0x4b, 0x4f, 0x4b, 0xff, 0x4a, 0x4e, 0x4a, 0xff, 0x47, 0x4b, 0x47, 0xff, 0x46, 0x4a, 0x46, 0xff, 0x4b, 0x4c, 0x48, 0xff, 0x52, 0x54, 0x52, 0xff, 0x51, 0x56, 0x55, 0xff, 0x52, 0x5a, 0x5a, 0xff, 0x56, 0x5c, 0x61, 0xff, 0x5b, 0x5f, 0x6a, 0xff, 0x5a, 0x5e, 0x6e, 0xff, 0x5a, 0x63, 0x74, 0xff, 0x5b, 0x66, 0x78, 0xff, 0x57, 0x63, 0x7a, 0xff, 0x5e, 0x67, 0x7b, 0xff, 0x7a, 0x77, 0x7e, 0xff, 0x96, 0x84, 0x8e, 0xff, 0xa1, 0x8e, 0x9c, 0xff, 0xab, 0x97, 0xa5, 0xff, 0xc1, 0xa8, 0xb6, 0xff, 0xcf, 0xbb, 0xc3, 0xff, 0xd0, 0xbf, 0xc4, 0xff, 0xd0, 0xbf, 0xc6, 0xff, 0xcd, 0xbd, 0xc5, 0xff, 0xca, 0xb4, 0xc3, 0xff, 0xbb, 0xaa, 0xbf, 0xff, 0xa8, 0xa0, 0xb7, 0xff, 0x9b, 0x91, 0xa1, 0xff, 0x79, 0x78, 0x89, 0xff, 0x57, 0x62, 0x7f, 0xff, 0x50, 0x5c, 0x7d, 0xff, 0x50, 0x5b, 0x7c, 0xff, 0x4d, 0x59, 0x77, 0xab,
    0xd8, 0xda, 0xda, 0x8b, 0xd7, 0xd9, 0xd9, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd1, 0xd2, 0xd3, 0xff, 0xd0, 0xd2, 0xd2, 0xff, 0xd1, 0xd3, 0xd2, 0xff, 0xd3, 0xd5, 0xd4, 0xff, 0xd5, 0xd7, 0xd6, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xd1, 0xd4, 0xda, 0xff, 0xcc, 0xd1, 0xd9, 0xff, 0xcc, 0xd1, 0xd5, 0xff, 0xc6, 0xcd, 0xd3, 0xff, 0xc4, 0xce, 0xd7, 0xff, 0xc7, 0xcf, 0xd8, 0xff, 0xc4, 0xcc, 0xd8, 0xff, 0xbf, 0xc9, 0xd9, 0xff, 0xbb, 0xc8, 0xd8, 0xff, 0xb7, 0xc5, 0xd5, 0xff, 0xb8, 0xc5, 0xd5, 0xff, 0xbd, 0xca, 0xda, 0xff, 0xc6, 0xd0, 0xdd, 0xff, 0xcb, 0xd4, 0xdd, 0xff, 0xce, 0xd4, 0xde, 0xff, 0xd0, 0xd4, 0xde, 0xff, 0xd3, 0xd5, 0xda, 0xff, 0xd5, 0xd6, 0xd8, 0xff, 0xd5, 0xd8, 0xd8, 0xff, 0xd4, 0xd6, 0xd7, 0xff, 0xd4, 0xd6, 0xd7, 0xff, 0xd3, 0xd5, 0xd6, 0xff, 0xce, 0xd0, 0xd1, 0xff, 0xcc, 0xce, 0xd0, 0xff, 0xc9, 0xcb, 0xcb, 0xff, 0xc7, 0xc9, 0xc9, 0xff, 0xc7, 0xc8, 0xc7, 0xff, 0xc9, 0xc9, 0xc6, 0xff, 0x9b, 0xaa, 0xb1, 0xff, 0x51, 0x64, 0x7b, 0xff, 0x29, 0x39, 0x58, 0xff, 0x1e, 0x31, 0x54, 0xff, 0x20, 0x32, 0x5c, 0xff, 0x21, 0x34, 0x5e, 0xff, 0x20, 0x33, 0x5a, 0xff, 0x21, 0x30, 0x59, 0xff, 0x1b, 0x26, 0x51, 0xff, 0x11, 0x1f, 0x45, 0xff, 0x19, 0x2d, 0x4c, 0xff, 0x20, 0x33, 0x5c, 0xff, 0x1d, 0x2f, 0x5d, 0xff, 0x17, 0x28, 0x50, 0xff, 0x0e, 0x1d, 0x40, 0xff, 0x0c, 0x18, 0x33, 0xff, 0x0c, 0x18, 0x30, 0xff, 0x08, 0x17, 0x2b, 0xff, 0x09, 0x15, 0x30, 0xff, 0x0d, 0x18, 0x38, 0xff, 0x1e, 0x2d, 0x4f, 0xff, 0x28, 0x36, 0x5b, 0xff, 0x22, 0x2e, 0x57, 0xff, 0x26, 0x31, 0x5f, 0xff, 0x3b, 0x45, 0x70, 0xff, 0x3f, 0x4a, 0x74, 0xff, 0x3f, 0x4e, 0x7b, 0xff, 0x4f, 0x60, 0x8f, 0xff, 0x5c, 0x6e, 0xa1, 0xff, 0x5f, 0x73, 0xa8, 0xff, 0x57, 0x6d, 0xa3, 0xff, 0x4a, 0x61, 0x9b, 0xff, 0x41, 0x54, 0x8d, 0xff, 0x34, 0x4a, 0x74, 0xff, 0x1f, 0x2e, 0x53, 0xff, 0x0a, 0x10, 0x34, 0xff, 0x07, 0x13, 0x32, 0xff, 0x19, 0x24, 0x46, 0xff, 0x22, 0x32, 0x59, 0xff, 0x28, 0x3d, 0x6a, 0xff, 0x33, 0x46, 0x77, 0xff, 0x3b, 0x4e, 0x7e, 0xff, 0x3f, 0x52, 0x82, 0xff, 0x44, 0x55, 0x86, 0xff, 0x4a, 0x59, 0x8a, 0xff, 0x54, 0x63, 0x97, 0xff, 0x64, 0x74, 0x9e, 0xff, 0x5e, 0x71, 0x94, 0xff, 0x50, 0x5c, 0x7c, 0xff, 0x62, 0x67, 0x6f, 0xff, 0x6f, 0x71, 0x69, 0xff, 0x68, 0x6c, 0x69, 0xff, 0x61, 0x6a, 0x67, 0xff, 0x68, 0x65, 0x62, 0xff, 0x60, 0x61, 0x5f, 0xff, 0x55, 0x5a, 0x57, 0xff, 0x4a, 0x4e, 0x49, 0xff, 0x46, 0x4a, 0x44, 0xff, 0x4a, 0x4d, 0x48, 0xff, 0x47, 0x4b, 0x46, 0xff, 0x44, 0x48, 0x43, 0xff, 0x43, 0x46, 0x41, 0xff, 0x49, 0x4a, 0x45, 0xff, 0x4f, 0x50, 0x4d, 0xff, 0x4b, 0x50, 0x4f, 0xff, 0x4c, 0x53, 0x53, 0xff, 0x50, 0x56, 0x5c, 0xff, 0x57, 0x5a, 0x65, 0xff, 0x58, 0x5c, 0x6d, 0xff, 0x59, 0x61, 0x73, 0xff, 0x59, 0x63, 0x76, 0xff, 0x56, 0x62, 0x7a, 0xff, 0x5f, 0x67, 0x7d, 0xff, 0x79, 0x77, 0x7e, 0xff, 0x92, 0x83, 0x8d, 0xff, 0xa2, 0x90, 0x9f, 0xff, 0xaf, 0x9a, 0xa9, 0xff, 0xc4, 0xab, 0xba, 0xff, 0xd0, 0xbe, 0xc6, 0xff, 0xd3, 0xc2, 0xc8, 0xff, 0xd4, 0xc2, 0xc9, 0xff, 0xd0, 0xbf, 0xc8, 0xff, 0xcd, 0xb7, 0xc6, 0xff, 0xbd, 0xad, 0xc3, 0xff, 0xaa, 0xa3, 0xba, 0xff, 0xa1, 0x99, 0xaa, 0xff, 0x7d, 0x7f, 0x90, 0xff, 0x55, 0x61, 0x7f, 0xff, 0x4c, 0x58, 0x79, 0xff, 0x4b, 0x56, 0x78, 0xff, 0x49, 0x54, 0x73, 0x8b,
    0xd4, 0xd6, 0xd6, 0x6b, 0xd5, 0xd8, 0xd7, 0xff, 0xd5, 0xd8, 0xd6, 0xff, 0xd1, 0xd4, 0xd3, 0xff, 0xcf, 0xd2, 0xd1, 0xff, 0xd0, 0xd2, 0xd2, 0xff, 0xd4, 0xd6, 0xd6, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd2, 0xd4, 0xd5, 0xff, 0xcd, 0xd1, 0xd5, 0xff, 0xca, 0xcf, 0xd4, 0xff, 0xc9, 0xcf, 0xd2, 0xff, 0xc4, 0xcb, 0xcf, 0xff, 0xc2, 0xca, 0xd2, 0xff, 0xc5, 0xcd, 0xd6, 0xff, 0xc1, 0xcb, 0xd5, 0xff, 0xbc, 0xc8, 0xd5, 0xff, 0xb8, 0xc4, 0xd4, 0xff, 0xb4, 0xc1, 0xd2, 0xff, 0xb4, 0xc0, 0xd2, 0xff, 0xbb, 0xc6, 0xd6, 0xff, 0xc2, 0xcc, 0xd9, 0xff, 0xc7, 0xcf, 0xda, 0xff, 0xcb, 0xd2, 0xda, 0xff, 0xce, 0xd4, 0xda, 0xff, 0xd1, 0xd3, 0xd8, 0xff, 0xd3, 0xd4, 0xd7, 0xff, 0xd3, 0xd6, 0xd6, 0xff, 0xd2, 0xd4, 0xd5, 0xff, 0xd1, 0xd3, 0xd4, 0xff, 0xce, 0xd1, 0xd2, 0xff, 0xca, 0xcc, 0xcd, 0xff, 0xc7, 0xca, 0xc9, 0xff, 0xc5, 0xc8, 0xc7, 0xff, 0xc4, 0xc7, 0xc7, 0xff, 0xc2, 0xc4, 0xc4, 0xff, 0xcc, 0xcb, 0xc5, 0xff, 0xbb, 0xc2, 0xc2, 0xff, 0x5c, 0x6a, 0x7a, 0xff, 0x20, 0x2f, 0x49, 0xff, 0x1b, 0x2c, 0x4a, 0xff, 0x16, 0x27, 0x4d, 0xff, 0x1d, 0x2e, 0x55, 0xff, 0x1b, 0x2d, 0x4f, 0xff, 0x15, 0x25, 0x47, 0xff, 0x13, 0x1f, 0x44, 0xff, 0x11, 0x1e, 0x43, 0xff, 0x18, 0x2b, 0x4d, 0xff, 0x22, 0x37, 0x5f, 0xff, 0x26, 0x3b, 0x62, 0xff, 0x2e, 0x42, 0x62, 0xff, 0x20, 0x32, 0x4e, 0xff, 0x0c, 0x19, 0x34, 0xff, 0x08, 0x14, 0x2d, 0xff, 0x08, 0x15, 0x2c, 0xff, 0x0e, 0x19, 0x38, 0xff, 0x17, 0x23, 0x47, 0xff, 0x1e, 0x2f, 0x55, 0xff, 0x29, 0x3a, 0x62, 0xff, 0x26, 0x35, 0x61, 0xff, 0x33, 0x42, 0x70, 0xff, 0x4e, 0x5a, 0x88, 0xff, 0x58, 0x64, 0x92, 0xff, 0x5b, 0x6a, 0x9a, 0xff, 0x66, 0x76, 0xaa, 0xff, 0x68, 0x7c, 0xb2, 0xff, 0x5d, 0x74, 0xa9, 0xff, 0x50, 0x6b, 0x9f, 0xff, 0x4f, 0x67, 0x9d, 0xff, 0x45, 0x5b, 0x91, 0xff, 0x39, 0x4f, 0x7d, 0xff, 0x32, 0x3e, 0x67, 0xff, 0x1d, 0x22, 0x42, 0xff, 0x0c, 0x13, 0x29, 0xff, 0x0c, 0x15, 0x32, 0xff, 0x1d, 0x27, 0x49, 0xff, 0x24, 0x32, 0x5a, 0xff, 0x2f, 0x40, 0x6c, 0xff, 0x3d, 0x4d, 0x7a, 0xff, 0x3f, 0x50, 0x7c, 0xff, 0x44, 0x54, 0x81, 0xff, 0x49, 0x58, 0x85, 0xff, 0x4f, 0x5e, 0x8d, 0xff, 0x5e, 0x6f, 0x97, 0xff, 0x5b, 0x6e, 0x90, 0xff, 0x4d, 0x58, 0x78, 0xff, 0x65, 0x6c, 0x7a, 0xff, 0x73, 0x75, 0x75, 0xff, 0x6f, 0x70, 0x6a, 0xff, 0x62, 0x6d, 0x65, 0xff, 0x61, 0x68, 0x66, 0xff, 0x63, 0x69, 0x65, 0xff, 0x5c, 0x62, 0x5d, 0xff, 0x52, 0x57, 0x51, 0xff, 0x47, 0x4c, 0x46, 0xff, 0x46, 0x4b, 0x45, 0xff, 0x44, 0x49, 0x43, 0xff, 0x41, 0x46, 0x41, 0xff, 0x40, 0x45, 0x3f, 0xff, 0x46, 0x49, 0x44, 0xff, 0x4d, 0x4f, 0x4c, 0xff, 0x4a, 0x4f, 0x4c, 0xff, 0x4b, 0x53, 0x50, 0xff, 0x50, 0x56, 0x5a, 0xff, 0x57, 0x5a, 0x64, 0xff, 0x59, 0x5d, 0x6b, 0xff, 0x59, 0x63, 0x73, 0xff, 0x5b, 0x66, 0x77, 0xff, 0x59, 0x65, 0x7a, 0xff, 0x5c, 0x67, 0x7a, 0xff, 0x76, 0x75, 0x7e, 0xff, 0x96, 0x85, 0x8d, 0xff, 0xa4, 0x92, 0x9d, 0xff, 0xb1, 0x9f, 0xab, 0xff, 0xc5, 0xb0, 0xbe, 0xff, 0xd3, 0xc0, 0xc8, 0xff, 0xd6, 0xc3, 0xcb, 0xff, 0xd8, 0xc5, 0xcb, 0xff, 0xd4, 0xc3, 0xcc, 0xff, 0xce, 0xbb, 0xc9, 0xff, 0xc0, 0xb1, 0xc4, 0xff, 0xae, 0xa5, 0xba, 0xff, 0xa5, 0x9b, 0xad, 0xff, 0x84, 0x80, 0x93, 0xff, 0x57, 0x60, 0x7c, 0xff, 0x49, 0x55, 0x75, 0xff, 0x48, 0x53, 0x72, 0xff, 0x45, 0x51, 0x70, 0x6b,
    0xd6, 0xd6, 0xd6, 0x4b, 0xd4, 0xd7, 0xd6, 0xff, 0xd4, 0xd8, 0xd5, 0xff, 0xd2, 0xd5, 0xd2, 0xff, 0xcd, 0xd0, 0xcf, 0xff, 0xcd, 0xcf, 0xd1, 0xff, 0xd1, 0xd3, 0xd4, 0xff, 0xd5, 0xd7, 0xd8, 0xff, 0xd3, 0xd5, 0xd6, 0xff, 0xca, 0xcf, 0xd0, 0xff, 0xc4, 0xcc, 0xcc, 0xff, 0xc2, 0xca, 0xcc, 0xff, 0xc2, 0xc8, 0xce, 0xff, 0xc4, 0xc9, 0xd1, 0xff, 0xc1, 0xc8, 0xd0, 0xff, 0xbc, 0xc6, 0xcf, 0xff, 0xb7, 0xc3, 0xcf, 0xff, 0xb7, 0xc3, 0xd0, 0xff, 0xb6, 0xc0, 0xce, 0xff, 0xb6, 0xc1, 0xcf, 0xff, 0xbc, 0xc6, 0xd3, 0xff, 0xc1, 0xca, 0xd5, 0xff, 0xc8, 0xcd, 0xd7, 0xff, 0xca, 0xd0, 0xd6, 0xff, 0xc9, 0xd2, 0xd4, 0xff, 0xcc, 0xd1, 0xd4, 0xff, 0xce, 0xd0, 0xd4, 0xff, 0xd0, 0xd1, 0xd2, 0xff, 0xce, 0xd1, 0xd1, 0xff, 0xce, 0xd0, 0xd1, 0xff, 0xcb, 0xcd, 0xcf, 0xff, 0xc8, 0xca, 0xca, 0xff, 0xc4, 0xc7, 0xc4, 0xff, 0xc2, 0xc5, 0xc4, 0xff, 0xc4, 0xc6, 0xc6, 0xff, 0xc1, 0xc3, 0xc3, 0xff, 0xc3, 0xc5, 0xc3, 0xff, 0xcf, 0xd0, 0xce, 0xff, 0x77, 0x82, 0x8e, 0xff, 0x1c, 0x29, 0x46, 0xff, 0x1b, 0x27, 0x46, 0xff, 0x1a, 0x2a, 0x4b, 0xff, 0x1a, 0x29, 0x4b, 0xff, 0x17, 0x24, 0x45, 0xff, 0x0e, 0x1c, 0x3c, 0xff, 0x0f, 0x1e, 0x3d, 0xff, 0x11, 0x21, 0x42, 0xff, 0x1a, 0x2d, 0x50, 0xff, 0x27, 0x39, 0x61, 0xff, 0x2d, 0x40, 0x6e, 0xff, 0x37, 0x49, 0x71, 0xff, 0x35, 0x46, 0x68, 0xff, 0x22, 0x33, 0x55, 0xff, 0x18, 0x26, 0x49, 0xff, 0x1e, 0x29, 0x4c, 0xff, 0x2b, 0x32, 0x5b, 0xff, 0x31, 0x39, 0x66, 0xff, 0x2b, 0x37, 0x66, 0xff, 0x2c, 0x3a, 0x6b, 0xff, 0x3e, 0x4c, 0x7d, 0xff, 0x55, 0x63, 0x93, 0xff, 0x66, 0x74, 0xa2, 0xff, 0x7b, 0x8b, 0xb9, 0xff, 0x7d, 0x8d, 0xbd, 0xff, 0x7c, 0x90, 0xc5, 0xff, 0x71, 0x8c, 0xc7, 0xff, 0x61, 0x80, 0xc2, 0xff, 0x5a, 0x79, 0xbe, 0xff, 0x59, 0x75, 0xb3, 0xff, 0x47, 0x64, 0x9b, 0xff, 0x38, 0x50, 0x83, 0xff, 0x33, 0x42, 0x72, 0xff, 0x28, 0x34, 0x58, 0xff, 0x1a, 0x25, 0x3d, 0xff, 0x08, 0x13, 0x2a, 0xff, 0x0e, 0x14, 0x2b, 0xff, 0x1f, 0x29, 0x4a, 0xff, 0x29, 0x3b, 0x5f, 0xff, 0x37, 0x46, 0x73, 0xff, 0x3e, 0x4c, 0x7a, 0xff, 0x43, 0x53, 0x7b, 0xff, 0x46, 0x56, 0x7e, 0xff, 0x4b, 0x5b, 0x84, 0xff, 0x5e, 0x70, 0x95, 0xff, 0x59, 0x6a, 0x8e, 0xff, 0x45, 0x56, 0x78, 0xff, 0x67, 0x75, 0x86, 0xff, 0x76, 0x7b, 0x81, 0xff, 0x6b, 0x72, 0x78, 0xff, 0x64, 0x6d, 0x70, 0xff, 0x61, 0x6d, 0x70, 0xff, 0x65, 0x6e, 0x6f, 0xff, 0x63, 0x69, 0x66, 0xff, 0x5c, 0x61, 0x5d, 0xff, 0x4f, 0x55, 0x51, 0xff, 0x48, 0x4d, 0x4a, 0xff, 0x44, 0x49, 0x47, 0xff, 0x42, 0x47, 0x45, 0xff, 0x41, 0x47, 0x44, 0xff, 0x44, 0x4a, 0x47, 0xff, 0x49, 0x4f, 0x4c, 0xff, 0x4b, 0x50, 0x4d, 0xff, 0x4c, 0x54, 0x51, 0xff, 0x51, 0x5a, 0x5b, 0xff, 0x58, 0x5c, 0x65, 0xff, 0x5a, 0x60, 0x6b, 0xff, 0x5c, 0x65, 0x73, 0xff, 0x5e, 0x69, 0x7b, 0xff, 0x5d, 0x69, 0x7a, 0xff, 0x5b, 0x69, 0x78, 0xff, 0x6f, 0x70, 0x82, 0xff, 0x96, 0x86, 0x8d, 0xff, 0xa3, 0x92, 0x96, 0xff, 0xaf, 0x9f, 0xa8, 0xff, 0xc2, 0xb1, 0xbe, 0xff, 0xd6, 0xc0, 0xc9, 0xff, 0xda, 0xc4, 0xcc, 0xff, 0xdb, 0xc5, 0xcd, 0xff, 0xd8, 0xc6, 0xd0, 0xff, 0xce, 0xbe, 0xcb, 0xff, 0xc1, 0xb3, 0xc2, 0xff, 0xb4, 0xa8, 0xb8, 0xff, 0xab, 0x9b, 0xaf, 0xff, 0x8e, 0x83, 0x97, 0xff, 0x5d, 0x63, 0x7b, 0xff, 0x46, 0x52, 0x71, 0xff, 0x46, 0x50, 0x6e, 0xff, 0x40, 0x4a, 0x69, 0x4b,
    0xd0, 0xd0, 0xd0, 0x21, 0xd3, 0xd5, 0xd4, 0xff, 0xd1, 0xd4, 0xd2, 0xff, 0xce, 0xd0, 0xce, 0xff, 0xcd, 0xcf, 0xcf, 0xff, 0xcc, 0xce, 0xd0, 0xff, 0xcd, 0xcf, 0xd0, 0xff, 0xd1, 0xd3, 0xd3, 0xff, 0xd1, 0xd2, 0xd2, 0xff, 0xc7, 0xcc, 0xcc, 0xff, 0xbe, 0xc6, 0xc6, 0xff, 0xbc, 0xc4, 0xc6, 0xff, 0xbd, 0xc4, 0xc9, 0xff, 0xc0, 0xc5, 0xce, 0xff, 0xbd, 0xc3, 0xc7, 0xff, 0xba, 0xc1, 0xc7, 0xff, 0xb8, 0xc1, 0xcb, 0xff, 0xbb, 0xc5, 0xcd, 0xff, 0xba, 0xc4, 0xcc, 0xff, 0xbc, 0xc6, 0xcd, 0xff, 0xc2, 0xcb, 0xd4, 0xff, 0xc6, 0xcd, 0xd6, 0xff, 0xcd, 0xcf, 0xd5, 0xff, 0xca, 0xd0, 0xd4, 0xff, 0xc6, 0xd0, 0xd3, 0xff, 0xcb, 0xcf, 0xd3, 0xff, 0xcd, 0xcf, 0xd3, 0xff, 0xce, 0xcf, 0xd0, 0xff, 0xcc, 0xce, 0xcf, 0xff, 0xcc, 0xce, 0xd0, 0xff, 0xcc, 0xcd, 0xd0, 0xff, 0xc6, 0xc8, 0xc8, 0xff, 0xc1, 0xc4, 0xc1, 0xff, 0xc0, 0xc1, 0xc1, 0xff, 0xc0, 0xc2, 0xc2, 0xff, 0xbf, 0xc0, 0xc1, 0xff, 0xb8, 0xbf, 0xbd, 0xff, 0xc6, 0xcc, 0xc9, 0xff, 0x8f, 0x99, 0x9d, 0xff, 0x27, 0x34, 0x4b, 0xff, 0x1e, 0x29, 0x47, 0xff, 0x1d, 0x2a, 0x48, 0xff, 0x17, 0x24, 0x44, 0xff, 0x10, 0x1a, 0x39, 0xff, 0x0f, 0x1a, 0x39, 0xff, 0x11, 0x1d, 0x3d, 0xff, 0x11, 0x22, 0x44, 0xff, 0x1b, 0x2e, 0x50, 0xff, 0x2c, 0x40, 0x6a, 0xff, 0x3c, 0x51, 0x88, 0xff, 0x41, 0x56, 0x86, 0xff, 0x3c, 0x50, 0x7a, 0xff, 0x30, 0x45, 0x6b, 0xff, 0x26, 0x37, 0x5d, 0xff, 0x24, 0x31, 0x57, 0xff, 0x2b, 0x34, 0x5e, 0xff, 0x32, 0x38, 0x65, 0xff, 0x37, 0x3b, 0x6d, 0xff, 0x37, 0x3e, 0x6f, 0xff, 0x3b, 0x43, 0x72, 0xff, 0x45, 0x4b, 0x7b, 0xff, 0x4c, 0x59, 0x81, 0xff, 0x5a, 0x6a, 0x8e, 0xff, 0x5e, 0x6e, 0x97, 0xff, 0x59, 0x6d, 0x9a, 0xff, 0x57, 0x6e, 0xa4, 0xff, 0x5b, 0x73, 0xb6, 0xff, 0x5b, 0x76, 0xc1, 0xff, 0x5a, 0x76, 0xbc, 0xff, 0x51, 0x6d, 0xac, 0xff, 0x40, 0x5b, 0x92, 0xff, 0x31, 0x47, 0x79, 0xff, 0x25, 0x37, 0x64, 0xff, 0x21, 0x30, 0x55, 0xff, 0x18, 0x25, 0x3a, 0xff, 0x06, 0x0d, 0x1f, 0xff, 0x12, 0x1e, 0x3c, 0xff, 0x20, 0x34, 0x58, 0xff, 0x30, 0x3e, 0x6c, 0xff, 0x3d, 0x4b, 0x7b, 0xff, 0x41, 0x53, 0x79, 0xff, 0x44, 0x57, 0x7a, 0xff, 0x45, 0x57, 0x7b, 0xff, 0x5d, 0x70, 0x94, 0xff, 0x58, 0x68, 0x91, 0xff, 0x47, 0x55, 0x7a, 0xff, 0x6b, 0x77, 0x8d, 0xff, 0x77, 0x7d, 0x8a, 0xff, 0x6d, 0x76, 0x80, 0xff, 0x67, 0x71, 0x79, 0xff, 0x65, 0x71, 0x79, 0xff, 0x67, 0x71, 0x77, 0xff, 0x67, 0x6f, 0x72, 0xff, 0x62, 0x68, 0x6a, 0xff, 0x57, 0x5d, 0x60, 0xff, 0x4d, 0x52, 0x57, 0xff, 0x46, 0x4d, 0x4c, 0xff, 0x44, 0x4b, 0x49, 0xff, 0x43, 0x4a, 0x49, 0xff, 0x48, 0x4f, 0x4e, 0xff, 0x4d, 0x54, 0x53, 0xff, 0x4c, 0x53, 0x51, 0xff, 0x4d, 0x57, 0x56, 0xff, 0x50, 0x5d, 0x61, 0xff, 0x57, 0x5f, 0x6a, 0xff, 0x5b, 0x62, 0x71, 0xff, 0x5e, 0x66, 0x78, 0xff, 0x5e, 0x69, 0x7d, 0xff, 0x5d, 0x68, 0x7d, 0xff, 0x5d, 0x65, 0x7d, 0xff, 0x69, 0x6b, 0x82, 0xff, 0x8b, 0x80, 0x8c, 0xff, 0x9f, 0x8d, 0x92, 0xff, 0xa8, 0x97, 0xa1, 0xff, 0xbd, 0xac, 0xb9, 0xff, 0xd4, 0xbf, 0xc8, 0xff, 0xd8, 0xc2, 0xc9, 0xff, 0xd9, 0xc3, 0xcb, 0xff, 0xd5, 0xc2, 0xcc, 0xff, 0xcb, 0xba, 0xc8, 0xff, 0xbe, 0xaf, 0xc0, 0xff, 0xb0, 0xa5, 0xb6, 0xff, 0xa8, 0x99, 0xad, 0xff, 0x8c, 0x81, 0x95, 0xff, 0x59, 0x5f, 0x78, 0xff, 0x44, 0x50, 0x70, 0xff, 0x45, 0x4f, 0x6d, 0xff, 0x3d, 0x45, 0x64, 0x21,
    0xff, 0xff, 0xff, 0x01, 0xcf, 0xd3, 0xd0, 0xf2, 0xcc, 0xcf, 0xcc, 0xff, 0xc8, 0xcb, 0xc9, 0xff, 0xc8, 0xcb, 0xca, 0xff, 0xc7, 0xc9, 0xca, 0xff, 0xc7, 0xca, 0xcb, 0xff, 0xc9, 0xcb, 0xcd, 0xff, 0xc6, 0xc9, 0xcb, 0xff, 0xc0, 0xc5, 0xc7, 0xff, 0xbb, 0xc2, 0xc3, 0xff, 0xb7, 0xbe, 0xc1, 0xff, 0xb5, 0xbc, 0xc1, 0xff, 0xb5, 0xbb, 0xc2, 0xff, 0xb9, 0xbe, 0xc2, 0xff, 0xbb, 0xc1, 0xc5, 0xff, 0xb9, 0xc2, 0xca, 0xff, 0xbd, 0xc7, 0xcd, 0xff, 0xbf, 0xc8, 0xcc, 0xff, 0xc0, 0xca, 0xce, 0xff, 0xc6, 0xce, 0xd5, 0xff, 0xca, 0xcf, 0xd6, 0xff, 0xcd, 0xd0, 0xd4, 0xff, 0xca, 0xd0, 0xd3, 0xff, 0xc8, 0xd0, 0xd2, 0xff, 0xcb, 0xcf, 0xd3, 0xff, 0xcd, 0xcf, 0xd3, 0xff, 0xce, 0xd0, 0xd1, 0xff, 0xcd, 0xcf, 0xcf, 0xff, 0xcd, 0xce, 0xd0, 0xff, 0xcb, 0xce, 0xcf, 0xff, 0xc5, 0xc8, 0xc7, 0xff, 0xbf, 0xc3, 0xbf, 0xff, 0xbe, 0xc1, 0xbf, 0xff, 0xbe, 0xc1, 0xc0, 0xff, 0xbc, 0xbf, 0xbf, 0xff, 0xb6, 0xbf, 0xbc, 0xff, 0xbf, 0xc6, 0xc0, 0xff, 0xa4, 0xac, 0xac, 0xff, 0x3e, 0x45, 0x59, 0xff, 0x1b, 0x24, 0x40, 0xff, 0x16, 0x21, 0x3e, 0xff, 0x15, 0x1f, 0x3d, 0xff, 0x11, 0x1b, 0x37, 0xff, 0x17, 0x21, 0x3d, 0xff, 0x12, 0x1d, 0x3b, 0xff, 0x12, 0x22, 0x44, 0xff, 0x20, 0x33, 0x56, 0xff, 0x32, 0x47, 0x70, 0xff, 0x3b, 0x4b, 0x7c, 0xff, 0x2d, 0x3a, 0x64, 0xff, 0x1f, 0x2b, 0x4d, 0xff, 0x1a, 0x26, 0x42, 0xff, 0x1a, 0x25, 0x41, 0xff, 0x17, 0x21, 0x3d, 0xff, 0x0d, 0x18, 0x35, 0xff, 0x0f, 0x18, 0x38, 0xff, 0x1f, 0x23, 0x44, 0xff, 0x25, 0x29, 0x48, 0xff, 0x1c, 0x22, 0x43, 0xff, 0x13, 0x1b, 0x3c, 0xff, 0x14, 0x16, 0x3a, 0xff, 0x15, 0x17, 0x3c, 0xff, 0x15, 0x1d, 0x40, 0xff, 0x1e, 0x20, 0x44, 0xff, 0x26, 0x25, 0x4c, 0xff, 0x30, 0x36, 0x5e, 0xff, 0x3e, 0x48, 0x6e, 0xff, 0x48, 0x56, 0x7e, 0xff, 0x55, 0x5e, 0x90, 0xff, 0x51, 0x5d, 0x92, 0xff, 0x40, 0x53, 0x80, 0xff, 0x31, 0x40, 0x65, 0xff, 0x1f, 0x28, 0x4b, 0xff, 0x1c, 0x26, 0x42, 0xff, 0x12, 0x18, 0x34, 0xff, 0x10, 0x1a, 0x3f, 0xff, 0x21, 0x32, 0x59, 0xff, 0x2c, 0x3c, 0x6a, 0xff, 0x3c, 0x4a, 0x78, 0xff, 0x43, 0x53, 0x77, 0xff, 0x43, 0x55, 0x78, 0xff, 0x40, 0x53, 0x76, 0xff, 0x5c, 0x6d, 0x91, 0xff, 0x5b, 0x68, 0x92, 0xff, 0x4c, 0x5c, 0x81, 0xff, 0x6c, 0x79, 0x90, 0xff, 0x79, 0x80, 0x91, 0xff, 0x72, 0x7b, 0x89, 0xff, 0x6e, 0x78, 0x86, 0xff, 0x68, 0x75, 0x83, 0xff, 0x67, 0x70, 0x7c, 0xff, 0x64, 0x6c, 0x77, 0xff, 0x5f, 0x67, 0x70, 0xff, 0x59, 0x62, 0x69, 0xff, 0x4f, 0x58, 0x60, 0xff, 0x45, 0x4f, 0x52, 0xff, 0x43, 0x4e, 0x4f, 0xff, 0x42, 0x4d, 0x4f, 0xff, 0x48, 0x53, 0x54, 0xff, 0x4d, 0x59, 0x5a, 0xff, 0x4a, 0x55, 0x56, 0xff, 0x4c, 0x57, 0x5a, 0xff, 0x52, 0x5d, 0x65, 0xff, 0x59, 0x63, 0x70, 0xff, 0x5c, 0x63, 0x75, 0xff, 0x5d, 0x63, 0x78, 0xff, 0x5d, 0x65, 0x7c, 0xff, 0x5b, 0x63, 0x7c, 0xff, 0x5b, 0x60, 0x7c, 0xff, 0x5f, 0x66, 0x81, 0xff, 0x7b, 0x77, 0x88, 0xff, 0x92, 0x82, 0x87, 0xff, 0x9c, 0x8b, 0x94, 0xff, 0xb3, 0xa1, 0xae, 0xff, 0xcd, 0xb7, 0xc0, 0xff, 0xd1, 0xbc, 0xc3, 0xff, 0xd2, 0xbd, 0xc4, 0xff, 0xcd, 0xba, 0xc4, 0xff, 0xc3, 0xb2, 0xbf, 0xff, 0xb5, 0xa7, 0xb6, 0xff, 0xab, 0x9e, 0xad, 0xff, 0xa2, 0x92, 0xa5, 0xff, 0x83, 0x7b, 0x8e, 0xff, 0x53, 0x59, 0x72, 0xff, 0x41, 0x4e, 0x6d, 0xff, 0x43, 0x4c, 0x6b, 0xf2, 0x00, 0x00, 0x00, 0x01,
    0x00, 0x00, 0x00, 0x00, 0xc8, 0xcf, 0xca, 0xc6, 0xc6, 0xcc, 0xc5, 0xff, 0xc1, 0xc6, 0xc5, 0xff, 0xc1, 0xc3, 0xc2, 0xff, 0xc1, 0xc3, 0xc2, 0xff, 0xbf, 0xc3, 0xc4, 0xff, 0xbd, 0xc3, 0xc9, 0xff, 0xb9, 0xc0, 0xc9, 0xff, 0xb5, 0xbc, 0xc1, 0xff, 0xb3, 0xbb, 0xbd, 0xff, 0xaf, 0xb6, 0xba, 0xff, 0xad, 0xb4, 0xb7, 0xff, 0xae, 0xb5, 0xb7, 0xff, 0xb3, 0xbb, 0xc1, 0xff, 0xba, 0xc1, 0xc8, 0xff, 0xbd, 0xc4, 0xcb, 0xff, 0xc0, 0xc8, 0xcf, 0xff, 0xc2, 0xc9, 0xd0, 0xff, 0xc2, 0xca, 0xd1, 0xff, 0xc7, 0xce, 0xd3, 0xff, 0xcc, 0xd2, 0xd5, 0xff, 0xcb, 0xd1, 0xd5, 0xff, 0xc9, 0xcf, 0xd1, 0xff, 0xca, 0xce, 0xce, 0xff, 0xc9, 0xcd, 0xce, 0xff, 0xc9, 0xcc, 0xcd, 0xff, 0xc9, 0xcb, 0xcc, 0xff, 0xc9, 0xcb, 0xcc, 0xff, 0xc9, 0xcb, 0xcd, 0xff, 0xc4, 0xc8, 0xca, 0xff, 0xbe, 0xc3, 0xc1, 0xff, 0xbb, 0xc0, 0xbc, 0xff, 0xba, 0xc0, 0xbc, 0xff, 0xb9, 0xbf, 0xbc, 0xff, 0xb8, 0xbe, 0xbb, 0xff, 0xb8, 0xbc, 0xb8, 0xff, 0xb8, 0xbd, 0xb7, 0xff, 0xb1, 0xb6, 0xbb, 0xff, 0x55, 0x58, 0x67, 0xff, 0x1e, 0x28, 0x40, 0xff, 0x16, 0x1f, 0x3c, 0xff, 0x13, 0x1b, 0x38, 0xff, 0x11, 0x1d, 0x3b, 0xff, 0x1b, 0x26, 0x43, 0xff, 0x13, 0x1f, 0x3d, 0xff, 0x13, 0x22, 0x41, 0xff, 0x25, 0x35, 0x56, 0xff, 0x26, 0x39, 0x5a, 0xff, 0x1f, 0x2a, 0x4b, 0xff, 0x1a, 0x1a, 0x39, 0xff, 0x12, 0x13, 0x2f, 0xff, 0x10, 0x12, 0x32, 0xff, 0x10, 0x14, 0x36, 0xff, 0x12, 0x18, 0x3d, 0xff, 0x10, 0x18, 0x39, 0xff, 0x14, 0x1a, 0x41, 0xff, 0x16, 0x1b, 0x44, 0xff, 0x1f, 0x20, 0x46, 0xff, 0x1c, 0x25, 0x4f, 0xff, 0x11, 0x1c, 0x4a, 0xff, 0x19, 0x18, 0x4d, 0xff, 0x29, 0x29, 0x5e, 0xff, 0x1b, 0x1e, 0x4d, 0xff, 0x1e, 0x1b, 0x48, 0xff, 0x2b, 0x31, 0x58, 0xff, 0x24, 0x2b, 0x52, 0xff, 0x20, 0x21, 0x42, 0xff, 0x1b, 0x1c, 0x3a, 0xff, 0x15, 0x12, 0x34, 0xff, 0x0e, 0x13, 0x30, 0xff, 0x16, 0x1b, 0x33, 0xff, 0x12, 0x1a, 0x2d, 0xff, 0x0f, 0x19, 0x33, 0xff, 0x1e, 0x27, 0x49, 0xff, 0x16, 0x22, 0x45, 0xff, 0x14, 0x21, 0x46, 0xff, 0x24, 0x33, 0x5e, 0xff, 0x2a, 0x3f, 0x6f, 0xff, 0x3b, 0x4d, 0x76, 0xff, 0x4f, 0x57, 0x77, 0xff, 0x41, 0x4f, 0x76, 0xff, 0x40, 0x52, 0x7d, 0xff, 0x62, 0x6b, 0x8f, 0xff, 0x58, 0x63, 0x89, 0xff, 0x4b, 0x60, 0x83, 0xff, 0x69, 0x7a, 0x90, 0xff, 0x76, 0x82, 0x93, 0xff, 0x6f, 0x7d, 0x8f, 0xff, 0x6d, 0x7b, 0x8d, 0xff, 0x6a, 0x77, 0x87, 0xff, 0x6a, 0x72, 0x84, 0xff, 0x66, 0x6d, 0x7e, 0xff, 0x62, 0x6e, 0x7a, 0xff, 0x5c, 0x69, 0x72, 0xff, 0x51, 0x5f, 0x66, 0xff, 0x42, 0x50, 0x57, 0xff, 0x40, 0x4e, 0x55, 0xff, 0x42, 0x50, 0x57, 0xff, 0x49, 0x58, 0x5d, 0xff, 0x4f, 0x5e, 0x62, 0xff, 0x4b, 0x5b, 0x5e, 0xff, 0x4d, 0x59, 0x61, 0xff, 0x56, 0x5d, 0x6b, 0xff, 0x5c, 0x64, 0x73, 0xff, 0x5c, 0x62, 0x76, 0xff, 0x5d, 0x61, 0x79, 0xff, 0x5e, 0x62, 0x7a, 0xff, 0x5b, 0x5f, 0x78, 0xff, 0x51, 0x5c, 0x73, 0xff, 0x53, 0x62, 0x7a, 0xff, 0x6d, 0x70, 0x87, 0xff, 0x86, 0x7e, 0x86, 0xff, 0x94, 0x86, 0x8f, 0xff, 0xad, 0x98, 0xa5, 0xff, 0xc7, 0xb2, 0xbb, 0xff, 0xcb, 0xba, 0xc1, 0xff, 0xc9, 0xbb, 0xc1, 0xff, 0xc8, 0xba, 0xbf, 0xff, 0xbf, 0xb1, 0xb9, 0xff, 0xb2, 0xa6, 0xb2, 0xff, 0xa9, 0x9a, 0xa6, 0xff, 0x96, 0x8b, 0x98, 0xff, 0x76, 0x74, 0x87, 0xff, 0x50, 0x58, 0x6e, 0xff, 0x3f, 0x4e, 0x6b, 0xff, 0x3f, 0x4a, 0x6a, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xbf, 0xc6, 0xc4, 0x90, 0xb9, 0xc2, 0xbd, 0xff, 0xb4, 0xbb, 0xbc, 0xff, 0xb7, 0xbc, 0xbd, 0xff, 0xb8, 0xbb, 0xbd, 0xff, 0xb1, 0xb8, 0xbc, 0xff, 0xae, 0xb6, 0xbe, 0xff, 0xaf, 0xb4, 0xbd, 0xff, 0xab, 0xb1, 0xb6, 0xff, 0xaa, 0xb1, 0xb3, 0xff, 0xa6, 0xad, 0xb0, 0xff, 0xa4, 0xab, 0xae, 0xff, 0xa9, 0xb0, 0xb3, 0xff, 0xb3, 0xb7, 0xbb, 0xff, 0xba, 0xbf, 0xc2, 0xff, 0xc0, 0xc5, 0xc8, 0xff, 0xc0, 0xc5, 0xc8, 0xff, 0xc1, 0xc7, 0xca, 0xff, 0xc4, 0xc9, 0xcd, 0xff, 0xc9, 0xcd, 0xcf, 0xff, 0xcd, 0xd1, 0xd1, 0xff, 0xcc, 0xd0, 0xd1, 0xff, 0xc9, 0xce, 0xce, 0xff, 0xc7, 0xcb, 0xcc, 0xff, 0xc5, 0xc9, 0xc9, 0xff, 0xc4, 0xc7, 0xc8, 0xff, 0xc5, 0xc7, 0xc8, 0xff, 0xc6, 0xc7, 0xc8, 0xff, 0xc5, 0xc7, 0xc8, 0xff, 0xbe, 0xc1, 0xc6, 0xff, 0xb9, 0xbd, 0xbf, 0xff, 0xb7, 0xbc, 0xba, 0xff, 0xb7, 0xbc, 0xba, 0xff, 0xb6, 0xbb, 0xba, 0xff, 0xb5, 0xba, 0xb9, 0xff, 0xb7, 0xb8, 0xba, 0xff, 0xb0, 0xb3, 0xb1, 0xff, 0xb9, 0xbf, 0xc0, 0xff, 0x70, 0x75, 0x7f, 0xff, 0x20, 0x2b, 0x41, 0xff, 0x18, 0x21, 0x3c, 0xff, 0x13, 0x1b, 0x35, 0xff, 0x12, 0x1e, 0x3d, 0xff, 0x1a, 0x25, 0x43, 0xff, 0x11, 0x1c, 0x39, 0xff, 0x13, 0x1d, 0x38, 0xff, 0x1a, 0x23, 0x3b, 0xff, 0x15, 0x1b, 0x2e, 0xff, 0x11, 0x10, 0x23, 0xff, 0x11, 0x0c, 0x28, 0xff, 0x1c, 0x1f, 0x42, 0xff, 0x1c, 0x21, 0x52, 0xff, 0x25, 0x2b, 0x60, 0xff, 0x2b, 0x32, 0x69, 0xff, 0x2c, 0x32, 0x69, 0xff, 0x4a, 0x53, 0x88, 0xff, 0x3a, 0x3f, 0x80, 0xff, 0x41, 0x48, 0x8a, 0xff, 0x6d, 0x7b, 0xaf, 0xff, 0x5d, 0x68, 0xa0, 0xff, 0x45, 0x50, 0x90, 0xff, 0x85, 0x8f, 0xc3, 0xff, 0x6e, 0x75, 0xb0, 0xff, 0x3b, 0x49, 0x90, 0xff, 0x75, 0x8a, 0xbf, 0xff, 0x57, 0x66, 0x95, 0xff, 0x2c, 0x35, 0x64, 0xff, 0x20, 0x24, 0x40, 0xff, 0x0c, 0x0b, 0x19, 0xff, 0x11, 0x0d, 0x19, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x02, 0x07, 0xff, 0x0f, 0x21, 0x39, 0xff, 0x28, 0x38, 0x5f, 0xff, 0x28, 0x38, 0x61, 0xff, 0x20, 0x32, 0x5d, 0xff, 0x29, 0x3a, 0x65, 0xff, 0x2f, 0x44, 0x73, 0xff, 0x40, 0x53, 0x7b, 0xff, 0x4e, 0x57, 0x76, 0xff, 0x3f, 0x4c, 0x73, 0xff, 0x45, 0x57, 0x83, 0xff, 0x61, 0x6b, 0x8e, 0xff, 0x51, 0x5d, 0x82, 0xff, 0x4b, 0x5e, 0x86, 0xff, 0x69, 0x7a, 0x97, 0xff, 0x71, 0x84, 0x98, 0xff, 0x6c, 0x7f, 0x96, 0xff, 0x6a, 0x7b, 0x91, 0xff, 0x6e, 0x7c, 0x8e, 0xff, 0x70, 0x7c, 0x8d, 0xff, 0x70, 0x78, 0x8b, 0xff, 0x70, 0x7a, 0x8a, 0xff, 0x6a, 0x76, 0x82, 0xff, 0x5f, 0x6b, 0x74, 0xff, 0x4f, 0x5c, 0x65, 0xff, 0x49, 0x56, 0x60, 0xff, 0x47, 0x53, 0x5d, 0xff, 0x4b, 0x57, 0x61, 0xff, 0x4e, 0x5b, 0x64, 0xff, 0x4c, 0x59, 0x61, 0xff, 0x4f, 0x5b, 0x66, 0xff, 0x53, 0x5e, 0x6e, 0xff, 0x57, 0x61, 0x72, 0xff, 0x57, 0x5f, 0x75, 0xff, 0x5a, 0x60, 0x7b, 0xff, 0x59, 0x61, 0x7c, 0xff, 0x59, 0x5f, 0x79, 0xff, 0x4d, 0x56, 0x70, 0xff, 0x4c, 0x59, 0x73, 0xff, 0x69, 0x6b, 0x85, 0xff, 0x7c, 0x77, 0x89, 0xff, 0x87, 0x7e, 0x8d, 0xff, 0x9e, 0x8f, 0x9c, 0xff, 0xba, 0xab, 0xb1, 0xff, 0xc0, 0xb5, 0xba, 0xff, 0xbe, 0xb6, 0xbc, 0xff, 0xbe, 0xb7, 0xbb, 0xff, 0xb8, 0xb0, 0xb6, 0xff, 0xad, 0xa7, 0xb2, 0xff, 0x9e, 0x9a, 0xa4, 0xff, 0x87, 0x84, 0x90, 0xff, 0x6e, 0x6d, 0x80, 0xff, 0x50, 0x59, 0x6e, 0xff, 0x41, 0x4f, 0x6d, 0xff, 0x41, 0x4a, 0x6c, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xb3, 0xbf, 0xbf, 0x58, 0xac, 0xb7, 0xb6, 0xff, 0xa7, 0xb1, 0xb4, 0xff, 0xad, 0xb5, 0xb9, 0xff, 0xae, 0xb3, 0xb8, 0xff, 0xa4, 0xae, 0xb5, 0xff, 0xa1, 0xa9, 0xb3, 0xff, 0xa4, 0xa9, 0xb2, 0xff, 0xa1, 0xa7, 0xac, 0xff, 0xa1, 0xa8, 0xab, 0xff, 0x9d, 0xa4, 0xa7, 0xff, 0x9a, 0xa2, 0xa5, 0xff, 0xa5, 0xac, 0xaf, 0xff, 0xaf, 0xb4, 0xb4, 0xff, 0xb3, 0xb8, 0xb8, 0xff, 0xb5, 0xb9, 0xba, 0xff, 0xba, 0xbe, 0xbe, 0xff, 0xbf, 0xc2, 0xc3, 0xff, 0xc2, 0xc6, 0xc7, 0xff, 0xc8, 0xca, 0xca, 0xff, 0xcb, 0xcc, 0xcc, 0xff, 0xcb, 0xcc, 0xcb, 0xff, 0xc8, 0xcb, 0xcb, 0xff, 0xc5, 0xc9, 0xca, 0xff, 0xc1, 0xc6, 0xc7, 0xff, 0xc0, 0xc4, 0xc5, 0xff, 0xc1, 0xc3, 0xc4, 0xff, 0xc1, 0xc3, 0xc4, 0xff, 0xc0, 0xc3, 0xc4, 0xff, 0xbb, 0xbd, 0xc6, 0xff, 0xb8, 0xbb, 0xc0, 0xff, 0xb7, 0xbb, 0xbc, 0xff, 0xb7, 0xba, 0xbc, 0xff, 0xb6, 0xb9, 0xbb, 0xff, 0xb6, 0xb9, 0xbc, 0xff, 0xb5, 0xb4, 0xbb, 0xff, 0xae, 0xb2, 0xb0, 0xff, 0xba, 0xc2, 0xbe, 0xff, 0x8a, 0x91, 0x97, 0xff, 0x2a, 0x36, 0x48, 0xff, 0x1c, 0x28, 0x3e, 0xff, 0x15, 0x1e, 0x37, 0xff, 0x12, 0x1e, 0x3e, 0xff, 0x1e, 0x29, 0x49, 0xff, 0x1a, 0x25, 0x42, 0xff, 0x0e, 0x14, 0x2a, 0xff, 0x07, 0x08, 0x16, 0xff, 0x01, 0x00, 0x06, 0xff, 0x01, 0x00, 0x01, 0xff, 0x07, 0x09, 0x1e, 0xff, 0x34, 0x42, 0x66, 0xff, 0x39, 0x46, 0x72, 0xff, 0x4b, 0x59, 0x84, 0xff, 0x47, 0x58, 0x89, 0xff, 0x53, 0x63, 0x9b, 0xff, 0xa8, 0xbd, 0xdd, 0xff, 0x83, 0x97, 0xb8, 0xff, 0x69, 0x7e, 0xac, 0xff, 0xc2, 0xcf, 0xeb, 0xff, 0xc8, 0xd1, 0xec, 0xff, 0x6c, 0x80, 0xb2, 0xff, 0xa2, 0xb0, 0xcf, 0xff, 0xac, 0xbb, 0xcc, 0xff, 0x66, 0x80, 0xac, 0xff, 0x81, 0x8f, 0xb4, 0xff, 0x66, 0x6a, 0x7f, 0xff, 0x57, 0x57, 0x77, 0xff, 0x34, 0x2f, 0x3d, 0xff, 0x2c, 0x2b, 0x28, 0xff, 0x36, 0x39, 0x44, 0xff, 0x11, 0x14, 0x24, 0xff, 0x1e, 0x2b, 0x3a, 0xff, 0x20, 0x32, 0x51, 0xff, 0x2a, 0x3c, 0x66, 0xff, 0x2c, 0x3f, 0x6d, 0xff, 0x2b, 0x41, 0x71, 0xff, 0x2f, 0x41, 0x6e, 0xff, 0x34, 0x47, 0x75, 0xff, 0x45, 0x57, 0x7f, 0xff, 0x48, 0x52, 0x73, 0xff, 0x40, 0x4c, 0x74, 0xff, 0x4d, 0x5e, 0x89, 0xff, 0x5f, 0x69, 0x8a, 0xff, 0x50, 0x5b, 0x80, 0xff, 0x4e, 0x5e, 0x8c, 0xff, 0x6d, 0x7d, 0xa2, 0xff, 0x79, 0x90, 0xa8, 0xff, 0x76, 0x8b, 0xa7, 0xff, 0x72, 0x87, 0xa0, 0xff, 0x78, 0x88, 0x9c, 0xff, 0x7b, 0x8a, 0x9e, 0xff, 0x7e, 0x89, 0x9e, 0xff, 0x7f, 0x88, 0x9b, 0xff, 0x79, 0x83, 0x92, 0xff, 0x6f, 0x7a, 0x86, 0xff, 0x64, 0x71, 0x7d, 0xff, 0x5a, 0x67, 0x74, 0xff, 0x4f, 0x5b, 0x67, 0xff, 0x4b, 0x56, 0x65, 0xff, 0x4a, 0x55, 0x64, 0xff, 0x49, 0x54, 0x63, 0xff, 0x4c, 0x59, 0x68, 0xff, 0x53, 0x60, 0x71, 0xff, 0x59, 0x65, 0x78, 0xff, 0x5a, 0x64, 0x7d, 0xff, 0x5c, 0x65, 0x82, 0xff, 0x5d, 0x66, 0x83, 0xff, 0x5a, 0x62, 0x7f, 0xff, 0x50, 0x59, 0x76, 0xff, 0x4e, 0x5a, 0x76, 0xff, 0x62, 0x63, 0x81, 0xff, 0x70, 0x6d, 0x88, 0xff, 0x7a, 0x75, 0x89, 0xff, 0x8e, 0x86, 0x92, 0xff, 0xa4, 0x9d, 0xa2, 0xff, 0xad, 0xa8, 0xab, 0xff, 0xae, 0xac, 0xaf, 0xff, 0xae, 0xab, 0xaf, 0xff, 0xa6, 0xa5, 0xaa, 0xff, 0x9c, 0x9d, 0xa6, 0xff, 0x8c, 0x91, 0x9a, 0xff, 0x7c, 0x83, 0x8d, 0xff, 0x66, 0x6a, 0x7c, 0xff, 0x4b, 0x54, 0x6a, 0xff, 0x43, 0x50, 0x6e, 0xff, 0x3f, 0x4b, 0x6b, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xb3, 0xb3, 0xbc, 0x1b, 0xa8, 0xaf, 0xb8, 0xff, 0x9f, 0xaa, 0xb2, 0xff, 0x9e, 0xa9, 0xb1, 0xff, 0x9e, 0xa7, 0xb0, 0xff, 0x9c, 0xa5, 0xae, 0xff, 0x97, 0xa0, 0xa9, 0xff, 0x95, 0x9e, 0xa7, 0xff, 0x92, 0x9b, 0xa3, 0xff, 0x93, 0x9c, 0xa6, 0xff, 0x8f, 0x9a, 0xa0, 0xff, 0x94, 0x9d, 0x9f, 0xff, 0xa2, 0xa8, 0xa8, 0xff, 0xad, 0xb0, 0xb1, 0xff, 0xae, 0xb1, 0xb2, 0xff, 0xad, 0xb0, 0xb1, 0xff, 0xb6, 0xb7, 0xb7, 0xff, 0xbc, 0xbc, 0xbc, 0xff, 0xc1, 0xc1, 0xc0, 0xff, 0xc3, 0xc5, 0xc5, 0xff, 0xc4, 0xc7, 0xc7, 0xff, 0xc6, 0xc7, 0xc8, 0xff, 0xc4, 0xc7, 0xc8, 0xff, 0xc1, 0xc5, 0xc6, 0xff, 0xbc, 0xc1, 0xc1, 0xff, 0xbb, 0xbe, 0xbf, 0xff, 0xbd, 0xbf, 0xc2, 0xff, 0xb9, 0xbd, 0xc3, 0xff, 0xb8, 0xbc, 0xc4, 0xff, 0xb6, 0xb9, 0xc2, 0xff, 0xb4, 0xb6, 0xbe, 0xff, 0xb1, 0xb4, 0xbc, 0xff, 0xb2, 0xb4, 0xb9, 0xff, 0xb0, 0xb4, 0xb9, 0xff, 0xae, 0xb4, 0xb9, 0xff, 0xaa, 0xb1, 0xb6, 0xff, 0xa7, 0xad, 0xb2, 0xff, 0xaf, 0xb5, 0xb8, 0xff, 0xa5, 0xae, 0xb4, 0xff, 0x48, 0x53, 0x5f, 0xff, 0x21, 0x2c, 0x3f, 0xff, 0x13, 0x1f, 0x37, 0xff, 0x13, 0x1f, 0x40, 0xff, 0x1c, 0x2c, 0x50, 0xff, 0x24, 0x33, 0x50, 0xff, 0x17, 0x1e, 0x37, 0xff, 0x09, 0x0c, 0x1e, 0xff, 0x03, 0x02, 0x0e, 0xff, 0x06, 0x02, 0x04, 0xff, 0x27, 0x28, 0x29, 0xff, 0x4c, 0x57, 0x6d, 0xff, 0x3f, 0x48, 0x5e, 0xff, 0x5c, 0x60, 0x69, 0xff, 0x73, 0x89, 0xa1, 0xff, 0x61, 0x71, 0x88, 0xff, 0x9d, 0x9a, 0x9c, 0xff, 0xb9, 0xc7, 0xd0, 0xff, 0x8e, 0xa5, 0xc1, 0xff, 0x90, 0x8b, 0x8f, 0xff, 0xad, 0xa9, 0xa6, 0xff, 0xb5, 0xcc, 0xeb, 0xff, 0x84, 0x92, 0xa4, 0xff, 0x77, 0x79, 0x71, 0xff, 0x8e, 0xa0, 0xaf, 0xff, 0x68, 0x7a, 0x94, 0xff, 0x3c, 0x41, 0x4e, 0xff, 0x49, 0x48, 0x5a, 0xff, 0x23, 0x1e, 0x22, 0xff, 0x0a, 0x09, 0x00, 0xff, 0x1b, 0x1f, 0x2f, 0xff, 0x39, 0x44, 0x68, 0xff, 0x35, 0x44, 0x67, 0xff, 0x27, 0x3a, 0x5e, 0xff, 0x2d, 0x41, 0x6a, 0xff, 0x2e, 0x42, 0x6f, 0xff, 0x32, 0x44, 0x77, 0xff, 0x31, 0x43, 0x71, 0xff, 0x3f, 0x49, 0x74, 0xff, 0x4a, 0x55, 0x7d, 0xff, 0x43, 0x4e, 0x77, 0xff, 0x4b, 0x52, 0x7b, 0xff, 0x57, 0x62, 0x84, 0xff, 0x5a, 0x61, 0x81, 0xff, 0x4d, 0x58, 0x80, 0xff, 0x4a, 0x5d, 0x89, 0xff, 0x6e, 0x81, 0xa8, 0xff, 0x83, 0x97, 0xb9, 0xff, 0x85, 0x9a, 0xb9, 0xff, 0x83, 0x99, 0xb5, 0xff, 0x83, 0x95, 0xb0, 0xff, 0x8a, 0x99, 0xb1, 0xff, 0x8b, 0x9a, 0xaf, 0xff, 0x89, 0x97, 0xa9, 0xff, 0x83, 0x8f, 0x9f, 0xff, 0x7b, 0x86, 0x92, 0xff, 0x72, 0x80, 0x8e, 0xff, 0x69, 0x77, 0x87, 0xff, 0x60, 0x69, 0x79, 0xff, 0x51, 0x5b, 0x6c, 0xff, 0x49, 0x54, 0x65, 0xff, 0x44, 0x4f, 0x61, 0xff, 0x4b, 0x56, 0x68, 0xff, 0x5a, 0x66, 0x7a, 0xff, 0x64, 0x6e, 0x86, 0xff, 0x67, 0x71, 0x8d, 0xff, 0x6d, 0x76, 0x94, 0xff, 0x74, 0x78, 0x95, 0xff, 0x6b, 0x71, 0x90, 0xff, 0x5e, 0x6b, 0x8c, 0xff, 0x68, 0x71, 0x8d, 0xff, 0x73, 0x77, 0x91, 0xff, 0x79, 0x77, 0x96, 0xff, 0x7f, 0x7c, 0x95, 0xff, 0x8a, 0x87, 0x98, 0xff, 0x95, 0x96, 0xa0, 0xff, 0x9c, 0xa0, 0x9e, 0xff, 0xa3, 0xa3, 0xa0, 0xff, 0xa2, 0x9f, 0xa3, 0xff, 0x9b, 0x9a, 0xa1, 0xff, 0x90, 0x92, 0x96, 0xff, 0x82, 0x87, 0x90, 0xff, 0x77, 0x81, 0x8a, 0xff, 0x5d, 0x6c, 0x7a, 0xff, 0x46, 0x53, 0x6d, 0xff, 0x45, 0x50, 0x6f, 0xff, 0x42, 0x4b, 0x67, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xb0, 0xbb, 0xd7, 0x9f, 0xa9, 0xb2, 0xff, 0x97, 0xa2, 0xaa, 0xff, 0x92, 0x9b, 0xa5, 0xff, 0x91, 0x9a, 0xa3, 0xff, 0x91, 0x9a, 0xa3, 0xff, 0x8f, 0x98, 0xa1, 0xff, 0x8d, 0x97, 0xa1, 0xff, 0x8e, 0x98, 0xa1, 0xff, 0x8c, 0x94, 0x9b, 0xff, 0x92, 0x97, 0x9a, 0xff, 0x9e, 0xa2, 0xa3, 0xff, 0xa9, 0xa8, 0xaa, 0xff, 0xac, 0xab, 0xad, 0xff, 0xad, 0xad, 0xaf, 0xff, 0xb4, 0xb3, 0xb4, 0xff, 0xb8, 0xb7, 0xb9, 0xff, 0xbd, 0xbb, 0xbc, 0xff, 0xbf, 0xbf, 0xc2, 0xff, 0xbf, 0xc0, 0xc4, 0xff, 0xc1, 0xc1, 0xc5, 0xff, 0xbf, 0xc1, 0xc5, 0xff, 0xba, 0xbe, 0xc1, 0xff, 0xb5, 0xb8, 0xbb, 0xff, 0xb3, 0xb5, 0xb8, 0xff, 0xb5, 0xb5, 0xbc, 0xff, 0xb4, 0xb6, 0xc1, 0xff, 0xb4, 0xb8, 0xc3, 0xff, 0xb4, 0xb6, 0xc0, 0xff, 0xae, 0xb0, 0xbb, 0xff, 0xa9, 0xab, 0xb6, 0xff, 0xa9, 0xab, 0xb5, 0xff, 0xa6, 0xab, 0xb4, 0xff, 0xa1, 0xa9, 0xb2, 0xff, 0xa1, 0xab, 0xb3, 0xff, 0xa1, 0xa9, 0xb2, 0xff, 0xa0, 0xa8, 0xb0, 0xff, 0xac, 0xb4, 0xc1, 0xff, 0x68, 0x71, 0x83, 0xff, 0x22, 0x2d, 0x40, 0xff, 0x1f, 0x2b, 0x41, 0xff, 0x15, 0x21, 0x42, 0xff, 0x17, 0x26, 0x4b, 0xff, 0x1d, 0x2e, 0x4e, 0xff, 0x1b, 0x24, 0x47, 0xff, 0x18, 0x1e, 0x3e, 0xff, 0x16, 0x17, 0x35, 0xff, 0x00, 0x00, 0x08, 0xff, 0x0f, 0x10, 0x0b, 0xff, 0x40, 0x4a, 0x54, 0xff, 0x46, 0x52, 0x64, 0xff, 0x53, 0x5c, 0x68, 0xff, 0x93, 0xa5, 0xb9, 0xff, 0x5f, 0x67, 0x74, 0xff, 0x61, 0x5d, 0x69, 0xff, 0xb3, 0xc6, 0xe1, 0xff, 0x9c, 0xb6, 0xd8, 0xff, 0x6d, 0x7a, 0x83, 0xff, 0x9e, 0xad, 0xa7, 0xff, 0xc9, 0xdb, 0xe9, 0xff, 0x90, 0x99, 0xad, 0xff, 0x81, 0x81, 0x94, 0xff, 0x43, 0x4b, 0x70, 0xff, 0x29, 0x33, 0x56, 0xff, 0x1b, 0x21, 0x28, 0xff, 0x0d, 0x14, 0x13, 0xff, 0x02, 0x09, 0x0a, 0xff, 0x0e, 0x0f, 0x21, 0xff, 0x42, 0x45, 0x73, 0xff, 0x4c, 0x58, 0x8a, 0xff, 0x30, 0x41, 0x68, 0xff, 0x32, 0x44, 0x65, 0xff, 0x32, 0x44, 0x70, 0xff, 0x33, 0x45, 0x75, 0xff, 0x34, 0x47, 0x76, 0xff, 0x37, 0x49, 0x72, 0xff, 0x4b, 0x54, 0x79, 0xff, 0x4b, 0x55, 0x77, 0xff, 0x43, 0x4f, 0x72, 0xff, 0x52, 0x5b, 0x7f, 0xff, 0x5a, 0x63, 0x84, 0xff, 0x52, 0x5b, 0x7d, 0xff, 0x46, 0x57, 0x81, 0xff, 0x4a, 0x5e, 0x8c, 0xff, 0x70, 0x82, 0xab, 0xff, 0x87, 0x9a, 0xbf, 0xff, 0x88, 0x9c, 0xbf, 0xff, 0x89, 0x9e, 0xbc, 0xff, 0x8d, 0xa0, 0xbc, 0xff, 0x94, 0xa3, 0xbe, 0xff, 0x95, 0xa3, 0xbb, 0xff, 0x93, 0xa2, 0xb5, 0xff, 0x8d, 0x9a, 0xac, 0xff, 0x86, 0x8f, 0x9f, 0xff, 0x7d, 0x87, 0x97, 0xff, 0x76, 0x7d, 0x8e, 0xff, 0x6d, 0x70, 0x83, 0xff, 0x5a, 0x5f, 0x71, 0xff, 0x4b, 0x52, 0x63, 0xff, 0x49, 0x4e, 0x5f, 0xff, 0x53, 0x5c, 0x6d, 0xff, 0x64, 0x6e, 0x83, 0xff, 0x6f, 0x76, 0x90, 0xff, 0x74, 0x7c, 0x98, 0xff, 0x7b, 0x81, 0x9e, 0xff, 0x81, 0x83, 0xa0, 0xff, 0x80, 0x82, 0xa1, 0xff, 0x7d, 0x83, 0xa5, 0xff, 0x85, 0x8b, 0xab, 0xff, 0x8d, 0x91, 0xb1, 0xff, 0x8d, 0x8f, 0xb0, 0xff, 0x8f, 0x8d, 0xac, 0xff, 0x96, 0x92, 0xaf, 0xff, 0x9e, 0x9e, 0xb2, 0xff, 0x9c, 0x9f, 0xa5, 0xff, 0x9b, 0x9b, 0xa0, 0xff, 0x9a, 0x98, 0x9f, 0xff, 0x94, 0x95, 0x9a, 0xff, 0x8a, 0x8e, 0x92, 0xff, 0x7f, 0x85, 0x8e, 0xff, 0x77, 0x80, 0x89, 0xff, 0x61, 0x70, 0x7d, 0xff, 0x4b, 0x58, 0x74, 0xff, 0x4a, 0x55, 0x76, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xac, 0xb3, 0xbc, 0x8e, 0x9e, 0xa9, 0xb1, 0xff, 0x91, 0x9c, 0xa4, 0xff, 0x87, 0x90, 0x99, 0xff, 0x7f, 0x88, 0x91, 0xff, 0x83, 0x8c, 0x95, 0xff, 0x87, 0x90, 0x99, 0xff, 0x88, 0x91, 0x9b, 0xff, 0x88, 0x91, 0x9a, 0xff, 0x87, 0x8b, 0x92, 0xff, 0x8b, 0x8e, 0x91, 0xff, 0x97, 0x99, 0x99, 0xff, 0xa8, 0xa4, 0xa6, 0xff, 0xae, 0xaa, 0xab, 0xff, 0xb2, 0xaf, 0xb0, 0xff, 0xb4, 0xb2, 0xb5, 0xff, 0xb4, 0xb3, 0xb8, 0xff, 0xb7, 0xb6, 0xba, 0xff, 0xba, 0xb9, 0xc0, 0xff, 0xba, 0xba, 0xc3, 0xff, 0xbc, 0xba, 0xc3, 0xff, 0xbc, 0xbd, 0xc4, 0xff, 0xb9, 0xbb, 0xc2, 0xff, 0xb1, 0xb4, 0xbb, 0xff, 0xae, 0xaf, 0xb7, 0xff, 0xb1, 0xaf, 0xba, 0xff, 0xad, 0xae, 0xba, 0xff, 0xab, 0xae, 0xbc, 0xff, 0xad, 0xae, 0xbc, 0xff, 0xa9, 0xaa, 0xb8, 0xff, 0xa6, 0xa6, 0xb4, 0xff, 0xa0, 0xa3, 0xb2, 0xff, 0x9a, 0xa1, 0xb0, 0xff, 0x95, 0x9e, 0xad, 0xff, 0x95, 0x9e, 0xad, 0xff, 0x96, 0x9f, 0xad, 0xff, 0x8f, 0x99, 0xa6, 0xff, 0x9b, 0xa1, 0xb6, 0xff, 0x88, 0x8e, 0xa6, 0xff, 0x30, 0x3b, 0x4d, 0xff, 0x25, 0x31, 0x46, 0xff, 0x16, 0x22, 0x44, 0xff, 0x18, 0x26, 0x4b, 0xff, 0x1e, 0x33, 0x50, 0xff, 0x17, 0x2a, 0x47, 0xff, 0x1e, 0x29, 0x49, 0xff, 0x2b, 0x2f, 0x57, 0xff, 0x11, 0x11, 0x30, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x13, 0x19, 0xff, 0x1c, 0x26, 0x33, 0xff, 0x2c, 0x34, 0x46, 0xff, 0x40, 0x43, 0x56, 0xff, 0x51, 0x5b, 0x72, 0xff, 0x68, 0x72, 0x90, 0xff, 0x5c, 0x6e, 0x8b, 0xff, 0x4b, 0x5e, 0x78, 0xff, 0x53, 0x6e, 0x82, 0xff, 0x68, 0x79, 0x91, 0xff, 0x3f, 0x3c, 0x57, 0xff, 0x26, 0x26, 0x42, 0xff, 0x42, 0x49, 0x6c, 0xff, 0x2e, 0x38, 0x57, 0xff, 0x1f, 0x23, 0x38, 0xff, 0x69, 0x69, 0x7b, 0xff, 0x4a, 0x50, 0x6f, 0xff, 0x4d, 0x53, 0x85, 0xff, 0x64, 0x65, 0x9e, 0xff, 0x55, 0x63, 0x96, 0xff, 0x3f, 0x56, 0x81, 0xff, 0x39, 0x4a, 0x76, 0xff, 0x39, 0x45, 0x74, 0xff, 0x2e, 0x3d, 0x71, 0xff, 0x32, 0x44, 0x74, 0xff, 0x35, 0x4a, 0x75, 0xff, 0x42, 0x55, 0x77, 0xff, 0x53, 0x5b, 0x7a, 0xff, 0x4b, 0x55, 0x71, 0xff, 0x49, 0x57, 0x70, 0xff, 0x55, 0x60, 0x7d, 0xff, 0x59, 0x61, 0x82, 0xff, 0x4c, 0x5a, 0x7e, 0xff, 0x47, 0x5f, 0x8b, 0xff, 0x51, 0x65, 0x96, 0xff, 0x6e, 0x7c, 0xa9, 0xff, 0x83, 0x96, 0xbc, 0xff, 0x84, 0x97, 0xbc, 0xff, 0x88, 0x9c, 0xbe, 0xff, 0x94, 0xa4, 0xc4, 0xff, 0x99, 0xa7, 0xc4, 0xff, 0x9a, 0xa6, 0xc2, 0xff, 0x97, 0xa4, 0xbb, 0xff, 0x95, 0xa1, 0xb6, 0xff, 0x92, 0x9c, 0xaf, 0xff, 0x8e, 0x92, 0xa3, 0xff, 0x86, 0x86, 0x98, 0xff, 0x7b, 0x78, 0x8b, 0xff, 0x67, 0x66, 0x79, 0xff, 0x52, 0x54, 0x66, 0xff, 0x52, 0x51, 0x63, 0xff, 0x5f, 0x63, 0x75, 0xff, 0x6f, 0x76, 0x8b, 0xff, 0x7b, 0x80, 0x9a, 0xff, 0x81, 0x85, 0xa1, 0xff, 0x84, 0x88, 0xa5, 0xff, 0x8b, 0x8b, 0xa9, 0xff, 0x94, 0x91, 0xaf, 0xff, 0x98, 0x95, 0xb6, 0xff, 0x98, 0x99, 0xbf, 0xff, 0x96, 0x9a, 0xc2, 0xff, 0x97, 0x99, 0xbf, 0xff, 0x99, 0x97, 0xbe, 0xff, 0xa0, 0x9a, 0xc5, 0xff, 0xa6, 0xa5, 0xc3, 0xff, 0xa1, 0xa4, 0xb2, 0xff, 0xa1, 0xa1, 0xb0, 0xff, 0x9e, 0xa0, 0xa8, 0xff, 0x91, 0x96, 0x98, 0xff, 0x83, 0x8c, 0x8f, 0xff, 0x7e, 0x85, 0x8e, 0xff, 0x7b, 0x83, 0x8c, 0xff, 0x67, 0x77, 0x83, 0xff, 0x54, 0x60, 0x7d, 0xff, 0x4d, 0x56, 0x7a, 0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0xb2, 0xb9, 0x46, 0xa5, 0xac, 0xb3, 0xff, 0x91, 0x9a, 0xa1, 0xff, 0x7f, 0x8a, 0x91, 0xff, 0x75, 0x7f, 0x85, 0xff, 0x74, 0x7e, 0x86, 0xff, 0x78, 0x82, 0x8c, 0xff, 0x7d, 0x85, 0x8e, 0xff, 0x80, 0x85, 0x90, 0xff, 0x7b, 0x80, 0x8e, 0xff, 0x82, 0x84, 0x8e, 0xff, 0x90, 0x8f, 0x94, 0xff, 0x9f, 0x9c, 0x9e, 0xff, 0xad, 0xa5, 0xa9, 0xff, 0xb1, 0xab, 0xb1, 0xff, 0xb0, 0xad, 0xb7, 0xff, 0xb1, 0xad, 0xb8, 0xff, 0xb2, 0xaf, 0xb9, 0xff, 0xb4, 0xb3, 0xbc, 0xff, 0xb5, 0xb6, 0xbf, 0xff, 0xb3, 0xb8, 0xc0, 0xff, 0xb5, 0xb8, 0xc0, 0xff, 0xb5, 0xb4, 0xbd, 0xff, 0xab, 0xaf, 0xb6, 0xff, 0xa6, 0xaa, 0xb2, 0xff, 0xa6, 0xaa, 0xb4, 0xff, 0xa9, 0xab, 0xb9, 0xff, 0xa6, 0xa9, 0xb9, 0xff, 0xa3, 0xa9, 0xb8, 0xff, 0xa4, 0xaa, 0xb8, 0xff, 0xa4, 0xa8, 0xb7, 0xff, 0x9e, 0xa0, 0xb2, 0xff, 0x91, 0x97, 0xaa, 0xff, 0x89, 0x94, 0xa9, 0xff, 0x84, 0x92, 0xa7, 0xff, 0x85, 0x90, 0xa6, 0xff, 0x83, 0x8b, 0xa1, 0xff, 0x8c, 0x92, 0xaa, 0xff, 0xa1, 0xa7, 0xbf, 0xff, 0x56, 0x5d, 0x73, 0xff, 0x21, 0x2c, 0x40, 0xff, 0x22, 0x30, 0x48, 0xff, 0x16, 0x23, 0x47, 0xff, 0x1f, 0x2d, 0x54, 0xff, 0x1c, 0x2c, 0x4d, 0xff, 0x18, 0x28, 0x46, 0xff, 0x2c, 0x39, 0x5a, 0xff, 0x3c, 0x40, 0x6c, 0xff, 0x1b, 0x18, 0x3a, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x06, 0x04, 0xff, 0x0f, 0x15, 0x1e, 0xff, 0x14, 0x19, 0x2a, 0xff, 0x22, 0x28, 0x3f, 0xff, 0x25, 0x29, 0x47, 0xff, 0x1d, 0x21, 0x37, 0xff, 0x23, 0x2a, 0x41, 0xff, 0x3b, 0x47, 0x6b, 0xff, 0x43, 0x4c, 0x73, 0xff, 0x45, 0x54, 0x77, 0xff, 0x55, 0x68, 0x8b, 0xff, 0x6e, 0x83, 0x9e, 0xff, 0xa4, 0xbc, 0xd2, 0xff, 0x82, 0x8f, 0xaf, 0xff, 0x7a, 0x80, 0xb1, 0xff, 0x71, 0x77, 0xbd, 0xff, 0x7d, 0x7c, 0xc5, 0xff, 0x68, 0x6f, 0x9f, 0xff, 0x42, 0x5b, 0x82, 0xff, 0x40, 0x5a, 0x84, 0xff, 0x40, 0x52, 0x87, 0xff, 0x35, 0x44, 0x78, 0xff, 0x2a, 0x3d, 0x6d, 0xff, 0x32, 0x43, 0x6f, 0xff, 0x3f, 0x4b, 0x76, 0xff, 0x51, 0x5c, 0x7e, 0xff, 0x51, 0x59, 0x78, 0xff, 0x4a, 0x4f, 0x6f, 0xff, 0x4f, 0x56, 0x71, 0xff, 0x55, 0x62, 0x7d, 0xff, 0x4c, 0x59, 0x7e, 0xff, 0x45, 0x57, 0x81, 0xff, 0x4e, 0x63, 0x94, 0xff, 0x55, 0x68, 0x9a, 0xff, 0x69, 0x7b, 0xa9, 0xff, 0x7f, 0x91, 0xbf, 0xff, 0x82, 0x95, 0xc0, 0xff, 0x89, 0x9d, 0xc4, 0xff, 0x92, 0xa6, 0xc9, 0xff, 0x96, 0xa8, 0xc8, 0xff, 0x97, 0xa7, 0xc5, 0xff, 0x98, 0xa4, 0xbf, 0xff, 0x9a, 0xa1, 0xbb, 0xff, 0x9b, 0x9e, 0xb5, 0xff, 0x97, 0x97, 0xaa, 0xff, 0x8c, 0x8a, 0x9d, 0xff, 0x81, 0x7b, 0x8f, 0xff, 0x75, 0x6c, 0x81, 0xff, 0x5b, 0x55, 0x68, 0xff, 0x59, 0x58, 0x69, 0xff, 0x68, 0x69, 0x7e, 0xff, 0x78, 0x77, 0x91, 0xff, 0x86, 0x85, 0x9f, 0xff, 0x8e, 0x8c, 0xa8, 0xff, 0x92, 0x8e, 0xac, 0xff, 0x94, 0x93, 0xb0, 0xff, 0x9c, 0x9b, 0xb9, 0xff, 0xa3, 0x9f, 0xc3, 0xff, 0xa0, 0xa0, 0xc5, 0xff, 0x9d, 0xa0, 0xc4, 0xff, 0x99, 0x99, 0xbf, 0xff, 0x98, 0x95, 0xc1, 0xff, 0x9e, 0x9d, 0xc5, 0xff, 0x9e, 0xa5, 0xc3, 0xff, 0x9d, 0xa3, 0xbb, 0xff, 0xa3, 0xa4, 0xb8, 0xff, 0xa4, 0xa6, 0xb4, 0xff, 0x97, 0x9c, 0xa4, 0xff, 0x89, 0x8f, 0x95, 0xff, 0x86, 0x8c, 0x95, 0xff, 0x7f, 0x88, 0x96, 0xff, 0x6e, 0x7b, 0x8e, 0xff, 0x5c, 0x6a, 0x87, 0xff, 0x4c, 0x5e, 0x83, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0xb6, 0xb6, 0x07, 0xa4, 0xa7, 0xb2, 0xef, 0x8c, 0x94, 0x9d, 0xff, 0x77, 0x80, 0x89, 0xff, 0x6c, 0x76, 0x7e, 0xff, 0x6b, 0x75, 0x7e, 0xff, 0x6c, 0x76, 0x80, 0xff, 0x70, 0x77, 0x81, 0xff, 0x73, 0x77, 0x83, 0xff, 0x70, 0x75, 0x82, 0xff, 0x78, 0x77, 0x84, 0xff, 0x88, 0x82, 0x8c, 0xff, 0x91, 0x8b, 0x92, 0xff, 0xa4, 0x96, 0xa1, 0xff, 0xab, 0xa0, 0xaf, 0xff, 0xa9, 0xa4, 0xb2, 0xff, 0xa9, 0xa4, 0xb1, 0xff, 0xac, 0xa6, 0xb4, 0xff, 0xaf, 0xaa, 0xb6, 0xff, 0xb1, 0xaf, 0xba, 0xff, 0xb1, 0xb4, 0xbe, 0xff, 0xaf, 0xb1, 0xba, 0xff, 0xaa, 0xa8, 0xb3, 0xff, 0xa2, 0xa4, 0xae, 0xff, 0x9c, 0xa2, 0xab, 0xff, 0x9e, 0xa4, 0xad, 0xff, 0xa7, 0xa9, 0xb8, 0xff, 0xa6, 0xa9, 0xba, 0xff, 0xa2, 0xa9, 0xb8, 0xff, 0xa2, 0xaa, 0xb9, 0xff, 0xa2, 0xa9, 0xb8, 0xff, 0x9f, 0xa1, 0xb3, 0xff, 0x90, 0x96, 0xa8, 0xff, 0x83, 0x8f, 0xa4, 0xff, 0x79, 0x88, 0x9e, 0xff, 0x74, 0x81, 0x97, 0xff, 0x7c, 0x84, 0x9b, 0xff, 0x8a, 0x8f, 0xa6, 0xff, 0xa1, 0xa5, 0xbd, 0xff, 0x8e, 0x91, 0xa9, 0xff, 0x30, 0x38, 0x4d, 0xff, 0x24, 0x32, 0x48, 0xff, 0x19, 0x24, 0x43, 0xff, 0x1b, 0x26, 0x4e, 0xff, 0x23, 0x31, 0x56, 0xff, 0x1a, 0x2a, 0x4b, 0xff, 0x1c, 0x2d, 0x48, 0xff, 0x48, 0x4f, 0x79, 0xff, 0x55, 0x50, 0x8d, 0xff, 0x28, 0x22, 0x54, 0xff, 0x0a, 0x13, 0x2f, 0xff, 0x21, 0x31, 0x4f, 0xff, 0x43, 0x58, 0x79, 0xff, 0x40, 0x56, 0x79, 0xff, 0x48, 0x5c, 0x86, 0xff, 0x6d, 0x82, 0xa7, 0xff, 0x7e, 0x91, 0xb6, 0xff, 0x6c, 0x7d, 0xaa, 0xff, 0x82, 0x99, 0xba, 0xff, 0x6e, 0x93, 0xb1, 0xff, 0x93, 0xb0, 0xd9, 0xff, 0x74, 0x8c, 0xb5, 0xff, 0x6f, 0x83, 0xb4, 0xff, 0x8d, 0x8c, 0xce, 0xff, 0x86, 0x83, 0xcd, 0xff, 0x71, 0x76, 0xba, 0xff, 0x5c, 0x65, 0x95, 0xff, 0x54, 0x61, 0x92, 0xff, 0x4d, 0x62, 0x96, 0xff, 0x4e, 0x66, 0x97, 0xff, 0x40, 0x53, 0x88, 0xff, 0x32, 0x44, 0x73, 0xff, 0x2e, 0x41, 0x6b, 0xff, 0x39, 0x48, 0x71, 0xff, 0x49, 0x51, 0x78, 0xff, 0x55, 0x5d, 0x7f, 0xff, 0x4f, 0x57, 0x77, 0xff, 0x4c, 0x4f, 0x72, 0xff, 0x4e, 0x54, 0x71, 0xff, 0x4b, 0x58, 0x76, 0xff, 0x45, 0x56, 0x7e, 0xff, 0x47, 0x5c, 0x89, 0xff, 0x50, 0x65, 0x9a, 0xff, 0x53, 0x67, 0x9c, 0xff, 0x66, 0x7c, 0xaa, 0xff, 0x7d, 0x8f, 0xbe, 0xff, 0x7f, 0x92, 0xbf, 0xff, 0x89, 0x9d, 0xc6, 0xff, 0x95, 0xa9, 0xcd, 0xff, 0x9a, 0xab, 0xcc, 0xff, 0x9b, 0xa9, 0xc9, 0xff, 0x9e, 0xa7, 0xc3, 0xff, 0x9e, 0xa0, 0xbc, 0xff, 0x9b, 0x98, 0xb1, 0xff, 0x97, 0x94, 0xa9, 0xff, 0x8f, 0x8a, 0x9f, 0xff, 0x86, 0x7d, 0x93, 0xff, 0x7e, 0x72, 0x86, 0xff, 0x65, 0x5a, 0x6c, 0xff, 0x66, 0x5e, 0x6f, 0xff, 0x74, 0x6d, 0x84, 0xff, 0x80, 0x79, 0x94, 0xff, 0x8e, 0x87, 0xa0, 0xff, 0x93, 0x8c, 0xa4, 0xff, 0x93, 0x8d, 0xa8, 0xff, 0x98, 0x93, 0xb2, 0xff, 0xa0, 0x9c, 0xbf, 0xff, 0xa8, 0xa4, 0xc9, 0xff, 0xa8, 0xa8, 0xcb, 0xff, 0xa5, 0xa8, 0xc9, 0xff, 0x9e, 0x9e, 0xc3, 0xff, 0x99, 0x96, 0xc2, 0xff, 0x98, 0x99, 0xbd, 0xff, 0x97, 0xa2, 0xbd, 0xff, 0x9d, 0xa4, 0xbe, 0xff, 0xa5, 0xa7, 0xbb, 0xff, 0xa5, 0xa8, 0xb7, 0xff, 0x9b, 0x9f, 0xad, 0xff, 0x93, 0x96, 0xa0, 0xff, 0x8a, 0x91, 0x9b, 0xff, 0x81, 0x8d, 0x9d, 0xff, 0x7d, 0x8b, 0x9f, 0xff, 0x6c, 0x81, 0x9d, 0xef, 0x48, 0x6d, 0x91, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9f, 0xa2, 0xaf, 0xa0, 0x8b, 0x93, 0x9f, 0xff, 0x76, 0x7f, 0x8b, 0xff, 0x67, 0x6f, 0x7c, 0xff, 0x66, 0x6e, 0x7a, 0xff, 0x64, 0x6e, 0x78, 0xff, 0x66, 0x6e, 0x79, 0xff, 0x68, 0x6d, 0x79, 0xff, 0x68, 0x6c, 0x73, 0xff, 0x70, 0x6c, 0x78, 0xff, 0x7f, 0x74, 0x85, 0xff, 0x88, 0x7d, 0x8c, 0xff, 0x9c, 0x88, 0x9a, 0xff, 0xa2, 0x92, 0xa8, 0xff, 0x9f, 0x97, 0xa9, 0xff, 0xa2, 0x99, 0xa9, 0xff, 0xa8, 0x9d, 0xaf, 0xff, 0xab, 0xa1, 0xb1, 0xff, 0xaa, 0xa4, 0xb2, 0xff, 0xa8, 0xa5, 0xb4, 0xff, 0xa3, 0xa0, 0xae, 0xff, 0x9c, 0x9a, 0xa8, 0xff, 0x9a, 0x9a, 0xa9, 0xff, 0x9b, 0x9e, 0xab, 0xff, 0xa1, 0xa5, 0xaf, 0xff, 0xa6, 0xa8, 0xb7, 0xff, 0xa4, 0xa7, 0xb7, 0xff, 0x9f, 0xa7, 0xb6, 0xff, 0xa0, 0xa8, 0xb6, 0xff, 0xa0, 0xa5, 0xb5, 0xff, 0x9b, 0x9c, 0xae, 0xff, 0x90, 0x95, 0xa8, 0xff, 0x83, 0x8e, 0xa2, 0xff, 0x72, 0x81, 0x97, 0xff, 0x6e, 0x7a, 0x90, 0xff, 0x75, 0x7f, 0x95, 0xff, 0x84, 0x85, 0x9e, 0xff, 0x92, 0x92, 0xab, 0xff, 0xac, 0xab, 0xc4, 0xff, 0x68, 0x6b, 0x83, 0xff, 0x24, 0x2c, 0x45, 0xff, 0x23, 0x2d, 0x46, 0xff, 0x15, 0x21, 0x42, 0xff, 0x1a, 0x29, 0x50, 0xff, 0x1e, 0x2e, 0x4f, 0xff, 0x19, 0x29, 0x44, 0xff, 0x2d, 0x3a, 0x55, 0xff, 0x55, 0x5b, 0x92, 0xff, 0x62, 0x5f, 0xac, 0xff, 0x43, 0x43, 0x82, 0xff, 0x2d, 0x37, 0x68, 0xff, 0x3a, 0x50, 0x76, 0xff, 0x2e, 0x43, 0x6b, 0xff, 0x3b, 0x4b, 0x7d, 0xff, 0x4b, 0x5f, 0x8a, 0xff, 0x70, 0x80, 0xa5, 0xff, 0x5e, 0x68, 0x9b, 0xff, 0x65, 0x72, 0xa9, 0xff, 0x5d, 0x6f, 0xaa, 0xff, 0x80, 0x8a, 0xcd, 0xff, 0x83, 0x8a, 0xc9, 0xff, 0x88, 0x94, 0xd0, 0xff, 0x9f, 0xa1, 0xdd, 0xff, 0x78, 0x7b, 0xb2, 0xff, 0x55, 0x5e, 0x89, 0xff, 0x57, 0x63, 0x8e, 0xff, 0x4f, 0x64, 0x9f, 0xff, 0x58, 0x6b, 0xa4, 0xff, 0x4d, 0x61, 0x97, 0xff, 0x41, 0x54, 0x8a, 0xff, 0x33, 0x47, 0x6f, 0xff, 0x2f, 0x42, 0x69, 0xff, 0x41, 0x4e, 0x75, 0xff, 0x51, 0x58, 0x7d, 0xff, 0x4f, 0x58, 0x7a, 0xff, 0x4b, 0x53, 0x74, 0xff, 0x57, 0x59, 0x7b, 0xff, 0x50, 0x59, 0x76, 0xff, 0x43, 0x54, 0x74, 0xff, 0x46, 0x58, 0x82, 0xff, 0x4a, 0x61, 0x90, 0xff, 0x51, 0x68, 0x9f, 0xff, 0x51, 0x67, 0xa0, 0xff, 0x64, 0x7a, 0xaa, 0xff, 0x78, 0x89, 0xb8, 0xff, 0x77, 0x8b, 0xb8, 0xff, 0x86, 0x9a, 0xc3, 0xff, 0x9b, 0xab, 0xcf, 0xff, 0x9f, 0xac, 0xcd, 0xff, 0x9f, 0xab, 0xca, 0xff, 0xa2, 0xa7, 0xc4, 0xff, 0xa0, 0xa1, 0xbc, 0xff, 0x9d, 0x99, 0xb2, 0xff, 0x9b, 0x95, 0xaa, 0xff, 0x96, 0x8d, 0xa2, 0xff, 0x8e, 0x82, 0x98, 0xff, 0x85, 0x7b, 0x8c, 0xff, 0x6e, 0x63, 0x71, 0xff, 0x77, 0x65, 0x74, 0xff, 0x83, 0x73, 0x87, 0xff, 0x88, 0x7d, 0x94, 0xff, 0x96, 0x89, 0x9f, 0xff, 0x96, 0x8b, 0x9f, 0xff, 0x93, 0x8b, 0xa3, 0xff, 0x9b, 0x92, 0xb6, 0xff, 0xa4, 0x9c, 0xc5, 0xff, 0xab, 0xa8, 0xcd, 0xff, 0xb0, 0xb0, 0xd4, 0xff, 0xaa, 0xad, 0xcf, 0xff, 0xa4, 0xa4, 0xc9, 0xff, 0xa0, 0x9d, 0xc8, 0xff, 0x9b, 0x9b, 0xc0, 0xff, 0x9c, 0xa6, 0xc2, 0xff, 0xa4, 0xab, 0xc4, 0xff, 0xa9, 0xab, 0xbf, 0xff, 0xa3, 0xa4, 0xb7, 0xff, 0x9b, 0x9d, 0xae, 0xff, 0x98, 0x9a, 0xa7, 0xff, 0x8e, 0x97, 0xa2, 0xff, 0x85, 0x94, 0xa3, 0xff, 0x84, 0x96, 0xa9, 0xff, 0x77, 0x8f, 0xaa, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa1, 0xa1, 0xb3, 0x4a, 0x93, 0x95, 0xa6, 0xff, 0x79, 0x7e, 0x8f, 0xff, 0x64, 0x6b, 0x79, 0xff, 0x60, 0x69, 0x75, 0xff, 0x63, 0x6d, 0x78, 0xff, 0x66, 0x6c, 0x73, 0xff, 0x6a, 0x6d, 0x70, 0xff, 0x69, 0x69, 0x6d, 0xff, 0x6f, 0x67, 0x73, 0xff, 0x7a, 0x6e, 0x80, 0xff, 0x83, 0x73, 0x85, 0xff, 0x8f, 0x7a, 0x8f, 0xff, 0x97, 0x83, 0x9c, 0xff, 0x9b, 0x8a, 0xa1, 0xff, 0x9d, 0x8e, 0xa2, 0xff, 0x9d, 0x93, 0xa4, 0xff, 0xa2, 0x96, 0xa6, 0xff, 0xa1, 0x96, 0xa6, 0xff, 0x96, 0x93, 0xa1, 0xff, 0x8d, 0x8c, 0x9c, 0xff, 0x8c, 0x8c, 0x9d, 0xff, 0x90, 0x93, 0xa1, 0xff, 0x93, 0x9a, 0xa5, 0xff, 0x9a, 0xa0, 0xa9, 0xff, 0x9c, 0xa1, 0xad, 0xff, 0x9a, 0x9f, 0xae, 0xff, 0x9a, 0xa0, 0xb1, 0xff, 0x99, 0xa1, 0xb2, 0xff, 0x96, 0x9e, 0xaf, 0xff, 0x91, 0x99, 0xab, 0xff, 0x8a, 0x94, 0xa9, 0xff, 0x83, 0x8f, 0xa7, 0xff, 0x76, 0x82, 0x9b, 0xff, 0x6f, 0x78, 0x92, 0xff, 0x71, 0x76, 0x91, 0xff, 0x78, 0x7b, 0x94, 0xff, 0x87, 0x85, 0x9c, 0xff, 0x99, 0x98, 0xaf, 0xff, 0x9b, 0x9f, 0xb7, 0xff, 0x44, 0x49, 0x61, 0xff, 0x27, 0x31, 0x46, 0xff, 0x1c, 0x27, 0x41, 0xff, 0x10, 0x1c, 0x3d, 0xff, 0x1d, 0x2c, 0x4c, 0xff, 0x22, 0x31, 0x4f, 0xff, 0x1e, 0x2c, 0x43, 0xff, 0x31, 0x3b, 0x61, 0xff, 0x58, 0x5c, 0x98, 0xff, 0x7c, 0x77, 0xce, 0xff, 0x7d, 0x7a, 0xce, 0xff, 0x62, 0x62, 0xb2, 0xff, 0x60, 0x61, 0xb4, 0xff, 0x65, 0x64, 0xbc, 0xff, 0x5f, 0x60, 0xb5, 0xff, 0x65, 0x65, 0xb3, 0xff, 0x69, 0x66, 0xba, 0xff, 0x6e, 0x68, 0xca, 0xff, 0x70, 0x6f, 0xcf, 0xff, 0x7d, 0x7e, 0xd3, 0xff, 0x90, 0x8e, 0xda, 0xff, 0x7f, 0x7f, 0xc2, 0xff, 0x50, 0x53, 0x89, 0xff, 0x49, 0x50, 0x7f, 0xff, 0x59, 0x63, 0x94, 0xff, 0x52, 0x64, 0x94, 0xff, 0x4e, 0x64, 0x9a, 0xff, 0x54, 0x6a, 0x9e, 0xff, 0x42, 0x58, 0x8c, 0xff, 0x3a, 0x4d, 0x7e, 0xff, 0x33, 0x44, 0x6b, 0xff, 0x3b, 0x48, 0x6b, 0xff, 0x4b, 0x54, 0x77, 0xff, 0x54, 0x5a, 0x7d, 0xff, 0x50, 0x58, 0x7a, 0xff, 0x4b, 0x54, 0x76, 0xff, 0x51, 0x58, 0x7a, 0xff, 0x4d, 0x56, 0x74, 0xff, 0x48, 0x54, 0x79, 0xff, 0x49, 0x5a, 0x89, 0xff, 0x4d, 0x65, 0x95, 0xff, 0x55, 0x6a, 0xa3, 0xff, 0x53, 0x69, 0xa4, 0xff, 0x63, 0x79, 0xaa, 0xff, 0x74, 0x86, 0xb2, 0xff, 0x72, 0x84, 0xae, 0xff, 0x86, 0x96, 0xbd, 0xff, 0x9c, 0xa8, 0xce, 0xff, 0xa1, 0xab, 0xcd, 0xff, 0xa4, 0xa9, 0xc7, 0xff, 0xa6, 0xa6, 0xc3, 0xff, 0xa5, 0xa1, 0xbb, 0xff, 0xa6, 0x9b, 0xb3, 0xff, 0xa1, 0x96, 0xac, 0xff, 0x9c, 0x8f, 0xa4, 0xff, 0x9c, 0x8b, 0xa2, 0xff, 0x92, 0x83, 0x97, 0xff, 0x7e, 0x6f, 0x80, 0xff, 0x86, 0x72, 0x81, 0xff, 0x91, 0x7d, 0x90, 0xff, 0x95, 0x83, 0x9a, 0xff, 0x9c, 0x8d, 0xa2, 0xff, 0x97, 0x8a, 0x9e, 0xff, 0x96, 0x8c, 0xa3, 0xff, 0xa2, 0x98, 0xb7, 0xff, 0xa8, 0x9f, 0xc4, 0xff, 0xac, 0xa7, 0xc9, 0xff, 0xae, 0xad, 0xd1, 0xff, 0xa8, 0xac, 0xcf, 0xff, 0xa8, 0xa8, 0xcd, 0xff, 0xa4, 0xa3, 0xce, 0xff, 0xa1, 0xa2, 0xc7, 0xff, 0xa6, 0xac, 0xca, 0xff, 0xae, 0xb4, 0xcd, 0xff, 0xad, 0xaf, 0xc3, 0xff, 0xa4, 0xa7, 0xb7, 0xff, 0x9b, 0xa0, 0xae, 0xff, 0x98, 0x9c, 0xa8, 0xff, 0x94, 0x9f, 0xa8, 0xff, 0x8b, 0x9c, 0xa8, 0xff, 0x84, 0x96, 0xae, 0xff, 0x7c, 0x90, 0xaf, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbf, 0x7f, 0xbf, 0x04, 0x97, 0x96, 0xaa, 0xe7, 0x7d, 0x80, 0x92, 0xff, 0x61, 0x69, 0x77, 0xff, 0x57, 0x61, 0x6d, 0xff, 0x61, 0x69, 0x73, 0xff, 0x67, 0x6a, 0x6d, 0xff, 0x6b, 0x6a, 0x68, 0xff, 0x6a, 0x64, 0x67, 0xff, 0x6c, 0x63, 0x6c, 0xff, 0x75, 0x68, 0x77, 0xff, 0x7e, 0x69, 0x7b, 0xff, 0x84, 0x6e, 0x84, 0xff, 0x8e, 0x76, 0x8f, 0xff, 0x90, 0x79, 0x93, 0xff, 0x90, 0x7c, 0x94, 0xff, 0x8f, 0x81, 0x94, 0xff, 0x92, 0x85, 0x97, 0xff, 0x93, 0x86, 0x99, 0xff, 0x8b, 0x86, 0x98, 0xff, 0x83, 0x83, 0x94, 0xff, 0x86, 0x85, 0x95, 0xff, 0x8e, 0x91, 0x9d, 0xff, 0x92, 0x99, 0xa0, 0xff, 0x93, 0x99, 0x9f, 0xff, 0x93, 0x98, 0xa1, 0xff, 0x97, 0x9b, 0xa8, 0xff, 0x98, 0x9d, 0xab, 0xff, 0x95, 0x9f, 0xaa, 0xff, 0x8e, 0x9b, 0xa6, 0xff, 0x88, 0x96, 0xa7, 0xff, 0x84, 0x93, 0xa6, 0xff, 0x81, 0x90, 0xa6, 0xff, 0x79, 0x84, 0x9d, 0xff, 0x6f, 0x76, 0x91, 0xff, 0x6d, 0x71, 0x8c, 0xff, 0x75, 0x79, 0x8f, 0xff, 0x85, 0x84, 0x98, 0xff, 0x8c, 0x8c, 0xa0, 0xff, 0x9f, 0xa4, 0xba, 0xff, 0x88, 0x8e, 0xa6, 0xff, 0x36, 0x41, 0x56, 0xff, 0x29, 0x34, 0x49, 0xff, 0x19, 0x23, 0x3d, 0xff, 0x17, 0x24, 0x40, 0xff, 0x21, 0x2f, 0x4f, 0xff, 0x26, 0x30, 0x53, 0xff, 0x20, 0x2a, 0x4b, 0xff, 0x26, 0x31, 0x51, 0xff, 0x41, 0x48, 0x7c, 0xff, 0x65, 0x66, 0xac, 0xff, 0x75, 0x6e, 0xc5, 0xff, 0x73, 0x70, 0xc9, 0xff, 0x73, 0x73, 0xc9, 0xff, 0x71, 0x70, 0xc6, 0xff, 0x68, 0x64, 0xc0, 0xff, 0x64, 0x61, 0xba, 0xff, 0x5d, 0x5b, 0xae, 0xff, 0x50, 0x50, 0x9a, 0xff, 0x46, 0x47, 0x89, 0xff, 0x42, 0x3f, 0x80, 0xff, 0x3b, 0x3a, 0x73, 0xff, 0x38, 0x3f, 0x6d, 0xff, 0x4d, 0x57, 0x85, 0xff, 0x50, 0x5f, 0x91, 0xff, 0x51, 0x67, 0x97, 0xff, 0x5a, 0x70, 0xa2, 0xff, 0x4d, 0x65, 0x9a, 0xff, 0x42, 0x5a, 0x89, 0xff, 0x38, 0x4b, 0x74, 0xff, 0x36, 0x44, 0x6a, 0xff, 0x46, 0x50, 0x72, 0xff, 0x4f, 0x57, 0x78, 0xff, 0x4f, 0x55, 0x78, 0xff, 0x4d, 0x55, 0x78, 0xff, 0x4f, 0x58, 0x7a, 0xff, 0x4c, 0x55, 0x77, 0xff, 0x46, 0x4f, 0x6f, 0xff, 0x48, 0x52, 0x7e, 0xff, 0x4c, 0x5f, 0x91, 0xff, 0x51, 0x69, 0x99, 0xff, 0x59, 0x6d, 0xa6, 0xff, 0x56, 0x6a, 0xa5, 0xff, 0x5f, 0x74, 0xa5, 0xff, 0x72, 0x84, 0xaf, 0xff, 0x71, 0x81, 0xaa, 0xff, 0x82, 0x90, 0xb8, 0xff, 0x95, 0xa1, 0xc9, 0xff, 0xa0, 0xa9, 0xcb, 0xff, 0xa7, 0xa9, 0xc6, 0xff, 0xa9, 0xa6, 0xc3, 0xff, 0xa9, 0xa3, 0xbc, 0xff, 0xad, 0xa0, 0xb7, 0xff, 0xaa, 0x9a, 0xb2, 0xff, 0xa7, 0x96, 0xac, 0xff, 0xaa, 0x94, 0xac, 0xff, 0x9d, 0x89, 0xa0, 0xff, 0x90, 0x7b, 0x90, 0xff, 0x96, 0x7e, 0x90, 0xff, 0xa1, 0x89, 0x9c, 0xff, 0xa3, 0x8d, 0xa2, 0xff, 0xa6, 0x93, 0xa8, 0xff, 0x9e, 0x90, 0xa2, 0xff, 0x9d, 0x91, 0xa7, 0xff, 0xa9, 0x9c, 0xb7, 0xff, 0xaf, 0xa4, 0xc3, 0xff, 0xb2, 0xab, 0xca, 0xff, 0xaf, 0xad, 0xce, 0xff, 0xaa, 0xac, 0xcf, 0xff, 0xa8, 0xaa, 0xcf, 0xff, 0xa4, 0xa3, 0xcf, 0xff, 0xa3, 0xa4, 0xc9, 0xff, 0xa8, 0xab, 0xc9, 0xff, 0xb2, 0xb4, 0xce, 0xff, 0xb3, 0xb4, 0xc8, 0xff, 0xaa, 0xad, 0xbb, 0xff, 0x9d, 0xa2, 0xae, 0xff, 0x98, 0x9a, 0xa8, 0xff, 0x95, 0x9d, 0xa9, 0xff, 0x8b, 0x9a, 0xa9, 0xff, 0x83, 0x92, 0xac, 0xe7, 0x66, 0x99, 0x99, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x91, 0x8f, 0xa2, 0x87, 0x7a, 0x7d, 0x8f, 0xff, 0x5d, 0x65, 0x74, 0xff, 0x4e, 0x58, 0x63, 0xff, 0x5b, 0x61, 0x67, 0xff, 0x63, 0x64, 0x66, 0xff, 0x66, 0x63, 0x60, 0xff, 0x68, 0x63, 0x62, 0xff, 0x6b, 0x61, 0x67, 0xff, 0x75, 0x64, 0x71, 0xff, 0x7b, 0x63, 0x75, 0xff, 0x80, 0x66, 0x7c, 0xff, 0x88, 0x6d, 0x85, 0xff, 0x84, 0x6a, 0x87, 0xff, 0x83, 0x6b, 0x87, 0xff, 0x87, 0x73, 0x8a, 0xff, 0x88, 0x77, 0x8d, 0xff, 0x87, 0x7a, 0x91, 0xff, 0x81, 0x7c, 0x94, 0xff, 0x82, 0x80, 0x92, 0xff, 0x8b, 0x87, 0x92, 0xff, 0x8e, 0x91, 0x99, 0xff, 0x93, 0x98, 0x9d, 0xff, 0x94, 0x98, 0x9c, 0xff, 0x96, 0x97, 0xa0, 0xff, 0x99, 0x9a, 0xa5, 0xff, 0x96, 0x9f, 0xa6, 0xff, 0x92, 0x9e, 0xa3, 0xff, 0x8a, 0x99, 0x9e, 0xff, 0x87, 0x94, 0xa1, 0xff, 0x80, 0x8f, 0x9f, 0xff, 0x79, 0x8a, 0x9c, 0xff, 0x73, 0x81, 0x95, 0xff, 0x6d, 0x76, 0x8c, 0xff, 0x6d, 0x74, 0x8a, 0xff, 0x76, 0x7c, 0x8f, 0xff, 0x82, 0x82, 0x95, 0xff, 0x87, 0x89, 0x99, 0xff, 0x91, 0x97, 0xac, 0xff, 0xa9, 0xb0, 0xc8, 0xff, 0x6e, 0x7b, 0x91, 0xff, 0x31, 0x3c, 0x52, 0xff, 0x28, 0x2f, 0x47, 0xff, 0x11, 0x1c, 0x37, 0xff, 0x1c, 0x28, 0x45, 0xff, 0x24, 0x30, 0x4e, 0xff, 0x25, 0x33, 0x4d, 0xff, 0x1e, 0x2c, 0x42, 0xff, 0x15, 0x20, 0x3a, 0xff, 0x1d, 0x25, 0x4b, 0xff, 0x29, 0x2e, 0x60, 0xff, 0x2c, 0x2e, 0x64, 0xff, 0x2d, 0x30, 0x65, 0xff, 0x33, 0x36, 0x6b, 0xff, 0x2f, 0x31, 0x69, 0xff, 0x29, 0x2d, 0x65, 0xff, 0x25, 0x2a, 0x60, 0xff, 0x26, 0x28, 0x56, 0xff, 0x2d, 0x2d, 0x57, 0xff, 0x39, 0x3d, 0x69, 0xff, 0x3d, 0x48, 0x6d, 0xff, 0x46, 0x53, 0x7c, 0xff, 0x4f, 0x5c, 0x90, 0xff, 0x50, 0x61, 0x91, 0xff, 0x51, 0x65, 0x95, 0xff, 0x4f, 0x64, 0x99, 0xff, 0x47, 0x5f, 0x95, 0xff, 0x44, 0x58, 0x86, 0xff, 0x3b, 0x4c, 0x73, 0xff, 0x3f, 0x4b, 0x6e, 0xff, 0x4c, 0x56, 0x78, 0xff, 0x51, 0x5a, 0x7c, 0xff, 0x51, 0x56, 0x7a, 0xff, 0x50, 0x57, 0x7a, 0xff, 0x51, 0x5a, 0x7c, 0xff, 0x4f, 0x57, 0x77, 0xff, 0x44, 0x4e, 0x6e, 0xff, 0x46, 0x53, 0x81, 0xff, 0x4e, 0x64, 0x9a, 0xff, 0x51, 0x6b, 0x9b, 0xff, 0x58, 0x6c, 0xa4, 0xff, 0x57, 0x6a, 0xa6, 0xff, 0x5f, 0x74, 0xa5, 0xff, 0x6f, 0x82, 0xad, 0xff, 0x72, 0x82, 0xad, 0xff, 0x7f, 0x8e, 0xb6, 0xff, 0x8f, 0x9b, 0xc3, 0xff, 0x9c, 0xa4, 0xc7, 0xff, 0xa1, 0xa5, 0xc2, 0xff, 0xa3, 0xa3, 0xbe, 0xff, 0xa7, 0xa3, 0xbb, 0xff, 0xaf, 0xa3, 0xba, 0xff, 0xb5, 0xa2, 0xba, 0xff, 0xb7, 0xa1, 0xb9, 0xff, 0xb3, 0x9b, 0xb4, 0xff, 0xa9, 0x90, 0xa7, 0xff, 0xa2, 0x86, 0x9c, 0xff, 0xab, 0x8b, 0x9f, 0xff, 0xb3, 0x97, 0xa8, 0xff, 0xad, 0x97, 0xa9, 0xff, 0xad, 0x9a, 0xab, 0xff, 0xaa, 0x9a, 0xab, 0xff, 0xa7, 0x99, 0xac, 0xff, 0xad, 0x9e, 0xb8, 0xff, 0xb6, 0xa9, 0xc7, 0xff, 0xb9, 0xae, 0xcc, 0xff, 0xb1, 0xad, 0xcc, 0xff, 0xac, 0xad, 0xcc, 0xff, 0xa7, 0xaa, 0xce, 0xff, 0xa3, 0xa2, 0xce, 0xff, 0xa1, 0xa2, 0xc6, 0xff, 0xa7, 0xa6, 0xc5, 0xff, 0xae, 0xac, 0xc7, 0xff, 0xb0, 0xae, 0xc1, 0xff, 0xac, 0xaa, 0xbb, 0xff, 0x9f, 0x9e, 0xb0, 0xff, 0x97, 0x95, 0xa7, 0xff, 0x95, 0x97, 0xa9, 0xff, 0x90, 0x9a, 0xad, 0xff, 0x89, 0x95, 0xab, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87, 0x8e, 0x9d, 0x22, 0x70, 0x76, 0x87, 0xfe, 0x56, 0x5e, 0x6e, 0xff, 0x49, 0x51, 0x5e, 0xff, 0x50, 0x55, 0x5c, 0xff, 0x5b, 0x5c, 0x5a, 0xff, 0x63, 0x5f, 0x5b, 0xff, 0x6c, 0x65, 0x64, 0xff, 0x70, 0x62, 0x68, 0xff, 0x74, 0x62, 0x6e, 0xff, 0x79, 0x61, 0x75, 0xff, 0x7b, 0x61, 0x7a, 0xff, 0x7c, 0x62, 0x7c, 0xff, 0x7b, 0x60, 0x7d, 0xff, 0x7c, 0x64, 0x81, 0xff, 0x80, 0x6a, 0x84, 0xff, 0x83, 0x71, 0x8a, 0xff, 0x82, 0x74, 0x8c, 0xff, 0x7d, 0x76, 0x8d, 0xff, 0x82, 0x7c, 0x8e, 0xff, 0x8d, 0x87, 0x93, 0xff, 0x8f, 0x8d, 0x94, 0xff, 0x93, 0x94, 0x96, 0xff, 0x9b, 0x9c, 0x9e, 0xff, 0x9c, 0x9e, 0xa1, 0xff, 0x9b, 0x9f, 0xa2, 0xff, 0x98, 0xa1, 0xa2, 0xff, 0x93, 0xa0, 0xa1, 0xff, 0x8b, 0x99, 0x9c, 0xff, 0x7f, 0x8e, 0x98, 0xff, 0x77, 0x87, 0x95, 0xff, 0x6e, 0x7f, 0x90, 0xff, 0x67, 0x74, 0x86, 0xff, 0x63, 0x6c, 0x7e, 0xff, 0x67, 0x6f, 0x81, 0xff, 0x74, 0x78, 0x8a, 0xff, 0x79, 0x7c, 0x8b, 0xff, 0x82, 0x85, 0x96, 0xff, 0x92, 0x95, 0xaa, 0xff, 0xa0, 0xa3, 0xba, 0xff, 0xa7, 0xac, 0xc2, 0xff, 0x66, 0x6b, 0x81, 0xff, 0x35, 0x3c, 0x50, 0xff, 0x19, 0x23, 0x3b, 0xff, 0x1a, 0x26, 0x3f, 0xff, 0x1f, 0x2a, 0x43, 0xff, 0x24, 0x30, 0x4c, 0xff, 0x27, 0x33, 0x53, 0xff, 0x21, 0x2f, 0x49, 0xff, 0x1b, 0x25, 0x40, 0xff, 0x1c, 0x22, 0x40, 0xff, 0x19, 0x20, 0x40, 0xff, 0x17, 0x21, 0x3f, 0xff, 0x1b, 0x25, 0x42, 0xff, 0x19, 0x25, 0x3f, 0xff, 0x1f, 0x2a, 0x47, 0xff, 0x27, 0x31, 0x53, 0xff, 0x2e, 0x34, 0x5c, 0xff, 0x32, 0x39, 0x62, 0xff, 0x3b, 0x47, 0x74, 0xff, 0x41, 0x50, 0x78, 0xff, 0x42, 0x51, 0x77, 0xff, 0x49, 0x58, 0x89, 0xff, 0x50, 0x62, 0x90, 0xff, 0x49, 0x5e, 0x8c, 0xff, 0x4a, 0x5f, 0x8f, 0xff, 0x47, 0x5d, 0x8e, 0xff, 0x40, 0x52, 0x81, 0xff, 0x3f, 0x4d, 0x76, 0xff, 0x46, 0x52, 0x75, 0xff, 0x4e, 0x59, 0x7b, 0xff, 0x52, 0x5c, 0x7f, 0xff, 0x53, 0x5c, 0x7e, 0xff, 0x51, 0x5a, 0x7c, 0xff, 0x4b, 0x55, 0x76, 0xff, 0x48, 0x52, 0x74, 0xff, 0x43, 0x50, 0x75, 0xff, 0x47, 0x57, 0x84, 0xff, 0x4c, 0x62, 0x99, 0xff, 0x51, 0x69, 0x9f, 0xff, 0x55, 0x69, 0xa2, 0xff, 0x54, 0x69, 0xa1, 0xff, 0x5c, 0x71, 0xa2, 0xff, 0x6d, 0x7e, 0xa9, 0xff, 0x6f, 0x7f, 0xa7, 0xff, 0x7a, 0x88, 0xad, 0xff, 0x87, 0x94, 0xba, 0xff, 0x92, 0x9b, 0xbd, 0xff, 0x97, 0x9b, 0xb9, 0xff, 0x9f, 0x9c, 0xb9, 0xff, 0xa5, 0x9e, 0xba, 0xff, 0xaf, 0xa1, 0xbb, 0xff, 0xb8, 0xa8, 0xbe, 0xff, 0xbb, 0xa9, 0xbf, 0xff, 0xb9, 0xa2, 0xb9, 0xff, 0xb8, 0x9a, 0xb1, 0xff, 0xb3, 0x94, 0xa9, 0xff, 0xb8, 0x98, 0xa9, 0xff, 0xba, 0x9d, 0xad, 0xff, 0xb3, 0x9a, 0xab, 0xff, 0xb4, 0x9c, 0xac, 0xff, 0xb2, 0x9d, 0xac, 0xff, 0xae, 0x9d, 0xae, 0xff, 0xb0, 0xa1, 0xb6, 0xff, 0xb9, 0xab, 0xc4, 0xff, 0xb7, 0xab, 0xc7, 0xff, 0xb1, 0xab, 0xca, 0xff, 0xae, 0xac, 0xce, 0xff, 0xa9, 0xaa, 0xd0, 0xff, 0xa4, 0xa4, 0xce, 0xff, 0xa2, 0xa6, 0xcb, 0xff, 0xa9, 0xaa, 0xc6, 0xff, 0xac, 0xaa, 0xc3, 0xff, 0xac, 0xa8, 0xbf, 0xff, 0xa7, 0xa4, 0xb9, 0xff, 0x9e, 0x98, 0xac, 0xff, 0x95, 0x90, 0xa3, 0xff, 0x94, 0x94, 0xaa, 0xff, 0x93, 0x98, 0xaf, 0xfe, 0x8a, 0x99, 0xa7, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x6e, 0x82, 0xb4, 0x51, 0x58, 0x68, 0xff, 0x40, 0x46, 0x56, 0xff, 0x45, 0x4a, 0x54, 0xff, 0x4f, 0x53, 0x4d, 0xff, 0x5d, 0x5a, 0x55, 0xff, 0x6a, 0x61, 0x61, 0xff, 0x6e, 0x60, 0x67, 0xff, 0x70, 0x5e, 0x6b, 0xff, 0x74, 0x5f, 0x73, 0xff, 0x73, 0x5b, 0x74, 0xff, 0x6f, 0x56, 0x71, 0xff, 0x73, 0x5a, 0x74, 0xff, 0x7b, 0x64, 0x7d, 0xff, 0x7f, 0x6b, 0x84, 0xff, 0x85, 0x74, 0x8c, 0xff, 0x83, 0x77, 0x8c, 0xff, 0x81, 0x77, 0x88, 0xff, 0x80, 0x78, 0x87, 0xff, 0x88, 0x81, 0x8d, 0xff, 0x92, 0x8d, 0x91, 0xff, 0x96, 0x93, 0x93, 0xff, 0x9b, 0x9b, 0x9a, 0xff, 0x9c, 0x9f, 0x9f, 0xff, 0x9c, 0xa3, 0x9f, 0xff, 0x9a, 0xa3, 0xa0, 0xff, 0x96, 0xa2, 0xa1, 0xff, 0x8e, 0x9b, 0x9d, 0xff, 0x7c, 0x8e, 0x94, 0xff, 0x76, 0x88, 0x91, 0xff, 0x6c, 0x7c, 0x89, 0xff, 0x5f, 0x6b, 0x78, 0xff, 0x54, 0x5d, 0x6a, 0xff, 0x5b, 0x61, 0x6f, 0xff, 0x6b, 0x6e, 0x7a, 0xff, 0x72, 0x74, 0x81, 0xff, 0x84, 0x84, 0x96, 0xff, 0x98, 0x97, 0xab, 0xff, 0x9d, 0x9c, 0xaf, 0xff, 0xab, 0xa9, 0xbc, 0xff, 0xa5, 0xa5, 0xb8, 0xff, 0x5c, 0x64, 0x75, 0xff, 0x23, 0x2c, 0x40, 0xff, 0x15, 0x1f, 0x37, 0xff, 0x1f, 0x27, 0x44, 0xff, 0x22, 0x2b, 0x4a, 0xff, 0x20, 0x2c, 0x4c, 0xff, 0x25, 0x32, 0x50, 0xff, 0x27, 0x30, 0x50, 0xff, 0x24, 0x29, 0x4d, 0xff, 0x1e, 0x27, 0x4a, 0xff, 0x1c, 0x28, 0x47, 0xff, 0x19, 0x27, 0x41, 0xff, 0x17, 0x23, 0x40, 0xff, 0x1b, 0x26, 0x47, 0xff, 0x27, 0x31, 0x53, 0xff, 0x2c, 0x36, 0x5e, 0xff, 0x2b, 0x36, 0x64, 0xff, 0x34, 0x41, 0x72, 0xff, 0x3f, 0x4d, 0x7c, 0xff, 0x44, 0x53, 0x7d, 0xff, 0x42, 0x54, 0x81, 0xff, 0x42, 0x55, 0x85, 0xff, 0x41, 0x55, 0x83, 0xff, 0x4a, 0x5e, 0x8b, 0xff, 0x48, 0x5c, 0x8b, 0xff, 0x3f, 0x51, 0x81, 0xff, 0x45, 0x53, 0x7e, 0xff, 0x4e, 0x59, 0x7f, 0xff, 0x52, 0x5c, 0x7e, 0xff, 0x52, 0x5c, 0x7e, 0xff, 0x4d, 0x58, 0x7a, 0xff, 0x4f, 0x59, 0x78, 0xff, 0x4b, 0x55, 0x76, 0xff, 0x44, 0x51, 0x74, 0xff, 0x42, 0x52, 0x7a, 0xff, 0x46, 0x58, 0x84, 0xff, 0x47, 0x5d, 0x94, 0xff, 0x50, 0x65, 0xa1, 0xff, 0x56, 0x6a, 0xa4, 0xff, 0x53, 0x69, 0x9f, 0xff, 0x58, 0x6c, 0x9e, 0xff, 0x6a, 0x79, 0xa5, 0xff, 0x67, 0x77, 0x9d, 0xff, 0x6d, 0x7c, 0x9e, 0xff, 0x7d, 0x8b, 0xaf, 0xff, 0x87, 0x91, 0xb3, 0xff, 0x90, 0x93, 0xb3, 0xff, 0x9c, 0x97, 0xb6, 0xff, 0xa6, 0x9c, 0xba, 0xff, 0xb1, 0xa1, 0xbd, 0xff, 0xb4, 0xa8, 0xbd, 0xff, 0xb9, 0xab, 0xbe, 0xff, 0xbf, 0xa9, 0xbf, 0xff, 0xc4, 0xa7, 0xbd, 0xff, 0xbe, 0xa1, 0xb4, 0xff, 0xb9, 0x9f, 0xad, 0xff, 0xbb, 0xa2, 0xb0, 0xff, 0xba, 0xa0, 0xaf, 0xff, 0xbc, 0xa0, 0xaf, 0xff, 0xb6, 0x9e, 0xac, 0xff, 0xad, 0x9b, 0xa9, 0xff, 0xaf, 0x9f, 0xae, 0xff, 0xb4, 0xa6, 0xb8, 0xff, 0xaf, 0xa5, 0xbb, 0xff, 0xae, 0xa8, 0xc4, 0xff, 0xae, 0xab, 0xcd, 0xff, 0xa9, 0xa7, 0xcc, 0xff, 0xa4, 0xa6, 0xc9, 0xff, 0xa3, 0xa8, 0xcb, 0xff, 0xa5, 0xa9, 0xc1, 0xff, 0xa8, 0xa7, 0xbe, 0xff, 0xa8, 0xa4, 0xbf, 0xff, 0xa2, 0xa0, 0xb6, 0xff, 0x9f, 0x95, 0xa9, 0xff, 0x94, 0x8d, 0xa2, 0xff, 0x91, 0x92, 0xa9, 0xff, 0x92, 0x95, 0xae, 0xb5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x6b, 0x7f, 0x40, 0x4e, 0x56, 0x66, 0xff, 0x3b, 0x42, 0x51, 0xff, 0x41, 0x47, 0x4e, 0xff, 0x49, 0x4e, 0x47, 0xff, 0x55, 0x52, 0x4c, 0xff, 0x62, 0x58, 0x59, 0xff, 0x68, 0x5a, 0x61, 0xff, 0x6c, 0x59, 0x67, 0xff, 0x71, 0x5b, 0x6d, 0xff, 0x6f, 0x59, 0x6d, 0xff, 0x73, 0x5b, 0x73, 0xff, 0x7c, 0x63, 0x7b, 0xff, 0x84, 0x6e, 0x85, 0xff, 0x88, 0x76, 0x8d, 0xff, 0x8e, 0x7f, 0x93, 0xff, 0x8d, 0x83, 0x91, 0xff, 0x89, 0x84, 0x8f, 0xff, 0x83, 0x7f, 0x88, 0xff, 0x85, 0x82, 0x86, 0xff, 0x8d, 0x8b, 0x87, 0xff, 0x8e, 0x8f, 0x87, 0xff, 0x97, 0x98, 0x95, 0xff, 0x9f, 0xa2, 0x9e, 0xff, 0x9c, 0xa3, 0x9e, 0xff, 0x99, 0xa4, 0x9d, 0xff, 0x95, 0xa2, 0x9f, 0xff, 0x8e, 0x9d, 0x9c, 0xff, 0x85, 0x95, 0x96, 0xff, 0x79, 0x89, 0x8e, 0xff, 0x6a, 0x79, 0x7f, 0xff, 0x5a, 0x66, 0x6d, 0xff, 0x50, 0x58, 0x5f, 0xff, 0x52, 0x58, 0x5e, 0xff, 0x60, 0x60, 0x69, 0xff, 0x72, 0x70, 0x7c, 0xff, 0x8a, 0x88, 0x97, 0xff, 0x9b, 0x98, 0xa9, 0xff, 0xa0, 0x9d, 0xaf, 0xff, 0xa3, 0x9e, 0xb1, 0xff, 0xac, 0xac, 0xbc, 0xff, 0x98, 0xa0, 0xaf, 0xff, 0x49, 0x4f, 0x61, 0xff, 0x14, 0x1c, 0x31, 0xff, 0x1b, 0x26, 0x3e, 0xff, 0x20, 0x2b, 0x45, 0xff, 0x1b, 0x27, 0x44, 0xff, 0x1e, 0x28, 0x49, 0xff, 0x1d, 0x28, 0x4c, 0xff, 0x1e, 0x2c, 0x52, 0xff, 0x1e, 0x2b, 0x56, 0xff, 0x19, 0x26, 0x4f, 0xff, 0x17, 0x26, 0x49, 0xff, 0x18, 0x27, 0x4b, 0xff, 0x1d, 0x29, 0x51, 0xff, 0x24, 0x30, 0x5a, 0xff, 0x2d, 0x3a, 0x65, 0xff, 0x36, 0x43, 0x72, 0xff, 0x3d, 0x4c, 0x7f, 0xff, 0x40, 0x50, 0x80, 0xff, 0x46, 0x57, 0x83, 0xff, 0x42, 0x57, 0x86, 0xff, 0x3e, 0x53, 0x86, 0xff, 0x44, 0x57, 0x87, 0xff, 0x4b, 0x5e, 0x90, 0xff, 0x4b, 0x5f, 0x91, 0xff, 0x47, 0x5a, 0x89, 0xff, 0x4b, 0x59, 0x85, 0xff, 0x4f, 0x5b, 0x82, 0xff, 0x56, 0x60, 0x7f, 0xff, 0x54, 0x5d, 0x7b, 0xff, 0x4c, 0x54, 0x74, 0xff, 0x4a, 0x54, 0x73, 0xff, 0x46, 0x51, 0x72, 0xff, 0x43, 0x50, 0x73, 0xff, 0x41, 0x51, 0x77, 0xff, 0x44, 0x56, 0x83, 0xff, 0x45, 0x5b, 0x92, 0xff, 0x4e, 0x63, 0x9f, 0xff, 0x58, 0x6b, 0xa5, 0xff, 0x56, 0x6d, 0xa3, 0xff, 0x5b, 0x6f, 0xa0, 0xff, 0x68, 0x77, 0xa3, 0xff, 0x62, 0x72, 0x99, 0xff, 0x63, 0x73, 0x96, 0xff, 0x75, 0x83, 0xa8, 0xff, 0x80, 0x89, 0xac, 0xff, 0x8d, 0x8e, 0xb0, 0xff, 0x9a, 0x95, 0xb4, 0xff, 0xa6, 0x9c, 0xba, 0xff, 0xb3, 0xa4, 0xc0, 0xff, 0xb8, 0xac, 0xc2, 0xff, 0xc0, 0xb1, 0xc5, 0xff, 0xc9, 0xb2, 0xc9, 0xff, 0xcb, 0xaf, 0xc5, 0xff, 0xc5, 0xab, 0xbe, 0xff, 0xc0, 0xa8, 0xb7, 0xff, 0xc1, 0xa9, 0xb6, 0xff, 0xbe, 0xa6, 0xb1, 0xff, 0xb8, 0x9f, 0xad, 0xff, 0xb3, 0x9d, 0xa6, 0xff, 0xad, 0x9a, 0xa0, 0xff, 0xaa, 0x98, 0xa2, 0xff, 0xac, 0x9d, 0xa9, 0xff, 0xa6, 0x9c, 0xa8, 0xff, 0xa4, 0x9d, 0xb0, 0xff, 0xa1, 0x9e, 0xb7, 0xff, 0x9f, 0x9d, 0xb8, 0xff, 0xa0, 0xa2, 0xbc, 0xff, 0x9e, 0xa2, 0xbc, 0xff, 0x9a, 0x9d, 0xb5, 0xff, 0x9e, 0x9c, 0xb5, 0xff, 0xa1, 0x9d, 0xb7, 0xff, 0x9e, 0x9c, 0xb1, 0xff, 0x9c, 0x92, 0xa7, 0xff, 0x8e, 0x87, 0x9b, 0xff, 0x88, 0x8a, 0xa1, 0xff, 0x8b, 0x8f, 0xa7, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4e, 0x55, 0x66, 0xcd, 0x3d, 0x41, 0x52, 0xff, 0x45, 0x47, 0x54, 0xff, 0x4d, 0x4f, 0x4e, 0xff, 0x53, 0x50, 0x4c, 0xff, 0x5a, 0x52, 0x52, 0xff, 0x60, 0x53, 0x59, 0xff, 0x66, 0x56, 0x60, 0xff, 0x70, 0x5c, 0x6b, 0xff, 0x75, 0x60, 0x71, 0xff, 0x7e, 0x68, 0x7c, 0xff, 0x85, 0x6e, 0x84, 0xff, 0x87, 0x73, 0x88, 0xff, 0x8b, 0x78, 0x8f, 0xff, 0x8e, 0x80, 0x8f, 0xff, 0x8d, 0x84, 0x8c, 0xff, 0x8e, 0x89, 0x8e, 0xff, 0x90, 0x8c, 0x8e, 0xff, 0x90, 0x8b, 0x8b, 0xff, 0x89, 0x88, 0x82, 0xff, 0x87, 0x88, 0x80, 0xff, 0x95, 0x96, 0x92, 0xff, 0x9f, 0xa3, 0x9e, 0xff, 0x9f, 0xa7, 0xa1, 0xff, 0x9b, 0xa6, 0xa0, 0xff, 0x97, 0xa3, 0x9f, 0xff, 0x93, 0xa0, 0x9d, 0xff, 0x87, 0x97, 0x92, 0xff, 0x71, 0x80, 0x7f, 0xff, 0x62, 0x6e, 0x72, 0xff, 0x55, 0x60, 0x63, 0xff, 0x4e, 0x56, 0x5a, 0xff, 0x4a, 0x4e, 0x54, 0xff, 0x50, 0x52, 0x58, 0xff, 0x6c, 0x6b, 0x72, 0xff, 0x88, 0x86, 0x92, 0xff, 0x97, 0x95, 0xa3, 0xff, 0x9e, 0x9b, 0xa9, 0xff, 0xa2, 0x9f, 0xad, 0xff, 0x9e, 0x9b, 0xa9, 0xff, 0xa7, 0xa7, 0xb6, 0xff, 0x87, 0x8d, 0x9c, 0xff, 0x28, 0x31, 0x43, 0xff, 0x19, 0x23, 0x3a, 0xff, 0x1c, 0x28, 0x42, 0xff, 0x1a, 0x28, 0x43, 0xff, 0x21, 0x2a, 0x4c, 0xff, 0x20, 0x2b, 0x51, 0xff, 0x20, 0x30, 0x57, 0xff, 0x22, 0x2f, 0x5a, 0xff, 0x1f, 0x2b, 0x55, 0xff, 0x23, 0x30, 0x57, 0xff, 0x2d, 0x3b, 0x65, 0xff, 0x34, 0x40, 0x6f, 0xff, 0x36, 0x41, 0x73, 0xff, 0x3d, 0x49, 0x7c, 0xff, 0x48, 0x56, 0x88, 0xff, 0x4e, 0x5f, 0x94, 0xff, 0x4d, 0x5f, 0x93, 0xff, 0x50, 0x63, 0x95, 0xff, 0x4c, 0x62, 0x96, 0xff, 0x44, 0x59, 0x8f, 0xff, 0x4a, 0x5d, 0x94, 0xff, 0x51, 0x64, 0x9a, 0xff, 0x51, 0x64, 0x99, 0xff, 0x50, 0x63, 0x94, 0xff, 0x51, 0x60, 0x8a, 0xff, 0x4f, 0x59, 0x7c, 0xff, 0x51, 0x59, 0x76, 0xff, 0x52, 0x59, 0x75, 0xff, 0x4b, 0x52, 0x6e, 0xff, 0x48, 0x50, 0x6f, 0xff, 0x45, 0x4e, 0x70, 0xff, 0x41, 0x4c, 0x70, 0xff, 0x3d, 0x4d, 0x72, 0xff, 0x41, 0x53, 0x7f, 0xff, 0x46, 0x5c, 0x91, 0xff, 0x4e, 0x64, 0x9d, 0xff, 0x53, 0x67, 0xa0, 0xff, 0x54, 0x6a, 0xa0, 0xff, 0x58, 0x6c, 0xa0, 0xff, 0x67, 0x79, 0xa7, 0xff, 0x68, 0x78, 0x9d, 0xff, 0x69, 0x76, 0x99, 0xff, 0x73, 0x7f, 0xa3, 0xff, 0x7b, 0x85, 0xa6, 0xff, 0x88, 0x8c, 0xac, 0xff, 0x97, 0x94, 0xb3, 0xff, 0xa6, 0x9c, 0xb9, 0xff, 0xb5, 0xa5, 0xbf, 0xff, 0xbe, 0xb0, 0xc6, 0xff, 0xc8, 0xb7, 0xcc, 0xff, 0xce, 0xb8, 0xcd, 0xff, 0xcc, 0xb6, 0xc9, 0xff, 0xc9, 0xb5, 0xc5, 0xff, 0xc9, 0xb4, 0xc4, 0xff, 0xc8, 0xb1, 0xbd, 0xff, 0xc1, 0xaa, 0xb3, 0xff, 0xbb, 0xa3, 0xad, 0xff, 0xb5, 0x9d, 0xa4, 0xff, 0xac, 0x98, 0x9c, 0xff, 0xa5, 0x94, 0x9a, 0xff, 0xa5, 0x97, 0x9c, 0xff, 0xa2, 0x96, 0x9d, 0xff, 0x9e, 0x96, 0xa1, 0xff, 0x94, 0x91, 0xa0, 0xff, 0x8e, 0x8e, 0xa0, 0xff, 0x93, 0x94, 0xa6, 0xff, 0x92, 0x97, 0xaa, 0xff, 0x93, 0x95, 0xab, 0xff, 0x97, 0x95, 0xad, 0xff, 0x98, 0x94, 0xae, 0xff, 0x96, 0x93, 0xa9, 0xff, 0x94, 0x8d, 0xa2, 0xff, 0x86, 0x80, 0x95, 0xff, 0x80, 0x80, 0x96, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x51, 0x58, 0x64, 0x51, 0x45, 0x46, 0x55, 0xff, 0x4f, 0x4a, 0x5d, 0xff, 0x56, 0x52, 0x5a, 0xff, 0x54, 0x52, 0x50, 0xff, 0x55, 0x4f, 0x4f, 0xff, 0x59, 0x4f, 0x52, 0xff, 0x5f, 0x52, 0x57, 0xff, 0x6a, 0x59, 0x64, 0xff, 0x79, 0x66, 0x74, 0xff, 0x83, 0x70, 0x81, 0xff, 0x85, 0x73, 0x83, 0xff, 0x82, 0x71, 0x81, 0xff, 0x88, 0x75, 0x87, 0xff, 0x8b, 0x7d, 0x87, 0xff, 0x8a, 0x80, 0x84, 0xff, 0x8f, 0x85, 0x86, 0xff, 0x9a, 0x8f, 0x8f, 0xff, 0x99, 0x92, 0x91, 0xff, 0x8e, 0x8b, 0x8a, 0xff, 0x8c, 0x8b, 0x8b, 0xff, 0x95, 0x96, 0x94, 0xff, 0x9f, 0xa1, 0xa0, 0xff, 0xa2, 0xa8, 0xa6, 0xff, 0x9f, 0xa7, 0xa4, 0xff, 0x9f, 0xa6, 0xa3, 0xff, 0x98, 0xa0, 0x9c, 0xff, 0x83, 0x8f, 0x8a, 0xff, 0x6c, 0x77, 0x74, 0xff, 0x5b, 0x66, 0x67, 0xff, 0x53, 0x5d, 0x5f, 0xff, 0x4d, 0x53, 0x58, 0xff, 0x49, 0x4a, 0x55, 0xff, 0x4d, 0x51, 0x56, 0xff, 0x69, 0x68, 0x6b, 0xff, 0x80, 0x7e, 0x87, 0xff, 0x8e, 0x8f, 0x9a, 0xff, 0x9c, 0x99, 0xa5, 0xff, 0xa0, 0x9f, 0xa9, 0xff, 0xa6, 0xa2, 0xaf, 0xff, 0xab, 0xa4, 0xb6, 0xff, 0xb0, 0xb4, 0xc2, 0xff, 0x69, 0x73, 0x7f, 0xff, 0x1a, 0x21, 0x36, 0xff, 0x1c, 0x27, 0x46, 0xff, 0x20, 0x31, 0x4d, 0xff, 0x29, 0x33, 0x57, 0xff, 0x28, 0x32, 0x59, 0xff, 0x24, 0x31, 0x5a, 0xff, 0x26, 0x33, 0x5c, 0xff, 0x2b, 0x38, 0x60, 0xff, 0x35, 0x42, 0x6a, 0xff, 0x3e, 0x4a, 0x79, 0xff, 0x41, 0x4d, 0x82, 0xff, 0x47, 0x51, 0x89, 0xff, 0x4d, 0x58, 0x90, 0xff, 0x4c, 0x5a, 0x8f, 0xff, 0x5d, 0x6f, 0xa3, 0xff, 0x65, 0x79, 0xaf, 0xff, 0x62, 0x77, 0xb0, 0xff, 0x55, 0x69, 0xa0, 0xff, 0x4e, 0x62, 0x9a, 0xff, 0x53, 0x68, 0xa1, 0xff, 0x50, 0x61, 0x97, 0xff, 0x4e, 0x5f, 0x91, 0xff, 0x56, 0x65, 0x95, 0xff, 0x53, 0x5f, 0x86, 0xff, 0x4d, 0x55, 0x72, 0xff, 0x4e, 0x54, 0x70, 0xff, 0x4f, 0x55, 0x72, 0xff, 0x4d, 0x52, 0x6f, 0xff, 0x49, 0x50, 0x6f, 0xff, 0x42, 0x4a, 0x6b, 0xff, 0x3f, 0x47, 0x6c, 0xff, 0x3d, 0x4c, 0x71, 0xff, 0x40, 0x53, 0x7c, 0xff, 0x44, 0x5c, 0x8f, 0xff, 0x4d, 0x64, 0x9b, 0xff, 0x50, 0x64, 0x9b, 0xff, 0x53, 0x68, 0x9f, 0xff, 0x55, 0x6b, 0xa4, 0xff, 0x5f, 0x77, 0xa7, 0xff, 0x6c, 0x7e, 0xa1, 0xff, 0x6b, 0x75, 0x99, 0xff, 0x72, 0x7b, 0x9d, 0xff, 0x7b, 0x85, 0xa5, 0xff, 0x86, 0x8f, 0xad, 0xff, 0x96, 0x96, 0xb5, 0xff, 0xa8, 0xa0, 0xba, 0xff, 0xbb, 0xaa, 0xc0, 0xff, 0xc5, 0xb4, 0xca, 0xff, 0xd0, 0xbf, 0xd2, 0xff, 0xd3, 0xbf, 0xcf, 0xff, 0xcf, 0xc0, 0xcd, 0xff, 0xcd, 0xbd, 0xca, 0xff, 0xd0, 0xbb, 0xcb, 0xff, 0xcb, 0xb5, 0xc3, 0xff, 0xc3, 0xad, 0xb7, 0xff, 0xc0, 0xa8, 0xad, 0xff, 0xb9, 0xa1, 0xa8, 0xff, 0xac, 0x98, 0xa1, 0xff, 0xa1, 0x92, 0x94, 0xff, 0x9c, 0x8f, 0x91, 0xff, 0x9e, 0x90, 0x96, 0xff, 0xa0, 0x95, 0x9d, 0xff, 0x97, 0x92, 0x9c, 0xff, 0x8e, 0x8e, 0x9a, 0xff, 0x8b, 0x8d, 0x9b, 0xff, 0x8c, 0x8f, 0xa1, 0xff, 0x90, 0x90, 0xa3, 0xff, 0x92, 0x90, 0xa6, 0xff, 0x91, 0x8e, 0xa9, 0xff, 0x90, 0x8d, 0xa4, 0xff, 0x8c, 0x89, 0x9f, 0xff, 0x81, 0x7d, 0x93, 0xff, 0x7a, 0x77, 0x8d, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x4f, 0x50, 0x5a, 0xce, 0x53, 0x54, 0x5b, 0xff, 0x57, 0x56, 0x58, 0xff, 0x59, 0x55, 0x54, 0xff, 0x57, 0x50, 0x51, 0xff, 0x58, 0x4f, 0x51, 0xff, 0x5a, 0x4c, 0x54, 0xff, 0x66, 0x55, 0x5e, 0xff, 0x7b, 0x69, 0x74, 0xff, 0x87, 0x74, 0x84, 0xff, 0x86, 0x76, 0x82, 0xff, 0x82, 0x73, 0x7d, 0xff, 0x84, 0x75, 0x80, 0xff, 0x87, 0x79, 0x80, 0xff, 0x89, 0x7c, 0x7e, 0xff, 0x8d, 0x80, 0x7e, 0xff, 0x95, 0x8a, 0x89, 0xff, 0x9a, 0x94, 0x94, 0xff, 0x96, 0x93, 0x92, 0xff, 0x91, 0x92, 0x90, 0xff, 0x95, 0x97, 0x95, 0xff, 0x9c, 0xa0, 0x9e, 0xff, 0x9d, 0xa3, 0xa1, 0xff, 0xa0, 0xa4, 0xa2, 0xff, 0xa1, 0xa5, 0xa3, 0xff, 0x95, 0x9a, 0x98, 0xff, 0x81, 0x87, 0x86, 0xff, 0x6e, 0x75, 0x71, 0xff, 0x61, 0x68, 0x62, 0xff, 0x57, 0x5e, 0x5c, 0xff, 0x53, 0x57, 0x59, 0xff, 0x54, 0x54, 0x5a, 0xff, 0x5e, 0x5e, 0x64, 0xff, 0x76, 0x6f, 0x75, 0xff, 0x86, 0x7e, 0x89, 0xff, 0x92, 0x91, 0x9d, 0xff, 0xa1, 0x9f, 0xac, 0xff, 0xab, 0xa9, 0xb5, 0xff, 0xb1, 0xaf, 0xbb, 0xff, 0xb0, 0xae, 0xbb, 0xff, 0xb7, 0xb6, 0xc3, 0xff, 0xb2, 0xb5, 0xbe, 0xff, 0x44, 0x4c, 0x59, 0xff, 0x16, 0x1e, 0x3c, 0xff, 0x26, 0x32, 0x52, 0xff, 0x2b, 0x36, 0x5d, 0xff, 0x2d, 0x3b, 0x65, 0xff, 0x2c, 0x3c, 0x68, 0xff, 0x2b, 0x3b, 0x68, 0xff, 0x31, 0x41, 0x6c, 0xff, 0x33, 0x43, 0x6d, 0xff, 0x38, 0x48, 0x76, 0xff, 0x40, 0x4f, 0x82, 0xff, 0x46, 0x54, 0x8a, 0xff, 0x51, 0x5f, 0x95, 0xff, 0x55, 0x67, 0x9a, 0xff, 0x61, 0x77, 0xaa, 0xff, 0x68, 0x7f, 0xb3, 0xff, 0x6b, 0x80, 0xb8, 0xff, 0x5c, 0x71, 0xa9, 0xff, 0x58, 0x6d, 0xa2, 0xff, 0x53, 0x66, 0x97, 0xff, 0x4c, 0x5e, 0x8e, 0xff, 0x50, 0x5f, 0x8c, 0xff, 0x5c, 0x64, 0x89, 0xff, 0x51, 0x58, 0x79, 0xff, 0x47, 0x4e, 0x6b, 0xff, 0x4f, 0x55, 0x72, 0xff, 0x48, 0x4e, 0x6b, 0xff, 0x41, 0x47, 0x63, 0xff, 0x3f, 0x46, 0x66, 0xff, 0x41, 0x4a, 0x6c, 0xff, 0x3f, 0x48, 0x6d, 0xff, 0x37, 0x45, 0x6a, 0xff, 0x3d, 0x50, 0x79, 0xff, 0x45, 0x5e, 0x90, 0xff, 0x4a, 0x61, 0x99, 0xff, 0x4b, 0x5f, 0x97, 0xff, 0x50, 0x66, 0x9d, 0xff, 0x54, 0x6a, 0xa3, 0xff, 0x5b, 0x72, 0xa0, 0xff, 0x6f, 0x80, 0xa3, 0xff, 0x6e, 0x78, 0x9d, 0xff, 0x72, 0x7b, 0x9d, 0xff, 0x80, 0x88, 0xa9, 0xff, 0x8d, 0x95, 0xb3, 0xff, 0x9b, 0x9b, 0xb7, 0xff, 0xae, 0xa7, 0xbe, 0xff, 0xc3, 0xb3, 0xc6, 0xff, 0xcb, 0xbc, 0xce, 0xff, 0xd5, 0xc4, 0xd5, 0xff, 0xd9, 0xc5, 0xd2, 0xff, 0xd5, 0xc6, 0xd3, 0xff, 0xd3, 0xc2, 0xd1, 0xff, 0xd6, 0xc1, 0xd1, 0xff, 0xd1, 0xbb, 0xc9, 0xff, 0xc8, 0xb2, 0xbc, 0xff, 0xc3, 0xab, 0xb2, 0xff, 0xbb, 0xa3, 0xa9, 0xff, 0xac, 0x99, 0x9d, 0xff, 0x9d, 0x8e, 0x8e, 0xff, 0x96, 0x89, 0x89, 0xff, 0x97, 0x8b, 0x8e, 0xff, 0x9b, 0x92, 0x97, 0xff, 0x9a, 0x98, 0x9f, 0xff, 0x93, 0x95, 0x9f, 0xff, 0x8d, 0x8f, 0x9c, 0xff, 0x89, 0x8e, 0x9d, 0xff, 0x8e, 0x8e, 0xa1, 0xff, 0x8e, 0x8a, 0xa2, 0xff, 0x90, 0x8c, 0xa7, 0xff, 0x8d, 0x8c, 0xa2, 0xff, 0x84, 0x85, 0x98, 0xff, 0x7b, 0x7b, 0x8f, 0xce, 0x00, 0x00, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x53, 0x57, 0x5e, 0x46, 0x5a, 0x5c, 0x5c, 0xff, 0x59, 0x58, 0x59, 0xff, 0x5a, 0x57, 0x58, 0xff, 0x58, 0x51, 0x52, 0xff, 0x56, 0x4c, 0x4f, 0xff, 0x57, 0x49, 0x4f, 0xff, 0x63, 0x54, 0x5a, 0xff, 0x7a, 0x6a, 0x71, 0xff, 0x86, 0x75, 0x7f, 0xff, 0x85, 0x76, 0x7c, 0xff, 0x80, 0x71, 0x75, 0xff, 0x7f, 0x70, 0x75, 0xff, 0x84, 0x76, 0x78, 0xff, 0x87, 0x78, 0x79, 0xff, 0x89, 0x7b, 0x78, 0xff, 0x91, 0x88, 0x86, 0xff, 0x9b, 0x96, 0x96, 0xff, 0x9d, 0x9a, 0x99, 0xff, 0x9b, 0x9a, 0x98, 0xff, 0x9b, 0x9c, 0x99, 0xff, 0x9f, 0xa3, 0xa0, 0xff, 0xa0, 0xa5, 0xa2, 0xff, 0x9e, 0xa1, 0x9e, 0xff, 0x99, 0x9c, 0x99, 0xff, 0x93, 0x95, 0x93, 0xff, 0x88, 0x89, 0x88, 0xff, 0x76, 0x78, 0x73, 0xff, 0x6b, 0x6f, 0x65, 0xff, 0x61, 0x66, 0x62, 0xff, 0x5e, 0x61, 0x61, 0xff, 0x64, 0x62, 0x65, 0xff, 0x6f, 0x6e, 0x71, 0xff, 0x83, 0x7d, 0x82, 0xff, 0x94, 0x8c, 0x95, 0xff, 0xa1, 0x9e, 0xaa, 0xff, 0xad, 0xac, 0xb8, 0xff, 0xb7, 0xb6, 0xc1, 0xff, 0xbc, 0xbb, 0xc6, 0xff, 0xbc, 0xbb, 0xc5, 0xff, 0xbb, 0xb7, 0xc2, 0xff, 0xc6, 0xc4, 0xcb, 0xff, 0x8c, 0x93, 0x98, 0xff, 0x1d, 0x24, 0x3c, 0xff, 0x1c, 0x27, 0x46, 0xff, 0x27, 0x35, 0x5e, 0xff, 0x2f, 0x40, 0x6c, 0xff, 0x34, 0x45, 0x73, 0xff, 0x2e, 0x3f, 0x6d, 0xff, 0x2b, 0x3c, 0x6b, 0xff, 0x2a, 0x3b, 0x69, 0xff, 0x2d, 0x40, 0x6f, 0xff, 0x32, 0x46, 0x78, 0xff, 0x3a, 0x4c, 0x81, 0xff, 0x48, 0x5a, 0x91, 0xff, 0x51, 0x66, 0x9c, 0xff, 0x5a, 0x72, 0xa7, 0xff, 0x67, 0x7d, 0xb4, 0xff, 0x60, 0x72, 0xab, 0xff, 0x59, 0x6c, 0xa5, 0xff, 0x52, 0x65, 0x9b, 0xff, 0x44, 0x56, 0x83, 0xff, 0x4f, 0x5f, 0x88, 0xff, 0x59, 0x63, 0x87, 0xff, 0x58, 0x58, 0x75, 0xff, 0x50, 0x53, 0x6f, 0xff, 0x4e, 0x54, 0x70, 0xff, 0x48, 0x4e, 0x6b, 0xff, 0x3f, 0x45, 0x62, 0xff, 0x39, 0x3f, 0x5c, 0xff, 0x3b, 0x40, 0x5e, 0xff, 0x3f, 0x45, 0x67, 0xff, 0x3e, 0x46, 0x69, 0xff, 0x38, 0x46, 0x6c, 0xff, 0x3e, 0x51, 0x7c, 0xff, 0x43, 0x5a, 0x8e, 0xff, 0x45, 0x5c, 0x93, 0xff, 0x48, 0x5d, 0x93, 0xff, 0x4f, 0x64, 0x9b, 0xff, 0x53, 0x68, 0xa0, 0xff, 0x57, 0x6e, 0x9e, 0xff, 0x6e, 0x7f, 0xa3, 0xff, 0x7d, 0x88, 0xab, 0xff, 0x7b, 0x87, 0xa9, 0xff, 0x86, 0x8f, 0xb0, 0xff, 0x93, 0x9b, 0xb8, 0xff, 0xa3, 0xa5, 0xbd, 0xff, 0xb3, 0xac, 0xc0, 0xff, 0xc2, 0xb6, 0xc5, 0xff, 0xcc, 0xbf, 0xcf, 0xff, 0xd6, 0xc7, 0xd5, 0xff, 0xd9, 0xc9, 0xd4, 0xff, 0xd7, 0xc8, 0xd4, 0xff, 0xd5, 0xc4, 0xd2, 0xff, 0xd5, 0xc2, 0xd1, 0xff, 0xd2, 0xbd, 0xca, 0xff, 0xcc, 0xb6, 0xbf, 0xff, 0xc7, 0xae, 0xb4, 0xff, 0xbc, 0xa4, 0xa9, 0xff, 0xa9, 0x97, 0x99, 0xff, 0x9a, 0x8c, 0x8a, 0xff, 0x96, 0x8a, 0x86, 0xff, 0x97, 0x8b, 0x8a, 0xff, 0x97, 0x8f, 0x91, 0xff, 0x99, 0x96, 0x9a, 0xff, 0x99, 0x9a, 0xa1, 0xff, 0x94, 0x97, 0x9f, 0xff, 0x90, 0x95, 0xa1, 0xff, 0x91, 0x92, 0xa4, 0xff, 0x8f, 0x8e, 0xa4, 0xff, 0x92, 0x90, 0xaa, 0xff, 0x89, 0x8b, 0xa2, 0xff, 0x7c, 0x80, 0x96, 0xff, 0x76, 0x76, 0x8f, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x60, 0x60, 0xb4, 0x5c, 0x5b, 0x5d, 0xff, 0x59, 0x58, 0x5a, 0xff, 0x59, 0x53, 0x54, 0xff, 0x55, 0x4b, 0x4c, 0xff, 0x55, 0x49, 0x4b, 0xff, 0x60, 0x55, 0x56, 0xff, 0x76, 0x69, 0x6b, 0xff, 0x84, 0x73, 0x75, 0xff, 0x84, 0x73, 0x76, 0xff, 0x7e, 0x6d, 0x70, 0xff, 0x7e, 0x6c, 0x70, 0xff, 0x84, 0x73, 0x74, 0xff, 0x83, 0x74, 0x75, 0xff, 0x83, 0x77, 0x78, 0xff, 0x8d, 0x87, 0x86, 0xff, 0x9c, 0x99, 0x98, 0xff, 0xa4, 0xa0, 0x9f, 0xff, 0xa3, 0x9f, 0x9e, 0xff, 0xa3, 0xa0, 0x9e, 0xff, 0xa3, 0xa5, 0xa2, 0xff, 0xa2, 0xa6, 0xa4, 0xff, 0x9f, 0xa2, 0x9f, 0xff, 0x97, 0x9a, 0x97, 0xff, 0x94, 0x97, 0x94, 0xff, 0x8b, 0x8e, 0x8b, 0xff, 0x77, 0x7a, 0x77, 0xff, 0x6d, 0x70, 0x6e, 0xff, 0x6c, 0x6d, 0x6b, 0xff, 0x69, 0x68, 0x68, 0xff, 0x6c, 0x6a, 0x6f, 0xff, 0x79, 0x78, 0x7a, 0xff, 0x8e, 0x8e, 0x8f, 0xff, 0xa0, 0x9e, 0xa3, 0xff, 0xac, 0xaa, 0xb3, 0xff, 0xb7, 0xb6, 0xc1, 0xff, 0xc1, 0xc0, 0xca, 0xff, 0xc4, 0xc2, 0xcc, 0xff, 0xc6, 0xc2, 0xcd, 0xff, 0xc3, 0xbe, 0xca, 0xff, 0xbd, 0xb9, 0xc2, 0xff, 0xbe, 0xbf, 0xc2, 0xff, 0x53, 0x5c, 0x67, 0xff, 0x0f, 0x1e, 0x34, 0xff, 0x1d, 0x2d, 0x53, 0xff, 0x1c, 0x2e, 0x54, 0xff, 0x29, 0x3d, 0x64, 0xff, 0x2b, 0x3e, 0x6b, 0xff, 0x29, 0x3c, 0x6a, 0xff, 0x2e, 0x41, 0x6d, 0xff, 0x31, 0x45, 0x75, 0xff, 0x31, 0x46, 0x78, 0xff, 0x32, 0x46, 0x76, 0xff, 0x39, 0x4e, 0x81, 0xff, 0x3b, 0x51, 0x87, 0xff, 0x47, 0x5d, 0x91, 0xff, 0x53, 0x67, 0x9e, 0xff, 0x4b, 0x5c, 0x94, 0xff, 0x4b, 0x5f, 0x93, 0xff, 0x47, 0x5b, 0x8c, 0xff, 0x40, 0x52, 0x7d, 0xff, 0x50, 0x5b, 0x7b, 0xff, 0x5a, 0x5d, 0x75, 0xff, 0x4e, 0x4d, 0x64, 0xff, 0x4b, 0x4d, 0x68, 0xff, 0x49, 0x4d, 0x6b, 0xff, 0x3f, 0x44, 0x63, 0xff, 0x3b, 0x3f, 0x5f, 0xff, 0x3b, 0x3f, 0x5f, 0xff, 0x3e, 0x40, 0x5b, 0xff, 0x38, 0x3d, 0x5a, 0xff, 0x37, 0x3f, 0x61, 0xff, 0x3d, 0x4c, 0x77, 0xff, 0x41, 0x54, 0x86, 0xff, 0x45, 0x57, 0x90, 0xff, 0x44, 0x59, 0x90, 0xff, 0x49, 0x5e, 0x91, 0xff, 0x50, 0x65, 0x99, 0xff, 0x53, 0x68, 0x9e, 0xff, 0x52, 0x68, 0x9c, 0xff, 0x66, 0x78, 0xa0, 0xff, 0x82, 0x91, 0xb3, 0xff, 0x7e, 0x8f, 0xb5, 0xff, 0x87, 0x95, 0xb5, 0xff, 0x9a, 0xa3, 0xbf, 0xff, 0xab, 0xaf, 0xc8, 0xff, 0xb7, 0xb0, 0xc6, 0xff, 0xbb, 0xb6, 0xc5, 0xff, 0xc9, 0xc1, 0xd1, 0xff, 0xd5, 0xca, 0xd8, 0xff, 0xd8, 0xcc, 0xd6, 0xff, 0xda, 0xc9, 0xd5, 0xff, 0xd6, 0xc5, 0xd2, 0xff, 0xd2, 0xc2, 0xcf, 0xff, 0xcf, 0xbc, 0xc8, 0xff, 0xcb, 0xb4, 0xbd, 0xff, 0xc7, 0xad, 0xb3, 0xff, 0xbb, 0xa5, 0xaa, 0xff, 0xa7, 0x97, 0x9b, 0xff, 0x97, 0x87, 0x88, 0xff, 0x96, 0x86, 0x84, 0xff, 0x99, 0x8b, 0x8a, 0xff, 0x96, 0x8d, 0x8e, 0xff, 0x97, 0x91, 0x95, 0xff, 0x9b, 0x9c, 0xa1, 0xff, 0x99, 0x9a, 0xa2, 0xff, 0x95, 0x97, 0xa2, 0xff, 0x8f, 0x96, 0xa6, 0xff, 0x8f, 0x97, 0xa9, 0xff, 0x8f, 0x94, 0xa9, 0xff, 0x88, 0x8b, 0xa7, 0xff, 0x7f, 0x82, 0xa0, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x62, 0x62, 0x27, 0x66, 0x66, 0x68, 0xfa, 0x63, 0x61, 0x63, 0xff, 0x60, 0x5b, 0x5c, 0xff, 0x5b, 0x51, 0x52, 0xff, 0x5a, 0x4d, 0x4e, 0xff, 0x61, 0x57, 0x56, 0xff, 0x71, 0x65, 0x65, 0xff, 0x7c, 0x6d, 0x6d, 0xff, 0x7a, 0x6a, 0x6b, 0xff, 0x76, 0x67, 0x67, 0xff, 0x78, 0x69, 0x6a, 0xff, 0x7d, 0x6d, 0x6c, 0xff, 0x7f, 0x70, 0x6e, 0xff, 0x83, 0x78, 0x74, 0xff, 0x8f, 0x88, 0x86, 0xff, 0xa0, 0x9e, 0x9d, 0xff, 0xaa, 0xa6, 0xa5, 0xff, 0xa7, 0xa2, 0xa2, 0xff, 0xa7, 0xa4, 0xa5, 0xff, 0xa8, 0xa8, 0xa9, 0xff, 0xa7, 0xaa, 0xaa, 0xff, 0xa3, 0xa5, 0xa5, 0xff, 0x9f, 0xa0, 0xa1, 0xff, 0x98, 0x9a, 0x9a, 0xff, 0x8d, 0x8f, 0x90, 0xff, 0x7c, 0x7e, 0x7e, 0xff, 0x70, 0x73, 0x72, 0xff, 0x6f, 0x6e, 0x6f, 0xff, 0x6d, 0x6b, 0x6f, 0xff, 0x70, 0x6e, 0x76, 0xff, 0x80, 0x7e, 0x84, 0xff, 0x98, 0x96, 0x9b, 0xff, 0xab, 0xa7, 0xb2, 0xff, 0xb7, 0xb5, 0xc0, 0xff, 0xc0, 0xc0, 0xca, 0xff, 0xc7, 0xc7, 0xd0, 0xff, 0xc8, 0xc6, 0xd0, 0xff, 0xc8, 0xc4, 0xcf, 0xff, 0xc5, 0xc1, 0xcd, 0xff, 0xbf, 0xbc, 0xc5, 0xff, 0xbb, 0xbe, 0xc3, 0xff, 0xa4, 0xab, 0xb4, 0xff, 0x38, 0x41, 0x50, 0xff, 0x0d, 0x19, 0x31, 0xff, 0x1b, 0x29, 0x47, 0xff, 0x1d, 0x2b, 0x4f, 0xff, 0x24, 0x36, 0x5e, 0xff, 0x24, 0x36, 0x5e, 0xff, 0x2a, 0x3c, 0x63, 0xff, 0x2b, 0x3c, 0x66, 0xff, 0x27, 0x37, 0x61, 0xff, 0x28, 0x38, 0x62, 0xff, 0x2d, 0x3e, 0x67, 0xff, 0x2c, 0x3e, 0x66, 0xff, 0x32, 0x44, 0x6b, 0xff, 0x3a, 0x4c, 0x76, 0xff, 0x39, 0x4c, 0x77, 0xff, 0x39, 0x50, 0x77, 0xff, 0x44, 0x56, 0x7a, 0xff, 0x4c, 0x53, 0x71, 0xff, 0x48, 0x4c, 0x65, 0xff, 0x4a, 0x4b, 0x60, 0xff, 0x45, 0x46, 0x5a, 0xff, 0x43, 0x47, 0x5e, 0xff, 0x3c, 0x40, 0x5b, 0xff, 0x36, 0x3d, 0x5a, 0xff, 0x37, 0x3e, 0x59, 0xff, 0x39, 0x3f, 0x5b, 0xff, 0x34, 0x3b, 0x56, 0xff, 0x2f, 0x39, 0x59, 0xff, 0x34, 0x43, 0x69, 0xff, 0x3f, 0x51, 0x7b, 0xff, 0x43, 0x55, 0x86, 0xff, 0x44, 0x57, 0x90, 0xff, 0x43, 0x58, 0x90, 0xff, 0x49, 0x5e, 0x91, 0xff, 0x4e, 0x64, 0x97, 0xff, 0x52, 0x68, 0x9c, 0xff, 0x53, 0x67, 0x9b, 0xff, 0x5b, 0x71, 0x9f, 0xff, 0x75, 0x89, 0xb3, 0xff, 0x83, 0x96, 0xbe, 0xff, 0x8d, 0x9c, 0xbf, 0xff, 0xa2, 0xad, 0xcc, 0xff, 0xb3, 0xb5, 0xd1, 0xff, 0xbf, 0xb7, 0xd0, 0xff, 0xc0, 0xba, 0xcc, 0xff, 0xc9, 0xc1, 0xd1, 0xff, 0xd5, 0xc9, 0xd7, 0xff, 0xd8, 0xcb, 0xd6, 0xff, 0xdc, 0xcb, 0xd7, 0xff, 0xda, 0xc8, 0xd5, 0xff, 0xd5, 0xc5, 0xd1, 0xff, 0xd1, 0xbf, 0xcd, 0xff, 0xca, 0xb5, 0xc2, 0xff, 0xc6, 0xad, 0xb6, 0xff, 0xb8, 0xa3, 0xac, 0xff, 0xa5, 0x97, 0x9e, 0xff, 0x98, 0x89, 0x8d, 0xff, 0x95, 0x87, 0x88, 0xff, 0x9a, 0x8e, 0x8f, 0xff, 0x97, 0x90, 0x94, 0xff, 0x96, 0x94, 0x9a, 0xff, 0x9a, 0x9b, 0xa4, 0xff, 0x9b, 0x9e, 0xa9, 0xff, 0x94, 0x9b, 0xa8, 0xff, 0x90, 0x97, 0xa8, 0xff, 0x91, 0x97, 0xa9, 0xff, 0x90, 0x94, 0xaa, 0xff, 0x8b, 0x8e, 0xaa, 0xfa, 0x85, 0x85, 0xa5, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0x72, 0x74, 0x88, 0x6f, 0x6e, 0x6f, 0xff, 0x6b, 0x65, 0x66, 0xff, 0x63, 0x59, 0x5a, 0xff, 0x60, 0x53, 0x56, 0xff, 0x63, 0x59, 0x56, 0xff, 0x6c, 0x60, 0x5d, 0xff, 0x75, 0x66, 0x63, 0xff, 0x73, 0x64, 0x61, 0xff, 0x73, 0x64, 0x61, 0xff, 0x78, 0x69, 0x66, 0xff, 0x7a, 0x6b, 0x66, 0xff, 0x7d, 0x70, 0x6a, 0xff, 0x87, 0x7d, 0x75, 0xff, 0x93, 0x8d, 0x89, 0xff, 0x9f, 0x9b, 0x9b, 0xff, 0xa7, 0xa3, 0xa2, 0xff, 0xaa, 0xa5, 0xa5, 0xff, 0xaa, 0xa7, 0xaa, 0xff, 0xac, 0xac, 0xaf, 0xff, 0xab, 0xae, 0xb0, 0xff, 0xaa, 0xac, 0xae, 0xff, 0xa7, 0xa9, 0xab, 0xff, 0x9e, 0x9f, 0xa2, 0xff, 0x92, 0x93, 0x95, 0xff, 0x83, 0x85, 0x87, 0xff, 0x75, 0x77, 0x79, 0xff, 0x6e, 0x6d, 0x71, 0xff, 0x6c, 0x69, 0x70, 0xff, 0x71, 0x6d, 0x78, 0xff, 0x82, 0x7f, 0x87, 0xff, 0x9b, 0x98, 0xa0, 0xff, 0xae, 0xaa, 0xb6, 0xff, 0xbd, 0xbb, 0xc6, 0xff, 0xc8, 0xc7, 0xd1, 0xff, 0xcb, 0xcb, 0xd4, 0xff, 0xcb, 0xc9, 0xd4, 0xff, 0xcc, 0xc8, 0xd3, 0xff, 0xca, 0xc5, 0xd0, 0xff, 0xc2, 0xc0, 0xca, 0xff, 0xb7, 0xba, 0xc1, 0xff, 0xc2, 0xc4, 0xcc, 0xff, 0x8e, 0x92, 0x9a, 0xff, 0x1d, 0x28, 0x36, 0xff, 0x11, 0x1d, 0x34, 0xff, 0x16, 0x20, 0x42, 0xff, 0x1a, 0x29, 0x4e, 0xff, 0x1e, 0x31, 0x53, 0xff, 0x1a, 0x2d, 0x4f, 0xff, 0x19, 0x2a, 0x4c, 0xff, 0x1b, 0x2a, 0x4c, 0xff, 0x1a, 0x2b, 0x4e, 0xff, 0x1e, 0x2c, 0x4c, 0xff, 0x22, 0x2e, 0x4c, 0xff, 0x2b, 0x37, 0x55, 0xff, 0x30, 0x39, 0x5a, 0xff, 0x32, 0x38, 0x5b, 0xff, 0x34, 0x3d, 0x5d, 0xff, 0x43, 0x4a, 0x65, 0xff, 0x4a, 0x4a, 0x5e, 0xff, 0x41, 0x40, 0x55, 0xff, 0x47, 0x45, 0x59, 0xff, 0x40, 0x41, 0x54, 0xff, 0x36, 0x3a, 0x50, 0xff, 0x32, 0x37, 0x4f, 0xff, 0x2d, 0x34, 0x4c, 0xff, 0x2d, 0x34, 0x4d, 0xff, 0x30, 0x37, 0x4f, 0xff, 0x31, 0x3b, 0x58, 0xff, 0x34, 0x44, 0x67, 0xff, 0x37, 0x4b, 0x72, 0xff, 0x3a, 0x4c, 0x78, 0xff, 0x3c, 0x4f, 0x7f, 0xff, 0x43, 0x56, 0x8d, 0xff, 0x46, 0x5a, 0x91, 0xff, 0x4a, 0x5f, 0x91, 0xff, 0x4d, 0x62, 0x96, 0xff, 0x4f, 0x64, 0x98, 0xff, 0x53, 0x68, 0x99, 0xff, 0x5a, 0x72, 0xa2, 0xff, 0x65, 0x7e, 0xad, 0xff, 0x7c, 0x91, 0xbc, 0xff, 0x8f, 0xa0, 0xc5, 0xff, 0x9f, 0xab, 0xcd, 0xff, 0xb5, 0xb4, 0xd3, 0xff, 0xc6, 0xbb, 0xd7, 0xff, 0xc5, 0xbe, 0xd3, 0xff, 0xcc, 0xc4, 0xd5, 0xff, 0xd7, 0xcd, 0xda, 0xff, 0xda, 0xce, 0xda, 0xff, 0xdd, 0xcd, 0xd9, 0xff, 0xdb, 0xca, 0xd7, 0xff, 0xd8, 0xc7, 0xd3, 0xff, 0xd4, 0xc3, 0xd1, 0xff, 0xcc, 0xba, 0xc9, 0xff, 0xc5, 0xb0, 0xbb, 0xff, 0xb6, 0xa3, 0xad, 0xff, 0xa5, 0x98, 0xa2, 0xff, 0x9e, 0x92, 0x98, 0xff, 0x9c, 0x91, 0x94, 0xff, 0x9f, 0x95, 0x98, 0xff, 0x9d, 0x98, 0x9e, 0xff, 0x9b, 0x9b, 0xa3, 0xff, 0x9c, 0x9f, 0xaa, 0xff, 0x9f, 0xa4, 0xb1, 0xff, 0x99, 0xa1, 0xb0, 0xff, 0x96, 0x9c, 0xad, 0xff, 0x96, 0x9c, 0xae, 0xff, 0x94, 0x99, 0xae, 0xff, 0x8f, 0x93, 0xad, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x71, 0x71, 0x09, 0x7b, 0x7b, 0x7b, 0xdb, 0x72, 0x6c, 0x6d, 0xff, 0x67, 0x5d, 0x60, 0xff, 0x65, 0x58, 0x5a, 0xff, 0x62, 0x55, 0x54, 0xff, 0x67, 0x59, 0x55, 0xff, 0x6f, 0x5f, 0x58, 0xff, 0x73, 0x63, 0x5c, 0xff, 0x78, 0x68, 0x62, 0xff, 0x7e, 0x6d, 0x67, 0xff, 0x7c, 0x6f, 0x67, 0xff, 0x80, 0x76, 0x6e, 0xff, 0x89, 0x81, 0x7c, 0xff, 0x94, 0x8e, 0x8c, 0xff, 0x9d, 0x97, 0x9a, 0xff, 0xa1, 0x9f, 0xa2, 0xff, 0xa3, 0xa2, 0xa5, 0xff, 0xa9, 0xa9, 0xa8, 0xff, 0xac, 0xae, 0xb1, 0xff, 0xaa, 0xae, 0xb3, 0xff, 0xab, 0xaf, 0xb2, 0xff, 0xa8, 0xab, 0xaf, 0xff, 0xa0, 0xa3, 0xa6, 0xff, 0x99, 0x9a, 0x9b, 0xff, 0x8c, 0x8c, 0x8f, 0xff, 0x7a, 0x79, 0x80, 0xff, 0x71, 0x6e, 0x7b, 0xff, 0x6e, 0x6a, 0x78, 0xff, 0x76, 0x72, 0x80, 0xff, 0x87, 0x84, 0x90, 0xff, 0x9f, 0x9d, 0xa7, 0xff, 0xb2, 0xb2, 0xb8, 0xff, 0xc0, 0xc0, 0xc7, 0xff, 0xcb, 0xca, 0xd5, 0xff, 0xd1, 0xd0, 0xda, 0xff, 0xcf, 0xce, 0xd8, 0xff, 0xcd, 0xcc, 0xd6, 0xff, 0xcb, 0xc9, 0xd3, 0xff, 0xc5, 0xc4, 0xcf, 0xff, 0xc1, 0xc2, 0xcc, 0xff, 0xb9, 0xba, 0xc3, 0xff, 0xc2, 0xc3, 0xcc, 0xff, 0x84, 0x89, 0x97, 0xff, 0x2b, 0x30, 0x44, 0xff, 0x1d, 0x23, 0x3e, 0xff, 0x19, 0x21, 0x41, 0xff, 0x1b, 0x27, 0x46, 0xff, 0x13, 0x25, 0x43, 0xff, 0x1d, 0x2a, 0x49, 0xff, 0x21, 0x2a, 0x49, 0xff, 0x1e, 0x28, 0x47, 0xff, 0x1f, 0x2b, 0x48, 0xff, 0x24, 0x2f, 0x4a, 0xff, 0x31, 0x35, 0x51, 0xff, 0x2f, 0x31, 0x4f, 0xff, 0x33, 0x35, 0x53, 0xff, 0x39, 0x3d, 0x57, 0xff, 0x3a, 0x3d, 0x53, 0xff, 0x3c, 0x3c, 0x4f, 0xff, 0x3b, 0x3b, 0x50, 0xff, 0x37, 0x38, 0x4b, 0xff, 0x2e, 0x33, 0x44, 0xff, 0x27, 0x2c, 0x3f, 0xff, 0x28, 0x2b, 0x41, 0xff, 0x2a, 0x2f, 0x42, 0xff, 0x27, 0x2f, 0x46, 0xff, 0x2b, 0x36, 0x52, 0xff, 0x32, 0x41, 0x64, 0xff, 0x38, 0x4a, 0x70, 0xff, 0x39, 0x4b, 0x74, 0xff, 0x35, 0x49, 0x78, 0xff, 0x39, 0x4d, 0x7d, 0xff, 0x3f, 0x53, 0x83, 0xff, 0x48, 0x5d, 0x8c, 0xff, 0x49, 0x5c, 0x8d, 0xff, 0x49, 0x5a, 0x90, 0xff, 0x4e, 0x61, 0x96, 0xff, 0x54, 0x6b, 0x9a, 0xff, 0x5f, 0x75, 0xa4, 0xff, 0x64, 0x7b, 0xaa, 0xff, 0x6d, 0x86, 0xb2, 0xff, 0x84, 0x97, 0xc2, 0xff, 0x96, 0xa0, 0xc9, 0xff, 0xab, 0xac, 0xce, 0xff, 0xbd, 0xba, 0xd4, 0xff, 0xc8, 0xc4, 0xd6, 0xff, 0xd4, 0xcc, 0xdb, 0xff, 0xdb, 0xd4, 0xdd, 0xff, 0xda, 0xd0, 0xdd, 0xff, 0xd8, 0xcd, 0xda, 0xff, 0xdd, 0xd1, 0xdb, 0xff, 0xdf, 0xcf, 0xda, 0xff, 0xda, 0xc8, 0xd3, 0xff, 0xcf, 0xbd, 0xc9, 0xff, 0xc4, 0xb4, 0xc0, 0xff, 0xb7, 0xab, 0xb5, 0xff, 0xab, 0xa0, 0xaa, 0xff, 0xa0, 0x98, 0xa4, 0xff, 0xa0, 0x98, 0xa6, 0xff, 0xa1, 0x9a, 0xa7, 0xff, 0xa1, 0x9e, 0xaa, 0xff, 0xa0, 0xa1, 0xad, 0xff, 0xa2, 0xa6, 0xb3, 0xff, 0xa5, 0xa9, 0xb6, 0xff, 0xa6, 0xa9, 0xb7, 0xff, 0xa4, 0xa6, 0xb8, 0xff, 0xa0, 0xa3, 0xb8, 0xff, 0x9c, 0xa0, 0xb7, 0xdb, 0x8d, 0xaa, 0xaa, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7d, 0x7d, 0x7d, 0x41, 0x73, 0x6e, 0x6f, 0xfe, 0x6a, 0x60, 0x63, 0xff, 0x65, 0x58, 0x5a, 0xff, 0x5d, 0x50, 0x4f, 0xff, 0x5f, 0x51, 0x4d, 0xff, 0x68, 0x58, 0x51, 0xff, 0x73, 0x63, 0x5c, 0xff, 0x7b, 0x6b, 0x65, 0xff, 0x82, 0x70, 0x6a, 0xff, 0x83, 0x76, 0x6e, 0xff, 0x87, 0x7d, 0x76, 0xff, 0x8e, 0x85, 0x82, 0xff, 0x94, 0x8e, 0x8d, 0xff, 0x9b, 0x96, 0x98, 0xff, 0xa2, 0xa0, 0xa4, 0xff, 0xa5, 0xa4, 0xa9, 0xff, 0xaa, 0xaa, 0xab, 0xff, 0xac, 0xad, 0xb1, 0xff, 0xab, 0xae, 0xb4, 0xff, 0xaa, 0xac, 0xb3, 0xff, 0xaa, 0xac, 0xb2, 0xff, 0xa5, 0xa8, 0xae, 0xff, 0x9f, 0xa3, 0xa5, 0xff, 0x92, 0x94, 0x99, 0xff, 0x80, 0x81, 0x8a, 0xff, 0x79, 0x78, 0x88, 0xff, 0x78, 0x76, 0x88, 0xff, 0x7e, 0x7c, 0x8d, 0xff, 0x8c, 0x8b, 0x9a, 0xff, 0xa0, 0xa1, 0xad, 0xff, 0xb4, 0xb6, 0xbe, 0xff, 0xc3, 0xc3, 0xcb, 0xff, 0xcd, 0xcc, 0xd6, 0xff, 0xd4, 0xd3, 0xdd, 0xff, 0xd2, 0xd1, 0xdb, 0xff, 0xce, 0xcd, 0xd7, 0xff, 0xcc, 0xcb, 0xd5, 0xff, 0xca, 0xc9, 0xd3, 0xff, 0xc2, 0xc3, 0xcd, 0xff, 0xc0, 0xc0, 0xcb, 0xff, 0xbe, 0xbe, 0xc8, 0xff, 0xba, 0xbb, 0xc3, 0xff, 0x66, 0x68, 0x74, 0xff, 0x29, 0x2d, 0x3f, 0xff, 0x37, 0x37, 0x4f, 0xff, 0x3d, 0x3e, 0x56, 0xff, 0x3a, 0x3c, 0x54, 0xff, 0x3d, 0x3c, 0x55, 0xff, 0x3a, 0x38, 0x51, 0xff, 0x3a, 0x38, 0x51, 0xff, 0x3a, 0x3b, 0x55, 0xff, 0x36, 0x3b, 0x54, 0xff, 0x3c, 0x3e, 0x57, 0xff, 0x37, 0x3b, 0x54, 0xff, 0x33, 0x37, 0x4f, 0xff, 0x32, 0x37, 0x4d, 0xff, 0x36, 0x3a, 0x4c, 0xff, 0x37, 0x39, 0x4b, 0xff, 0x32, 0x34, 0x46, 0xff, 0x25, 0x28, 0x39, 0xff, 0x19, 0x21, 0x31, 0xff, 0x1c, 0x24, 0x36, 0xff, 0x22, 0x28, 0x3d, 0xff, 0x27, 0x2c, 0x43, 0xff, 0x2b, 0x33, 0x51, 0xff, 0x33, 0x41, 0x63, 0xff, 0x36, 0x47, 0x6d, 0xff, 0x35, 0x48, 0x71, 0xff, 0x35, 0x4a, 0x76, 0xff, 0x38, 0x4e, 0x7d, 0xff, 0x3d, 0x51, 0x81, 0xff, 0x3d, 0x51, 0x80, 0xff, 0x42, 0x56, 0x85, 0xff, 0x42, 0x56, 0x86, 0xff, 0x45, 0x57, 0x8b, 0xff, 0x4e, 0x60, 0x96, 0xff, 0x55, 0x6a, 0x9d, 0xff, 0x5f, 0x73, 0xa5, 0xff, 0x63, 0x78, 0xab, 0xff, 0x65, 0x7d, 0xb0, 0xff, 0x75, 0x8a, 0xba, 0xff, 0x81, 0x96, 0xc4, 0xff, 0x89, 0xa3, 0xc9, 0xff, 0x9d, 0xad, 0xcb, 0xff, 0xbe, 0xbf, 0xd8, 0xff, 0xd6, 0xcf, 0xe3, 0xff, 0xdf, 0xd8, 0xe0, 0xff, 0xe2, 0xd5, 0xdc, 0xff, 0xdc, 0xd1, 0xdd, 0xff, 0xdd, 0xd4, 0xe1, 0xff, 0xde, 0xd1, 0xdd, 0xff, 0xd9, 0xc7, 0xd5, 0xff, 0xd1, 0xc0, 0xce, 0xff, 0xc9, 0xbd, 0xc9, 0xff, 0xbe, 0xb5, 0xc3, 0xff, 0xb1, 0xaa, 0xb8, 0xff, 0xa5, 0xa0, 0xb1, 0xff, 0xa4, 0xa1, 0xb5, 0xff, 0xa7, 0xa4, 0xb7, 0xff, 0xa6, 0xa7, 0xb8, 0xff, 0xa5, 0xa9, 0xba, 0xff, 0xa5, 0xab, 0xbe, 0xff, 0xa8, 0xad, 0xc0, 0xff, 0xaa, 0xae, 0xc1, 0xff, 0xaa, 0xad, 0xc2, 0xff, 0xa6, 0xab, 0xc2, 0xfe, 0xa4, 0xac, 0xc4, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0x6a, 0x6c, 0x8f, 0x6e, 0x64, 0x66, 0xff, 0x6b, 0x5d, 0x60, 0xff, 0x5c, 0x4f, 0x4e, 0xff, 0x56, 0x48, 0x44, 0xff, 0x5d, 0x4c, 0x45, 0xff, 0x68, 0x58, 0x51, 0xff, 0x75, 0x65, 0x5f, 0xff, 0x82, 0x70, 0x6b, 0xff, 0x89, 0x7b, 0x73, 0xff, 0x8c, 0x82, 0x7b, 0xff, 0x8f, 0x86, 0x82, 0xff, 0x94, 0x8d, 0x8c, 0xff, 0x9c, 0x97, 0x99, 0xff, 0xa7, 0xa5, 0xa8, 0xff, 0xad, 0xac, 0xb1, 0xff, 0xb0, 0xaf, 0xb4, 0xff, 0xaf, 0xaf, 0xb7, 0xff, 0xaf, 0xb1, 0xbb, 0xff, 0xad, 0xae, 0xb8, 0xff, 0xad, 0xad, 0xb8, 0xff, 0xa7, 0xa8, 0xb2, 0xff, 0x9d, 0xa3, 0xa8, 0xff, 0x93, 0x99, 0xa0, 0xff, 0x87, 0x8a, 0x95, 0xff, 0x82, 0x84, 0x94, 0xff, 0x82, 0x83, 0x96, 0xff, 0x85, 0x86, 0x98, 0xff, 0x8d, 0x8f, 0xa1, 0xff, 0x9e, 0xa0, 0xaf, 0xff, 0xb0, 0xb5, 0xc0, 0xff, 0xc4, 0xc6, 0xcf, 0xff, 0xd1, 0xcf, 0xd9, 0xff, 0xd6, 0xd5, 0xdf, 0xff, 0xd5, 0xd4, 0xde, 0xff, 0xd1, 0xd0, 0xda, 0xff, 0xcf, 0xce, 0xd8, 0xff, 0xcc, 0xcc, 0xd6, 0xff, 0xc8, 0xc9, 0xd3, 0xff, 0xc5, 0xc5, 0xcf, 0xff, 0xbc, 0xbd, 0xc7, 0xff, 0xb9, 0xbf, 0xc9, 0xff, 0x83, 0x8a, 0x99, 0xff, 0x22, 0x2a, 0x3e, 0xff, 0x1c, 0x22, 0x33, 0xff, 0x31, 0x32, 0x41, 0xff, 0x46, 0x42, 0x52, 0xff, 0x51, 0x4d, 0x5e, 0xff, 0x53, 0x51, 0x61, 0xff, 0x4c, 0x4a, 0x5a, 0xff, 0x4a, 0x44, 0x58, 0xff, 0x46, 0x42, 0x57, 0xff, 0x3f, 0x41, 0x55, 0xff, 0x38, 0x3b, 0x4f, 0xff, 0x37, 0x39, 0x4b, 0xff, 0x32, 0x34, 0x43, 0xff, 0x29, 0x2c, 0x3a, 0xff, 0x1e, 0x21, 0x2f, 0xff, 0x19, 0x1b, 0x28, 0xff, 0x1a, 0x1e, 0x2d, 0xff, 0x1a, 0x20, 0x3c, 0xff, 0x22, 0x27, 0x46, 0xff, 0x27, 0x2c, 0x4c, 0xff, 0x2d, 0x33, 0x51, 0xff, 0x32, 0x3c, 0x5e, 0xff, 0x36, 0x45, 0x6c, 0xff, 0x33, 0x46, 0x70, 0xff, 0x32, 0x46, 0x73, 0xff, 0x35, 0x4e, 0x7c, 0xff, 0x3b, 0x51, 0x81, 0xff, 0x3c, 0x50, 0x80, 0xff, 0x3d, 0x51, 0x81, 0xff, 0x3f, 0x53, 0x83, 0xff, 0x3d, 0x51, 0x82, 0xff, 0x45, 0x57, 0x8b, 0xff, 0x50, 0x62, 0x99, 0xff, 0x55, 0x6a, 0x9f, 0xff, 0x5d, 0x71, 0xa5, 0xff, 0x61, 0x76, 0xac, 0xff, 0x61, 0x79, 0xb1, 0xff, 0x67, 0x82, 0xb6, 0xff, 0x71, 0x8e, 0xc0, 0xff, 0x76, 0x95, 0xcd, 0xff, 0x82, 0x9c, 0xce, 0xff, 0x96, 0xab, 0xd5, 0xff, 0xb6, 0xbd, 0xe0, 0xff, 0xd0, 0xd0, 0xe2, 0xff, 0xe2, 0xd9, 0xe5, 0xff, 0xe2, 0xd8, 0xe5, 0xff, 0xdb, 0xd2, 0xe0, 0xff, 0xd7, 0xcb, 0xdb, 0xff, 0xd6, 0xc8, 0xd9, 0xff, 0xd0, 0xc2, 0xd2, 0xff, 0xcc, 0xc1, 0xd1, 0xff, 0xc1, 0xb9, 0xcb, 0xff, 0xac, 0xaa, 0xbf, 0xff, 0xa4, 0xa3, 0xbb, 0xff, 0xa8, 0xa9, 0xc1, 0xff, 0xab, 0xad, 0xc4, 0xff, 0xa8, 0xac, 0xc3, 0xff, 0xa6, 0xad, 0xc4, 0xff, 0xaa, 0xb0, 0xc8, 0xff, 0xae, 0xb3, 0xcc, 0xff, 0xae, 0xb3, 0xcc, 0xff, 0xad, 0xb2, 0xc9, 0xff, 0xab, 0xb1, 0xc9, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6d, 0x6d, 0x07, 0x6d, 0x62, 0x64, 0xd1, 0x6b, 0x5f, 0x5d, 0xff, 0x57, 0x4a, 0x45, 0xff, 0x4e, 0x40, 0x3a, 0xff, 0x57, 0x49, 0x43, 0xff, 0x60, 0x51, 0x4a, 0xff, 0x6c, 0x5c, 0x56, 0xff, 0x79, 0x69, 0x64, 0xff, 0x80, 0x73, 0x6c, 0xff, 0x87, 0x7e, 0x78, 0xff, 0x8a, 0x83, 0x80, 0xff, 0x96, 0x90, 0x91, 0xff, 0xa4, 0xa1, 0xa3, 0xff, 0xae, 0xaf, 0xb0, 0xff, 0xb3, 0xb4, 0xb7, 0xff, 0xb4, 0xb4, 0xbb, 0xff, 0xb1, 0xb3, 0xbd, 0xff, 0xb2, 0xb5, 0xc0, 0xff, 0xb3, 0xb4, 0xbf, 0xff, 0xae, 0xaf, 0xbb, 0xff, 0xa7, 0xa9, 0xb4, 0xff, 0xa3, 0xa5, 0xae, 0xff, 0x99, 0x9b, 0xa7, 0xff, 0x8d, 0x90, 0x9e, 0xff, 0x8a, 0x8c, 0x9f, 0xff, 0x8c, 0x8d, 0xa1, 0xff, 0x91, 0x92, 0xa6, 0xff, 0x94, 0x97, 0xa8, 0xff, 0xa1, 0xa3, 0xb2, 0xff, 0xb4, 0xb4, 0xc4, 0xff, 0xc1, 0xc3, 0xd0, 0xff, 0xc8, 0xcd, 0xd6, 0xff, 0xd4, 0xd6, 0xdd, 0xff, 0xd7, 0xd7, 0xdf, 0xff, 0xd4, 0xd3, 0xdd, 0xff, 0xd0, 0xd1, 0xdb, 0xff, 0xc9, 0xcd, 0xd6, 0xff, 0xcb, 0xcd, 0xd7, 0xff, 0xc6, 0xc8, 0xd1, 0xff, 0xbf, 0xc1, 0xcb, 0xff, 0xbd, 0xc5, 0xd0, 0xff, 0x98, 0xa1, 0xb2, 0xff, 0x39, 0x42, 0x5a, 0xff, 0x0d, 0x14, 0x2a, 0xff, 0x0d, 0x11, 0x23, 0xff, 0x18, 0x1a, 0x28, 0xff, 0x20, 0x22, 0x30, 0xff, 0x24, 0x27, 0x34, 0xff, 0x23, 0x28, 0x31, 0xff, 0x28, 0x27, 0x33, 0xff, 0x29, 0x25, 0x35, 0xff, 0x22, 0x21, 0x30, 0xff, 0x1f, 0x1f, 0x2e, 0xff, 0x1b, 0x1b, 0x29, 0xff, 0x11, 0x12, 0x22, 0xff, 0x0b, 0x0e, 0x1f, 0xff, 0x0e, 0x14, 0x23, 0xff, 0x15, 0x1c, 0x2d, 0xff, 0x19, 0x23, 0x37, 0xff, 0x26, 0x31, 0x4e, 0xff, 0x2c, 0x35, 0x55, 0xff, 0x2c, 0x34, 0x57, 0xff, 0x30, 0x3b, 0x62, 0xff, 0x31, 0x3f, 0x66, 0xff, 0x31, 0x42, 0x6a, 0xff, 0x2e, 0x44, 0x6d, 0xff, 0x32, 0x48, 0x73, 0xff, 0x3b, 0x4f, 0x7e, 0xff, 0x3b, 0x4f, 0x7f, 0xff, 0x3a, 0x4e, 0x7f, 0xff, 0x40, 0x53, 0x84, 0xff, 0x3f, 0x54, 0x83, 0xff, 0x3d, 0x53, 0x84, 0xff, 0x44, 0x58, 0x8c, 0xff, 0x4d, 0x62, 0x98, 0xff, 0x54, 0x69, 0xa1, 0xff, 0x5c, 0x70, 0xa8, 0xff, 0x63, 0x78, 0xaf, 0xff, 0x63, 0x7a, 0xb3, 0xff, 0x66, 0x7e, 0xb5, 0xff, 0x6d, 0x86, 0xbd, 0xff, 0x75, 0x8f, 0xca, 0xff, 0x77, 0x95, 0xd1, 0xff, 0x74, 0x96, 0xd2, 0xff, 0x87, 0xa1, 0xd2, 0xff, 0xa8, 0xb6, 0xdb, 0xff, 0xc1, 0xc3, 0xe1, 0xff, 0xd3, 0xcd, 0xe2, 0xff, 0xdc, 0xd2, 0xe2, 0xff, 0xdb, 0xcb, 0xe1, 0xff, 0xda, 0xce, 0xd8, 0xff, 0xda, 0xd2, 0xd9, 0xff, 0xdd, 0xd3, 0xe1, 0xff, 0xda, 0xd3, 0xdc, 0xff, 0xc8, 0xc2, 0xd0, 0xff, 0xaf, 0xab, 0xc4, 0xff, 0xa5, 0xa3, 0xc2, 0xff, 0xa8, 0xab, 0xc8, 0xff, 0xb0, 0xb3, 0xcf, 0xff, 0xad, 0xb2, 0xce, 0xff, 0xac, 0xb4, 0xd1, 0xff, 0xb0, 0xb8, 0xd5, 0xff, 0xb3, 0xba, 0xd7, 0xff, 0xb2, 0xbb, 0xd4, 0xd1, 0xbf, 0xbf, 0xdf, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x61, 0x61, 0x2a, 0x63, 0x5b, 0x57, 0xf3, 0x51, 0x48, 0x43, 0xff, 0x50, 0x47, 0x43, 0xff, 0x5c, 0x53, 0x50, 0xff, 0x63, 0x58, 0x55, 0xff, 0x6c, 0x61, 0x5d, 0xff, 0x75, 0x6a, 0x66, 0xff, 0x79, 0x6f, 0x6a, 0xff, 0x84, 0x7c, 0x79, 0xff, 0x8e, 0x89, 0x88, 0xff, 0x9d, 0x98, 0x9a, 0xff, 0xaa, 0xa7, 0xaa, 0xff, 0xb3, 0xb4, 0xb5, 0xff, 0xb5, 0xb7, 0xb9, 0xff, 0xb5, 0xb5, 0xbc, 0xff, 0xb2, 0xb4, 0xbe, 0xff, 0xb3, 0xb7, 0xc2, 0xff, 0xb3, 0xb7, 0xc3, 0xff, 0xad, 0xb1, 0xbd, 0xff, 0xa8, 0xac, 0xb7, 0xff, 0xa8, 0xa9, 0xb5, 0xff, 0x9e, 0x9f, 0xae, 0xff, 0x91, 0x96, 0xa8, 0xff, 0x91, 0x96, 0xa9, 0xff, 0x96, 0x9a, 0xae, 0xff, 0x9b, 0x9f, 0xb3, 0xff, 0x9d, 0xa3, 0xb3, 0xff, 0xa6, 0xab, 0xb9, 0xff, 0xb5, 0xb3, 0xc5, 0xff, 0xbc, 0xbe, 0xcd, 0xff, 0xc2, 0xca, 0xd3, 0xff, 0xce, 0xd2, 0xd8, 0xff, 0xd5, 0xd6, 0xdc, 0xff, 0xd6, 0xd5, 0xe0, 0xff, 0xd0, 0xd3, 0xdd, 0xff, 0xc9, 0xcf, 0xd7, 0xff, 0xc8, 0xca, 0xd4, 0xff, 0xc7, 0xc9, 0xd3, 0xff, 0xc1, 0xc3, 0xcd, 0xff, 0xbc, 0xc2, 0xcd, 0xff, 0xab, 0xb3, 0xc3, 0xff, 0x54, 0x5b, 0x72, 0xff, 0x1b, 0x23, 0x3a, 0xff, 0x17, 0x1e, 0x33, 0xff, 0x17, 0x1b, 0x2f, 0xff, 0x13, 0x17, 0x2a, 0xff, 0x04, 0x0a, 0x19, 0xff, 0x00, 0x04, 0x0f, 0xff, 0x00, 0x06, 0x10, 0xff, 0x02, 0x06, 0x10, 0xff, 0x04, 0x08, 0x10, 0xff, 0x06, 0x09, 0x15, 0xff, 0x09, 0x0e, 0x20, 0xff, 0x0c, 0x14, 0x29, 0xff, 0x0d, 0x16, 0x2d, 0xff, 0x14, 0x1d, 0x34, 0xff, 0x1e, 0x2a, 0x43, 0xff, 0x25, 0x32, 0x4f, 0xff, 0x2e, 0x3c, 0x5b, 0xff, 0x33, 0x3f, 0x61, 0xff, 0x34, 0x3f, 0x65, 0xff, 0x35, 0x43, 0x6d, 0xff, 0x30, 0x41, 0x6c, 0xff, 0x31, 0x45, 0x6d, 0xff, 0x34, 0x49, 0x72, 0xff, 0x35, 0x4b, 0x75, 0xff, 0x36, 0x4a, 0x78, 0xff, 0x37, 0x4b, 0x7b, 0xff, 0x3a, 0x4e, 0x7e, 0xff, 0x3f, 0x53, 0x83, 0xff, 0x40, 0x55, 0x84, 0xff, 0x42, 0x59, 0x89, 0xff, 0x44, 0x5a, 0x8d, 0xff, 0x49, 0x5e, 0x94, 0xff, 0x55, 0x6a, 0xa1, 0xff, 0x5e, 0x73, 0xaa, 0xff, 0x60, 0x76, 0xad, 0xff, 0x66, 0x7d, 0xb5, 0xff, 0x67, 0x7d, 0xb5, 0xff, 0x69, 0x7f, 0xb7, 0xff, 0x70, 0x8b, 0xc2, 0xff, 0x73, 0x91, 0xcb, 0xff, 0x6f, 0x90, 0xce, 0xff, 0x73, 0x95, 0xd0, 0xff, 0x84, 0xa1, 0xd7, 0xff, 0x92, 0xa9, 0xd8, 0xff, 0xa1, 0xb2, 0xde, 0xff, 0xb3, 0xbb, 0xe0, 0xff, 0xc9, 0xc5, 0xdd, 0xff, 0xe6, 0xda, 0xe9, 0xff, 0xee, 0xe2, 0xf0, 0xff, 0xe4, 0xdf, 0xe5, 0xff, 0xfa, 0xfa, 0xf2, 0xff, 0xfc, 0xf9, 0xf6, 0xff, 0xe6, 0xe1, 0xe5, 0xff, 0xc5, 0xbf, 0xd2, 0xff, 0xad, 0xae, 0xd2, 0xff, 0xaf, 0xb6, 0xd3, 0xff, 0xaf, 0xba, 0xd5, 0xff, 0xb0, 0xb9, 0xd6, 0xff, 0xb3, 0xbb, 0xd9, 0xff, 0xb5, 0xbf, 0xdc, 0xf4, 0xb6, 0xbc, 0xda, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5d, 0x57, 0x54, 0x55, 0x4e, 0x49, 0x47, 0xfe, 0x57, 0x53, 0x53, 0xff, 0x65, 0x62, 0x61, 0xff, 0x6a, 0x64, 0x63, 0xff, 0x71, 0x6b, 0x6a, 0xff, 0x74, 0x6e, 0x6d, 0xff, 0x79, 0x72, 0x70, 0xff, 0x88, 0x82, 0x81, 0xff, 0x93, 0x8f, 0x93, 0xff, 0xa3, 0x9f, 0xa2, 0xff, 0xb0, 0xac, 0xae, 0xff, 0xb7, 0xb8, 0xb8, 0xff, 0xb8, 0xba, 0xbc, 0xff, 0xb6, 0xb6, 0xbe, 0xff, 0xb4, 0xb5, 0xc0, 0xff, 0xb4, 0xb9, 0xc4, 0xff, 0xb2, 0xbb, 0xc6, 0xff, 0xaa, 0xb3, 0xbd, 0xff, 0xa7, 0xb0, 0xba, 0xff, 0xa6, 0xa9, 0xb8, 0xff, 0x9c, 0xa1, 0xb2, 0xff, 0x94, 0x9b, 0xb0, 0xff, 0x94, 0x9b, 0xaf, 0xff, 0x9a, 0xa1, 0xb4, 0xff, 0x9e, 0xa5, 0xb9, 0xff, 0x9f, 0xa7, 0xb7, 0xff, 0xa6, 0xaf, 0xbc, 0xff, 0xb2, 0xb5, 0xc5, 0xff, 0xba, 0xbe, 0xcc, 0xff, 0xc0, 0xc7, 0xd0, 0xff, 0xca, 0xcd, 0xd3, 0xff, 0xcf, 0xd0, 0xd6, 0xff, 0xd2, 0xd0, 0xdb, 0xff, 0xcb, 0xce, 0xd8, 0xff, 0xc6, 0xca, 0xd3, 0xff, 0xc3, 0xc5, 0xcf, 0xff, 0xc5, 0xc6, 0xd0, 0xff, 0xc1, 0xc3, 0xcc, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xb5, 0xbc, 0xcd, 0xff, 0x6d, 0x76, 0x8d, 0xff, 0x22, 0x30, 0x43, 0xff, 0x15, 0x1f, 0x34, 0xff, 0x19, 0x1b, 0x3a, 0xff, 0x1e, 0x23, 0x3b, 0xff, 0x13, 0x1c, 0x2c, 0xff, 0x07, 0x10, 0x1d, 0xff, 0x01, 0x09, 0x18, 0xff, 0x03, 0x09, 0x1a, 0xff, 0x09, 0x0f, 0x1e, 0xff, 0x0a, 0x13, 0x24, 0xff, 0x0f, 0x1b, 0x33, 0xff, 0x14, 0x23, 0x3c, 0xff, 0x13, 0x22, 0x3e, 0xff, 0x1d, 0x28, 0x48, 0xff, 0x27, 0x35, 0x57, 0xff, 0x2f, 0x3f, 0x63, 0xff, 0x35, 0x43, 0x69, 0xff, 0x38, 0x45, 0x6e, 0xff, 0x3d, 0x49, 0x76, 0xff, 0x3a, 0x4a, 0x76, 0xff, 0x31, 0x44, 0x70, 0xff, 0x30, 0x47, 0x72, 0xff, 0x37, 0x4d, 0x76, 0xff, 0x36, 0x4d, 0x76, 0xff, 0x33, 0x47, 0x76, 0xff, 0x35, 0x49, 0x79, 0xff, 0x3a, 0x4e, 0x7e, 0xff, 0x3d, 0x51, 0x81, 0xff, 0x3e, 0x53, 0x82, 0xff, 0x44, 0x5a, 0x8a, 0xff, 0x4a, 0x5e, 0x91, 0xff, 0x4c, 0x60, 0x97, 0xff, 0x55, 0x69, 0xa1, 0xff, 0x5b, 0x70, 0xa6, 0xff, 0x5f, 0x74, 0xac, 0xff, 0x68, 0x7e, 0xb6, 0xff, 0x6b, 0x81, 0xb9, 0xff, 0x69, 0x80, 0xb7, 0xff, 0x6c, 0x87, 0xbe, 0xff, 0x71, 0x8f, 0xc8, 0xff, 0x71, 0x91, 0xcd, 0xff, 0x72, 0x92, 0xcf, 0xff, 0x74, 0x98, 0xd3, 0xff, 0x77, 0x9f, 0xd7, 0xff, 0x84, 0xa4, 0xe1, 0xff, 0x92, 0xaa, 0xe0, 0xff, 0xa1, 0xb6, 0xdb, 0xff, 0xba, 0xc7, 0xec, 0xff, 0xbb, 0xbf, 0xe6, 0xff, 0xc6, 0xc4, 0xd0, 0xff, 0xf6, 0xf6, 0xec, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfe, 0xf7, 0xf7, 0xff, 0xd9, 0xd6, 0xe1, 0xff, 0xb2, 0xb7, 0xcf, 0xff, 0xaf, 0xb7, 0xda, 0xff, 0xb1, 0xba, 0xd9, 0xff, 0xb5, 0xbe, 0xda, 0xff, 0xb7, 0xc0, 0xde, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, 0x4a, 0x4a, 0x8d, 0x5b, 0x58, 0x59, 0xff, 0x6b, 0x68, 0x69, 0xff, 0x72, 0x6d, 0x6e, 0xff, 0x79, 0x73, 0x75, 0xff, 0x7b, 0x76, 0x77, 0xff, 0x80, 0x7b, 0x7d, 0xff, 0x90, 0x8b, 0x8d, 0xff, 0x9e, 0x9a, 0x9c, 0xff, 0xa7, 0xa5, 0xa9, 0xff, 0xb4, 0xb4, 0xb8, 0xff, 0xbc, 0xbd, 0xc0, 0xff, 0xbb, 0xbd, 0xc0, 0xff, 0xb8, 0xb9, 0xbe, 0xff, 0xb5, 0xb7, 0xc0, 0xff, 0xb6, 0xbc, 0xc5, 0xff, 0xb6, 0xbe, 0xc6, 0xff, 0xb1, 0xb7, 0xc1, 0xff, 0xab, 0xb0, 0xbd, 0xff, 0xa4, 0xaa, 0xb9, 0xff, 0x9d, 0xa5, 0xb7, 0xff, 0x94, 0x9e, 0xb2, 0xff, 0x94, 0x9e, 0xb2, 0xff, 0x9c, 0xa5, 0xb8, 0xff, 0x9f, 0xa7, 0xbb, 0xff, 0x9d, 0xa8, 0xbb, 0xff, 0xa4, 0xae, 0xbf, 0xff, 0xae, 0xb5, 0xc6, 0xff, 0xb8, 0xbe, 0xca, 0xff, 0xbf, 0xc3, 0xcd, 0xff, 0xc7, 0xcb, 0xd5, 0xff, 0xc8, 0xcb, 0xd4, 0xff, 0xc7, 0xc8, 0xd2, 0xff, 0xc4, 0xc7, 0xd1, 0xff, 0xc1, 0xc3, 0xce, 0xff, 0xbe, 0xc1, 0xcc, 0xff, 0xbe, 0xc1, 0xcc, 0xff, 0xbd, 0xc0, 0xcb, 0xff, 0xb4, 0xbd, 0xca, 0xff, 0xaf, 0xba, 0xcc, 0xff, 0x82, 0x8d, 0xa4, 0xff, 0x35, 0x3d, 0x50, 0xff, 0x1a, 0x24, 0x3b, 0xff, 0x1b, 0x25, 0x3d, 0xff, 0x2a, 0x34, 0x47, 0xff, 0x20, 0x26, 0x3b, 0xff, 0x0f, 0x17, 0x2a, 0xff, 0x05, 0x11, 0x23, 0xff, 0x06, 0x12, 0x27, 0xff, 0x09, 0x16, 0x2b, 0xff, 0x0c, 0x1c, 0x34, 0xff, 0x10, 0x24, 0x41, 0xff, 0x15, 0x29, 0x48, 0xff, 0x19, 0x2c, 0x4f, 0xff, 0x24, 0x34, 0x5a, 0xff, 0x2a, 0x3b, 0x62, 0xff, 0x31, 0x42, 0x6a, 0xff, 0x35, 0x47, 0x6e, 0xff, 0x36, 0x47, 0x72, 0xff, 0x39, 0x49, 0x77, 0xff, 0x3a, 0x49, 0x78, 0xff, 0x34, 0x46, 0x75, 0xff, 0x31, 0x46, 0x75, 0xff, 0x34, 0x49, 0x76, 0xff, 0x35, 0x4a, 0x77, 0xff, 0x33, 0x47, 0x76, 0xff, 0x35, 0x49, 0x79, 0xff, 0x3b, 0x4f, 0x80, 0xff, 0x40, 0x54, 0x84, 0xff, 0x42, 0x56, 0x86, 0xff, 0x45, 0x5b, 0x8d, 0xff, 0x4b, 0x62, 0x97, 0xff, 0x4d, 0x64, 0x9b, 0xff, 0x51, 0x68, 0x9d, 0xff, 0x55, 0x6b, 0xa3, 0xff, 0x5c, 0x72, 0xac, 0xff, 0x61, 0x7c, 0xb7, 0xff, 0x67, 0x81, 0xbc, 0xff, 0x68, 0x83, 0xbd, 0xff, 0x6a, 0x89, 0xc1, 0xff, 0x6d, 0x8c, 0xc7, 0xff, 0x6d, 0x8c, 0xca, 0xff, 0x72, 0x8f, 0xcd, 0xff, 0x76, 0x97, 0xd3, 0xff, 0x77, 0x9f, 0xd7, 0xff, 0x7b, 0x9e, 0xda, 0xff, 0x87, 0xa3, 0xdb, 0xff, 0x8b, 0xab, 0xdc, 0xff, 0x84, 0xa7, 0xdf, 0xff, 0x9c, 0xae, 0xd9, 0xff, 0xe5, 0xe2, 0xec, 0xff, 0xff, 0xfc, 0xfa, 0xff, 0xfb, 0xf5, 0xf0, 0xff, 0xfc, 0xf7, 0xf5, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xfc, 0xf8, 0xeb, 0xff, 0xd1, 0xd2, 0xdf, 0xff, 0xb3, 0xb9, 0xda, 0xff, 0xac, 0xb6, 0xd5, 0xff, 0xb3, 0xbd, 0xda, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x55, 0x03, 0x5d, 0x5c, 0x5c, 0xb1, 0x6b, 0x6a, 0x6a, 0xff, 0x75, 0x72, 0x74, 0xff, 0x7f, 0x7a, 0x7d, 0xff, 0x83, 0x7e, 0x81, 0xff, 0x8a, 0x87, 0x89, 0xff, 0x98, 0x96, 0x99, 0xff, 0xa8, 0xa5, 0xa6, 0xff, 0xae, 0xad, 0xb0, 0xff, 0xb8, 0xba, 0xbf, 0xff, 0xbe, 0xbf, 0xc4, 0xff, 0xbe, 0xbe, 0xc3, 0xff, 0xbb, 0xbd, 0xc0, 0xff, 0xb8, 0xbb, 0xc2, 0xff, 0xba, 0xbf, 0xc8, 0xff, 0xbc, 0xc1, 0xc8, 0xff, 0xb9, 0xbc, 0xc7, 0xff, 0xb3, 0xb4, 0xc4, 0xff, 0xac, 0xb1, 0xbf, 0xff, 0xa6, 0xae, 0xbd, 0xff, 0x9d, 0xa6, 0xb9, 0xff, 0x9b, 0xa5, 0xb7, 0xff, 0xa1, 0xab, 0xbc, 0xff, 0xa4, 0xae, 0xbf, 0xff, 0xa3, 0xad, 0xc0, 0xff, 0xa6, 0xae, 0xc1, 0xff, 0xae, 0xb5, 0xc3, 0xff, 0xb8, 0xbc, 0xc8, 0xff, 0xbc, 0xc0, 0xca, 0xff, 0xc3, 0xc8, 0xd4, 0xff, 0xc4, 0xc8, 0xd3, 0xff, 0xc3, 0xc6, 0xd0, 0xff, 0xc4, 0xc5, 0xcf, 0xff, 0xc0, 0xc2, 0xcc, 0xff, 0xbb, 0xbf, 0xcb, 0xff, 0xbb, 0xbf, 0xca, 0xff, 0xba, 0xbf, 0xc9, 0xff, 0xb3, 0xbb, 0xc4, 0xff, 0xb5, 0xbc, 0xc7, 0xff, 0xbb, 0xbe, 0xc9, 0xff, 0x6b, 0x68, 0x75, 0xff, 0x25, 0x2c, 0x42, 0xff, 0x1e, 0x2a, 0x3d, 0xff, 0x2b, 0x36, 0x48, 0xff, 0x35, 0x39, 0x4e, 0xff, 0x20, 0x26, 0x3b, 0xff, 0x0d, 0x1c, 0x30, 0xff, 0x0d, 0x1c, 0x34, 0xff, 0x11, 0x22, 0x3b, 0xff, 0x17, 0x2a, 0x48, 0xff, 0x1b, 0x30, 0x51, 0xff, 0x1d, 0x30, 0x55, 0xff, 0x21, 0x34, 0x5a, 0xff, 0x26, 0x38, 0x60, 0xff, 0x29, 0x3a, 0x62, 0xff, 0x2e, 0x3f, 0x66, 0xff, 0x32, 0x46, 0x6c, 0xff, 0x34, 0x46, 0x70, 0xff, 0x35, 0x45, 0x73, 0xff, 0x37, 0x46, 0x76, 0xff, 0x35, 0x47, 0x76, 0xff, 0x31, 0x45, 0x75, 0xff, 0x2e, 0x42, 0x72, 0xff, 0x32, 0x46, 0x76, 0xff, 0x37, 0x4b, 0x79, 0xff, 0x37, 0x4b, 0x7a, 0xff, 0x3b, 0x4f, 0x7f, 0xff, 0x41, 0x55, 0x84, 0xff, 0x45, 0x59, 0x8b, 0xff, 0x47, 0x5c, 0x90, 0xff, 0x49, 0x62, 0x98, 0xff, 0x4a, 0x64, 0x9a, 0xff, 0x4f, 0x67, 0x9a, 0xff, 0x53, 0x6a, 0xa1, 0xff, 0x57, 0x6e, 0xa9, 0xff, 0x59, 0x75, 0xb2, 0xff, 0x60, 0x7d, 0xba, 0xff, 0x67, 0x84, 0xc0, 0xff, 0x6b, 0x8c, 0xc5, 0xff, 0x6c, 0x8d, 0xc9, 0xff, 0x6e, 0x8c, 0xcc, 0xff, 0x73, 0x91, 0xd1, 0xff, 0x72, 0x92, 0xd0, 0xff, 0x72, 0x98, 0xd0, 0xff, 0x7a, 0x9c, 0xd2, 0xff, 0x7f, 0xa1, 0xdb, 0xff, 0x79, 0xa3, 0xe0, 0xff, 0x79, 0x9f, 0xd8, 0xff, 0xc7, 0xd2, 0xeb, 0xff, 0xff, 0xfd, 0xf6, 0xff, 0xfd, 0xf2, 0xec, 0xff, 0xf8, 0xee, 0xec, 0xff, 0xfc, 0xf8, 0xf5, 0xff, 0xf8, 0xf7, 0xf0, 0xff, 0xfc, 0xfa, 0xf2, 0xff, 0xf9, 0xf6, 0xf2, 0xff, 0xe9, 0xe4, 0xe6, 0xff, 0xd2, 0xcf, 0xdf, 0xb1, 0xaa, 0xaa, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x66, 0x66, 0x0a, 0x70, 0x71, 0x71, 0xc8, 0x7b, 0x7a, 0x7b, 0xff, 0x87, 0x85, 0x86, 0xff, 0x8c, 0x8a, 0x8b, 0xff, 0x95, 0x95, 0x96, 0xff, 0xa2, 0xa4, 0xa5, 0xff, 0xac, 0xae, 0xae, 0xff, 0xb4, 0xb6, 0xb7, 0xff, 0xbe, 0xbf, 0xc3, 0xff, 0xbf, 0xc0, 0xc4, 0xff, 0xc1, 0xc1, 0xc6, 0xff, 0xc0, 0xc0, 0xc5, 0xff, 0xbb, 0xbe, 0xc6, 0xff, 0xbd, 0xc3, 0xcc, 0xff, 0xbf, 0xc6, 0xcc, 0xff, 0xbd, 0xc2, 0xcd, 0xff, 0xb9, 0xbb, 0xca, 0xff, 0xb6, 0xb8, 0xc3, 0xff, 0xb1, 0xb6, 0xc2, 0xff, 0xa8, 0xb0, 0xc0, 0xff, 0xa6, 0xad, 0xbd, 0xff, 0xac, 0xb2, 0xc1, 0xff, 0xb0, 0xb7, 0xc6, 0xff, 0xad, 0xb4, 0xc5, 0xff, 0xaf, 0xb5, 0xc4, 0xff, 0xb6, 0xb8, 0xc3, 0xff, 0xbb, 0xbe, 0xc8, 0xff, 0xbc, 0xc0, 0xcb, 0xff, 0xc1, 0xc6, 0xd1, 0xff, 0xc3, 0xc7, 0xd1, 0xff, 0xc5, 0xc6, 0xd0, 0xff, 0xc4, 0xc5, 0xcf, 0xff, 0xbf, 0xc1, 0xcc, 0xff, 0xbc, 0xbf, 0xcb, 0xff, 0xbb, 0xbe, 0xcc, 0xff, 0xb3, 0xb9, 0xc5, 0xff, 0xb7, 0xba, 0xbc, 0xff, 0xde, 0xda, 0xd9, 0xff, 0xff, 0xfb, 0xf9, 0xff, 0xbe, 0xb2, 0xb5, 0xff, 0x3b, 0x3a, 0x4d, 0xff, 0x22, 0x2a, 0x41, 0xff, 0x29, 0x31, 0x45, 0xff, 0x42, 0x41, 0x57, 0xff, 0x41, 0x42, 0x55, 0xff, 0x19, 0x27, 0x3f, 0xff, 0x0c, 0x1c, 0x3b, 0xff, 0x15, 0x28, 0x48, 0xff, 0x1c, 0x30, 0x52, 0xff, 0x23, 0x36, 0x59, 0xff, 0x27, 0x37, 0x5e, 0xff, 0x2a, 0x3a, 0x62, 0xff, 0x2b, 0x3c, 0x63, 0xff, 0x29, 0x3a, 0x61, 0xff, 0x2b, 0x3c, 0x62, 0xff, 0x30, 0x43, 0x6a, 0xff, 0x32, 0x43, 0x6d, 0xff, 0x35, 0x44, 0x71, 0xff, 0x37, 0x44, 0x74, 0xff, 0x33, 0x45, 0x74, 0xff, 0x2f, 0x43, 0x72, 0xff, 0x2d, 0x41, 0x70, 0xff, 0x32, 0x46, 0x75, 0xff, 0x36, 0x4a, 0x79, 0xff, 0x37, 0x4b, 0x7b, 0xff, 0x39, 0x4c, 0x7d, 0xff, 0x3f, 0x52, 0x82, 0xff, 0x46, 0x5a, 0x8b, 0xff, 0x48, 0x5d, 0x91, 0xff, 0x49, 0x62, 0x98, 0xff, 0x4a, 0x63, 0x99, 0xff, 0x4f, 0x66, 0x9a, 0xff, 0x52, 0x67, 0x9e, 0xff, 0x55, 0x6b, 0xa6, 0xff, 0x58, 0x73, 0xb0, 0xff, 0x5f, 0x7a, 0xb7, 0xff, 0x64, 0x80, 0xba, 0xff, 0x69, 0x8b, 0xc1, 0xff, 0x6d, 0x8e, 0xc9, 0xff, 0x70, 0x8e, 0xce, 0xff, 0x74, 0x91, 0xd2, 0xff, 0x6f, 0x92, 0xcf, 0xff, 0x6e, 0x95, 0xcb, 0xff, 0x75, 0x98, 0xcd, 0xff, 0x70, 0x9a, 0xd7, 0xff, 0x67, 0x94, 0xce, 0xff, 0xa6, 0xbf, 0xdb, 0xff, 0xf9, 0xf5, 0xf0, 0xff, 0xff, 0xf2, 0xe6, 0xff, 0xf4, 0xe9, 0xe6, 0xff, 0xf6, 0xee, 0xeb, 0xff, 0xf7, 0xf3, 0xef, 0xff, 0xed, 0xeb, 0xe7, 0xff, 0xef, 0xee, 0xe9, 0xff, 0xfb, 0xf8, 0xef, 0xff, 0xff, 0xff, 0xf3, 0xc8, 0xff, 0xff, 0xff, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0x85, 0x85, 0x15, 0x88, 0x89, 0x8d, 0xd7, 0x94, 0x95, 0x99, 0xff, 0x9b, 0x9b, 0x9f, 0xff, 0x9e, 0xa1, 0xa3, 0xff, 0xa9, 0xad, 0xaf, 0xff, 0xb2, 0xb6, 0xb8, 0xff, 0xbb, 0xbe, 0xc1, 0xff, 0xc2, 0xc4, 0xc8, 0xff, 0xc3, 0xc5, 0xca, 0xff, 0xc4, 0xc6, 0xc9, 0xff, 0xc2, 0xc4, 0xc6, 0xff, 0xc0, 0xc3, 0xc8, 0xff, 0xc2, 0xc5, 0xce, 0xff, 0xc6, 0xc8, 0xd2, 0xff, 0xc4, 0xc9, 0xd3, 0xff, 0xbf, 0xc4, 0xd0, 0xff, 0xbd, 0xc1, 0xca, 0xff, 0xba, 0xbf, 0xc8, 0xff, 0xb5, 0xba, 0xc5, 0xff, 0xb2, 0xb6, 0xc4, 0xff, 0xb4, 0xb8, 0xc7, 0xff, 0xb8, 0xbc, 0xca, 0xff, 0xb6, 0xbb, 0xc8, 0xff, 0xb6, 0xba, 0xc6, 0xff, 0xba, 0xbc, 0xc8, 0xff, 0xbc, 0xc0, 0xcb, 0xff, 0xbd, 0xc3, 0xcd, 0xff, 0xc4, 0xc6, 0xd1, 0xff, 0xc4, 0xc7, 0xd1, 0xff, 0xc1, 0xc5, 0xcf, 0xff, 0xc2, 0xc5, 0xcf, 0xff, 0xbd, 0xc1, 0xca, 0xff, 0xb7, 0xc0, 0xc7, 0xff, 0xb3, 0xbc, 0xc3, 0xff, 0xbb, 0xba, 0xc0, 0xff, 0xdb, 0xd8, 0xd5, 0xff, 0xff, 0xf8, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xd5, 0xd5, 0xff, 0x56, 0x54, 0x66, 0xff, 0x29, 0x2f, 0x47, 0xff, 0x29, 0x2e, 0x44, 0xff, 0x39, 0x3c, 0x51, 0xff, 0x4f, 0x4f, 0x61, 0xff, 0x38, 0x3e, 0x56, 0xff, 0x19, 0x29, 0x49, 0xff, 0x17, 0x2a, 0x4d, 0xff, 0x1c, 0x2d, 0x52, 0xff, 0x1f, 0x31, 0x57, 0xff, 0x26, 0x38, 0x62, 0xff, 0x29, 0x3b, 0x66, 0xff, 0x2a, 0x3c, 0x62, 0xff, 0x27, 0x3a, 0x60, 0xff, 0x28, 0x3a, 0x60, 0xff, 0x2f, 0x41, 0x69, 0xff, 0x31, 0x44, 0x6f, 0xff, 0x30, 0x42, 0x71, 0xff, 0x31, 0x43, 0x72, 0xff, 0x2f, 0x41, 0x70, 0xff, 0x2c, 0x40, 0x6f, 0xff, 0x2e, 0x42, 0x71, 0xff, 0x31, 0x45, 0x74, 0xff, 0x33, 0x47, 0x76, 0xff, 0x37, 0x4b, 0x7b, 0xff, 0x37, 0x4c, 0x7c, 0xff, 0x3a, 0x4f, 0x7f, 0xff, 0x45, 0x5c, 0x8c, 0xff, 0x48, 0x5e, 0x90, 0xff, 0x48, 0x5f, 0x93, 0xff, 0x47, 0x5f, 0x96, 0xff, 0x49, 0x65, 0x9b, 0xff, 0x4c, 0x67, 0x9e, 0xff, 0x50, 0x6a, 0xa3, 0xff, 0x56, 0x74, 0xad, 0xff, 0x5d, 0x7a, 0xb5, 0xff, 0x5f, 0x7b, 0xb7, 0xff, 0x60, 0x7e, 0xba, 0xff, 0x65, 0x84, 0xc2, 0xff, 0x6a, 0x8b, 0xca, 0xff, 0x72, 0x8f, 0xd0, 0xff, 0x7b, 0x95, 0xd2, 0xff, 0x74, 0x95, 0xd2, 0xff, 0x75, 0x96, 0xd0, 0xff, 0x5a, 0x86, 0xcb, 0xff, 0x84, 0x9e, 0xcd, 0xff, 0xf2, 0xeb, 0xea, 0xff, 0xfd, 0xf1, 0xe3, 0xff, 0xf2, 0xe9, 0xe4, 0xff, 0xf4, 0xef, 0xef, 0xff, 0xf6, 0xef, 0xec, 0xff, 0xed, 0xe8, 0xe5, 0xff, 0xed, 0xe8, 0xe5, 0xff, 0xf4, 0xed, 0xe9, 0xff, 0xf5, 0xf3, 0xee, 0xd8, 0xf2, 0xf2, 0xf2, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x91, 0x9a, 0x9a, 0x1c, 0x9e, 0xa3, 0xa6, 0xdc, 0xa5, 0xa9, 0xad, 0xff, 0xa7, 0xab, 0xaf, 0xff, 0xb2, 0xb4, 0xb9, 0xff, 0xba, 0xbd, 0xc2, 0xff, 0xc0, 0xc3, 0xc8, 0xff, 0xc4, 0xc7, 0xcb, 0xff, 0xc5, 0xc9, 0xcd, 0xff, 0xc5, 0xc9, 0xcc, 0xff, 0xc4, 0xc9, 0xca, 0xff, 0xc5, 0xc8, 0xcd, 0xff, 0xc8, 0xc8, 0xd2, 0xff, 0xca, 0xc9, 0xd5, 0xff, 0xc8, 0xcc, 0xd5, 0xff, 0xc3, 0xca, 0xd3, 0xff, 0xc0, 0xc6, 0xce, 0xff, 0xbe, 0xc3, 0xcc, 0xff, 0xbc, 0xc1, 0xc9, 0xff, 0xbb, 0xbf, 0xca, 0xff, 0xbb, 0xbe, 0xcc, 0xff, 0xbc, 0xbf, 0xcc, 0xff, 0xbc, 0xc0, 0xca, 0xff, 0xbc, 0xc1, 0xc9, 0xff, 0xbe, 0xc3, 0xcd, 0xff, 0xbe, 0xc4, 0xce, 0xff, 0xc1, 0xc7, 0xd0, 0xff, 0xc7, 0xc7, 0xd1, 0xff, 0xc6, 0xc7, 0xd1, 0xff, 0xc1, 0xc7, 0xcf, 0xff, 0xc1, 0xc5, 0xce, 0xff, 0xb7, 0xbb, 0xc3, 0xff, 0xaf, 0xb9, 0xba, 0xff, 0xb8, 0xc0, 0xbe, 0xff, 0xdd, 0xd7, 0xd4, 0xff, 0xf9, 0xf4, 0xef, 0xff, 0xff, 0xfa, 0xf6, 0xff, 0xff, 0xf6, 0xf2, 0xff, 0xd2, 0xc9, 0xcd, 0xff, 0x5d, 0x5e, 0x71, 0xff, 0x36, 0x3a, 0x52, 0xff, 0x2f, 0x31, 0x4a, 0xff, 0x28, 0x33, 0x4a, 0xff, 0x41, 0x45, 0x57, 0xff, 0x50, 0x4e, 0x65, 0xff, 0x34, 0x3f, 0x5c, 0xff, 0x23, 0x32, 0x54, 0xff, 0x23, 0x2f, 0x55, 0xff, 0x22, 0x34, 0x5a, 0xff, 0x28, 0x3a, 0x63, 0xff, 0x28, 0x3c, 0x65, 0xff, 0x27, 0x3b, 0x60, 0xff, 0x27, 0x3b, 0x60, 0xff, 0x28, 0x3b, 0x61, 0xff, 0x2f, 0x3f, 0x69, 0xff, 0x31, 0x44, 0x71, 0xff, 0x2d, 0x42, 0x72, 0xff, 0x2d, 0x42, 0x71, 0xff, 0x2b, 0x3f, 0x6e, 0xff, 0x2c, 0x40, 0x6f, 0xff, 0x2e, 0x42, 0x71, 0xff, 0x30, 0x44, 0x73, 0xff, 0x33, 0x47, 0x75, 0xff, 0x37, 0x4a, 0x7a, 0xff, 0x35, 0x4a, 0x7a, 0xff, 0x34, 0x4c, 0x7c, 0xff, 0x3e, 0x58, 0x86, 0xff, 0x44, 0x5b, 0x8b, 0xff, 0x47, 0x5c, 0x8e, 0xff, 0x49, 0x61, 0x98, 0xff, 0x48, 0x69, 0xa1, 0xff, 0x4a, 0x69, 0xa0, 0xff, 0x4b, 0x6a, 0xa1, 0xff, 0x51, 0x70, 0xa7, 0xff, 0x58, 0x77, 0xb1, 0xff, 0x5b, 0x78, 0xb6, 0xff, 0x5c, 0x77, 0xb8, 0xff, 0x61, 0x7e, 0xbf, 0xff, 0x69, 0x8b, 0xca, 0xff, 0x6d, 0x93, 0xd0, 0xff, 0x79, 0x97, 0xd5, 0xff, 0x78, 0x9a, 0xd9, 0xff, 0x5a, 0x88, 0xd1, 0xff, 0x7c, 0x96, 0xca, 0xff, 0xde, 0xd8, 0xe5, 0xff, 0xff, 0xf0, 0xec, 0xff, 0xf1, 0xe5, 0xe3, 0xff, 0xef, 0xe9, 0xe7, 0xff, 0xf2, 0xed, 0xea, 0xff, 0xec, 0xe7, 0xe4, 0xff, 0xe7, 0xe3, 0xe0, 0xff, 0xf0, 0xe9, 0xe6, 0xff, 0xf8, 0xed, 0xeb, 0xdd, 0xf5, 0xec, 0xec, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xb1, 0xb1, 0x21, 0xb2, 0xb5, 0xb8, 0xde, 0xb6, 0xb9, 0xbd, 0xff, 0xbc, 0xbf, 0xc3, 0xff, 0xc2, 0xc5, 0xc9, 0xff, 0xc7, 0xca, 0xce, 0xff, 0xc9, 0xcc, 0xd0, 0xff, 0xc9, 0xcc, 0xd0, 0xff, 0xc8, 0xcc, 0xce, 0xff, 0xc7, 0xcb, 0xcc, 0xff, 0xc7, 0xca, 0xcf, 0xff, 0xcb, 0xcc, 0xd5, 0xff, 0xcb, 0xcb, 0xd7, 0xff, 0xc6, 0xca, 0xd4, 0xff, 0xc4, 0xcb, 0xd5, 0xff, 0xc3, 0xc9, 0xd3, 0xff, 0xc2, 0xc7, 0xd0, 0xff, 0xc1, 0xc7, 0xd0, 0xff, 0xc2, 0xc7, 0xcf, 0xff, 0xc1, 0xc7, 0xcf, 0xff, 0xc0, 0xc6, 0xce, 0xff, 0xc1, 0xc7, 0xcd, 0xff, 0xc0, 0xc7, 0xcd, 0xff, 0xc1, 0xc6, 0xcd, 0xff, 0xc2, 0xc9, 0xd1, 0xff, 0xc3, 0xc9, 0xd4, 0xff, 0xc6, 0xc6, 0xd2, 0xff, 0xc6, 0xc7, 0xcf, 0xff, 0xc4, 0xc8, 0xca, 0xff, 0xb9, 0xbb, 0xc1, 0xff, 0xb6, 0xb9, 0xbd, 0xff, 0xcd, 0xc9, 0xca, 0xff, 0xe5, 0xdd, 0xdc, 0xff, 0xf4, 0xf3, 0xee, 0xff, 0xf9, 0xf8, 0xf4, 0xff, 0xf8, 0xf4, 0xf0, 0xff, 0xf7, 0xed, 0xe8, 0xff, 0xb7, 0xb3, 0xbc, 0xff, 0x53, 0x57, 0x70, 0xff, 0x43, 0x45, 0x5b, 0xff, 0x36, 0x3b, 0x55, 0xff, 0x22, 0x2f, 0x4c, 0xff, 0x32, 0x3a, 0x50, 0xff, 0x4e, 0x49, 0x60, 0xff, 0x45, 0x4a, 0x63, 0xff, 0x32, 0x3b, 0x5b, 0xff, 0x2d, 0x35, 0x59, 0xff, 0x2a, 0x38, 0x5c, 0xff, 0x2b, 0x3c, 0x62, 0xff, 0x29, 0x3c, 0x62, 0xff, 0x27, 0x3b, 0x61, 0xff, 0x27, 0x3b, 0x60, 0xff, 0x27, 0x3a, 0x60, 0xff, 0x2e, 0x3f, 0x68, 0xff, 0x30, 0x43, 0x70, 0xff, 0x2c, 0x41, 0x71, 0xff, 0x2c, 0x41, 0x70, 0xff, 0x2c, 0x40, 0x6f, 0xff, 0x2e, 0x42, 0x71, 0xff, 0x30, 0x44, 0x73, 0xff, 0x30, 0x44, 0x73, 0xff, 0x31, 0x45, 0x74, 0xff, 0x34, 0x48, 0x77, 0xff, 0x35, 0x4a, 0x7a, 0xff, 0x35, 0x4d, 0x7d, 0xff, 0x3a, 0x52, 0x81, 0xff, 0x3e, 0x55, 0x85, 0xff, 0x46, 0x5b, 0x8e, 0xff, 0x4a, 0x62, 0x99, 0xff, 0x49, 0x6a, 0xa1, 0xff, 0x4b, 0x6a, 0xa0, 0xff, 0x4c, 0x6c, 0xa2, 0xff, 0x50, 0x6f, 0xa6, 0xff, 0x53, 0x72, 0xab, 0xff, 0x58, 0x74, 0xb1, 0xff, 0x5d, 0x77, 0xb8, 0xff, 0x60, 0x7f, 0xc0, 0xff, 0x6b, 0x8c, 0xcb, 0xff, 0x6c, 0x92, 0xcc, 0xff, 0x6a, 0x92, 0xd5, 0xff, 0x68, 0x8f, 0xcc, 0xff, 0x76, 0x9b, 0xce, 0xff, 0xdf, 0xdd, 0xe1, 0xff, 0xff, 0xf5, 0xe2, 0xff, 0xeb, 0xe1, 0xe1, 0xff, 0xef, 0xe3, 0xe2, 0xff, 0xf3, 0xed, 0xea, 0xff, 0xec, 0xe8, 0xe5, 0xff, 0xe7, 0xe2, 0xdf, 0xff, 0xe9, 0xe5, 0xe2, 0xff, 0xee, 0xe9, 0xe5, 0xde, 0xf7, 0xe7, 0xe7, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xba, 0xba, 0xc4, 0x1a, 0xbd, 0xc1, 0xc4, 0xd7, 0xc1, 0xc4, 0xc8, 0xff, 0xc4, 0xc7, 0xcb, 0xff, 0xc8, 0xcb, 0xcf, 0xff, 0xcb, 0xce, 0xd2, 0xff, 0xca, 0xcd, 0xd1, 0xff, 0xca, 0xcd, 0xd1, 0xff, 0xcb, 0xcf, 0xd1, 0xff, 0xcc, 0xcf, 0xd3, 0xff, 0xce, 0xd0, 0xd7, 0xff, 0xcd, 0xcf, 0xd6, 0xff, 0xc9, 0xcc, 0xd4, 0xff, 0xc8, 0xcd, 0xd4, 0xff, 0xc9, 0xce, 0xd5, 0xff, 0xc8, 0xcc, 0xd3, 0xff, 0xc7, 0xca, 0xd3, 0xff, 0xc6, 0xca, 0xd3, 0xff, 0xc6, 0xca, 0xd2, 0xff, 0xc5, 0xc9, 0xd1, 0xff, 0xc5, 0xca, 0xd1, 0xff, 0xc4, 0xcb, 0xd0, 0xff, 0xc4, 0xcb, 0xce, 0xff, 0xc4, 0xcb, 0xd1, 0xff, 0xc5, 0xca, 0xd2, 0xff, 0xc3, 0xc7, 0xd0, 0xff, 0xc0, 0xc0, 0xc7, 0xff, 0xbc, 0xb9, 0xbd, 0xff, 0xb9, 0xb7, 0xbc, 0xff, 0xce, 0xcd, 0xcf, 0xff, 0xee, 0xe7, 0xe7, 0xff, 0xf9, 0xf0, 0xef, 0xff, 0xf5, 0xf5, 0xf1, 0xff, 0xf6, 0xf3, 0xef, 0xff, 0xf6, 0xee, 0xe9, 0xff, 0xec, 0xe3, 0xe1, 0xff, 0x9e, 0x9e, 0xb1, 0xff, 0x4e, 0x56, 0x71, 0xff, 0x46, 0x4a, 0x5e, 0xff, 0x3d, 0x41, 0x59, 0xff, 0x29, 0x33, 0x4f, 0xff, 0x29, 0x31, 0x4c, 0xff, 0x3e, 0x3a, 0x55, 0xff, 0x45, 0x44, 0x5e, 0xff, 0x3a, 0x3f, 0x5d, 0xff, 0x32, 0x39, 0x59, 0xff, 0x2b, 0x37, 0x58, 0xff, 0x2a, 0x38, 0x5c, 0xff, 0x27, 0x37, 0x5c, 0xff, 0x25, 0x37, 0x5c, 0xff, 0x23, 0x37, 0x5c, 0xff, 0x26, 0x39, 0x60, 0xff, 0x2d, 0x3e, 0x68, 0xff, 0x2e, 0x41, 0x6e, 0xff, 0x2c, 0x40, 0x70, 0xff, 0x2b, 0x40, 0x6f, 0xff, 0x2c, 0x40, 0x6f, 0xff, 0x2e, 0x42, 0x71, 0xff, 0x31, 0x45, 0x74, 0xff, 0x31, 0x45, 0x74, 0xff, 0x31, 0x45, 0x74, 0xff, 0x34, 0x48, 0x78, 0xff, 0x37, 0x4b, 0x7b, 0xff, 0x37, 0x4f, 0x7f, 0xff, 0x3a, 0x52, 0x82, 0xff, 0x3e, 0x54, 0x84, 0xff, 0x44, 0x5a, 0x8c, 0xff, 0x47, 0x5f, 0x94, 0xff, 0x4a, 0x64, 0x9d, 0xff, 0x4c, 0x67, 0xa0, 0xff, 0x4e, 0x6b, 0xa3, 0xff, 0x4f, 0x6c, 0xa4, 0xff, 0x4d, 0x6b, 0xa6, 0xff, 0x54, 0x72, 0xb0, 0xff, 0x5e, 0x7c, 0xbb, 0xff, 0x66, 0x81, 0xbc, 0xff, 0x69, 0x87, 0xc2, 0xff, 0x66, 0x8e, 0xcb, 0xff, 0x59, 0x81, 0xc6, 0xff, 0x70, 0x8a, 0xc2, 0xff, 0xd7, 0xd7, 0xdd, 0xff, 0xff, 0xf3, 0xe2, 0xff, 0xec, 0xe5, 0xdc, 0xff, 0xeb, 0xe4, 0xe0, 0xff, 0xf2, 0xe7, 0xe3, 0xff, 0xee, 0xe7, 0xe4, 0xff, 0xe7, 0xe1, 0xde, 0xff, 0xe8, 0xe2, 0xdf, 0xff, 0xe9, 0xe3, 0xe1, 0xd7, 0xec, 0xe2, 0xe2, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc5, 0xc5, 0xd0, 0x16, 0xc6, 0xc8, 0xcc, 0xca, 0xc9, 0xcc, 0xd0, 0xff, 0xca, 0xcd, 0xd1, 0xff, 0xca, 0xcd, 0xd1, 0xff, 0xcb, 0xce, 0xd2, 0xff, 0xcc, 0xcf, 0xd3, 0xff, 0xcd, 0xd0, 0xd4, 0xff, 0xce, 0xd1, 0xd5, 0xff, 0xcf, 0xd2, 0xd5, 0xff, 0xd0, 0xd3, 0xd6, 0xff, 0xcf, 0xd2, 0xd5, 0xff, 0xce, 0xd0, 0xd4, 0xff, 0xcd, 0xd0, 0xd3, 0xff, 0xcd, 0xcf, 0xd4, 0xff, 0xcc, 0xcd, 0xd7, 0xff, 0xc9, 0xc9, 0xd5, 0xff, 0xc9, 0xca, 0xd4, 0xff, 0xca, 0xca, 0xd4, 0xff, 0xc8, 0xca, 0xd5, 0xff, 0xc7, 0xcc, 0xd4, 0xff, 0xc6, 0xce, 0xd1, 0xff, 0xc8, 0xcc, 0xcf, 0xff, 0xc6, 0xc8, 0xcc, 0xff, 0xbe, 0xc4, 0xc6, 0xff, 0xbe, 0xbe, 0xc3, 0xff, 0xcd, 0xc4, 0xca, 0xff, 0xdd, 0xd7, 0xd8, 0xff, 0xee, 0xe9, 0xe9, 0xff, 0xf5, 0xf4, 0xf2, 0xff, 0xf4, 0xf3, 0xf2, 0xff, 0xf0, 0xee, 0xef, 0xff, 0xf5, 0xed, 0xeb, 0xff, 0xf9, 0xec, 0xe6, 0xff, 0xe5, 0xdb, 0xdd, 0xff, 0x95, 0x97, 0xb2, 0xff, 0x50, 0x5b, 0x75, 0xff, 0x45, 0x4c, 0x60, 0xff, 0x44, 0x44, 0x5b, 0xff, 0x35, 0x37, 0x50, 0xff, 0x24, 0x2c, 0x48, 0xff, 0x2a, 0x2d, 0x4c, 0xff, 0x38, 0x38, 0x56, 0xff, 0x33, 0x38, 0x54, 0xff, 0x2b, 0x34, 0x50, 0xff, 0x28, 0x31, 0x51, 0xff, 0x24, 0x2f, 0x52, 0xff, 0x24, 0x31, 0x56, 0xff, 0x24, 0x33, 0x56, 0xff, 0x21, 0x33, 0x59, 0xff, 0x26, 0x38, 0x61, 0xff, 0x29, 0x3a, 0x64, 0xff, 0x2a, 0x3d, 0x69, 0xff, 0x2a, 0x3e, 0x6d, 0xff, 0x2d, 0x41, 0x70, 0xff, 0x2d, 0x41, 0x70, 0xff, 0x2f, 0x43, 0x72, 0xff, 0x31, 0x45, 0x74, 0xff, 0x31, 0x45, 0x74, 0xff, 0x31, 0x45, 0x73, 0xff, 0x32, 0x46, 0x75, 0xff, 0x37, 0x4c, 0x7c, 0xff, 0x39, 0x50, 0x80, 0xff, 0x37, 0x4f, 0x7f, 0xff, 0x3b, 0x51, 0x81, 0xff, 0x41, 0x58, 0x86, 0xff, 0x46, 0x5c, 0x8d, 0xff, 0x4d, 0x5f, 0x98, 0xff, 0x4b, 0x62, 0x9c, 0xff, 0x4c, 0x65, 0xa1, 0xff, 0x4b, 0x66, 0xa0, 0xff, 0x4c, 0x68, 0xa6, 0xff, 0x54, 0x75, 0xb4, 0xff, 0x54, 0x79, 0xb8, 0xff, 0x5f, 0x7e, 0xbf, 0xff, 0x5d, 0x84, 0xc9, 0xff, 0x4b, 0x77, 0xbe, 0xff, 0x64, 0x84, 0xb8, 0xff, 0xc6, 0xcb, 0xdc, 0xff, 0xff, 0xf2, 0xe9, 0xff, 0xf0, 0xe6, 0xdc, 0xff, 0xe8, 0xe0, 0xda, 0xff, 0xef, 0xe8, 0xdf, 0xff, 0xed, 0xe6, 0xdd, 0xff, 0xea, 0xe0, 0xdc, 0xff, 0xea, 0xe0, 0xde, 0xff, 0xea, 0xe0, 0xde, 0xca, 0xe7, 0xe7, 0xdc, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd0, 0xd0, 0xd0, 0x0b, 0xcc, 0xce, 0xd3, 0xaf, 0xcd, 0xd0, 0xd4, 0xff, 0xcd, 0xd0, 0xd4, 0xff, 0xce, 0xd1, 0xd5, 0xff, 0xcf, 0xd2, 0xd6, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xcf, 0xd2, 0xd6, 0xff, 0xcf, 0xd2, 0xd6, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xce, 0xd1, 0xd5, 0xff, 0xcd, 0xd0, 0xd6, 0xff, 0xce, 0xd0, 0xda, 0xff, 0xce, 0xce, 0xd9, 0xff, 0xcc, 0xcd, 0xd8, 0xff, 0xca, 0xcb, 0xd6, 0xff, 0xcc, 0xcd, 0xd7, 0xff, 0xcb, 0xcc, 0xd5, 0xff, 0xc5, 0xc8, 0xce, 0xff, 0xc5, 0xc6, 0xc9, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xca, 0xcc, 0xcc, 0xff, 0xdb, 0xda, 0xdb, 0xff, 0xf3, 0xed, 0xef, 0xff, 0xfb, 0xf7, 0xf4, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xf4, 0xf2, 0xf2, 0xff, 0xec, 0xea, 0xea, 0xff, 0xea, 0xe9, 0xe9, 0xff, 0xef, 0xe8, 0xe5, 0xff, 0xf3, 0xe7, 0xe1, 0xff, 0xe0, 0xd5, 0xd8, 0xff, 0x97, 0x97, 0xb2, 0xff, 0x53, 0x5e, 0x77, 0xff, 0x42, 0x4b, 0x5d, 0xff, 0x45, 0x46, 0x5d, 0xff, 0x39, 0x3d, 0x56, 0xff, 0x27, 0x2c, 0x49, 0xff, 0x1f, 0x27, 0x44, 0xff, 0x24, 0x2c, 0x49, 0xff, 0x26, 0x2a, 0x47, 0xff, 0x24, 0x2a, 0x48, 0xff, 0x20, 0x29, 0x4b, 0xff, 0x1e, 0x2a, 0x4e, 0xff, 0x20, 0x2d, 0x53, 0xff, 0x22, 0x30, 0x55, 0xff, 0x1e, 0x30, 0x56, 0xff, 0x21, 0x34, 0x5c, 0xff, 0x26, 0x36, 0x61, 0xff, 0x28, 0x3b, 0x68, 0xff, 0x29, 0x3e, 0x6e, 0xff, 0x2d, 0x41, 0x70, 0xff, 0x2d, 0x41, 0x70, 0xff, 0x2f, 0x43, 0x72, 0xff, 0x31, 0x45, 0x74, 0xff, 0x2f, 0x42, 0x71, 0xff, 0x2c, 0x3f, 0x6e, 0xff, 0x2f, 0x42, 0x72, 0xff, 0x36, 0x4a, 0x7b, 0xff, 0x36, 0x4e, 0x7e, 0xff, 0x35, 0x4d, 0x7d, 0xff, 0x38, 0x4f, 0x7f, 0xff, 0x3b, 0x51, 0x81, 0xff, 0x3d, 0x54, 0x84, 0xff, 0x43, 0x59, 0x8d, 0xff, 0x43, 0x5d, 0x94, 0xff, 0x43, 0x5f, 0x98, 0xff, 0x46, 0x61, 0x9b, 0xff, 0x4a, 0x66, 0xa3, 0xff, 0x55, 0x73, 0xb0, 0xff, 0x50, 0x6f, 0xb4, 0xff, 0x49, 0x6f, 0xbc, 0xff, 0x46, 0x71, 0xb3, 0xff, 0x73, 0x8a, 0xb6, 0xff, 0xcf, 0xd1, 0xdf, 0xff, 0xff, 0xf8, 0xec, 0xff, 0xf2, 0xe9, 0xdd, 0xff, 0xe5, 0xdd, 0xd7, 0xff, 0xeb, 0xe3, 0xdb, 0xff, 0xea, 0xe3, 0xd9, 0xff, 0xe7, 0xdf, 0xd7, 0xff, 0xe5, 0xdc, 0xd8, 0xff, 0xe9, 0xe0, 0xdd, 0xb0, 0xe7, 0xe7, 0xe7, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xaa, 0xff, 0x03, 0xcf, 0xd2, 0xd6, 0x8b, 0xd0, 0xd3, 0xd7, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xd2, 0xd5, 0xd9, 0xff, 0xd3, 0xd6, 0xda, 0xff, 0xd1, 0xd4, 0xd8, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xd2, 0xd5, 0xd9, 0xff, 0xd3, 0xd6, 0xda, 0xff, 0xd1, 0xd4, 0xd8, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xce, 0xce, 0xd7, 0xff, 0xce, 0xd0, 0xd9, 0xff, 0xcd, 0xcf, 0xd8, 0xff, 0xc9, 0xcb, 0xd3, 0xff, 0xca, 0xc9, 0xd4, 0xff, 0xcb, 0xc7, 0xd2, 0xff, 0xca, 0xca, 0xcd, 0xff, 0xd2, 0xd3, 0xd3, 0xff, 0xdf, 0xde, 0xdd, 0xff, 0xec, 0xec, 0xeb, 0xff, 0xf9, 0xf7, 0xf7, 0xff, 0xff, 0xfb, 0xfb, 0xff, 0xfb, 0xf8, 0xf5, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xe6, 0xe4, 0xe2, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xeb, 0xe4, 0xe1, 0xff, 0xf1, 0xe5, 0xdf, 0xff, 0xda, 0xd1, 0xd3, 0xff, 0x95, 0x97, 0xb1, 0xff, 0x53, 0x5f, 0x7b, 0xff, 0x42, 0x49, 0x5e, 0xff, 0x45, 0x48, 0x5e, 0xff, 0x3f, 0x42, 0x5b, 0xff, 0x2f, 0x34, 0x4f, 0xff, 0x1c, 0x29, 0x44, 0xff, 0x1a, 0x29, 0x44, 0xff, 0x23, 0x27, 0x44, 0xff, 0x23, 0x28, 0x46, 0xff, 0x1d, 0x28, 0x48, 0xff, 0x1e, 0x2a, 0x4b, 0xff, 0x1f, 0x2c, 0x50, 0xff, 0x1e, 0x2f, 0x52, 0xff, 0x1d, 0x2f, 0x54, 0xff, 0x21, 0x33, 0x5c, 0xff, 0x27, 0x39, 0x62, 0xff, 0x2a, 0x3d, 0x69, 0xff, 0x2b, 0x40, 0x6f, 0xff, 0x2b, 0x3f, 0x6e, 0xff, 0x2a, 0x3e, 0x6d, 0xff, 0x2c, 0x40, 0x6f, 0xff, 0x32, 0x45, 0x75, 0xff, 0x31, 0x46, 0x75, 0xff, 0x2d, 0x42, 0x71, 0xff, 0x2e, 0x44, 0x73, 0xff, 0x33, 0x49, 0x78, 0xff, 0x34, 0x4c, 0x7b, 0xff, 0x39, 0x51, 0x80, 0xff, 0x3b, 0x53, 0x83, 0xff, 0x35, 0x4d, 0x7d, 0xff, 0x32, 0x4b, 0x7a, 0xff, 0x39, 0x52, 0x81, 0xff, 0x40, 0x59, 0x8a, 0xff, 0x46, 0x5e, 0x90, 0xff, 0x47, 0x61, 0x98, 0xff, 0x49, 0x67, 0xa8, 0xff, 0x46, 0x69, 0xac, 0xff, 0x3a, 0x5d, 0x9f, 0xff, 0x48, 0x62, 0x9e, 0xff, 0x94, 0x9f, 0xbc, 0xff, 0xed, 0xe9, 0xe4, 0xff, 0xff, 0xf6, 0xe6, 0xff, 0xef, 0xdf, 0xd8, 0xff, 0xdd, 0xd5, 0xd1, 0xff, 0xe3, 0xdb, 0xd5, 0xff, 0xee, 0xe5, 0xe0, 0xff, 0xe8, 0xe1, 0xd9, 0xff, 0xe9, 0xe1, 0xda, 0xff, 0xea, 0xe1, 0xdd, 0x8b, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd3, 0xd5, 0xd8, 0x57, 0xd0, 0xd3, 0xd8, 0xf3, 0xd1, 0xd4, 0xd8, 0xff, 0xd2, 0xd5, 0xd9, 0xff, 0xd2, 0xd5, 0xd9, 0xff, 0xd2, 0xd5, 0xd9, 0xff, 0xd1, 0xd4, 0xd8, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xd0, 0xd4, 0xd8, 0xff, 0xd3, 0xd4, 0xd8, 0xff, 0xd4, 0xd2, 0xd6, 0xff, 0xcd, 0xce, 0xd1, 0xff, 0xc6, 0xc8, 0xcc, 0xff, 0xc2, 0xc5, 0xc8, 0xff, 0xcb, 0xc7, 0xd0, 0xff, 0xd8, 0xd2, 0xd9, 0xff, 0xe6, 0xe5, 0xe2, 0xff, 0xf3, 0xf1, 0xed, 0xff, 0xf7, 0xf5, 0xf4, 0xff, 0xfa, 0xf8, 0xf7, 0xff, 0xf9, 0xf7, 0xf6, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf1, 0xee, 0xed, 0xff, 0xed, 0xea, 0xe6, 0xff, 0xe7, 0xe4, 0xdf, 0xff, 0xe3, 0xe1, 0xdd, 0xff, 0xe7, 0xe0, 0xdd, 0xff, 0xf0, 0xe4, 0xdf, 0xff, 0xdd, 0xd4, 0xd4, 0xff, 0x91, 0x95, 0xad, 0xff, 0x4f, 0x5b, 0x7c, 0xff, 0x41, 0x47, 0x61, 0xff, 0x43, 0x47, 0x5d, 0xff, 0x45, 0x45, 0x5d, 0xff, 0x37, 0x3d, 0x53, 0xff, 0x20, 0x2f, 0x45, 0xff, 0x20, 0x2b, 0x45, 0xff, 0x22, 0x2b, 0x47, 0xff, 0x20, 0x2a, 0x47, 0xff, 0x1e, 0x2b, 0x47, 0xff, 0x1d, 0x2b, 0x4a, 0xff, 0x1d, 0x2f, 0x50, 0xff, 0x1c, 0x30, 0x53, 0xff, 0x1e, 0x32, 0x58, 0xff, 0x23, 0x35, 0x5d, 0xff, 0x29, 0x3a, 0x62, 0xff, 0x2a, 0x3e, 0x67, 0xff, 0x29, 0x3e, 0x6b, 0xff, 0x29, 0x3d, 0x6b, 0xff, 0x28, 0x3c, 0x6b, 0xff, 0x2b, 0x3f, 0x6e, 0xff, 0x2e, 0x42, 0x72, 0xff, 0x2f, 0x44, 0x75, 0xff, 0x31, 0x49, 0x79, 0xff, 0x2c, 0x45, 0x73, 0xff, 0x31, 0x49, 0x76, 0xff, 0x35, 0x4d, 0x7a, 0xff, 0x36, 0x4f, 0x7c, 0xff, 0x36, 0x51, 0x7f, 0xff, 0x36, 0x4e, 0x82, 0xff, 0x35, 0x4f, 0x81, 0xff, 0x36, 0x55, 0x83, 0xff, 0x3e, 0x57, 0x8c, 0xff, 0x46, 0x61, 0x98, 0xff, 0x40, 0x63, 0xa0, 0xff, 0x38, 0x5a, 0xa0, 0xff, 0x2f, 0x52, 0x92, 0xff, 0x64, 0x79, 0xa4, 0xff, 0xb8, 0xbd, 0xd2, 0xff, 0xf7, 0xec, 0xea, 0xff, 0xf9, 0xed, 0xdf, 0xff, 0xe3, 0xd9, 0xd0, 0xff, 0xe0, 0xd3, 0xd3, 0xff, 0xe8, 0xde, 0xdc, 0xff, 0xeb, 0xe2, 0xe0, 0xff, 0xe8, 0xdf, 0xdc, 0xff, 0xe7, 0xdf, 0xdd, 0xf3, 0xed, 0xe1, 0xe1, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcf, 0xd5, 0xdb, 0x2b, 0xcf, 0xd3, 0xd6, 0xd2, 0xcf, 0xd2, 0xd6, 0xff, 0xd2, 0xd4, 0xd9, 0xff, 0xd3, 0xd6, 0xda, 0xff, 0xd2, 0xd5, 0xd9, 0xff, 0xd0, 0xd3, 0xd7, 0xff, 0xcf, 0xd3, 0xd7, 0xff, 0xcf, 0xd4, 0xd7, 0xff, 0xcc, 0xcd, 0xd1, 0xff, 0xc9, 0xc8, 0xcd, 0xff, 0xc9, 0xc8, 0xca, 0xff, 0xcd, 0xcb, 0xcd, 0xff, 0xd3, 0xd2, 0xd3, 0xff, 0xe3, 0xe0, 0xe2, 0xff, 0xf3, 0xf0, 0xf1, 0xff, 0xf9, 0xf7, 0xf5, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfc, 0xfb, 0xfa, 0xff, 0xf8, 0xf6, 0xf6, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xec, 0xeb, 0xeb, 0xff, 0xea, 0xe8, 0xe6, 0xff, 0xe8, 0xe5, 0xe0, 0xff, 0xe4, 0xe1, 0xdd, 0xff, 0xe2, 0xe0, 0xdd, 0xff, 0xe2, 0xdb, 0xd9, 0xff, 0xec, 0xe1, 0xdb, 0xff, 0xe6, 0xdd, 0xdf, 0xff, 0x9b, 0x9d, 0xb1, 0xff, 0x4f, 0x5a, 0x79, 0xff, 0x39, 0x44, 0x60, 0xff, 0x3c, 0x40, 0x5b, 0xff, 0x45, 0x42, 0x5f, 0xff, 0x3a, 0x3f, 0x59, 0xff, 0x27, 0x32, 0x4c, 0xff, 0x20, 0x2b, 0x48, 0xff, 0x19, 0x29, 0x48, 0xff, 0x1b, 0x2a, 0x4a, 0xff, 0x1d, 0x2b, 0x4a, 0xff, 0x1a, 0x2b, 0x4f, 0xff, 0x1c, 0x2f, 0x54, 0xff, 0x1e, 0x32, 0x55, 0xff, 0x1e, 0x32, 0x57, 0xff, 0x21, 0x33, 0x5c, 0xff, 0x26, 0x37, 0x5e, 0xff, 0x26, 0x3b, 0x64, 0xff, 0x25, 0x3b, 0x68, 0xff, 0x26, 0x3a, 0x6a, 0xff, 0x28, 0x3c, 0x6b, 0xff, 0x2b, 0x3f, 0x6e, 0xff, 0x2e, 0x42, 0x71, 0xff, 0x2d, 0x42, 0x72, 0xff, 0x2d, 0x45, 0x74, 0xff, 0x2a, 0x42, 0x71, 0xff, 0x2d, 0x43, 0x73, 0xff, 0x31, 0x47, 0x78, 0xff, 0x33, 0x4c, 0x7c, 0xff, 0x36, 0x4f, 0x83, 0xff, 0x3f, 0x57, 0x8f, 0xff, 0x45, 0x5d, 0x93, 0xff, 0x44, 0x5d, 0x92, 0xff, 0x3b, 0x59, 0x98, 0xff, 0x31, 0x56, 0x99, 0xff, 0x31, 0x53, 0x92, 0xff, 0x51, 0x68, 0x97, 0xff, 0x9c, 0xa5, 0xba, 0xff, 0xf2, 0xe2, 0xe4, 0xff, 0xff, 0xf1, 0xe9, 0xff, 0xed, 0xe4, 0xd4, 0xff, 0xdd, 0xd5, 0xc9, 0xff, 0xe2, 0xd8, 0xd3, 0xff, 0xec, 0xe0, 0xde, 0xff, 0xed, 0xe2, 0xe0, 0xff, 0xea, 0xe1, 0xde, 0xff, 0xe9, 0xdf, 0xdd, 0xd2, 0xed, 0xe2, 0xdc, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdf, 0xdf, 0xdf, 0x08, 0xd0, 0xd2, 0xd7, 0x8f, 0xce, 0xd2, 0xd5, 0xfe, 0xd0, 0xd4, 0xd7, 0xff, 0xcf, 0xd2, 0xd7, 0xff, 0xcb, 0xce, 0xd3, 0xff, 0xc8, 0xcc, 0xd0, 0xff, 0xc4, 0xc7, 0xcb, 0xff, 0xc4, 0xc4, 0xc9, 0xff, 0xcb, 0xca, 0xce, 0xff, 0xdd, 0xda, 0xdb, 0xff, 0xec, 0xe9, 0xe9, 0xff, 0xf2, 0xee, 0xef, 0xff, 0xf6, 0xf5, 0xf1, 0xff, 0xfb, 0xfb, 0xf7, 0xff, 0xf8, 0xf5, 0xf5, 0xff, 0xf4, 0xf1, 0xf1, 0xff, 0xf6, 0xf3, 0xf2, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xf4, 0xf2, 0xf1, 0xff, 0xf1, 0xef, 0xed, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xe7, 0xe5, 0xe3, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xe7, 0xe4, 0xe0, 0xff, 0xe9, 0xe6, 0xe3, 0xff, 0xe4, 0xde, 0xdb, 0xff, 0xeb, 0xdf, 0xda, 0xff, 0xef, 0xe5, 0xe6, 0xff, 0xb8, 0xb5, 0xc4, 0xff, 0x56, 0x5d, 0x7c, 0xff, 0x37, 0x42, 0x62, 0xff, 0x36, 0x3c, 0x58, 0xff, 0x39, 0x39, 0x56, 0xff, 0x37, 0x3b, 0x59, 0xff, 0x2d, 0x35, 0x53, 0xff, 0x22, 0x2b, 0x4b, 0xff, 0x15, 0x28, 0x4a, 0xff, 0x18, 0x2a, 0x4d, 0xff, 0x1b, 0x2c, 0x4d, 0xff, 0x19, 0x2c, 0x52, 0xff, 0x1c, 0x30, 0x55, 0xff, 0x1f, 0x33, 0x56, 0xff, 0x1e, 0x31, 0x57, 0xff, 0x20, 0x32, 0x5a, 0xff, 0x23, 0x35, 0x5c, 0xff, 0x24, 0x38, 0x62, 0xff, 0x23, 0x39, 0x65, 0xff, 0x25, 0x39, 0x68, 0xff, 0x27, 0x3b, 0x6a, 0xff, 0x2b, 0x3f, 0x6e, 0xff, 0x2e, 0x43, 0x71, 0xff, 0x2f, 0x45, 0x74, 0xff, 0x2f, 0x46, 0x76, 0xff, 0x2f, 0x48, 0x79, 0xff, 0x34, 0x4c, 0x7f, 0xff, 0x35, 0x4d, 0x80, 0xff, 0x39, 0x53, 0x86, 0xff, 0x42, 0x58, 0x8a, 0xff, 0x4a, 0x5d, 0x93, 0xff, 0x45, 0x5c, 0xa0, 0xff, 0x38, 0x57, 0x9d, 0xff, 0x2b, 0x4e, 0x89, 0xff, 0x4d, 0x62, 0x90, 0xff, 0x99, 0x9a, 0xb3, 0xff, 0xde, 0xd7, 0xdf, 0xff, 0xfe, 0xf3, 0xe3, 0xff, 0xf5, 0xe7, 0xd7, 0xff, 0xe6, 0xd8, 0xcd, 0xff, 0xe2, 0xd7, 0xcf, 0xff, 0xe7, 0xdd, 0xd5, 0xff, 0xed, 0xe4, 0xde, 0xff, 0xee, 0xe2, 0xe0, 0xff, 0xe7, 0xdd, 0xdb, 0xfe, 0xe9, 0xe0, 0xdd, 0x8f, 0xdf, 0xdf, 0xdf, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xd4, 0xd4, 0x42, 0xc8, 0xcc, 0xcb, 0xdb, 0xc6, 0xc9, 0xcb, 0xff, 0xc6, 0xc8, 0xcb, 0xff, 0xc5, 0xc8, 0xca, 0xff, 0xd0, 0xcf, 0xce, 0xff, 0xdd, 0xda, 0xd9, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xfa, 0xf7, 0xf6, 0xff, 0xfb, 0xf8, 0xf8, 0xff, 0xf6, 0xf6, 0xf4, 0xff, 0xf5, 0xf4, 0xf2, 0xff, 0xf2, 0xed, 0xed, 0xff, 0xec, 0xe5, 0xe3, 0xff, 0xf0, 0xea, 0xe7, 0xff, 0xf6, 0xf5, 0xf1, 0xff, 0xf4, 0xf3, 0xef, 0xff, 0xf0, 0xed, 0xe9, 0xff, 0xe9, 0xe6, 0xe2, 0xff, 0xe6, 0xe3, 0xdf, 0xff, 0xe7, 0xe4, 0xe0, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xea, 0xe8, 0xe4, 0xff, 0xea, 0xe4, 0xe1, 0xff, 0xea, 0xdf, 0xdc, 0xff, 0xf4, 0xe4, 0xe1, 0xff, 0xe5, 0xd8, 0xd7, 0xff, 0x77, 0x7b, 0x92, 0xff, 0x32, 0x3d, 0x64, 0xff, 0x30, 0x3c, 0x60, 0xff, 0x27, 0x34, 0x52, 0xff, 0x2d, 0x39, 0x50, 0xff, 0x2d, 0x36, 0x51, 0xff, 0x24, 0x2c, 0x4f, 0xff, 0x1f, 0x2a, 0x4e, 0xff, 0x1a, 0x2a, 0x4f, 0xff, 0x17, 0x2c, 0x4f, 0xff, 0x19, 0x2d, 0x50, 0xff, 0x1a, 0x2e, 0x52, 0xff, 0x1e, 0x31, 0x55, 0xff, 0x20, 0x33, 0x57, 0xff, 0x21, 0x34, 0x59, 0xff, 0x21, 0x32, 0x5d, 0xff, 0x22, 0x33, 0x5e, 0xff, 0x23, 0x34, 0x5f, 0xff, 0x25, 0x35, 0x63, 0xff, 0x25, 0x37, 0x66, 0xff, 0x29, 0x3d, 0x6c, 0xff, 0x2f, 0x45, 0x72, 0xff, 0x31, 0x48, 0x76, 0xff, 0x35, 0x4a, 0x7d, 0xff, 0x3c, 0x51, 0x81, 0xff, 0x41, 0x56, 0x84, 0xff, 0x46, 0x59, 0x88, 0xff, 0x41, 0x59, 0x8d, 0xff, 0x36, 0x55, 0x90, 0xff, 0x36, 0x54, 0x92, 0xff, 0x3e, 0x56, 0x94, 0xff, 0x5a, 0x6d, 0xa0, 0xff, 0x8f, 0x9b, 0xb5, 0xff, 0xd4, 0xce, 0xd6, 0xff, 0xff, 0xee, 0xef, 0xff, 0xff, 0xea, 0xe5, 0xff, 0xea, 0xe2, 0xd3, 0xff, 0xe2, 0xdc, 0xd1, 0xff, 0xe7, 0xdc, 0xd7, 0xff, 0xec, 0xdf, 0xdf, 0xff, 0xed, 0xe3, 0xe0, 0xff, 0xe7, 0xde, 0xdb, 0xff, 0xe8, 0xdf, 0xdd, 0xdb, 0xeb, 0xe0, 0xe0, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0xc6, 0xc6, 0x09, 0xcf, 0xcf, 0xcf, 0x87, 0xda, 0xdb, 0xdb, 0xf9, 0xe5, 0xe5, 0xe5, 0xff, 0xef, 0xed, 0xec, 0xff, 0xf9, 0xf6, 0xf5, 0xff, 0xfb, 0xf9, 0xf8, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfb, 0xf9, 0xf8, 0xff, 0xfa, 0xf9, 0xf8, 0xff, 0xf8, 0xf5, 0xf4, 0xff, 0xed, 0xe8, 0xe7, 0xff, 0xe6, 0xdd, 0xdd, 0xff, 0xe7, 0xdd, 0xdc, 0xff, 0xec, 0xe6, 0xe3, 0xff, 0xf2, 0xf1, 0xec, 0xff, 0xf0, 0xee, 0xea, 0xff, 0xec, 0xe8, 0xe4, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xe7, 0xe4, 0xe0, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xef, 0xe9, 0xe5, 0xff, 0xef, 0xe4, 0xe2, 0xff, 0xf0, 0xe1, 0xde, 0xff, 0xf6, 0xea, 0xe0, 0xff, 0xba, 0xbc, 0xc2, 0xff, 0x4f, 0x61, 0x81, 0xff, 0x2d, 0x3f, 0x6d, 0xff, 0x2f, 0x3c, 0x63, 0xff, 0x2d, 0x3b, 0x53, 0xff, 0x27, 0x33, 0x4d, 0xff, 0x20, 0x2b, 0x4c, 0xff, 0x1b, 0x28, 0x4b, 0xff, 0x1b, 0x2c, 0x52, 0xff, 0x1a, 0x2f, 0x54, 0xff, 0x1a, 0x2e, 0x52, 0xff, 0x1b, 0x2e, 0x54, 0xff, 0x1f, 0x32, 0x5a, 0xff, 0x21, 0x33, 0x5a, 0xff, 0x21, 0x33, 0x5b, 0xff, 0x1d, 0x2e, 0x5a, 0xff, 0x1d, 0x2e, 0x59, 0xff, 0x1e, 0x2e, 0x59, 0xff, 0x21, 0x30, 0x5f, 0xff, 0x23, 0x34, 0x64, 0xff, 0x29, 0x3d, 0x6c, 0xff, 0x31, 0x48, 0x75, 0xff, 0x33, 0x49, 0x7a, 0xff, 0x3e, 0x52, 0x86, 0xff, 0x46, 0x5b, 0x99, 0xff, 0x43, 0x5b, 0x9c, 0xff, 0x44, 0x5e, 0x9c, 0xff, 0x41, 0x59, 0x91, 0xff, 0x38, 0x5b, 0x9b, 0xff, 0x54, 0x77, 0xae, 0xff, 0x9b, 0xa9, 0xba, 0xff, 0xdf, 0xd1, 0xd8, 0xff, 0xff, 0xe9, 0xec, 0xff, 0xfa, 0xec, 0xe7, 0xff, 0xef, 0xe2, 0xe1, 0xff, 0xeb, 0xde, 0xdb, 0xff, 0xeb, 0xe0, 0xdb, 0xff, 0xee, 0xe5, 0xdc, 0xff, 0xee, 0xe4, 0xde, 0xff, 0xeb, 0xdf, 0xde, 0xff, 0xe9, 0xdf, 0xdd, 0xf9, 0xe8, 0xdf, 0xdb, 0x88, 0xe2, 0xe2, 0xe2, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xf8, 0xf1, 0x25, 0xfd, 0xfa, 0xf9, 0xb7, 0xfe, 0xfc, 0xfb, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfd, 0xfb, 0xfa, 0xff, 0xf9, 0xf8, 0xf7, 0xff, 0xf6, 0xf6, 0xf4, 0xff, 0xee, 0xeb, 0xea, 0xff, 0xe2, 0xda, 0xda, 0xff, 0xe3, 0xd6, 0xd6, 0xff, 0xe5, 0xdb, 0xda, 0xff, 0xe9, 0xe4, 0xe0, 0xff, 0xea, 0xe9, 0xe5, 0xff, 0xe7, 0xe7, 0xe3, 0xff, 0xe9, 0xe5, 0xe1, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xe9, 0xe7, 0xe2, 0xff, 0xe9, 0xe6, 0xe2, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xe7, 0xe4, 0xe0, 0xff, 0xeb, 0xe5, 0xe1, 0xff, 0xf2, 0xe7, 0xe4, 0xff, 0xf1, 0xe4, 0xe2, 0xff, 0xed, 0xe4, 0xe2, 0xff, 0xed, 0xe4, 0xe0, 0xff, 0xb9, 0xb8, 0xc4, 0xff, 0x60, 0x71, 0x95, 0xff, 0x3e, 0x53, 0x7e, 0xff, 0x34, 0x41, 0x70, 0xff, 0x2a, 0x37, 0x5d, 0xff, 0x1d, 0x2e, 0x4b, 0xff, 0x16, 0x28, 0x49, 0xff, 0x17, 0x2a, 0x4e, 0xff, 0x1a, 0x2b, 0x53, 0xff, 0x20, 0x2f, 0x58, 0xff, 0x21, 0x32, 0x5a, 0xff, 0x1f, 0x32, 0x5b, 0xff, 0x20, 0x33, 0x5d, 0xff, 0x21, 0x33, 0x5d, 0xff, 0x1f, 0x2f, 0x59, 0xff, 0x1d, 0x2d, 0x57, 0xff, 0x1b, 0x2b, 0x55, 0xff, 0x22, 0x30, 0x5d, 0xff, 0x25, 0x36, 0x64, 0xff, 0x2c, 0x40, 0x6f, 0xff, 0x35, 0x4c, 0x7f, 0xff, 0x39, 0x50, 0x89, 0xff, 0x41, 0x56, 0x92, 0xff, 0x44, 0x5c, 0x9e, 0xff, 0x4a, 0x64, 0xa3, 0xff, 0x52, 0x72, 0xa8, 0xff, 0x7b, 0x91, 0xba, 0xff, 0xc4, 0xbd, 0xc8, 0xff, 0xee, 0xd8, 0xd5, 0xff, 0xfc, 0xe9, 0xe5, 0xff, 0xf7, 0xee, 0xe3, 0xff, 0xea, 0xe2, 0xde, 0xff, 0xf0, 0xde, 0xe0, 0xff, 0xf1, 0xe3, 0xdf, 0xff, 0xea, 0xe3, 0xe0, 0xff, 0xf3, 0xe4, 0xe3, 0xff, 0xef, 0xe4, 0xdc, 0xff, 0xe7, 0xdd, 0xd7, 0xff, 0xe8, 0xdc, 0xda, 0xb7, 0xea, 0xdd, 0xdd, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0xfb, 0xf7, 0x46, 0xf7, 0xf7, 0xf7, 0xca, 0xf6, 0xf7, 0xf5, 0xff, 0xf4, 0xf7, 0xf5, 0xff, 0xf5, 0xf4, 0xf3, 0xff, 0xf5, 0xed, 0xed, 0xff, 0xe1, 0xd9, 0xd7, 0xff, 0xd8, 0xd1, 0xce, 0xff, 0xdd, 0xd5, 0xd2, 0xff, 0xe3, 0xdc, 0xd9, 0xff, 0xe5, 0xe0, 0xdd, 0xff, 0xe3, 0xe2, 0xdf, 0xff, 0xe3, 0xe3, 0xdf, 0xff, 0xe5, 0xe2, 0xde, 0xff, 0xe6, 0xe3, 0xdf, 0xff, 0xe7, 0xe5, 0xe1, 0xff, 0xeb, 0xe6, 0xe3, 0xff, 0xeb, 0xe6, 0xe3, 0xff, 0xe8, 0xe2, 0xdf, 0xff, 0xe6, 0xdd, 0xda, 0xff, 0xef, 0xe5, 0xe2, 0xff, 0xf0, 0xe6, 0xe4, 0xff, 0xe6, 0xdf, 0xde, 0xff, 0xed, 0xe2, 0xdf, 0xff, 0xf5, 0xe5, 0xe0, 0xff, 0xcc, 0xce, 0xda, 0xff, 0x8a, 0x9b, 0xb6, 0xff, 0x54, 0x67, 0x8e, 0xff, 0x28, 0x3e, 0x63, 0xff, 0x11, 0x29, 0x4a, 0xff, 0x11, 0x27, 0x4b, 0xff, 0x17, 0x2c, 0x54, 0xff, 0x1c, 0x2c, 0x59, 0xff, 0x25, 0x32, 0x62, 0xff, 0x2b, 0x38, 0x68, 0xff, 0x26, 0x37, 0x64, 0xff, 0x1e, 0x32, 0x60, 0xff, 0x18, 0x31, 0x5c, 0xff, 0x17, 0x2d, 0x54, 0xff, 0x16, 0x2b, 0x54, 0xff, 0x13, 0x2a, 0x55, 0xff, 0x10, 0x2a, 0x5a, 0xff, 0x11, 0x2c, 0x60, 0xff, 0x19, 0x33, 0x6c, 0xff, 0x28, 0x40, 0x7a, 0xff, 0x45, 0x59, 0x8e, 0xff, 0x63, 0x73, 0xa5, 0xff, 0x8b, 0x94, 0xb9, 0xff, 0xb5, 0xb4, 0xc8, 0xff, 0xdc, 0xcd, 0xd1, 0xff, 0xf2, 0xe3, 0xe1, 0xff, 0xfc, 0xec, 0xe3, 0xff, 0xf7, 0xe7, 0xdb, 0xff, 0xef, 0xdf, 0xda, 0xff, 0xf3, 0xe2, 0xdc, 0xff, 0xef, 0xe6, 0xe0, 0xff, 0xf3, 0xe3, 0xe1, 0xff, 0xef, 0xe3, 0xe0, 0xff, 0xec, 0xe2, 0xe0, 0xff, 0xee, 0xe1, 0xe0, 0xff, 0xe9, 0xdc, 0xda, 0xca, 0xe9, 0xde, 0xda, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf5, 0xf8, 0xf5, 0x4d, 0xf3, 0xf6, 0xf3, 0xcc, 0xf6, 0xf2, 0xf1, 0xff, 0xeb, 0xdf, 0xe0, 0xff, 0xd6, 0xca, 0xc8, 0xff, 0xd5, 0xcb, 0xc8, 0xff, 0xde, 0xd4, 0xd1, 0xff, 0xe5, 0xdc, 0xd9, 0xff, 0xe4, 0xde, 0xdb, 0xff, 0xe2, 0xdf, 0xdc, 0xff, 0xe3, 0xe0, 0xdc, 0xff, 0xdf, 0xda, 0xd7, 0xff, 0xdc, 0xd7, 0xd4, 0xff, 0xde, 0xd8, 0xd5, 0xff, 0xe8, 0xe1, 0xde, 0xff, 0xed, 0xe6, 0xe3, 0xff, 0xe5, 0xdd, 0xda, 0xff, 0xdd, 0xd0, 0xce, 0xff, 0xe1, 0xd4, 0xd2, 0xff, 0xee, 0xe2, 0xe0, 0xff, 0xef, 0xe6, 0xe3, 0xff, 0xed, 0xe2, 0xe0, 0xff, 0xed, 0xde, 0xdb, 0xff, 0xf1, 0xe2, 0xdd, 0xff, 0xe9, 0xdf, 0xde, 0xff, 0xc9, 0xc4, 0xc8, 0xff, 0x90, 0x96, 0xa5, 0xff, 0x54, 0x61, 0x7f, 0xff, 0x2a, 0x3b, 0x65, 0xff, 0x17, 0x2e, 0x5b, 0xff, 0x12, 0x2c, 0x5a, 0xff, 0x0f, 0x2a, 0x5b, 0xff, 0x16, 0x2e, 0x61, 0xff, 0x18, 0x2e, 0x60, 0xff, 0x0e, 0x27, 0x59, 0xff, 0x09, 0x22, 0x53, 0xff, 0x05, 0x1d, 0x4a, 0xff, 0x07, 0x1e, 0x48, 0xff, 0x0f, 0x24, 0x4e, 0xff, 0x1d, 0x32, 0x5d, 0xff, 0x3f, 0x4d, 0x73, 0xff, 0x6a, 0x70, 0x8f, 0xff, 0x8f, 0x93, 0xac, 0xff, 0xba, 0xb7, 0xc5, 0xff, 0xe0, 0xd3, 0xd2, 0xff, 0xf1, 0xdf, 0xdd, 0xff, 0xf6, 0xe2, 0xe3, 0xff, 0xf6, 0xe0, 0xde, 0xff, 0xf7, 0xdf, 0xdc, 0xff, 0xf2, 0xdd, 0xdc, 0xff, 0xec, 0xde, 0xdd, 0xff, 0xed, 0xdf, 0xdd, 0xff, 0xef, 0xdb, 0xdc, 0xff, 0xec, 0xdc, 0xdb, 0xff, 0xe9, 0xde, 0xdc, 0xff, 0xea, 0xde, 0xdd, 0xff, 0xec, 0xdf, 0xde, 0xcc, 0xeb, 0xe1, 0xdd, 0x4d, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xeb, 0xe7, 0xe7, 0x41, 0xdb, 0xcd, 0xcd, 0xb4, 0xd3, 0xc5, 0xc5, 0xfe, 0xdb, 0xce, 0xcc, 0xff, 0xe4, 0xd6, 0xd4, 0xff, 0xe9, 0xdb, 0xda, 0xff, 0xe9, 0xde, 0xdc, 0xff, 0xe4, 0xdf, 0xdb, 0xff, 0xe4, 0xde, 0xdb, 0xff, 0xdf, 0xd6, 0xd3, 0xff, 0xd4, 0xcc, 0xc9, 0xff, 0xd7, 0xce, 0xca, 0xff, 0xe1, 0xd6, 0xd3, 0xff, 0xe4, 0xda, 0xd7, 0xff, 0xe0, 0xd6, 0xd3, 0xff, 0xde, 0xcf, 0xcd, 0xff, 0xd8, 0xc8, 0xc6, 0xff, 0xde, 0xcf, 0xcd, 0xff, 0xeb, 0xdf, 0xdd, 0xff, 0xf0, 0xe3, 0xe1, 0xff, 0xf3, 0xe0, 0xe0, 0xff, 0xf0, 0xdb, 0xd5, 0xff, 0xec, 0xd9, 0xd1, 0xff, 0xf0, 0xde, 0xd7, 0xff, 0xef, 0xdf, 0xde, 0xff, 0xde, 0xd0, 0xd5, 0xff, 0xc0, 0xb1, 0xbb, 0xff, 0x9f, 0x94, 0xa3, 0xff, 0x8f, 0x8b, 0x9c, 0xff, 0x7d, 0x81, 0x91, 0xff, 0x67, 0x71, 0x85, 0xff, 0x64, 0x71, 0x86, 0xff, 0x70, 0x78, 0x8d, 0xff, 0x76, 0x78, 0x8f, 0xff, 0x79, 0x77, 0x8b, 0xff, 0x8c, 0x86, 0x93, 0xff, 0xa7, 0x9b, 0xa2, 0xff, 0xc3, 0xb0, 0xb3, 0xff, 0xda, 0xc6, 0xc2, 0xff, 0xee, 0xd9, 0xce, 0xff, 0xf6, 0xe0, 0xd9, 0xff, 0xf7, 0xde, 0xda, 0xff, 0xf4, 0xda, 0xd2, 0xff, 0xf0, 0xda, 0xd6, 0xff, 0xec, 0xda, 0xdc, 0xff, 0xe7, 0xd7, 0xda, 0xff, 0xea, 0xd8, 0xd9, 0xff, 0xeb, 0xd6, 0xd5, 0xff, 0xe2, 0xd2, 0xd1, 0xff, 0xe0, 0xd0, 0xce, 0xff, 0xe6, 0xd4, 0xd3, 0xff, 0xe6, 0xd8, 0xd6, 0xfe, 0xe5, 0xda, 0xd7, 0xb4, 0xe7, 0xdb, 0xdb, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0xcc, 0xcc, 0x23, 0xdf, 0xd3, 0xd0, 0x88, 0xe4, 0xda, 0xd8, 0xe6, 0xe9, 0xe0, 0xdd, 0xff, 0xeb, 0xe1, 0xdf, 0xff, 0xec, 0xe2, 0xdf, 0xff, 0xe6, 0xde, 0xdb, 0xff, 0xdd, 0xd5, 0xd2, 0xff, 0xd7, 0xcc, 0xc9, 0xff, 0xd8, 0xcb, 0xc9, 0xff, 0xdd, 0xce, 0xcd, 0xff, 0xe0, 0xd1, 0xd0, 0xff, 0xe3, 0xd5, 0xd3, 0xff, 0xe2, 0xd3, 0xd1, 0xff, 0xd9, 0xca, 0xc8, 0xff, 0xd1, 0xc1, 0xc0, 0xff, 0xd6, 0xc5, 0xc4, 0xff, 0xe1, 0xcf, 0xce, 0xff, 0xee, 0xdb, 0xda, 0xff, 0xf0, 0xe0, 0xe0, 0xff, 0xe7, 0xd8, 0xd9, 0xff, 0xde, 0xd0, 0xd1, 0xff, 0xe1, 0xce, 0xcc, 0xff, 0xe8, 0xd0, 0xcb, 0xff, 0xee, 0xd5, 0xcf, 0xff, 0xf2, 0xd8, 0xd3, 0xff, 0xec, 0xd5, 0xd1, 0xff, 0xe6, 0xd1, 0xcc, 0xff, 0xe2, 0xcf, 0xcc, 0xff, 0xe4, 0xce, 0xcc, 0xff, 0xe6, 0xce, 0xcd, 0xff, 0xeb, 0xd3, 0xd1, 0xff, 0xed, 0xd5, 0xd3, 0xff, 0xf1, 0xd7, 0xd2, 0xff, 0xf5, 0xd9, 0xd3, 0xff, 0xef, 0xd6, 0xd2, 0xff, 0xe7, 0xd1, 0xcd, 0xff, 0xe4, 0xd0, 0xcc, 0xff, 0xe7, 0xd1, 0xcd, 0xff, 0xe9, 0xd2, 0xd0, 0xff, 0xec, 0xd8, 0xda, 0xff, 0xef, 0xdb, 0xda, 0xff, 0xeb, 0xd6, 0xd2, 0xff, 0xe3, 0xcb, 0xc7, 0xff, 0xdc, 0xc7, 0xc4, 0xff, 0xd8, 0xc8, 0xc7, 0xff, 0xdc, 0xcc, 0xcb, 0xff, 0xe2, 0xd0, 0xcf, 0xe7, 0xe6, 0xd3, 0xd3, 0x88, 0xe9, 0xd3, 0xd3, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xcc, 0xcc, 0x05, 0xea, 0xe3, 0xdf, 0x4a, 0xea, 0xe2, 0xdf, 0xa0, 0xec, 0xe3, 0xe0, 0xef, 0xe4, 0xdb, 0xd8, 0xff, 0xda, 0xd2, 0xcf, 0xff, 0xda, 0xce, 0xcb, 0xff, 0xdb, 0xcc, 0xcb, 0xff, 0xdd, 0xcd, 0xcb, 0xff, 0xdf, 0xd0, 0xce, 0xff, 0xe1, 0xd2, 0xd0, 0xff, 0xe2, 0xd3, 0xd1, 0xff, 0xe2, 0xd3, 0xd1, 0xff, 0xd6, 0xc8, 0xc5, 0xff, 0xd0, 0xbe, 0xbd, 0xff, 0xd0, 0xbd, 0xbc, 0xff, 0xd1, 0xbf, 0xbd, 0xff, 0xd6, 0xc5, 0xc6, 0xff, 0xe0, 0xcf, 0xd0, 0xff, 0xe3, 0xd2, 0xd4, 0xff, 0xe3, 0xd1, 0xd1, 0xff, 0xe3, 0xce, 0xcd, 0xff, 0xdf, 0xcc, 0xcb, 0xff, 0xe0, 0xcd, 0xcb, 0xff, 0xde, 0xc9, 0xc7, 0xff, 0xda, 0xc5, 0xc3, 0xff, 0xdb, 0xc5, 0xc3, 0xff, 0xdb, 0xc5, 0xc3, 0xff, 0xdb, 0xc6, 0xc3, 0xff, 0xda, 0xc5, 0xc3, 0xff, 0xdc, 0xc6, 0xc4, 0xff, 0xda, 0xc4, 0xc3, 0xff, 0xdb, 0xc6, 0xc6, 0xff, 0xde, 0xcb, 0xca, 0xff, 0xdf, 0xcb, 0xca, 0xff, 0xdf, 0xcb, 0xcb, 0xff, 0xdf, 0xcb, 0xcb, 0xff, 0xdb, 0xc8, 0xc7, 0xff, 0xd8, 0xc4, 0xc2, 0xff, 0xd4, 0xc0, 0xbf, 0xff, 0xd1, 0xbe, 0xbd, 0xff, 0xd2, 0xbf, 0xbe, 0xff, 0xd7, 0xc4, 0xc3, 0xef, 0xda, 0xc7, 0xc7, 0xa0, 0xdc, 0xcb, 0xcb, 0x4a, 0xcc, 0xcc, 0xcc, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0xda, 0xda, 0x07, 0xe1, 0xda, 0xd6, 0x46, 0xdb, 0xd3, 0xd0, 0x8e, 0xde, 0xd3, 0xd0, 0xd7, 0xdf, 0xd2, 0xd0, 0xff, 0xe1, 0xd2, 0xd0, 0xff, 0xe0, 0xd1, 0xcf, 0xff, 0xdf, 0xd0, 0xce, 0xff, 0xe3, 0xd4, 0xd2, 0xff, 0xe5, 0xd6, 0xd4, 0xff, 0xdf, 0xd0, 0xce, 0xff, 0xd7, 0xc6, 0xc5, 0xff, 0xcd, 0xbb, 0xba, 0xff, 0xc5, 0xb3, 0xb2, 0xff, 0xc2, 0xad, 0xaf, 0xff, 0xc6, 0xb0, 0xb3, 0xff, 0xcd, 0xb7, 0xba, 0xff, 0xd3, 0xbe, 0xbe, 0xff, 0xdb, 0xc8, 0xc6, 0xff, 0xe1, 0xcd, 0xcc, 0xff, 0xdf, 0xcb, 0xca, 0xff, 0xe1, 0xcd, 0xcc, 0xff, 0xe0, 0xcc, 0xcb, 0xff, 0xdb, 0xc7, 0xc6, 0xff, 0xda, 0xc6, 0xc5, 0xff, 0xdb, 0xc7, 0xc6, 0xff, 0xd6, 0xc2, 0xc1, 0xff, 0xd7, 0xc5, 0xc4, 0xff, 0xd7, 0xc6, 0xc5, 0xff, 0xd6, 0xc4, 0xc3, 0xff, 0xd7, 0xc3, 0xc2, 0xff, 0xd1, 0xbd, 0xbc, 0xff, 0xca, 0xb6, 0xb5, 0xff, 0xc9, 0xb5, 0xb4, 0xff, 0xc8, 0xb4, 0xb3, 0xff, 0xc4, 0xb0, 0xaf, 0xff, 0xc4, 0xb0, 0xaf, 0xd7, 0xc9, 0xb5, 0xb5, 0x8e, 0xd3, 0xbd, 0xbd, 0x46, 0xda, 0xb6, 0xb6, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe2, 0xcf, 0xcf, 0x1b, 0xe1, 0xd2, 0xcf, 0x55, 0xde, 0xd0, 0xce, 0x8f, 0xdf, 0xcf, 0xce, 0xc2, 0xe0, 0xd1, 0xcf, 0xea, 0xdf, 0xd0, 0xce, 0xff, 0xdf, 0xd0, 0xce, 0xff, 0xdc, 0xca, 0xc9, 0xff, 0xd5, 0xc3, 0xc2, 0xff, 0xd0, 0xbe, 0xbd, 0xff, 0xca, 0xb7, 0xb6, 0xff, 0xc4, 0xb0, 0xb1, 0xff, 0xc0, 0xad, 0xad, 0xff, 0xc2, 0xae, 0xae, 0xff, 0xc6, 0xb1, 0xb0, 0xff, 0xc8, 0xb4, 0xb3, 0xff, 0xcc, 0xb8, 0xb7, 0xff, 0xd0, 0xbc, 0xbb, 0xff, 0xd1, 0xbd, 0xbc, 0xff, 0xcf, 0xbb, 0xba, 0xff, 0xd2, 0xbe, 0xbd, 0xff, 0xd1, 0xbc, 0xbb, 0xff, 0xc8, 0xb4, 0xb3, 0xff, 0xcb, 0xb7, 0xb6, 0xff, 0xca, 0xb6, 0xb5, 0xff, 0xc3, 0xb0, 0xaf, 0xff, 0xc3, 0xaf, 0xae, 0xff, 0xbe, 0xaa, 0xa8, 0xea, 0xb9, 0xa5, 0xa4, 0xc2, 0xb7, 0xa4, 0xa2, 0x8f, 0xbd, 0xa8, 0xa8, 0x55, 0xc6, 0xb3, 0xaa, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe1, 0xd2, 0xd2, 0x11, 0xde, 0xd0, 0xcc, 0x37, 0xde, 0xce, 0xcc, 0x5f, 0xdc, 0xc9, 0xc9, 0x86, 0xd7, 0xc5, 0xc5, 0xa8, 0xd3, 0xc0, 0xc0, 0xc0, 0xcc, 0xbb, 0xb8, 0xd3, 0xc6, 0xb5, 0xb3, 0xe6, 0xc6, 0xb2, 0xb0, 0xee, 0xc8, 0xb4, 0xb3, 0xf5, 0xc9, 0xb5, 0xb4, 0xff, 0xc7, 0xb3, 0xb2, 0xff, 0xc6, 0xb3, 0xb1, 0xf5, 0xc7, 0xb2, 0xb1, 0xee, 0xc6, 0xb2, 0xb2, 0xe6, 0xc8, 0xb5, 0xb4, 0xd3, 0xc7, 0xb3, 0xb1, 0xc0, 0xbf, 0xab, 0xaa, 0xa8, 0xc5, 0xaf, 0xaf, 0x86, 0xc3, 0xae, 0xae, 0x5f, 0xbe, 0xa6, 0xa6, 0x37, 0xc3, 0xa5, 0xa5, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_image_dsc_t img_multilang_avatar_8 = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 128,
    .header.h = 128,
    .header.stride = 512,
    .data = img_multilang_avatar_8_map,
    .data_size = sizeof(img_multilang_avatar_8_map),
};

#endif
