
#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: bin.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_bin_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC7,0xE9,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x05,0xEA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x44,0xFA,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x44,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x27,0xF2,0x27,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x86,0xF9,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xEE,0xF3,0xDE,0xFF,0xBE,0xFF,0xCE,0xF3,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x44,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x0B,0xF3,0x4B,0xF3,0x4B,0xF3,0x4B,0xF3,0x4B,0xF3,0x4B,0xF3,0x1B,0xFF,0xFF,0xFF,
0xFF,0xFF,0x1B,0xFF,0x4B,0xF3,0x4B,0xF3,0x4B,0xF3,0x4B,0xF3,0x4B,0xF3,0x0B,0xF3,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x8D,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x8D,0xF3,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x26,0xF2,0x00,0x00,
0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x88,0xF2,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0x88,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x05,0xEA,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x27,0xF2,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,
0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0xAD,0xF3,0x27,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x05,0xEA,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x0A,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xA9,0xF2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0xFF,0xFF,0xBE,0xFF,0xBE,0xFF,0xFF,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA9,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x67,0xF2,0xFF,0xFF,0xFF,0xFF,0x54,0xFD,0xC9,0xF2,0xFF,0xFF,0xFF,0xFF,0x0F,0xF4,
0x0F,0xF4,0xFF,0xFF,0xFF,0xFF,0xC9,0xF2,0x54,0xFD,0xFF,0xFF,0xFF,0xFF,0x67,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xFF,0xFF,0xFF,0xFF,0x33,0xFD,0x47,0xF2,0xFF,0xFF,0xFF,0xFF,0xCD,0xF3,0xCD,0xF3,0xFF,0xFF,0xFF,0xFF,0x47,0xF2,0x34,0xFD,0xFF,0xFF,0xFF,0xFF,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xBE,0xFF,0xFF,0xFF,0x54,0xFD,0x27,0xF2,0xFF,0xFF,0xFF,0xFF,0xCD,0xF3,0xCD,0xF3,0xFF,0xFF,0xFF,0xFF,0x26,0xF2,0x54,0xFD,0xFF,0xFF,0xBE,0xFF,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x7D,0xFF,0xFF,0xFF,0x75,0xFD,0x06,0xF2,0xFF,0xFF,0xFF,0xFF,0xCD,0xF3,
0xCD,0xF3,0xFF,0xFF,0xFF,0xFF,0x06,0xF2,0x95,0xFD,0xFF,0xFF,0x5D,0xFF,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x1C,0xFF,0xFF,0xFF,0xB5,0xFD,0x06,0xF2,0xDF,0xFF,0xFF,0xFF,0xCD,0xF3,0xCD,0xF3,0xFF,0xFF,0xDF,0xFF,0x06,0xF2,0xB6,0xFD,0xFF,0xFF,0x1B,0xFF,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xDA,0xFE,0xFF,0xFF,0xD6,0xFD,0x06,0xF2,0xBE,0xFF,0xFF,0xFF,0xCD,0xF3,0xCD,0xF3,0xFF,0xFF,0x9E,0xFF,0x06,0xF2,0xD6,0xFD,0xFF,0xFF,0xBA,0xFE,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x79,0xFE,0xFF,0xFF,0xF7,0xFD,0x06,0xF2,0x7D,0xFF,0xFF,0xFF,0xCD,0xF3,
0xCD,0xF3,0xFF,0xFF,0x7D,0xFF,0x06,0xF2,0xF7,0xFD,0xFF,0xFF,0x79,0xFE,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x38,0xFE,0xFF,0xFF,0x17,0xFE,0x06,0xF2,0x5D,0xFF,0xFF,0xFF,0xCD,0xF3,0xCD,0xF3,0xFF,0xFF,0x5C,0xFF,0x06,0xF2,0x38,0xFE,0xFF,0xFF,0x38,0xFE,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x25,0xEA,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xD6,0xFD,0xFF,0xFF,0x58,0xFE,0x06,0xF2,0x3C,0xFF,0xFF,0xFF,0xCD,0xF3,0xCD,0xF3,0xFF,0xFF,0x3C,0xFF,0x06,0xF2,0x58,0xFE,0xFF,0xFF,0xD6,0xFD,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x05,0xEA,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x95,0xFD,0xFF,0xFF,0x79,0xFE,0x06,0xF2,0xFB,0xFE,0xFF,0xFF,0xCD,0xF3,
0xCD,0xF3,0xFF,0xFF,0xFB,0xFE,0x06,0xF2,0x79,0xFE,0xFF,0xFF,0x95,0xFD,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x34,0xFD,0xFF,0xFF,0x1C,0xFF,0x68,0xF2,0x5D,0xFF,0xFF,0xFF,0x91,0xFC,0x91,0xFC,0xFF,0xFF,0x5D,0xFF,0x68,0xF2,0x1C,0xFF,0xFF,0xFF,0x34,0xFD,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x26,0xF2,0x00,0x00,
0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xF2,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0xFC,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x86,0xF9,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x8D,0xF3,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0x8C,0xF3,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x44,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x68,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0xCA,0xF2,0x68,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x44,0xFA,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0xA5,0xFA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x26,0xF2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x05,0xEA,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,
0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x06,0xF2,0x25,0xEA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0D,0x58,0x9A,0xCA,0xEA,0xFA,0xFA,0xEA,0xC9,0x9A,0x58,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x95,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x95,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x80,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x14,0xC0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x2B,0xE1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x2B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x14,0xDD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x17,0x00,0x00,0x00,
0x00,0x00,0x05,0xC0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC5,0x07,0x00,0x00,0x00,0x00,0x80,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x86,0x00,0x00,0x00,0x18,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x95,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0x00,0x0C,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x56,
0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0B,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x95,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x00,0x00,0x17,0xF3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x21,0x00,
0x00,0x00,0x80,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x84,0x00,0x00,0x00,0x00,0x05,0xC0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC5,0x07,0x00,0x00,0x00,0x00,0x00,0x14,0xDD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x17,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2B,0xE1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE1,0x2B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x14,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC5,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x85,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0x84,0x06,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x22,0x99,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x98,0x21,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x56,0x97,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x55,0x0B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_bin_icon = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.w = 32,
    .header.h = 32,
    .data_size = sizeof(img_bin_icon_data),
    .header.cf = LV_COLOR_FORMAT_RGB565A8,
    .data = img_bin_icon_data
};

