#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: sms.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_sms_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x27,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x2D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,
0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x06,0x36,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x27,0x3E,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE7,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x36,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x06,0x36,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x36,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,
0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x36,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xE7,0x3D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x27,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x2A,0x4E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,
0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x4B,0x5E,0x2A,0x4E,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x2D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x6D,0x66,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2B,0x56,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x00,0x00,
0x00,0x00,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x84,0x1D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x00,0x00,0xA6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x83,0x15,0xE6,0x35,0xE6,0x35,0x27,0x3E,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x63,0x15,0xE6,0x35,0xE6,0x35,
0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x63,0x15,0x06,0x36,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0xD6,0x1C,0xE7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xB6,0xB5,
0xB6,0xB5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,0xDE,0x9A,0xD6,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x63,0x15,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD3,0x9C,0x00,0x00,0x20,0x00,0x79,0xCE,0xFF,0xFF,0xEF,0x7B,0x00,0x00,0x00,0x00,0x10,0x84,0xFF,0xFF,0x79,0xCE,0x20,0x00,0x00,0x00,0xD3,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x21,0x05,
0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2C,0x63,0x00,0x00,0x00,0x00,0xF3,0x9C,0xFF,0xFF,0x49,0x4A,0x00,0x00,0x00,0x00,0x08,0x42,0xFF,0xFF,0xF3,0x9C,0x00,0x00,0x00,0x00,0x2C,0x63,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x21,0x05,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDB,0xDE,0x28,0x42,0xCB,0x5A,0xBE,0xF7,0xFF,0xFF,0x3C,0xE7,0x24,0x21,
0xC3,0x18,0xBA,0xD6,0xFF,0xFF,0xBE,0xF7,0xCB,0x5A,0x28,0x42,0xDB,0xDE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x21,0x05,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x21,0x05,
0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x22,0x0D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x43,0x0D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2D,0x66,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x63,0x15,
0xE7,0x2D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xB0,0x7E,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x2C,0x5E,0x01,0x05,0x01,0x05,0x01,0x05,0x21,0x05,0xE5,0x15,0x00,0x00,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x28,0x46,0x16,0xAF,0x37,0xBF,0x37,0xBF,0x37,0xBF,0x37,0xBF,0x37,0xBF,0x37,0xBF,0x37,0xBF,0x37,0xBF,0x58,0xBF,
0xBD,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBD,0xEF,0x37,0xBF,0x37,0xBF,0x15,0xAF,0x44,0x15,0x01,0x05,0x01,0x05,0x01,0x05,0x42,0x0D,0x00,0x00,0x00,0x00,0x06,0x36,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xC6,0x2D,0x22,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x6F,0x76,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x15,0xAF,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x84,0x1D,0x00,0x00,
0x00,0x00,0x00,0x00,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x2D,0x22,0x0D,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0xEA,0x4D,0xDD,0xEF,0xFF,0xFF,0xFF,0xFF,0x15,0xAF,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x63,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE7,0x3D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x2D,0x42,0x0D,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,
0x01,0x05,0x01,0x05,0x01,0x05,0x85,0x25,0x7A,0xD7,0xFF,0xFF,0x15,0xAF,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x42,0x0D,0x22,0x15,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xC6,0x2D,0x42,0x0D,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x22,0x0D,0xF4,0x9E,0x15,0xAF,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x22,0x05,0x63,0x15,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x2D,0x42,0x0D,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0xA7,0x35,0x01,0x05,0x01,0x05,0x01,0x05,0x22,0x05,0x83,0x15,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x36,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x2D,0x42,0x0D,0x01,0x05,0x01,0x05,0x01,0x05,
0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x42,0x0D,0x64,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA4,0x25,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x2D,0x42,0x0D,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x43,0x0D,0x22,0x15,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x2D,0x42,0x0D,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x01,0x05,0x21,0x05,0x62,0x0D,0x84,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE7,0x2D,0xE6,0x35,0xE6,0x35,0xE6,0x35,0xE6,0x35,0x42,0x0D,
0x01,0x05,0x21,0x05,0x42,0x0D,0x43,0x0D,0x63,0x15,0xE5,0x15,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE6,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD5,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x8E,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x17,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD7,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0x0E,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x33,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x52,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xD6,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xEA,0xFD,0xFF,0xFD,0xF3,0xD5,0x8F,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_sms_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_sms_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_sms_icon_data};

