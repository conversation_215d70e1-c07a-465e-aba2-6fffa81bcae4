[zlib] Fix zlib sources for compilation with FreeType

We must ensure that they do not issue compiler errors or warnings when they
are compiled as part of `src/gzip/ftgzip.c`.

* src/gzip/gzguts.h (COPY): Rename to...
(COPY__): ... this since `COPY` and `COPY_` conflict with enum values,
which have the same name in `zlib.h`.

* src/gzip/inflate.c, src/gzip/adler32.c, src/gzip/crc32.c,
src/gzip/zutil.c: Omit unused function declarations and definitions when
`Z_FREETYPE` is defined.

* src/gzip/inffast.h (inflate_fast): Declare as static.

* src/gzip/inftrees.c (inflate_copyright): Declare as static.

* src/gzip/zlib.h: Include `ftzconf.h` instead of `zconf.h` to avoid
conflicts with system-installed headers.
Omit unused function declarations when `Z_FREETYPE` is defined.
(inflateInit2)[Z_FREETYPE]: Provide proper declaration.

* src/gzip/zutil.h: Use `ft_memxxx` functions instead of `memxxx`.
Omit unused function declarations when `Z_FREETYPE` is defined.

* src/gzip/inflate.h, src/gzip/inftrees.h: Add header guard macros to
prevent compiler errors.

* src/gzip/inftrees.h: Add header guard macros to prevent compiler errors.
(inflate_table): Declare as static.

diff --git b/src/gzip/adler32.c a/src/gzip/adler32.c
index 04b81d29b..260185b67 100644
--- b/src/gzip/adler32.c
+++ a/src/gzip/adler32.c
@@ -129,6 +129,8 @@ uLong ZEXPORT adler32(uLong adler, const Bytef *buf, uInt len) {
     return adler32_z(adler, buf, len);
 }
 
+#ifndef Z_FREETYPE
+
 /* ========================================================================= */
 local uLong adler32_combine_(uLong adler1, uLong adler2, z_off64_t len2) {
     unsigned long sum1;
@@ -162,3 +164,5 @@ uLong ZEXPORT adler32_combine(uLong adler1, uLong adler2, z_off_t len2) {
 uLong ZEXPORT adler32_combine64(uLong adler1, uLong adler2, z_off64_t len2) {
     return adler32_combine_(adler1, adler2, len2);
 }
+
+#endif  /* !Z_FREETYPE */
diff --git b/src/gzip/crc32.c a/src/gzip/crc32.c
index 6c38f5c04..27487dcc2 100644
--- b/src/gzip/crc32.c
+++ a/src/gzip/crc32.c
@@ -148,6 +148,8 @@ local z_word_t byte_swap(z_word_t word) {
 /* CRC polynomial. */
 #define POLY 0xedb88320         /* p(x) reflected, with x^32 implied */
 
+#ifndef Z_FREETYPE
+
 /*
   Return a(x) multiplied by b(x) modulo p(x), where p(x) is the CRC polynomial,
   reflected. For speed, this requires that a not be zero.
@@ -186,6 +188,8 @@ local z_crc_t x2nmodp(z_off64_t n, unsigned k) {
     return p;
 }
 
+#endif  /* !Z_FREETYPE */
+
 #ifdef DYNAMIC_CRC_TABLE
 /* =========================================================================
  * Build the tables for byte-wise and braided CRC-32 calculations, and a table
@@ -542,6 +546,8 @@ local void braid(z_crc_t ltl[][256], z_word_t big[][256], int n, int w) {
 
 #endif /* DYNAMIC_CRC_TABLE */
 
+#ifndef Z_FREETYPE
+
 /* =========================================================================
  * This function can be used by asm versions of crc32(), and to force the
  * generation of the CRC tables in a threaded application.
@@ -553,6 +559,8 @@ const z_crc_t FAR * ZEXPORT get_crc_table(void) {
     return (const z_crc_t FAR *)crc_table;
 }
 
+#endif   /* !Z_FREETYPE */
+
 /* =========================================================================
  * Use ARM machine instructions if available. This will compute the CRC about
  * ten times faster than the braided calculation. This code does not check for
@@ -1017,6 +1025,8 @@ unsigned long ZEXPORT crc32(unsigned long crc, const unsigned char FAR *buf,
     return crc32_z(crc, buf, len);
 }
 
+#ifndef Z_FREETYPE
+
 /* ========================================================================= */
 uLong ZEXPORT crc32_combine64(uLong crc1, uLong crc2, z_off64_t len2) {
 #ifdef DYNAMIC_CRC_TABLE
@@ -1047,3 +1057,5 @@ uLong ZEXPORT crc32_combine_gen(z_off_t len2) {
 uLong ZEXPORT crc32_combine_op(uLong crc1, uLong crc2, uLong op) {
     return multmodp(op, crc1) ^ (crc2 & 0xffffffff);
 }
+
+#endif  /* !Z_FREETYPE */
diff --git b/src/gzip/gzguts.h a/src/gzip/gzguts.h
index f9375047e..f9a250b85 100644
--- b/src/gzip/gzguts.h
+++ a/src/gzip/gzguts.h
@@ -162,7 +162,7 @@
 
 /* values for gz_state how */
 #define LOOK 0      /* look for a gzip header */
-#define COPY 1      /* copy input directly */
+#define COPY__ 1    /* copy input directly */
 #define GZIP 2      /* decompress a gzip stream */
 
 /* internal gzip file state data structure */
diff --git b/src/gzip/inffast.h a/src/gzip/inffast.h
index 49c6d156c..a38c5be45 100644
--- b/src/gzip/inffast.h
+++ a/src/gzip/inffast.h
@@ -8,4 +8,4 @@
    subject to change. Applications should only use zlib.h.
  */
 
-void ZLIB_INTERNAL inflate_fast(z_streamp strm, unsigned start);
+static void ZLIB_INTERNAL inflate_fast(z_streamp strm, unsigned start);
diff --git b/src/gzip/inflate.c a/src/gzip/inflate.c
index b0757a9b2..f7ed5d181 100644
--- b/src/gzip/inflate.c
+++ a/src/gzip/inflate.c
@@ -215,6 +215,8 @@ int ZEXPORT inflateInit2_(z_streamp strm, int windowBits,
     return ret;
 }
 
+#ifndef Z_FREETYPE
+
 int ZEXPORT inflateInit_(z_streamp strm, const char *version,
                          int stream_size) {
     return inflateInit2_(strm, DEF_WBITS, version, stream_size);
@@ -239,6 +241,8 @@ int ZEXPORT inflatePrime(z_streamp strm, int bits, int value) {
     return Z_OK;
 }
 
+#endif  /* !Z_FREETYPE */
+
 /*
    Return state with length and distance decoding tables and index sizes set to
    fixed code decoding.  Normally this returns fixed tables from inffixed.h.
@@ -1275,6 +1279,8 @@ int ZEXPORT inflateEnd(z_streamp strm) {
     return Z_OK;
 }
 
+#ifndef Z_FREETYPE
+
 int ZEXPORT inflateGetDictionary(z_streamp strm, Bytef *dictionary,
                                  uInt *dictLength) {
     struct inflate_state FAR *state;
@@ -1524,3 +1530,5 @@ unsigned long ZEXPORT inflateCodesUsed(z_streamp strm) {
     state = (struct inflate_state FAR *)strm->state;
     return (unsigned long)(state->next - state->codes);
 }
+
+#endif  /* !Z_FREETYPE */
diff --git b/src/gzip/inflate.h a/src/gzip/inflate.h
index f127b6b1f..8a0e437ae 100644
--- b/src/gzip/inflate.h
+++ a/src/gzip/inflate.h
@@ -3,6 +3,9 @@
  * For conditions of distribution and use, see copyright notice in zlib.h
  */
 
+#ifndef INFLATE_H
+#define INFLATE_H
+
 /* WARNING: this file should *not* be used by applications. It is
    part of the implementation of the compression library and is
    subject to change. Applications should only use zlib.h.
@@ -124,3 +127,5 @@ struct inflate_state {
     int back;                   /* bits back of last unprocessed length/lit */
     unsigned was;               /* initial length of match */
 };
+
+#endif  /* !INFLATE_H */
diff --git b/src/gzip/inftrees.c a/src/gzip/inftrees.c
index 8a208c2da..1fd655593 100644
--- b/src/gzip/inftrees.c
+++ a/src/gzip/inftrees.c
@@ -8,7 +8,7 @@
 
 #define MAXBITS 15
 
-const char inflate_copyright[] =
+static const char inflate_copyright[] =
    " inflate 1.3 Copyright 1995-2023 Mark Adler ";
 /*
   If you use the zlib library in a product, an acknowledgment is welcome
diff --git b/src/gzip/inftrees.h a/src/gzip/inftrees.h
index a10712d8c..47f726c36 100644
--- b/src/gzip/inftrees.h
+++ a/src/gzip/inftrees.h
@@ -3,6 +3,9 @@
  * For conditions of distribution and use, see copyright notice in zlib.h
  */
 
+#ifndef INFTREES_H
+#define INFTREES_H
+
 /* WARNING: this file should *not* be used by applications. It is
    part of the implementation of the compression library and is
    subject to change. Applications should only use zlib.h.
@@ -57,6 +60,8 @@ typedef enum {
     DISTS
 } codetype;
 
-int ZLIB_INTERNAL inflate_table(codetype type, unsigned short FAR *lens,
+static int ZLIB_INTERNAL inflate_table(codetype type, unsigned short FAR *lens,
                                 unsigned codes, code FAR * FAR *table,
                                 unsigned FAR *bits, unsigned short FAR *work);
+
+#endif  /* !INFTREES_H */
diff --git b/src/gzip/zlib.h a/src/gzip/zlib.h
index 6b7244f99..5c7a884c9 100644
--- b/src/gzip/zlib.h
+++ a/src/gzip/zlib.h
@@ -31,7 +31,7 @@
 #ifndef ZLIB_H
 #define ZLIB_H
 
-#include "zconf.h"
+#include "ftzconf.h"
 
 #ifdef __cplusplus
 extern "C" {
@@ -211,6 +211,8 @@ typedef gz_header FAR *gz_headerp;
 
 #define Z_NULL  0  /* for initializing zalloc, zfree, opaque */
 
+#ifndef Z_FREETYPE
+
 #define zlib_version zlibVersion()
 /* for compatibility with versions < 1.0.2 */
 
@@ -373,6 +375,7 @@ ZEXTERN int ZEXPORT deflateEnd(z_streamp strm);
    deallocated).
 */
 
+#endif  /* !Z_FREETYPE */
 
 /*
 ZEXTERN int ZEXPORT inflateInit(z_streamp strm);
@@ -535,6 +538,8 @@ ZEXTERN int ZEXPORT inflateEnd(z_streamp strm);
     The following functions are needed only in some special applications.
 */
 
+#ifndef Z_FREETYPE
+
 /*
 ZEXTERN int ZEXPORT deflateInit2(z_streamp strm,
                                  int level,
@@ -958,6 +963,8 @@ ZEXTERN int ZEXPORT inflateCopy(z_streamp dest,
    destination.
 */
 
+#endif  /* !Z_FREETYPE */
+
 ZEXTERN int ZEXPORT inflateReset(z_streamp strm);
 /*
      This function is equivalent to inflateEnd followed by inflateInit,
@@ -983,6 +990,8 @@ ZEXTERN int ZEXPORT inflateReset2(z_streamp strm,
    the windowBits parameter is invalid.
 */
 
+#ifndef Z_FREETYPE
+
 ZEXTERN int ZEXPORT inflatePrime(z_streamp strm,
                                  int bits,
                                  int value);
@@ -1072,6 +1081,8 @@ ZEXTERN int ZEXPORT inflateGetHeader(z_streamp strm,
    stream state was inconsistent.
 */
 
+#endif  /* !Z_FREETYPE */
+
 /*
 ZEXTERN int ZEXPORT inflateBackInit(z_streamp strm, int windowBits,
                                     unsigned char FAR *window);
@@ -1098,6 +1109,8 @@ typedef unsigned (*in_func)(void FAR *,
                             z_const unsigned char FAR * FAR *);
 typedef int (*out_func)(void FAR *, unsigned char FAR *, unsigned);
 
+#ifndef Z_FREETYPE
+
 ZEXTERN int ZEXPORT inflateBack(z_streamp strm,
                                 in_func in, void FAR *in_desc,
                                 out_func out, void FAR *out_desc);
@@ -1217,6 +1230,8 @@ ZEXTERN uLong ZEXPORT zlibCompileFlags(void);
      27-31: 0 (reserved)
  */
 
+#endif  /* !Z_FREETYPE */
+
 #ifndef Z_SOLO
 
                         /* utility functions */
@@ -1768,6 +1783,8 @@ ZEXTERN uLong ZEXPORT crc32_combine_gen(z_off_t len2);
    crc32_combine_op().
 */
 
+#ifndef Z_FREETYPE
+
 ZEXTERN uLong ZEXPORT crc32_combine_op(uLong crc1, uLong crc2, uLong op);
 /*
      Give the same result as crc32_combine(), using op in place of len2. op is
@@ -1825,6 +1842,17 @@ ZEXTERN int ZEXPORT inflateBackInit_(z_streamp strm, int windowBits,
                            ZLIB_VERSION, (int)sizeof(z_stream))
 #endif
 
+#else  /* Z_FREETYPE */
+
+ZEXTERN int ZEXPORT inflateInit2_(z_streamp strm, int  windowBits,
+                                  const char *version, int stream_size);
+
+#  define inflateInit2(strm, windowBits) \
+          inflateInit2_((strm), (windowBits), ZLIB_VERSION, \
+                        (int)sizeof(z_stream))
+
+#endif  /* Z_FREETYPE */
+
 #ifndef Z_SOLO
 
 /* gzgetc() macro and its supporting function and exposed data structure.  Note
@@ -1904,20 +1932,25 @@ ZEXTERN int ZEXPORT gzgetc_(gzFile file);       /* backward compatibility */
 
 #else /* Z_SOLO */
 
+#ifndef Z_FREETYPE
    ZEXTERN uLong ZEXPORT adler32_combine(uLong, uLong, z_off_t);
    ZEXTERN uLong ZEXPORT crc32_combine(uLong, uLong, z_off_t);
    ZEXTERN uLong ZEXPORT crc32_combine_gen(z_off_t);
+#endif
 
 #endif /* !Z_SOLO */
 
 /* undocumented functions */
+#ifndef Z_FREETYPE
 ZEXTERN const char   * ZEXPORT zError(int);
 ZEXTERN int            ZEXPORT inflateSyncPoint(z_streamp);
 ZEXTERN const z_crc_t FAR * ZEXPORT get_crc_table(void);
 ZEXTERN int            ZEXPORT inflateUndermine(z_streamp, int);
 ZEXTERN int            ZEXPORT inflateValidate(z_streamp, int);
 ZEXTERN unsigned long  ZEXPORT inflateCodesUsed(z_streamp);
+#endif  /* !Z_FREETYPE */
 ZEXTERN int            ZEXPORT inflateResetKeep(z_streamp);
+#ifndef Z_FREETYPE
 ZEXTERN int            ZEXPORT deflateResetKeep(z_streamp);
 #if defined(_WIN32) && !defined(Z_SOLO)
 ZEXTERN gzFile         ZEXPORT gzopen_w(const wchar_t *path,
@@ -1930,6 +1963,7 @@ ZEXTERN int            ZEXPORTVA gzvprintf(gzFile file,
                                            va_list va);
 #  endif
 #endif
+#endif  /* !Z_FREETYPE */
 
 #ifdef __cplusplus
 }
diff --git b/src/gzip/zutil.c a/src/gzip/zutil.c
index b1c5d2d3c..f76def425 100644
--- b/src/gzip/zutil.c
+++ a/src/gzip/zutil.c
@@ -10,6 +10,8 @@
 #  include "gzguts.h"
 #endif
 
+#ifndef Z_FREETYPE
+
 z_const char * const z_errmsg[10] = {
     (z_const char *)"need dictionary",     /* Z_NEED_DICT       2  */
     (z_const char *)"stream end",          /* Z_STREAM_END      1  */
@@ -132,6 +134,8 @@ const char * ZEXPORT zError(int err) {
     return ERR_MSG(err);
 }
 
+#endif  /* !Z_FREETYPE */
+
 #if defined(_WIN32_WCE) && _WIN32_WCE < 0x800
     /* The older Microsoft C Run-Time Library for Windows CE doesn't have
      * errno.  We define it as a global variable to simplify porting.
@@ -149,6 +153,7 @@ void ZLIB_INTERNAL zmemcpy(Bytef* dest, const Bytef* source, uInt len) {
     } while (--len != 0);
 }
 
+#ifndef Z_FREETYPE
 int ZLIB_INTERNAL zmemcmp(const Bytef* s1, const Bytef* s2, uInt len) {
     uInt j;
 
@@ -164,6 +169,7 @@ void ZLIB_INTERNAL zmemzero(Bytef* dest, uInt len) {
         *dest++ = 0;  /* ??? to be unrolled */
     } while (--len != 0);
 }
+#endif  /* !Z_FREETYPE */
 #endif
 
 #ifndef Z_SOLO
diff --git b/src/gzip/zutil.h a/src/gzip/zutil.h
index 902a304cc..a2c046a1f 100644
--- b/src/gzip/zutil.h
+++ a/src/gzip/zutil.h
@@ -53,8 +53,10 @@ typedef unsigned long  ulg;
 #  endif
 #endif
 
+#ifndef Z_FREETYPE
 extern z_const char * const z_errmsg[10]; /* indexed by 2-zlib_error */
 /* (size given to avoid silly warnings with Visual C++) */
+#endif
 
 #define ERR_MSG(err) z_errmsg[Z_NEED_DICT-(err)]
 
@@ -188,6 +190,8 @@ extern z_const char * const z_errmsg[10]; /* indexed by 2-zlib_error */
   #pragma warn -8066
 #endif
 
+#ifndef Z_FREETYPE
+
 /* provide prototypes for these when building zlib without LFS */
 #if !defined(_WIN32) && \
     (!defined(_LARGEFILE64_SOURCE) || _LFS64_LARGEFILE-0 == 0)
@@ -196,6 +200,8 @@ extern z_const char * const z_errmsg[10]; /* indexed by 2-zlib_error */
     ZEXTERN uLong ZEXPORT crc32_combine_gen64(z_off_t);
 #endif
 
+#endif  /* !Z_FREETYPE */
+
         /* common defaults */
 
 #ifndef OS_CODE
@@ -227,9 +233,9 @@ extern z_const char * const z_errmsg[10]; /* indexed by 2-zlib_error */
 #    define zmemcmp _fmemcmp
 #    define zmemzero(dest, len) _fmemset(dest, 0, len)
 #  else
-#    define zmemcpy memcpy
-#    define zmemcmp memcmp
-#    define zmemzero(dest, len) memset(dest, 0, len)
+#    define zmemcpy ft_memcpy
+#    define zmemcmp ft_memcmp
+#    define zmemzero(dest, len) ft_memset(dest, 0, len)
 #  endif
 #else
    void ZLIB_INTERNAL zmemcpy(Bytef* dest, const Bytef* source, uInt len);
