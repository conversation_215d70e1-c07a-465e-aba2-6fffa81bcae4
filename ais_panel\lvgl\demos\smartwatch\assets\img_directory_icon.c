
#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: directory.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_directory_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xFF,0xE9,0xEC,0xC9,0xEC,0xC9,0xE4,0xA8,0xE4,0xA8,0xE4,0xA8,0xE4,0x88,0xE4,0x88,0xE4,0x68,0xDC,0x68,0xDC,0x48,0xDC,0x48,0xDC,0x48,0xDC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xE9,0xEC,0xC9,0xEC,0xC9,0xE4,0xA8,0xE4,0xA8,0xE4,0xA8,0xE4,0x88,0xE4,0x88,0xE4,0x68,0xDC,0x68,0xDC,0x48,0xDC,0x48,0xDC,0x48,0xDC,0x27,0xD4,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC9,0xEC,0xC9,0xE4,0xA8,0xE4,0xA8,0xE4,0xA8,0xE4,0x88,0xE4,0x88,0xE4,0x68,0xDC,0x68,0xDC,0x48,0xDC,0x48,0xDC,0x48,0xDC,0x28,0xD4,
0x28,0xD4,0x07,0xD4,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC9,0xE4,0xC8,0xE4,0xE8,0xE4,0x08,0xE5,0x08,0xE5,0xE8,0xE4,0xE8,0xE4,0xC8,0xE4,0xC8,0xE4,0xC8,0xE4,0xA8,0xE4,0xA8,0xDC,0xA8,0xDC,0x88,0xDC,0x88,0xDC,0x89,0xED,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0x10,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xC8,0xE4,0xE9,0xED,0x29,0xF6,0x29,0xF6,0x29,0xF6,0x29,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xC8,0xED,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x89,0xED,0x29,0xF6,0x29,0xF6,0x29,0xF6,0x29,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,
0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC9,0xED,0x29,0xF6,0x29,0xF6,0x29,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xE9,0xED,0x29,0xF6,0x29,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x29,0xF6,0x29,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,
0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x29,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x29,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0xF6,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,
0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0xF6,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x09,0xF6,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0xF6,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,
0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x49,0xF5,0x49,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE9,0xF5,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,
0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE9,0xF5,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xE9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x29,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,
0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x29,0xF5,0x29,0xF5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xFF,0xC9,0xF5,0xC9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0xA9,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x89,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x69,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x49,0xF5,0x29,0xF5,0x29,0xF5,0xE0,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x87,0xEC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xAC,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x5D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x99,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x4E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xED,0x50,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x2D,0x02,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xA3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC4,0x0B,0x00,0x00,0x00,0x00,0x00,0x00,0xCB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x74,0x00,0x00,0x00,0x00,0x00,0x00,0xDA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9E,0x00,0x00,0x00,
0x00,0x00,0x00,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,
0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,
0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,
0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,
0x00,0x00,0x00,0x99,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5A,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x86,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEB,0x85,0x01,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_directory_icon = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.w = 32,
    .header.h = 32,
    .data_size = sizeof(img_directory_icon_data),
    .header.cf = LV_COLOR_FORMAT_RGB565A8,
    .data = img_directory_icon_data
};

