#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif


#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_IMG_RENDER_LVGL_LOGO_RGB565
    #define LV_ATTRIBUTE_IMG_IMG_RENDER_LVGL_LOGO_RGB565
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_IMG_RENDER_LVGL_LOGO_RGB565 uint8_t
img_render_lvgl_logo_rgb565_map[] = {
    /*Pixel format: Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
    0xff,0xff,0xff,0xff,0xd7,0xbd,0xcf,0x7b,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0xae,0x73,0x92,0x94,0x5d,0xef,0xff,0xff,
    0xff,0xff,0xb2,0x94,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x86,0x29,0x45,0x21,0x45,0x21,0x66,0x29,0x8a,0x4a,0xfb,0xde,
    0xf7,0xbd,0x86,0x29,0xa6,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0x86,0x29,0x0c,0x5b,0x4d,0x6b,0xa6,0x31,0x45,0x21,0x4d,0x63,
    0xcf,0x7b,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0x65,0x29,0xcf,0x7b,0xdf,0xff,0xff,0xff,0xf3,0x9c,0x65,0x29,0x28,0x42,
    0xae,0x73,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0x45,0x21,0x96,0xad,0xff,0xff,0xff,0xff,0xdb,0xde,0x86,0x29,0x08,0x3a,
    0xae,0x73,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0x86,0x29,0x0c,0x5b,0x5d,0xef,0x9e,0xf7,0x10,0x7c,0x65,0x29,0x28,0x42,
    0xae,0x73,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0x86,0x29,0x28,0x42,0x49,0x42,0x86,0x29,0xa6,0x31,0x28,0x42,
    0x8e,0x73,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0x86,0x29,0x86,0x29,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x10,0x7c,0x45,0x21,0x45,0x21,0x45,0x21,0x45,0x21,0x45,0x21,0x45,0x21,0x45,0x21,0x45,0x21,0x66,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x7d,0xef,0x96,0xad,0x55,0xa5,0x76,0xa5,0x76,0xa5,0x76,0xa5,0x76,0xa5,0x76,0xa5,0x55,0xa5,0x4d,0x63,0x66,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0xff,0xff,0xf9,0xfe,0xd8,0xfe,0xd8,0xfe,0xd8,0xfe,0xd8,0xfe,0xd8,0xfe,0xd8,0xfe,0x1a,0xff,0xff,0xff,0xcf,0x73,0x65,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x30,0xfd,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0x81,0xf2,0x77,0xfe,0x7a,0xc6,0x65,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x6c,0xf4,0xc3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0x82,0xf2,0xf3,0xfd,0x9a,0xc6,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x8c,0xf4,0xc3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xa2,0xf2,0xf4,0xfd,0x7a,0xc6,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x8c,0xf4,0xc3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xa2,0xf2,0xf4,0xfd,0x7a,0xc6,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x8c,0xf4,0xc3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xa2,0xf2,0xf4,0xfd,0x7a,0xc6,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x8c,0xf4,0xc3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xa2,0xf2,0xf4,0xfd,0x7a,0xc6,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x8c,0xf4,0xc3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xe3,0xf2,0xa2,0xf2,0xf4,0xfd,0x7a,0xc6,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x6c,0xf4,0x81,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0xa2,0xf2,0x60,0xf2,0xf4,0xfd,0x7a,0xc6,0x24,0x21,0x65,0x29,0x65,0x29,0x65,0x29,0x65,0x29,0x65,0x29,0x65,0x29,0x65,0x29,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x98,0xfe,0x4b,0xf4,0x2a,0xf4,0x2b,0xf4,0x2b,0xf4,0x2b,0xf4,0x2b,0xf4,0x2a,0xf4,0x4b,0xf4,0x3b,0xff,0xbe,0xf7,0xef,0x7b,0x4d,0x63,0x6d,0x6b,0x6d,0x6b,0x6d,0x6b,0x6d,0x6b,0x6d,0x6b,0x6d,0x6b,0xaa,0x52,0x66,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xdf,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xde,0xff,0xf0,0x7b,0x65,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x79,0xcf,0xcf,0x7e,0xcf,0x76,0xcf,0x76,0xcf,0x76,0xcf,0x76,0xcf,0x76,0xcf,0x76,0xcf,0x76,0xdb,0xe7,0x7f,0xef,0x1f,0x64,0xbf,0x53,0xbf,0x53,0xbf,0x53,0xbf,0x53,0xbf,0x53,0xbf,0x53,0x9f,0x4b,0xff,0xad,0x9d,0xf7,0x08,0x3a,0xa6,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x12,0x97,0x8b,0x5e,0x8c,0x5e,0x8c,0x5e,0x8c,0x5e,0x8c,0x5e,0x8c,0x5e,0x8c,0x5e,0x6b,0x56,0x97,0xc7,0x9f,0xce,0xde,0x2a,0x1e,0x33,0x1e,0x33,0x1e,0x33,0x1e,0x33,0x1e,0x33,0x1e,0x33,0xde,0x2a,0x5f,0x6c,0xde,0xff,0x8a,0x4a,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x13,0x97,0x8c,0x5e,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x6b,0x56,0x97,0xc7,0xbf,0xd6,0xfe,0x32,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0xfe,0x32,0x9f,0x74,0xde,0xff,0x69,0x4a,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x13,0x97,0x8c,0x5e,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x6b,0x56,0x97,0xc7,0xbf,0xd6,0xfe,0x32,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0xfe,0x32,0x9f,0x74,0xde,0xff,0x69,0x4a,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x13,0x97,0x8c,0x5e,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x6b,0x56,0x97,0xc7,0xbf,0xd6,0xfe,0x32,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0xfe,0x32,0x9f,0x74,0xde,0xff,0x69,0x4a,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x42,
    0x12,0x97,0x8c,0x5e,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x6b,0x56,0x97,0xc7,0xbf,0xd6,0xfe,0x32,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0xfe,0x32,0x9f,0x74,0xde,0xff,0x69,0x4a,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa6,0x31,0x28,0x3a,
    0x56,0xb7,0x8c,0x5e,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x8c,0x66,0x6b,0x56,0x97,0xc7,0xbf,0xd6,0xfe,0x32,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0x3e,0x3b,0xfe,0x32,0x9f,0x74,0xde,0xff,0x69,0x4a,0x86,0x29,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0xa7,0x31,0x86,0x29,0x69,0x4a,
    0xdd,0xef,0xcf,0x7e,0x6b,0x56,0x8c,0x5e,0x8c,0x5e,0x8c,0x5e,0x8c,0x5e,0x8c,0x5e,0x6b,0x56,0x97,0xc7,0xbf,0xce,0xde,0x32,0x1e,0x3b,0x1e,0x3b,0x1e,0x3b,0x1e,0x3b,0x1e,0x3b,0x1e,0x3b,0xde,0x2a,0x7f,0x6c,0xde,0xff,0x49,0x42,0x86,0x29,0xa6,0x31,0xa6,0x31,0xa6,0x31,0xa6,0x31,0x86,0x29,0x45,0x21,0x14,0x9d,
    0xff,0xff,0xdd,0xe7,0xf2,0x8e,0xae,0x6e,0xae,0x6e,0xae,0x6e,0xae,0x6e,0xad,0x6e,0xae,0x6e,0xdb,0xdf,0x5f,0xef,0xdf,0x53,0x7f,0x43,0x7f,0x4b,0x7f,0x4b,0x7f,0x4b,0x7f,0x4b,0x7f,0x4b,0x5f,0x43,0x7f,0x9d,0xff,0xff,0xae,0x73,0xe7,0x39,0x28,0x42,0x28,0x42,0x28,0x42,0x08,0x3a,0x69,0x4a,0xf3,0x9c,0xff,0xff,
};

const lv_image_dsc_t img_render_lvgl_logo_rgb565 = {
  .header.magic = LV_IMAGE_HEADER_MAGIC,
  .header.cf = LV_COLOR_FORMAT_RGB565,
  .header.flags = 0,
  .header.w = 30,
  .header.h = 30,
  .header.stride = 60,
  .data_size = sizeof(img_render_lvgl_logo_rgb565_map),
  .data = img_render_lvgl_logo_rgb565_map,
};
