/*******************************************************************************
 * Size: 24 px
 * Bpp: 8
 * Opts: --bpp 8 --size 24 --no-compress --font RobotoSlab-Bold.ttf --range 32-127,176 --format lvgl -o font_lv_demo_high_res_roboto_slab_bold_24.c
 ******************************************************************************/

#include "../../../lvgl.h"

#if LV_USE_DEMO_HIGH_RES

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x9c, 0xff, 0xff, 0xd0, 0x9c, 0xff, 0xff, 0xd0,
    0x9c, 0xff, 0xff, 0xd0, 0x9c, 0xff, 0xff, 0xd0,
    0x9c, 0xff, 0xff, 0xd0, 0x9c, 0xff, 0xff, 0xd0,
    0x9c, 0xff, 0xff, 0xd0, 0x9c, 0xff, 0xff, 0xd0,
    0x9c, 0xff, 0xff, 0xd0, 0x9c, 0xff, 0xff, 0xd0,
    0x9c, 0xff, 0xff, 0xd0, 0x27, 0x40, 0x40, 0x34,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x4, 0x4, 0x3,
    0x9c, 0xff, 0xff, 0xd0, 0x9c, 0xff, 0xff, 0xd0,
    0x9c, 0xff, 0xff, 0xd0,

    /* U+0022 "\"" */
    0x60, 0xff, 0xff, 0x28, 0x6c, 0xff, 0xff, 0x1c,
    0x60, 0xff, 0xff, 0x28, 0x6c, 0xff, 0xff, 0x1c,
    0x60, 0xff, 0xff, 0x28, 0x6c, 0xff, 0xff, 0x1c,
    0x60, 0xff, 0xf5, 0x9, 0x6c, 0xff, 0xee, 0x4,
    0x60, 0xff, 0xae, 0x0, 0x6c, 0xff, 0xa3, 0x0,
    0x60, 0xff, 0x5e, 0x0, 0x6c, 0xff, 0x52, 0x0,
    0x15, 0x38, 0xa, 0x0, 0x18, 0x38, 0x7, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xce, 0xff, 0x56,
    0x0, 0x6a, 0xff, 0xbe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf8, 0xff, 0x2a, 0x0, 0x96,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x29, 0xff, 0xf9, 0x5, 0x0, 0xc3, 0xff, 0x63,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x56, 0xff,
    0xd2, 0x0, 0x0, 0xef, 0xff, 0x36, 0x0, 0x0,
    0x0, 0x3, 0x4, 0x4, 0x83, 0xff, 0xa7, 0x4,
    0x1e, 0xff, 0xfe, 0x10, 0x4, 0x2, 0x0, 0xa8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x88, 0x0, 0xa8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x88, 0x0, 0x0, 0x0, 0xc, 0xfe, 0xff,
    0x1e, 0x0, 0xa2, 0xff, 0x82, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xea, 0x0, 0x0,
    0xd6, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x72, 0xff, 0xb6, 0x0, 0xc, 0xfe, 0xff,
    0x1e, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa8, 0x0, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0x0,
    0x0, 0x0, 0x1, 0xf4, 0xff, 0x33, 0x0, 0x8d,
    0xff, 0x97, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x21, 0xff, 0xfe, 0xa, 0x0, 0xb8, 0xff, 0x6b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xff,
    0xdc, 0x0, 0x0, 0xe4, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x77, 0xff, 0xb1, 0x0,
    0xf, 0xff, 0xff, 0x15, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa3, 0xff, 0x85, 0x0, 0x3b, 0xff,
    0xe9, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x28, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0xc4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18,
    0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0x54, 0x9a, 0xff, 0xe5, 0x6b,
    0x16, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0xdd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x5d, 0x0,
    0x0, 0x0, 0x17, 0xec, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x42, 0x0, 0x0, 0x82,
    0xff, 0xff, 0xfd, 0x58, 0x6, 0x34, 0xe5, 0xff,
    0xff, 0xc6, 0x0, 0x0, 0xb7, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x69, 0xff, 0xff, 0xfe, 0xd,
    0x0, 0xb9, 0xff, 0xff, 0xb9, 0x0, 0x0, 0x0,
    0x30, 0xd0, 0xd0, 0xd0, 0x1d, 0x0, 0x8b, 0xff,
    0xff, 0xf9, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x22, 0xf5, 0xff, 0xff, 0xfa,
    0x97, 0x2b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x46, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xba,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19,
    0x98, 0xf7, 0xff, 0xff, 0xff, 0xff, 0x8c, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x6f,
    0xe1, 0xff, 0xff, 0xff, 0x7d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xc0, 0xff,
    0xff, 0xf2, 0x6, 0x3e, 0xc4, 0xc4, 0xc4, 0x7,
    0x0, 0x0, 0x0, 0x4a, 0xff, 0xff, 0xff, 0x2b,
    0x3c, 0xff, 0xff, 0xff, 0x37, 0x0, 0x0, 0x0,
    0x4c, 0xff, 0xff, 0xff, 0x2d, 0x8, 0xf1, 0xff,
    0xff, 0xcc, 0x22, 0x0, 0x18, 0xc4, 0xff, 0xff,
    0xf5, 0x8, 0x0, 0x75, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xfe, 0xff, 0xff, 0xff, 0x87, 0x0, 0x0,
    0x1, 0x84, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x94, 0x4, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x76, 0xc2, 0xff, 0xd4, 0x7e, 0x2c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x54, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x54, 0xff, 0x88, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x20, 0xaa, 0xef, 0xfd, 0xdb, 0x77, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xe3, 0xff, 0xfd, 0xf0, 0xff, 0xff,
    0x86, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x59, 0xff, 0xf6, 0x25, 0x1, 0x88,
    0xff, 0xec, 0x0, 0x0, 0x2, 0xc2, 0xd4, 0x1d,
    0x0, 0x0, 0x0, 0x6c, 0xff, 0xd8, 0x0, 0x0,
    0x44, 0xff, 0xff, 0x0, 0x0, 0x6b, 0xff, 0xb6,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xf5, 0x22,
    0x0, 0x82, 0xff, 0xed, 0x0, 0x1f, 0xf3, 0xf3,
    0x1e, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe4, 0xff,
    0xfd, 0xf0, 0xff, 0xff, 0x87, 0x0, 0xb9, 0xff,
    0x6d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x22,
    0xaa, 0xef, 0xfd, 0xdc, 0x77, 0x3, 0x60, 0xff,
    0xc6, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xef,
    0xf9, 0x29, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xae,
    0xff, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x56,
    0xff, 0xd4, 0xd, 0x88, 0xe5, 0xfd, 0xe0, 0x7c,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0xe9, 0xfd, 0x38, 0xa6, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0x8c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa5, 0xff, 0x91, 0x1e, 0xff, 0xff, 0x5d, 0x0,
    0x78, 0xff, 0xf7, 0x9, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xe0, 0xb, 0x41, 0xff, 0xff, 0x4,
    0x0, 0x20, 0xff, 0xff, 0x25, 0x0, 0x0, 0x0,
    0xe, 0xe3, 0xff, 0x47, 0x0, 0x42, 0xff, 0xff,
    0x4, 0x0, 0x1f, 0xff, 0xff, 0x26, 0x0, 0x0,
    0x0, 0x64, 0xff, 0xa2, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0x60, 0x0, 0x67, 0xff, 0xf9, 0xa, 0x0,
    0x0, 0x0, 0x0, 0x39, 0x14, 0x0, 0x0, 0x0,
    0xa6, 0xff, 0xff, 0xf0, 0xff, 0xff, 0x8e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x88, 0xe5, 0xfe, 0xe1, 0x7f, 0x4,
    0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x18, 0x96, 0xe1, 0xfc, 0xec,
    0xa5, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xe4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xeb, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9e, 0xff, 0xff, 0xf5, 0xad, 0xdd,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe5, 0xff, 0xff, 0x53, 0x0, 0xf,
    0xec, 0xff, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf3, 0xff, 0xff, 0x1e, 0x0, 0x0,
    0xd7, 0xff, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc7, 0xff, 0xff, 0x61, 0x0, 0x54,
    0xff, 0xff, 0x78, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5a, 0xff, 0xff, 0xe6, 0x9b, 0xff,
    0xff, 0xc2, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xb9, 0xff, 0xff, 0xff, 0xff,
    0x99, 0x9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0xe4, 0xff, 0xff, 0xff, 0xe3,
    0x13, 0x0, 0x0, 0x15, 0x54, 0x54, 0x1c, 0x0,
    0x0, 0x48, 0xf9, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xc7, 0xa, 0x0, 0x4f, 0xff, 0xff, 0x49, 0x0,
    0x13, 0xf0, 0xff, 0xff, 0x87, 0x3d, 0xf8, 0xff,
    0xff, 0xb2, 0x4, 0x8a, 0xff, 0xff, 0x2a, 0x0,
    0x60, 0xff, 0xff, 0xfe, 0xd, 0x0, 0x51, 0xfd,
    0xff, 0xff, 0xa2, 0xec, 0xff, 0xea, 0x2, 0x0,
    0x74, 0xff, 0xff, 0xfc, 0x6, 0x0, 0x0, 0x68,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8c, 0x0, 0x0,
    0x50, 0xff, 0xff, 0xff, 0x76, 0x0, 0x0, 0x0,
    0x9a, 0xff, 0xff, 0xff, 0xe7, 0x10, 0x0, 0x0,
    0x8, 0xe2, 0xff, 0xff, 0xff, 0xc6, 0xa9, 0xce,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x3b, 0x0, 0x0,
    0x0, 0x36, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xe5, 0x16, 0x0,
    0x0, 0x0, 0x17, 0x8b, 0xd4, 0xf8, 0xfd, 0xe3,
    0xa9, 0x4d, 0x26, 0xee, 0xff, 0xff, 0xbe, 0x3,

    /* U+0027 "'" */
    0x60, 0xff, 0xff, 0x28, 0x60, 0xff, 0xff, 0x28,
    0x60, 0xff, 0xff, 0x28, 0x60, 0xff, 0xf5, 0x9,
    0x60, 0xff, 0xae, 0x0, 0x60, 0xff, 0x5e, 0x0,
    0x15, 0x38, 0xa, 0x0,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x0, 0x33, 0xcd, 0x1d, 0x0,
    0x0, 0x0, 0x4a, 0xf7, 0xff, 0x68, 0x0, 0x0,
    0x2c, 0xf5, 0xff, 0xb4, 0x5, 0x0, 0x2, 0xca,
    0xff, 0xe4, 0xf, 0x0, 0x0, 0x58, 0xff, 0xff,
    0x64, 0x0, 0x0, 0x0, 0xc6, 0xff, 0xee, 0x8,
    0x0, 0x0, 0x22, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x6b, 0xff, 0xff, 0x61, 0x0, 0x0, 0x0,
    0xa2, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0xcb,
    0xff, 0xff, 0xd, 0x0, 0x0, 0x0, 0xe5, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xf1, 0xff, 0xee,
    0x0, 0x0, 0x0, 0x0, 0xf2, 0xff, 0xed, 0x0,
    0x0, 0x0, 0x0, 0xea, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xd3, 0xff, 0xff, 0x7, 0x0, 0x0,
    0x0, 0xb1, 0xff, 0xff, 0x23, 0x0, 0x0, 0x0,
    0x7d, 0xff, 0xff, 0x4f, 0x0, 0x0, 0x0, 0x39,
    0xff, 0xff, 0x8d, 0x0, 0x0, 0x0, 0x2, 0xe2,
    0xff, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xff,
    0xff, 0x3f, 0x0, 0x0, 0x0, 0xf, 0xeb, 0xff,
    0xc2, 0x1, 0x0, 0x0, 0x0, 0x58, 0xff, 0xff,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xfb, 0x38,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x0,

    /* U+0029 ")" */
    0x0, 0xc4, 0x63, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x23, 0xff, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x71, 0xff, 0xff, 0x6d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb2, 0xff, 0xf8, 0x26, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xfe, 0xff, 0xaf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0x25, 0x0,
    0x0, 0x0, 0x0, 0x62, 0xff, 0xff, 0x84, 0x0,
    0x0, 0x0, 0x0, 0x21, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xee, 0xff, 0xfe, 0xf,
    0x0, 0x0, 0x0, 0x0, 0xca, 0xff, 0xff, 0x37,
    0x0, 0x0, 0x0, 0x0, 0xb6, 0xff, 0xff, 0x53,
    0x0, 0x0, 0x0, 0x0, 0xaa, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xa9, 0xff, 0xff, 0x62,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0xff, 0xff, 0x58,
    0x0, 0x0, 0x0, 0x0, 0xc2, 0xff, 0xff, 0x42,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0x1c,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0x9e, 0x0,
    0x0, 0x0, 0x0, 0x96, 0xff, 0xff, 0x44, 0x0,
    0x0, 0x0, 0xd, 0xf0, 0xff, 0xd6, 0x1, 0x0,
    0x0, 0x0, 0x7e, 0xff, 0xff, 0x4d, 0x0, 0x0,
    0x0, 0x33, 0xf8, 0xff, 0xa8, 0x0, 0x0, 0x0,
    0x1c, 0xeb, 0xff, 0xcc, 0xd, 0x0, 0x0, 0x0,
    0x7, 0xed, 0xb1, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x0, 0x0, 0xa2, 0xff, 0x86, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x95,
    0xff, 0x76, 0x0, 0x0, 0x0, 0x0, 0x1, 0x2c,
    0x0, 0x0, 0x88, 0xff, 0x68, 0x0, 0x0, 0x16,
    0x0, 0x36, 0xff, 0xc3, 0x58, 0x81, 0xff, 0x5e,
    0x53, 0xbd, 0xed, 0x4, 0x68, 0xfd, 0xff, 0xff,
    0xfd, 0xff, 0xf7, 0xff, 0xff, 0xff, 0x3b, 0x0,
    0x13, 0x5a, 0xaf, 0xff, 0xff, 0xfa, 0xb7, 0x6c,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xff,
    0xfc, 0x3b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0xff, 0xe7, 0x67, 0xff, 0xe4, 0x13, 0x0, 0x0,
    0x0, 0x2d, 0xf5, 0xff, 0x58, 0x0, 0xbe, 0xff,
    0xb6, 0x1, 0x0, 0x0, 0x15, 0xaf, 0xbb, 0x0,
    0x0, 0x2b, 0xf8, 0x82, 0x3, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0x1e, 0x0, 0x0,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x18, 0x80, 0x80, 0x80,
    0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x30, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x30, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x69, 0xf8, 0xf8, 0xf8, 0xfa, 0xff, 0xff,
    0xff, 0xf9, 0xf8, 0xf8, 0xf8, 0x42, 0x6c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x44, 0x6c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x30, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x30, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0,

    /* U+002C "," */
    0x0, 0x60, 0xff, 0xff, 0xb4, 0x0, 0x60, 0xff,
    0xff, 0xb1, 0x0, 0x61, 0xff, 0xff, 0xad, 0x0,
    0x70, 0xff, 0xff, 0x91, 0x0, 0xa1, 0xff, 0xff,
    0x41, 0xb, 0xef, 0xff, 0xbb, 0x0, 0x16, 0xae,
    0xe3, 0x1d, 0x0, 0x0, 0x0, 0x9, 0x0, 0x0,

    /* U+002D "-" */
    0x4f, 0xa8, 0xa8, 0xa8, 0xa8, 0xa8, 0xa8, 0x18,
    0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x24,
    0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x24,

    /* U+002E "." */
    0x2, 0x4, 0x4, 0x4, 0x7c, 0xff, 0xff, 0xec,
    0x7c, 0xff, 0xff, 0xec, 0x7c, 0xff, 0xff, 0xec,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x68,
    0xff, 0xff, 0xbc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcb, 0xff, 0xff, 0x5a, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xf0,
    0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d,
    0xff, 0xff, 0x98, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xea, 0xff, 0xff, 0x35, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x50, 0xff, 0xff, 0xd4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb2,
    0xff, 0xff, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x18, 0xfc, 0xff, 0xfb, 0x18, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x74, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd7,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xff, 0xff, 0xea, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x99, 0xff, 0xff, 0x8c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf2,
    0xff, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xff, 0xcb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbe, 0xff, 0xff, 0x68,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0xfe,
    0xff, 0xf8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x80, 0xff, 0xff, 0xa7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xe2, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0x80,
    0x80, 0x7c, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x16, 0x8b, 0xda, 0xfb, 0xf7, 0xce,
    0x75, 0x8, 0x0, 0x0, 0x0, 0x2b, 0xe8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0x13, 0x0,
    0x6, 0xda, 0xff, 0xff, 0xf9, 0xb3, 0xbf, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x5a, 0xff, 0xff, 0xfe,
    0x39, 0x0, 0x0, 0x6b, 0xff, 0xff, 0xff, 0x2d,
    0xa5, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0x77, 0xcc, 0xff, 0xff, 0x97,
    0x0, 0x0, 0x0, 0x0, 0xc7, 0xff, 0xff, 0x9f,
    0xda, 0xff, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0xae, 0xdc, 0xff, 0xff, 0x8c,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0xb0,
    0xdc, 0xff, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0xb0, 0xdc, 0xff, 0xff, 0x8c,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0xb0,
    0xda, 0xff, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0xae, 0xcc, 0xff, 0xff, 0x97,
    0x0, 0x0, 0x0, 0x0, 0xc7, 0xff, 0xff, 0x9f,
    0xa5, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0x79, 0x5b, 0xff, 0xff, 0xfe,
    0x39, 0x0, 0x0, 0x66, 0xff, 0xff, 0xff, 0x2f,
    0x6, 0xdb, 0xff, 0xff, 0xf9, 0xb3, 0xbc, 0xfe,
    0xff, 0xff, 0xb5, 0x0, 0x0, 0x2b, 0xe8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x15, 0x0,
    0x0, 0x0, 0x16, 0x8c, 0xda, 0xfb, 0xf9, 0xd1,
    0x7a, 0xa, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x2,
    0x0, 0x0, 0x0, 0x1b, 0x4a, 0x7a, 0xa8, 0xd6,
    0xfd, 0x10, 0x0, 0x0, 0x43, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x44, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x5, 0x27,
    0x44, 0x98, 0xff, 0xff, 0xff, 0x65, 0x3c, 0x1c,
    0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0,

    /* U+0032 "2" */
    0x0, 0x0, 0x1, 0x5a, 0xbd, 0xee, 0xfd, 0xed,
    0xbb, 0x5a, 0x1, 0x0, 0x0, 0x0, 0x8, 0xb5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf,
    0x5, 0x0, 0x0, 0x92, 0xff, 0xff, 0xff, 0xd7,
    0xb2, 0xe5, 0xff, 0xff, 0xff, 0x7c, 0x0, 0x12,
    0xfa, 0xff, 0xff, 0x9b, 0x1, 0x0, 0xa, 0xcd,
    0xff, 0xff, 0xe4, 0x0, 0x48, 0xff, 0xff, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x69, 0xff, 0xff, 0xff,
    0xe, 0x28, 0x78, 0x78, 0x78, 0x3, 0x0, 0x0,
    0x0, 0x69, 0xff, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbe, 0xff,
    0xff, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0x55, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x36, 0xf6,
    0xff, 0xff, 0xaa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0xeb, 0xff, 0xff, 0xcb, 0xb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0xe6,
    0xff, 0xff, 0xd9, 0x16, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xe1, 0xff, 0xff, 0xdf, 0x1d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xdb,
    0xff, 0xff, 0xe5, 0x23, 0x0, 0x7, 0x38, 0x38,
    0x1f, 0x0, 0x13, 0xd4, 0xff, 0xff, 0xeb, 0x2a,
    0x0, 0x0, 0x30, 0xff, 0xff, 0x8c, 0x7, 0xcc,
    0xff, 0xff, 0xff, 0xd0, 0xa8, 0xa8, 0xa8, 0xc0,
    0xff, 0xff, 0x8c, 0x18, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8c,
    0x18, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8c,

    /* U+0033 "3" */
    0x0, 0x0, 0x7, 0x6c, 0xc4, 0xef, 0xfe, 0xf2,
    0xc9, 0x7a, 0xf, 0x0, 0x0, 0x0, 0x17, 0xd3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0x29, 0x0, 0x0, 0xb2, 0xff, 0xff, 0xff, 0xd1,
    0xaa, 0xcc, 0xff, 0xff, 0xff, 0xcc, 0x0, 0xe,
    0xfe, 0xff, 0xff, 0x8a, 0x0, 0x0, 0x0, 0x8e,
    0xff, 0xff, 0xff, 0x20, 0xb, 0x5c, 0x5c, 0x5c,
    0x15, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0x2e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x82, 0xff, 0xff, 0xed, 0x7, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x94, 0x97, 0xc0, 0xff, 0xff,
    0xfa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0xff, 0xff, 0xe9, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd8, 0x24, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x32, 0xcf, 0xff, 0xff,
    0xd6, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0x49, 0x8,
    0x18, 0x18, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf9, 0xff, 0xff, 0x72, 0x4c, 0xff, 0xff, 0xff,
    0x19, 0x0, 0x0, 0x0, 0x10, 0xff, 0xff, 0xff,
    0x6a, 0x1e, 0xff, 0xff, 0xff, 0x9a, 0x2, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0x32, 0x0, 0xab,
    0xff, 0xff, 0xff, 0xd4, 0xa9, 0xd3, 0xff, 0xff,
    0xff, 0xb7, 0x0, 0x0, 0x11, 0xc5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x12, 0x0,
    0x0, 0x0, 0x4, 0x61, 0xbc, 0xee, 0xfe, 0xee,
    0xbf, 0x63, 0x4, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x60, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14,
    0xec, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x41, 0xff, 0xff, 0xe9, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xd9, 0xff, 0xe5,
    0x77, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x80, 0xff, 0xff, 0x52, 0x68, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x27, 0xf9, 0xff,
    0xb0, 0x0, 0x68, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xff, 0xf3, 0x1d, 0x0, 0x68,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x60, 0xff,
    0xff, 0x6c, 0x0, 0x0, 0x68, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x14, 0xec, 0xff, 0xe8, 0x4a, 0x48,
    0x48, 0x93, 0xff, 0xff, 0xff, 0x48, 0x44, 0x79,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x6a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x68, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x26, 0x95, 0xff, 0xff,
    0xff, 0x42, 0x12, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd4,

    /* U+0035 "5" */
    0x0, 0x2, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x19, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x34, 0xff, 0xff, 0xf8, 0xc4,
    0xc4, 0xc4, 0xc4, 0xe2, 0xff, 0xe4, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xc7, 0x0, 0x0, 0x0, 0x0,
    0x51, 0xec, 0xd2, 0x0, 0x0, 0x6a, 0xff, 0xff,
    0xa4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0xff, 0xff, 0x81, 0x3f, 0x7a,
    0x7d, 0x56, 0xd, 0x0, 0x0, 0x0, 0x0, 0xa1,
    0xff, 0xff, 0xee, 0xff, 0xff, 0xff, 0xff, 0xeb,
    0x4b, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x31, 0x0,
    0x0, 0xd7, 0xff, 0xff, 0xab, 0x1b, 0x7, 0x53,
    0xf8, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x3e, 0x44,
    0x44, 0xb, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff,
    0xf8, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4c, 0xff, 0xff, 0xff, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0xff, 0xff, 0xff, 0x29, 0x2f, 0xf4, 0xf4, 0xf4,
    0x25, 0x0, 0x0, 0x0, 0x66, 0xff, 0xff, 0xff,
    0x10, 0xd, 0xf9, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x9, 0xd0, 0xff, 0xff, 0xcc, 0x0, 0x0, 0x95,
    0xff, 0xff, 0xff, 0xd0, 0xa9, 0xe1, 0xff, 0xff,
    0xff, 0x54, 0x0, 0x0, 0x9, 0xb5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x86, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x5a, 0xbb, 0xee, 0xfd, 0xeb,
    0xb2, 0x44, 0x0, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x30, 0x9d, 0xde, 0xfa, 0xf9,
    0xd8, 0x95, 0x1a, 0x0, 0x0, 0x0, 0x80, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x76, 0xff, 0xff, 0xff, 0xef, 0xbb, 0xb6,
    0xda, 0xdf, 0x0, 0x0, 0x1e, 0xf9, 0xff, 0xff,
    0xa8, 0xb, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0,
    0x80, 0xff, 0xff, 0xee, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc1, 0xff, 0xff, 0xab,
    0xf, 0x64, 0x95, 0x94, 0x63, 0xc, 0x0, 0x0,
    0xe4, 0xff, 0xff, 0xca, 0xec, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0x32, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6, 0x10,
    0xf0, 0xff, 0xff, 0xe7, 0x51, 0xa, 0x15, 0x94,
    0xff, 0xff, 0xff, 0x77, 0xf0, 0xff, 0xff, 0x7d,
    0x0, 0x0, 0x0, 0x1, 0xd0, 0xff, 0xff, 0xc1,
    0xed, 0xff, 0xff, 0x7f, 0x0, 0x0, 0x0, 0x0,
    0x8d, 0xff, 0xff, 0xe4, 0xdb, 0xff, 0xff, 0x97,
    0x0, 0x0, 0x0, 0x0, 0x87, 0xff, 0xff, 0xe6,
    0xab, 0xff, 0xff, 0xd8, 0x1, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0xc5, 0x55, 0xff, 0xff, 0xff,
    0x68, 0x0, 0x0, 0x46, 0xff, 0xff, 0xff, 0x78,
    0x2, 0xc6, 0xff, 0xff, 0xff, 0xbf, 0xb4, 0xfb,
    0xff, 0xff, 0xe4, 0xf, 0x0, 0x19, 0xd0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x31, 0x0,
    0x0, 0x0, 0x7, 0x70, 0xca, 0xf5, 0xfa, 0xd9,
    0x8b, 0x16, 0x0, 0x0,

    /* U+0037 "7" */
    0x6c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa4, 0x6c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa4, 0x6c, 0xff, 0xff, 0xc4, 0xa8, 0xa8,
    0xa8, 0xa8, 0xa8, 0xef, 0xff, 0xff, 0x98, 0x6c,
    0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0x59,
    0xff, 0xff, 0xdf, 0x13, 0x25, 0x58, 0x58, 0x13,
    0x0, 0x0, 0x0, 0x25, 0xf4, 0xff, 0xfb, 0x35,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xc0, 0xff, 0xff, 0x81, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x55, 0xff, 0xff, 0xde,
    0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xd7, 0xff, 0xff, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xff,
    0xeb, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xad, 0xff, 0xff, 0x98, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf7,
    0xff, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xfe, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x85, 0xff, 0xff, 0xda, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb7, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe0, 0xff, 0xff, 0x8c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfc,
    0xff, 0xff, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x14, 0xff, 0xff, 0xff, 0x58,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x2, 0x5f, 0xbd, 0xed, 0xfd, 0xf3,
    0xc9, 0x76, 0xb, 0x0, 0x0, 0x0, 0x5, 0xb5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8,
    0x17, 0x0, 0x0, 0x71, 0xff, 0xff, 0xff, 0xe1,
    0xaa, 0xd6, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0,
    0xc0, 0xff, 0xff, 0xdc, 0xa, 0x0, 0x2, 0xb4,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0xce, 0xff, 0xff,
    0xa2, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0xfe,
    0x2, 0x0, 0x96, 0xff, 0xff, 0xde, 0xd, 0x0,
    0x2, 0xb4, 0xff, 0xff, 0xc9, 0x0, 0x0, 0x17,
    0xdf, 0xff, 0xff, 0xe5, 0xae, 0xd6, 0xff, 0xff,
    0xf3, 0x34, 0x0, 0x0, 0x0, 0x13, 0xc2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0x26, 0x0, 0x0,
    0x0, 0x2, 0x95, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbb, 0xf, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xee, 0x47, 0x5, 0x31, 0xd5, 0xff, 0xff,
    0xb0, 0x0, 0x8, 0xf3, 0xff, 0xff, 0x6d, 0x0,
    0x0, 0x0, 0x39, 0xff, 0xff, 0xff, 0x2c, 0x31,
    0xff, 0xff, 0xff, 0x39, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0x61, 0x2d, 0xff, 0xff, 0xff,
    0x4f, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0x5d, 0x9, 0xf6, 0xff, 0xff, 0xc2, 0x8, 0x0,
    0x1, 0x94, 0xff, 0xff, 0xff, 0x2f, 0x0, 0x92,
    0xff, 0xff, 0xff, 0xe1, 0xa9, 0xd0, 0xff, 0xff,
    0xff, 0xc3, 0x0, 0x0, 0xa, 0xbb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0x1e, 0x0,
    0x0, 0x0, 0x3, 0x5e, 0xbb, 0xec, 0xfd, 0xf3,
    0xc9, 0x76, 0xc, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x0, 0x34, 0xab, 0xe9, 0xfc, 0xe9,
    0xad, 0x39, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b,
    0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x74,
    0x0, 0x0, 0x0, 0x46, 0xfe, 0xff, 0xff, 0xe6,
    0xac, 0xdf, 0xff, 0xff, 0xff, 0x46, 0x0, 0x0,
    0xcd, 0xff, 0xff, 0xda, 0x10, 0x0, 0x8, 0xc5,
    0xff, 0xff, 0xca, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0x63, 0x0, 0x0, 0x0, 0x49, 0xff, 0xff, 0xff,
    0x1b, 0x3e, 0xff, 0xff, 0xff, 0x2f, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xff, 0xff, 0x46, 0x3c, 0xff,
    0xff, 0xff, 0x34, 0x0, 0x0, 0x0, 0x10, 0xff,
    0xff, 0xff, 0x55, 0x19, 0xff, 0xff, 0xff, 0x6e,
    0x0, 0x0, 0x0, 0x11, 0xff, 0xff, 0xff, 0x58,
    0x0, 0xcb, 0xff, 0xff, 0xeb, 0x41, 0x4, 0x26,
    0xaf, 0xff, 0xff, 0xff, 0x58, 0x0, 0x42, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x58, 0x0, 0x0, 0x5b, 0xf2, 0xff, 0xff,
    0xff, 0xfe, 0x89, 0xff, 0xff, 0xff, 0x54, 0x0,
    0x0, 0x0, 0x12, 0x60, 0x82, 0x76, 0x29, 0x27,
    0xff, 0xff, 0xff, 0x3f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xfc,
    0xf, 0x0, 0x0, 0xa, 0x3, 0x0, 0x0, 0x0,
    0x2e, 0xeb, 0xff, 0xff, 0xae, 0x0, 0x0, 0x0,
    0x6c, 0xef, 0xbe, 0xa8, 0xbe, 0xfb, 0xff, 0xff,
    0xf3, 0x25, 0x0, 0x0, 0x0, 0x94, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xed, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xac, 0xe2, 0xfb, 0xf6, 0xd1,
    0x84, 0x16, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0xf0, 0xff, 0xff, 0x7c, 0xf0, 0xff, 0xff, 0x7c,
    0xf0, 0xff, 0xff, 0x7c, 0x8, 0x8, 0x8, 0x4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x4, 0x4, 0x2,
    0xf0, 0xff, 0xff, 0x7c, 0xf0, 0xff, 0xff, 0x7c,
    0xf0, 0xff, 0xff, 0x7c,

    /* U+003B ";" */
    0x0, 0xf0, 0xff, 0xff, 0x78, 0x0, 0xf0, 0xff,
    0xff, 0x78, 0x0, 0xf0, 0xff, 0xff, 0x78, 0x0,
    0x8, 0x8, 0x8, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb2, 0xf8, 0xf8, 0x55, 0x0,
    0xb8, 0xff, 0xff, 0x58, 0x0, 0xb9, 0xff, 0xff,
    0x55, 0x0, 0xc8, 0xff, 0xff, 0x39, 0x5, 0xf3,
    0xff, 0xe6, 0x4, 0x52, 0xff, 0xff, 0x66, 0x0,
    0x49, 0xe1, 0xab, 0x1, 0x0, 0x0, 0x7, 0x5,
    0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x29, 0x96, 0xb7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x4d, 0xba, 0xff, 0xff,
    0xc4, 0x0, 0x0, 0x0, 0xf, 0x72, 0xdc, 0xff,
    0xff, 0xff, 0xff, 0xc4, 0x0, 0x2b, 0x96, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xe8, 0x8c, 0x2a, 0x46,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xac, 0x4c, 0x4,
    0x0, 0x0, 0x50, 0xff, 0xff, 0xff, 0xae, 0x1a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xff,
    0xff, 0xff, 0xed, 0x96, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x35, 0xa2, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xd9, 0x7c, 0x1e, 0x0, 0x0, 0x0, 0x17, 0x7f,
    0xe7, 0xff, 0xff, 0xff, 0xff, 0xc4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x5b, 0xc9, 0xff, 0xff,
    0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x37, 0xa6, 0xbe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,

    /* U+003D "=" */
    0x63, 0xb0, 0xb0, 0xb0, 0xb0, 0xb0, 0xb0, 0xb0,
    0xb0, 0xb0, 0xa5, 0x90, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x90, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x63,
    0xb0, 0xb0, 0xb0, 0xb0, 0xb0, 0xb0, 0xb0, 0xb0,
    0xb0, 0xa5, 0x90, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x90, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xad, 0x7d, 0x16, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0xf9, 0xa3, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0xff, 0xff, 0xff, 0xca, 0x5e,
    0x7, 0x0, 0x0, 0x0, 0x38, 0x9c, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xea, 0x85, 0x1c, 0x0, 0x0,
    0x0, 0x8, 0x56, 0xb3, 0xfb, 0xff, 0xff, 0xff,
    0xfc, 0x55, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17,
    0xa3, 0xfe, 0xff, 0xff, 0x6c, 0x0, 0x0, 0x0,
    0x21, 0x80, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x6c,
    0xc, 0x60, 0xc0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc8, 0x5d, 0x7, 0xc4, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xa2, 0x35, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0xff, 0xe4, 0x7b, 0x16, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xc1, 0x56, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x30, 0xa1, 0xe1, 0xfc, 0xfa, 0xda,
    0x94, 0x21, 0x0, 0x0, 0x0, 0x61, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x40, 0x0,
    0x1f, 0xf9, 0xff, 0xff, 0xfc, 0xc7, 0xd1, 0xff,
    0xff, 0xff, 0xe7, 0xa, 0x6c, 0xff, 0xff, 0xfd,
    0x37, 0x0, 0x0, 0x69, 0xff, 0xff, 0xff, 0x4e,
    0x47, 0x8c, 0x8c, 0x78, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0x6c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xff, 0xff, 0xff, 0x58,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xf6, 0x14, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xfb, 0xff, 0xff, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x74, 0xfd, 0xff,
    0xff, 0x97, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xff, 0xff, 0xff, 0x84, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe9, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0xff, 0xff, 0xff, 0x5e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x30, 0x30, 0x30, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x4, 0x4, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xff, 0xff, 0xff, 0x54,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18,
    0xff, 0xff, 0xff, 0x54, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xff, 0xff, 0xff, 0x54,
    0x0, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x6b,
    0xb5, 0xe3, 0xfb, 0xfc, 0xeb, 0xc1, 0x7d, 0x1c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6e, 0xf0, 0xff, 0xff, 0xf9, 0xde,
    0xdd, 0xf4, 0xff, 0xff, 0xf9, 0x82, 0x4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xab, 0xff,
    0xff, 0xaa, 0x3f, 0x5, 0x0, 0x0, 0x1, 0x2a,
    0x86, 0xf6, 0xff, 0xb2, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa2, 0xff, 0xf6, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xe5,
    0xff, 0x89, 0x0, 0x0, 0x0, 0x0, 0x59, 0xff,
    0xfe, 0x49, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0xfd, 0xfc, 0x27,
    0x0, 0x0, 0x6, 0xe2, 0xff, 0x97, 0x0, 0x0,
    0x0, 0x3a, 0xba, 0xf4, 0xfa, 0xd9, 0x84, 0x12,
    0x0, 0x0, 0xa3, 0xff, 0x94, 0x0, 0x0, 0x56,
    0xff, 0xfb, 0x1a, 0x0, 0x0, 0x4f, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x41,
    0xff, 0xe4, 0x0, 0x0, 0xac, 0xff, 0xb4, 0x0,
    0x0, 0x13, 0xf0, 0xff, 0xd5, 0x2e, 0xc, 0xae,
    0xff, 0xc8, 0x0, 0x0, 0x7, 0xfb, 0xff, 0x1d,
    0x0, 0xe9, 0xff, 0x6f, 0x0, 0x0, 0x7b, 0xff,
    0xff, 0x32, 0x0, 0x0, 0xb0, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0xdd, 0xff, 0x3c, 0x14, 0xff, 0xff,
    0x3f, 0x0, 0x0, 0xce, 0xff, 0xd5, 0x0, 0x0,
    0x0, 0xc5, 0xff, 0x9c, 0x0, 0x0, 0x0, 0xce,
    0xff, 0x4a, 0x2e, 0xff, 0xff, 0x24, 0x0, 0xa,
    0xfd, 0xff, 0x9c, 0x0, 0x0, 0x0, 0xdc, 0xff,
    0x85, 0x0, 0x0, 0x0, 0xd2, 0xff, 0x48, 0x3a,
    0xff, 0xff, 0x18, 0x0, 0x2b, 0xff, 0xff, 0x7b,
    0x0, 0x0, 0x0, 0xf1, 0xff, 0x6f, 0x0, 0x0,
    0x0, 0xe9, 0xff, 0x32, 0x35, 0xff, 0xff, 0x19,
    0x0, 0x3b, 0xff, 0xff, 0x6e, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x58, 0x0, 0x0, 0x1c, 0xff, 0xfa,
    0x9, 0x23, 0xff, 0xff, 0x2f, 0x0, 0x2f, 0xff,
    0xff, 0x85, 0x0, 0x0, 0x35, 0xff, 0xff, 0x47,
    0x0, 0x0, 0x7d, 0xff, 0xb4, 0x0, 0x4, 0xf7,
    0xff, 0x5f, 0x0, 0x7, 0xf4, 0xff, 0xe3, 0x33,
    0x3e, 0xd8, 0xff, 0xff, 0x61, 0x0, 0x36, 0xf4,
    0xfe, 0x3b, 0x0, 0x0, 0xbf, 0xff, 0xa9, 0x0,
    0x0, 0x8b, 0xff, 0xff, 0xff, 0xff, 0xc4, 0xba,
    0xff, 0xf5, 0xcd, 0xfd, 0xff, 0x77, 0x0, 0x0,
    0x0, 0x69, 0xff, 0xfa, 0x1f, 0x0, 0x6, 0x95,
    0xf3, 0xf4, 0xa4, 0x14, 0x18, 0xb6, 0xf8, 0xf4,
    0xbf, 0x4b, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe9,
    0xff, 0xb8, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x55, 0xff, 0xff, 0xa7,
    0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7a, 0xff, 0xff, 0xdf, 0x6e, 0x23,
    0x2, 0x0, 0xc, 0x3a, 0x7f, 0x34, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x55, 0xe9, 0xff, 0xff, 0xff, 0xfd, 0xf7, 0xff,
    0xff, 0xff, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x63,
    0xb0, 0xe0, 0xf9, 0xfe, 0xea, 0xbe, 0x77, 0x14,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xfb,
    0xff, 0xff, 0xce, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0xff, 0xff, 0x2c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xca, 0xff, 0xff, 0xff, 0xff, 0x88,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xff, 0xff, 0xec, 0xff,
    0xff, 0xe3, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x84, 0xff, 0xff,
    0x88, 0xcf, 0xff, 0xff, 0x42, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xe2,
    0xff, 0xff, 0x32, 0x78, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x40, 0xff, 0xff, 0xdc, 0x0, 0x23, 0xff, 0xff,
    0xf2, 0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xff, 0x84, 0x0, 0x0,
    0xcc, 0xff, 0xff, 0x59, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf2, 0xff, 0xff, 0x2e,
    0x0, 0x0, 0x75, 0xff, 0xff, 0xb6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0xff, 0xff,
    0xd8, 0x0, 0x0, 0x0, 0x21, 0xff, 0xff, 0xfc,
    0x17, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x70, 0xff, 0xff, 0xe6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x90, 0xff, 0xff, 0xff, 0x2a,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x8b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd3, 0xff,
    0xff, 0x87, 0x0, 0x0, 0x3, 0x3e, 0xff, 0xff,
    0xff, 0x50, 0x1, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x95, 0xff, 0xff, 0xe2, 0x16, 0x0, 0x84, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x0,
    0x0, 0x94, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3c,
    0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4c, 0x0,
    0x0, 0x0, 0x0, 0x94, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x3c,

    /* U+0042 "B" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xef, 0xcb, 0x89, 0x21, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x5e, 0x0, 0x0,
    0xa, 0x3f, 0xb7, 0xff, 0xff, 0xf6, 0xa8, 0xa8,
    0xab, 0xce, 0xff, 0xff, 0xff, 0xfb, 0x28, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd7, 0xff, 0xff, 0x9d, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xe7, 0xff, 0xff, 0x82, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x3, 0x23, 0x9f, 0xff, 0xff, 0xf5, 0x20, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdd, 0x69, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xef, 0x60, 0x60,
    0x60, 0x64, 0x99, 0xfd, 0xff, 0xff, 0x79, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf5, 0xa,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xff, 0x37,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xff, 0xff, 0xff, 0x39,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xa9, 0xff, 0xff, 0xfd, 0x12,
    0x9, 0x3b, 0xb5, 0xff, 0xff, 0xf6, 0xa4, 0xa4,
    0xa4, 0xac, 0xe3, 0xff, 0xff, 0xff, 0xa8, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xca, 0x13, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xe8, 0xb9, 0x60, 0x5, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x0, 0x14, 0x7b, 0xc7, 0xef,
    0xfe, 0xf5, 0xd5, 0x9b, 0x42, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x58, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0x1f, 0x0, 0x0,
    0x66, 0xff, 0xff, 0xff, 0xfe, 0xcb, 0xa8, 0xb4,
    0xea, 0xff, 0xff, 0xff, 0x90, 0x0, 0x28, 0xfa,
    0xff, 0xff, 0xd5, 0x2b, 0x0, 0x0, 0x0, 0x4,
    0x84, 0xff, 0xff, 0x90, 0x0, 0xa7, 0xff, 0xff,
    0xed, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0x90, 0xb, 0xf8, 0xff, 0xff, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf3,
    0xf4, 0x89, 0x3d, 0xff, 0xff, 0xff, 0x35, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x64, 0xff, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0x13, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0xff,
    0xff, 0x87, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x83, 0x84, 0x4a, 0x0, 0xae, 0xff, 0xff,
    0xf3, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0xff, 0xff, 0x90, 0x0, 0x2d, 0xfb, 0xff, 0xff,
    0xdf, 0x39, 0x0, 0x0, 0x0, 0x0, 0x58, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x6a, 0xff, 0xff, 0xff,
    0xff, 0xd2, 0xaa, 0xb0, 0xdb, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x58, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x57,
    0x0, 0x0, 0x0, 0x0, 0x11, 0x72, 0xbe, 0xea,
    0xfd, 0xf9, 0xe2, 0xb3, 0x6a, 0x11, 0x0,

    /* U+0044 "D" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe3, 0xaa, 0x47, 0x0, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb3, 0x10, 0x0, 0x0,
    0xa, 0x42, 0xb5, 0xff, 0xff, 0xf9, 0xa4, 0xa4,
    0xae, 0xe4, 0xff, 0xff, 0xff, 0xcd, 0xa, 0x0,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x3, 0x78, 0xff, 0xff, 0xff, 0x94, 0x0,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8d, 0xff, 0xff, 0xfc, 0x1f,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x11, 0xf9, 0xff, 0xff, 0x79,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbd, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x97, 0xff, 0xff, 0xd4,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff, 0xde,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x99, 0xff, 0xff, 0xd5,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0xf9, 0xff, 0xff, 0x79,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8e, 0xff, 0xff, 0xfd, 0x21,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x2, 0x76, 0xff, 0xff, 0xff, 0x97, 0x0,
    0x9, 0x3e, 0xb3, 0xff, 0xff, 0xf9, 0xa0, 0xa0,
    0xaa, 0xe2, 0xff, 0xff, 0xff, 0xcf, 0xc, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb7, 0x12, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe6, 0xad, 0x4d, 0x1, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x0, 0x58,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe8, 0x0, 0xa, 0x3f,
    0xb7, 0xff, 0xff, 0xf6, 0xa8, 0xa8, 0xa8, 0xa8,
    0xa8, 0xf4, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7d,
    0xa8, 0x98, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0xf6, 0xa8, 0xa8, 0xa8,
    0xa8, 0xa8, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x48,
    0x9c, 0x9c, 0x18, 0x0, 0x0, 0x88, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x87, 0xff,
    0xff, 0x28, 0x9, 0x3b, 0xb5, 0xff, 0xff, 0xf6,
    0xa4, 0xa4, 0xa4, 0xa4, 0xa4, 0xda, 0xff, 0xff,
    0x28, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x28,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x28,

    /* U+0046 "F" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3c, 0x58,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x3c, 0xa, 0x3f,
    0xb7, 0xff, 0xff, 0xf6, 0xa8, 0xa8, 0xa8, 0xa8,
    0xa8, 0xd6, 0xff, 0xff, 0x3c, 0x0, 0x0, 0x88,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7a, 0xff, 0xff, 0x3c, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xb0, 0xb0, 0x29, 0x0, 0x0, 0x88, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xf6, 0xa8,
    0xa8, 0xa8, 0xa8, 0xa8, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0x32, 0xb0, 0xff, 0xff, 0xee,
    0x42, 0x12, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x0, 0x21, 0x8b, 0xd1, 0xf5,
    0xfe, 0xf0, 0xce, 0x93, 0x3a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0xf9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xbc, 0x1e, 0x0,
    0x0, 0x0, 0x7a, 0xff, 0xff, 0xff, 0xfd, 0xc5,
    0xa8, 0xb6, 0xeb, 0xff, 0xff, 0xff, 0x94, 0x0,
    0x0, 0x36, 0xfe, 0xff, 0xff, 0xd0, 0x27, 0x0,
    0x0, 0x0, 0x4, 0x91, 0xff, 0xff, 0x94, 0x0,
    0x0, 0xb7, 0xff, 0xff, 0xea, 0x17, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x36, 0xff, 0xff, 0x94, 0x0,
    0x14, 0xfd, 0xff, 0xff, 0x7a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xa7, 0xa8, 0x61, 0x0,
    0x4a, 0xff, 0xff, 0xff, 0x2c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6a, 0xff, 0xff, 0xff, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x73, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x59, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x3,
    0x6d, 0xff, 0xff, 0xfe, 0x1, 0x0, 0x0, 0x0,
    0x0, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8,
    0x54, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xbd, 0xf6, 0xff, 0xff, 0xff, 0xff, 0x8,
    0x1e, 0xff, 0xff, 0xff, 0x66, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x68, 0xff, 0xff, 0xff, 0x8,
    0x0, 0xc6, 0xff, 0xff, 0xde, 0xc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x64, 0xff, 0xff, 0xff, 0x8,
    0x0, 0x45, 0xff, 0xff, 0xff, 0xc4, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x71, 0xff, 0xff, 0xff, 0x8,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xfc, 0xc6,
    0xa8, 0xb0, 0xd9, 0xff, 0xff, 0xff, 0xff, 0x8,
    0x0, 0x0, 0x0, 0x75, 0xf9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x89, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x86, 0xcc, 0xf2,
    0xff, 0xf9, 0xe1, 0xb5, 0x70, 0x16, 0x0, 0x0,

    /* U+0048 "H" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x18, 0x58, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb8, 0x0, 0x0, 0x0, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x18, 0x9, 0x3c,
    0xb5, 0xff, 0xff, 0xf0, 0x4e, 0x19, 0x0, 0x0,
    0x0, 0x28, 0x6f, 0xff, 0xff, 0xff, 0x86, 0x2f,
    0x2, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0xff, 0xff,
    0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x24, 0xff, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x24, 0xff, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0xff,
    0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0xf6, 0xa8, 0xa8, 0xa8, 0xa8, 0xa8,
    0xa8, 0xb4, 0xff, 0xff, 0xff, 0x44, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x44, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x24, 0xff, 0xff, 0xff, 0x44, 0x0,
    0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0xff, 0xff,
    0xff, 0x44, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x24, 0xff, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x24, 0xff, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x6, 0x32, 0xb0, 0xff, 0xff, 0xee,
    0x42, 0x12, 0x0, 0x0, 0x0, 0x1e, 0x64, 0xff,
    0xff, 0xff, 0x7e, 0x26, 0x1, 0x58, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb8, 0x0, 0x0, 0x0,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x18,

    /* U+0049 "I" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x9, 0x39, 0xb4, 0xff, 0xff, 0xef, 0x4a, 0x18,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x7, 0x36, 0xb2, 0xff, 0xff, 0xef, 0x46, 0x15,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x35, 0xa4, 0xff, 0xff, 0xf6,
    0x42, 0x12, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x78, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x78, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x78, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x78, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x78, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x78, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x56, 0x98, 0x98, 0x66, 0x0, 0x0,
    0x0, 0x0, 0x79, 0xff, 0xff, 0xef, 0x0, 0x0,
    0x79, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x93, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x39, 0xff,
    0xff, 0xff, 0x4d, 0x0, 0x0, 0x24, 0xef, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0xc1, 0xff, 0xff,
    0xfe, 0xc6, 0xba, 0xf7, 0xff, 0xff, 0xff, 0x4a,
    0x0, 0x0, 0x0, 0x1b, 0xd7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0x72, 0xc4, 0xef, 0xfe, 0xed,
    0xb9, 0x4f, 0x0, 0x0, 0x0, 0x0,

    /* U+004B "K" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0xe4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0xe4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x9, 0x39, 0xb4, 0xff,
    0xff, 0xef, 0x4a, 0x13, 0x0, 0x0, 0x15, 0xa4,
    0xff, 0xff, 0xff, 0x80, 0x31, 0x2, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x79, 0xff, 0xff, 0xff, 0x6d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x72, 0xff, 0xff, 0xff, 0x6f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x6b, 0xff, 0xff, 0xff, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x65, 0xff, 0xff, 0xff, 0x77,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x60, 0xfe, 0xff, 0xff,
    0xd1, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xfa, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe9, 0xff, 0xff, 0xfa, 0x36, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xff, 0xff, 0x84, 0x20, 0xef, 0xff, 0xff,
    0xdf, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xff, 0x87, 0x0, 0x0, 0x55,
    0xff, 0xff, 0xff, 0xaf, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xeb, 0x0, 0x0,
    0x0, 0x0, 0x9a, 0xff, 0xff, 0xff, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xd6, 0xff, 0xff,
    0xfa, 0x37, 0x0, 0x0, 0x7, 0x36, 0xb2, 0xff,
    0xff, 0xef, 0x44, 0x11, 0x0, 0x0, 0x9, 0x51,
    0xff, 0xff, 0xff, 0xdf, 0x44, 0x15, 0x58, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbc,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x84, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xbc,

    /* U+004C "L" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0x37, 0xb4, 0xff,
    0xff, 0xef, 0x4d, 0x1f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x20, 0x20, 0x10,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0xff, 0xff, 0x7c, 0x0, 0x0,
    0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x3a, 0xff, 0xff, 0x7c, 0x9, 0x3b, 0xb5, 0xff,
    0xff, 0xf6, 0xa4, 0xa4, 0xa4, 0xa4, 0xbd, 0xff,
    0xff, 0x7c, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7c,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7c,

    /* U+004D "M" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xba,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8,
    0x9, 0x39, 0xb4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x47, 0x14,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xe4, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x90,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xac, 0xff, 0xff,
    0xff, 0x4d, 0x0, 0x0, 0x0, 0x0, 0x7, 0xeb,
    0xff, 0xff, 0x9e, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0x8f, 0xbd, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x54, 0xff,
    0xff, 0xc8, 0x78, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0x96, 0x59, 0xff,
    0xff, 0xfc, 0x1a, 0x0, 0x0, 0x0, 0xb4, 0xff,
    0xff, 0x66, 0x78, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0x9d, 0x7, 0xec,
    0xff, 0xff, 0x7b, 0x0, 0x0, 0x18, 0xfc, 0xff,
    0xf6, 0xe, 0x7a, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xa3, 0x0, 0x8e,
    0xff, 0xff, 0xde, 0x2, 0x0, 0x78, 0xff, 0xff,
    0xa4, 0x0, 0x7c, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xaa, 0x0, 0x29,
    0xff, 0xff, 0xff, 0x44, 0x0, 0xd8, 0xff, 0xff,
    0x40, 0x0, 0x7c, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0xc5, 0xff, 0xff, 0xa9, 0x39, 0xff, 0xff, 0xde,
    0x1, 0x0, 0x80, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x60, 0xff, 0xff, 0xf9, 0xaf, 0xff, 0xff, 0x7d,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0xa, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x1e,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x96, 0xff, 0xff, 0xff, 0xff, 0xba, 0x0,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x7, 0x36, 0xb2, 0xff, 0xff, 0xd0, 0x41, 0x14,
    0x0, 0x31, 0xff, 0xff, 0xff, 0xff, 0x58, 0x0,
    0x9, 0x36, 0xad, 0xff, 0xff, 0xe4, 0x42, 0x11,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0xcb, 0xff, 0xff, 0xef, 0x8, 0x0,
    0x6c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x66, 0xff, 0xff, 0x94, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8,

    /* U+004E "N" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0x41, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x54, 0x58, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xda, 0x8, 0x0, 0x0, 0x0, 0xbc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x54, 0x9, 0x39,
    0xb4, 0xff, 0xff, 0xff, 0xff, 0x83, 0x0, 0x0,
    0x0, 0x17, 0x42, 0xba, 0xff, 0xff, 0xaf, 0x39,
    0x8, 0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x2b, 0x0, 0x0, 0x0, 0x0, 0x90, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc5, 0x2, 0x0, 0x0,
    0x0, 0x90, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0xcd, 0xff, 0xff, 0xff,
    0x68, 0x0, 0x0, 0x0, 0x90, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x88,
    0xa1, 0xff, 0xff, 0xf0, 0x18, 0x0, 0x0, 0x90,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x88,
    0xff, 0xff, 0x88, 0x13, 0xe8, 0xff, 0xff, 0xab,
    0x0, 0x0, 0x90, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0x88, 0x0, 0x54,
    0xff, 0xff, 0xff, 0x4c, 0x0, 0x90, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff,
    0x88, 0x0, 0x0, 0xae, 0xff, 0xff, 0xe1, 0xb,
    0x90, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x88, 0xff, 0xff, 0x88, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0x90, 0x90, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x60, 0xff, 0xff, 0xfd, 0xc3, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0x88, 0x0, 0x0, 0x0, 0x1, 0xba, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x7, 0x36, 0xb2, 0xff, 0xff, 0xb3,
    0x3d, 0x13, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x58, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb8, 0x0, 0x0, 0x0,
    0x2, 0xc3, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xf8, 0xff, 0xff,
    0x80, 0x0, 0x0,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x0, 0xc, 0x6f, 0xc3, 0xef,
    0xfe, 0xee, 0xc0, 0x6a, 0x9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xe3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xf7, 0xff, 0xff,
    0xfe, 0xcb, 0xb4, 0xd0, 0xff, 0xff, 0xff, 0xf6,
    0x3b, 0x0, 0x0, 0x0, 0xd, 0xe5, 0xff, 0xff,
    0xd8, 0x2a, 0x0, 0x0, 0x0, 0x32, 0xdd, 0xff,
    0xff, 0xe2, 0xc, 0x0, 0x0, 0x78, 0xff, 0xff,
    0xf8, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0xfa, 0xff, 0xff, 0x73, 0x0, 0x0, 0xd5, 0xff,
    0xff, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa4, 0xff, 0xff, 0xd2, 0x0, 0x11, 0xff,
    0xff, 0xff, 0x5d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5b, 0xff, 0xff, 0xff, 0x11, 0x31,
    0xff, 0xff, 0xff, 0x3b, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0xff, 0xff, 0xff, 0x31,
    0x3a, 0xff, 0xff, 0xff, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0x3a, 0x31, 0xff, 0xff, 0xff, 0x3b, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0xff, 0xff,
    0xff, 0x31, 0x11, 0xff, 0xff, 0xff, 0x5e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xff, 0xff, 0x11, 0x0, 0xd5, 0xff, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa5,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x78, 0xff, 0xff,
    0xf9, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xfa, 0xff, 0xff, 0x73, 0x0, 0x0, 0xd, 0xe5,
    0xff, 0xff, 0xda, 0x29, 0x0, 0x0, 0x0, 0x2d,
    0xdd, 0xff, 0xff, 0xe2, 0xc, 0x0, 0x0, 0x0,
    0x3c, 0xf7, 0xff, 0xff, 0xfe, 0xca, 0xb0, 0xcb,
    0xfe, 0xff, 0xff, 0xf6, 0x3b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xe2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x6f, 0xc3, 0xef,
    0xfe, 0xee, 0xc0, 0x6a, 0x9, 0x0, 0x0, 0x0,
    0x0,

    /* U+0050 "P" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xd7, 0x92, 0x26, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x68, 0x0, 0x0,
    0xa, 0x3f, 0xb7, 0xff, 0xff, 0xf6, 0xa8, 0xa8,
    0xa8, 0xbc, 0xf9, 0xff, 0xff, 0xff, 0x47, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x25, 0xe9, 0xff, 0xff, 0xc7, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x78, 0xff, 0xff, 0xfe, 0x8,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x59, 0xff, 0xff, 0xff, 0x14,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x96, 0xff, 0xff, 0xf4, 0x3,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x15, 0x71, 0xfd, 0xff, 0xff, 0xa6, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0x20, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcc, 0x2a, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xf6, 0xa8, 0xa8,
    0xa8, 0x9f, 0x7f, 0x3e, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x46, 0xbb, 0xff, 0xff, 0xf1, 0x5a, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x0, 0xa, 0x6d, 0xc0, 0xef,
    0xfe, 0xee, 0xc0, 0x6a, 0x9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3b, 0xe1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x3a, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xf6, 0xff, 0xff,
    0xfe, 0xcd, 0xb5, 0xcf, 0xff, 0xff, 0xff, 0xf7,
    0x3e, 0x0, 0x0, 0x0, 0xa, 0xe1, 0xff, 0xff,
    0xd9, 0x2b, 0x0, 0x0, 0x0, 0x2e, 0xdb, 0xff,
    0xff, 0xe5, 0xe, 0x0, 0x0, 0x71, 0xff, 0xff,
    0xf8, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0xf8, 0xff, 0xff, 0x7b, 0x0, 0x0, 0xce, 0xff,
    0xff, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa0, 0xff, 0xff, 0xd9, 0x0, 0xe, 0xfe,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x57, 0xff, 0xff, 0xff, 0x15, 0x2d,
    0xff, 0xff, 0xff, 0x3f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x33, 0xff, 0xff, 0xff, 0x35,
    0x36, 0xff, 0xff, 0xff, 0x35, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff,
    0x3e, 0x2d, 0xff, 0xff, 0xff, 0x3f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0xff, 0xff,
    0xff, 0x34, 0xe, 0xfe, 0xff, 0xff, 0x62, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x57, 0xff,
    0xff, 0xff, 0x11, 0x0, 0xce, 0xff, 0xff, 0xa8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x71, 0xff, 0xff,
    0xfa, 0x29, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21,
    0xf6, 0xff, 0xff, 0x6f, 0x0, 0x0, 0xa, 0xe1,
    0xff, 0xff, 0xdd, 0x2d, 0x0, 0x0, 0x0, 0x28,
    0xd8, 0xff, 0xff, 0xde, 0x9, 0x0, 0x0, 0x0,
    0x3a, 0xf6, 0xff, 0xff, 0xfe, 0xcb, 0xb1, 0xcb,
    0xfe, 0xff, 0xff, 0xf2, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0xe1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x92, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0x6b, 0xc0, 0xef,
    0xfc, 0xea, 0xe6, 0xff, 0xff, 0xff, 0xc6, 0x2d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0x92, 0xff, 0xff, 0xff,
    0xfb, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x43, 0xe0,
    0xff, 0xff, 0x45, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x9d, 0x8e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+0052 "R" */
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xda, 0x9e, 0x38, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0x0, 0x0,
    0xa, 0x3f, 0xb7, 0xff, 0xff, 0xf6, 0xa8, 0xa8,
    0xa9, 0xbb, 0xf6, 0xff, 0xff, 0xff, 0x64, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xdf, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x75, 0xff, 0xff, 0xfe, 0x3,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xfb, 0x1,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xd4, 0xff, 0xff, 0xc4, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xf6, 0xa8, 0xa8,
    0xa9, 0xb9, 0xf3, 0xff, 0xff, 0xff, 0x48, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0xd0, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x6d, 0xff, 0xff, 0xf9, 0x16, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x13, 0xf9, 0xff, 0xff, 0x7c, 0x0, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0xac, 0xff, 0xff, 0xe3, 0x4, 0x0,
    0xe, 0x4d, 0xbe, 0xff, 0xff, 0xf2, 0x60, 0x23,
    0x0, 0x0, 0x4c, 0xff, 0xff, 0xff, 0x8a, 0x2b,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x4, 0xe8, 0xff, 0xff, 0xff, 0xc8,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0xff, 0xc8,

    /* U+0053 "S" */
    0x0, 0x0, 0x16, 0x82, 0xcd, 0xf3, 0xfd, 0xf3,
    0xd5, 0x9d, 0x45, 0x1, 0x0, 0x0, 0x45, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xca, 0x28, 0x1d, 0xf4, 0xff, 0xff, 0xff, 0xcc,
    0xac, 0xba, 0xec, 0xff, 0xff, 0xff, 0x7c, 0x81,
    0xff, 0xff, 0xfc, 0x45, 0x0, 0x0, 0x0, 0x4,
    0x96, 0xff, 0xff, 0x7c, 0xa4, 0xff, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xff,
    0x7c, 0x8d, 0xff, 0xff, 0xee, 0x17, 0x0, 0x0,
    0x0, 0x0, 0x16, 0x90, 0x90, 0x46, 0x33, 0xfe,
    0xff, 0xff, 0xe6, 0x6c, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6a, 0xfd, 0xff, 0xff,
    0xff, 0xf9, 0xb4, 0x63, 0xd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xb9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x78, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x25, 0x7d, 0xce, 0xff, 0xff, 0xff, 0xff,
    0xbb, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xae, 0xff, 0xff, 0xff, 0x79, 0xbd,
    0xdc, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbe, 0xff, 0xff, 0xd1, 0xdc, 0xff, 0xfb, 0x6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0xff, 0xff,
    0xe8, 0xdc, 0xff, 0xff, 0x63, 0x2, 0x0, 0x0,
    0x0, 0x16, 0xdd, 0xff, 0xff, 0xc8, 0xdc, 0xff,
    0xff, 0xff, 0xe6, 0xb1, 0x9e, 0xb0, 0xf2, 0xff,
    0xff, 0xff, 0x5c, 0x53, 0xe1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x81, 0x0,
    0x0, 0x6, 0x58, 0xa7, 0xd8, 0xf4, 0xfe, 0xf5,
    0xd5, 0x96, 0x30, 0x0, 0x0,

    /* U+0054 "T" */
    0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x78, 0xff, 0xff, 0xc2, 0xa8, 0xa8, 0xdc, 0xff,
    0xff, 0xf1, 0xa8, 0xa8, 0xaa, 0xff, 0xff, 0xc0,
    0x78, 0xff, 0xff, 0x3c, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0xf4, 0xff, 0xc0,
    0x51, 0xac, 0xac, 0x1b, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x96, 0xac, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0x39, 0xbd, 0xff,
    0xff, 0xe4, 0x42, 0x14, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x8c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x88,
    0x0, 0x0, 0x48, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc8, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x88, 0x0, 0x0, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc8, 0x9, 0x36, 0xd0, 0xff,
    0xff, 0xcd, 0x36, 0x9, 0x0, 0x0, 0x3, 0x2a,
    0xa2, 0xff, 0xff, 0xf8, 0x42, 0x12, 0x0, 0x0,
    0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x78, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x78, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xb8, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x78, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x78, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x78, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xb8, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x78, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb2, 0xff, 0xff, 0xbb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xee, 0x0, 0x0,
    0x0, 0x0, 0x91, 0xff, 0xff, 0xeb, 0x7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xcd,
    0x0, 0x0, 0x0, 0x0, 0x43, 0xff, 0xff, 0xff,
    0x9c, 0x6, 0x0, 0x0, 0x0, 0x67, 0xff, 0xff,
    0xff, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb4,
    0xff, 0xff, 0xff, 0xe9, 0xaf, 0xab, 0xdb, 0xff,
    0xff, 0xff, 0xdf, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xb2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd6, 0x24, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0xa5, 0xdf,
    0xf9, 0xfc, 0xe7, 0xb4, 0x61, 0x5, 0x0, 0x0,
    0x0, 0x0,

    /* U+0056 "V" */
    0x98, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xdc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x9c, 0x98, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0xdc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x9c, 0x10, 0x36, 0xf2, 0xff,
    0xff, 0xd5, 0x29, 0x4, 0x0, 0x0, 0x0, 0x1a,
    0x60, 0xff, 0xff, 0xf2, 0x34, 0x10, 0x0, 0x0,
    0x9c, 0xff, 0xff, 0xfc, 0x14, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7c, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0x65, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd1, 0xff, 0xff, 0x46,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xbb, 0x0, 0x0, 0x0, 0x0, 0x27, 0xff, 0xff,
    0xe9, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x80,
    0xff, 0xff, 0xfc, 0x14, 0x0, 0x0, 0x0, 0x7c,
    0xff, 0xff, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x21, 0xff, 0xff, 0xff, 0x65, 0x0, 0x0,
    0x0, 0xd0, 0xff, 0xff, 0x3b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc0, 0xff, 0xff, 0xbb,
    0x0, 0x0, 0x25, 0xff, 0xff, 0xe0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x63, 0xff,
    0xff, 0xfc, 0x14, 0x0, 0x7b, 0xff, 0xff, 0x87,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf6, 0xff, 0xff, 0x65, 0x0, 0xcf, 0xff,
    0xff, 0x2e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa4, 0xff, 0xff, 0xbb, 0x24,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xff,
    0xfc, 0x87, 0xff, 0xff, 0x7b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xe2, 0xff, 0xff, 0xf8, 0xff, 0xff, 0x22, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0xff, 0xff, 0xff, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xff, 0xff, 0xff,
    0xff, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc8,
    0xff, 0xff, 0xfd, 0x18, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0057 "W" */
    0x98, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x44,
    0x0, 0x0, 0x0, 0x7e, 0xff, 0xff, 0x59, 0x0,
    0x0, 0x0, 0x58, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x64, 0x98, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x44, 0x0, 0x0, 0x0, 0xce, 0xff, 0xff,
    0xac, 0x0, 0x0, 0x0, 0x58, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x64, 0xf, 0x2e, 0xd2, 0xff,
    0xff, 0xa4, 0x2d, 0x5, 0x0, 0x0, 0x20, 0xff,
    0xff, 0xff, 0xf5, 0x9, 0x0, 0x0, 0x8, 0x2d,
    0xa0, 0xff, 0xff, 0xbe, 0x2b, 0x9, 0x0, 0x0,
    0x86, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x72, 0xff, 0xff, 0xff, 0xff, 0x52, 0x0, 0x0,
    0x0, 0x0, 0xac, 0xff, 0xff, 0x6d, 0x0, 0x0,
    0x0, 0x0, 0x41, 0xff, 0xff, 0xf1, 0x3, 0x0,
    0x0, 0x0, 0xc2, 0xff, 0xff, 0xff, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x1, 0xeb, 0xff, 0xff, 0x28,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xf6, 0xff, 0xff,
    0x32, 0x0, 0x0, 0x18, 0xfe, 0xff, 0xf8, 0xfa,
    0xff, 0xf0, 0x6, 0x0, 0x0, 0x2a, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb7,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x66, 0xff, 0xff,
    0xb2, 0xbb, 0xff, 0xff, 0x49, 0x0, 0x0, 0x67,
    0xff, 0xff, 0x9d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x72, 0xff, 0xff, 0xb0, 0x0, 0x0, 0xb8,
    0xff, 0xff, 0x5e, 0x6c, 0xff, 0xff, 0x9c, 0x0,
    0x0, 0xa5, 0xff, 0xff, 0x57, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xeb, 0x1,
    0x10, 0xfa, 0xff, 0xf9, 0x10, 0x1a, 0xfe, 0xff,
    0xeb, 0x3, 0x0, 0xe4, 0xff, 0xfe, 0x14, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xe8, 0xff,
    0xff, 0x2c, 0x5a, 0xff, 0xff, 0xb5, 0x0, 0x0,
    0xc5, 0xff, 0xff, 0x42, 0x22, 0xff, 0xff, 0xcd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa4, 0xff, 0xff, 0x6b, 0xac, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x72, 0xff, 0xff, 0x93, 0x60, 0xff,
    0xff, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xad, 0xf1, 0xff,
    0xfa, 0x11, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xe3,
    0xa3, 0xff, 0xff, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xb6, 0x0, 0x0, 0x0, 0x0, 0xcb,
    0xff, 0xff, 0xf9, 0xff, 0xf6, 0x7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd5,
    0xff, 0xff, 0xff, 0xff, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x78, 0xff, 0xff, 0xff, 0xff, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x91, 0xff, 0xff, 0xff, 0xfb, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xff, 0xff, 0xff, 0xff,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd1, 0xff,
    0xff, 0xff, 0x2d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfa, 0xff,
    0xff, 0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7e, 0xff, 0xff, 0xe6, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0058 "X" */
    0x30, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7c,
    0x0, 0x0, 0xe4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x30, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x7c, 0x0, 0x0, 0xe4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x4, 0x36, 0xbb, 0xff,
    0xff, 0xfb, 0x45, 0xa, 0x0, 0x0, 0x17, 0xa4,
    0xff, 0xff, 0xf1, 0x50, 0x17, 0x0, 0x0, 0x0,
    0x16, 0xe6, 0xff, 0xff, 0xc4, 0x3, 0x0, 0x0,
    0x3c, 0xfd, 0xff, 0xff, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x42, 0xfe, 0xff, 0xff, 0x7f,
    0x0, 0xf, 0xe0, 0xff, 0xff, 0x9a, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x84, 0xff,
    0xff, 0xfc, 0x3a, 0xa7, 0xff, 0xff, 0xd3, 0x9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xc5, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xf5,
    0x2b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xef, 0xff, 0xff, 0xff,
    0xff, 0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x76, 0xff,
    0xff, 0xff, 0xf4, 0x16, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18,
    0xe5, 0xff, 0xff, 0xff, 0xff, 0xbd, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xc4, 0xff, 0xff, 0xd6, 0xfb, 0xff, 0xff,
    0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x94, 0xff, 0xff, 0xee, 0x1e, 0x77,
    0xff, 0xff, 0xfe, 0x4a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x4d,
    0x0, 0x2, 0xba, 0xff, 0xff, 0xee, 0x1f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x33, 0xf8, 0xff, 0xff,
    0x8c, 0x0, 0x0, 0x0, 0x17, 0xe8, 0xff, 0xff,
    0xca, 0x7, 0x0, 0x0, 0xe, 0x43, 0xe1, 0xff,
    0xff, 0xdd, 0x1d, 0x0, 0x0, 0x0, 0x9, 0x69,
    0xff, 0xff, 0xff, 0xa2, 0x32, 0x6, 0x94, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x54,
    0x94, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x54,

    /* U+0059 "Y" */
    0x94, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3c,
    0x0, 0x0, 0x78, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x38, 0x94, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x3c, 0x0, 0x0, 0x78, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x38, 0xf, 0x37, 0xd1, 0xff,
    0xff, 0xfa, 0x37, 0x5, 0x0, 0x0, 0xa, 0x29,
    0xda, 0xff, 0xff, 0x82, 0x2a, 0x4, 0x0, 0x0,
    0x41, 0xff, 0xff, 0xff, 0x7e, 0x0, 0x0, 0x0,
    0x0, 0x51, 0xff, 0xff, 0xd1, 0x3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xae, 0xff, 0xff, 0xf1, 0x12,
    0x0, 0x0, 0x7, 0xdc, 0xff, 0xff, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0xf9, 0xff,
    0xff, 0x88, 0x0, 0x0, 0x78, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8a, 0xff, 0xff, 0xf6, 0x19, 0x17, 0xf3, 0xff,
    0xf7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xea, 0xff, 0xff, 0x91, 0x9e,
    0xff, 0xff, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x66, 0xff, 0xff,
    0xf9, 0xfd, 0xff, 0xe3, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xd1, 0xff, 0xff, 0xff, 0xff, 0x59, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x42, 0xff, 0xff, 0xff, 0xc4, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe2, 0xff, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe0,
    0xff, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe0, 0xff, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x46, 0xec, 0xff, 0xff, 0xb2, 0x36,
    0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x5c, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+005A "Z" */
    0x0, 0xc4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x64, 0x0, 0xcc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x64, 0x0, 0xd2, 0xff, 0xfe,
    0xa8, 0xa8, 0xa8, 0xa8, 0xa8, 0xf9, 0xff, 0xff,
    0xfb, 0x30, 0x0, 0xda, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x66, 0xff, 0xff, 0xff, 0x81, 0x0,
    0x0, 0xbb, 0xd4, 0xa7, 0x0, 0x0, 0x0, 0x22,
    0xf3, 0xff, 0xff, 0xcd, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xc3, 0xff, 0xff,
    0xf8, 0x29, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x76, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xf9, 0xff, 0xff, 0xc0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xd0, 0xff, 0xff,
    0xf2, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0xff, 0xff, 0xff, 0x62, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39,
    0xfd, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xdb, 0xff, 0xff,
    0xeb, 0x17, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x96, 0xff, 0xff, 0xff, 0x52, 0x0,
    0x0, 0x0, 0x0, 0xa2, 0xcc, 0xac, 0x0, 0x47,
    0xff, 0xff, 0xff, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe8, 0xff, 0xcd, 0xb, 0xe5, 0xff, 0xff,
    0xff, 0xb3, 0xa4, 0xa4, 0xa4, 0xa4, 0xa4, 0xff,
    0xff, 0xc3, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xad,

    /* U+005B "[" */
    0x60, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x60, 0xff,
    0xff, 0xff, 0xff, 0xb4, 0x60, 0xff, 0xff, 0xff,
    0x91, 0x62, 0x60, 0xff, 0xff, 0xff, 0xc, 0x0,
    0x60, 0xff, 0xff, 0xff, 0xc, 0x0, 0x60, 0xff,
    0xff, 0xff, 0xc, 0x0, 0x60, 0xff, 0xff, 0xff,
    0xc, 0x0, 0x60, 0xff, 0xff, 0xff, 0xc, 0x0,
    0x60, 0xff, 0xff, 0xff, 0xc, 0x0, 0x60, 0xff,
    0xff, 0xff, 0xc, 0x0, 0x60, 0xff, 0xff, 0xff,
    0xc, 0x0, 0x60, 0xff, 0xff, 0xff, 0xc, 0x0,
    0x60, 0xff, 0xff, 0xff, 0xc, 0x0, 0x60, 0xff,
    0xff, 0xff, 0xc, 0x0, 0x60, 0xff, 0xff, 0xff,
    0xc, 0x0, 0x60, 0xff, 0xff, 0xff, 0xc, 0x0,
    0x60, 0xff, 0xff, 0xff, 0xc, 0x0, 0x60, 0xff,
    0xff, 0xff, 0xc, 0x0, 0x60, 0xff, 0xff, 0xff,
    0xc, 0x0, 0x60, 0xff, 0xff, 0xff, 0xc, 0x0,
    0x60, 0xff, 0xff, 0xff, 0xc, 0x0, 0x60, 0xff,
    0xff, 0xff, 0x91, 0x62, 0x60, 0xff, 0xff, 0xff,
    0xff, 0xb4, 0x60, 0xff, 0xff, 0xff, 0xff, 0xb4,

    /* U+005C "\\" */
    0xac, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x48, 0xff, 0xff, 0xfd, 0x1b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xe3,
    0xff, 0xff, 0x7b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x84, 0xff, 0xff, 0xdb, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbe, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xf6,
    0xe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf0, 0xff, 0xff, 0x67, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x95, 0xff, 0xff, 0xcb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff,
    0xeb, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x13, 0xf9, 0xff, 0xff, 0x54, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa9, 0xff, 0xff, 0xb7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x46,
    0xff, 0xff, 0xfd, 0x1c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xe2, 0xff, 0xff, 0x7c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x81, 0xff,
    0xff, 0xde, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x21, 0xfe, 0xff, 0xff, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6a, 0x80, 0x80,
    0x46,

    /* U+005D "]" */
    0xc0, 0xff, 0xff, 0xff, 0xff, 0x54, 0xc0, 0xff,
    0xff, 0xff, 0xff, 0x54, 0x69, 0x95, 0xff, 0xff,
    0xff, 0x54, 0x0, 0x14, 0xff, 0xff, 0xff, 0x54,
    0x0, 0x14, 0xff, 0xff, 0xff, 0x54, 0x0, 0x14,
    0xff, 0xff, 0xff, 0x54, 0x0, 0x14, 0xff, 0xff,
    0xff, 0x54, 0x0, 0x14, 0xff, 0xff, 0xff, 0x54,
    0x0, 0x14, 0xff, 0xff, 0xff, 0x54, 0x0, 0x14,
    0xff, 0xff, 0xff, 0x54, 0x0, 0x14, 0xff, 0xff,
    0xff, 0x54, 0x0, 0x14, 0xff, 0xff, 0xff, 0x54,
    0x0, 0x14, 0xff, 0xff, 0xff, 0x54, 0x0, 0x14,
    0xff, 0xff, 0xff, 0x54, 0x0, 0x14, 0xff, 0xff,
    0xff, 0x54, 0x0, 0x14, 0xff, 0xff, 0xff, 0x54,
    0x0, 0x14, 0xff, 0xff, 0xff, 0x54, 0x0, 0x14,
    0xff, 0xff, 0xff, 0x54, 0x0, 0x14, 0xff, 0xff,
    0xff, 0x54, 0x0, 0x14, 0xff, 0xff, 0xff, 0x54,
    0x0, 0x14, 0xff, 0xff, 0xff, 0x54, 0x69, 0x95,
    0xff, 0xff, 0xff, 0x54, 0xc0, 0xff, 0xff, 0xff,
    0xff, 0x54, 0xc0, 0xff, 0xff, 0xff, 0xff, 0x54,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0xa, 0x80, 0x80, 0x4f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x61, 0xff,
    0xff, 0xe8, 0x5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcc, 0xff, 0xff, 0xff, 0x57, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xfa, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xff,
    0xf5, 0x8b, 0xff, 0xff, 0x2a, 0x0, 0x0, 0x0,
    0x11, 0xf6, 0xff, 0x9e, 0x1c, 0xfc, 0xff, 0x93,
    0x0, 0x0, 0x0, 0x71, 0xff, 0xff, 0x3a, 0x0,
    0xb2, 0xff, 0xf1, 0xb, 0x0, 0x1, 0xd9, 0xff,
    0xd3, 0x0, 0x0, 0x4b, 0xff, 0xff, 0x66, 0x0,
    0x43, 0xff, 0xff, 0x6d, 0x0, 0x0, 0x3, 0xe1,
    0xff, 0xcf, 0x0,

    /* U+005F "_" */
    0x2c, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x24, 0xec, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xec, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0,

    /* U+0060 "`" */
    0x15, 0x30, 0x30, 0x30, 0x9, 0x0, 0x0, 0xf,
    0xc7, 0xff, 0xff, 0x9d, 0x0, 0x0, 0x0, 0xe,
    0xc6, 0xff, 0xff, 0x5a, 0x0, 0x0, 0x0, 0xb,
    0xc1, 0xff, 0xf2, 0x23,

    /* U+0061 "a" */
    0x0, 0x0, 0x2f, 0x91, 0xd6, 0xf5, 0xfe, 0xeb,
    0xb8, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x9a, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xff, 0xff,
    0xc8, 0x6f, 0x69, 0xbe, 0xff, 0xff, 0xff, 0x4b,
    0x0, 0x0, 0x0, 0x67, 0xff, 0xff, 0x36, 0x0,
    0x0, 0x5, 0xe2, 0xff, 0xff, 0x96, 0x0, 0x0,
    0x0, 0x1e, 0x4c, 0x4c, 0x7, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0xff, 0xab, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x7f, 0xcc, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xac, 0x0, 0x0, 0x0, 0x25, 0xe3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0xc1, 0xff, 0xff, 0xe5, 0x4c,
    0x1f, 0x1c, 0xc7, 0xff, 0xff, 0xac, 0x0, 0x0,
    0x10, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0xff, 0xac, 0x0, 0x0, 0x19, 0xff,
    0xff, 0xff, 0x66, 0x0, 0x0, 0x1e, 0xe7, 0xff,
    0xff, 0xac, 0x0, 0x0, 0x2, 0xe9, 0xff, 0xff,
    0xf3, 0x9a, 0xa1, 0xf2, 0xff, 0xff, 0xff, 0xd6,
    0x5c, 0x3, 0x0, 0x66, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xce, 0xff, 0xff, 0xff, 0xff, 0xc,
    0x0, 0x0, 0x56, 0xcb, 0xf9, 0xf7, 0xba, 0x33,
    0x5c, 0xff, 0xff, 0xff, 0xff, 0xc,

    /* U+0062 "b" */
    0x44, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x8d, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x70, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x70, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x70, 0xff, 0xff, 0xf8, 0x1b, 0xa7, 0xf0, 0xfa,
    0xce, 0x5e, 0x0, 0x0, 0x0, 0x0, 0x70, 0xff,
    0xff, 0xfb, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7e, 0x0, 0x0, 0x0, 0x70, 0xff, 0xff, 0xff,
    0xff, 0xc4, 0xb7, 0xf7, 0xff, 0xff, 0xfd, 0x29,
    0x0, 0x0, 0x70, 0xff, 0xff, 0xff, 0x55, 0x0,
    0x0, 0x33, 0xfb, 0xff, 0xff, 0x8f, 0x0, 0x0,
    0x70, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xae, 0xff, 0xff, 0xcf, 0x0, 0x0, 0x70, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x70, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x69, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x70, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x73, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x70, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0xff, 0xdd, 0x0, 0x0, 0x70, 0xff,
    0xff, 0xff, 0x48, 0x0, 0x0, 0x25, 0xf2, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0x70, 0xff, 0xff, 0xff,
    0xfe, 0xc3, 0xb7, 0xf5, 0xff, 0xff, 0xff, 0x3c,
    0x0, 0x0, 0x70, 0xff, 0xff, 0xd5, 0xed, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x70, 0xff, 0xff, 0x96, 0x26, 0xb2, 0xf2, 0xfa,
    0xd1, 0x67, 0x1, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x0, 0x28, 0x9e, 0xe2, 0xfc, 0xf5,
    0xcf, 0x7f, 0x14, 0x0, 0x0, 0x0, 0x55, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x4e,
    0x0, 0x2d, 0xf9, 0xff, 0xff, 0xe3, 0x91, 0x9a,
    0xf2, 0xff, 0xff, 0xa3, 0x0, 0xae, 0xff, 0xff,
    0xe0, 0x12, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xa0,
    0x8, 0xf8, 0xff, 0xff, 0x73, 0x0, 0x0, 0x0,
    0x24, 0xff, 0xff, 0x9e, 0x2b, 0xff, 0xff, 0xff,
    0x41, 0x0, 0x0, 0x0, 0x1, 0x54, 0x54, 0x33,
    0x36, 0xff, 0xff, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf9, 0xff, 0xff, 0x77, 0x0, 0x0, 0x0,
    0xe, 0x6c, 0x6c, 0x5d, 0x0, 0xb1, 0xff, 0xff,
    0xe8, 0x1c, 0x0, 0x0, 0x82, 0xff, 0xff, 0xb3,
    0x0, 0x2f, 0xfa, 0xff, 0xff, 0xf0, 0xad, 0xc6,
    0xff, 0xff, 0xff, 0x54, 0x0, 0x0, 0x58, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xad, 0x1,
    0x0, 0x0, 0x0, 0x29, 0xa0, 0xe4, 0xfd, 0xf5,
    0xcb, 0x6b, 0x3, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x58, 0xff, 0xff, 0xff,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xff, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x34, 0xff, 0xff, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x3, 0x75, 0xda, 0xfc, 0xe6, 0x87, 0x3b, 0xff,
    0xff, 0xff, 0x38, 0x0, 0x0, 0x0, 0xa6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xff,
    0x38, 0x0, 0x0, 0x53, 0xff, 0xff, 0xff, 0xed,
    0xb2, 0xd6, 0xff, 0xff, 0xff, 0xff, 0x38, 0x0,
    0x0, 0xc1, 0xff, 0xff, 0xe6, 0x18, 0x0, 0x1,
    0x8d, 0xff, 0xff, 0xff, 0x38, 0x0, 0x9, 0xfb,
    0xff, 0xff, 0x7d, 0x0, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xff, 0x38, 0x0, 0x29, 0xff, 0xff, 0xff,
    0x46, 0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xff,
    0x38, 0x0, 0x37, 0xff, 0xff, 0xff, 0x35, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xff, 0x38, 0x0,
    0x31, 0xff, 0xff, 0xff, 0x3f, 0x0, 0x0, 0x0,
    0x34, 0xff, 0xff, 0xff, 0x38, 0x0, 0x13, 0xff,
    0xff, 0xff, 0x69, 0x0, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xff, 0x38, 0x0, 0x0, 0xd5, 0xff, 0xff,
    0xd6, 0xc, 0x0, 0x0, 0x82, 0xff, 0xff, 0xff,
    0x38, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xe8,
    0xb2, 0xd4, 0xff, 0xff, 0xff, 0xff, 0xa9, 0x51,
    0x0, 0x4, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc6, 0xf9, 0xff, 0xff, 0xff, 0xdc, 0x0, 0x0,
    0x7, 0x7f, 0xde, 0xfc, 0xe8, 0x92, 0xd, 0xd4,
    0xff, 0xff, 0xff, 0xdc,

    /* U+0065 "e" */
    0x0, 0x0, 0x0, 0x30, 0xaa, 0xe9, 0xfd, 0xed,
    0xb8, 0x45, 0x0, 0x0, 0x0, 0x0, 0x60, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x74, 0x0,
    0x0, 0x30, 0xfb, 0xff, 0xff, 0xd3, 0x8d, 0xba,
    0xff, 0xff, 0xfd, 0x29, 0x0, 0xaf, 0xff, 0xff,
    0xd5, 0x8, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x8b,
    0x7, 0xf8, 0xff, 0xff, 0x71, 0x0, 0x0, 0x0,
    0x6e, 0xff, 0xff, 0xba, 0x2a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcc, 0x2d, 0xff, 0xff, 0xff,
    0x8c, 0x4c, 0x4c, 0x4c, 0x4c, 0x4c, 0x4c, 0x3d,
    0xb, 0xfa, 0xff, 0xff, 0x92, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb1, 0xff, 0xff,
    0xf6, 0x3a, 0x0, 0x0, 0x0, 0x1a, 0x5b, 0x0,
    0x0, 0x2f, 0xf9, 0xff, 0xff, 0xfd, 0xbc, 0xaa,
    0xc9, 0xfc, 0xf9, 0x17, 0x0, 0x0, 0x51, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x4f,
    0x0, 0x0, 0x0, 0x22, 0x97, 0xde, 0xfb, 0xf8,
    0xd8, 0x94, 0x2a, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x4, 0x6d, 0xcc, 0xf5, 0xfd,
    0xe4, 0x4e, 0x0, 0x0, 0x0, 0xad, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x38, 0xff,
    0xff, 0xff, 0xef, 0xb6, 0xbb, 0x26, 0x0, 0x0,
    0x68, 0xff, 0xff, 0xff, 0x23, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x50, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x50, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x21, 0x68,
    0xa8, 0xff, 0xff, 0xff, 0x68, 0x68, 0x5b, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6c, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x2, 0x3c,
    0xa8, 0xff, 0xff, 0xff, 0x64, 0x3a, 0x8, 0x0,
    0x14, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x3c, 0x0, 0x14, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3c, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x3, 0x73, 0xd9, 0xfc, 0xe8, 0x8f,
    0xa, 0xcd, 0xff, 0xff, 0xff, 0xb8, 0x0, 0x0,
    0xa5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbb, 0xf1,
    0xff, 0xff, 0xff, 0xb8, 0x0, 0x53, 0xff, 0xff,
    0xff, 0xef, 0xb3, 0xd0, 0xff, 0xff, 0xff, 0xff,
    0xa8, 0x48, 0x0, 0xc1, 0xff, 0xff, 0xe7, 0x1b,
    0x0, 0x0, 0x80, 0xff, 0xff, 0xff, 0x3c, 0x0,
    0x9, 0xfb, 0xff, 0xff, 0x7c, 0x0, 0x0, 0x0,
    0x34, 0xff, 0xff, 0xff, 0x3c, 0x0, 0x29, 0xff,
    0xff, 0xff, 0x46, 0x0, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xff, 0x3c, 0x0, 0x37, 0xff, 0xff, 0xff,
    0x35, 0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xff,
    0x3c, 0x0, 0x30, 0xff, 0xff, 0xff, 0x3f, 0x0,
    0x0, 0x0, 0x34, 0xff, 0xff, 0xff, 0x3c, 0x0,
    0x12, 0xff, 0xff, 0xff, 0x6c, 0x0, 0x0, 0x0,
    0x34, 0xff, 0xff, 0xff, 0x3c, 0x0, 0x0, 0xd3,
    0xff, 0xff, 0xd8, 0xf, 0x0, 0x0, 0x72, 0xff,
    0xff, 0xff, 0x3c, 0x0, 0x0, 0x68, 0xff, 0xff,
    0xff, 0xea, 0xb2, 0xcb, 0xff, 0xff, 0xff, 0xff,
    0x3c, 0x0, 0x0, 0x3, 0xb4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe8, 0xff, 0xff, 0xff, 0x3c, 0x0,
    0x0, 0x0, 0x5, 0x77, 0xd8, 0xfd, 0xec, 0x99,
    0x45, 0xff, 0xff, 0xff, 0x39, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x59, 0xff,
    0xff, 0xff, 0x25, 0x0, 0x0, 0x0, 0x23, 0x24,
    0x0, 0x0, 0x0, 0xb, 0xca, 0xff, 0xff, 0xe6,
    0x2, 0x0, 0x0, 0x0, 0x8b, 0xff, 0xdc, 0xb0,
    0xab, 0xe7, 0xff, 0xff, 0xff, 0x6d, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x9a, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0x94, 0xcd, 0xf1, 0xfc, 0xea, 0xb1, 0x49,
    0x0, 0x0, 0x0, 0x0,

    /* U+0068 "h" */
    0xa0, 0xff, 0xff, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0x9c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20,
    0xd8, 0xff, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc,
    0xff, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff,
    0xff, 0x9c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff,
    0x9c, 0x27, 0xb0, 0xf2, 0xf9, 0xc8, 0x48, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff, 0xc4,
    0xee, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x4c, 0x0,
    0x0, 0x0, 0x0, 0xcc, 0xff, 0xff, 0xff, 0xf1,
    0xb5, 0xca, 0xff, 0xff, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xd7, 0x17, 0x0,
    0x0, 0x91, 0xff, 0xff, 0xff, 0x19, 0x0, 0x0,
    0x0, 0xcc, 0xff, 0xff, 0x9c, 0x0, 0x0, 0x0,
    0x3a, 0xff, 0xff, 0xff, 0x38, 0x0, 0x0, 0x0,
    0xcc, 0xff, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x2c,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0xcc,
    0xff, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0xcc, 0xff,
    0xff, 0x9c, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff,
    0x9c, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff, 0x9c,
    0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0x40,
    0x0, 0xd, 0x44, 0xe1, 0xff, 0xff, 0xc2, 0x34,
    0x2, 0x17, 0x70, 0xff, 0xff, 0xff, 0x81, 0x25,
    0x74, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18,
    0xa4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x74,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18, 0xa4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+0069 "i" */
    0x0, 0x0, 0xd4, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xd4, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xc7, 0xf0, 0xf0, 0xa9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x16, 0x51, 0xd8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0xd, 0x4c, 0xd8, 0xff, 0xff, 0xd6, 0x4c, 0xd,
    0x5c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x58,
    0x5c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x58,

    /* U+006A "j" */
    0x0, 0x0, 0x0, 0xe0, 0xff, 0xff, 0xa8, 0x0,
    0x0, 0x0, 0xe0, 0xff, 0xff, 0xa8, 0x0, 0x0,
    0x0, 0xd2, 0xf0, 0xf0, 0x9e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xff, 0xff,
    0xff, 0xb4, 0x0, 0x8c, 0xff, 0xff, 0xff, 0xff,
    0xb4, 0x0, 0x14, 0x4a, 0xd5, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0xb8,
    0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0xb8, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4,
    0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0xbc, 0xff, 0xff, 0xb1, 0x0, 0x0, 0xb,
    0xeb, 0xff, 0xff, 0x99, 0x3f, 0xaf, 0xd7, 0xff,
    0xff, 0xff, 0x51, 0x5e, 0xff, 0xff, 0xff, 0xff,
    0xb9, 0x2, 0x5b, 0xf4, 0xfd, 0xdc, 0x82, 0x8,
    0x0,

    /* U+006B "k" */
    0xa0, 0xff, 0xff, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x90,
    0xfe, 0xff, 0xff, 0xff, 0x9c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xd3, 0xff, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc,
    0xff, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff,
    0xff, 0x9c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff,
    0x9c, 0x0, 0x28, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0xcc, 0xff, 0xff, 0x9c,
    0x0, 0x28, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0xcc, 0xff, 0xff, 0x9c, 0x0,
    0x4, 0x5c, 0xfd, 0xff, 0xff, 0xc7, 0x5b, 0x12,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0x9c, 0x0, 0x37,
    0xee, 0xff, 0xff, 0xb3, 0x9, 0x0, 0x0, 0x0,
    0x0, 0xcc, 0xff, 0xff, 0x9c, 0x42, 0xf3, 0xff,
    0xff, 0x9e, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcc, 0xff, 0xff, 0xd7, 0xf8, 0xff, 0xff, 0xd6,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x65,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff,
    0xff, 0xff, 0xfb, 0xd7, 0xff, 0xff, 0xf7, 0x2d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff,
    0xf6, 0x49, 0x1c, 0xed, 0xff, 0xff, 0xd7, 0xb,
    0x0, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x53, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x13, 0x50, 0xe3, 0xff, 0xff, 0xc6, 0x40,
    0x3, 0x4, 0xb6, 0xff, 0xff, 0xff, 0x88, 0x36,
    0x74, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18,
    0x60, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x74,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18, 0x60,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+006C "l" */
    0xa0, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0xa0, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0xe, 0x3e, 0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x7, 0x37, 0xde, 0xff, 0xff, 0xbf, 0x2e, 0x3,
    0x74, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x48,
    0x74, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x48,

    /* U+006D "m" */
    0x90, 0xff, 0xff, 0xff, 0xff, 0x7b, 0x32, 0xb4,
    0xf1, 0xfc, 0xd4, 0x61, 0x0, 0x1d, 0xa3, 0xec,
    0xfc, 0xd9, 0x69, 0x0, 0x0, 0x0, 0x0, 0x90,
    0xff, 0xff, 0xff, 0xff, 0xcb, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x7c, 0xe8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x77, 0x0, 0x0, 0x0, 0x12, 0x47,
    0xd7, 0xff, 0xff, 0xff, 0xca, 0x78, 0x96, 0xfd,
    0xff, 0xff, 0xff, 0xf8, 0x91, 0x7c, 0xe2, 0xff,
    0xff, 0xf3, 0xb, 0x0, 0x0, 0x0, 0x0, 0xbc,
    0xff, 0xff, 0xcc, 0x4, 0x0, 0x0, 0x9c, 0xff,
    0xff, 0xff, 0x59, 0x0, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0x42, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xff,
    0xff, 0xac, 0x0, 0x0, 0x0, 0x67, 0xff, 0xff,
    0xff, 0x9, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x61, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff,
    0xac, 0x0, 0x0, 0x0, 0x60, 0xff, 0xff, 0xff,
    0x8, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x68,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x60, 0xff, 0xff, 0xff, 0x8,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x68, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0xff, 0xff, 0xac, 0x0,
    0x0, 0x0, 0x60, 0xff, 0xff, 0xff, 0x8, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x68, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0xac, 0x0, 0x0,
    0x0, 0x60, 0xff, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x68, 0x0, 0x0, 0x0,
    0x0, 0xbc, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0,
    0x60, 0xff, 0xff, 0xff, 0x8, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x68, 0x0, 0x0, 0xf, 0x4e,
    0xda, 0xff, 0xff, 0xd0, 0x44, 0x5, 0x28, 0x9e,
    0xff, 0xff, 0xff, 0x5d, 0x12, 0x16, 0x5c, 0xff,
    0xff, 0xff, 0xa5, 0x3c, 0x2, 0x64, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x28, 0xc4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x6c, 0x80, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x64, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x28, 0xc4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x6c, 0x80, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10,

    /* U+006E "n" */
    0x90, 0xff, 0xff, 0xff, 0xff, 0x7b, 0x25, 0xa9,
    0xef, 0xfc, 0xd5, 0x65, 0x0, 0x0, 0x0, 0x90,
    0xff, 0xff, 0xff, 0xff, 0xbc, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x6f, 0x0, 0x0, 0x12, 0x47,
    0xda, 0xff, 0xff, 0xff, 0xd0, 0x7a, 0x88, 0xf0,
    0xff, 0xff, 0xec, 0x4, 0x0, 0x0, 0x0, 0xc0,
    0xff, 0xff, 0xd0, 0x7, 0x0, 0x0, 0x5d, 0xff,
    0xff, 0xff, 0x2e, 0x0, 0x0, 0x0, 0xc0, 0xff,
    0xff, 0xac, 0x0, 0x0, 0x0, 0x26, 0xff, 0xff,
    0xff, 0x48, 0x0, 0x0, 0x0, 0xc0, 0xff, 0xff,
    0xac, 0x0, 0x0, 0x0, 0x20, 0xff, 0xff, 0xff,
    0x4c, 0x0, 0x0, 0x0, 0xc0, 0xff, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x20, 0xff, 0xff, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0xc0, 0xff, 0xff, 0xac, 0x0,
    0x0, 0x0, 0x20, 0xff, 0xff, 0xff, 0x4c, 0x0,
    0x0, 0x0, 0xc0, 0xff, 0xff, 0xac, 0x0, 0x0,
    0x0, 0x20, 0xff, 0xff, 0xff, 0x4c, 0x0, 0x0,
    0x0, 0xc0, 0xff, 0xff, 0xac, 0x0, 0x0, 0x0,
    0x20, 0xff, 0xff, 0xff, 0x4c, 0x0, 0xf, 0x4e,
    0xdc, 0xff, 0xff, 0xd0, 0x41, 0x2, 0x1b, 0x71,
    0xff, 0xff, 0xff, 0x92, 0x32, 0x64, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x98, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x64, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x98, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0,

    /* U+006F "o" */
    0x0, 0x0, 0x0, 0x28, 0x9c, 0xe2, 0xfc, 0xf4,
    0xc9, 0x6e, 0x6, 0x0, 0x0, 0x0, 0x0, 0x56,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xca,
    0x11, 0x0, 0x0, 0x2f, 0xf9, 0xff, 0xff, 0xee,
    0xad, 0xc4, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0,
    0xae, 0xff, 0xff, 0xe4, 0x18, 0x0, 0x0, 0x72,
    0xff, 0xff, 0xff, 0x38, 0x7, 0xf8, 0xff, 0xff,
    0x78, 0x0, 0x0, 0x0, 0x3, 0xe9, 0xff, 0xff,
    0x89, 0x2a, 0xff, 0xff, 0xff, 0x42, 0x0, 0x0,
    0x0, 0x0, 0xb7, 0xff, 0xff, 0xb2, 0x36, 0xff,
    0xff, 0xff, 0x35, 0x0, 0x0, 0x0, 0x0, 0xaa,
    0xff, 0xff, 0xbe, 0x2a, 0xff, 0xff, 0xff, 0x42,
    0x0, 0x0, 0x0, 0x0, 0xb7, 0xff, 0xff, 0xb2,
    0x7, 0xf8, 0xff, 0xff, 0x74, 0x0, 0x0, 0x0,
    0x3, 0xe9, 0xff, 0xff, 0x88, 0x0, 0xae, 0xff,
    0xff, 0xe1, 0x16, 0x0, 0x0, 0x70, 0xff, 0xff,
    0xff, 0x38, 0x0, 0x2f, 0xf9, 0xff, 0xff, 0xed,
    0xac, 0xc2, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x56, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xca, 0x11, 0x0, 0x0, 0x0, 0x0, 0x28,
    0x9c, 0xe2, 0xfc, 0xf5, 0xca, 0x6e, 0x6, 0x0,
    0x0,

    /* U+0070 "p" */
    0x84, 0xff, 0xff, 0xff, 0xff, 0x81, 0x54, 0xcd,
    0xf9, 0xf2, 0xb4, 0x2f, 0x0, 0x0, 0x84, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x3a, 0x0, 0x11, 0x45, 0xd0, 0xff,
    0xff, 0xff, 0xcf, 0x79, 0x91, 0xf8, 0xff, 0xff,
    0xda, 0x3, 0x0, 0x0, 0xb0, 0xff, 0xff, 0xd8,
    0x7, 0x0, 0x0, 0x59, 0xff, 0xff, 0xff, 0x4a,
    0x0, 0x0, 0xb0, 0xff, 0xff, 0xb8, 0x0, 0x0,
    0x0, 0x2, 0xe9, 0xff, 0xff, 0x8d, 0x0, 0x0,
    0xb0, 0xff, 0xff, 0xb8, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0xff, 0xff, 0xb1, 0x0, 0x0, 0xb0, 0xff,
    0xff, 0xb8, 0x0, 0x0, 0x0, 0x0, 0xae, 0xff,
    0xff, 0xbe, 0x0, 0x0, 0xb0, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0xff, 0xb8,
    0x0, 0x0, 0xb0, 0xff, 0xff, 0xb8, 0x0, 0x0,
    0x0, 0x2, 0xe6, 0xff, 0xff, 0x9a, 0x0, 0x0,
    0xb0, 0xff, 0xff, 0xdf, 0x13, 0x0, 0x0, 0x65,
    0xff, 0xff, 0xff, 0x5c, 0x0, 0x0, 0xb0, 0xff,
    0xff, 0xff, 0xed, 0xac, 0xc0, 0xff, 0xff, 0xff,
    0xe9, 0xb, 0x0, 0x0, 0xb0, 0xff, 0xff, 0xe9,
    0xfd, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x49, 0x0,
    0x0, 0x0, 0xb0, 0xff, 0xff, 0xb8, 0x47, 0xca,
    0xf9, 0xf2, 0xb8, 0x37, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0xff, 0xff, 0xb8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0xff,
    0xff, 0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0x6d, 0xde, 0xff, 0xff, 0xe2,
    0x6e, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x58, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x3, 0x74, 0xd9, 0xfc, 0xe8, 0x90,
    0xa, 0xdf, 0xff, 0xff, 0x2c, 0x0, 0x0, 0x0,
    0xa5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbc, 0xfe,
    0xff, 0xff, 0x2c, 0x0, 0x0, 0x53, 0xff, 0xff,
    0xff, 0xeb, 0xab, 0xca, 0xff, 0xff, 0xff, 0xff,
    0x2c, 0x0, 0x0, 0xc1, 0xff, 0xff, 0xe4, 0x16,
    0x0, 0x0, 0x84, 0xff, 0xff, 0xff, 0x2c, 0x0,
    0x9, 0xfb, 0xff, 0xff, 0x7b, 0x0, 0x0, 0x0,
    0x40, 0xff, 0xff, 0xff, 0x2c, 0x0, 0x29, 0xff,
    0xff, 0xff, 0x46, 0x0, 0x0, 0x0, 0x40, 0xff,
    0xff, 0xff, 0x2c, 0x0, 0x37, 0xff, 0xff, 0xff,
    0x35, 0x0, 0x0, 0x0, 0x40, 0xff, 0xff, 0xff,
    0x2c, 0x0, 0x31, 0xff, 0xff, 0xff, 0x3f, 0x0,
    0x0, 0x0, 0x40, 0xff, 0xff, 0xff, 0x2c, 0x0,
    0x12, 0xff, 0xff, 0xff, 0x69, 0x0, 0x0, 0x0,
    0x40, 0xff, 0xff, 0xff, 0x2c, 0x0, 0x0, 0xd4,
    0xff, 0xff, 0xd4, 0xb, 0x0, 0x0, 0x7a, 0xff,
    0xff, 0xff, 0x2c, 0x0, 0x0, 0x69, 0xff, 0xff,
    0xff, 0xe4, 0xaa, 0xc9, 0xff, 0xff, 0xff, 0xff,
    0x2c, 0x0, 0x0, 0x3, 0xb7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xeb, 0xff, 0xff, 0xff, 0x2c, 0x0,
    0x0, 0x0, 0x6, 0x7b, 0xdc, 0xfc, 0xea, 0x95,
    0x4d, 0xff, 0xff, 0xff, 0x2c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0xff,
    0xff, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x40, 0xff, 0xff, 0xff,
    0x2c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x57, 0xa5, 0xff, 0xff, 0xff, 0x97, 0x3b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb8,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x1, 0x2c, 0xff, 0xff, 0xff, 0xff,
    0xde, 0x1b, 0xb6, 0xf8, 0xff, 0xc, 0x2c, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xcc, 0xff, 0xff, 0xec,
    0x0, 0x4, 0x36, 0x98, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcd, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xff, 0xff, 0xa3, 0x21, 0x10, 0xc, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xff, 0xff, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3a, 0x9f, 0xff, 0xff, 0xff, 0x6a,
    0x22, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x12, 0x89, 0xd7, 0xf8, 0xfe, 0xec, 0xc5,
    0x84, 0x23, 0x0, 0x18, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x95, 0xff,
    0xff, 0xf1, 0x7a, 0x5c, 0x8c, 0xfc, 0xff, 0xc4,
    0x0, 0xd1, 0xff, 0xff, 0x88, 0x0, 0x0, 0x0,
    0xcb, 0xff, 0xc6, 0x0, 0xc3, 0xff, 0xff, 0xd8,
    0x2f, 0x0, 0x0, 0x47, 0x70, 0x57, 0x0, 0x62,
    0xff, 0xff, 0xff, 0xff, 0xdd, 0x9b, 0x4a, 0x2,
    0x0, 0x0, 0x0, 0x77, 0xf7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x2c, 0x0, 0x0, 0x0, 0x17,
    0x6e, 0xb2, 0xed, 0xff, 0xff, 0xff, 0xe2, 0x7,
    0x80, 0x98, 0x5d, 0x0, 0x0, 0x4, 0x71, 0xff,
    0xff, 0xff, 0x3d, 0xd6, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0x44, 0xd4, 0xff,
    0xfe, 0xa1, 0x6c, 0x6d, 0xc2, 0xff, 0xff, 0xf7,
    0x12, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x6e, 0x0, 0x18, 0x6f, 0xb5, 0xe4,
    0xfa, 0xfd, 0xe9, 0xb2, 0x45, 0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0xf4, 0xff, 0xff, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf4, 0xff, 0xff, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf4, 0xff, 0xff, 0x74,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc8, 0x0, 0xb4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc8, 0x0, 0x49, 0x68, 0xf9,
    0xff, 0xff, 0xad, 0x68, 0x51, 0x0, 0x0, 0x0,
    0xf4, 0xff, 0xff, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf4, 0xff, 0xff, 0x74, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0xff, 0xff, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf4, 0xff, 0xff, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf4, 0xff, 0xff, 0x74,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0xff, 0xff,
    0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0xed, 0xff,
    0xff, 0x8d, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc6,
    0xff, 0xff, 0xfc, 0xc0, 0xa8, 0x0, 0x0, 0x0,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x65, 0xd7, 0xfb, 0xf3, 0xc8, 0x7,

    /* U+0075 "u" */
    0x90, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x90,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x15, 0x6f,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x1, 0x34, 0xb4,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x88, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x24, 0xff, 0xff, 0xff, 0x4e, 0x0, 0x0,
    0x0, 0x88, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x8, 0xfb, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x13,
    0xcc, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0xff, 0xcb, 0xb4, 0xf0, 0xff,
    0xff, 0xff, 0xf6, 0x80, 0x23, 0x0, 0x0, 0x37,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb6, 0xff,
    0xff, 0xff, 0xff, 0x6c, 0x0, 0x0, 0x0, 0x37,
    0xba, 0xf5, 0xf9, 0xc9, 0x52, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0x6c,

    /* U+0076 "v" */
    0xbc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x68, 0x0,
    0xc8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0xbc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x68, 0x0, 0xc8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x20, 0x77,
    0xff, 0xff, 0xff, 0x64, 0xf, 0x0, 0x23, 0x87,
    0xff, 0xff, 0xe3, 0x41, 0xd, 0x0, 0x7, 0xed,
    0xff, 0xff, 0x73, 0x0, 0x0, 0x0, 0xa1, 0xff,
    0xff, 0x84, 0x0, 0x0, 0x0, 0x0, 0x97, 0xff,
    0xff, 0xc6, 0x0, 0x0, 0xa, 0xf3, 0xff, 0xff,
    0x25, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xff, 0xff,
    0xfe, 0x1b, 0x0, 0x59, 0xff, 0xff, 0xc4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0x6c, 0x0, 0xb5, 0xff, 0xff, 0x64, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x83, 0xff, 0xff, 0xbf,
    0x15, 0xfc, 0xff, 0xf6, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x27, 0xff, 0xff, 0xfd, 0x7c,
    0xff, 0xff, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcb, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x17, 0xfc, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb6, 0xff, 0xff, 0xff, 0x21, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x84, 0x0,
    0x0, 0x9a, 0xff, 0xfa, 0x11, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x9c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x84, 0x0, 0x6, 0xed, 0xff,
    0xff, 0x60, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1a, 0x54, 0xfb, 0xff, 0xff, 0x77,
    0x15, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xb5, 0x0,
    0x0, 0x32, 0xbb, 0xff, 0xff, 0xa6, 0x36, 0x0,
    0x0, 0xc2, 0xff, 0xff, 0x73, 0x0, 0x0, 0xa8,
    0xff, 0xff, 0xff, 0xfa, 0x11, 0x0, 0x0, 0xd2,
    0xff, 0xff, 0x32, 0x0, 0x0, 0x0, 0x7a, 0xff,
    0xff, 0xaf, 0x0, 0xc, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0x61, 0x0, 0x18, 0xff, 0xff, 0xe5, 0x1,
    0x0, 0x0, 0x0, 0x34, 0xff, 0xff, 0xea, 0x0,
    0x5b, 0xff, 0xff, 0xba, 0xff, 0xff, 0xb7, 0x0,
    0x5c, 0xff, 0xff, 0x96, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xea, 0xff, 0xff, 0x25, 0xb6, 0xff, 0xff,
    0x3a, 0xe9, 0xff, 0xfa, 0x12, 0xa1, 0xff, 0xff,
    0x47, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa3, 0xff,
    0xff, 0x73, 0xfb, 0xff, 0xde, 0x1, 0x92, 0xff,
    0xff, 0x62, 0xe6, 0xff, 0xf3, 0x7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5e, 0xff, 0xff, 0xec, 0xff,
    0xff, 0x85, 0x0, 0x35, 0xff, 0xff, 0xdb, 0xff,
    0xff, 0xab, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2a, 0x0,
    0x0, 0xd7, 0xff, 0xff, 0xff, 0xff, 0x5e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce, 0xff,
    0xff, 0xff, 0xcf, 0x0, 0x0, 0x0, 0x7c, 0xff,
    0xff, 0xff, 0xfd, 0x13, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x86, 0xff, 0xff, 0xff, 0x74,
    0x0, 0x0, 0x0, 0x20, 0xff, 0xff, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x40, 0xff, 0xff, 0xfe, 0x1b, 0x0, 0x0, 0x0,
    0x0, 0xc3, 0xff, 0xff, 0x72, 0x0, 0x0, 0x0,
    0x0,

    /* U+0078 "x" */
    0x5c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0x0,
    0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0x98, 0x0,
    0x5c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0x0,
    0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0x98, 0x0,
    0xc, 0x38, 0xae, 0xff, 0xff, 0xff, 0x49, 0x0,
    0x6, 0xb5, 0xff, 0xff, 0xe9, 0x49, 0x17, 0x0,
    0x0, 0x0, 0x9, 0xcd, 0xff, 0xff, 0xdd, 0xf,
    0x5c, 0xff, 0xff, 0xfa, 0x3c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xe9, 0xff, 0xff, 0xbc,
    0xf4, 0xff, 0xff, 0x6a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3b, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0x9b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9b, 0xff, 0xff,
    0xff, 0xfe, 0x27, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x48, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x31, 0xf3, 0xff, 0xff, 0xb7,
    0xf5, 0xff, 0xff, 0xb6, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xe4, 0xff, 0xff, 0xd3, 0xb,
    0x59, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x18, 0x44, 0xd0, 0xff, 0xff, 0xfc, 0x38, 0x0,
    0x2, 0xa6, 0xff, 0xff, 0xff, 0x80, 0x32, 0x3,
    0xa4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0x0,
    0x64, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c,
    0xa4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0x0,
    0x64, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c,

    /* U+0079 "y" */
    0xbc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x68, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0xbc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x68,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0x1d, 0x52,
    0xfe, 0xff, 0xff, 0xae, 0x2b, 0x0, 0xc, 0x40,
    0xfc, 0xff, 0xff, 0x7d, 0x22, 0x0, 0x0, 0xba,
    0xff, 0xff, 0xda, 0x0, 0x0, 0x0, 0x56, 0xff,
    0xff, 0xf0, 0x8, 0x0, 0x0, 0x0, 0x53, 0xff,
    0xff, 0xff, 0x35, 0x0, 0x0, 0xb4, 0xff, 0xff,
    0x94, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe8, 0xff,
    0xff, 0x8f, 0x0, 0x18, 0xfc, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x86, 0xff, 0xff,
    0xe7, 0x3, 0x73, 0xff, 0xff, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x22, 0xfe, 0xff, 0xff,
    0x45, 0xd1, 0xff, 0xff, 0x69, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xba, 0xff, 0xff, 0xca,
    0xff, 0xff, 0xf6, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x53, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xe8, 0xff, 0xff, 0xff, 0xff,
    0x3d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0xff, 0xff, 0xff, 0xd8, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x22, 0xfe, 0xff, 0xff, 0x75, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x21, 0xff, 0xff, 0xfb, 0x18, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xa4,
    0xff, 0xff, 0xad, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x40, 0xbd, 0xdf, 0xff, 0xff,
    0xff, 0x37, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x72, 0xff, 0xff, 0xff, 0xff, 0x8e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x75, 0xef, 0xfd, 0xd7, 0x6a, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xb0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbc, 0xb0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xbc, 0xb0, 0xff,
    0xff, 0x80, 0x68, 0x68, 0xc4, 0xff, 0xff, 0xff,
    0x80, 0xb0, 0xff, 0xfd, 0x8, 0x0, 0x3d, 0xfb,
    0xff, 0xff, 0xc1, 0x3, 0x71, 0xa4, 0x91, 0x0,
    0x16, 0xe5, 0xff, 0xff, 0xea, 0x1a, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xfe, 0x47,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x86, 0xff, 0xff,
    0xff, 0x87, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xff, 0xff, 0xc3, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x22, 0xef, 0xff, 0xff, 0xed, 0x1c, 0x0,
    0x72, 0xe4, 0xe4, 0x8, 0xcf, 0xff, 0xff, 0xff,
    0x4a, 0x0, 0x0, 0xb1, 0xff, 0xff, 0x9c, 0xff,
    0xff, 0xff, 0xd9, 0x78, 0x78, 0x78, 0xec, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x8b, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xa3, 0xff, 0xff, 0x33,
    0x0, 0x0, 0x0, 0x90, 0xff, 0xff, 0xb7, 0x18,
    0x0, 0x0, 0x15, 0xfb, 0xff, 0xf3, 0xe, 0x0,
    0x0, 0x0, 0x55, 0xff, 0xff, 0xb8, 0x0, 0x0,
    0x0, 0x0, 0x71, 0xff, 0xff, 0x9f, 0x0, 0x0,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x7b, 0xff, 0xff, 0x96, 0x0, 0x0,
    0x0, 0x0, 0xb6, 0xff, 0xff, 0x69, 0x0, 0x0,
    0x38, 0xa2, 0xff, 0xff, 0xe5, 0xe, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0xdc, 0x29, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xff, 0xfd, 0x6e, 0x0, 0x0, 0x0,
    0x3, 0x44, 0xf3, 0xff, 0xfd, 0x2a, 0x0, 0x0,
    0x0, 0x0, 0x95, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x75, 0xff, 0xff, 0x9b, 0x0, 0x0,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x74, 0xff, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x69, 0xff, 0xff, 0xa4, 0x0, 0x0,
    0x0, 0x0, 0x40, 0xff, 0xff, 0xcb, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xe3, 0xff, 0xff, 0x36, 0x0,
    0x0, 0x0, 0x0, 0x50, 0xfe, 0xff, 0xf0, 0x36,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xdd, 0xfc, 0x16,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x33, 0x0,

    /* U+007C "|" */
    0x74, 0xff, 0x98, 0x74, 0xff, 0x98, 0x74, 0xff,
    0x98, 0x74, 0xff, 0x98, 0x74, 0xff, 0x98, 0x74,
    0xff, 0x98, 0x74, 0xff, 0x98, 0x74, 0xff, 0x98,
    0x74, 0xff, 0x98, 0x74, 0xff, 0x98, 0x74, 0xff,
    0x98, 0x74, 0xff, 0x98, 0x74, 0xff, 0x98, 0x74,
    0xff, 0x98, 0x74, 0xff, 0x98, 0x74, 0xff, 0x98,
    0x74, 0xff, 0x98, 0x74, 0xff, 0x98, 0x74, 0xff,
    0x98, 0x74, 0xff, 0x98, 0x2a, 0x5c, 0x37,

    /* U+007D "}" */
    0x8, 0x92, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x53, 0xff, 0xfe, 0x8b, 0x1, 0x0, 0x0, 0x0,
    0x23, 0xce, 0xff, 0xff, 0x6f, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xff, 0xff, 0xeb, 0x5, 0x0, 0x0,
    0x0, 0x0, 0xda, 0xff, 0xff, 0x34, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x4e, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0x54, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0x54, 0x0, 0x0,
    0x0, 0x0, 0xb5, 0xff, 0xff, 0x5b, 0x0, 0x0,
    0x0, 0x0, 0x89, 0xff, 0xff, 0x96, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf3, 0xff, 0xfd, 0x8f, 0x28,
    0x0, 0x0, 0x0, 0x37, 0xe6, 0xff, 0xff, 0x68,
    0x0, 0x0, 0x0, 0x8b, 0xff, 0xff, 0xff, 0x68,
    0x0, 0x0, 0x4c, 0xff, 0xff, 0xe4, 0x31, 0x2,
    0x0, 0x0, 0xa1, 0xff, 0xff, 0x75, 0x0, 0x0,
    0x0, 0x0, 0xbb, 0xff, 0xff, 0x55, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0x54, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xff, 0xff, 0x54, 0x0, 0x0,
    0x0, 0x0, 0xc5, 0xff, 0xff, 0x4a, 0x0, 0x0,
    0x0, 0x2, 0xec, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x59, 0xff, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x4e, 0xf9, 0xff, 0xf7, 0x36, 0x0, 0x0, 0x0,
    0x32, 0xff, 0xd1, 0x37, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x36, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x2a, 0xbb, 0xf8, 0xf2, 0xb2, 0x3a, 0x0,
    0x0, 0x0, 0x19, 0x80, 0x63, 0x12, 0x17, 0xea,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x89, 0x6, 0x0,
    0x76, 0xff, 0xff, 0x2b, 0x88, 0xff, 0xff, 0xd9,
    0xc9, 0xff, 0xff, 0xff, 0xe6, 0xc3, 0xfe, 0xff,
    0xe6, 0x3, 0xc5, 0xff, 0xce, 0x5, 0x0, 0x42,
    0xe5, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0x0,
    0x42, 0x6b, 0x47, 0x0, 0x0, 0x0, 0x13, 0x8e,
    0xe2, 0xfd, 0xda, 0x5c, 0x0, 0x0,

    /* U+00B0 "°" */
    0x0, 0x18, 0x8d, 0xb4, 0x7e, 0xc, 0x0, 0x17,
    0xe3, 0xff, 0xff, 0xff, 0xc9, 0x6, 0x89, 0xff,
    0x74, 0xa, 0x98, 0xff, 0x5e, 0xbb, 0xfc, 0x3,
    0x0, 0x2c, 0xff, 0x8f, 0xa2, 0xff, 0x3a, 0x0,
    0x61, 0xff, 0x77, 0x37, 0xfc, 0xf6, 0xc5, 0xfb,
    0xf0, 0x19, 0x0, 0x4e, 0xd7, 0xfc, 0xc9, 0x35,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 94, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 95, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 68, .adv_w = 147, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 124, .adv_w = 225, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 362, .adv_w = 208, .box_w = 13, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 661, .adv_w = 268, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 950, .adv_w = 240, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1222, .adv_w = 84, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 1250, .adv_w = 128, .box_w = 7, .box_h = 25, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 1425, .adv_w = 134, .box_w = 8, .box_h = 25, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 1625, .adv_w = 171, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 1746, .adv_w = 206, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1915, .adv_w = 94, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 1955, .adv_w = 153, .box_w = 8, .box_h = 3, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 1979, .adv_w = 98, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1995, .adv_w = 149, .box_w = 11, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 2204, .adv_w = 219, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2408, .adv_w = 170, .box_w = 10, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2588, .adv_w = 213, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2809, .adv_w = 209, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3030, .adv_w = 217, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3251, .adv_w = 205, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3472, .adv_w = 215, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3676, .adv_w = 208, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3897, .adv_w = 209, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4118, .adv_w = 214, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4339, .adv_w = 85, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4391, .adv_w = 85, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 4481, .adv_w = 190, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4624, .adv_w = 211, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 4712, .adv_w = 193, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4855, .adv_w = 185, .box_w = 12, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5059, .adv_w = 339, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5521, .adv_w = 284, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5827, .adv_w = 252, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6099, .adv_w = 248, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6354, .adv_w = 267, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6626, .adv_w = 245, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6881, .adv_w = 237, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7136, .adv_w = 257, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7408, .adv_w = 300, .box_w = 19, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7731, .adv_w = 134, .box_w = 8, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7867, .adv_w = 225, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8105, .adv_w = 290, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8411, .adv_w = 225, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8649, .adv_w = 387, .box_w = 24, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9057, .adv_w = 301, .box_w = 19, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9380, .adv_w = 271, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9669, .adv_w = 248, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9941, .adv_w = 272, .box_w = 17, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10298, .adv_w = 264, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10570, .adv_w = 234, .box_w = 13, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10791, .adv_w = 260, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11063, .adv_w = 292, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11369, .adv_w = 288, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11675, .adv_w = 413, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12117, .adv_w = 284, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12423, .adv_w = 283, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12729, .adv_w = 230, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12967, .adv_w = 112, .box_w = 6, .box_h = 24, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 13111, .adv_w = 160, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13320, .adv_w = 109, .box_w = 6, .box_h = 24, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13464, .adv_w = 166, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 13563, .adv_w = 205, .box_w = 11, .box_h = 3, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13596, .adv_w = 111, .box_w = 7, .box_h = 4, .ofs_x = 0, .ofs_y = 15},
    {.bitmap_index = 13624, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13806, .adv_w = 221, .box_w = 14, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 14058, .adv_w = 203, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14214, .adv_w = 227, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14466, .adv_w = 202, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14622, .adv_w = 150, .box_w = 10, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14802, .adv_w = 225, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 15054, .adv_w = 244, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15324, .adv_w = 124, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15468, .adv_w = 113, .box_w = 7, .box_h = 23, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 15629, .adv_w = 247, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15899, .adv_w = 122, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16043, .adv_w = 359, .box_w = 23, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16342, .adv_w = 245, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16537, .adv_w = 217, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16706, .adv_w = 233, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 16958, .adv_w = 217, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 17210, .adv_w = 166, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17364, .adv_w = 194, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17507, .adv_w = 138, .box_w = 9, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17651, .adv_w = 239, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17846, .adv_w = 234, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18041, .adv_w = 342, .box_w = 21, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18314, .adv_w = 247, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18522, .adv_w = 241, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 18792, .adv_w = 208, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 18935, .adv_w = 126, .box_w = 8, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 19127, .adv_w = 80, .box_w = 3, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 19190, .adv_w = 127, .box_w = 8, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 19382, .adv_w = 248, .box_w = 14, .box_h = 5, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 19452, .adv_w = 140, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 11}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 1, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    3, 3,
    3, 8,
    3, 34,
    3, 66,
    3, 68,
    3, 69,
    3, 70,
    3, 72,
    3, 74,
    3, 78,
    3, 79,
    3, 80,
    3, 81,
    3, 82,
    3, 84,
    3, 88,
    8, 3,
    8, 8,
    8, 34,
    8, 66,
    8, 68,
    8, 69,
    8, 70,
    8, 72,
    8, 74,
    8, 78,
    8, 79,
    8, 80,
    8, 81,
    8, 82,
    8, 84,
    8, 88,
    9, 55,
    9, 56,
    9, 58,
    13, 3,
    13, 8,
    15, 3,
    15, 8,
    16, 16,
    34, 3,
    34, 8,
    34, 32,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 68,
    34, 69,
    34, 70,
    34, 72,
    34, 74,
    34, 78,
    34, 79,
    34, 80,
    34, 81,
    34, 82,
    34, 85,
    34, 86,
    34, 87,
    34, 88,
    34, 90,
    35, 53,
    35, 55,
    35, 58,
    36, 10,
    36, 53,
    36, 62,
    36, 94,
    37, 13,
    37, 15,
    37, 34,
    37, 36,
    37, 40,
    37, 48,
    37, 50,
    37, 53,
    37, 55,
    37, 57,
    37, 58,
    37, 59,
    38, 53,
    38, 68,
    38, 69,
    38, 70,
    38, 72,
    38, 80,
    38, 82,
    38, 86,
    38, 87,
    38, 88,
    38, 90,
    39, 13,
    39, 15,
    39, 34,
    39, 36,
    39, 40,
    39, 43,
    39, 48,
    39, 50,
    39, 53,
    39, 66,
    39, 68,
    39, 69,
    39, 70,
    39, 72,
    39, 80,
    39, 82,
    39, 83,
    39, 86,
    39, 87,
    39, 90,
    41, 34,
    42, 34,
    43, 34,
    44, 14,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 80,
    44, 82,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 86,
    45, 87,
    45, 88,
    45, 90,
    46, 34,
    47, 34,
    48, 13,
    48, 15,
    48, 34,
    48, 36,
    48, 40,
    48, 48,
    48, 50,
    48, 53,
    48, 55,
    48, 57,
    48, 58,
    48, 59,
    49, 13,
    49, 15,
    49, 34,
    49, 43,
    49, 57,
    49, 59,
    49, 66,
    49, 68,
    49, 69,
    49, 70,
    49, 72,
    49, 80,
    49, 82,
    49, 85,
    49, 87,
    49, 90,
    50, 53,
    50, 55,
    50, 56,
    50, 58,
    51, 53,
    51, 55,
    51, 58,
    53, 13,
    53, 14,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 43,
    53, 48,
    53, 50,
    53, 52,
    53, 53,
    53, 55,
    53, 56,
    53, 58,
    53, 66,
    53, 67,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 74,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    55, 10,
    55, 13,
    55, 14,
    55, 15,
    55, 34,
    55, 36,
    55, 40,
    55, 43,
    55, 48,
    55, 50,
    55, 62,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 80,
    55, 82,
    55, 83,
    55, 86,
    55, 87,
    55, 90,
    55, 94,
    56, 10,
    56, 13,
    56, 14,
    56, 15,
    56, 34,
    56, 43,
    56, 53,
    56, 62,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 83,
    56, 86,
    56, 94,
    57, 14,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 55,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 80,
    57, 82,
    57, 86,
    57, 87,
    57, 90,
    58, 7,
    58, 10,
    58, 11,
    58, 13,
    58, 14,
    58, 15,
    58, 34,
    58, 36,
    58, 40,
    58, 43,
    58, 48,
    58, 50,
    58, 52,
    58, 53,
    58, 55,
    58, 56,
    58, 57,
    58, 58,
    58, 62,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 74,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    58, 94,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    59, 68,
    59, 69,
    59, 70,
    59, 72,
    59, 80,
    59, 82,
    59, 86,
    59, 87,
    59, 88,
    59, 90,
    60, 43,
    60, 54,
    60, 62,
    61, 61,
    66, 3,
    66, 8,
    66, 67,
    66, 85,
    66, 86,
    66, 87,
    66, 88,
    66, 90,
    67, 3,
    67, 8,
    67, 68,
    67, 69,
    67, 70,
    67, 72,
    67, 73,
    67, 74,
    67, 76,
    67, 77,
    67, 78,
    67, 79,
    67, 80,
    67, 81,
    67, 82,
    67, 87,
    67, 89,
    67, 90,
    67, 91,
    68, 3,
    68, 8,
    69, 85,
    69, 87,
    69, 90,
    70, 3,
    70, 8,
    70, 70,
    70, 80,
    70, 87,
    70, 90,
    71, 10,
    71, 62,
    71, 68,
    71, 69,
    71, 72,
    71, 82,
    71, 94,
    72, 36,
    72, 40,
    72, 48,
    72, 50,
    72, 70,
    72, 80,
    73, 3,
    73, 8,
    73, 68,
    73, 69,
    73, 70,
    73, 72,
    73, 80,
    73, 82,
    73, 85,
    73, 86,
    73, 87,
    73, 88,
    73, 90,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 80,
    76, 82,
    77, 70,
    77, 80,
    77, 87,
    77, 90,
    78, 3,
    78, 8,
    78, 68,
    78, 69,
    78, 70,
    78, 72,
    78, 80,
    78, 82,
    78, 85,
    78, 86,
    78, 87,
    78, 88,
    78, 90,
    79, 3,
    79, 8,
    79, 68,
    79, 69,
    79, 70,
    79, 72,
    79, 80,
    79, 82,
    79, 85,
    79, 86,
    79, 87,
    79, 88,
    79, 90,
    80, 3,
    80, 8,
    80, 67,
    80, 68,
    80, 69,
    80, 70,
    80, 72,
    80, 73,
    80, 76,
    80, 77,
    80, 80,
    80, 82,
    80, 85,
    80, 87,
    80, 88,
    80, 89,
    80, 90,
    80, 91,
    81, 3,
    81, 8,
    81, 68,
    81, 69,
    81, 70,
    81, 72,
    81, 73,
    81, 74,
    81, 76,
    81, 77,
    81, 78,
    81, 79,
    81, 80,
    81, 81,
    81, 82,
    81, 87,
    81, 89,
    81, 90,
    81, 91,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 72,
    83, 73,
    83, 74,
    83, 76,
    83, 77,
    83, 78,
    83, 79,
    83, 80,
    83, 81,
    83, 82,
    83, 85,
    83, 87,
    83, 88,
    83, 90,
    85, 70,
    85, 74,
    85, 78,
    85, 79,
    85, 80,
    85, 81,
    86, 87,
    86, 88,
    86, 90,
    87, 3,
    87, 8,
    87, 13,
    87, 15,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 71,
    87, 72,
    87, 80,
    87, 82,
    87, 84,
    88, 13,
    88, 15,
    88, 70,
    88, 80,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 80,
    89, 82,
    90, 3,
    90, 8,
    90, 13,
    90, 15,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 71,
    90, 72,
    90, 80,
    90, 82,
    90, 84,
    91, 68,
    91, 69,
    91, 70,
    91, 72,
    91, 80,
    91, 82,
    92, 43,
    92, 54,
    92, 94
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -18, -18, -22, -9, -11, -11, -30, -11,
    -4, -4, -4, -30, -4, -11, -17, 2,
    -18, -18, -22, -9, -11, -11, -30, -11,
    -4, -4, -4, -30, -4, -11, -17, 2,
    4, 7, 4, -53, -53, -53, -53, -23,
    -52, -52, -28, -11, -11, -11, -11, -28,
    -11, -37, -29, -48, -2, -2, -2, -2,
    -4, -4, -4, -2, -4, -2, -22, -21,
    -36, -32, -36, -5, -4, -10, -5, -5,
    -2, -3, -23, -23, -11, 4, 4, 4,
    4, -5, -4, -6, -8, -4, 4, -4,
    -4, -4, -4, -4, -4, -3, -5, -4,
    -5, -57, -57, -43, -7, -7, -39, -7,
    -7, 4, -6, -4, -4, -9, -4, -9,
    -4, -5, -4, -4, -4, -18, -18, -19,
    -36, -18, -18, -18, -18, -5, -5, -9,
    -5, -9, -5, -4, -7, -12, -7, -58,
    -58, -5, -5, -5, -5, -38, -14, -49,
    -17, -52, -3, -23, -10, -23, -18, -18,
    -23, -23, -11, 4, 4, 4, 4, -5,
    -4, -6, -8, -4, -76, -76, -44, -34,
    -10, -7, -2, -2, -2, -2, -2, -2,
    -2, 3, 3, 3, -6, -5, -3, -7,
    -9, -17, -19, -48, -51, -48, -34, -5,
    -5, -37, -5, -5, -3, 2, 3, 3,
    3, -16, 5, -17, -17, -15, -17, -17,
    -17, -17, -15, -17, -17, -12, -14, -12,
    -6, -9, -14, -6, -11, -19, 4, -40,
    -29, -40, -41, -2, -2, -40, -2, -2,
    3, -9, -8, -8, -9, -8, -9, -8,
    -6, -5, -2, -2, 4, 7, -27, -19,
    -27, -32, -28, 3, 2, -6, -6, -6,
    -6, -6, -6, -6, -4, -4, 3, -37,
    -6, -6, -6, -6, 3, -5, -5, -4,
    -5, -4, -5, -4, -6, -6, -6, 4,
    -9, -43, -40, -43, -48, -5, -5, -54,
    -5, -5, -3, 3, 3, 3, 2, 3,
    3, -12, -12, -12, -12, -15, -12, -15,
    -15, -15, -12, -15, -12, -7, -11, -4,
    -7, -4, -4, -4, -6, 4, -5, -5,
    -5, -5, -4, -4, -4, -4, -4, -4,
    -4, -5, -5, -5, -3, -3, 6, -23,
    -14, -14, 0, -7, -6, -8, -7, -8,
    -17, -17, 3, 3, 2, 3, -4, -4,
    -4, -4, -4, -4, 2, -4, 3, -2,
    -3, -2, -3, -13, -13, -12, -3, -3,
    -14, -14, 1, 1, -4, -4, 9, 3,
    -4, -4, -4, -4, 4, 6, 6, 6,
    6, -2, -2, -31, -31, -2, -2, -2,
    -2, -2, -2, -7, -8, -15, -14, -15,
    -4, -4, -10, -4, -10, -4, -4, -4,
    -1, -1, -31, -31, -2, -2, -2, -2,
    -2, -2, -7, -8, -15, -14, -15, -31,
    -31, -2, -2, -2, -2, -2, -2, -7,
    -8, -15, -14, -15, -22, -22, -4, 2,
    2, 2, 2, -4, -4, -4, 2, 2,
    -1, -6, -6, -4, -6, -3, -17, -17,
    3, 3, 2, 3, -4, -4, -4, -4,
    -4, -4, 2, -4, 3, -2, -3, -2,
    -3, 1, 1, -32, -32, -4, -4, -4,
    -4, 4, -4, -9, 3, -9, -9, 1,
    1, -4, 1, -4, 5, 3, 3, 3,
    -5, 5, 5, 5, -5, 5, -6, -2,
    -6, 1, 1, -31, -31, -3, -5, -5,
    -4, 2, -5, -4, -5, -1, -23, -23,
    -3, -3, -4, -4, -4, -4, -4, -4,
    1, 1, -31, -31, -3, -5, -5, -4,
    2, -5, -4, -5, -1, -3, -3, -3,
    -3, -3, -3, -4, -4, 8
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 550,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 8,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_lv_demo_high_res_roboto_slab_bold_24 = {
#else
lv_font_t font_lv_demo_high_res_roboto_slab_bold_24 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 26,          /*The maximum line height required by the font*/
    .base_line = 6,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_USE_DEMO_HIGH_RES*/

