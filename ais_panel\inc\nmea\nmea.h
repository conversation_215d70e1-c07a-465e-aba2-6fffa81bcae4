/**
 * @file    nmea.h
 * @brief   nmea header file
 * <AUTHOR>
 * @version 0.1
 * @date    2024-12-21
 * 
 * @copyright Copyright (c) 2024
 */

#ifndef __NMEA_H__
#define __NMEA_H__

#include "sentence.h"

#include "abk.h"
#include "abm.h"
#include "aca.h"
#include "ack.h"
#include "acs.h"
#include "air.h"
#include "alr.h"
#include "bbm.h"
#include "lr1.h"
#include "lr2.h"
#include "lr3.h"
#include "lrf.h"
#include "lri.h"
#include "ssd.h"
#include "txt.h"
#include "vdm.h"
#include "vdo.h"
#include "vsd.h"
#include "spw.h"
#include "gsa.h"
#include "gsv.h"
#include "alf.h"


struct SentenceHandler {
    const char* type;
    CSentence*  parser;
    int32_t     returnType;
};

class CNmea
{
private:
    CSentence *m_pCurParser;
    CSentence *abk;
    CSentence *abm;
    CSentence *aca;
    CSentence *ack;
    CSentence *acs;
    CSentence *air;
    CSentence *alr;
    CSentence *alf;
    CSentence *bbm;
    CSentence *gsa;
    CSentence *gsv;
    CSentence *lr1;
    CSentence *lr2;
    CSentence *lr3;
    CSentence *lrf;
    CSentence *lri;
    CSentence *ssd;
    CSentence *txt;
    CSentence *vdo;
    CSentence *vdm;
    CSentence *vsd;
    CSentence *spw;

public:
    CNmea();
    ~CNmea();

    void       Parse();
    int32_t    SetSentence(const char *pszSentence);
    CSentence *GetCurParser() { return m_pCurParser; }
    int32_t    GetFormat() { return (m_pCurParser) ? m_pCurParser->GetFormat() : CSentence::NMEA_UNKNOWN; }
    bool       IsValidChecksum() { return (m_pCurParser) ? m_pCurParser->IsValidChecksum() : false; }

    inline int32_t FORMAT_COMP(const char *pszText, const char *pszComp) {
        int32_t nOffset = (pszText[1] == 'P') ? 1 : 0;
        return strncmp(pszText+(3-nOffset), pszComp, 3);
    }
};

#endif /* __NMEA_H__ */

