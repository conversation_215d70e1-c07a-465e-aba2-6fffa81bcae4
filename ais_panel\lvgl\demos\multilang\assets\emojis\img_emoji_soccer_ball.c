#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_SOCCER_BALL
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_SOCCER_BALL
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_SOCCER_BALL uint8_t
img_emoji_soccer_ball_map[] = {
    0xfa, 0xfc, 0xfd, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfc, 0xfe, 0xff, 0xff, 0xf5, 0xf7, 0xf8, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xee, 0xf0, 0xf1, 0xff, 0xec, 0xee, 0xef, 0xff, 0xde, 0xe0, 0xe0, 0xff, 0xc9, 0xcb, 0xcb, 0xff, 0xf4, 0xf6, 0xf6, 0xff, 0xf6, 0xf8, 0xf8, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf9, 0xfb, 0xfc, 0xff, 0xf4, 0xf6, 0xf7, 0xff, 0xe9, 0xeb, 0xec, 0xff, 0xac, 0xae, 0xaf, 0xff, 0x5a, 0x5c, 0x5d, 0xff, 0x5b, 0x5d, 0x5d, 0xff, 0x3d, 0x3f, 0x3f, 0xff, 0x62, 0x64, 0x64, 0xff, 0x97, 0x99, 0x99, 0xff, 0xeb, 0xed, 0xed, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfa, 0xfc, 0xfc, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xfc, 0xfc, 0xfc, 0xff,
    0xfb, 0xfd, 0xfe, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0xef, 0xf1, 0xf2, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xbf, 0xc1, 0xc2, 0xff, 0x6a, 0x6c, 0x6c, 0xff, 0x87, 0x89, 0x89, 0xff, 0xd2, 0xd4, 0xd4, 0xff, 0xee, 0xf0, 0xf0, 0xff, 0xf8, 0xfa, 0xfa, 0xff, 0xdd, 0xdf, 0xdf, 0xff, 0xe3, 0xe5, 0xe5, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xfe, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xd7, 0xd9, 0xda, 0xff, 0xe1, 0xe3, 0xe4, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf0, 0xf2, 0xf3, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xce, 0xd0, 0xd1, 0xff, 0xc1, 0xc3, 0xc4, 0xff, 0xe9, 0xeb, 0xec, 0xff, 0xf4, 0xf6, 0xf7, 0xff, 0xf3, 0xf5, 0xf6, 0xff, 0xeb, 0xed, 0xee, 0xff, 0xf5, 0xf7, 0xf8, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xfe, 0xfd, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0xca, 0xcc, 0xcd, 0xff, 0x92, 0x94, 0x95, 0xff, 0xe3, 0xe5, 0xe6, 0xff, 0xee, 0xf0, 0xf1, 0xff, 0xf5, 0xf7, 0xf8, 0xff, 0xf1, 0xf3, 0xf4, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xc4, 0xc6, 0xc7, 0xff, 0xf8, 0xfa, 0xfb, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf5, 0xf7, 0xf8, 0xff, 0xf3, 0xf5, 0xf6, 0xff, 0xee, 0xf0, 0xf1, 0xff, 0x8b, 0x8d, 0x8e, 0xff, 0xce, 0xcd, 0xcf, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xf7, 0xf9, 0xfa, 0xff, 0x79, 0x7b, 0x7c, 0xff, 0x6b, 0x6d, 0x6e, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xee, 0xf0, 0xf1, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0xc7, 0xc9, 0xca, 0xff, 0x4b, 0x4c, 0x50, 0xff, 0x63, 0x64, 0x68, 0xff, 0xca, 0xcb, 0xcf, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xf9, 0xfa, 0xfe, 0xff, 0xfc, 0xfd, 0xff, 0xff, 0xf0, 0xf1, 0xf5, 0xff, 0x6c, 0x6d, 0x71, 0xff, 0x8b, 0x8a, 0x8e, 0xff, 0xf2, 0xf1, 0xf5, 0xff,
    0xcc, 0xce, 0xcf, 0xff, 0x4b, 0x4d, 0x4e, 0xff, 0x53, 0x55, 0x56, 0xff, 0xe9, 0xeb, 0xec, 0xff, 0xf1, 0xf3, 0xf4, 0xff, 0xeb, 0xed, 0xee, 0xff, 0x9a, 0x9c, 0x9d, 0xff, 0x5b, 0x5d, 0x5e, 0xff, 0x6c, 0x6d, 0x71, 0xff, 0x50, 0x51, 0x55, 0xff, 0x5f, 0x60, 0x64, 0xff, 0x93, 0x94, 0x98, 0xff, 0xdb, 0xdc, 0xe0, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xe6, 0xe7, 0xeb, 0xff, 0x5a, 0x5b, 0x5f, 0xff, 0x61, 0x60, 0x64, 0xff, 0xce, 0xcd, 0xd1, 0xff,
    0x96, 0x98, 0x99, 0xff, 0x5b, 0x5d, 0x5e, 0xff, 0x5d, 0x5f, 0x60, 0xff, 0x9d, 0x9f, 0xa0, 0xff, 0xa6, 0xa8, 0xa9, 0xff, 0x7a, 0x7c, 0x7d, 0xff, 0x37, 0x39, 0x3a, 0xff, 0x5f, 0x61, 0x62, 0xff, 0x4f, 0x4f, 0x55, 0xff, 0x53, 0x53, 0x59, 0xff, 0x4a, 0x4a, 0x50, 0xff, 0x51, 0x51, 0x57, 0xff, 0x80, 0x80, 0x86, 0xff, 0x94, 0x94, 0x9a, 0xff, 0xa2, 0xa2, 0xa8, 0xff, 0x6c, 0x6c, 0x72, 0xff, 0x56, 0x54, 0x5a, 0xff, 0xad, 0xab, 0xb1, 0xff,
    0x84, 0x86, 0x87, 0xff, 0x6f, 0x71, 0x72, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xf1, 0xf3, 0xf4, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xae, 0xb0, 0xb1, 0xff, 0x45, 0x47, 0x48, 0xff, 0x43, 0x45, 0x46, 0xff, 0x4d, 0x4f, 0x50, 0xff, 0x46, 0x48, 0x49, 0xff, 0x43, 0x45, 0x46, 0xff, 0x54, 0x56, 0x57, 0xff, 0xbf, 0xc1, 0xc2, 0xff, 0xf5, 0xf7, 0xf8, 0xff, 0xed, 0xef, 0xf0, 0xff, 0xe9, 0xeb, 0xec, 0xff, 0x77, 0x7b, 0x80, 0xff, 0x77, 0x7b, 0x80, 0xff,
    0x8f, 0x91, 0x92, 0xff, 0xd9, 0xdb, 0xdc, 0xff, 0xfb, 0xfd, 0xfe, 0xff, 0xf8, 0xfa, 0xfb, 0xff, 0xe9, 0xeb, 0xec, 0xff, 0xe7, 0xe9, 0xea, 0xff, 0x41, 0x43, 0x44, 0xff, 0x47, 0x49, 0x4a, 0xff, 0x46, 0x48, 0x49, 0xff, 0x3b, 0x3d, 0x3e, 0xff, 0x44, 0x46, 0x47, 0xff, 0x45, 0x47, 0x48, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0xcb, 0xce, 0xd2, 0xff, 0xaa, 0xad, 0xb1, 0xff,
    0xec, 0xee, 0xef, 0xff, 0xd0, 0xd2, 0xd3, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0xf0, 0xf2, 0xf3, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0x6c, 0x6e, 0x6f, 0xff, 0x4b, 0x4d, 0x4e, 0xff, 0x46, 0x48, 0x49, 0xff, 0x36, 0x38, 0x39, 0xff, 0x49, 0x4b, 0x4c, 0xff, 0x78, 0x7a, 0x7b, 0xff, 0xf9, 0xfb, 0xfc, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xe8, 0xea, 0xeb, 0xff, 0xe8, 0xea, 0xeb, 0xff, 0xee, 0xf1, 0xf5, 0xff, 0xdb, 0xde, 0xe2, 0xff,
    0xeb, 0xed, 0xee, 0xff, 0xd4, 0xd6, 0xd7, 0xff, 0xe4, 0xe6, 0xe7, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xe4, 0xe6, 0xe7, 0xff, 0xfd, 0xff, 0xff, 0xff, 0x91, 0x93, 0x94, 0xff, 0x66, 0x68, 0x69, 0xff, 0x73, 0x75, 0x76, 0xff, 0x6f, 0x71, 0x72, 0xff, 0x78, 0x7a, 0x7b, 0xff, 0x9f, 0xa1, 0xa2, 0xff, 0xf2, 0xf4, 0xf5, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xe2, 0xe4, 0xe5, 0xff, 0xf4, 0xf6, 0xf7, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xe5, 0xe7, 0xe8, 0xff,
    0xf1, 0xf3, 0xf4, 0xff, 0xef, 0xf1, 0xf2, 0xff, 0xec, 0xee, 0xef, 0xff, 0xf4, 0xf6, 0xf7, 0xff, 0xee, 0xf0, 0xf1, 0xff, 0xd8, 0xda, 0xdb, 0xff, 0xb1, 0xb3, 0xb4, 0xff, 0xf3, 0xf5, 0xf6, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xf7, 0xf9, 0xfa, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xa0, 0xa2, 0xa3, 0xff, 0xe3, 0xe5, 0xe6, 0xff, 0xed, 0xef, 0xf0, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xe5, 0xe7, 0xe8, 0xff, 0xe7, 0xe9, 0xea, 0xff, 0xf6, 0xf8, 0xf9, 0xff,
    0xf5, 0xf7, 0xf8, 0xff, 0xac, 0xae, 0xaf, 0xff, 0xb5, 0xb7, 0xb8, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xa0, 0xa2, 0xa3, 0xff, 0xd3, 0xd5, 0xd6, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf4, 0xf6, 0xf7, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xe5, 0xe7, 0xe8, 0xff, 0xea, 0xec, 0xed, 0xff, 0xa4, 0xa6, 0xa7, 0xff, 0xf1, 0xf3, 0xf4, 0xff, 0xc3, 0xc5, 0xc6, 0xff, 0xc4, 0xc6, 0xc7, 0xff, 0xc3, 0xc3, 0xc3, 0xff, 0xeb, 0xeb, 0xeb, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0xc8, 0xca, 0xcb, 0xff, 0x58, 0x5a, 0x5b, 0xff, 0x47, 0x49, 0x4a, 0xff, 0x3f, 0x41, 0x42, 0xff, 0x7f, 0x81, 0x82, 0xff, 0xf7, 0xf9, 0xfa, 0xff, 0xed, 0xef, 0xf0, 0xff, 0xf9, 0xfb, 0xfc, 0xff, 0xe0, 0xe2, 0xe3, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xdb, 0xdd, 0xde, 0xff, 0x8d, 0x8f, 0x90, 0xff, 0x51, 0x53, 0x54, 0xff, 0x50, 0x52, 0x53, 0xff, 0x59, 0x5b, 0x5c, 0xff, 0xd3, 0xd3, 0xd3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xfb, 0xfc, 0xff, 0xfd, 0xff, 0xff, 0xff, 0x96, 0x98, 0x99, 0xff, 0x4d, 0x4f, 0x50, 0xff, 0x51, 0x53, 0x54, 0xff, 0x4a, 0x4c, 0x4d, 0xff, 0xda, 0xdc, 0xdd, 0xff, 0xe9, 0xeb, 0xec, 0xff, 0xea, 0xec, 0xed, 0xff, 0xee, 0xf0, 0xf1, 0xff, 0xe8, 0xea, 0xeb, 0xff, 0xf0, 0xf2, 0xf3, 0xff, 0x3f, 0x41, 0x42, 0xff, 0x4d, 0x4f, 0x50, 0xff, 0x5a, 0x5c, 0x5d, 0xff, 0xa4, 0xa6, 0xa7, 0xff, 0xf8, 0xf8, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xf3, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa9, 0xa9, 0xa9, 0xff, 0x53, 0x53, 0x53, 0xff, 0x48, 0x48, 0x48, 0xff, 0x87, 0x87, 0x87, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xdc, 0xe1, 0xe2, 0xff, 0xe1, 0xe5, 0xe6, 0xff, 0xdb, 0xdf, 0xe0, 0xff, 0x8c, 0x8e, 0x8f, 0xff, 0x4b, 0x4d, 0x4e, 0xff, 0x60, 0x5f, 0x61, 0xff, 0xa5, 0xa4, 0xa6, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xf5, 0xf5, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdb, 0xdb, 0xdb, 0xff, 0x83, 0x83, 0x83, 0xff, 0x79, 0x79, 0x79, 0xff, 0xcf, 0xcf, 0xcf, 0xff, 0xd9, 0xde, 0xdf, 0xff, 0xe8, 0xec, 0xed, 0xff, 0xc2, 0xc6, 0xc7, 0xff, 0x66, 0x68, 0x69, 0xff, 0x77, 0x79, 0x7a, 0xff, 0xee, 0xed, 0xef, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfb, 0xfa, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xed, 0xed, 0xff, 0xf1, 0xf1, 0xf1, 0xff, 0xee, 0xf3, 0xf4, 0xff, 0xea, 0xee, 0xef, 0xff, 0xf2, 0xf6, 0xf7, 0xff, 0xf0, 0xf2, 0xf3, 0xff, 0xf1, 0xf3, 0xf4, 0xff, 0xfd, 0xfc, 0xfe, 0xff, 0xfa, 0xf9, 0xfb, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
};

const lv_image_dsc_t img_emoji_soccer_ball = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_soccer_ball_map,
    .data_size = sizeof(img_emoji_soccer_ball_map),
};

#endif
