
Bind a slider's value to a label
--------------------------------

.. lv_example:: others/observer/lv_example_observer_1
  :language: c

Handling login and its states
-----------------------------

.. lv_example:: others/observer/lv_example_observer_2
  :language: c

Set time with 12/24 mode and AM/PM
----------------------------------

.. lv_example:: others/observer/lv_example_observer_3
  :language: c

Custom tab view with state management
-------------------------------------

.. lv_example:: others/observer/lv_example_observer_4
  :language: c

Firmware update process
-----------------------

.. lv_example:: others/observer/lv_example_observer_5
  :language: c

Modular style update on theme change
------------------------------------

.. lv_example:: others/observer/lv_example_observer_6
  :language: c


