#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: mail.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_mail_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9D,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xFD,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xDE,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x1C,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xFF,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xDE,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBE,0x1C,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xFF,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x7F,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,
0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xDE,0x6D,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0xDE,0x6D,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,0x9D,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x65,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9E,0x5D,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xFD,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xDE,0x2C,0x3F,0x8E,0xBF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xF7,0x9F,0x9E,0xFE,0x34,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3E,0x3D,0xDE,0x2C,0xDE,0x2C,0x3F,0x86,0xBF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xF7,0x9F,0x9E,0x1E,0x35,0xBE,0x24,0x1E,0x35,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xBF,0xEF,0x5F,0x96,0xFE,0x2C,0xDE,0x2C,0x3F,0x86,0xBF,0xEF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xDF,0xF7,0x7F,0x96,0xFE,0x34,0xDE,0x24,0xFE,0x75,0x7F,0xDF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xFF,0xFF,0xFF,0xFF,0xBF,0xEF,0x5F,0x96,0xFE,0x2C,0xDE,0x2C,0x3F,0x86,0xBF,0xEF,0xBF,0xEF,0x5F,0x96,0xFE,0x2C,0xDE,0x24,0xFE,0x75,0x9F,0xE7,0xFF,0xFF,0xFF,0xFF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xEF,0x5F,0x96,0xFE,0x2C,0xDE,0x2C,0xDE,0x2C,0xDE,0x24,0x1F,0x7E,0x9F,0xE7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xEF,0x3F,0x86,
0x3F,0x86,0xBF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x3F,0x86,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3F,0x86,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBD,0x2C,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x9E,0x5D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9E,0x5D,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBD,0x2C,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xDE,0x6D,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,
0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0x7F,0x96,0xDE,0x6D,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,0x00,0x00,0xDE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x1C,0x00,0x00,
0x00,0x00,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x9F,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x9E,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9E,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0x9F,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBD,0x2C,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,
0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBE,0x24,0xBD,0x2C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xCA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x1B,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_mail_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_mail_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_mail_icon_data};

