/*******************************************************************************
 * Size: 48 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 48 --font TrumpGothicPro.ttf -r 0x20-0x7F --format lvgl -o font_ebike_trump_48.c --force-fast-kern-format
 ******************************************************************************/

#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "../../../lvgl.h"
#endif

#ifndef FONT_EBIKE_TRUMP_48
    #define FONT_EBIKE_TRUMP_48 1
#endif

#if FONT_EBIKE_TRUMP_48

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x1f, 0xff, 0x30, 0xff, 0xf3, 0xf, 0xff, 0x20,
    0xff, 0xf2, 0xf, 0xff, 0x20, 0xff, 0xf1, 0xe,
    0xff, 0x10, 0xef, 0xf0, 0xe, 0xff, 0x0, 0xdf,
    0xf0, 0xd, 0xff, 0x0, 0xdf, 0xf0, 0xc, 0xfe,
    0x0, 0xcf, 0xe0, 0xb, 0xfe, 0x0, 0xbf, 0xd0,
    0xb, 0xfd, 0x0, 0xaf, 0xd0, 0xa, 0xfc, 0x0,
    0xaf, 0xc0, 0x9, 0xfb, 0x0, 0x9f, 0xb0, 0x8,
    0xfb, 0x0, 0x8f, 0xa0, 0x8, 0xfa, 0x0, 0x7f,
    0x90, 0x7, 0xf9, 0x0, 0x7f, 0x90, 0x6, 0xf8,
    0x0, 0x25, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xc1, 0x5f, 0xff, 0x75,
    0xff, 0xf7, 0xa, 0xfb, 0x0,

    /* U+0022 "\"" */
    0xcf, 0xf0, 0x5f, 0xf7, 0xbf, 0xe0, 0x4f, 0xf6,
    0xaf, 0xd0, 0x3f, 0xf4, 0x9f, 0xc0, 0x2f, 0xf3,
    0x8f, 0xb0, 0x1f, 0xf2, 0x7f, 0xa0, 0xf, 0xf1,
    0x6f, 0x90, 0xe, 0xf0, 0x5f, 0x80, 0xd, 0xf0,
    0x3f, 0x60, 0xc, 0xe0, 0x2f, 0x50, 0xb, 0xd0,
    0x3, 0x10, 0x2, 0x20,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0x60, 0x0,
    0x5, 0x87, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xa0, 0x0, 0xd, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x60, 0x0, 0x1f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x20,
    0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0, 0xdf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf2, 0x0, 0x5, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xe0, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0xd, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x60, 0x0, 0x1f, 0xfa, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x7b, 0xbb, 0xcf, 0xfd, 0xbb, 0xbb, 0xff, 0xeb,
    0xbb, 0x90, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x0,
    0x2, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf1, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0xa, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x90,
    0x0, 0xd, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x50, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x6, 0xbb, 0xbd, 0xff, 0xcb, 0xbb, 0xcf,
    0xfd, 0xbb, 0xb9, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x0, 0x1,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf1, 0x0, 0x5, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xd0, 0x0, 0x9, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0,
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x60, 0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x20, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe, 0x0,
    0x0, 0x9f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfa, 0x0, 0x0, 0xdf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf6, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2,
    0x0, 0x5, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x68, 0x70, 0x0, 0x4, 0x88, 0x10, 0x0,
    0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x4, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf1,
    0x0, 0x0, 0x0, 0x6, 0xcf, 0xfd, 0x81, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xfe, 0x30, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0xe, 0xff, 0x78, 0xf3,
    0xef, 0xf5, 0x3f, 0xfd, 0x8, 0xf1, 0x6f, 0xf9,
    0x5f, 0xf9, 0x8, 0xf1, 0x3f, 0xfc, 0x6f, 0xf8,
    0x8, 0xf1, 0x1f, 0xfd, 0x7f, 0xf8, 0x8, 0xf1,
    0x1f, 0xfd, 0x7f, 0xf8, 0x8, 0xf1, 0x1f, 0xfe,
    0x7f, 0xf8, 0x8, 0xf1, 0x1f, 0xfe, 0x7f, 0xf8,
    0x8, 0xf1, 0x1f, 0xfe, 0x7f, 0xf8, 0x8, 0xf1,
    0x1f, 0xfe, 0x6f, 0xfa, 0x8, 0xf1, 0x1f, 0xfe,
    0x4f, 0xfd, 0x8, 0xf1, 0x8, 0x87, 0xf, 0xff,
    0x38, 0xf1, 0x0, 0x0, 0xa, 0xff, 0xb8, 0xf1,
    0x0, 0x0, 0x3, 0xff, 0xfe, 0xf1, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x8, 0xf8,
    0xff, 0xf4, 0x0, 0x0, 0x8, 0xf1, 0xdf, 0xf9,
    0x37, 0x73, 0x8, 0xf1, 0x6f, 0xfc, 0x7f, 0xf8,
    0x8, 0xf1, 0x2f, 0xfd, 0x7f, 0xf8, 0x8, 0xf1,
    0x1f, 0xfe, 0x7f, 0xf8, 0x8, 0xf1, 0x1f, 0xfe,
    0x7f, 0xf8, 0x8, 0xf1, 0x1f, 0xfe, 0x7f, 0xf8,
    0x8, 0xf1, 0x1f, 0xfd, 0x6f, 0xf8, 0x8, 0xf1,
    0x1f, 0xfd, 0x5f, 0xf9, 0x8, 0xf1, 0x2f, 0xfc,
    0x3f, 0xfa, 0x8, 0xf1, 0x4f, 0xfa, 0xf, 0xff,
    0x18, 0xf1, 0xaf, 0xf7, 0xc, 0xff, 0xcb, 0xf9,
    0xff, 0xf2, 0x4, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x8f, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x2,
    0x6c, 0xf8, 0x30, 0x0, 0x0, 0x0, 0x8, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf1, 0x0, 0x0,

    /* U+0025 "%" */
    0x1, 0x9e, 0xfe, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfb, 0x48, 0xff, 0x70,
    0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x10, 0xd, 0xfb, 0x0, 0x0, 0x0,
    0xe, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf0,
    0x0, 0xbf, 0xd0, 0x0, 0x0, 0x3, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xb, 0xfe, 0x0, 0xa, 0xfe,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xe0, 0x0, 0xaf, 0xf0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfe,
    0x0, 0xa, 0xff, 0x0, 0x0, 0x0, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0xaf,
    0xf0, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0xa, 0xff, 0x0, 0x0,
    0x8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xe0, 0x0, 0xaf, 0xf0, 0x0, 0x0, 0xcf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x0, 0xa,
    0xff, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xe0, 0x0, 0xaf, 0xf0, 0x0,
    0x5, 0xff, 0x10, 0x0, 0x46, 0x62, 0x0, 0xc,
    0xfe, 0x0, 0xa, 0xff, 0x0, 0x0, 0x9f, 0xd0,
    0x2, 0xdf, 0xff, 0xfa, 0x0, 0xcf, 0xe0, 0x0,
    0xaf, 0xf0, 0x0, 0xe, 0xf9, 0x0, 0xcf, 0xff,
    0xff, 0xf6, 0xc, 0xfe, 0x0, 0xa, 0xff, 0x0,
    0x2, 0xff, 0x40, 0x2f, 0xfb, 0x13, 0xff, 0xb0,
    0xbf, 0xe0, 0x0, 0xaf, 0xf0, 0x0, 0x6f, 0xf0,
    0x5, 0xff, 0x40, 0xa, 0xff, 0xb, 0xfe, 0x0,
    0xa, 0xff, 0x0, 0xa, 0xfc, 0x0, 0x7f, 0xf2,
    0x0, 0x8f, 0xf0, 0xaf, 0xe0, 0x0, 0xbf, 0xe0,
    0x0, 0xef, 0x80, 0x8, 0xff, 0x10, 0x7, 0xff,
    0x28, 0xff, 0x10, 0xd, 0xfc, 0x0, 0x3f, 0xf3,
    0x0, 0x9f, 0xf1, 0x0, 0x7f, 0xf2, 0x5f, 0xf8,
    0x15, 0xff, 0x90, 0x7, 0xff, 0x0, 0x9, 0xff,
    0x10, 0x7, 0xff, 0x20, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xcf, 0xb0, 0x0, 0x9f, 0xf1, 0x0, 0x7f,
    0xf2, 0x4, 0xff, 0xff, 0xf8, 0x0, 0xf, 0xf7,
    0x0, 0x9, 0xff, 0x10, 0x7, 0xff, 0x20, 0x0,
    0x46, 0x52, 0x0, 0x4, 0xff, 0x20, 0x0, 0x9f,
    0xf1, 0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xe0, 0x0, 0x9, 0xff, 0x10, 0x7,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x0, 0x0, 0x9f, 0xf1, 0x0, 0x7f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0, 0x9,
    0xff, 0x10, 0x7, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0x0, 0x0, 0x9f, 0xf1, 0x0,
    0x7f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfd,
    0x0, 0x0, 0x9, 0xff, 0x10, 0x7, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0,
    0x9f, 0xf1, 0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf4, 0x0, 0x0, 0x8, 0xff, 0x10,
    0x7, 0xff, 0x20, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x0, 0x0, 0x0, 0x8f, 0xf1, 0x0, 0x7f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0, 0x0, 0x0,
    0x6, 0xff, 0x20, 0x8, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x4f, 0xf6,
    0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x30, 0x0, 0x0, 0x0, 0xff, 0xe8, 0xaf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xdf, 0xfc, 0x40, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x26, 0x76, 0x30, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfc, 0x20, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x5, 0xff, 0xf9,
    0x47, 0xff, 0xf8, 0x0, 0xa, 0xff, 0x80, 0x0,
    0x4f, 0xfe, 0x0, 0xd, 0xff, 0x20, 0x0, 0xd,
    0xff, 0x10, 0xf, 0xff, 0x0, 0x0, 0xb, 0xff,
    0x30, 0xf, 0xff, 0x0, 0x0, 0xb, 0xff, 0x40,
    0xf, 0xff, 0x0, 0x0, 0xb, 0xff, 0x40, 0xf,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x8, 0xdd,
    0x30, 0x9, 0xff, 0x70, 0x0, 0xb, 0xff, 0x40,
    0x2, 0xff, 0xf9, 0x30, 0xb, 0xff, 0x40, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x3d, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x1, 0xef, 0xf9, 0x30,
    0xb, 0xff, 0x40, 0x7, 0xff, 0x70, 0x0, 0xb,
    0xff, 0x40, 0xc, 0xff, 0x0, 0x0, 0xb, 0xff,
    0x40, 0xf, 0xff, 0x0, 0x0, 0xb, 0xff, 0x40,
    0xf, 0xff, 0x0, 0x0, 0xb, 0xff, 0x40, 0xf,
    0xff, 0x0, 0x0, 0xb, 0xff, 0x40, 0xf, 0xff,
    0x0, 0x0, 0xb, 0xff, 0x40, 0xf, 0xff, 0x0,
    0x0, 0xb, 0xff, 0x40, 0xf, 0xff, 0x0, 0x0,
    0xb, 0xff, 0x40, 0xf, 0xff, 0x0, 0x0, 0xb,
    0xff, 0x40, 0xf, 0xff, 0x0, 0x0, 0xb, 0xff,
    0x40, 0xf, 0xff, 0x0, 0x0, 0xb, 0xff, 0x40,
    0xf, 0xff, 0x0, 0x0, 0xb, 0xff, 0x30, 0xd,
    0xff, 0x20, 0x0, 0xe, 0xff, 0x10, 0xa, 0xff,
    0x90, 0x0, 0x5f, 0xfe, 0x0, 0x4, 0xff, 0xfa,
    0x57, 0xff, 0xf8, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfc,
    0x20, 0x0, 0x0, 0x0, 0x26, 0x76, 0x30, 0x0,
    0x0,

    /* U+0027 "'" */
    0xcf, 0xf0, 0xbf, 0xe0, 0xaf, 0xd0, 0x9f, 0xc0,
    0x8f, 0xb0, 0x7f, 0xa0, 0x6f, 0x90, 0x5f, 0x80,
    0x3f, 0x60, 0x2f, 0x50, 0x3, 0x10,

    /* U+0028 "(" */
    0x0, 0x5, 0xff, 0x80, 0x0, 0xaf, 0xf3, 0x0,
    0xe, 0xfd, 0x0, 0x3, 0xff, 0x90, 0x0, 0x8f,
    0xf4, 0x0, 0xc, 0xff, 0x0, 0x0, 0xff, 0xc0,
    0x0, 0x3f, 0xf8, 0x0, 0x7, 0xff, 0x50, 0x0,
    0xaf, 0xf2, 0x0, 0xe, 0xff, 0x0, 0x0, 0xff,
    0xd0, 0x0, 0x3f, 0xfb, 0x0, 0x5, 0xff, 0x90,
    0x0, 0x7f, 0xf7, 0x0, 0x9, 0xff, 0x50, 0x0,
    0xbf, 0xf3, 0x0, 0xc, 0xff, 0x20, 0x0, 0xdf,
    0xf1, 0x0, 0xd, 0xff, 0x10, 0x0, 0xef, 0xf0,
    0x0, 0xe, 0xff, 0x0, 0x0, 0xff, 0xf0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0xef, 0xf0, 0x0, 0xe,
    0xff, 0x0, 0x0, 0xdf, 0xf1, 0x0, 0xc, 0xff,
    0x20, 0x0, 0xbf, 0xf2, 0x0, 0xa, 0xff, 0x40,
    0x0, 0x8f, 0xf6, 0x0, 0x6, 0xff, 0x80, 0x0,
    0x4f, 0xfa, 0x0, 0x2, 0xff, 0xc0, 0x0, 0xf,
    0xfe, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x9, 0xff,
    0x30, 0x0, 0x5f, 0xf6, 0x0, 0x2, 0xff, 0xa0,
    0x0, 0xe, 0xfe, 0x0, 0x0, 0xaf, 0xf2, 0x0,
    0x6, 0xff, 0x60, 0x0, 0x1f, 0xfb, 0x0, 0x0,
    0xcf, 0xf0, 0x0, 0x7, 0xff, 0x60, 0x0, 0x28,
    0x85,

    /* U+0029 ")" */
    0x8f, 0xf5, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x8, 0xff, 0x40, 0x0,
    0x4, 0xff, 0x80, 0x0, 0x0, 0xff, 0xd0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x8f, 0xf4, 0x0,
    0x0, 0x5f, 0xf7, 0x0, 0x0, 0x2f, 0xfb, 0x0,
    0x0, 0xf, 0xfe, 0x0, 0x0, 0xc, 0xff, 0x10,
    0x0, 0xa, 0xff, 0x30, 0x0, 0x8, 0xff, 0x50,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x4, 0xff, 0x90,
    0x0, 0x3, 0xff, 0xb0, 0x0, 0x1, 0xff, 0xc0,
    0x0, 0x0, 0xff, 0xd0, 0x0, 0x0, 0xff, 0xe0,
    0x0, 0x0, 0xff, 0xe0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0xef, 0xf0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0xff, 0xe0,
    0x0, 0x0, 0xff, 0xd0, 0x0, 0x1, 0xff, 0xd0,
    0x0, 0x2, 0xff, 0xc0, 0x0, 0x3, 0xff, 0xa0,
    0x0, 0x5, 0xff, 0x80, 0x0, 0x7, 0xff, 0x60,
    0x0, 0x9, 0xff, 0x40, 0x0, 0xb, 0xff, 0x20,
    0x0, 0xd, 0xff, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x3f, 0xf9, 0x0, 0x0, 0x6f, 0xf6, 0x0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0xef, 0xf0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x6, 0xff, 0x60, 0x0,
    0xb, 0xff, 0x10, 0x0, 0xf, 0xfc, 0x0, 0x0,
    0x5f, 0xf8, 0x0, 0x0, 0x58, 0x82, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x7, 0x90, 0x0, 0xa5, 0x0, 0x0, 0x5,
    0xff, 0x20, 0x4f, 0xf4, 0x0, 0x0, 0xb, 0xfa,
    0xc, 0xfa, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0xfe,
    0x10, 0x0, 0x2, 0x22, 0x8f, 0xff, 0x72, 0x22,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x5,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xef, 0x7f,
    0xe1, 0x0, 0x0, 0x0, 0xbf, 0xb0, 0xcf, 0xb0,
    0x0, 0x0, 0x4f, 0xf3, 0x4, 0xff, 0x40, 0x0,
    0x0, 0x49, 0x0, 0x9, 0x30, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x2, 0xaa, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0xa,
    0xaa, 0xaa, 0xab, 0xff, 0xca, 0xaa, 0xaa, 0xa3,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0, 0x0,
    0x0,

    /* U+002C "," */
    0x0, 0x2, 0x88, 0x70, 0x0, 0x9, 0xff, 0xb0,
    0x0, 0xe, 0xff, 0x30, 0x0, 0x4f, 0xfa, 0x0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0xff, 0xa0, 0x0,
    0x5, 0xff, 0x20, 0x0, 0xa, 0xf9, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x5f, 0x80, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x0,

    /* U+002D "-" */
    0x5b, 0xbb, 0xbb, 0x87, 0xff, 0xff, 0xfb, 0x7f,
    0xff, 0xff, 0xb0,

    /* U+002E "." */
    0x0, 0x0, 0x0, 0xbf, 0xc1, 0x5f, 0xff, 0x75,
    0xff, 0xf7, 0xa, 0xfb, 0x0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x2, 0x67, 0x74, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xd3, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x3f, 0xff, 0x94, 0x7e, 0xff, 0x90,
    0x9f, 0xf9, 0x0, 0x3, 0xff, 0xe0, 0xcf, 0xf2,
    0x0, 0x0, 0xdf, 0xf2, 0xef, 0xf0, 0x0, 0x0,
    0xbf, 0xf4, 0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf4,
    0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0,
    0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5,
    0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0,
    0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5,
    0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0,
    0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5,
    0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0,
    0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5,
    0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0,
    0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5,
    0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0,
    0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf4,
    0xef, 0xf0, 0x0, 0x0, 0xbf, 0xf4, 0xcf, 0xf2,
    0x0, 0x0, 0xdf, 0xf1, 0x9f, 0xf9, 0x0, 0x4,
    0xff, 0xe0, 0x3f, 0xff, 0xa5, 0x7f, 0xff, 0x90,
    0xa, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0xaf,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x2, 0x67, 0x74,
    0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x2, 0xaf, 0xfa, 0x3, 0xaf, 0xff, 0xfa,
    0x9f, 0xff, 0xff, 0xfa, 0xbf, 0xff, 0xcf, 0xfa,
    0xbd, 0x60, 0x5f, 0xfa, 0x20, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x0, 0x0, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa,

    /* U+0032 "2" */
    0x0, 0x0, 0x47, 0x75, 0x10, 0x0, 0x0, 0x5e,
    0xff, 0xff, 0xf7, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xd, 0xff, 0xd6, 0x5b, 0xff, 0xf1,
    0x3f, 0xff, 0x10, 0x0, 0xcf, 0xf6, 0x6f, 0xf9,
    0x0, 0x0, 0x6f, 0xf9, 0x8f, 0xf7, 0x0, 0x0,
    0x4f, 0xfb, 0x8f, 0xf6, 0x0, 0x0, 0x3f, 0xfc,
    0x9f, 0xf6, 0x0, 0x0, 0x3f, 0xfc, 0x9f, 0xf6,
    0x0, 0x0, 0x3f, 0xfc, 0x9f, 0xf6, 0x0, 0x0,
    0x3f, 0xfc, 0x9f, 0xf6, 0x0, 0x0, 0x3f, 0xfc,
    0x9f, 0xf6, 0x0, 0x0, 0x4f, 0xfc, 0x24, 0x41,
    0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x90, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0xaf, 0xfb, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xdf, 0xfc, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x6f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xe0,

    /* U+0033 "3" */
    0x0, 0x0, 0x47, 0x75, 0x10, 0x0, 0x0, 0x4e,
    0xff, 0xff, 0xf8, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xc, 0xff, 0xd6, 0x5b, 0xff, 0xf1,
    0x2f, 0xff, 0x10, 0x0, 0xbf, 0xf7, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x7f, 0xf7, 0x0, 0x0,
    0x3f, 0xfc, 0x8f, 0xf7, 0x0, 0x0, 0x2f, 0xfd,
    0x8f, 0xf7, 0x0, 0x0, 0x2f, 0xfd, 0x8f, 0xf7,
    0x0, 0x0, 0x2f, 0xfd, 0x8f, 0xf7, 0x0, 0x0,
    0x2f, 0xfd, 0x8f, 0xf7, 0x0, 0x0, 0x2f, 0xfd,
    0x7e, 0xe6, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf6,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0x2,
    0xab, 0xef, 0xfb, 0x10, 0x0, 0x3, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x1, 0x5b, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x8f, 0xf7, 0x0, 0x0,
    0x2f, 0xfd, 0x8f, 0xf7, 0x0, 0x0, 0x2f, 0xfd,
    0x8f, 0xf7, 0x0, 0x0, 0x2f, 0xfd, 0x8f, 0xf7,
    0x0, 0x0, 0x2f, 0xfd, 0x8f, 0xf7, 0x0, 0x0,
    0x2f, 0xfd, 0x8f, 0xf7, 0x0, 0x0, 0x2f, 0xfc,
    0x7f, 0xf8, 0x0, 0x0, 0x3f, 0xfc, 0x5f, 0xfa,
    0x0, 0x0, 0x5f, 0xfa, 0x1f, 0xff, 0x10, 0x0,
    0xcf, 0xf6, 0xc, 0xff, 0xe6, 0x5b, 0xff, 0xf1,
    0x3, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x3d,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x47, 0x75,
    0x10, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x3,
    0xfe, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x7, 0xfb,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xb, 0xf8, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0xf, 0xf5, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x3f, 0xe2, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x8f, 0xb2, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0xcf, 0x82, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xff, 0x52, 0xff, 0xd0, 0x0, 0x0, 0x4, 0xff,
    0x12, 0xff, 0xd0, 0x0, 0x0, 0x8, 0xfe, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0xd, 0xfb, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x1f, 0xf7, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x5f, 0xf4, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x9f, 0xf1, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0xdf, 0xd0, 0x2, 0xff, 0xd0, 0x0, 0x2, 0xff,
    0xa0, 0x2, 0xff, 0xd0, 0x0, 0x6, 0xff, 0x70,
    0x2, 0xff, 0xd0, 0x0, 0xa, 0xff, 0x30, 0x2,
    0xff, 0xd0, 0x0, 0xe, 0xff, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x3f, 0xfd, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x7f, 0xf9, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0,

    /* U+0035 "5" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x3b,
    0xfe, 0x91, 0x0, 0x2f, 0xfd, 0x5f, 0xff, 0xff,
    0xc0, 0x2, 0xff, 0xee, 0xfc, 0xef, 0xff, 0x70,
    0x2f, 0xff, 0xc1, 0x0, 0x9f, 0xfd, 0x2, 0xff,
    0xf4, 0x0, 0x0, 0xff, 0xf1, 0x2f, 0xff, 0x0,
    0x0, 0xd, 0xff, 0x21, 0xbb, 0xa0, 0x0, 0x0,
    0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x31,
    0xff, 0xc0, 0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd,
    0x0, 0x0, 0xc, 0xff, 0x32, 0xff, 0xd0, 0x0,
    0x0, 0xcf, 0xf3, 0x1f, 0xfd, 0x0, 0x0, 0xc,
    0xff, 0x20, 0xff, 0xe0, 0x0, 0x0, 0xdf, 0xf2,
    0xf, 0xff, 0x0, 0x0, 0xf, 0xff, 0x0, 0xbf,
    0xf6, 0x0, 0x7, 0xff, 0xd0, 0x6, 0xff, 0xf9,
    0x58, 0xff, 0xf7, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xfc, 0x20,
    0x0, 0x0, 0x2, 0x67, 0x63, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x2, 0x67, 0x64, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xd3, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x3f, 0xff, 0x94, 0x7e, 0xff, 0xb0,
    0x9f, 0xf9, 0x0, 0x3, 0xff, 0xf0, 0xcf, 0xf2,
    0x0, 0x0, 0xdf, 0xf3, 0xef, 0xf0, 0x0, 0x0,
    0xaf, 0xf4, 0xef, 0xf0, 0x0, 0x0, 0xaf, 0xf5,
    0xff, 0xf0, 0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0,
    0x0, 0x0, 0xaf, 0xf5, 0xff, 0xf0, 0x0, 0x0,
    0x7c, 0xc4, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf0, 0x6, 0xaa,
    0x60, 0x0, 0xff, 0xf1, 0xdf, 0xff, 0xfb, 0x0,
    0xff, 0xfb, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x60, 0x2b, 0xff, 0xe0, 0xff, 0xf8, 0x0, 0x0,
    0xff, 0xf3, 0xff, 0xf3, 0x0, 0x0, 0xbf, 0xf5,
    0xff, 0xf1, 0x0, 0x0, 0xaf, 0xf6, 0xff, 0xf0,
    0x0, 0x0, 0x9f, 0xf6, 0xff, 0xf0, 0x0, 0x0,
    0x9f, 0xf6, 0xff, 0xf0, 0x0, 0x0, 0x9f, 0xf6,
    0xff, 0xf0, 0x0, 0x0, 0x9f, 0xf6, 0xff, 0xf0,
    0x0, 0x0, 0x9f, 0xf6, 0xff, 0xf0, 0x0, 0x0,
    0x9f, 0xf6, 0xff, 0xf0, 0x0, 0x0, 0x9f, 0xf6,
    0xff, 0xf0, 0x0, 0x0, 0x9f, 0xf6, 0xff, 0xf0,
    0x0, 0x0, 0x9f, 0xf6, 0xff, 0xf0, 0x0, 0x0,
    0x9f, 0xf6, 0xef, 0xf0, 0x0, 0x0, 0x9f, 0xf5,
    0xdf, 0xf1, 0x0, 0x0, 0xaf, 0xf5, 0xcf, 0xf3,
    0x0, 0x0, 0xcf, 0xf3, 0x8f, 0xf9, 0x0, 0x4,
    0xff, 0xf0, 0x3f, 0xff, 0xa5, 0x7f, 0xff, 0xa0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x9f,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x1, 0x57, 0x74,
    0x0, 0x0,

    /* U+0037 "7" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x26, 0x76, 0x30, 0x0, 0x0, 0x1,
    0xaf, 0xff, 0xff, 0xc2, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x6f, 0xff, 0x84, 0x7f,
    0xff, 0x70, 0xc, 0xff, 0x60, 0x0, 0x5f, 0xfd,
    0x0, 0xff, 0xf0, 0x0, 0x0, 0xff, 0xf0, 0x1f,
    0xfe, 0x0, 0x0, 0xd, 0xff, 0x21, 0xff, 0xd0,
    0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0,
    0xc, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf,
    0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff, 0x31,
    0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf2, 0xf, 0xfd,
    0x0, 0x0, 0xc, 0xff, 0x10, 0xdf, 0xe0, 0x0,
    0x0, 0xdf, 0xe0, 0x7, 0xff, 0x10, 0x0, 0xf,
    0xf8, 0x0, 0xd, 0xfc, 0x30, 0x2b, 0xfe, 0x10,
    0x0, 0x2c, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x9f, 0xfd,
    0xac, 0xff, 0x90, 0x0, 0x4f, 0xf8, 0x0, 0x6,
    0xff, 0x30, 0xa, 0xff, 0x0, 0x0, 0xe, 0xfa,
    0x0, 0xff, 0xe0, 0x0, 0x0, 0xcf, 0xf0, 0xf,
    0xfd, 0x0, 0x0, 0xc, 0xff, 0x11, 0xff, 0xd0,
    0x0, 0x0, 0xcf, 0xf2, 0x2f, 0xfd, 0x0, 0x0,
    0xc, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf,
    0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff, 0x32,
    0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd,
    0x0, 0x0, 0xc, 0xff, 0x32, 0xff, 0xd0, 0x0,
    0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc,
    0xff, 0x31, 0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf2,
    0xf, 0xfe, 0x0, 0x0, 0xd, 0xff, 0x20, 0xef,
    0xf0, 0x0, 0x0, 0xff, 0xf0, 0xb, 0xff, 0x70,
    0x0, 0x6f, 0xfc, 0x0, 0x5f, 0xff, 0x95, 0x8f,
    0xff, 0x70, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x26, 0x76, 0x30, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x36, 0x76, 0x20, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xb1, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x8f, 0xff, 0x84, 0x8f,
    0xff, 0x60, 0xd, 0xff, 0x60, 0x0, 0x6f, 0xfb,
    0x1, 0xff, 0xf0, 0x0, 0x0, 0xff, 0xe0, 0x2f,
    0xfd, 0x0, 0x0, 0xe, 0xff, 0x3, 0xff, 0xc0,
    0x0, 0x0, 0xef, 0xf1, 0x3f, 0xfc, 0x0, 0x0,
    0xe, 0xff, 0x13, 0xff, 0xc0, 0x0, 0x0, 0xef,
    0xf1, 0x3f, 0xfc, 0x0, 0x0, 0xe, 0xff, 0x13,
    0xff, 0xc0, 0x0, 0x0, 0xef, 0xf1, 0x3f, 0xfc,
    0x0, 0x0, 0xe, 0xff, 0x13, 0xff, 0xc0, 0x0,
    0x0, 0xef, 0xf1, 0x3f, 0xfc, 0x0, 0x0, 0xe,
    0xff, 0x13, 0xff, 0xc0, 0x0, 0x0, 0xef, 0xf1,
    0x3f, 0xfc, 0x0, 0x0, 0xe, 0xff, 0x13, 0xff,
    0xc0, 0x0, 0x0, 0xef, 0xf1, 0x3f, 0xfd, 0x0,
    0x0, 0xf, 0xff, 0x11, 0xff, 0xf1, 0x0, 0x4,
    0xff, 0xf1, 0xe, 0xff, 0x90, 0x1, 0xcf, 0xff,
    0x10, 0x8f, 0xff, 0xec, 0xff, 0xef, 0xf1, 0x0,
    0xdf, 0xff, 0xff, 0x5d, 0xff, 0x10, 0x1, 0x9e,
    0xfb, 0x40, 0xdf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x10,
    0x44, 0x30, 0x0, 0x0, 0xdf, 0xf1, 0x2f, 0xfd,
    0x0, 0x0, 0xd, 0xff, 0x12, 0xff, 0xd0, 0x0,
    0x0, 0xdf, 0xf1, 0x2f, 0xfd, 0x0, 0x0, 0xd,
    0xff, 0x12, 0xff, 0xd0, 0x0, 0x0, 0xdf, 0xf1,
    0x1f, 0xfe, 0x0, 0x0, 0xd, 0xff, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xf0, 0xd, 0xff, 0x70,
    0x0, 0x7f, 0xfc, 0x0, 0x7f, 0xff, 0x85, 0x9f,
    0xff, 0x60, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x36, 0x76, 0x20, 0x0, 0x0,

    /* U+003A ":" */
    0x0, 0x0, 0x0, 0xbf, 0xc1, 0x5f, 0xff, 0x75,
    0xff, 0xf7, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x15,
    0xff, 0xf7, 0x5f, 0xff, 0x70, 0xaf, 0xb0,

    /* U+003B ";" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0,
    0x0, 0x6, 0xff, 0xf7, 0x0, 0x6, 0xff, 0xf6,
    0x0, 0x0, 0xaf, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x30,
    0x0, 0x7, 0xff, 0xd0, 0x0, 0xd, 0xff, 0x50,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x8f, 0xf5, 0x0,
    0x0, 0xdf, 0xd0, 0x0, 0x2, 0xff, 0x50, 0x0,
    0x8, 0xfd, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x3f, 0xc0, 0x0, 0x0, 0x8f, 0x40, 0x0, 0x0,
    0x67, 0x0, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2a, 0x0,

    /* U+003D "=" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x9, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa3,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xc9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x26, 0x76, 0x30, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xd2, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x5f, 0xff, 0x84, 0x7f,
    0xff, 0x80, 0xb, 0xff, 0x70, 0x0, 0x4f, 0xfe,
    0x0, 0xef, 0xf0, 0x0, 0x0, 0xef, 0xf1, 0xf,
    0xfe, 0x0, 0x0, 0xc, 0xff, 0x21, 0xff, 0xe0,
    0x0, 0x0, 0xbf, 0xf3, 0x1f, 0xfe, 0x0, 0x0,
    0xb, 0xff, 0x31, 0xff, 0xe0, 0x0, 0x0, 0xbf,
    0xf3, 0x1f, 0xfe, 0x0, 0x0, 0xb, 0xff, 0x31,
    0xff, 0xe0, 0x0, 0x0, 0xbf, 0xf3, 0xa, 0xa9,
    0x0, 0x0, 0xb, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x3, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x28, 0xff, 0xf8, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0xbf,
    0xff, 0xfc, 0x20, 0x0, 0x0, 0xa, 0xff, 0x83,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xed, 0x30, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8c,
    0xef, 0xfd, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xcb,
    0xce, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xa4, 0x0, 0x0,
    0x2, 0x7f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x70, 0x0, 0x0, 0x0, 0x6f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf6, 0x0, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x1, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x3d, 0xfc, 0x30, 0x99, 0x70, 0x0,
    0xc, 0xff, 0x10, 0x0, 0x7f, 0xf9, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xfe, 0x5f, 0xfa, 0x0, 0x0,
    0x7f, 0xf5, 0x0, 0xd, 0xff, 0x10, 0x0, 0x0,
    0x7, 0xff, 0xfd, 0xff, 0xff, 0x60, 0x0, 0x4,
    0xff, 0x80, 0x3, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0xdf, 0xf6, 0x2, 0xff, 0xf3, 0x0, 0x0, 0x1f,
    0xfb, 0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x1f,
    0xfd, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xc0, 0xc, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x80, 0x1, 0xff, 0xc0, 0x0, 0x0, 0xe, 0xfd,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x9f, 0xf4,
    0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xe0,
    0x4f, 0xf8, 0x0, 0x0, 0x0, 0xd, 0xff, 0x10,
    0x7, 0xff, 0x50, 0x0, 0x0, 0xe, 0xfe, 0x7,
    0xff, 0x50, 0x0, 0x0, 0x0, 0xff, 0xd0, 0x0,
    0xaf, 0xf2, 0x0, 0x0, 0x0, 0xff, 0xd0, 0x9f,
    0xf2, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0xb, 0xff,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x60, 0x1, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xff, 0xa0, 0xdf, 0xd0,
    0x0, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x5f, 0xf8, 0xe, 0xfc, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x0, 0x7, 0xff, 0x50,
    0x0, 0x0, 0x9, 0xff, 0x40, 0xff, 0xb0, 0x0,
    0x0, 0x1, 0xff, 0xc0, 0x0, 0xbf, 0xf1, 0x0,
    0x0, 0x0, 0xdf, 0xf1, 0xf, 0xfb, 0x0, 0x0,
    0x0, 0x4f, 0xf9, 0x0, 0xe, 0xfe, 0x0, 0x0,
    0x0, 0x1f, 0xfc, 0x0, 0xff, 0xa0, 0x0, 0x0,
    0x8, 0xff, 0x60, 0x2, 0xff, 0xb0, 0x0, 0x0,
    0x6, 0xff, 0x70, 0xf, 0xfb, 0x0, 0x0, 0x0,
    0xbf, 0xf3, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0,
    0xcf, 0xf1, 0x0, 0xef, 0xc0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x9, 0xff, 0x50, 0x0, 0x0, 0x4f,
    0xf9, 0x0, 0xd, 0xfd, 0x0, 0x0, 0x0, 0xff,
    0xe0, 0x0, 0xef, 0xf2, 0x0, 0x0, 0xc, 0xff,
    0x20, 0x0, 0xbf, 0xf0, 0x0, 0x0, 0x1f, 0xff,
    0x0, 0x8f, 0xff, 0x0, 0x0, 0x8, 0xff, 0x70,
    0x0, 0x8, 0xff, 0x20, 0x0, 0x1, 0xff, 0xfd,
    0xcf, 0x7f, 0xf3, 0x0, 0x7, 0xff, 0xa0, 0x0,
    0x0, 0x5f, 0xf5, 0x0, 0x0, 0xe, 0xff, 0xff,
    0x80, 0xbf, 0xe9, 0x8d, 0xff, 0xa0, 0x0, 0x0,
    0x2, 0xff, 0x90, 0x0, 0x0, 0x4e, 0xeb, 0x40,
    0x1, 0xdf, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x47, 0x64, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x4, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfa, 0x52, 0x10, 0x13, 0x7d, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x47, 0xab, 0xba, 0x85, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xd, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xef, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xce, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xac, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x8b, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x69, 0xfe, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x57, 0xff, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x35, 0xff, 0x20, 0x0, 0x0, 0x7, 0xff, 0x13,
    0xff, 0x40, 0x0, 0x0, 0x9, 0xff, 0x2, 0xff,
    0x60, 0x0, 0x0, 0xb, 0xfe, 0x0, 0xff, 0x80,
    0x0, 0x0, 0xd, 0xfc, 0x0, 0xef, 0xb0, 0x0,
    0x0, 0xf, 0xfa, 0x0, 0xcf, 0xd0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0xaf, 0xf0, 0x0, 0x0, 0x3f,
    0xf6, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x6f, 0xf5,
    0x0, 0x7f, 0xf3, 0x0, 0x0, 0x8f, 0xf3, 0x0,
    0x5f, 0xf5, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x3f,
    0xf8, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x1f, 0xfa,
    0x0, 0x0, 0xef, 0xe0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0xe, 0xfe, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x7, 0xff, 0xdc,
    0xcc, 0xcd, 0xff, 0x50, 0x9, 0xff, 0x30, 0x0,
    0x5, 0xff, 0x70, 0xb, 0xff, 0x10, 0x0, 0x3,
    0xff, 0x90, 0xd, 0xff, 0x0, 0x0, 0x2, 0xff,
    0xb0, 0xf, 0xfe, 0x0, 0x0, 0x0, 0xff, 0xd0,
    0x1f, 0xfc, 0x0, 0x0, 0x0, 0xef, 0xf0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0xcf, 0xf1, 0x6f, 0xf8,
    0x0, 0x0, 0x0, 0xaf, 0xf4, 0x8f, 0xf6, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0xaf, 0xf5, 0x0, 0x0,
    0x0, 0x6f, 0xf8,

    /* U+0042 "B" */
    0x2f, 0xff, 0xff, 0xfd, 0xa4, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x2, 0xff, 0xd0, 0x0, 0x4e,
    0xff, 0x80, 0x2f, 0xfd, 0x0, 0x0, 0x4f, 0xfb,
    0x2, 0xff, 0xd0, 0x0, 0x1, 0xff, 0xe0, 0x2f,
    0xfd, 0x0, 0x0, 0xf, 0xfe, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0xff, 0xf0, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2, 0xff, 0xd0, 0x0, 0x0, 0xff,
    0xf0, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0xff, 0xf0, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xfe, 0x2, 0xff, 0xd0, 0x0,
    0x1, 0xff, 0xc0, 0x2f, 0xfd, 0x0, 0x0, 0x4f,
    0xf8, 0x2, 0xff, 0xd0, 0x0, 0x4e, 0xff, 0x20,
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x2, 0xff,
    0xff, 0xff, 0xfe, 0x30, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x2, 0xff, 0xd0, 0x0, 0x4d,
    0xff, 0x60, 0x2f, 0xfd, 0x0, 0x0, 0x1f, 0xfd,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0xbf, 0xf2, 0x2f,
    0xfd, 0x0, 0x0, 0x9, 0xff, 0x52, 0xff, 0xd0,
    0x0, 0x0, 0x8f, 0xf6, 0x2f, 0xfd, 0x0, 0x0,
    0x8, 0xff, 0x72, 0xff, 0xd0, 0x0, 0x0, 0x8f,
    0xf7, 0x2f, 0xfd, 0x0, 0x0, 0x8, 0xff, 0x72,
    0xff, 0xd0, 0x0, 0x0, 0x8f, 0xf7, 0x2f, 0xfd,
    0x0, 0x0, 0x8, 0xff, 0x72, 0xff, 0xd0, 0x0,
    0x0, 0x8f, 0xf6, 0x2f, 0xfd, 0x0, 0x0, 0x8,
    0xff, 0x62, 0xff, 0xd0, 0x0, 0x0, 0xaf, 0xf4,
    0x2f, 0xfd, 0x0, 0x0, 0xd, 0xff, 0x22, 0xff,
    0xd0, 0x0, 0x29, 0xff, 0xe0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x2f, 0xff, 0xff, 0xfe, 0xc6, 0x0,
    0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x36, 0x76, 0x20, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xb1, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x8f, 0xff, 0x74, 0x9f,
    0xff, 0x40, 0xd, 0xff, 0x50, 0x0, 0x8f, 0xfa,
    0x0, 0xff, 0xe0, 0x0, 0x1, 0xff, 0xd0, 0x2f,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0x3, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xf0, 0x4f, 0xfb, 0x0, 0x0,
    0xe, 0xff, 0x4, 0xff, 0xb0, 0x0, 0x0, 0xef,
    0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14,
    0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb,
    0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0,
    0x0, 0xde, 0xe0, 0x4f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xb0, 0x0, 0x0, 0x66,
    0x60, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14,
    0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb,
    0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0,
    0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe,
    0xff, 0x3, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xf0,
    0x2f, 0xfc, 0x0, 0x0, 0xf, 0xff, 0x0, 0xff,
    0xe0, 0x0, 0x1, 0xff, 0xd0, 0xd, 0xff, 0x50,
    0x0, 0x8f, 0xfa, 0x0, 0x8f, 0xff, 0x85, 0x9f,
    0xff, 0x40, 0x1, 0xef, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x2, 0xdf, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x46, 0x76, 0x20, 0x0, 0x0,

    /* U+0044 "D" */
    0x2f, 0xff, 0xff, 0xfd, 0xa3, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x2f, 0xfd, 0x0, 0x4, 0xef, 0xf8,
    0x2f, 0xfd, 0x0, 0x0, 0x5f, 0xfb, 0x2f, 0xfd,
    0x0, 0x0, 0x1f, 0xfd, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xfe, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xfe, 0x2f, 0xfd, 0x0, 0x0, 0x1f, 0xfd,
    0x2f, 0xfd, 0x0, 0x0, 0x5f, 0xfb, 0x2f, 0xfd,
    0x0, 0x4, 0xef, 0xf8, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x2f, 0xff, 0xff, 0xfd, 0xa3, 0x0,

    /* U+0045 "E" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xa2, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xa2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x52, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xa2, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xa0,

    /* U+0046 "F" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xa2, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xa2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x52, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x36, 0x77, 0x40, 0x0, 0x0, 0x2,
    0xcf, 0xff, 0xff, 0xe4, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x8f, 0xff, 0x84, 0x6e,
    0xff, 0xc0, 0xd, 0xff, 0x50, 0x0, 0x1f, 0xff,
    0x21, 0xff, 0xe0, 0x0, 0x0, 0x9f, 0xf5, 0x2f,
    0xfc, 0x0, 0x0, 0x7, 0xff, 0x73, 0xff, 0xb0,
    0x0, 0x0, 0x6f, 0xf8, 0x4f, 0xfb, 0x0, 0x0,
    0x6, 0xff, 0x84, 0xff, 0xb0, 0x0, 0x0, 0x6f,
    0xf8, 0x4f, 0xfb, 0x0, 0x0, 0x6, 0xff, 0x84,
    0xff, 0xb0, 0x0, 0x0, 0x6f, 0xf8, 0x4f, 0xfb,
    0x0, 0x0, 0x6, 0xff, 0x84, 0xff, 0xb0, 0x0,
    0x0, 0x6f, 0xf8, 0x4f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfb, 0x0, 0xaf, 0xff, 0xff,
    0x84, 0xff, 0xb0, 0xa, 0xff, 0xff, 0xf8, 0x4f,
    0xfb, 0x0, 0x9e, 0xef, 0xff, 0x84, 0xff, 0xb0,
    0x0, 0x0, 0x6f, 0xf8, 0x4f, 0xfb, 0x0, 0x0,
    0x6, 0xff, 0x84, 0xff, 0xb0, 0x0, 0x0, 0x6f,
    0xf8, 0x4f, 0xfb, 0x0, 0x0, 0x6, 0xff, 0x84,
    0xff, 0xb0, 0x0, 0x0, 0x6f, 0xf8, 0x4f, 0xfb,
    0x0, 0x0, 0x6, 0xff, 0x84, 0xff, 0xb0, 0x0,
    0x0, 0x6f, 0xf8, 0x4f, 0xfb, 0x0, 0x0, 0x6,
    0xff, 0x83, 0xff, 0xb0, 0x0, 0x0, 0x6f, 0xf8,
    0x2f, 0xfc, 0x0, 0x0, 0x7, 0xff, 0x70, 0xff,
    0xe0, 0x0, 0x0, 0x9f, 0xf5, 0xd, 0xff, 0x50,
    0x0, 0x1f, 0xff, 0x20, 0x8f, 0xff, 0x85, 0x7e,
    0xff, 0xd0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xe5, 0x0, 0x0,
    0x0, 0x36, 0x77, 0x40, 0x0, 0x0,

    /* U+0048 "H" */
    0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff, 0x32, 0xff,
    0xd0, 0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0,
    0x0, 0xc, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0,
    0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff,
    0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf3, 0x2f,
    0xfd, 0x0, 0x0, 0xc, 0xff, 0x32, 0xff, 0xd0,
    0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0,
    0xc, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf,
    0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff, 0x32,
    0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd,
    0x0, 0x0, 0xc, 0xff, 0x32, 0xff, 0xd0, 0x0,
    0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc,
    0xff, 0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf3,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x32, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0,
    0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff,
    0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf3, 0x2f,
    0xfd, 0x0, 0x0, 0xc, 0xff, 0x32, 0xff, 0xd0,
    0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0,
    0xc, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf,
    0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff, 0x32,
    0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd,
    0x0, 0x0, 0xc, 0xff, 0x32, 0xff, 0xd0, 0x0,
    0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc,
    0xff, 0x32, 0xff, 0xd0, 0x0, 0x0, 0xcf, 0xf3,
    0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff, 0x32, 0xff,
    0xd0, 0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfd, 0x0,
    0x0, 0xc, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0,
    0xcf, 0xf3, 0x2f, 0xfd, 0x0, 0x0, 0xc, 0xff,
    0x30,

    /* U+0049 "I" */
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd, 0x2f, 0xfd,
    0x2f, 0xfd,

    /* U+004A "J" */
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x2f, 0xfd, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x2f,
    0xfd, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x2f, 0xfd, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x2f,
    0xfd, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x2f, 0xfd, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x3f, 0xfb, 0x0, 0x7, 0xff, 0x90, 0x3,
    0xef, 0xf4, 0xd, 0xff, 0xfd, 0x0, 0xff, 0xff,
    0x30, 0xf, 0xe9, 0x10, 0x0,

    /* U+004B "K" */
    0x2f, 0xfd, 0x0, 0x0, 0x6, 0xff, 0x90, 0x2f,
    0xfd, 0x0, 0x0, 0xb, 0xff, 0x40, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x2f, 0xfd, 0x0,
    0x0, 0x5f, 0xfa, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0xaf, 0xf5, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0xff,
    0xf1, 0x0, 0x2f, 0xfd, 0x0, 0x4, 0xff, 0xc0,
    0x0, 0x2f, 0xfd, 0x0, 0x9, 0xff, 0x70, 0x0,
    0x2f, 0xfd, 0x0, 0xe, 0xff, 0x20, 0x0, 0x2f,
    0xfd, 0x0, 0x3f, 0xfd, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x8f, 0xf8, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0xdf, 0xf3, 0x0, 0x0, 0x2f, 0xfd, 0x3, 0xff,
    0xe0, 0x0, 0x0, 0x2f, 0xfd, 0x8, 0xff, 0xa0,
    0x0, 0x0, 0x2f, 0xfd, 0xd, 0xff, 0x50, 0x0,
    0x0, 0x2f, 0xfd, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x7f, 0xfb, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x2f, 0xfe,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0xbf,
    0xf8, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x5f, 0xfe,
    0x0, 0x0, 0x0, 0x2f, 0xfd, 0xf, 0xff, 0x40,
    0x0, 0x0, 0x2f, 0xfd, 0xa, 0xff, 0x90, 0x0,
    0x0, 0x2f, 0xfd, 0x4, 0xff, 0xe0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0xef, 0xf4, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x8f, 0xfa, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x2f, 0xff, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0xc, 0xff, 0x50, 0x0, 0x2f, 0xfd, 0x0, 0x7,
    0xff, 0xb0, 0x0, 0x2f, 0xfd, 0x0, 0x1, 0xff,
    0xf1, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0xbf, 0xf6,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x5f, 0xfb, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x10, 0x2f,
    0xfd, 0x0, 0x0, 0x9, 0xff, 0x60, 0x2f, 0xfd,
    0x0, 0x0, 0x3, 0xff, 0xc0, 0x2f, 0xfd, 0x0,
    0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x8f, 0xf7,

    /* U+004C "L" */
    0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x2, 0xff, 0xd0, 0x0, 0x0, 0x2f, 0xfd, 0x0,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xc2, 0xff, 0xff,
    0xff, 0xfc, 0x2f, 0xff, 0xff, 0xff, 0xc0,

    /* U+004D "M" */
    0x2f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0x22, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf2, 0x2f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0x22, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xf2, 0x2f, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x22,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf2, 0x2f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0x22, 0xff, 0xdf, 0xf9, 0x0, 0x0,
    0x8, 0xff, 0xef, 0xf2, 0x2f, 0xfb, 0xff, 0xb0,
    0x0, 0x0, 0xbf, 0xfb, 0xff, 0x22, 0xff, 0xbe,
    0xfe, 0x0, 0x0, 0xd, 0xfe, 0xaf, 0xf2, 0x2f,
    0xfb, 0xbf, 0xf0, 0x0, 0x0, 0xff, 0xba, 0xff,
    0x22, 0xff, 0xb9, 0xff, 0x30, 0x0, 0x2f, 0xf8,
    0xaf, 0xf2, 0x2f, 0xfb, 0x7f, 0xf5, 0x0, 0x4,
    0xff, 0x6a, 0xff, 0x22, 0xff, 0xb4, 0xff, 0x80,
    0x0, 0x7f, 0xf3, 0xaf, 0xf2, 0x2f, 0xfb, 0x2f,
    0xfa, 0x0, 0x9, 0xff, 0xb, 0xff, 0x22, 0xff,
    0xb0, 0xff, 0xd0, 0x0, 0xcf, 0xd0, 0xbf, 0xf2,
    0x2f, 0xfb, 0xd, 0xff, 0x0, 0xe, 0xfb, 0xb,
    0xff, 0x22, 0xff, 0xb0, 0xaf, 0xf2, 0x1, 0xff,
    0x80, 0xbf, 0xf2, 0x2f, 0xfb, 0x8, 0xff, 0x40,
    0x3f, 0xf5, 0xb, 0xff, 0x22, 0xff, 0xc0, 0x6f,
    0xf6, 0x6, 0xff, 0x20, 0xbf, 0xf2, 0x2f, 0xfc,
    0x3, 0xff, 0x90, 0x8f, 0xf0, 0xb, 0xff, 0x22,
    0xff, 0xc0, 0x1f, 0xfb, 0xb, 0xfd, 0x0, 0xbf,
    0xf2, 0x2f, 0xfc, 0x0, 0xef, 0xe0, 0xdf, 0xa0,
    0xb, 0xff, 0x22, 0xff, 0xc0, 0xc, 0xff, 0x1f,
    0xf8, 0x0, 0xbf, 0xf2, 0x2f, 0xfc, 0x0, 0xaf,
    0xf5, 0xff, 0x50, 0xc, 0xff, 0x22, 0xff, 0xc0,
    0x7, 0xff, 0xaf, 0xf2, 0x0, 0xcf, 0xf2, 0x2f,
    0xfc, 0x0, 0x5f, 0xff, 0xff, 0x0, 0xc, 0xff,
    0x22, 0xff, 0xc0, 0x2, 0xff, 0xff, 0xd0, 0x0,
    0xcf, 0xf2, 0x2f, 0xfc, 0x0, 0xf, 0xff, 0xfa,
    0x0, 0xc, 0xff, 0x22, 0xff, 0xc0, 0x0, 0xdf,
    0xff, 0x70, 0x0, 0xcf, 0xf2, 0x2f, 0xfd, 0x0,
    0xb, 0xff, 0xf4, 0x0, 0xc, 0xff, 0x22, 0xff,
    0xd0, 0x0, 0x9f, 0xff, 0x20, 0x0, 0xcf, 0xf2,
    0x2f, 0xfd, 0x0, 0x6, 0xff, 0xf0, 0x0, 0xc,
    0xff, 0x22, 0xff, 0xd0, 0x0, 0x4f, 0xfc, 0x0,
    0x0, 0xcf, 0xf2, 0x2f, 0xfd, 0x0, 0x1, 0xff,
    0x90, 0x0, 0xd, 0xff, 0x22, 0xff, 0xd0, 0x0,
    0xf, 0xf7, 0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0xd, 0xff, 0x20,

    /* U+004E "N" */
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0xbf, 0xf4, 0x2f,
    0xff, 0x10, 0x0, 0x0, 0xbf, 0xf4, 0x2f, 0xff,
    0x50, 0x0, 0x0, 0xbf, 0xf4, 0x2f, 0xff, 0x90,
    0x0, 0x0, 0xbf, 0xf4, 0x2f, 0xff, 0xd0, 0x0,
    0x0, 0xbf, 0xf4, 0x2f, 0xff, 0xf1, 0x0, 0x0,
    0xbf, 0xf4, 0x2f, 0xff, 0xf5, 0x0, 0x0, 0xbf,
    0xf4, 0x2f, 0xff, 0xf9, 0x0, 0x0, 0xbf, 0xf4,
    0x2f, 0xff, 0xfd, 0x0, 0x0, 0xbf, 0xf4, 0x2f,
    0xff, 0xff, 0x10, 0x0, 0xaf, 0xf4, 0x2f, 0xff,
    0xff, 0x60, 0x0, 0xaf, 0xf4, 0x2f, 0xfe, 0xff,
    0xa0, 0x0, 0xaf, 0xf4, 0x2f, 0xfc, 0xef, 0xe0,
    0x0, 0xaf, 0xf4, 0x2f, 0xfc, 0xaf, 0xf2, 0x0,
    0xaf, 0xf4, 0x2f, 0xfc, 0x6f, 0xf6, 0x0, 0xaf,
    0xf4, 0x2f, 0xfc, 0x2f, 0xfa, 0x0, 0xaf, 0xf4,
    0x2f, 0xfc, 0xe, 0xfe, 0x0, 0xaf, 0xf4, 0x2f,
    0xfc, 0xa, 0xff, 0x20, 0xaf, 0xf4, 0x2f, 0xfc,
    0x6, 0xff, 0x60, 0xaf, 0xf4, 0x2f, 0xfc, 0x2,
    0xff, 0xa0, 0xaf, 0xf4, 0x2f, 0xfc, 0x0, 0xef,
    0xe0, 0xaf, 0xf4, 0x2f, 0xfc, 0x0, 0xaf, 0xf2,
    0xaf, 0xf4, 0x2f, 0xfc, 0x0, 0x6f, 0xf6, 0xaf,
    0xf4, 0x2f, 0xfc, 0x0, 0x2f, 0xfa, 0xaf, 0xf4,
    0x2f, 0xfc, 0x0, 0xe, 0xfe, 0xaf, 0xf4, 0x2f,
    0xfd, 0x0, 0xa, 0xff, 0xcf, 0xf4, 0x2f, 0xfd,
    0x0, 0x6, 0xff, 0xff, 0xf4, 0x2f, 0xfd, 0x0,
    0x2, 0xff, 0xff, 0xf4, 0x2f, 0xfd, 0x0, 0x0,
    0xef, 0xff, 0xf4, 0x2f, 0xfd, 0x0, 0x0, 0xaf,
    0xff, 0xf4, 0x2f, 0xfd, 0x0, 0x0, 0x6f, 0xff,
    0xf4, 0x2f, 0xfd, 0x0, 0x0, 0x2f, 0xff, 0xf4,
    0x2f, 0xfd, 0x0, 0x0, 0xe, 0xff, 0xf4, 0x2f,
    0xfd, 0x0, 0x0, 0xa, 0xff, 0xf4, 0x2f, 0xfd,
    0x0, 0x0, 0x6, 0xff, 0xf4, 0x2f, 0xfd, 0x0,
    0x0, 0x2, 0xff, 0xf4, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0xef, 0xf4,

    /* U+004F "O" */
    0x0, 0x0, 0x36, 0x76, 0x20, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xb1, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x8f, 0xff, 0x74, 0x9f,
    0xff, 0x40, 0xd, 0xff, 0x50, 0x0, 0x8f, 0xfa,
    0x0, 0xff, 0xe0, 0x0, 0x1, 0xff, 0xd0, 0x2f,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0x3, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xf0, 0x4f, 0xfb, 0x0, 0x0,
    0xe, 0xff, 0x4, 0xff, 0xb0, 0x0, 0x0, 0xef,
    0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14,
    0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb,
    0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0,
    0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe,
    0xff, 0x14, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1,
    0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14, 0xff,
    0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0,
    0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0, 0x0,
    0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff,
    0x14, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f,
    0xfb, 0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0,
    0xe, 0xff, 0x14, 0xff, 0xb0, 0x0, 0x0, 0xef,
    0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14,
    0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb,
    0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0,
    0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe,
    0xff, 0x3, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xf0,
    0x2f, 0xfc, 0x0, 0x0, 0xf, 0xff, 0x0, 0xff,
    0xe0, 0x0, 0x1, 0xff, 0xd0, 0xd, 0xff, 0x50,
    0x0, 0x8f, 0xfa, 0x0, 0x8f, 0xff, 0x85, 0x9f,
    0xff, 0x40, 0x1, 0xef, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x2, 0xdf, 0xff, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x46, 0x76, 0x20, 0x0, 0x0,

    /* U+0050 "P" */
    0x2f, 0xff, 0xff, 0xfd, 0xa3, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x2f, 0xfd, 0x0, 0x4, 0xef, 0xf8,
    0x2f, 0xfd, 0x0, 0x0, 0x5f, 0xfb, 0x2f, 0xfd,
    0x0, 0x0, 0x1f, 0xfd, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xfe, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff,
    0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xfe, 0x2f, 0xfd, 0x0, 0x0, 0x1f, 0xfd,
    0x2f, 0xfd, 0x0, 0x0, 0x5f, 0xfb, 0x2f, 0xfd,
    0x0, 0x4, 0xef, 0xf8, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x2f, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x2f, 0xff, 0xff, 0xfe, 0xa4, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x36, 0x76, 0x20, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xb1, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x8f, 0xff, 0x74, 0x9f,
    0xff, 0x40, 0xd, 0xff, 0x50, 0x0, 0x8f, 0xfa,
    0x0, 0xff, 0xe0, 0x0, 0x1, 0xff, 0xd0, 0x2f,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0x3, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xf0, 0x4f, 0xfb, 0x0, 0x0,
    0xe, 0xff, 0x4, 0xff, 0xb0, 0x0, 0x0, 0xef,
    0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14,
    0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb,
    0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0,
    0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe,
    0xff, 0x14, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1,
    0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14, 0xff,
    0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0,
    0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0, 0x0,
    0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff,
    0x14, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f,
    0xfb, 0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0,
    0xe, 0xff, 0x14, 0xff, 0xb0, 0x0, 0x0, 0xef,
    0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe, 0xff, 0x14,
    0xff, 0xb0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xfb,
    0x0, 0x0, 0xe, 0xff, 0x14, 0xff, 0xb0, 0x0,
    0x0, 0xef, 0xf1, 0x4f, 0xfb, 0x0, 0x0, 0xe,
    0xff, 0x3, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xf0,
    0x2f, 0xfc, 0x0, 0x0, 0xf, 0xff, 0x0, 0xff,
    0xe0, 0x0, 0x1, 0xff, 0xd0, 0xd, 0xff, 0x50,
    0x0, 0x8f, 0xf9, 0x0, 0x8f, 0xff, 0x75, 0x9f,
    0xff, 0x40, 0x1, 0xef, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x2, 0xdf, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x46, 0x7d, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8e, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+0052 "R" */
    0x2f, 0xff, 0xff, 0xfd, 0xa4, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x2, 0xff, 0xd0, 0x0, 0x4e,
    0xff, 0x80, 0x2f, 0xfd, 0x0, 0x0, 0x5f, 0xfb,
    0x2, 0xff, 0xd0, 0x0, 0x1, 0xff, 0xd0, 0x2f,
    0xfd, 0x0, 0x0, 0xf, 0xfe, 0x2, 0xff, 0xd0,
    0x0, 0x0, 0xff, 0xf0, 0x2f, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0x2, 0xff, 0xd0, 0x0, 0x0, 0xff,
    0xf0, 0x2f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0x2,
    0xff, 0xd0, 0x0, 0x0, 0xff, 0xf0, 0x2f, 0xfd,
    0x0, 0x0, 0xf, 0xff, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0xff, 0xf0, 0x2f, 0xfd, 0x0, 0x0, 0xf,
    0xfe, 0x2, 0xff, 0xd0, 0x0, 0x1, 0xff, 0xd0,
    0x2f, 0xfd, 0x0, 0x0, 0x5f, 0xfb, 0x2, 0xff,
    0xd0, 0x1, 0x4e, 0xff, 0x70, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xc3, 0x0,
    0x2, 0xff, 0xd0, 0x3f, 0xfa, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0xff, 0xe0, 0x0, 0x2, 0xff, 0xd0,
    0xb, 0xff, 0x20, 0x0, 0x2f, 0xfd, 0x0, 0x6f,
    0xf7, 0x0, 0x2, 0xff, 0xd0, 0x2, 0xff, 0xb0,
    0x0, 0x2f, 0xfd, 0x0, 0xe, 0xff, 0x0, 0x2,
    0xff, 0xd0, 0x0, 0x9f, 0xf4, 0x0, 0x2f, 0xfd,
    0x0, 0x5, 0xff, 0x90, 0x2, 0xff, 0xd0, 0x0,
    0x1f, 0xfd, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0xcf,
    0xf1, 0x2, 0xff, 0xd0, 0x0, 0x8, 0xff, 0x60,
    0x2f, 0xfd, 0x0, 0x0, 0x4f, 0xfa, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0xff, 0xe0, 0x2f, 0xfd, 0x0,
    0x0, 0xb, 0xff, 0x32, 0xff, 0xd0, 0x0, 0x0,
    0x7f, 0xf8, 0x2f, 0xfd, 0x0, 0x0, 0x2, 0xff,
    0xc0,

    /* U+0053 "S" */
    0x0, 0x0, 0x47, 0x75, 0x10, 0x0, 0x0, 0x5e,
    0xff, 0xff, 0xf7, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0x60, 0xe, 0xff, 0xd5, 0x5c, 0xff, 0xf0,
    0x3f, 0xfe, 0x0, 0x0, 0xdf, 0xf5, 0x7f, 0xf8,
    0x0, 0x0, 0x6f, 0xf8, 0x8f, 0xf6, 0x0, 0x0,
    0x4f, 0xfa, 0x9f, 0xf5, 0x0, 0x0, 0x4f, 0xfb,
    0x9f, 0xf5, 0x0, 0x0, 0x4f, 0xfb, 0x9f, 0xf5,
    0x0, 0x0, 0x4f, 0xfb, 0x9f, 0xf5, 0x0, 0x0,
    0x4f, 0xfb, 0x9f, 0xf5, 0x0, 0x0, 0x4f, 0xfb,
    0x9f, 0xf6, 0x0, 0x0, 0x4f, 0xfb, 0x7f, 0xf9,
    0x0, 0x0, 0x3f, 0xfb, 0x4f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf6, 0x46, 0x62,
    0x0, 0x0, 0x9f, 0xf9, 0x9f, 0xf5, 0x0, 0x0,
    0x5f, 0xfb, 0x9f, 0xf5, 0x0, 0x0, 0x4f, 0xfb,
    0x9f, 0xf5, 0x0, 0x0, 0x4f, 0xfb, 0x9f, 0xf5,
    0x0, 0x0, 0x4f, 0xfb, 0x9f, 0xf5, 0x0, 0x0,
    0x4f, 0xfb, 0x9f, 0xf5, 0x0, 0x0, 0x4f, 0xfb,
    0x7f, 0xf6, 0x0, 0x0, 0x4f, 0xf9, 0x5f, 0xf8,
    0x0, 0x0, 0x7f, 0xf7, 0x2f, 0xfe, 0x0, 0x0,
    0xdf, 0xf4, 0xd, 0xff, 0xd6, 0x6c, 0xff, 0xe0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x6f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x1, 0x57, 0x75,
    0x10, 0x0,

    /* U+0054 "T" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x0, 0x0,
    0x0,

    /* U+0055 "U" */
    0x2f, 0xfd, 0x0, 0x0, 0xd, 0xff, 0x22, 0xff,
    0xd0, 0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd, 0x0,
    0x0, 0xd, 0xff, 0x22, 0xff, 0xd0, 0x0, 0x0,
    0xdf, 0xf2, 0x2f, 0xfd, 0x0, 0x0, 0xd, 0xff,
    0x22, 0xff, 0xd0, 0x0, 0x0, 0xdf, 0xf2, 0x2f,
    0xfd, 0x0, 0x0, 0xd, 0xff, 0x22, 0xff, 0xd0,
    0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd, 0x0, 0x0,
    0xd, 0xff, 0x22, 0xff, 0xd0, 0x0, 0x0, 0xdf,
    0xf2, 0x2f, 0xfd, 0x0, 0x0, 0xd, 0xff, 0x22,
    0xff, 0xd0, 0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd,
    0x0, 0x0, 0xd, 0xff, 0x22, 0xff, 0xd0, 0x0,
    0x0, 0xdf, 0xf2, 0x2f, 0xfd, 0x0, 0x0, 0xd,
    0xff, 0x22, 0xff, 0xd0, 0x0, 0x0, 0xdf, 0xf2,
    0x2f, 0xfd, 0x0, 0x0, 0xd, 0xff, 0x22, 0xff,
    0xd0, 0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd, 0x0,
    0x0, 0xd, 0xff, 0x22, 0xff, 0xd0, 0x0, 0x0,
    0xdf, 0xf2, 0x2f, 0xfd, 0x0, 0x0, 0xd, 0xff,
    0x22, 0xff, 0xd0, 0x0, 0x0, 0xdf, 0xf2, 0x2f,
    0xfd, 0x0, 0x0, 0xd, 0xff, 0x22, 0xff, 0xd0,
    0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd, 0x0, 0x0,
    0xd, 0xff, 0x22, 0xff, 0xd0, 0x0, 0x0, 0xdf,
    0xf2, 0x2f, 0xfd, 0x0, 0x0, 0xd, 0xff, 0x22,
    0xff, 0xd0, 0x0, 0x0, 0xdf, 0xf2, 0x2f, 0xfd,
    0x0, 0x0, 0xd, 0xff, 0x22, 0xff, 0xd0, 0x0,
    0x0, 0xdf, 0xf2, 0x2f, 0xfd, 0x0, 0x0, 0xd,
    0xff, 0x11, 0xff, 0xd0, 0x0, 0x0, 0xef, 0xf1,
    0xf, 0xff, 0x0, 0x0, 0xf, 0xfe, 0x0, 0xcf,
    0xf6, 0x0, 0x7, 0xff, 0xb0, 0x6, 0xff, 0xf8,
    0x58, 0xff, 0xf6, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x3, 0x67, 0x63, 0x0, 0x0,

    /* U+0056 "V" */
    0x8f, 0xf7, 0x0, 0x0, 0x4, 0xff, 0xb6, 0xff,
    0x80, 0x0, 0x0, 0x5f, 0xf9, 0x4f, 0xfa, 0x0,
    0x0, 0x7, 0xff, 0x72, 0xff, 0xc0, 0x0, 0x0,
    0x8f, 0xf5, 0xf, 0xfd, 0x0, 0x0, 0xa, 0xff,
    0x30, 0xef, 0xf0, 0x0, 0x0, 0xcf, 0xf1, 0xc,
    0xff, 0x0, 0x0, 0xd, 0xff, 0x0, 0xaf, 0xf2,
    0x0, 0x0, 0xff, 0xd0, 0x8, 0xff, 0x30, 0x0,
    0xf, 0xfb, 0x0, 0x6f, 0xf5, 0x0, 0x2, 0xff,
    0xa0, 0x4, 0xff, 0x70, 0x0, 0x3f, 0xf8, 0x0,
    0x2f, 0xf8, 0x0, 0x5, 0xff, 0x60, 0x0, 0xff,
    0xa0, 0x0, 0x7f, 0xf4, 0x0, 0xe, 0xfb, 0x0,
    0x8, 0xff, 0x20, 0x0, 0xcf, 0xd0, 0x0, 0xaf,
    0xf0, 0x0, 0xa, 0xfe, 0x0, 0xb, 0xfe, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0xdf, 0xc0, 0x0, 0x7,
    0xff, 0x20, 0xf, 0xfa, 0x0, 0x0, 0x5f, 0xf3,
    0x0, 0xff, 0x80, 0x0, 0x3, 0xff, 0x50, 0x2f,
    0xf6, 0x0, 0x0, 0x1f, 0xf6, 0x3, 0xff, 0x40,
    0x0, 0x0, 0xff, 0x80, 0x5f, 0xf2, 0x0, 0x0,
    0xd, 0xfa, 0x7, 0xff, 0x0, 0x0, 0x0, 0xbf,
    0xb0, 0x8f, 0xe0, 0x0, 0x0, 0x9, 0xfd, 0xa,
    0xfc, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0xbf, 0xb0,
    0x0, 0x0, 0x5, 0xff, 0xd, 0xf9, 0x0, 0x0,
    0x0, 0x3f, 0xf1, 0xef, 0x70, 0x0, 0x0, 0x1,
    0xff, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0xf, 0xf7,
    0xff, 0x30, 0x0, 0x0, 0x0, 0xdf, 0xaf, 0xf1,
    0x0, 0x0, 0x0, 0xb, 0xfd, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x50, 0x0,
    0x0,

    /* U+0057 "W" */
    0x6f, 0xf8, 0x0, 0x0, 0xa, 0xff, 0x80, 0x0,
    0x0, 0xaf, 0xf4, 0x4f, 0xfa, 0x0, 0x0, 0xc,
    0xff, 0xa0, 0x0, 0x0, 0xcf, 0xf3, 0x2f, 0xfb,
    0x0, 0x0, 0xd, 0xff, 0xb0, 0x0, 0x0, 0xdf,
    0xf1, 0x1f, 0xfd, 0x0, 0x0, 0xf, 0xff, 0xd0,
    0x0, 0x0, 0xef, 0xf0, 0xf, 0xfe, 0x0, 0x0,
    0x1f, 0xff, 0xf0, 0x0, 0x0, 0xff, 0xd0, 0xd,
    0xff, 0x0, 0x0, 0x2f, 0xff, 0xf0, 0x0, 0x1,
    0xff, 0xc0, 0xb, 0xff, 0x10, 0x0, 0x4f, 0xff,
    0xf2, 0x0, 0x3, 0xff, 0xa0, 0xa, 0xff, 0x20,
    0x0, 0x5f, 0xff, 0xf4, 0x0, 0x4, 0xff, 0x80,
    0x8, 0xff, 0x40, 0x0, 0x7f, 0xff, 0xf5, 0x0,
    0x5, 0xff, 0x70, 0x6, 0xff, 0x50, 0x0, 0x9f,
    0xef, 0xf7, 0x0, 0x7, 0xff, 0x50, 0x5, 0xff,
    0x70, 0x0, 0xaf, 0xce, 0xf9, 0x0, 0x8, 0xff,
    0x30, 0x3, 0xff, 0x80, 0x0, 0xcf, 0xbc, 0xfa,
    0x0, 0xa, 0xff, 0x10, 0x1, 0xff, 0x90, 0x0,
    0xef, 0x9a, 0xfc, 0x0, 0xb, 0xff, 0x0, 0x0,
    0xff, 0xb0, 0x0, 0xff, 0x79, 0xfe, 0x0, 0xc,
    0xfe, 0x0, 0x0, 0xef, 0xc0, 0x1, 0xff, 0x57,
    0xff, 0x0, 0xe, 0xfc, 0x0, 0x0, 0xcf, 0xe0,
    0x3, 0xff, 0x45, 0xff, 0x10, 0xf, 0xfb, 0x0,
    0x0, 0xaf, 0xf0, 0x4, 0xff, 0x23, 0xff, 0x30,
    0x1f, 0xf9, 0x0, 0x0, 0x8f, 0xf1, 0x6, 0xff,
    0x2, 0xff, 0x50, 0x2f, 0xf7, 0x0, 0x0, 0x7f,
    0xf2, 0x8, 0xfe, 0x0, 0xff, 0x60, 0x3f, 0xf5,
    0x0, 0x0, 0x5f, 0xf3, 0x9, 0xfd, 0x0, 0xef,
    0x80, 0x5f, 0xf4, 0x0, 0x0, 0x3f, 0xf5, 0xb,
    0xfb, 0x0, 0xcf, 0xa0, 0x6f, 0xf2, 0x0, 0x0,
    0x1f, 0xf6, 0xd, 0xf9, 0x0, 0xbf, 0xb0, 0x8f,
    0xf0, 0x0, 0x0, 0xf, 0xf8, 0xe, 0xf7, 0x0,
    0x9f, 0xd0, 0x9f, 0xe0, 0x0, 0x0, 0xe, 0xf9,
    0xf, 0xf6, 0x0, 0x7f, 0xf0, 0xaf, 0xd0, 0x0,
    0x0, 0xc, 0xfb, 0x2f, 0xf4, 0x0, 0x5f, 0xf0,
    0xcf, 0xb0, 0x0, 0x0, 0xa, 0xfc, 0x3f, 0xf2,
    0x0, 0x4f, 0xf2, 0xdf, 0x90, 0x0, 0x0, 0x9,
    0xfd, 0x5f, 0xf1, 0x0, 0x2f, 0xf4, 0xff, 0x80,
    0x0, 0x0, 0x7, 0xff, 0x7f, 0xf0, 0x0, 0xf,
    0xf6, 0xff, 0x60, 0x0, 0x0, 0x5, 0xff, 0x9f,
    0xd0, 0x0, 0xe, 0xf9, 0xff, 0x40, 0x0, 0x0,
    0x3, 0xff, 0xcf, 0xb0, 0x0, 0xc, 0xfc, 0xff,
    0x20, 0x0, 0x0, 0x2, 0xff, 0xff, 0xa0, 0x0,
    0xb, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x80, 0x0, 0x9, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x60, 0x0, 0x7, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x40,
    0x0, 0x5, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0x30, 0x0, 0x4, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0x10, 0x0, 0x2,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf6, 0x0, 0x0,

    /* U+0058 "X" */
    0xb, 0xff, 0x40, 0x0, 0x0, 0x0, 0xff, 0xf1,
    0x6, 0xff, 0x90, 0x0, 0x0, 0x3, 0xff, 0xc0,
    0x1, 0xff, 0xe0, 0x0, 0x0, 0x8, 0xff, 0x70,
    0x0, 0xcf, 0xf3, 0x0, 0x0, 0xd, 0xff, 0x30,
    0x0, 0x6f, 0xf8, 0x0, 0x0, 0x1f, 0xfe, 0x0,
    0x0, 0x1f, 0xfd, 0x0, 0x0, 0x6f, 0xf9, 0x0,
    0x0, 0xc, 0xff, 0x10, 0x0, 0xaf, 0xf5, 0x0,
    0x0, 0x7, 0xff, 0x60, 0x0, 0xff, 0xf0, 0x0,
    0x0, 0x1, 0xff, 0xb0, 0x4, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xcf, 0xf1, 0x8, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x7f, 0xf5, 0xd, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x2f, 0xfa, 0x1f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x6f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xef, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xcf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfe, 0x5f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf9, 0x1f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf4, 0xc, 0xff, 0x10, 0x0,
    0x0, 0x0, 0xcf, 0xf0, 0x8, 0xff, 0x50, 0x0,
    0x0, 0x1, 0xff, 0xa0, 0x4, 0xff, 0xa0, 0x0,
    0x0, 0x6, 0xff, 0x60, 0x0, 0xff, 0xe0, 0x0,
    0x0, 0xc, 0xff, 0x10, 0x0, 0xbf, 0xf3, 0x0,
    0x0, 0x1f, 0xfc, 0x0, 0x0, 0x7f, 0xf7, 0x0,
    0x0, 0x6f, 0xf7, 0x0, 0x0, 0x2f, 0xfc, 0x0,
    0x0, 0xbf, 0xf2, 0x0, 0x0, 0xe, 0xff, 0x0,
    0x1, 0xff, 0xd0, 0x0, 0x0, 0xa, 0xff, 0x50,
    0x6, 0xff, 0x80, 0x0, 0x0, 0x5, 0xff, 0x90,
    0xb, 0xff, 0x40, 0x0, 0x0, 0x1, 0xff, 0xd0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf2,

    /* U+0059 "Y" */
    0x7f, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0x3f,
    0xfc, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xbf, 0xf3, 0xc, 0xff, 0x20,
    0x0, 0x0, 0xff, 0xf0, 0x9, 0xff, 0x50, 0x0,
    0x2, 0xff, 0xc0, 0x5, 0xff, 0x90, 0x0, 0x5,
    0xff, 0x80, 0x1, 0xff, 0xc0, 0x0, 0x8, 0xff,
    0x50, 0x0, 0xef, 0xf0, 0x0, 0xc, 0xff, 0x10,
    0x0, 0xaf, 0xf2, 0x0, 0xf, 0xfd, 0x0, 0x0,
    0x6f, 0xf6, 0x0, 0x2f, 0xfa, 0x0, 0x0, 0x3f,
    0xf9, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0xf, 0xfc,
    0x0, 0x9f, 0xf3, 0x0, 0x0, 0xc, 0xff, 0x0,
    0xcf, 0xf0, 0x0, 0x0, 0x8, 0xff, 0x30, 0xff,
    0xb0, 0x0, 0x0, 0x4, 0xff, 0x63, 0xff, 0x80,
    0x0, 0x0, 0x1, 0xff, 0x96, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xdf, 0xc9, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x90, 0x0, 0x0,

    /* U+005A "Z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xc, 0xff, 0x40,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0xc, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x4f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x4f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x3f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+005B "[" */
    0xdf, 0xff, 0xff, 0xbd, 0xff, 0xff, 0xfb, 0xdf,
    0xf8, 0x88, 0x6d, 0xff, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0, 0x0,
    0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0, 0x0, 0xdf,
    0xf0, 0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0, 0x0,
    0xdf, 0xf0, 0x0, 0xd, 0xff, 0x0, 0x0, 0xdf,
    0xf0, 0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0xd, 0xff,
    0xff, 0xfb, 0xdf, 0xff, 0xff, 0xb8, 0xaa, 0xaa,
    0xa7,

    /* U+005C "\\" */
    0x3f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xb0,

    /* U+005D "]" */
    0x5f, 0xff, 0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf2,
    0x38, 0x88, 0xef, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0xbf, 0xf2,
    0x0, 0x0, 0xbf, 0xf2, 0x5f, 0xff, 0xff, 0xf2,
    0x5f, 0xff, 0xff, 0xf2, 0x3b, 0xbb, 0xbb, 0xb1,

    /* U+005E "^" */
    0x0, 0x0, 0x4, 0x88, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfe, 0xff, 0x10, 0x0, 0x0, 0x0, 0x1f,
    0xfb, 0x9f, 0xf5, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x64, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0xe, 0xff, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0x0,
    0x9f, 0xf5, 0x0, 0x0, 0x7, 0xff, 0x60, 0x4,
    0xff, 0xb0, 0x0, 0x0, 0xcf, 0xf1, 0x0, 0xe,
    0xff, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0, 0xaf,
    0xf5, 0x0, 0x7, 0xff, 0x70, 0x0, 0x4, 0xff,
    0xb0, 0x0, 0xcf, 0xf2, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0, 0xaf, 0xf5,
    0x7, 0xff, 0x70, 0x0, 0x0, 0x5, 0xff, 0xb0,
    0xcf, 0xf2, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0,

    /* U+005F "_" */
    0x13, 0x33, 0x33, 0x33, 0x33, 0x30, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf3,

    /* U+0060 "`" */
    0x2e, 0xee, 0x20, 0x0, 0x8f, 0xf9, 0x0, 0x0,
    0xef, 0xf1, 0x0, 0x5, 0xff, 0x70, 0x0, 0xb,
    0xfd, 0x0,

    /* U+0061 "a" */
    0x0, 0x7d, 0xff, 0xc6, 0x0, 0xa, 0xff, 0xff,
    0xff, 0x70, 0x2f, 0xff, 0xcd, 0xff, 0xf0, 0x6f,
    0xf9, 0x0, 0xcf, 0xf4, 0x9f, 0xf4, 0x0, 0x7f,
    0xf6, 0xaf, 0xf3, 0x0, 0x6f, 0xf7, 0xbf, 0xf2,
    0x0, 0x5f, 0xf8, 0xbf, 0xf2, 0x0, 0x5f, 0xf8,
    0xbf, 0xf2, 0x0, 0x5f, 0xf8, 0x12, 0x20, 0x0,
    0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0,
    0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f,
    0xf8, 0x3, 0xcf, 0xd4, 0x5f, 0xf8, 0xe, 0xff,
    0xff, 0x8f, 0xf8, 0x4f, 0xff, 0xef, 0xff, 0xf8,
    0x7f, 0xfa, 0x1, 0xef, 0xf8, 0x9f, 0xf4, 0x0,
    0x9f, 0xf8, 0xaf, 0xf3, 0x0, 0x6f, 0xf8, 0xbf,
    0xf2, 0x0, 0x5f, 0xf8, 0xbf, 0xf2, 0x0, 0x5f,
    0xf8, 0xbf, 0xf2, 0x0, 0x5f, 0xf8, 0xaf, 0xf2,
    0x0, 0x6f, 0xf8, 0x9f, 0xf3, 0x0, 0x7f, 0xf8,
    0x7f, 0xf6, 0x0, 0xcf, 0xf8, 0x4f, 0xfe, 0x59,
    0xff, 0xf8, 0xe, 0xff, 0xff, 0x5f, 0xf8, 0x3,
    0xdf, 0xd5, 0xd, 0xf8,

    /* U+0062 "b" */
    0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0,
    0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f,
    0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0,
    0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x1b,
    0xfd, 0x50, 0x5f, 0xf8, 0xcf, 0xff, 0xf1, 0x5f,
    0xfe, 0xeb, 0xff, 0xf6, 0x5f, 0xff, 0x20, 0x7f,
    0xfa, 0x5f, 0xfb, 0x0, 0x2f, 0xfb, 0x5f, 0xf9,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f,
    0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f,
    0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xfa, 0x0, 0x1f, 0xfb, 0x5f, 0xfe, 0x0,
    0x4f, 0xfa, 0x5f, 0xff, 0xa4, 0xdf, 0xf7, 0x5f,
    0xf6, 0xff, 0xff, 0xf1, 0x5f, 0xf0, 0x4c, 0xfd,
    0x50,

    /* U+0063 "c" */
    0x0, 0x5c, 0xff, 0xd7, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xa0, 0x1f, 0xff, 0xdc, 0xff, 0xf3, 0x5f,
    0xfc, 0x0, 0x9f, 0xf8, 0x7f, 0xf7, 0x0, 0x4f,
    0xfa, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb,
    0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0,
    0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x16, 0x64, 0x8f,
    0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0,
    0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf5,
    0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x8f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0,
    0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f,
    0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f,
    0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x7f, 0xf7, 0x0, 0x4f, 0xfa,
    0x5f, 0xfc, 0x0, 0x9f, 0xf8, 0x1f, 0xff, 0xdc,
    0xff, 0xf3, 0x7, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x5c, 0xff, 0xd7, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0,
    0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0, 0x0, 0x3f,
    0xfa, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x3f, 0xfa, 0x0, 0x0, 0x0, 0x3f, 0xfa,
    0x0, 0x0, 0x0, 0x3f, 0xfa, 0x2, 0xcf, 0xd4,
    0x3f, 0xfa, 0xc, 0xff, 0xff, 0x6f, 0xfa, 0x2f,
    0xff, 0xcd, 0xef, 0xfa, 0x5f, 0xfb, 0x0, 0xcf,
    0xfa, 0x7f, 0xf6, 0x0, 0x6f, 0xfa, 0x8f, 0xf5,
    0x0, 0x4f, 0xfa, 0x8f, 0xf5, 0x0, 0x3f, 0xfa,
    0x8f, 0xf5, 0x0, 0x3f, 0xfa, 0x9f, 0xf5, 0x0,
    0x3f, 0xfa, 0x9f, 0xf5, 0x0, 0x3f, 0xfa, 0x9f,
    0xf5, 0x0, 0x3f, 0xfa, 0x9f, 0xf5, 0x0, 0x3f,
    0xfa, 0x9f, 0xf5, 0x0, 0x3f, 0xfa, 0x9f, 0xf5,
    0x0, 0x3f, 0xfa, 0x9f, 0xf5, 0x0, 0x3f, 0xfa,
    0x9f, 0xf5, 0x0, 0x3f, 0xfa, 0x9f, 0xf5, 0x0,
    0x3f, 0xfa, 0x9f, 0xf5, 0x0, 0x3f, 0xfa, 0x9f,
    0xf5, 0x0, 0x3f, 0xfa, 0x9f, 0xf5, 0x0, 0x3f,
    0xfa, 0x8f, 0xf5, 0x0, 0x3f, 0xfa, 0x8f, 0xf5,
    0x0, 0x3f, 0xfa, 0x8f, 0xf5, 0x0, 0x4f, 0xfa,
    0x7f, 0xf6, 0x0, 0x6f, 0xfa, 0x5f, 0xfb, 0x0,
    0xcf, 0xfa, 0x2f, 0xff, 0xbd, 0xef, 0xfa, 0xc,
    0xff, 0xff, 0x4d, 0xfa, 0x2, 0xcf, 0xd6, 0xb,
    0xfa,

    /* U+0065 "e" */
    0x0, 0x5c, 0xff, 0xd7, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xa0, 0x1f, 0xff, 0xdc, 0xff, 0xf3, 0x5f,
    0xfc, 0x0, 0x9f, 0xf8, 0x7f, 0xf7, 0x0, 0x4f,
    0xfa, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb,
    0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0,
    0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f,
    0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xfb, 0x99, 0xaf,
    0xfb, 0x8f, 0xff, 0xff, 0xff, 0xfb, 0x8f, 0xff,
    0xff, 0xff, 0xfb, 0x8f, 0xf5, 0x0, 0x0, 0x0,
    0x8f, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xf5, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0x0, 0x16, 0x64, 0x8f,
    0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f,
    0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x7f, 0xf7, 0x0, 0x4f, 0xfa,
    0x5f, 0xfc, 0x0, 0x9f, 0xf8, 0x1f, 0xff, 0xdc,
    0xff, 0xf3, 0x7, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x5c, 0xff, 0xd7, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x6d, 0xfe, 0xa0, 0x0, 0x5f, 0xff,
    0xfc, 0x0, 0xc, 0xff, 0xea, 0x80, 0x0, 0xff,
    0xe0, 0x0, 0x0, 0x2f, 0xfa, 0x0, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0x4, 0xff, 0xa0, 0x0, 0x0, 0x4f, 0xfa, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xfc, 0xef, 0xff, 0xff,
    0xff, 0xc9, 0xab, 0xff, 0xda, 0xa7, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x0, 0x0,
    0x4f, 0xfa, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x0,
    0x0, 0x4f, 0xfa, 0x0, 0x0, 0x4, 0xff, 0xa0,
    0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x4, 0xff,
    0xa0, 0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x4,
    0xff, 0xa0, 0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0,
    0x4, 0xff, 0xa0, 0x0, 0x0, 0x4f, 0xfa, 0x0,
    0x0, 0x4, 0xff, 0xa0, 0x0, 0x0, 0x4f, 0xfa,
    0x0, 0x0, 0x4, 0xff, 0xa0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x0, 0x0,
    0x4f, 0xfa, 0x0, 0x0, 0x4, 0xff, 0xa0, 0x0,
    0x0, 0x4f, 0xfa, 0x0, 0x0, 0x4, 0xff, 0xa0,
    0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x4, 0xff,
    0xa0, 0x0, 0x0, 0x4f, 0xfa, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x7, 0xdf, 0xff, 0xff, 0xff, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xf9, 0x9f,
    0xfe, 0x77, 0x4, 0xff, 0xa0, 0x8, 0xff, 0x20,
    0x6, 0xff, 0x60, 0x5, 0xff, 0x70, 0x8, 0xff,
    0x50, 0x3, 0xff, 0x90, 0x8, 0xff, 0x50, 0x3,
    0xff, 0xa0, 0x9, 0xff, 0x50, 0x3, 0xff, 0xa0,
    0x9, 0xff, 0x50, 0x3, 0xff, 0xa0, 0x9, 0xff,
    0x50, 0x3, 0xff, 0xa0, 0x9, 0xff, 0x50, 0x3,
    0xff, 0xa0, 0x8, 0xff, 0x50, 0x3, 0xff, 0xa0,
    0x8, 0xff, 0x50, 0x4, 0xff, 0x90, 0x6, 0xff,
    0x70, 0x6, 0xff, 0x80, 0x3, 0xff, 0xe2, 0x1d,
    0xff, 0x40, 0x0, 0xbf, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x5f,
    0xd9, 0x96, 0x10, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfd, 0x80, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xfa, 0x0, 0x2, 0xff, 0xfb, 0xcf, 0xff, 0x20,
    0x9, 0xff, 0x50, 0xa, 0xff, 0x60, 0xd, 0xff,
    0x0, 0x5, 0xff, 0x90, 0xf, 0xfe, 0x0, 0x3,
    0xff, 0xa0, 0xf, 0xfd, 0x0, 0x3, 0xff, 0xa0,
    0xf, 0xfd, 0x0, 0x3, 0xff, 0xa0, 0xf, 0xfd,
    0x0, 0x3, 0xff, 0xa0, 0xf, 0xfd, 0x0, 0x3,
    0xff, 0xa0, 0xf, 0xfd, 0x0, 0x3, 0xff, 0xa0,
    0xf, 0xfe, 0x0, 0x4, 0xff, 0x90, 0xd, 0xff,
    0x0, 0x6, 0xff, 0x80, 0xa, 0xff, 0x60, 0xc,
    0xff, 0x50, 0x5, 0xff, 0xfc, 0xdf, 0xff, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x8,
    0xdf, 0xfc, 0x50, 0x0,

    /* U+0068 "h" */
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0,
    0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x6f,
    0xf7, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0,
    0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x1b,
    0xfd, 0x40, 0x6f, 0xf7, 0xdf, 0xff, 0xe1, 0x6f,
    0xfe, 0xeb, 0xff, 0xf5, 0x6f, 0xff, 0x10, 0x7f,
    0xf8, 0x6f, 0xfa, 0x0, 0x3f, 0xfa, 0x6f, 0xf8,
    0x0, 0x1f, 0xfc, 0x6f, 0xf7, 0x0, 0x1f, 0xfc,
    0x6f, 0xf7, 0x0, 0x1f, 0xfc, 0x6f, 0xf7, 0x0,
    0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f, 0xfd, 0x6f,
    0xf7, 0x0, 0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f,
    0xfd, 0x6f, 0xf7, 0x0, 0x1f, 0xfd, 0x6f, 0xf7,
    0x0, 0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f, 0xfd,
    0x6f, 0xf7, 0x0, 0x1f, 0xfd, 0x6f, 0xf7, 0x0,
    0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f, 0xfd, 0x6f,
    0xf7, 0x0, 0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f,
    0xfd, 0x6f, 0xf7, 0x0, 0x1f, 0xfd, 0x6f, 0xf7,
    0x0, 0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f, 0xfd,
    0x6f, 0xf7, 0x0, 0x1f, 0xfd, 0x6f, 0xf7, 0x0,
    0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f, 0xfd, 0x6f,
    0xf7, 0x0, 0x1f, 0xfd, 0x6f, 0xf7, 0x0, 0x1f,
    0xfd,

    /* U+0069 "i" */
    0x0, 0x44, 0x0, 0xa, 0xff, 0xa0, 0xf, 0xff,
    0xf1, 0xf, 0xff, 0xf0, 0x4, 0xee, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x70, 0x6, 0xff, 0x70, 0x6, 0xff,
    0x70, 0x6, 0xff, 0x70, 0x6, 0xff, 0x70, 0x6,
    0xff, 0x70, 0x6, 0xff, 0x70, 0x6, 0xff, 0x70,
    0x6, 0xff, 0x70, 0x6, 0xff, 0x70, 0x6, 0xff,
    0x70, 0x6, 0xff, 0x70, 0x6, 0xff, 0x70, 0x6,
    0xff, 0x70, 0x6, 0xff, 0x70, 0x6, 0xff, 0x70,
    0x6, 0xff, 0x70, 0x6, 0xff, 0x70, 0x6, 0xff,
    0x70, 0x6, 0xff, 0x70, 0x6, 0xff, 0x70, 0x6,
    0xff, 0x70, 0x6, 0xff, 0x70, 0x6, 0xff, 0x70,
    0x6, 0xff, 0x70, 0x6, 0xff, 0x70, 0x6, 0xff,
    0x70, 0x6, 0xff, 0x70,

    /* U+006A "j" */
    0x0, 0x0, 0x44, 0x0, 0x0, 0xa, 0xff, 0xa0,
    0x0, 0xf, 0xff, 0xf1, 0x0, 0xf, 0xff, 0xf0,
    0x0, 0x4, 0xee, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x70, 0x0, 0x6, 0xff, 0x70,
    0x0, 0x6, 0xff, 0x60, 0x0, 0x8, 0xff, 0x50,
    0x0, 0xd, 0xff, 0x20, 0x6b, 0xef, 0xfe, 0x0,
    0x9f, 0xff, 0xf7, 0x0, 0x7e, 0xfd, 0x70, 0x0,

    /* U+006B "k" */
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0xcf, 0xf3, 0x6f, 0xf7, 0x0, 0x2,
    0xff, 0xd0, 0x6f, 0xf7, 0x0, 0x7, 0xff, 0x80,
    0x6f, 0xf7, 0x0, 0xd, 0xff, 0x20, 0x6f, 0xf7,
    0x0, 0x3f, 0xfd, 0x0, 0x6f, 0xf7, 0x0, 0x8f,
    0xf7, 0x0, 0x6f, 0xf7, 0x0, 0xef, 0xf2, 0x0,
    0x6f, 0xf7, 0x4, 0xff, 0xc0, 0x0, 0x6f, 0xf7,
    0xa, 0xff, 0x60, 0x0, 0x6f, 0xf7, 0xf, 0xff,
    0x10, 0x0, 0x6f, 0xf7, 0x5f, 0xfb, 0x0, 0x0,
    0x6f, 0xf7, 0xbf, 0xf6, 0x0, 0x0, 0x6f, 0xf8,
    0xff, 0xf1, 0x0, 0x0, 0x6f, 0xfd, 0xff, 0xc0,
    0x0, 0x0, 0x6f, 0xfa, 0xff, 0xf0, 0x0, 0x0,
    0x6f, 0xf7, 0xdf, 0xf5, 0x0, 0x0, 0x6f, 0xf7,
    0x8f, 0xfa, 0x0, 0x0, 0x6f, 0xf7, 0x2f, 0xfe,
    0x0, 0x0, 0x6f, 0xf7, 0xd, 0xff, 0x40, 0x0,
    0x6f, 0xf7, 0x7, 0xff, 0x90, 0x0, 0x6f, 0xf7,
    0x2, 0xff, 0xe0, 0x0, 0x6f, 0xf7, 0x0, 0xcf,
    0xf3, 0x0, 0x6f, 0xf7, 0x0, 0x7f, 0xf9, 0x0,
    0x6f, 0xf7, 0x0, 0x2f, 0xfe, 0x0, 0x6f, 0xf7,
    0x0, 0xc, 0xff, 0x30, 0x6f, 0xf7, 0x0, 0x7,
    0xff, 0x80, 0x6f, 0xf7, 0x0, 0x1, 0xff, 0xd0,
    0x6f, 0xf7, 0x0, 0x0, 0xcf, 0xf2,

    /* U+006C "l" */
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8, 0x5f, 0xf8,
    0x5f, 0xf8,

    /* U+006D "m" */
    0x5f, 0xf0, 0x2c, 0xfd, 0x30, 0x9, 0xee, 0x80,
    0x5, 0xff, 0x3e, 0xff, 0xfe, 0x19, 0xff, 0xff,
    0x60, 0x5f, 0xfd, 0xeb, 0xff, 0xf9, 0xfb, 0xef,
    0xfc, 0x5, 0xff, 0xf2, 0x7, 0xff, 0xf6, 0x1,
    0xff, 0xf0, 0x5f, 0xfb, 0x0, 0x2f, 0xff, 0x0,
    0xd, 0xff, 0x15, 0xff, 0x90, 0x0, 0xff, 0xe0,
    0x0, 0xbf, 0xf2, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x0, 0xb, 0xff, 0x25, 0xff, 0x80, 0x0, 0xff,
    0xd0, 0x0, 0xbf, 0xf2, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x0, 0xb, 0xff, 0x35, 0xff, 0x80, 0x0,
    0xff, 0xd0, 0x0, 0xbf, 0xf3, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x0, 0xb, 0xff, 0x35, 0xff, 0x80,
    0x0, 0xff, 0xd0, 0x0, 0xbf, 0xf3, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x0, 0xb, 0xff, 0x35, 0xff,
    0x80, 0x0, 0xff, 0xd0, 0x0, 0xbf, 0xf3, 0x5f,
    0xf8, 0x0, 0xf, 0xfd, 0x0, 0xb, 0xff, 0x35,
    0xff, 0x80, 0x0, 0xff, 0xd0, 0x0, 0xbf, 0xf3,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x0, 0xb, 0xff,
    0x35, 0xff, 0x80, 0x0, 0xff, 0xd0, 0x0, 0xbf,
    0xf3, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x0, 0xb,
    0xff, 0x35, 0xff, 0x80, 0x0, 0xff, 0xd0, 0x0,
    0xbf, 0xf3, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x0,
    0xb, 0xff, 0x35, 0xff, 0x80, 0x0, 0xff, 0xd0,
    0x0, 0xbf, 0xf3, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x0, 0xb, 0xff, 0x35, 0xff, 0x80, 0x0, 0xff,
    0xd0, 0x0, 0xbf, 0xf3, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x0, 0xb, 0xff, 0x35, 0xff, 0x80, 0x0,
    0xff, 0xd0, 0x0, 0xbf, 0xf3, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x0, 0xb, 0xff, 0x35, 0xff, 0x80,
    0x0, 0xff, 0xd0, 0x0, 0xbf, 0xf3,

    /* U+006E "n" */
    0x5f, 0xf0, 0x2c, 0xfd, 0x50, 0x5f, 0xf3, 0xef,
    0xff, 0xf1, 0x5f, 0xfd, 0xeb, 0xff, 0xf6, 0x5f,
    0xff, 0x20, 0x7f, 0xf9, 0x5f, 0xfb, 0x0, 0x2f,
    0xfb, 0x5f, 0xf9, 0x0, 0xf, 0xfc, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f,
    0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f,
    0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f,
    0xf8, 0x0, 0xf, 0xfd,

    /* U+006F "o" */
    0x0, 0x5c, 0xff, 0xd7, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xa0, 0x1f, 0xff, 0xdc, 0xff, 0xf3, 0x5f,
    0xfc, 0x0, 0x9f, 0xf8, 0x7f, 0xf7, 0x0, 0x4f,
    0xfa, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb,
    0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0,
    0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f,
    0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f,
    0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb,
    0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0,
    0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f,
    0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f,
    0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x7f, 0xf7, 0x0, 0x4f, 0xfa,
    0x5f, 0xfc, 0x0, 0x9f, 0xf8, 0x1f, 0xff, 0xdc,
    0xff, 0xf3, 0x7, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x5c, 0xff, 0xd7, 0x0,

    /* U+0070 "p" */
    0x5f, 0xf0, 0x2c, 0xfd, 0x50, 0x5f, 0xf3, 0xef,
    0xff, 0xf1, 0x5f, 0xfd, 0xeb, 0xff, 0xf6, 0x5f,
    0xff, 0x20, 0x7f, 0xfa, 0x5f, 0xfb, 0x0, 0x2f,
    0xfb, 0x5f, 0xf9, 0x0, 0xf, 0xfd, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f,
    0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8,
    0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd,
    0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0,
    0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f,
    0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf8, 0x0, 0xf,
    0xfd, 0x5f, 0xf8, 0x0, 0xf, 0xfd, 0x5f, 0xf9,
    0x0, 0x1f, 0xfd, 0x5f, 0xfb, 0x0, 0x2f, 0xfb,
    0x5f, 0xff, 0x20, 0x7f, 0xfa, 0x5f, 0xff, 0xea,
    0xff, 0xf7, 0x5f, 0xf9, 0xef, 0xff, 0xf1, 0x5f,
    0xf8, 0x2b, 0xfd, 0x50, 0x5f, 0xf8, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0,
    0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0,
    0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x5f,
    0xf8, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x1, 0xbf, 0xd5, 0xa, 0xfb, 0xb, 0xff, 0xff,
    0x5c, 0xfb, 0x1f, 0xff, 0xcd, 0xef, 0xfb, 0x4f,
    0xfc, 0x0, 0xcf, 0xfb, 0x6f, 0xf7, 0x0, 0x5f,
    0xfb, 0x7f, 0xf6, 0x0, 0x3f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb,
    0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0,
    0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f,
    0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f,
    0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5,
    0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb,
    0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0,
    0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x8f,
    0xf5, 0x0, 0x2f, 0xfb, 0x8f, 0xf5, 0x0, 0x2f,
    0xfb, 0x8f, 0xf5, 0x0, 0x2f, 0xfb, 0x7f, 0xf6,
    0x0, 0x3f, 0xfb, 0x6f, 0xf7, 0x0, 0x5f, 0xfb,
    0x4f, 0xfb, 0x0, 0xbf, 0xfb, 0x1f, 0xff, 0xbc,
    0xff, 0xfb, 0xb, 0xff, 0xff, 0x8f, 0xfb, 0x1,
    0xbf, 0xd6, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x2f,
    0xfb, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0, 0x0,
    0x0, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x2f, 0xfb,
    0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0, 0x0, 0x0,
    0x2f, 0xfb, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0,
    0x0, 0x0, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x2f,
    0xfb, 0x0, 0x0, 0x0, 0x2f, 0xfb,

    /* U+0072 "r" */
    0x5f, 0xf0, 0x3c, 0xfe, 0x15, 0xff, 0x4e, 0xff,
    0xd0, 0x5f, 0xfe, 0xff, 0xf7, 0x5, 0xff, 0xf7,
    0x38, 0x20, 0x5f, 0xfc, 0x0, 0x0, 0x5, 0xff,
    0x90, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x5,
    0xff, 0x80, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0,
    0x5, 0xff, 0x80, 0x0, 0x0, 0x5f, 0xf8, 0x0,
    0x0, 0x5, 0xff, 0x80, 0x0, 0x0, 0x5f, 0xf8,
    0x0, 0x0, 0x5, 0xff, 0x80, 0x0, 0x0, 0x5f,
    0xf8, 0x0, 0x0, 0x5, 0xff, 0x80, 0x0, 0x0,
    0x5f, 0xf8, 0x0, 0x0, 0x5, 0xff, 0x80, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x5, 0xff, 0x80,
    0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x5, 0xff,
    0x80, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x5,
    0xff, 0x80, 0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0,
    0x5, 0xff, 0x80, 0x0, 0x0, 0x5f, 0xf8, 0x0,
    0x0, 0x5, 0xff, 0x80, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x8d, 0xff, 0xc7, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xa0, 0x5f, 0xff, 0xcc, 0xff, 0xf3, 0xaf,
    0xf6, 0x0, 0x8f, 0xf8, 0xdf, 0xf0, 0x0, 0x3f,
    0xfa, 0xef, 0xf0, 0x0, 0x2f, 0xfb, 0xef, 0xf0,
    0x0, 0x2f, 0xfc, 0xef, 0xf0, 0x0, 0x2f, 0xfc,
    0xcf, 0xf2, 0x0, 0x2f, 0xfc, 0xaf, 0xf6, 0x0,
    0x18, 0x86, 0x6f, 0xfd, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x70, 0x0, 0x0, 0x7, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0x50, 0x0, 0x0, 0x1c,
    0xff, 0xf5, 0x0, 0x0, 0x1, 0xdf, 0xff, 0x30,
    0x0, 0x0, 0x1e, 0xff, 0xc0, 0x0, 0x0, 0x3,
    0xff, 0xf2, 0x56, 0x50, 0x0, 0xbf, 0xf7, 0xff,
    0xf0, 0x0, 0x6f, 0xf9, 0xff, 0xf0, 0x0, 0x3f,
    0xfb, 0xef, 0xf0, 0x0, 0x2f, 0xfc, 0xef, 0xf0,
    0x0, 0x2f, 0xfb, 0xdf, 0xf0, 0x0, 0x4f, 0xfa,
    0xaf, 0xf4, 0x0, 0xaf, 0xf7, 0x6f, 0xff, 0xbd,
    0xff, 0xf2, 0xd, 0xff, 0xff, 0xff, 0x90, 0x1,
    0x9e, 0xff, 0xc6, 0x0,

    /* U+0074 "t" */
    0x0, 0xaf, 0xf3, 0x0, 0x0, 0xa, 0xff, 0x30,
    0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0, 0xa, 0xff,
    0x30, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0, 0xa,
    0xff, 0x30, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xf5, 0xdf, 0xff, 0xff, 0xff,
    0x58, 0xae, 0xff, 0xba, 0xa3, 0x0, 0xaf, 0xf3,
    0x0, 0x0, 0xa, 0xff, 0x30, 0x0, 0x0, 0xaf,
    0xf3, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0, 0x0,
    0xaf, 0xf3, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0,
    0x0, 0xaf, 0xf3, 0x0, 0x0, 0xa, 0xff, 0x30,
    0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0, 0xa, 0xff,
    0x30, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0, 0xa,
    0xff, 0x30, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0,
    0xa, 0xff, 0x30, 0x0, 0x0, 0xaf, 0xf3, 0x0,
    0x0, 0xa, 0xff, 0x30, 0x0, 0x0, 0xaf, 0xf3,
    0x0, 0x0, 0xa, 0xff, 0x30, 0x0, 0x0, 0xaf,
    0xf3, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0,
    0x8f, 0xf4, 0x0, 0x0, 0x6, 0xff, 0x90, 0x0,
    0x0, 0x3f, 0xff, 0xba, 0x40, 0x0, 0xcf, 0xff,
    0xf6, 0x0, 0x1, 0xae, 0xfd, 0x40,

    /* U+0075 "u" */
    0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0,
    0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f,
    0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f,
    0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6,
    0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc,
    0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0,
    0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f,
    0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f,
    0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6,
    0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc,
    0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0,
    0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x7f,
    0xf6, 0x0, 0x2f, 0xfc, 0x7f, 0xf6, 0x0, 0x2f,
    0xfc, 0x7f, 0xf6, 0x0, 0x2f, 0xfc, 0x6f, 0xf7,
    0x0, 0x2f, 0xfc, 0x5f, 0xf8, 0x0, 0x5f, 0xfc,
    0x3f, 0xfc, 0x0, 0xbf, 0xfc, 0xf, 0xff, 0xcc,
    0xee, 0xfc, 0xa, 0xff, 0xff, 0x6c, 0xfc, 0x1,
    0xbf, 0xd7, 0x9, 0xfc,

    /* U+0076 "v" */
    0x9f, 0xf7, 0x0, 0x3, 0xff, 0xd6, 0xff, 0x90,
    0x0, 0x5f, 0xfb, 0x4f, 0xfa, 0x0, 0x6, 0xff,
    0x92, 0xff, 0xc0, 0x0, 0x8f, 0xf7, 0xf, 0xfd,
    0x0, 0x9, 0xff, 0x50, 0xef, 0xf0, 0x0, 0xbf,
    0xf2, 0xc, 0xff, 0x0, 0xc, 0xff, 0x0, 0xaf,
    0xf2, 0x0, 0xef, 0xe0, 0x8, 0xff, 0x30, 0xf,
    0xfc, 0x0, 0x5f, 0xf5, 0x1, 0xff, 0xa0, 0x3,
    0xff, 0x60, 0x2f, 0xf8, 0x0, 0x1f, 0xf8, 0x4,
    0xff, 0x60, 0x0, 0xff, 0x90, 0x5f, 0xf4, 0x0,
    0xd, 0xfb, 0x7, 0xff, 0x20, 0x0, 0xbf, 0xc0,
    0x8f, 0xf0, 0x0, 0x9, 0xfe, 0x9, 0xfe, 0x0,
    0x0, 0x7f, 0xf0, 0xbf, 0xc0, 0x0, 0x5, 0xff,
    0x1c, 0xf9, 0x0, 0x0, 0x2f, 0xf2, 0xef, 0x70,
    0x0, 0x0, 0xff, 0x4f, 0xf5, 0x0, 0x0, 0xe,
    0xf6, 0xff, 0x30, 0x0, 0x0, 0xcf, 0x9f, 0xf1,
    0x0, 0x0, 0xa, 0xfc, 0xff, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xd0, 0x0, 0x0, 0x6, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x90, 0x0, 0x0,
    0x1, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x50, 0x0,

    /* U+0077 "w" */
    0x9f, 0xf8, 0x0, 0x3, 0xff, 0xf1, 0x0, 0xa,
    0xff, 0x66, 0xff, 0x90, 0x0, 0x5f, 0xff, 0x30,
    0x0, 0xbf, 0xf4, 0x4f, 0xfb, 0x0, 0x6, 0xff,
    0xf4, 0x0, 0xd, 0xff, 0x22, 0xff, 0xc0, 0x0,
    0x8f, 0xff, 0x60, 0x0, 0xff, 0xf0, 0xf, 0xfe,
    0x0, 0x9, 0xff, 0xf7, 0x0, 0xf, 0xfe, 0x0,
    0xef, 0xf0, 0x0, 0xbf, 0xff, 0x90, 0x2, 0xff,
    0xb0, 0xc, 0xff, 0x10, 0xd, 0xff, 0xfb, 0x0,
    0x3f, 0xf9, 0x0, 0xaf, 0xf3, 0x0, 0xef, 0xef,
    0xc0, 0x5, 0xff, 0x70, 0x8, 0xff, 0x40, 0xf,
    0xfb, 0xfe, 0x0, 0x6f, 0xf5, 0x0, 0x5f, 0xf6,
    0x1, 0xff, 0x8f, 0xf0, 0x8, 0xff, 0x30, 0x3,
    0xff, 0x70, 0x3f, 0xf4, 0xff, 0x10, 0x9f, 0xf1,
    0x0, 0x1f, 0xf9, 0x5, 0xff, 0x2f, 0xf3, 0xb,
    0xff, 0x0, 0x0, 0xff, 0xa0, 0x6f, 0xd0, 0xff,
    0x40, 0xdf, 0xd0, 0x0, 0xd, 0xfc, 0x8, 0xfc,
    0xe, 0xf6, 0xe, 0xfb, 0x0, 0x0, 0xbf, 0xe0,
    0x9f, 0xa0, 0xdf, 0x70, 0xff, 0x90, 0x0, 0x9,
    0xff, 0xb, 0xf8, 0xb, 0xf9, 0x1f, 0xf6, 0x0,
    0x0, 0x7f, 0xf1, 0xdf, 0x70, 0x9f, 0xb3, 0xff,
    0x40, 0x0, 0x5, 0xff, 0x2e, 0xf5, 0x7, 0xfc,
    0x4f, 0xf2, 0x0, 0x0, 0x2f, 0xf4, 0xff, 0x30,
    0x6f, 0xe6, 0xff, 0x0, 0x0, 0x0, 0xff, 0x7f,
    0xf2, 0x4, 0xff, 0x8f, 0xe0, 0x0, 0x0, 0xe,
    0xfb, 0xff, 0x0, 0x2f, 0xfb, 0xfc, 0x0, 0x0,
    0x0, 0xcf, 0xef, 0xe0, 0x1, 0xff, 0xef, 0xa0,
    0x0, 0x0, 0xa, 0xff, 0xfd, 0x0, 0xf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xb0, 0x0,
    0xdf, 0xff, 0x60, 0x0, 0x0, 0x6, 0xff, 0xf9,
    0x0, 0xc, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x80, 0x0, 0xaf, 0xff, 0x10, 0x0, 0x0,
    0x1, 0xff, 0xf6, 0x0, 0x8, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x40, 0x0, 0x6f, 0xfd,
    0x0, 0x0,

    /* U+0078 "x" */
    0x9f, 0xf4, 0x0, 0x0, 0xdf, 0xf1, 0x4f, 0xf8,
    0x0, 0x1, 0xff, 0xc0, 0xf, 0xfd, 0x0, 0x5,
    0xff, 0x70, 0xb, 0xff, 0x10, 0xa, 0xff, 0x20,
    0x6, 0xff, 0x60, 0xe, 0xfd, 0x0, 0x1, 0xff,
    0xa0, 0x2f, 0xf8, 0x0, 0x0, 0xdf, 0xe0, 0x7f,
    0xf4, 0x0, 0x0, 0x8f, 0xf3, 0xbf, 0xe0, 0x0,
    0x0, 0x3f, 0xf8, 0xff, 0xa0, 0x0, 0x0, 0xe,
    0xfe, 0xff, 0x50, 0x0, 0x0, 0xa, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x1f, 0xf9, 0xff,
    0x70, 0x0, 0x0, 0x6f, 0xf2, 0xef, 0xc0, 0x0,
    0x0, 0xaf, 0xe0, 0xaf, 0xf1, 0x0, 0x0, 0xef,
    0xa0, 0x5f, 0xf6, 0x0, 0x3, 0xff, 0x60, 0x1f,
    0xfa, 0x0, 0x8, 0xff, 0x20, 0xc, 0xff, 0x0,
    0xc, 0xfe, 0x0, 0x8, 0xff, 0x40, 0x1f, 0xfa,
    0x0, 0x3, 0xff, 0x90, 0x5f, 0xf6, 0x0, 0x0,
    0xef, 0xe0, 0xaf, 0xf2, 0x0, 0x0, 0xaf, 0xf2,

    /* U+0079 "y" */
    0x6f, 0xfa, 0x0, 0x0, 0x9f, 0xf7, 0x4f, 0xfc,
    0x0, 0x0, 0xbf, 0xf4, 0x2f, 0xfd, 0x0, 0x0,
    0xdf, 0xf2, 0xf, 0xff, 0x0, 0x0, 0xff, 0xf0,
    0xe, 0xff, 0x0, 0x0, 0xff, 0xd0, 0xb, 0xff,
    0x20, 0x2, 0xff, 0xb0, 0x9, 0xff, 0x40, 0x4,
    0xff, 0x90, 0x7, 0xff, 0x50, 0x5, 0xff, 0x60,
    0x5, 0xff, 0x70, 0x7, 0xff, 0x40, 0x3, 0xff,
    0x90, 0x9, 0xff, 0x20, 0x1, 0xff, 0xa0, 0xa,
    0xff, 0x0, 0x0, 0xff, 0xc0, 0xc, 0xfd, 0x0,
    0x0, 0xdf, 0xd0, 0xe, 0xfb, 0x0, 0x0, 0xbf,
    0xf0, 0xf, 0xf8, 0x0, 0x0, 0x9f, 0xf1, 0x1f,
    0xf6, 0x0, 0x0, 0x7f, 0xf2, 0x3f, 0xf4, 0x0,
    0x0, 0x5f, 0xf4, 0x5f, 0xf1, 0x0, 0x0, 0x3f,
    0xf5, 0x6f, 0xf0, 0x0, 0x0, 0x1f, 0xf7, 0x8f,
    0xd0, 0x0, 0x0, 0xf, 0xf9, 0xaf, 0xa0, 0x0,
    0x0, 0xd, 0xfa, 0xbf, 0x80, 0x0, 0x0, 0xa,
    0xfc, 0xdf, 0x50, 0x0, 0x0, 0x8, 0xfd, 0xff,
    0x30, 0x0, 0x0, 0x6, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x4, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x7, 0xff, 0x70,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xb, 0xff, 0xff, 0xff, 0xff, 0xa0, 0xbf, 0xff,
    0xff, 0xff, 0xfa, 0x7, 0xaa, 0xaa, 0xae, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x4f, 0xfe, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x90, 0x0, 0x0, 0x0, 0xef, 0xf4, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0xcf, 0xf6, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x20, 0x0, 0x0, 0x6, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0xbf, 0xf8, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x40, 0x0, 0x0, 0x4, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x9f, 0xfa, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x60, 0x0, 0x0, 0x2, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x70, 0x0, 0x0, 0x1, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x90, 0x0, 0x0, 0x0, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xfb, 0xbb, 0xbb, 0xb0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff,
    0xff, 0xf1,

    /* U+007B "{" */
    0x0, 0x0, 0x2, 0xbf, 0xff, 0x60, 0x0, 0x0,
    0xdf, 0xff, 0xf6, 0x0, 0x0, 0x6f, 0xfe, 0x98,
    0x30, 0x0, 0xa, 0xff, 0x40, 0x0, 0x0, 0x0,
    0xcf, 0xf0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0xfe, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x10, 0x0, 0x5, 0xbf, 0xff, 0x70, 0x0,
    0x0, 0xdf, 0xff, 0x80, 0x0, 0x0, 0xd, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x37, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xf4, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xa, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x5, 0x9a, 0xa4,

    /* U+007C "|" */
    0x78, 0x80, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1, 0xef, 0xf1,
    0xef, 0xf1,

    /* U+007D "}" */
    0x5f, 0xff, 0xb2, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x38, 0x9e, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x4f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0, 0x0,
    0xe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xef, 0xe0,
    0x0, 0x0, 0x0, 0xe, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0xe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xe0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xe0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf5, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xb6, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0, 0x0,
    0x0, 0x1c, 0xff, 0xfe, 0x0, 0x0, 0xb, 0xff,
    0xe8, 0x30, 0x0, 0x3, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x9f, 0xf6, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xef, 0xf0, 0x0, 0x0,
    0x0, 0xe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xe0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xe0, 0x0, 0x0, 0x0, 0xe,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xef, 0xe0, 0x0,
    0x0, 0x0, 0xe, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xe0, 0x0, 0x0, 0x0,
    0xe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xa0, 0x0, 0x5, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x5f, 0xff, 0xfa, 0x0, 0x0, 0x3,
    0xaa, 0x96, 0x0, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x29, 0xef, 0xda, 0x40, 0x0, 0x0, 0xa,
    0x30, 0x4, 0xff, 0xff, 0xff, 0xff, 0x95, 0x23,
    0xbf, 0xf4, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe1, 0x3e, 0xf9, 0x10, 0x26, 0xcf,
    0xff, 0xff, 0xfe, 0x20, 0x2, 0x70, 0x0, 0x0,
    0x2, 0x8c, 0xfd, 0x81, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 93, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 114, .box_w = 5, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 93, .adv_w = 154, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 26},
    {.bitmap_index = 137, .adv_w = 356, .box_w = 22, .box_h = 33, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 500, .adv_w = 231, .box_w = 12, .box_h = 44, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 764, .adv_w = 455, .box_w = 27, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1264, .adv_w = 249, .box_w = 14, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1537, .adv_w = 83, .box_w = 4, .box_h = 11, .ofs_x = 1, .ofs_y = 26},
    {.bitmap_index = 1559, .adv_w = 128, .box_w = 7, .box_h = 46, .ofs_x = 1, .ofs_y = -9},
    {.bitmap_index = 1720, .adv_w = 128, .box_w = 8, .box_h = 46, .ofs_x = 0, .ofs_y = -9},
    {.bitmap_index = 1904, .adv_w = 206, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 26},
    {.bitmap_index = 1982, .adv_w = 324, .box_w = 18, .box_h = 17, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 2135, .adv_w = 147, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 2179, .adv_w = 148, .box_w = 7, .box_h = 3, .ofs_x = 1, .ofs_y = 13},
    {.bitmap_index = 2190, .adv_w = 114, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2203, .adv_w = 216, .box_w = 13, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2444, .adv_w = 246, .box_w = 12, .box_h = 39, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2678, .adv_w = 166, .box_w = 8, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2826, .adv_w = 222, .box_w = 12, .box_h = 38, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3054, .adv_w = 236, .box_w = 12, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3288, .adv_w = 230, .box_w = 14, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3547, .adv_w = 234, .box_w = 13, .box_h = 38, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3794, .adv_w = 243, .box_w = 12, .box_h = 39, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4028, .adv_w = 222, .box_w = 14, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4287, .adv_w = 242, .box_w = 13, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4541, .adv_w = 243, .box_w = 13, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4795, .adv_w = 114, .box_w = 5, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4850, .adv_w = 132, .box_w = 8, .box_h = 29, .ofs_x = -1, .ofs_y = -7},
    {.bitmap_index = 4966, .adv_w = 324, .box_w = 16, .box_h = 32, .ofs_x = 2, .ofs_y = 2},
    {.bitmap_index = 5222, .adv_w = 324, .box_w = 18, .box_h = 10, .ofs_x = 1, .ofs_y = 13},
    {.bitmap_index = 5312, .adv_w = 324, .box_w = 16, .box_h = 32, .ofs_x = 2, .ofs_y = 2},
    {.bitmap_index = 5568, .adv_w = 211, .box_w = 13, .box_h = 38, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5815, .adv_w = 515, .box_w = 31, .box_h = 44, .ofs_x = 0, .ofs_y = -11},
    {.bitmap_index = 6497, .adv_w = 221, .box_w = 14, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6756, .adv_w = 236, .box_w = 13, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6997, .adv_w = 230, .box_w = 13, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7251, .adv_w = 235, .box_w = 12, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7473, .adv_w = 205, .box_w = 11, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7677, .adv_w = 202, .box_w = 11, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7881, .adv_w = 241, .box_w = 13, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 8135, .adv_w = 241, .box_w = 13, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8376, .adv_w = 107, .box_w = 4, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8450, .adv_w = 107, .box_w = 7, .box_h = 47, .ofs_x = -2, .ofs_y = -10},
    {.bitmap_index = 8615, .adv_w = 228, .box_w = 14, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8874, .adv_w = 162, .box_w = 9, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9041, .adv_w = 336, .box_w = 19, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9393, .adv_w = 258, .box_w = 14, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9652, .adv_w = 236, .box_w = 13, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9906, .adv_w = 224, .box_w = 12, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10128, .adv_w = 236, .box_w = 13, .box_h = 45, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 10421, .adv_w = 228, .box_w = 13, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10662, .adv_w = 227, .box_w = 12, .box_h = 39, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 10896, .adv_w = 206, .box_w = 13, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11137, .adv_w = 239, .box_w = 13, .box_h = 38, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 11384, .adv_w = 212, .box_w = 13, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11625, .adv_w = 351, .box_w = 22, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12032, .adv_w = 230, .box_w = 16, .box_h = 37, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12328, .adv_w = 227, .box_w = 14, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12587, .adv_w = 202, .box_w = 12, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12809, .adv_w = 149, .box_w = 7, .box_h = 46, .ofs_x = 2, .ofs_y = -9},
    {.bitmap_index = 12970, .adv_w = 216, .box_w = 13, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13211, .adv_w = 149, .box_w = 8, .box_h = 46, .ofs_x = 0, .ofs_y = -9},
    {.bitmap_index = 13395, .adv_w = 324, .box_w = 15, .box_h = 17, .ofs_x = 3, .ofs_y = 21},
    {.bitmap_index = 13523, .adv_w = 220, .box_w = 12, .box_h = 3, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 13541, .adv_w = 75, .box_w = 7, .box_h = 5, .ofs_x = -1, .ofs_y = 31},
    {.bitmap_index = 13559, .adv_w = 193, .box_w = 10, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13699, .adv_w = 197, .box_w = 10, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13884, .adv_w = 190, .box_w = 10, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14024, .adv_w = 197, .box_w = 10, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14209, .adv_w = 192, .box_w = 10, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14349, .adv_w = 140, .box_w = 9, .box_h = 37, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14516, .adv_w = 194, .box_w = 12, .box_h = 38, .ofs_x = 0, .ofs_y = -10},
    {.bitmap_index = 14744, .adv_w = 197, .box_w = 10, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14929, .adv_w = 97, .box_w = 6, .box_h = 36, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15037, .adv_w = 97, .box_w = 8, .box_h = 46, .ofs_x = -2, .ofs_y = -10},
    {.bitmap_index = 15221, .adv_w = 199, .box_w = 12, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15443, .adv_w = 98, .box_w = 4, .box_h = 37, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15517, .adv_w = 299, .box_w = 17, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15755, .adv_w = 198, .box_w = 10, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15895, .adv_w = 195, .box_w = 10, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16035, .adv_w = 197, .box_w = 10, .box_h = 38, .ofs_x = 1, .ofs_y = -10},
    {.bitmap_index = 16225, .adv_w = 197, .box_w = 10, .box_h = 38, .ofs_x = 1, .ofs_y = -10},
    {.bitmap_index = 16415, .adv_w = 146, .box_w = 9, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16541, .adv_w = 189, .box_w = 10, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16681, .adv_w = 137, .box_w = 9, .box_h = 35, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16839, .adv_w = 198, .box_w = 10, .box_h = 28, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16979, .adv_w = 181, .box_w = 11, .box_h = 28, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17133, .adv_w = 302, .box_w = 19, .box_h = 28, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17399, .adv_w = 184, .box_w = 12, .box_h = 28, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17567, .adv_w = 188, .box_w = 12, .box_h = 38, .ofs_x = 0, .ofs_y = -10},
    {.bitmap_index = 17795, .adv_w = 177, .box_w = 11, .box_h = 28, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17949, .adv_w = 177, .box_w = 11, .box_h = 46, .ofs_x = 0, .ofs_y = -9},
    {.bitmap_index = 18202, .adv_w = 115, .box_w = 4, .box_h = 49, .ofs_x = 2, .ofs_y = -11},
    {.bitmap_index = 18300, .adv_w = 177, .box_w = 11, .box_h = 46, .ofs_x = 0, .ofs_y = -9},
    {.bitmap_index = 18553, .adv_w = 324, .box_w = 18, .box_h = 5, .ofs_x = 1, .ofs_y = 24}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 1, 0, 2, 0, 0, 0, 3,
    2, 4, 5, 6, 0, 7, 8, 7,
    9, 10, 11, 12, 13, 14, 15, 16,
    17, 16, 18, 19, 19, 0, 0, 0,
    0, 20, 21, 22, 23, 24, 25, 26,
    27, 0, 0, 0, 28, 29, 0, 0,
    30, 31, 30, 32, 33, 34, 35, 36,
    37, 38, 39, 40, 41, 42, 0, 0,
    0, 0, 43, 44, 45, 46, 47, 48,
    49, 50, 0, 0, 51, 0, 50, 50,
    52, 44, 53, 54, 55, 56, 57, 58,
    59, 60, 61, 62, 63, 0, 64, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 1, 0, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 8,
    10, 11, 12, 13, 14, 15, 16, 11,
    17, 18, 18, 19, 19, 0, 0, 0,
    20, 21, 22, 23, 24, 23, 23, 23,
    24, 23, 23, 25, 23, 23, 23, 23,
    24, 23, 24, 23, 26, 27, 28, 29,
    30, 31, 32, 33, 0, 34, 35, 0,
    0, 0, 36, 0, 37, 38, 37, 39,
    40, 0, 0, 41, 0, 0, 42, 42,
    37, 42, 38, 42, 43, 44, 45, 46,
    47, 48, 49, 50, 51, 0, 52, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -10, 0, 0,
    0, 0, -11, 0, -11, -11, 0, -15,
    0, 0, 0, 0, 0, 0, -9, 0,
    0, 0, 0, -7, 0, -10, -10, 0,
    -9, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, -42, -31, -29, 0, 0,
    0, 0, -14, 0, 0, 0, -11, 0,
    -30, -20, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    -6, -7, 0, -7, 0, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 24, 0, 0, 0,
    0, 0, 5, 0, 0, 0, 0, -12,
    -12, -12, 0, 0, 32, -10, -12, 0,
    -11, -10, -9, 0, 0, 0, -12, 0,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    -11, -12, 0, -12, 0, -7, -11, 0,
    -6, 0, 0, 0, 0, -7, 0, 0,
    0, -42, 0, 0, 0, 0, 0, 0,
    0, 0, -9, -21, 0, -6, -12, -9,
    0, -7, 0, 0, 0, 0, 0, -9,
    0, -7, -34, -9, -32, -28, 0, -44,
    0, 0, 0, 0, 0, 0, -7, 0,
    0, 0, 0, -6, 0, -15, -14, 0,
    -12, 0, 0, 0, 0, -31, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -16,
    -12, 0, 0, 0, -15, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -18, 0,
    -9, -7, -11, -25, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -111, 0, 0, 0, 0, -17, 0,
    0, 0, 0, 0, 0, -18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -17, -15, -15, 0, -18,
    0, -13, -15, 0, -12, -7, -6, -7,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -15, 0, 0, 0,
    0, 0, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, -7, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -9, 0,
    -6, 0, 0, -15, 0, 0, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -22, 0, 0, 9, 0, -42,
    -25, -36, 0, 5, 0, 0, -18, 0,
    0, 0, 0, 0, 0, -18, 0, 0,
    0, 0, 8, 0, 7, 5, 12, 8,
    0, 5, 4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 4, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -17, 0, -7, -5, 0, -19,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -25, 0,
    -9, -7, -13, -25, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -11, -20, 0, 0, 0, 0, -17, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    4, 0, 0, -7, 0, 0, 0, -2,
    0, -3, -26, 0, -12, -11, 0, -24,
    0, -18, -14, -2, 0, 0, -4, 0,
    0, 0, 0, -4, 0, -7, -6, 0,
    -4, 0, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, -6, 0,
    -5, -4, -4, -10, 0, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -2, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    -2, 0, -4, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, -56, 0, -23, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, -14, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    -7, -7, -2, -7, 0, -6, -6, 0,
    -4, -2, -2, -9, 0, -13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, -4, -4, -3, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 10, 0, 0,
    0, 0, -10, 0, 14, 0, 0, 0,
    0, 0, 0, -4, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -6, -5, -5, 0, 0, 0, -3, -5,
    -2, -12, -12, 0, -11, 0, 0, 0,
    -10, -29, 0, 0, 0, 0, -26, 0,
    -19, 0, 0, -16, 0, 0, -21, 0,
    0, 0, 0, -13, 0, 0, 0, 0,
    0, 0, -31, -2, -24, -22, 0, -35,
    0, -23, -15, 0, 0, 0, -11, 0,
    0, 0, 0, -11, -2, -21, -21, 0,
    -19, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    -3, -2, -4, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -10, 0, 0, 0, 0, 0, 0, -57,
    -10, -28, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -14, -16, 0, 0,
    0, 0, 0, 0, 0, 0, -7, 0,
    -4, 0, 0, -3, -3, -3, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -4, -3, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -5,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -11, 0, 0, 0,
    0, 0, 0, -34, -18, -20, 0, 3,
    0, 0, -18, 0, 0, 0, -17, 0,
    -23, -25, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -31,
    -26, -26, -19, -27, 0, -26, -31, -15,
    -26, -31, -31, -31, -29, -34, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, -11, 0, 0, 0,
    0, 0, 0, -32, -9, -17, 0, 0,
    0, 0, -6, 0, 0, 0, -7, 0,
    -14, -12, 0, -4, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    -12, -12, 0, -12, 0, -9, -10, 0,
    -7, 0, 0, -3, 0, -7, 0, 0,
    -11, 0, 0, 0, 0, 0, 0, -28,
    -7, -15, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, -11, -11, 0, -4,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, -9, -9, 0, -11,
    0, -6, -7, 0, -5, 0, 0, -2,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 4, 0, 0, -11, 0, 0, 0,
    0, 0, -6, 0, 6, 0, 0, 0,
    0, 0, 0, -4, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -7, -7, -5, 0, 0, -3, -5, -4,
    -5, -11, -11, 0, -9, 0, 0, 0,
    -15, 0, 0, -6, 0, 0, 0, -44,
    -25, -28, -7, 0, 0, 0, -22, 0,
    0, 0, -19, 0, -29, -24, 0, -7,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -31, -28, -28, -4, -29,
    0, -26, -29, -3, -24, -9, -9, -12,
    -7, -19, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, -15, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -3, -3, -4, 0, 0, -2, 0, -3,
    -3, -7, -7, 0, -5, 0, 0, 0,
    0, 0, 0, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -15, 0,
    0, 0, 0, 0, 0, -15, 0, 0,
    18, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -15, -15, -15, 0, 0,
    27, -13, -15, 0, -13, -9, -9, -9,
    0, -12, -14, 0, 0, -29, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -15,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -20, 0,
    -17, -15, 6, -28, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -9, -9, 0, -7, 0, 0, 0,
    0, -6, 0, 0, 0, -11, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -27, 0, -13, -11, 0, -28,
    0, -16, -15, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -15, 0, -7, 0, 0,
    0, -12, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -26, 0,
    -12, -9, -6, -28, -2, -15, -15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, 0, 0, -15,
    0, -6, 0, 0, 0, -12, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -34, 0, -10, -7, -4, -28,
    0, -15, -14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -3, -3,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, -12, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -30, 0, -11, -7, -5, -29,
    0, -15, -15, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -3, -4,
    0, 0, 0, -14, -10, 0, 0, 0,
    0, 13, 0, -9, -5, -7, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -7, -6, 0, 0, 0, 0, 6, 0,
    5, 4, 10, 5, 0, 9, 8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -29, 0, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, -11, -11, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -27, 0,
    -13, -10, 0, -28, 0, -16, -15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -15,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -32, 0, -3, 0, 0, -12,
    0, -7, -7, -3, -4, -3, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, -6, 0, 0,
    0, -12, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, -26, 0,
    -12, -10, -7, -28, -2, -15, -15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, -3, -4, 0, 0, 0, -15,
    0, 0, 0, 0, 0, -9, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -26, 0, -7, -6, 0, -25,
    0, -12, -13, 0, 0, 0, 0, 0,
    10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, -12, 0, 0, 0,
    0, -9, 0, -20, -15, -18, 0, 0,
    0, 0, 0, 0, 0, 0, -14, 0,
    -17, -16, -2, 0, -2, 0, -38, 0,
    0, 0, -12, -4, -7, 0, -6, -4,
    -4, -4, 0, -5, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, -6, 0, 0, 0, -11, -11, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -31, 0, -10, -7, -4, -28,
    0, -15, -14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -3, -2,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -24, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -9, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -26, 0, -7, -6, 0, -25,
    0, -12, -13, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, -10, 0, 0, 0,
    0, -10, 0, -15, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, -31, 0,
    0, 0, -11, -9, -7, -7, -7, -4,
    -4, 0, 0, -4, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    -10, 0, 0, 0, 0, -9, 0, -14,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, 0, -31, 0, 0, 0, -10, -9,
    -6, 0, -9, -4, -3, 0, 0, -4,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -31, -2,
    -3, 0, 0, -11, 0, -7, -7, -3,
    -4, -3, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    -11, 0, 0, 0, 0, -9, 0, -15,
    0, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, 0, -31, 0, 0, 0, -11, -7,
    -7, 0, -6, -4, -4, 0, 0, -4,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -35, 0,
    0, 0, 0, -9, 0, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -17, 0,
    0, 0, 0, 0, 0, -13, 0, 0,
    18, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -15, -15, -15, 0, 0,
    27, -13, -14, 0, -12, -9, -9, -7,
    0, -11, -15, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -15
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 64,
    .right_class_cnt     = 52,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
    /*Store all the custom data of the font*/
    static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_ebike_trump_48 = {
#else
lv_font_t font_ebike_trump_48 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 52,          /*The maximum line height required by the font*/
    .base_line = 11,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -6,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_EBIKE_TRUMP_48*/

