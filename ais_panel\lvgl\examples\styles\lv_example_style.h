/**
 * @file lv_example_style.h
 *
 */

#ifndef LV_EXAMPLE_STYLE_H
#define LV_EXAMPLE_STYLE_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_style_1(void);
void lv_example_style_2(void);
void lv_example_style_3(void);
void lv_example_style_4(void);
void lv_example_style_5(void);
void lv_example_style_6(void);
void lv_example_style_7(void);
void lv_example_style_8(void);
void lv_example_style_9(void);
void lv_example_style_10(void);
void lv_example_style_11(void);
void lv_example_style_12(void);
void lv_example_style_13(void);
void lv_example_style_14(void);
void lv_example_style_15(void);
void lv_example_style_16(void);
void lv_example_style_17(void);
void lv_example_style_18(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_STYLE_H*/
