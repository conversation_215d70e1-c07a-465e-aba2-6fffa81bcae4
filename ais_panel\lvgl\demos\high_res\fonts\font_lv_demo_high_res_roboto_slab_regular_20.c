/*******************************************************************************
 * Size: 20 px
 * Bpp: 8
 * Opts: --bpp 8 --size 20 --no-compress --font RobotoSlab-Regular.ttf --range 32-127,176 --format lvgl -o font_lv_demo_high_res_roboto_slab_regular_20.c
 ******************************************************************************/

#include "../../../lvgl.h"

#if LV_USE_DEMO_HIGH_RES

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x9c, 0xff, 0x54, 0x9c, 0xff, 0x54, 0x9c, 0xff,
    0x54, 0x9c, 0xff, 0x54, 0x9c, 0xff, 0x54, 0x9c,
    0xff, 0x54, 0x9c, 0xff, 0x54, 0x9c, 0xff, 0x54,
    0x9c, 0xff, 0x54, 0x77, 0xc4, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x8, 0x3, 0x9c,
    0xff, 0x54, 0x9c, 0xff, 0x54,

    /* U+0022 "\"" */
    0x60, 0xff, 0x2c, 0x58, 0xff, 0x30, 0x60, 0xff,
    0x2c, 0x58, 0xff, 0x30, 0x60, 0xff, 0x1b, 0x58,
    0xff, 0x20, 0x60, 0xe4, 0x0, 0x58, 0xea, 0x0,
    0x60, 0xae, 0x0, 0x58, 0xb4, 0x0, 0x3, 0x5,
    0x0, 0x3, 0x5, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x4a, 0x0,
    0x36, 0xff, 0x42, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x61, 0xff, 0x17, 0x0, 0x69, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x94, 0xe4, 0x0, 0x0,
    0x9c, 0xdc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc6, 0xb2, 0x0, 0x0, 0xce, 0xaa, 0x0, 0x0,
    0x0, 0xa4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x84, 0x0, 0x45, 0x6c, 0x89,
    0xff, 0x96, 0x6c, 0x8c, 0xff, 0x92, 0x6c, 0x38,
    0x0, 0x0, 0x0, 0x51, 0xff, 0x2a, 0x0, 0x56,
    0xff, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0, 0x76,
    0xfd, 0x7, 0x0, 0x7c, 0xfa, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9b, 0xde, 0x0, 0x0, 0xa2,
    0xd9, 0x0, 0x0, 0x0, 0x9, 0x18, 0x18, 0xc5,
    0xc0, 0x18, 0x18, 0xcc, 0xbc, 0x18, 0x13, 0x0,
    0x64, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc8, 0x0, 0x1c, 0x48, 0x68, 0xff,
    0x82, 0x48, 0x6a, 0xff, 0x7c, 0x48, 0x38, 0x0,
    0x0, 0x0, 0x59, 0xff, 0x23, 0x0, 0x5d, 0xff,
    0x1b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x90, 0xec,
    0x1, 0x0, 0x93, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc4, 0xb7, 0x0, 0x0, 0xc9, 0xaf,
    0x0, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x38, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x70,
    0xc8, 0xff, 0xcc, 0x69, 0x3, 0x0, 0x0, 0x6,
    0xc2, 0xff, 0xf4, 0xd6, 0xf7, 0xff, 0xb0, 0x1,
    0x0, 0x65, 0xff, 0xc0, 0xc, 0x0, 0x19, 0xdc,
    0xff, 0x4e, 0x0, 0xa6, 0xff, 0x4b, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x9d, 0x0, 0xad, 0xff, 0x43,
    0x0, 0x0, 0x0, 0x29, 0xdc, 0x9f, 0x0, 0x7e,
    0xff, 0xb2, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0xe4, 0xff, 0xd0, 0x59, 0x9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0xbc, 0xff, 0xff,
    0xf0, 0x87, 0xe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x33, 0x92, 0xf1, 0xff, 0xd8, 0x14, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0xc2, 0xff, 0x93,
    0x11, 0x54, 0x39, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0xff, 0xd6, 0x27, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xff, 0xdd, 0x3, 0xeb, 0xfc, 0x34,
    0x0, 0x0, 0x0, 0x76, 0xff, 0xad, 0x0, 0x68,
    0xff, 0xf5, 0xa0, 0x89, 0xb8, 0xff, 0xf8, 0x33,
    0x0, 0x0, 0x5c, 0xd7, 0xff, 0xff, 0xfd, 0xc0,
    0x37, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x22, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x68, 0xff, 0x1c, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x21, 0xbe, 0xfb, 0xe2, 0x5e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xd2,
    0xcd, 0x44, 0x8d, 0xfe, 0x37, 0x0, 0x0, 0xa,
    0x16, 0x0, 0x0, 0x0, 0x2c, 0xff, 0x47, 0x0,
    0x1, 0xe6, 0x8d, 0x0, 0x0, 0x85, 0xd5, 0x1,
    0x0, 0x0, 0x40, 0xff, 0x2c, 0x0, 0x0, 0xcc,
    0xa0, 0x0, 0x27, 0xfa, 0x62, 0x0, 0x0, 0x0,
    0x36, 0xff, 0x3a, 0x0, 0x0, 0xd9, 0x96, 0x0,
    0xbd, 0xc4, 0x1, 0x0, 0x0, 0x0, 0x7, 0xe8,
    0xad, 0x10, 0x56, 0xff, 0x51, 0x59, 0xfb, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x42, 0xeb, 0xff,
    0xfe, 0x92, 0x10, 0xe8, 0x89, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0x2f, 0x17, 0x0,
    0x94, 0xe1, 0xb, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0xfd, 0x4f,
    0x34, 0xcf, 0xff, 0xdb, 0x4b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xc9, 0xb0, 0xc, 0xe9, 0xb6,
    0x3d, 0x9b, 0xf9, 0x23, 0x0, 0x0, 0x0, 0x0,
    0x68, 0xf5, 0x1f, 0x4b, 0xff, 0x26, 0x0, 0x8,
    0xf8, 0x73, 0x0, 0x0, 0x0, 0x16, 0xef, 0x77,
    0x0, 0x5c, 0xff, 0x10, 0x0, 0x0, 0xe8, 0x84,
    0x0, 0x0, 0x0, 0xa2, 0xd3, 0x5, 0x0, 0x4c,
    0xff, 0x26, 0x0, 0x7, 0xf8, 0x73, 0x0, 0x0,
    0x0, 0x48, 0x38, 0x0, 0x0, 0xd, 0xec, 0xb7,
    0x3c, 0x97, 0xf9, 0x24, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x35, 0xcd, 0xfc, 0xda,
    0x4c, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x4, 0x80, 0xe2, 0xfe, 0xe1, 0x7b,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x83,
    0xff, 0xe0, 0x93, 0xd2, 0xff, 0x86, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xea, 0xfe, 0x21, 0x0,
    0x9, 0xe4, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xe9, 0x0, 0x0, 0x0, 0xcf, 0xf9,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe4, 0xff,
    0x23, 0x0, 0x4e, 0xfe, 0xa9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x78, 0xff, 0xb3, 0x8d, 0xfe,
    0xd1, 0x13, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xd0, 0xff, 0xff, 0xa9, 0xd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xf5, 0xff,
    0xfe, 0x45, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x60, 0xff, 0xe1, 0x69, 0xfb, 0xee, 0x22,
    0x0, 0x0, 0xdc, 0xbb, 0x0, 0xd, 0xf3, 0xf7,
    0x24, 0x0, 0x6b, 0xff, 0xd2, 0xc, 0x18, 0xff,
    0xad, 0x0, 0x40, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x9d, 0xff, 0xad, 0x6f, 0xff, 0x6c, 0x0, 0x3a,
    0xff, 0xb9, 0x0, 0x0, 0x0, 0x6, 0xc4, 0xff,
    0xfa, 0xec, 0x10, 0x0, 0x9, 0xee, 0xfb, 0x32,
    0x0, 0x0, 0x0, 0x40, 0xff, 0xff, 0x83, 0x0,
    0x0, 0x0, 0x59, 0xfd, 0xf6, 0xa6, 0x8d, 0xb6,
    0xfd, 0xfa, 0xf9, 0xed, 0x1c, 0x0, 0x0, 0x0,
    0x3c, 0xb5, 0xef, 0xfd, 0xe4, 0x9d, 0x2a, 0x77,
    0xff, 0xc0, 0x3,

    /* U+0027 "'" */
    0x60, 0xff, 0x2c, 0x60, 0xff, 0x2c, 0x60, 0xff,
    0x1b, 0x60, 0xe4, 0x0, 0x60, 0xae, 0x0, 0x3,
    0x5, 0x0,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x0, 0x1d, 0x3, 0x0, 0x0,
    0x0, 0x3e, 0xef, 0x39, 0x0, 0x0, 0x2a, 0xf3,
    0xab, 0x3, 0x0, 0x3, 0xcd, 0xdf, 0xb, 0x0,
    0x0, 0x60, 0xff, 0x5d, 0x0, 0x0, 0x0, 0xd3,
    0xeb, 0x5, 0x0, 0x0, 0x2f, 0xff, 0x9e, 0x0,
    0x0, 0x0, 0x78, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xad, 0xff, 0x35, 0x0, 0x0, 0x0, 0xce, 0xff,
    0x18, 0x0, 0x0, 0x0, 0xe0, 0xff, 0xb, 0x0,
    0x0, 0x0, 0xe2, 0xff, 0x9, 0x0, 0x0, 0x0,
    0xd5, 0xff, 0x12, 0x0, 0x0, 0x0, 0xb9, 0xff,
    0x2b, 0x0, 0x0, 0x0, 0x8a, 0xff, 0x52, 0x0,
    0x0, 0x0, 0x48, 0xff, 0x8a, 0x0, 0x0, 0x0,
    0x6, 0xec, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x87,
    0xff, 0x35, 0x0, 0x0, 0x0, 0x12, 0xec, 0xb7,
    0x0, 0x0, 0x0, 0x0, 0x58, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xfa, 0x2d, 0x0, 0x0,
    0x0, 0x0, 0x58, 0xe,

    /* U+0029 ")" */
    0x1f, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc3, 0x9e,
    0x3, 0x0, 0x0, 0x0, 0x37, 0xf8, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x79, 0xff, 0x46, 0x0, 0x0,
    0x0, 0x8, 0xe9, 0xd5, 0x2, 0x0, 0x0, 0x0,
    0x85, 0xff, 0x4b, 0x0, 0x0, 0x0, 0x33, 0xff,
    0xa6, 0x0, 0x0, 0x0, 0x2, 0xf1, 0xec, 0x1,
    0x0, 0x0, 0x0, 0xc4, 0xff, 0x21, 0x0, 0x0,
    0x0, 0xa7, 0xff, 0x42, 0x0, 0x0, 0x0, 0x98,
    0xff, 0x54, 0x0, 0x0, 0x0, 0x96, 0xff, 0x56,
    0x0, 0x0, 0x0, 0x9e, 0xff, 0x49, 0x0, 0x0,
    0x0, 0xb8, 0xff, 0x2e, 0x0, 0x0, 0x0, 0xde,
    0xf9, 0x7, 0x0, 0x0, 0x18, 0xff, 0xbd, 0x0,
    0x0, 0x0, 0x61, 0xff, 0x69, 0x0, 0x0, 0x0,
    0xbf, 0xef, 0xd, 0x0, 0x0, 0x3d, 0xff, 0x75,
    0x0, 0x0, 0xe, 0xda, 0xc7, 0x4, 0x0, 0x0,
    0xb1, 0xd8, 0x18, 0x0, 0x0, 0x0, 0x54, 0x12,
    0x0, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x0, 0x10, 0xff, 0x78, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x72, 0x0,
    0x0, 0x0, 0x3, 0x85, 0x28, 0x2, 0xff, 0x6a,
    0x7, 0x57, 0x35, 0x37, 0xff, 0xff, 0xc6, 0xff,
    0xcb, 0xed, 0xff, 0x94, 0x0, 0x1c, 0x6a, 0xd7,
    0xff, 0xf6, 0x9a, 0x4e, 0xa, 0x0, 0x0, 0x17,
    0xea, 0xe8, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x1,
    0xb5, 0xf3, 0x22, 0xd3, 0xe1, 0x10, 0x0, 0x0,
    0x2a, 0xf5, 0x70, 0x0, 0x3a, 0xff, 0x68, 0x0,
    0x0, 0x0, 0x24, 0x4, 0x0, 0x0, 0x37, 0x0,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x38, 0x94, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x60,
    0xff, 0x8c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x60, 0xff, 0x8c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x60, 0xff, 0x8c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x60, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0, 0x48,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x74, 0x38, 0xc8, 0xc8, 0xc8, 0xdd, 0xff,
    0xe7, 0xc8, 0xc8, 0xc8, 0x5b, 0x0, 0x0, 0x0,
    0x0, 0x60, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x60, 0xff, 0x8c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x60,
    0xff, 0x8c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x60, 0xff, 0x8c, 0x0, 0x0, 0x0,
    0x0,

    /* U+002C "," */
    0x0, 0xd8, 0xff, 0x14, 0x0, 0xdb, 0xff, 0x11,
    0x2, 0xf1, 0xf0, 0x1, 0x3d, 0xff, 0x99, 0x0,
    0x8d, 0xeb, 0x1b, 0x0, 0x2, 0x1d, 0x0, 0x0,

    /* U+002D "-" */
    0x44, 0x88, 0x88, 0x88, 0x88, 0x26, 0x80, 0xff,
    0xff, 0xff, 0xff, 0x48,

    /* U+002E "." */
    0x2, 0x4, 0x1, 0x94, 0xff, 0x58, 0x94, 0xff,
    0x58,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf4, 0xc,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf3, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x44, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0xe2, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xfe, 0x84, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7a, 0xff, 0x28, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd8, 0xc8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xff, 0x68, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x97, 0xf8, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xee, 0xab, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x54, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xe8, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xfc, 0x8c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x71, 0xff, 0x2e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x2, 0x6f, 0xd9, 0xfc, 0xed, 0xa4, 0x1c,
    0x0, 0x0, 0x0, 0x93, 0xff, 0xd9, 0x94, 0xb2,
    0xfe, 0xe4, 0x19, 0x0, 0x33, 0xff, 0xcb, 0x8,
    0x0, 0x0, 0x68, 0xff, 0x9d, 0x0, 0x8c, 0xff,
    0x58, 0x0, 0x0, 0x0, 0x3, 0xea, 0xf3, 0x5,
    0xbc, 0xff, 0x29, 0x0, 0x0, 0x0, 0x0, 0xbc,
    0xff, 0x27, 0xce, 0xff, 0x1d, 0x0, 0x0, 0x0,
    0x0, 0xb1, 0xff, 0x3a, 0xd0, 0xff, 0x1c, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0xff, 0x3c, 0xd0, 0xff,
    0x1c, 0x0, 0x0, 0x0, 0x0, 0xb0, 0xff, 0x3c,
    0xd0, 0xff, 0x1c, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0xff, 0x3c, 0xce, 0xff, 0x1d, 0x0, 0x0, 0x0,
    0x0, 0xb1, 0xff, 0x3a, 0xbc, 0xff, 0x29, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0xff, 0x28, 0x8d, 0xff,
    0x58, 0x0, 0x0, 0x0, 0x2, 0xea, 0xf5, 0x5,
    0x33, 0xff, 0xcb, 0x8, 0x0, 0x0, 0x63, 0xff,
    0xa1, 0x0, 0x0, 0x93, 0xff, 0xd8, 0x90, 0xac,
    0xfd, 0xe8, 0x1b, 0x0, 0x0, 0x1, 0x6e, 0xd6,
    0xfd, 0xee, 0xa7, 0x1f, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x8, 0x3b, 0x72, 0x2d, 0x0, 0x0, 0xb0,
    0xf9, 0xff, 0xff, 0x4c, 0x0, 0x0, 0x6e, 0x80,
    0xd0, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xa0,
    0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xa0, 0xff,
    0x4c, 0x0, 0x0, 0x0, 0x0, 0xa0, 0xff, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0xa0, 0xff, 0x4c, 0x0,
    0x0, 0x0, 0x0, 0xa0, 0xff, 0x4c, 0x0, 0x0,
    0x0, 0x0, 0xa0, 0xff, 0x4c, 0x0, 0x0, 0x0,
    0x0, 0xa0, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0,
    0xa0, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xa0,
    0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xa0, 0xff,
    0x4c, 0x0, 0x0, 0x43, 0x70, 0xd2, 0xff, 0xa6,
    0x64, 0x25, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7c,

    /* U+0032 "2" */
    0x0, 0x0, 0x32, 0xb0, 0xee, 0xfc, 0xde, 0x82,
    0x9, 0x0, 0x0, 0x0, 0x43, 0xf9, 0xf8, 0xa7,
    0x94, 0xd8, 0xff, 0xc0, 0x4, 0x0, 0x1, 0xdb,
    0xfd, 0x3a, 0x0, 0x0, 0x6, 0xc4, 0xff, 0x5d,
    0x0, 0x2b, 0xff, 0xbe, 0x0, 0x0, 0x0, 0x0,
    0x57, 0xff, 0x9e, 0x0, 0x22, 0x8c, 0x58, 0x0,
    0x0, 0x0, 0x0, 0x48, 0xff, 0xa4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x85, 0xff,
    0x6f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1b, 0xf1, 0xef, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xc2, 0xff, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xff, 0x9f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xff, 0xbe, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x57, 0xff, 0xd4, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0xf8, 0xe4, 0x1d, 0x0,
    0x0, 0x1d, 0x3c, 0x9, 0x0, 0x25, 0xec, 0xf1,
    0x2e, 0x0, 0x0, 0x0, 0x8a, 0xff, 0x28, 0x5,
    0xdb, 0xff, 0xc7, 0x90, 0x90, 0x90, 0x90, 0xd4,
    0xff, 0x28, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x28,

    /* U+0033 "3" */
    0x0, 0x0, 0x3f, 0xbb, 0xf3, 0xfc, 0xda, 0x7e,
    0x7, 0x0, 0x0, 0x55, 0xfd, 0xf5, 0xa3, 0x94,
    0xd9, 0xff, 0xb4, 0x1, 0x4, 0xe7, 0xfb, 0x30,
    0x0, 0x0, 0x7, 0xd1, 0xff, 0x3f, 0x23, 0xf4,
    0xb9, 0x0, 0x0, 0x0, 0x0, 0x7d, 0xff, 0x76,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b,
    0xff, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xc6, 0xfc, 0x25, 0x0, 0x0, 0x0, 0x3e,
    0x88, 0x8c, 0xcc, 0xfa, 0x65, 0x0, 0x0, 0x0,
    0x0, 0x74, 0xff, 0xff, 0xff, 0xd4, 0x36, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x3f, 0xe3,
    0xf6, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x66, 0xff, 0x89, 0x5, 0x14, 0xc, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0xff, 0xb0, 0x3c, 0xff,
    0xa9, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xa0,
    0xb, 0xf4, 0xf4, 0x25, 0x0, 0x0, 0x4, 0xbb,
    0xff, 0x57, 0x0, 0x66, 0xff, 0xf1, 0x9d, 0x90,
    0xd4, 0xff, 0xb8, 0x2, 0x0, 0x0, 0x47, 0xbe,
    0xf3, 0xfb, 0xd6, 0x79, 0x7, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6a, 0xff,
    0xec, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x15, 0xef, 0xff, 0xec, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0xfe, 0xff, 0xec, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xff, 0xae,
    0xff, 0xec, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xcf, 0xf5, 0x1f, 0xff, 0xec, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0xff, 0x77, 0x0, 0xff, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x17, 0xf0, 0xd3, 0x5,
    0x0, 0xff, 0xec, 0x0, 0x0, 0x0, 0x0, 0xa2,
    0xfe, 0x3a, 0x0, 0x0, 0xff, 0xec, 0x0, 0x0,
    0x0, 0x3c, 0xff, 0x99, 0x0, 0x0, 0x0, 0xff,
    0xec, 0x0, 0x0, 0x4, 0xd2, 0xea, 0x11, 0x0,
    0x0, 0x0, 0xff, 0xec, 0x0, 0x0, 0x56, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x35, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0xff, 0xf7, 0x88, 0x77, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xec, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0x81, 0xff, 0xf8,
    0x7e, 0x2e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98,
    0xff, 0xff, 0xff, 0xff, 0x84,

    /* U+0035 "5" */
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x36, 0xff, 0xe3, 0xc0, 0xc0,
    0xc0, 0xcb, 0xff, 0x70, 0x0, 0x51, 0xff, 0x7b,
    0x0, 0x0, 0x0, 0x10, 0xe0, 0x62, 0x0, 0x6a,
    0xff, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x84, 0xff, 0x46, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0xff, 0x57, 0x99, 0xb4,
    0x93, 0x2f, 0x0, 0x0, 0x0, 0xb7, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xfc, 0x59, 0x0, 0x0, 0xd1,
    0xfe, 0x5f, 0xb, 0xd, 0x75, 0xff, 0xf3, 0x13,
    0x0, 0x42, 0x4c, 0x0, 0x0, 0x0, 0x0, 0xac,
    0xff, 0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x64, 0xff, 0x8d, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5a, 0xff, 0x94, 0x7, 0xff,
    0xc3, 0x0, 0x0, 0x0, 0x0, 0x82, 0xff, 0x75,
    0x0, 0xd7, 0xfa, 0x2e, 0x0, 0x0, 0x15, 0xe5,
    0xfe, 0x2a, 0x0, 0x51, 0xff, 0xf3, 0x9d, 0x95,
    0xe6, 0xff, 0x8f, 0x0, 0x0, 0x0, 0x49, 0xc1,
    0xf6, 0xfa, 0xd2, 0x69, 0x1, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x19, 0x98, 0xe5, 0xfc, 0xe8, 0xa7,
    0x6, 0x0, 0x0, 0x22, 0xe7, 0xfd, 0xb3, 0x8e,
    0xa5, 0xca, 0x0, 0x0, 0x0, 0xc5, 0xfc, 0x47,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0x9f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8a, 0xff, 0x53, 0x0, 0x13, 0x1e, 0x3, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x71, 0xcc, 0xff, 0xff,
    0xed, 0x72, 0x0, 0x0, 0xcb, 0xff, 0xfd, 0x9c,
    0x5c, 0x70, 0xe3, 0xff, 0x74, 0x0, 0xd0, 0xff,
    0x5e, 0x0, 0x0, 0x0, 0x25, 0xf7, 0xf3, 0xd,
    0xd0, 0xff, 0x18, 0x0, 0x0, 0x0, 0x0, 0xab,
    0xff, 0x4c, 0xcb, 0xff, 0x1e, 0x0, 0x0, 0x0,
    0x0, 0x86, 0xff, 0x69, 0xb3, 0xff, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x8d, 0xff, 0x60, 0x78, 0xff,
    0x7f, 0x0, 0x0, 0x0, 0x0, 0xc3, 0xff, 0x34,
    0x1a, 0xf6, 0xf0, 0x24, 0x0, 0x0, 0x42, 0xff,
    0xd9, 0x1, 0x0, 0x63, 0xff, 0xf1, 0x99, 0x9d,
    0xf7, 0xf9, 0x40, 0x0, 0x0, 0x0, 0x46, 0xc2,
    0xf7, 0xf6, 0xbd, 0x38, 0x0, 0x0,

    /* U+0037 "7" */
    0x40, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5c, 0x40, 0xff, 0xc7, 0x8c, 0x8c,
    0x8c, 0x8c, 0x8c, 0xdd, 0xff, 0x48, 0x40, 0xff,
    0x74, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xfd, 0xa0,
    0x0, 0x12, 0x48, 0x1d, 0x0, 0x0, 0x0, 0xa,
    0xdd, 0xde, 0xc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x84, 0xff, 0x45, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x19, 0xf7, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0xff, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xed, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x7a, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9d,
    0xff, 0x37, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdd, 0xf9, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0xff, 0xac, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x8d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0xff, 0x74,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x17, 0x9b, 0xe7, 0xfe, 0xe8, 0x9c,
    0x1a, 0x0, 0x0, 0x0, 0x10, 0xdd, 0xff, 0xc8,
    0x91, 0xc8, 0xff, 0xe1, 0x14, 0x0, 0x0, 0x77,
    0xff, 0xab, 0x1, 0x0, 0x2, 0xaa, 0xff, 0x81,
    0x0, 0x0, 0xac, 0xff, 0x47, 0x0, 0x0, 0x0,
    0x3c, 0xff, 0xb8, 0x0, 0x0, 0xa6, 0xff, 0x47,
    0x0, 0x0, 0x0, 0x39, 0xff, 0xb2, 0x0, 0x0,
    0x5a, 0xff, 0xab, 0x1, 0x0, 0x1, 0x9f, 0xff,
    0x63, 0x0, 0x0, 0x1, 0x92, 0xff, 0xc9, 0x91,
    0xc3, 0xff, 0x96, 0x1, 0x0, 0x0, 0x0, 0x37,
    0xda, 0xff, 0xff, 0xff, 0xdc, 0x39, 0x0, 0x0,
    0x0, 0x45, 0xfa, 0xce, 0x32, 0x5, 0x30, 0xcb,
    0xfb, 0x4a, 0x0, 0x0, 0xd7, 0xfd, 0x1f, 0x0,
    0x0, 0x0, 0x19, 0xf9, 0xe0, 0x1, 0x11, 0xff,
    0xda, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcb, 0xff,
    0x1d, 0xb, 0xff, 0xeb, 0x1, 0x0, 0x0, 0x0,
    0x0, 0xdc, 0xff, 0x16, 0x0, 0xcc, 0xff, 0x65,
    0x0, 0x0, 0x0, 0x57, 0xff, 0xd5, 0x0, 0x0,
    0x3f, 0xf9, 0xff, 0xb7, 0x8c, 0xb2, 0xfe, 0xfa,
    0x44, 0x0, 0x0, 0x0, 0x31, 0xab, 0xeb, 0xfe,
    0xeb, 0xae, 0x34, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x13, 0x99, 0xeb, 0xfc, 0xd8, 0x7f,
    0x8, 0x0, 0x0, 0x0, 0x12, 0xdc, 0xff, 0xbd,
    0x94, 0xd2, 0xff, 0xbf, 0x5, 0x0, 0x0, 0x98,
    0xff, 0x8c, 0x0, 0x0, 0x2, 0xab, 0xff, 0x67,
    0x0, 0x5, 0xf3, 0xf6, 0xc, 0x0, 0x0, 0x0,
    0x28, 0xff, 0xc3, 0x0, 0x23, 0xff, 0xca, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xf6, 0xf1, 0x0, 0x26,
    0xff, 0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe9,
    0xff, 0x2, 0xb, 0xfd, 0xee, 0x4, 0x0, 0x0,
    0x0, 0x0, 0xea, 0xff, 0x4, 0x0, 0xba, 0xff,
    0x72, 0x0, 0x0, 0x0, 0x62, 0xff, 0xff, 0x4,
    0x0, 0x2c, 0xf3, 0xff, 0xb3, 0x91, 0xc0, 0xef,
    0xff, 0xff, 0x2, 0x0, 0x0, 0x2b, 0xae, 0xef,
    0xfb, 0xc9, 0x3f, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xff, 0xd2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6a, 0xff, 0x93, 0x0, 0x0, 0x0, 0x4, 0x0,
    0x0, 0x0, 0x1a, 0xe4, 0xfd, 0x2d, 0x0, 0x0,
    0x0, 0xb6, 0xb3, 0x8e, 0x98, 0xec, 0xff, 0x7c,
    0x0, 0x0, 0x0, 0x0, 0x87, 0xd9, 0xfa, 0xf8,
    0xc7, 0x55, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x94, 0xff, 0x58, 0x94, 0xff, 0x58, 0x2, 0x4,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x4, 0x1, 0x94, 0xff, 0x58, 0x94, 0xff,
    0x58,

    /* U+003B ";" */
    0x0, 0x94, 0xff, 0x58, 0x0, 0x94, 0xff, 0x58,
    0x0, 0x2, 0x4, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x44, 0x7c, 0x2f, 0x0, 0x8c, 0xff, 0x60,
    0x0, 0x95, 0xff, 0x55, 0x0, 0xc5, 0xfd, 0x1b,
    0x2d, 0xff, 0xa4, 0x0, 0x1e, 0x9f, 0x12, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19,
    0x88, 0xaa, 0x0, 0x0, 0x0, 0x0, 0x22, 0x96,
    0xf8, 0xff, 0xb5, 0x0, 0x0, 0x2c, 0xa4, 0xfc,
    0xff, 0xd5, 0x69, 0xb, 0x1a, 0xb3, 0xff, 0xfd,
    0xb1, 0x43, 0x1, 0x0, 0x0, 0x40, 0xff, 0xe2,
    0x5a, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x83,
    0xef, 0xff, 0xd3, 0x69, 0xd, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x77, 0xe7, 0xff, 0xf3, 0x95, 0x2a,
    0x0, 0x0, 0x0, 0x0, 0x8, 0x69, 0xde, 0xff,
    0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x5d, 0x8e,

    /* U+003D "=" */
    0x5a, 0xa0, 0xa0, 0xa0, 0xa0, 0xa0, 0xa0, 0xa0,
    0x5d, 0x90, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xa4, 0xa4, 0xa4,
    0xa4, 0xa4, 0xa4, 0xa4, 0x5f, 0x90, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x94,

    /* U+003E ">" */
    0x91, 0x55, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0xde, 0x6d, 0xb, 0x0, 0x0,
    0x0, 0x0, 0x25, 0x8c, 0xeb, 0xff, 0xef, 0x87,
    0x1a, 0x0, 0x0, 0x0, 0x0, 0x7, 0x59, 0xc0,
    0xff, 0xfa, 0xa0, 0x27, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x31, 0xc9, 0xff, 0x94, 0x0, 0x0, 0x0,
    0x2a, 0x92, 0xf0, 0xff, 0xd4, 0x46, 0x7, 0x5a,
    0xc2, 0xff, 0xff, 0xbd, 0x48, 0x1, 0x0, 0xbf,
    0xff, 0xfb, 0xa4, 0x31, 0x0, 0x0, 0x0, 0x0,
    0xb9, 0x8d, 0x1c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x7, 0x81, 0xde, 0xfc, 0xef, 0xab, 0x24,
    0x0, 0x0, 0xb2, 0xff, 0xe8, 0xad, 0xd2, 0xff,
    0xea, 0x17, 0x33, 0xff, 0xd5, 0xc, 0x0, 0x1,
    0xa2, 0xff, 0x7e, 0x39, 0xa4, 0x5a, 0x0, 0x0,
    0x0, 0x46, 0xff, 0xaa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4c, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0x63, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0xfe, 0xd5, 0x6, 0x0,
    0x0, 0x0, 0x0, 0x43, 0xf6, 0xef, 0x27, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xf4, 0xf8, 0x3e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x68, 0xff, 0x93, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6a, 0xdc, 0x62,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x14, 0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x80, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x80, 0xff, 0x80, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x90, 0xd6,
    0xf8, 0xfd, 0xe6, 0xad, 0x4c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x78, 0xfb, 0xc2,
    0x67, 0x3d, 0x39, 0x55, 0x96, 0xf4, 0xb2, 0xd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8b, 0xf8, 0x5d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xce,
    0xbb, 0x2, 0x0, 0x0, 0x0, 0x52, 0xff, 0x53,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xec, 0x6a, 0x0, 0x0, 0x6, 0xe1, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xa, 0x0, 0x0,
    0x0, 0x0, 0x73, 0xe1, 0x2, 0x0, 0x58, 0xff,
    0x26, 0x0, 0x0, 0x5, 0x91, 0xf7, 0xff, 0xdc,
    0x66, 0x1, 0x0, 0x15, 0xfe, 0x39, 0x0, 0xab,
    0xcc, 0x0, 0x0, 0x0, 0x9a, 0xfc, 0x80, 0x46,
    0xac, 0xff, 0x15, 0x0, 0x0, 0xd6, 0x72, 0x0,
    0xe6, 0x8c, 0x0, 0x0, 0x2f, 0xff, 0x87, 0x0,
    0x0, 0x81, 0xfd, 0x3, 0x0, 0x0, 0xb3, 0x94,
    0xe, 0xff, 0x64, 0x0, 0x0, 0x8f, 0xff, 0x22,
    0x0, 0x0, 0x97, 0xea, 0x0, 0x0, 0x0, 0xa3,
    0xa4, 0x24, 0xff, 0x4f, 0x0, 0x0, 0xce, 0xe7,
    0x0, 0x0, 0x0, 0xac, 0xd4, 0x0, 0x0, 0x0,
    0xa6, 0xa3, 0x28, 0xff, 0x4b, 0x0, 0x0, 0xf3,
    0xc5, 0x0, 0x0, 0x0, 0xc3, 0xbe, 0x0, 0x0,
    0x0, 0xbc, 0x8c, 0x1c, 0xff, 0x57, 0x0, 0x0,
    0xfe, 0xbc, 0x0, 0x0, 0x0, 0xd8, 0xa8, 0x0,
    0x0, 0x6, 0xef, 0x57, 0x4, 0xf9, 0x7a, 0x0,
    0x0, 0xe6, 0xe0, 0x1, 0x0, 0x16, 0xf5, 0xa0,
    0x0, 0x0, 0x64, 0xf0, 0xc, 0x0, 0xc5, 0xba,
    0x0, 0x0, 0x99, 0xff, 0xa4, 0x86, 0xdd, 0xd9,
    0xe5, 0x40, 0x6a, 0xf4, 0x60, 0x0, 0x0, 0x70,
    0xfc, 0x22, 0x0, 0x15, 0xc1, 0xfc, 0xd3, 0x4b,
    0x2a, 0xd7, 0xfa, 0xcf, 0x55, 0x0, 0x0, 0x0,
    0xc, 0xe9, 0xb4, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x48, 0xfc, 0x9b, 0x7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xf0, 0xdd, 0x78,
    0x40, 0x2f, 0x41, 0x72, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0x80,
    0xcb, 0xf3, 0xfc, 0xea, 0xbb, 0x64, 0x2, 0x0,
    0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b, 0xff,
    0x8a, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xe4,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x35, 0xff, 0xf3, 0xff, 0x42,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x93, 0xff, 0x67, 0xff, 0x9e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xeb, 0xe2, 0x1, 0xd9, 0xf2, 0x8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d,
    0xff, 0x8d, 0x0, 0x85, 0xff, 0x55, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xff,
    0x37, 0x0, 0x31, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0xf8, 0xdf, 0x1,
    0x0, 0x0, 0xdd, 0xfa, 0x13, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x66, 0xff, 0x8a, 0x0, 0x0,
    0x0, 0x88, 0xff, 0x69, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc4, 0xff, 0xc1, 0xa4, 0xa4, 0xa4,
    0xc1, 0xff, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x22, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xff, 0x78, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b,
    0xff, 0x7d, 0x0, 0x0, 0x0, 0x0, 0xdb, 0xfd,
    0x1b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfe,
    0xd9, 0x0, 0x0, 0x14, 0x6e, 0xff, 0xdf, 0x36,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xe4, 0xff,
    0x6b, 0x11, 0x50, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcc, 0xff, 0xff, 0xff,
    0x48,

    /* U+0042 "B" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xd6, 0x83, 0xc, 0x0, 0x0, 0xf, 0x64, 0xcf,
    0xff, 0xb0, 0x8c, 0x8c, 0x99, 0xda, 0xff, 0xcf,
    0x8, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x5, 0xb8, 0xff, 0x66, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x54, 0xff, 0x9e, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x52, 0xff, 0x9d,
    0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x2, 0xb2, 0xff, 0x48, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0xb0, 0x8c, 0x8c, 0x90, 0xcc, 0xf8,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xec, 0x7d, 0x3, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x17, 0x9c, 0xff, 0x8a, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5, 0xea,
    0xf9, 0xf, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbb, 0xff, 0x37, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xca, 0xff, 0x2e, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0xec,
    0x6, 0xe, 0x60, 0xce, 0xff, 0xae, 0x88, 0x88,
    0x8a, 0xab, 0xfa, 0xfe, 0x5d, 0x0, 0x34, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xb6,
    0x3f, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0xa, 0x78, 0xd0, 0xf6, 0xfa,
    0xda, 0x92, 0x23, 0x0, 0x0, 0x0, 0x1d, 0xd6,
    0xff, 0xe7, 0xac, 0xa7, 0xcf, 0xff, 0xf9, 0x4b,
    0x0, 0x6, 0xd0, 0xff, 0x8a, 0x5, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x78, 0x0, 0x68, 0xff, 0xae,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0x78,
    0x0, 0xcc, 0xff, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0xb4, 0x54, 0xd, 0xfe, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0xff, 0xba, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd1, 0xff, 0x2b, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x3c, 0x19, 0x0, 0x72, 0xff, 0xa8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xff, 0x6c,
    0x0, 0xa, 0xd9, 0xff, 0x88, 0x6, 0x0, 0x0,
    0x0, 0x4d, 0xff, 0x6c, 0x0, 0x0, 0x23, 0xdc,
    0xff, 0xe8, 0xaa, 0x9c, 0xc0, 0xfb, 0xff, 0x6b,
    0x0, 0x0, 0x0, 0xd, 0x79, 0xcd, 0xf5, 0xfd,
    0xe8, 0xb7, 0x62, 0x6,

    /* U+0044 "D" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe3,
    0x9f, 0x27, 0x0, 0x0, 0x0, 0xf, 0x64, 0xcf,
    0xff, 0xb0, 0x8c, 0x8f, 0xba, 0xfd, 0xf8, 0x56,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x37, 0xec, 0xfb, 0x37, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x4a, 0xff, 0xc9, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce, 0xff,
    0x2f, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x81, 0xff, 0x6f, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5b, 0xff, 0x91, 0x0, 0x0, 0x9c, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x52, 0xff, 0x9a,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0x91, 0x0, 0x0, 0x9c,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x85,
    0xff, 0x6f, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd2, 0xff, 0x30, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x52, 0xff, 0xcd, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x3b, 0xef, 0xfd, 0x3e,
    0x0, 0xe, 0x60, 0xce, 0xff, 0xae, 0x88, 0x8b,
    0xb7, 0xfd, 0xfb, 0x60, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xe7, 0xa7, 0x2f,
    0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0xf, 0x64, 0xcf, 0xff,
    0xab, 0x84, 0x84, 0x84, 0x84, 0xb8, 0xff, 0x50,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x56, 0xff, 0x50, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x24, 0x94, 0x2e,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xb0, 0x8c, 0x8c, 0x8c,
    0x8c, 0x4d, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8c, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x5d,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xa0, 0xf, 0x64, 0xcf, 0xff,
    0xa5, 0x7c, 0x7c, 0x7c, 0x7c, 0x88, 0xff, 0xa0,
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0,

    /* U+0046 "F" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x7c, 0xf, 0x64, 0xcf, 0xff,
    0xae, 0x88, 0x88, 0x88, 0x88, 0xa5, 0xff, 0x7c,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x2c, 0xff, 0x7c, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x10, 0xac, 0x53,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x84, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xb0, 0x8c, 0x8c, 0x8c,
    0x8c, 0x48, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x64, 0xcf, 0xff,
    0xa5, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x34, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0xa, 0x7b, 0xd5, 0xfa, 0xf9,
    0xdf, 0xa6, 0x3f, 0x0, 0x0, 0x0, 0x1b, 0xd5,
    0xff, 0xeb, 0xaa, 0xa1, 0xc3, 0xfc, 0xff, 0x7c,
    0x0, 0x5, 0xcc, 0xff, 0x9e, 0xa, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xa0, 0x0, 0x66, 0xff, 0xbd,
    0x2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xa0,
    0x0, 0xcc, 0xff, 0x3b, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x75, 0x4b, 0xd, 0xfe, 0xec, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0xff, 0xba, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0xac,
    0xff, 0xff, 0xff, 0xf8, 0xd, 0xfe, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x56, 0x80, 0x80, 0xfa, 0xf8,
    0x0, 0xcd, 0xff, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0xf8, 0x0, 0x68, 0xff, 0xae,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0xf8,
    0x0, 0x5, 0xce, 0xff, 0x8d, 0x6, 0x0, 0x0,
    0x0, 0x8, 0xf6, 0xf8, 0x0, 0x0, 0x1a, 0xd2,
    0xff, 0xe8, 0xa8, 0x9b, 0xb2, 0xef, 0xff, 0xc9,
    0x0, 0x0, 0x0, 0x7, 0x6f, 0xc9, 0xf5, 0xfe,
    0xec, 0xbe, 0x63, 0x5,

    /* U+0048 "H" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x0, 0x0,
    0x0, 0x7c, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x13,
    0x7c, 0xda, 0xff, 0xb8, 0x65, 0x0, 0x0, 0x0,
    0x31, 0x8a, 0xf6, 0xff, 0x94, 0x40, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe4, 0xff, 0x8, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe4, 0xff, 0x8, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe4,
    0xff, 0x8, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe4, 0xff,
    0x8, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe4, 0xff, 0x8,
    0x0, 0x0, 0x0, 0x9c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xb0, 0x8c, 0x8c, 0x8c,
    0x8c, 0x8c, 0x8c, 0xf3, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe4, 0xff, 0x8, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe4, 0xff, 0x8, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe4, 0xff, 0x8, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe4,
    0xff, 0x8, 0x0, 0xf, 0x64, 0xcf, 0xff, 0xa5,
    0x50, 0x0, 0x0, 0x0, 0x26, 0x72, 0xf3, 0xff,
    0x7a, 0x34, 0x34, 0xff, 0xff, 0xff, 0xff, 0xe8,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0xff, 0xff, 0xff,
    0xa0,

    /* U+0049 "I" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x11, 0x70,
    0xd4, 0xff, 0xae, 0x5b, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x10, 0x6e,
    0xd4, 0xff, 0xad, 0x57, 0x34, 0xff, 0xff, 0xff,
    0xff, 0xe8,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x79, 0xe6, 0xff, 0x95, 0x45, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc4, 0xff, 0x28,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc4, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc4, 0xff, 0x28, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc4, 0xff,
    0x28, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc4, 0xff, 0x28, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0xff, 0x28, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc4,
    0xff, 0x28, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc4, 0xff, 0x28, 0x0, 0x2b, 0x70,
    0x35, 0x0, 0x0, 0x0, 0x0, 0xc5, 0xff, 0x27,
    0x0, 0x4d, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xe0, 0xff, 0x15, 0x0, 0xf, 0xf5, 0xe6, 0x13,
    0x0, 0x0, 0x45, 0xff, 0xd5, 0x0, 0x0, 0x0,
    0x6d, 0xff, 0xe6, 0x9b, 0xa6, 0xf8, 0xfd, 0x4d,
    0x0, 0x0, 0x0, 0x0, 0x55, 0xca, 0xf9, 0xf5,
    0xc2, 0x44, 0x0, 0x0, 0x0,

    /* U+004B "K" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x0, 0x0,
    0x20, 0xff, 0xff, 0xff, 0xff, 0x6c, 0x11, 0x70,
    0xd4, 0xff, 0xae, 0x5b, 0x0, 0x0, 0xa, 0x8e,
    0xff, 0xf5, 0x80, 0x27, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x17, 0xe0, 0xfd, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x9, 0xc9, 0xff, 0x69, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x1, 0xaa,
    0xff, 0x89, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x87, 0xff, 0xa7, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x60, 0xff, 0xea, 0x8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x92, 0xfa,
    0xff, 0xff, 0x6b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xff, 0xe8, 0x42, 0xf4,
    0xf6, 0x29, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0xf4, 0x34, 0x0, 0x68, 0xff, 0xce,
    0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x6c, 0x0, 0x0, 0x1, 0xb8, 0xff, 0x8a, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x1b, 0xef, 0xfe, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xe4, 0x12, 0x0, 0x10, 0x6e,
    0xd4, 0xff, 0xad, 0x57, 0x0, 0x0, 0x0, 0x47,
    0xe4, 0xff, 0xc1, 0x5e, 0x34, 0xff, 0xff, 0xff,
    0xff, 0xe8, 0x0, 0x0, 0x0, 0xd0, 0xff, 0xff,
    0xff, 0xf0,

    /* U+004C "L" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x11, 0x72, 0xd5, 0xff,
    0xb0, 0x5b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xe0, 0x7,
    0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x90, 0xff, 0x8, 0xe, 0x60, 0xce, 0xff,
    0xa8, 0x80, 0x80, 0x80, 0x80, 0xd1, 0xff, 0x8,
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8,

    /* U+004D "M" */
    0x14, 0xff, 0xff, 0xff, 0xff, 0x46, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0xf8, 0xff,
    0xff, 0xff, 0x70, 0x6, 0x6a, 0xc4, 0xff, 0xff,
    0xad, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xff, 0xff, 0xef, 0x7a, 0x28, 0x0, 0x0,
    0x7c, 0xff, 0xfb, 0xfc, 0x19, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd3, 0xfb, 0xff, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0xab, 0xff, 0x7c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0xff, 0xac,
    0xfc, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff,
    0x50, 0xf5, 0xe1, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x99, 0xff, 0x46, 0xfc, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0xff, 0x44, 0x9b, 0xff, 0x4a, 0x0,
    0x0, 0x0, 0xb, 0xf2, 0xe0, 0x2, 0xfc, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0x48, 0x32,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x60, 0xff, 0x7d,
    0x0, 0xfc, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xff, 0x4b, 0x0, 0xca, 0xfc, 0x1c, 0x0, 0x0,
    0xc4, 0xfd, 0x1c, 0x0, 0xfc, 0xd8, 0x0, 0x0,
    0x0, 0x0, 0x7c, 0xff, 0x4e, 0x0, 0x61, 0xff,
    0x7e, 0x0, 0x28, 0xff, 0xb3, 0x0, 0x0, 0xfc,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0x50,
    0x0, 0xa, 0xef, 0xe3, 0x3, 0x8c, 0xff, 0x4e,
    0x0, 0x0, 0xfc, 0xd8, 0x0, 0x0, 0x0, 0x0,
    0x7c, 0xff, 0x50, 0x0, 0x0, 0x8f, 0xff, 0x51,
    0xea, 0xe5, 0x4, 0x0, 0x0, 0xfc, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0x50, 0x0, 0x0,
    0x28, 0xff, 0xea, 0xff, 0x83, 0x0, 0x0, 0x0,
    0xfc, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0xbe, 0xff, 0xfe, 0x21,
    0x0, 0x0, 0x0, 0xfc, 0xd8, 0x0, 0x0, 0x6,
    0x66, 0xc2, 0xff, 0xac, 0x62, 0x2, 0x0, 0x56,
    0xff, 0xba, 0x0, 0x0, 0x3d, 0x7d, 0xfe, 0xef,
    0x76, 0x26, 0x14, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8, 0x0, 0x5, 0x77, 0x34, 0x0, 0x0, 0xac,
    0xff, 0xff, 0xff, 0xff, 0x70,

    /* U+004E "N" */
    0x30, 0xff, 0xff, 0xff, 0xa3, 0x0, 0x0, 0x0,
    0x0, 0x60, 0xff, 0xff, 0xff, 0xff, 0xbc, 0x10,
    0x6f, 0xd2, 0xff, 0xff, 0x3d, 0x0, 0x0, 0x0,
    0x21, 0x74, 0xd6, 0xff, 0x96, 0x47, 0x0, 0x0,
    0x98, 0xff, 0xff, 0xd2, 0x4, 0x0, 0x0, 0x0,
    0x0, 0xa0, 0xff, 0x24, 0x0, 0x0, 0x0, 0x98,
    0xff, 0xc9, 0xff, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0xa0, 0xff, 0x24, 0x0, 0x0, 0x0, 0x98, 0xff,
    0x40, 0xec, 0xf0, 0x16, 0x0, 0x0, 0x0, 0xa0,
    0xff, 0x24, 0x0, 0x0, 0x0, 0x98, 0xff, 0x2c,
    0x65, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xa0, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x98, 0xff, 0x2c, 0x2,
    0xc7, 0xff, 0x39, 0x0, 0x0, 0xa0, 0xff, 0x24,
    0x0, 0x0, 0x0, 0x98, 0xff, 0x2c, 0x0, 0x31,
    0xfd, 0xce, 0x3, 0x0, 0xa0, 0xff, 0x24, 0x0,
    0x0, 0x0, 0x98, 0xff, 0x2c, 0x0, 0x0, 0x92,
    0xff, 0x6a, 0x0, 0xa0, 0xff, 0x24, 0x0, 0x0,
    0x0, 0x98, 0xff, 0x2c, 0x0, 0x0, 0xf, 0xe8,
    0xef, 0x14, 0xa0, 0xff, 0x24, 0x0, 0x0, 0x0,
    0x98, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x59, 0xff,
    0x9a, 0xa0, 0xff, 0x24, 0x0, 0x0, 0x0, 0x98,
    0xff, 0x2c, 0x0, 0x0, 0x0, 0x0, 0xbe, 0xfe,
    0xd3, 0xff, 0x24, 0x0, 0x0, 0x0, 0x98, 0xff,
    0x2c, 0x0, 0x0, 0x0, 0x0, 0x28, 0xfa, 0xff,
    0xff, 0x24, 0x0, 0xf, 0x6d, 0xd2, 0xff, 0x9b,
    0x59, 0x0, 0x0, 0x0, 0x0, 0x86, 0xff, 0xff,
    0x24, 0x0, 0x30, 0xff, 0xff, 0xff, 0xff, 0xec,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xe0, 0xff, 0x24,
    0x0,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x20, 0x9c, 0xe4, 0xfc, 0xee,
    0xb4, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0xf3, 0xff, 0xd2, 0xa8, 0xbf, 0xfc, 0xff, 0x78,
    0x0, 0x0, 0x0, 0x17, 0xed, 0xfb, 0x57, 0x0,
    0x0, 0x0, 0x29, 0xe0, 0xff, 0x48, 0x0, 0x0,
    0x8f, 0xff, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3b, 0xff, 0xcd, 0x0, 0x1, 0xea, 0xfc, 0x13,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xff,
    0x24, 0x26, 0xff, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x93, 0xff, 0x5d, 0x45, 0xff,
    0xab, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x76, 0xff, 0x7a, 0x4f, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0x82,
    0x45, 0xff, 0xab, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x76, 0xff, 0x7a, 0x27, 0xff, 0xcb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x93,
    0xff, 0x5d, 0x2, 0xeb, 0xfc, 0x12, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x26, 0x0,
    0x91, 0xff, 0x7d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0xff, 0xcd, 0x0, 0x0, 0x19, 0xee, 0xfa,
    0x53, 0x0, 0x0, 0x0, 0x23, 0xdc, 0xff, 0x49,
    0x0, 0x0, 0x0, 0x42, 0xf3, 0xff, 0xce, 0xa4,
    0xba, 0xfa, 0xff, 0x7a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x21, 0x9c, 0xe4, 0xfd, 0xee, 0xb6, 0x41,
    0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xe4, 0x9c, 0x1f, 0x0, 0x0, 0xf, 0x64, 0xcf,
    0xff, 0xb0, 0x8c, 0x8c, 0x91, 0xc3, 0xff, 0xed,
    0x29, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x7a, 0xff, 0xb8, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xef, 0xfd, 0xa, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd1, 0xff,
    0x1e, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xee, 0xfd, 0xa, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x73,
    0xff, 0xba, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xae,
    0x88, 0x88, 0x8c, 0xbd, 0xff, 0xee, 0x2b, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xe5, 0x9f, 0x21, 0x0, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x6c, 0xd2, 0xff, 0xab, 0x57, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0xff,
    0xff, 0xff, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x23, 0x9f, 0xe5, 0xfc, 0xed,
    0xb2, 0x3f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x42, 0xf4, 0xff, 0xd1, 0xa8, 0xc0, 0xfc, 0xff,
    0x75, 0x0, 0x0, 0x0, 0x0, 0x17, 0xed, 0xfb,
    0x57, 0x0, 0x0, 0x0, 0x29, 0xe2, 0xff, 0x43,
    0x0, 0x0, 0x0, 0x8e, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x40, 0xff, 0xc6, 0x0, 0x0,
    0x1, 0xe9, 0xfa, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd4, 0xff, 0x1f, 0x0, 0x26, 0xff,
    0xca, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x97, 0xff, 0x55, 0x0, 0x45, 0xff, 0xa7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0xff,
    0x72, 0x0, 0x4f, 0xff, 0x9e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x72, 0xff, 0x7a, 0x0,
    0x45, 0xff, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7b, 0xff, 0x74, 0x0, 0x26, 0xff,
    0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x98, 0xff, 0x5c, 0x0, 0x2, 0xeb, 0xfa, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0xff,
    0x31, 0x0, 0x0, 0x8f, 0xff, 0x7a, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xe7, 0x3, 0x0,
    0x0, 0x17, 0xed, 0xf9, 0x51, 0x0, 0x0, 0x0,
    0x29, 0xe2, 0xff, 0x6b, 0x0, 0x0, 0x0, 0x0,
    0x40, 0xf3, 0xff, 0xcb, 0xa4, 0xbd, 0xfb, 0xff,
    0xa7, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x9c, 0xe5, 0xfd, 0xed, 0xc6, 0xff, 0xf5, 0x67,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x51, 0xef, 0xff, 0xb1, 0x7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x21, 0xc7, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9, 0x0,

    /* U+0052 "R" */
    0x34, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xe0, 0x97, 0x1c, 0x0, 0x0, 0xe, 0x60, 0xce,
    0xff, 0xae, 0x88, 0x88, 0x8f, 0xc8, 0xff, 0xe9,
    0x20, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x8d, 0xff, 0x9d, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xff, 0xdf, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xec,
    0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xff, 0xcc, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0x50, 0x0, 0x0, 0xb, 0x41, 0xd9,
    0xff, 0x6d, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x98, 0x3, 0x0,
    0x0, 0x0, 0x9c, 0xff, 0xa8, 0x80, 0x80, 0xc7,
    0xff, 0x67, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9c,
    0xff, 0x50, 0x0, 0x0, 0x3a, 0xff, 0xba, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xcb, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x50, 0x0, 0x0, 0x0, 0x5b,
    0xff, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x5, 0xe5, 0xfb, 0x1c,
    0x0, 0xe, 0x60, 0xce, 0xff, 0xa3, 0x4d, 0x0,
    0x0, 0x0, 0x7a, 0xff, 0xb0, 0x2e, 0x34, 0xff,
    0xff, 0xff, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x15,
    0xf8, 0xff, 0x8c,

    /* U+0053 "S" */
    0x0, 0xe, 0x85, 0xda, 0xfb, 0xf8, 0xd2, 0x83,
    0x15, 0x0, 0x11, 0xd5, 0xff, 0xd6, 0x9b, 0x9f,
    0xd8, 0xff, 0xf0, 0x36, 0x8c, 0xff, 0x99, 0x2,
    0x0, 0x0, 0x1, 0x8b, 0xff, 0x60, 0xd1, 0xff,
    0x1e, 0x0, 0x0, 0x0, 0x0, 0x47, 0xff, 0x60,
    0xd2, 0xff, 0x29, 0x0, 0x0, 0x0, 0x0, 0x15,
    0x80, 0x30, 0x8a, 0xff, 0xc5, 0x1f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xcb, 0xff, 0xfb,
    0xb5, 0x6c, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x7c, 0xe7, 0xff, 0xff, 0xfc, 0xa2, 0x16, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x38, 0x80, 0xdf, 0xff,
    0xe0, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xa5, 0xff, 0x9b, 0x9e, 0x79, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0xff, 0xde, 0xe0, 0xc9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xdb,
    0xe0, 0xf0, 0x1f, 0x0, 0x0, 0x0, 0x2, 0x9a,
    0xff, 0x95, 0xa1, 0xff, 0xfc, 0xc0, 0x97, 0x9b,
    0xda, 0xff, 0xd8, 0x13, 0x0, 0x3f, 0xa1, 0xdf,
    0xfb, 0xf8, 0xd3, 0x80, 0xe, 0x0,

    /* U+0054 "T" */
    0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x10, 0xff, 0xd1,
    0x7c, 0x7c, 0x8a, 0xff, 0xe7, 0x7c, 0x7c, 0x7c,
    0xf8, 0xc0, 0x10, 0xff, 0x88, 0x0, 0x0, 0x1c,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xd3, 0xc0, 0x9,
    0x94, 0x40, 0x0, 0x0, 0x1c, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x6a, 0x6f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x41, 0x91, 0xff,
    0xec, 0x76, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0xff, 0xff, 0xff, 0x68,
    0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x50, 0xff, 0xff, 0xff, 0xff, 0xcc, 0x0, 0x0,
    0x0, 0xec, 0xff, 0xff, 0xff, 0xff, 0x30, 0x1a,
    0x72, 0xe0, 0xff, 0x9b, 0x4c, 0x0, 0x0, 0x0,
    0x59, 0xae, 0xff, 0xd1, 0x6b, 0xf, 0x0, 0x0,
    0xb8, 0xff, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x54, 0xff, 0x98, 0x0, 0x0, 0x0, 0x0, 0xb8,
    0xff, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0x54,
    0xff, 0x98, 0x0, 0x0, 0x0, 0x0, 0xb8, 0xff,
    0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0x54, 0xff,
    0x98, 0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0x34,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x54, 0xff, 0x98,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0xff, 0x34, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x54, 0xff, 0x98, 0x0,
    0x0, 0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x54, 0xff, 0x98, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x54, 0xff, 0x98, 0x0, 0x0, 0x0,
    0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x54, 0xff, 0x98, 0x0, 0x0, 0x0, 0x0,
    0xb1, 0xff, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0, 0x86,
    0xff, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94,
    0xff, 0x69, 0x0, 0x0, 0x0, 0x0, 0x29, 0xfc,
    0xea, 0x26, 0x0, 0x0, 0x0, 0x40, 0xf9, 0xf6,
    0x18, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff,
    0xf7, 0xae, 0x94, 0xb9, 0xfe, 0xfd, 0x5d, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xb2,
    0xee, 0xfd, 0xe9, 0xab, 0x37, 0x0, 0x0, 0x0,
    0x0,

    /* U+0056 "V" */
    0x5c, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x64, 0xff, 0xff, 0xff, 0xff, 0x18, 0x1f,
    0x84, 0xff, 0xf4, 0x6b, 0xa, 0x0, 0x0, 0x0,
    0x22, 0x74, 0xfe, 0xee, 0x67, 0x8, 0x0, 0x1,
    0xe0, 0xff, 0x27, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xff, 0x98, 0x0, 0x0, 0x0, 0x0, 0x85,
    0xff, 0x7c, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa0,
    0xff, 0x3e, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf1, 0xe3,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd2, 0xff,
    0x27, 0x0, 0x0, 0x0, 0x52, 0xff, 0x8a, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x77, 0xff, 0x7c,
    0x0, 0x0, 0x0, 0xaa, 0xff, 0x2e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xd3, 0x0,
    0x0, 0xc, 0xf6, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc3, 0xff, 0x28, 0x0,
    0x5b, 0xff, 0x7b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x69, 0xff, 0x7d, 0x0, 0xb4,
    0xff, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0xfb, 0xd3, 0x13, 0xfb, 0xc6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x82, 0xff, 0x6c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xf7, 0xfc, 0x16, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf4, 0xff, 0xb7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa6, 0xff, 0x5d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0057 "W" */
    0x4c, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x89, 0xff, 0x72, 0x0, 0x0, 0x0, 0x70,
    0xff, 0xff, 0xff, 0xff, 0x18, 0x19, 0x70, 0xff,
    0xf4, 0x72, 0x1b, 0x0, 0x0, 0x0, 0xd4, 0xff,
    0xbd, 0x0, 0x0, 0x0, 0x27, 0x74, 0xf4, 0xf4,
    0x65, 0x8, 0x0, 0x0, 0xd8, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xfa, 0xf, 0x0,
    0x0, 0x0, 0x10, 0xfe, 0xbb, 0x0, 0x0, 0x0,
    0x0, 0x99, 0xff, 0x49, 0x0, 0x0, 0x0, 0x69,
    0xff, 0xbd, 0xff, 0x54, 0x0, 0x0, 0x0, 0x49,
    0xff, 0x7d, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xff,
    0x81, 0x0, 0x0, 0x0, 0xb4, 0xfe, 0x3c, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x81, 0xff, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xbb, 0x0, 0x0,
    0x9, 0xf6, 0xc7, 0x0, 0xdd, 0xe9, 0x2, 0x0,
    0x0, 0xbb, 0xfb, 0xa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdc, 0xf1, 0x2, 0x0, 0x49, 0xff, 0x7b,
    0x0, 0x91, 0xff, 0x37, 0x0, 0x2, 0xf2, 0xc9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x2d, 0x0, 0x94, 0xff, 0x2e, 0x0, 0x45, 0xff,
    0x83, 0x0, 0x2d, 0xff, 0x8c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x60, 0xff, 0x66, 0x0, 0xdf,
    0xe1, 0x0, 0x0, 0x6, 0xf3, 0xce, 0x0, 0x66,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xff, 0x9f, 0x2a, 0xff, 0x95, 0x0, 0x0,
    0x0, 0xad, 0xff, 0x1a, 0x9f, 0xff, 0x15, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe3, 0xd9,
    0x74, 0xff, 0x48, 0x0, 0x0, 0x0, 0x61, 0xff,
    0x62, 0xdc, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa4, 0xff, 0xd2, 0xf5, 0x8,
    0x0, 0x0, 0x0, 0x18, 0xfe, 0xc4, 0xff, 0x9b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x64, 0xff, 0xff, 0xaf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xca, 0xff, 0xff, 0x5f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xff, 0xff,
    0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xff,
    0xff, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe7, 0xfe, 0x18, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x32, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0x0, 0xe8, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0xe0, 0xff, 0xff, 0xff, 0xc4, 0x0, 0x0,
    0x5b, 0xc2, 0xff, 0xba, 0x3e, 0x0, 0x0, 0x0,
    0x4e, 0xd1, 0xff, 0xa8, 0x4a, 0x0, 0x0, 0x0,
    0x16, 0xe9, 0xf4, 0x22, 0x0, 0x0, 0x0, 0x33,
    0xfc, 0xcf, 0x5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4d, 0xff, 0xc3, 0x2, 0x0, 0x5, 0xd0, 0xfb,
    0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9a, 0xff, 0x73, 0x0, 0x79, 0xff, 0x84, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xdb, 0xf8, 0x51, 0xf8, 0xd4, 0x6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x37,
    0xfc, 0xff, 0xfd, 0x34, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc3,
    0xff, 0xcb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x63, 0xff, 0xef,
    0xff, 0x65, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0xf4, 0xe0, 0x1e, 0xe4,
    0xf4, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xce, 0xfe, 0x3f, 0x0, 0x43, 0xff,
    0xc8, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x92, 0xff,
    0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff,
    0xd3, 0x7, 0x0, 0x0, 0x0, 0x9, 0xd8, 0xfc,
    0x3a, 0x0, 0x0, 0x12, 0x6f, 0xea, 0xff, 0x94,
    0x24, 0x0, 0x0, 0x0, 0x2b, 0x95, 0xff, 0xde,
    0x6a, 0xc, 0x38, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x84, 0xff, 0xff, 0xff, 0xff,
    0x28,

    /* U+0059 "Y" */
    0x5c, 0xff, 0xff, 0xff, 0xff, 0x88, 0x0, 0x0,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xa4, 0x1f, 0x7c,
    0xfe, 0xfe, 0x78, 0x2f, 0x0, 0x0, 0xe, 0x63,
    0xcb, 0xff, 0x9a, 0x3c, 0x0, 0x0, 0xa4, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x14, 0xf2, 0xd5,
    0x3, 0x0, 0x0, 0x0, 0x21, 0xf9, 0xe7, 0xa,
    0x0, 0x0, 0x0, 0x90, 0xff, 0x4d, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x76, 0x0, 0x0,
    0x20, 0xfa, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0xf0, 0xec, 0xc, 0x0, 0xa4, 0xff,
    0x37, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x79, 0xff, 0x7c, 0x2f, 0xfe, 0xaa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe4,
    0xee, 0xbf, 0xfb, 0x25, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x62, 0xff, 0xff,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xe2, 0xff, 0x23, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0x18, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0xff, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xff,
    0x18, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x22, 0x76, 0xec, 0xff, 0x8e, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x68, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0,

    /* U+005A "Z" */
    0x0, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x7c, 0x0, 0xf8, 0xe0, 0x8c, 0x8c,
    0x8c, 0x8c, 0x8c, 0xf2, 0xff, 0x65, 0x0, 0xf8,
    0xa8, 0x0, 0x0, 0x0, 0x0, 0x54, 0xff, 0xd3,
    0x4, 0x0, 0x7c, 0x4b, 0x0, 0x0, 0x0, 0xd,
    0xe3, 0xfe, 0x3a, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x90, 0xff, 0x99, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x30, 0xfc, 0xea, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc8,
    0xff, 0x5e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x68, 0xff, 0xbf, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xf0, 0xfa, 0x28, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0xff,
    0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0xff, 0xdd, 0x9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xdb, 0xff, 0x48, 0x0, 0x0,
    0x0, 0x0, 0x81, 0x8c, 0x0, 0x80, 0xff, 0xa8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xd0, 0x13,
    0xf9, 0xff, 0xa0, 0x88, 0x88, 0x88, 0x88, 0x88,
    0xf0, 0xd0, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0,

    /* U+005B "[" */
    0x42, 0x8c, 0x8c, 0x8c, 0x27, 0x78, 0xff, 0xff,
    0xff, 0x48, 0x78, 0xff, 0x74, 0x0, 0x0, 0x78,
    0xff, 0x74, 0x0, 0x0, 0x78, 0xff, 0x74, 0x0,
    0x0, 0x78, 0xff, 0x74, 0x0, 0x0, 0x78, 0xff,
    0x74, 0x0, 0x0, 0x78, 0xff, 0x74, 0x0, 0x0,
    0x78, 0xff, 0x74, 0x0, 0x0, 0x78, 0xff, 0x74,
    0x0, 0x0, 0x78, 0xff, 0x74, 0x0, 0x0, 0x78,
    0xff, 0x74, 0x0, 0x0, 0x78, 0xff, 0x74, 0x0,
    0x0, 0x78, 0xff, 0x74, 0x0, 0x0, 0x78, 0xff,
    0x74, 0x0, 0x0, 0x78, 0xff, 0x74, 0x0, 0x0,
    0x78, 0xff, 0x74, 0x0, 0x0, 0x78, 0xff, 0x74,
    0x0, 0x0, 0x78, 0xff, 0xc1, 0x8c, 0x27, 0x78,
    0xff, 0xff, 0xff, 0x48,

    /* U+005C "\\" */
    0x74, 0xff, 0x5c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x18, 0xfd, 0xbb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb8, 0xfd, 0x1b, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x58, 0xff, 0x78, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf0, 0xd7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98,
    0xff, 0x37, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xff, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdb, 0xed, 0x7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0x54, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xfe, 0xb3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc0, 0xfb,
    0x16, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x60,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa2, 0xff, 0x2e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x44, 0xff, 0x8c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xe2, 0xe8, 0x4,

    /* U+005D "]" */
    0x6d, 0x8c, 0x8c, 0x8a, 0xc8, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x0, 0x0, 0xf0, 0xfc, 0x0, 0x0, 0xf0, 0xfc,
    0x6d, 0x8c, 0xf9, 0xfc, 0xc8, 0xff, 0xff, 0xfc,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0x3e, 0x7e, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc8, 0xff, 0x4f, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x90, 0xff, 0xb3, 0xfc, 0x18, 0x0,
    0x0, 0x7, 0xec, 0xc4, 0x40, 0xff, 0x79, 0x0,
    0x0, 0x57, 0xff, 0x64, 0x2, 0xdf, 0xdb, 0x1,
    0x0, 0xbb, 0xf7, 0xe, 0x0, 0x80, 0xff, 0x3f,
    0x20, 0xfe, 0xa8, 0x0, 0x0, 0x21, 0xff, 0xa2,

    /* U+005F "_" */
    0x73, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x17, 0xd8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x2c,

    /* U+0060 "`" */
    0x2c, 0x80, 0x6a, 0x0, 0x0, 0x1, 0x8e, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x82, 0xf4, 0x25,

    /* U+0061 "a" */
    0x0, 0xb, 0x7c, 0xd0, 0xf7, 0xfa, 0xd7, 0x79,
    0x6, 0x0, 0x0, 0x0, 0x9d, 0xff, 0xe7, 0x9b,
    0x97, 0xe8, 0xff, 0xa5, 0x0, 0x0, 0x0, 0xa4,
    0xfe, 0x10, 0x0, 0x0, 0x18, 0xf1, 0xff, 0x1b,
    0x0, 0x0, 0x8d, 0xc6, 0x0, 0x0, 0x0, 0x0,
    0xb8, 0xff, 0x3b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x13, 0x14, 0xb6, 0xff, 0x3c, 0x0, 0x0,
    0x7, 0x7b, 0xd9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x3c, 0x0, 0x0, 0xb5, 0xff, 0xc1, 0x5c, 0x45,
    0x44, 0xc5, 0xff, 0x3c, 0x0, 0x28, 0xff, 0xdc,
    0x5, 0x0, 0x0, 0x0, 0xb0, 0xff, 0x3c, 0x0,
    0x32, 0xff, 0xcd, 0x1, 0x0, 0x0, 0x44, 0xf1,
    0xff, 0x3c, 0x0, 0x6, 0xe2, 0xff, 0xce, 0xa8,
    0xce, 0xf2, 0xd3, 0xff, 0xa9, 0x23, 0x0, 0x2b,
    0xba, 0xf4, 0xf8, 0xb7, 0x2a, 0x8f, 0xff, 0xff,
    0x48,

    /* U+0062 "b" */
    0x28, 0xff, 0xff, 0xff, 0x5c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x82, 0xd8, 0xff,
    0x5c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x90, 0xff, 0x5c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x90, 0xff,
    0x5c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x90, 0xff, 0x5c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x90, 0xff,
    0x64, 0x8e, 0xea, 0xfb, 0xce, 0x4e, 0x0, 0x0,
    0x0, 0x0, 0x90, 0xff, 0xe9, 0xe6, 0xa0, 0xb3,
    0xfd, 0xfe, 0x4c, 0x0, 0x0, 0x0, 0x90, 0xff,
    0xca, 0xd, 0x0, 0x0, 0x56, 0xff, 0xdc, 0x1,
    0x0, 0x0, 0x90, 0xff, 0x5e, 0x0, 0x0, 0x0,
    0x0, 0xcb, 0xff, 0x32, 0x0, 0x0, 0x90, 0xff,
    0x5c, 0x0, 0x0, 0x0, 0x0, 0x90, 0xff, 0x60,
    0x0, 0x0, 0x90, 0xff, 0x5c, 0x0, 0x0, 0x0,
    0x0, 0x7a, 0xff, 0x72, 0x0, 0x0, 0x90, 0xff,
    0x5c, 0x0, 0x0, 0x0, 0x0, 0x83, 0xff, 0x6c,
    0x0, 0x0, 0x90, 0xff, 0x5e, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0x49, 0x0, 0x0, 0x90, 0xff,
    0xca, 0xd, 0x0, 0x0, 0x3b, 0xfd, 0xf0, 0xa,
    0x0, 0x0, 0x90, 0xff, 0xd9, 0xe6, 0xa1, 0xb0,
    0xf9, 0xff, 0x6a, 0x0, 0x0, 0x0, 0x90, 0xff,
    0x2e, 0x8e, 0xe8, 0xfc, 0xd5, 0x5f, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x13, 0x93, 0xe4, 0xfd, 0xea, 0xa6,
    0x2b, 0x0, 0x0, 0x13, 0xdc, 0xff, 0xcc, 0x92,
    0xb3, 0xfd, 0xf7, 0x35, 0x0, 0x9c, 0xff, 0xa1,
    0x2, 0x0, 0x0, 0x71, 0xff, 0x60, 0x8, 0xf6,
    0xf7, 0x10, 0x0, 0x0, 0x0, 0x36, 0xff, 0x65,
    0x2f, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x24, 0xf, 0x3c, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xbe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf7,
    0xf0, 0x8, 0x0, 0x0, 0x0, 0x16, 0x88, 0x55,
    0x0, 0x9e, 0xff, 0x83, 0x0, 0x0, 0x0, 0x8a,
    0xff, 0x70, 0x0, 0x15, 0xdd, 0xff, 0xbe, 0x8d,
    0xbe, 0xff, 0xda, 0xd, 0x0, 0x0, 0x13, 0x95,
    0xe6, 0xfe, 0xe8, 0x9b, 0x16, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd0, 0xff,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x60, 0xb2, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xbb,
    0xf8, 0xf1, 0xa5, 0x4d, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x24, 0xf0, 0xff, 0xc5, 0x9d, 0xd4, 0xf0,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0xa9, 0xff, 0x8d,
    0x0, 0x0, 0x3, 0x9f, 0xff, 0xb4, 0x0, 0x0,
    0x5, 0xf6, 0xf6, 0xc, 0x0, 0x0, 0x0, 0x38,
    0xff, 0xb4, 0x0, 0x0, 0x28, 0xff, 0xc7, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xff, 0xb4, 0x0, 0x0,
    0x3a, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xff, 0xb4, 0x0, 0x0, 0x34, 0xff, 0xbb, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xff, 0xb4, 0x0, 0x0,
    0x13, 0xff, 0xe9, 0x3, 0x0, 0x0, 0x0, 0x38,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0xc4, 0xff, 0x6e,
    0x0, 0x0, 0x3, 0xa5, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x3a, 0xfa, 0xff, 0xbe, 0x9d, 0xd7, 0xe4,
    0xff, 0xea, 0x99, 0x26, 0x0, 0x0, 0x3f, 0xc5,
    0xf9, 0xf1, 0xa6, 0x1e, 0xff, 0xff, 0xff, 0x4c,

    /* U+0065 "e" */
    0x0, 0x0, 0x10, 0x95, 0xea, 0xfc, 0xd9, 0x76,
    0x3, 0x0, 0x0, 0x10, 0xd5, 0xff, 0xb7, 0x94,
    0xdb, 0xff, 0x9a, 0x0, 0x0, 0x9a, 0xff, 0x85,
    0x0, 0x0, 0x9, 0xd1, 0xff, 0x2c, 0xc, 0xf9,
    0xf4, 0xb, 0x0, 0x0, 0x0, 0x70, 0xff, 0x74,
    0x3a, 0xff, 0xe9, 0x90, 0x90, 0x90, 0x90, 0xb8,
    0xff, 0x91, 0x48, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x94, 0x3b, 0xff, 0xb9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfc,
    0xeb, 0x5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa6, 0xff, 0x7e, 0x0, 0x0, 0x0, 0x7,
    0x4a, 0x0, 0x0, 0x19, 0xe0, 0xff, 0xbe, 0x8c,
    0x9f, 0xe6, 0xfc, 0x18, 0x0, 0x0, 0x16, 0x95,
    0xe4, 0xfd, 0xef, 0xb5, 0x40, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x52, 0xd6, 0xfb, 0xe9, 0x10,
    0x0, 0x0, 0x38, 0xfd, 0xf9, 0xaa, 0x9f, 0x0,
    0x0, 0x0, 0x97, 0xff, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb5, 0xff, 0x37, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x60, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x2c, 0x74, 0xd9, 0xff, 0x90, 0x74, 0x3a, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0xff, 0x34, 0x0, 0x0, 0x0,
    0x19, 0x6e, 0xe0, 0xff, 0x9d, 0x5d, 0x1, 0x0,
    0x50, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x29, 0xb8, 0xf6, 0xf4, 0xae, 0x1e,
    0xec, 0xff, 0xff, 0x10, 0x0, 0x1f, 0xed, 0xff,
    0xca, 0x9d, 0xd1, 0xdf, 0xff, 0xee, 0x94, 0x7,
    0x0, 0xa3, 0xff, 0x94, 0x0, 0x0, 0x1, 0x92,
    0xff, 0xc0, 0x0, 0x0, 0x5, 0xf5, 0xf8, 0xf,
    0x0, 0x0, 0x0, 0x2c, 0xff, 0xc0, 0x0, 0x0,
    0x28, 0xff, 0xc8, 0x0, 0x0, 0x0, 0x0, 0x2c,
    0xff, 0xc0, 0x0, 0x0, 0x3a, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xff, 0xc0, 0x0, 0x0,
    0x34, 0xff, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x2c,
    0xff, 0xc0, 0x0, 0x0, 0x12, 0xff, 0xe9, 0x3,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xc0, 0xff, 0x73, 0x0, 0x0, 0x1, 0x9a,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x35, 0xf8, 0xff,
    0xc1, 0x9d, 0xd1, 0xf0, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xbf, 0xf8, 0xf3, 0xae, 0x4a,
    0xff, 0xbf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x46, 0xff, 0xab, 0x0, 0x0,
    0x0, 0x0, 0x16, 0x5, 0x0, 0x0, 0x4, 0xb3,
    0xff, 0x69, 0x0, 0x0, 0x0, 0x0, 0x9d, 0xef,
    0xbc, 0xad, 0xde, 0xff, 0xd1, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x54, 0xb8, 0xec, 0xfc, 0xe2, 0x91,
    0xf, 0x0, 0x0, 0x0,

    /* U+0068 "h" */
    0x84, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0x96, 0xfa,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xec, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xec, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xec, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xec, 0xff, 0x15, 0xa1, 0xf0,
    0xf9, 0xc2, 0x35, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xec, 0xff, 0xd5, 0xcc, 0x9e, 0xca, 0xff, 0xf2,
    0x1a, 0x0, 0x0, 0x0, 0x0, 0xec, 0xff, 0x73,
    0x0, 0x0, 0x1, 0xae, 0xff, 0x78, 0x0, 0x0,
    0x0, 0x0, 0xec, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x54, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0xec,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0xec, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x44, 0xff, 0xac, 0x0, 0x0, 0x0,
    0x0, 0xec, 0xff, 0x0, 0x0, 0x0, 0x0, 0x44,
    0xff, 0xac, 0x0, 0x0, 0x0, 0x0, 0xec, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x44, 0xff, 0xac, 0x0,
    0x0, 0x0, 0x0, 0xec, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x44, 0xff, 0xac, 0x0, 0x0, 0x28, 0x6f,
    0xf6, 0xff, 0x73, 0x30, 0x0, 0x49, 0x9c, 0xff,
    0xd6, 0x64, 0x12, 0x84, 0xff, 0xff, 0xff, 0xff,
    0x98, 0x0, 0xd8, 0xff, 0xff, 0xff, 0xff, 0x40,

    /* U+0069 "i" */
    0x0, 0x0, 0xd8, 0xff, 0x2c, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x18, 0x1c,
    0x5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0xff,
    0xff, 0xff, 0x2c, 0x0, 0x1d, 0x72, 0xe4, 0xff,
    0x2c, 0x0, 0x0, 0x0, 0xc0, 0xff, 0x2c, 0x0,
    0x0, 0x0, 0xc0, 0xff, 0x2c, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0x2c, 0x0, 0x0, 0x0, 0xc0, 0xff,
    0x2c, 0x0, 0x0, 0x0, 0xc0, 0xff, 0x2c, 0x0,
    0x0, 0x0, 0xc0, 0xff, 0x2c, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0x2c, 0x0, 0x1b, 0x6e, 0xe3, 0xff,
    0x94, 0x44, 0x58, 0xff, 0xff, 0xff, 0xff, 0xc4,

    /* U+006A "j" */
    0x0, 0x0, 0x4c, 0xff, 0xb8, 0x0, 0x0, 0x4c,
    0xff, 0xb8, 0x0, 0x0, 0x8, 0x1c, 0x14, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0xff, 0xb8, 0x0, 0x42,
    0x91, 0xff, 0xb8, 0x0, 0x0, 0x34, 0xff, 0xb8,
    0x0, 0x0, 0x34, 0xff, 0xb8, 0x0, 0x0, 0x34,
    0xff, 0xb8, 0x0, 0x0, 0x34, 0xff, 0xb8, 0x0,
    0x0, 0x34, 0xff, 0xb8, 0x0, 0x0, 0x34, 0xff,
    0xb8, 0x0, 0x0, 0x34, 0xff, 0xb8, 0x0, 0x0,
    0x34, 0xff, 0xb8, 0x0, 0x0, 0x34, 0xff, 0xb8,
    0x0, 0x0, 0x35, 0xff, 0xb7, 0x0, 0x0, 0x5b,
    0xff, 0x9f, 0x49, 0x94, 0xeb, 0xff, 0x46, 0x76,
    0xfb, 0xdf, 0x66, 0x0,

    /* U+006B "k" */
    0x60, 0xff, 0xff, 0xff, 0x24, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x7e, 0xea,
    0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc8, 0xff, 0x24, 0x0, 0x34,
    0xff, 0xff, 0xff, 0xff, 0x58, 0x0, 0x0, 0x0,
    0xc8, 0xff, 0x24, 0x0, 0x10, 0x92, 0xff, 0xe6,
    0x76, 0x1d, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x24,
    0x0, 0x2d, 0xee, 0xeb, 0x2b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc8, 0xff, 0x24, 0x28, 0xe9, 0xee,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc8,
    0xff, 0x47, 0xe5, 0xff, 0x43, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc8, 0xff, 0xf2, 0xff,
    0xff, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0xf8, 0x4a, 0xca, 0xff, 0x69,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0x5a, 0x0, 0x21, 0xf1, 0xf9, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc8, 0xff, 0x24, 0x0, 0x0,
    0x55, 0xff, 0xdf, 0x11, 0x0, 0x0, 0x1e, 0x6f,
    0xe6, 0xff, 0x8f, 0x41, 0x0, 0x30, 0xd6, 0xff,
    0xc1, 0x65, 0xe, 0x60, 0xff, 0xff, 0xff, 0xff,
    0xbc, 0x0, 0x98, 0xff, 0xff, 0xff, 0xff, 0x30,

    /* U+006C "l" */
    0x60, 0xff, 0xff, 0xff, 0x24, 0x0, 0x20, 0x76,
    0xe8, 0xff, 0x24, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0xc8, 0xff, 0x24, 0x0, 0x0, 0x0,
    0xc8, 0xff, 0x24, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0xc8, 0xff, 0x24, 0x0, 0x0, 0x0,
    0xc8, 0xff, 0x24, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0xc8, 0xff, 0x24, 0x0, 0x0, 0x0,
    0xc8, 0xff, 0x24, 0x0, 0x20, 0x73, 0xe7, 0xff,
    0x92, 0x44, 0x60, 0xff, 0xff, 0xff, 0xff, 0xbc,

    /* U+006D "m" */
    0x48, 0xff, 0xff, 0xff, 0x2e, 0x9f, 0xed, 0xfc,
    0xd1, 0x48, 0x1, 0x6d, 0xde, 0xfd, 0xdd, 0x63,
    0x0, 0x0, 0x0, 0x15, 0x67, 0xda, 0xff, 0xea,
    0x99, 0x6a, 0xa2, 0xff, 0xf9, 0x9f, 0xe1, 0x76,
    0x79, 0xe7, 0xff, 0x53, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0xff, 0x74, 0x0, 0x0, 0x0, 0xa7, 0xff,
    0xf8, 0x21, 0x0, 0x0, 0x3f, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0xff, 0x3c, 0x0, 0x0,
    0x0, 0x5e, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x2,
    0xfa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xb0, 0xff,
    0x3c, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xed, 0xff, 0x3, 0x0, 0x0,
    0x0, 0xb0, 0xff, 0x3c, 0x0, 0x0, 0x0, 0x4c,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xec, 0xff,
    0x4, 0x0, 0x0, 0x0, 0xb0, 0xff, 0x3c, 0x0,
    0x0, 0x0, 0x4c, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xec, 0xff, 0x4, 0x0, 0x0, 0x0, 0xb0,
    0xff, 0x3c, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xec, 0xff, 0x4, 0x0,
    0x0, 0x0, 0xb0, 0xff, 0x3c, 0x0, 0x0, 0x0,
    0x4c, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xec,
    0xff, 0x4, 0x0, 0x16, 0x6e, 0xdc, 0xff, 0x9f,
    0x4b, 0x0, 0x53, 0xa8, 0xff, 0xd4, 0x6a, 0x11,
    0x2c, 0x79, 0xf7, 0xff, 0x7f, 0x35, 0x48, 0xff,
    0xff, 0xff, 0xff, 0xd4, 0x0, 0xe4, 0xff, 0xff,
    0xff, 0xff, 0x38, 0x84, 0xff, 0xff, 0xff, 0xff,
    0x9c,

    /* U+006E "n" */
    0x48, 0xff, 0xff, 0xff, 0x1d, 0x8f, 0xe8, 0xfc,
    0xd6, 0x5a, 0x0, 0x0, 0x0, 0x15, 0x67, 0xda,
    0xff, 0xd5, 0xb5, 0x6b, 0x81, 0xee, 0xff, 0x4a,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x52, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0xff, 0x3c, 0x0, 0x0, 0x0, 0x13,
    0xff, 0xdb, 0x0, 0x0, 0x0, 0x0, 0xb0, 0xff,
    0x3c, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0xff, 0x3c, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0xff, 0x3c, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0xb0, 0xff, 0x3c,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0xb0, 0xff, 0x3c, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe4, 0x0, 0x0, 0x16, 0x6e, 0xdc,
    0xff, 0x9f, 0x4b, 0x0, 0x36, 0x81, 0xff, 0xf4,
    0x76, 0x28, 0x48, 0xff, 0xff, 0xff, 0xff, 0xd4,
    0x0, 0xa0, 0xff, 0xff, 0xff, 0xff, 0x7c,

    /* U+006F "o" */
    0x0, 0x0, 0x12, 0x93, 0xe5, 0xfc, 0xe4, 0x8e,
    0xf, 0x0, 0x0, 0x0, 0x11, 0xd8, 0xff, 0xc1,
    0x92, 0xc3, 0xff, 0xd5, 0xf, 0x0, 0x0, 0x96,
    0xff, 0x88, 0x0, 0x0, 0x0, 0x90, 0xff, 0x92,
    0x0, 0x6, 0xf4, 0xf3, 0x9, 0x0, 0x0, 0x0,
    0xd, 0xf6, 0xf1, 0x4, 0x2b, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc5, 0xff, 0x27, 0x3a,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb6,
    0xff, 0x36, 0x2d, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc5, 0xff, 0x27, 0x6, 0xf5, 0xf3,
    0x9, 0x0, 0x0, 0x0, 0xc, 0xf6, 0xf3, 0x5,
    0x0, 0x99, 0xff, 0x86, 0x0, 0x0, 0x0, 0x8b,
    0xff, 0x95, 0x0, 0x0, 0x12, 0xda, 0xff, 0xbe,
    0x8e, 0xbf, 0xff, 0xd9, 0x12, 0x0, 0x0, 0x0,
    0x12, 0x93, 0xe6, 0xfe, 0xe5, 0x93, 0x12, 0x0,
    0x0,

    /* U+0070 "p" */
    0xac, 0xff, 0xff, 0xa4, 0x57, 0xd1, 0xfc, 0xeb,
    0x99, 0xd, 0x0, 0x37, 0x81, 0xff, 0xf3, 0xe8,
    0x7e, 0x6d, 0xc4, 0xff, 0xc2, 0x2, 0x0, 0x14,
    0xff, 0xf3, 0x20, 0x0, 0x0, 0x4, 0xc3, 0xff,
    0x57, 0x0, 0x14, 0xff, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x4d, 0xff, 0xac, 0x0, 0x14, 0xff, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0x15, 0xff, 0xd8, 0x0,
    0x14, 0xff, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xea, 0x0, 0x14, 0xff, 0xd8, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe4, 0x0, 0x14, 0xff,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0x42, 0xff, 0xc1,
    0x0, 0x14, 0xff, 0xf7, 0x31, 0x0, 0x0, 0x5,
    0xc1, 0xff, 0x72, 0x0, 0x14, 0xff, 0xf6, 0xf4,
    0xa0, 0x8e, 0xd6, 0xff, 0xd8, 0xb, 0x0, 0x14,
    0xff, 0xd8, 0x4e, 0xcd, 0xfc, 0xf0, 0xa3, 0x17,
    0x0, 0x0, 0x14, 0xff, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0xff, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x84, 0xff, 0xee, 0x74, 0x2c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x90, 0xff, 0xff, 0xff, 0xff, 0x88,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x2e, 0xbb, 0xf8, 0xf1, 0xa5, 0x34,
    0xff, 0x98, 0x0, 0x0, 0x0, 0x24, 0xf0, 0xff,
    0xb9, 0x91, 0xcc, 0xed, 0xff, 0x98, 0x0, 0x0,
    0x0, 0xa9, 0xff, 0x85, 0x0, 0x0, 0x2, 0xa2,
    0xff, 0x98, 0x0, 0x0, 0x5, 0xf6, 0xf6, 0xb,
    0x0, 0x0, 0x0, 0x54, 0xff, 0x98, 0x0, 0x0,
    0x28, 0xff, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x54,
    0xff, 0x98, 0x0, 0x0, 0x3a, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x54, 0xff, 0x98, 0x0, 0x0,
    0x34, 0xff, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x54,
    0xff, 0x98, 0x0, 0x0, 0x13, 0xff, 0xe8, 0x2,
    0x0, 0x0, 0x0, 0x54, 0xff, 0x98, 0x0, 0x0,
    0x0, 0xc4, 0xff, 0x68, 0x0, 0x0, 0x1, 0x9f,
    0xff, 0x98, 0x0, 0x0, 0x0, 0x3a, 0xfa, 0xfe,
    0xae, 0x8d, 0xc9, 0xfa, 0xff, 0x98, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xc5, 0xf9, 0xf2, 0xa8, 0x6e,
    0xff, 0x98, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x54, 0xff, 0x98, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x54,
    0xff, 0x98, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x55, 0xab, 0xff, 0xcf, 0x64, 0x6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xec, 0xff,
    0xff, 0xff, 0xff, 0x14,

    /* U+0072 "r" */
    0x4c, 0xff, 0xff, 0xff, 0x25, 0xa9, 0xf8, 0xc7,
    0x16, 0x68, 0xdc, 0xff, 0xdd, 0xe2, 0xbd, 0x84,
    0x0, 0x0, 0xb4, 0xff, 0xa9, 0x4, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x3a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x17, 0x6c, 0xdd, 0xff, 0x9a, 0x4a, 0x0, 0x0,
    0x4c, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x50, 0xc5, 0xf6, 0xfb, 0xdd, 0x8d, 0x17,
    0x0, 0x63, 0xff, 0xed, 0x96, 0x8e, 0xd0, 0xff,
    0x9c, 0x0, 0xd6, 0xff, 0x2f, 0x0, 0x0, 0x3,
    0xf3, 0xa7, 0x0, 0xd8, 0xff, 0x2e, 0x0, 0x0,
    0x0, 0x5d, 0x4f, 0x0, 0x6a, 0xff, 0xf2, 0x9a,
    0x57, 0x11, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xc4,
    0xff, 0xff, 0xf9, 0x9b, 0xf, 0x0, 0x0, 0x0,
    0x0, 0x17, 0x5c, 0xcb, 0xff, 0xb5, 0x0, 0x66,
    0x35, 0x0, 0x0, 0x0, 0x8, 0xe9, 0xff, 0x12,
    0xfc, 0xaf, 0x0, 0x0, 0x0, 0x9, 0xeb, 0xfb,
    0xa, 0xf8, 0xfa, 0xaf, 0x80, 0x82, 0xd0, 0xff,
    0x97, 0x0, 0x4b, 0xa2, 0xdd, 0xfa, 0xfb, 0xd9,
    0x7e, 0x5, 0x0,

    /* U+0074 "t" */
    0x0, 0xc, 0x80, 0x6a, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x18,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0xc4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x59, 0x81, 0xff, 0xe8,
    0x74, 0x74, 0xf, 0x0, 0x18, 0xff, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x18, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x18,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x11, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xda, 0xff,
    0x97, 0x7b, 0x41, 0x0, 0x0, 0x39, 0xd6, 0xfd,
    0xe7, 0x6c,

    /* U+0075 "u" */
    0x7c, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0x54, 0x0, 0x28, 0x9d, 0xff, 0xa4,
    0x0, 0x0, 0x1, 0x5e, 0xcc, 0xff, 0x54, 0x0,
    0x0, 0x48, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x98, 0xff, 0x54, 0x0, 0x0, 0x48, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x98, 0xff, 0x54, 0x0,
    0x0, 0x48, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x98, 0xff, 0x54, 0x0, 0x0, 0x48, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x98, 0xff, 0x54, 0x0,
    0x0, 0x47, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x0,
    0x98, 0xff, 0x54, 0x0, 0x0, 0x36, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0x9b, 0xff, 0x54, 0x0,
    0x0, 0xa, 0xf7, 0xf1, 0x14, 0x0, 0x0, 0x29,
    0xee, 0xff, 0x54, 0x0, 0x0, 0x0, 0x90, 0xff,
    0xe3, 0x9f, 0xb1, 0xf4, 0xc4, 0xff, 0xc8, 0x49,
    0x0, 0x0, 0x5, 0x84, 0xe7, 0xfc, 0xd0, 0x4c,
    0x72, 0xff, 0xff, 0x88,

    /* U+0076 "v" */
    0xc0, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0xf0,
    0xff, 0xff, 0xff, 0x7c, 0x43, 0xc0, 0xff, 0xab,
    0x3d, 0x0, 0x0, 0x57, 0xc9, 0xff, 0x94, 0x29,
    0x0, 0x3e, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x1,
    0xdd, 0xf2, 0xa, 0x0, 0x0, 0x1, 0xd9, 0xfb,
    0x15, 0x0, 0x0, 0x37, 0xff, 0x9c, 0x0, 0x0,
    0x0, 0x0, 0x75, 0xff, 0x70, 0x0, 0x0, 0x92,
    0xff, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x15, 0xfa,
    0xd0, 0x0, 0x3, 0xe7, 0xdb, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaa, 0xff, 0x30, 0x44, 0xff,
    0x7c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x46,
    0xff, 0x8f, 0x9e, 0xfe, 0x1e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xde, 0xe4, 0xf0, 0xbc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7b, 0xff, 0xff, 0x5c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xfc, 0xf2, 0xa,
    0x0, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x7c, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0xd,
    0xf9, 0xea, 0x3, 0x0, 0x0, 0xe8, 0xff, 0xff,
    0xff, 0x5c, 0x29, 0x8b, 0xff, 0xd0, 0x53, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0x3e, 0x0, 0x0, 0x55,
    0xcf, 0xff, 0x7b, 0x1e, 0x0, 0xa, 0xf7, 0xd7,
    0x0, 0x0, 0x0, 0xaf, 0xf3, 0xfd, 0x8e, 0x0,
    0x0, 0x0, 0xd9, 0xe3, 0x1, 0x0, 0x0, 0x0,
    0xb7, 0xff, 0x1c, 0x0, 0xc, 0xf7, 0xac, 0xc7,
    0xde, 0x0, 0x0, 0x1f, 0xff, 0x9a, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0x5f, 0x0, 0x57, 0xff,
    0x60, 0x7a, 0xff, 0x2e, 0x0, 0x64, 0xff, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x22, 0xff, 0xa3, 0x0,
    0xab, 0xf9, 0x10, 0x28, 0xff, 0x7e, 0x0, 0xaa,
    0xfa, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd7,
    0xe7, 0xb, 0xf5, 0xb0, 0x0, 0x0, 0xd6, 0xce,
    0x2, 0xed, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0xff, 0x7b, 0xff, 0x57, 0x0, 0x0,
    0x82, 0xff, 0x57, 0xff, 0x74, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x43, 0xff, 0xf5, 0xf4, 0xa,
    0x0, 0x0, 0x2f, 0xff, 0xe3, 0xff, 0x2c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf3, 0xff,
    0xa6, 0x0, 0x0, 0x0, 0x0, 0xdd, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xad, 0xff, 0x4c, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0xff, 0x98, 0x0, 0x0, 0x0, 0x0,

    /* U+0078 "x" */
    0x64, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x28, 0xff,
    0xff, 0xff, 0xe8, 0x0, 0x22, 0x8f, 0xff, 0xef,
    0x45, 0x0, 0xc, 0x83, 0xff, 0xd6, 0x5c, 0x0,
    0x0, 0x0, 0x94, 0xff, 0x88, 0x0, 0x9, 0xd2,
    0xf5, 0x28, 0x0, 0x0, 0x0, 0x0, 0x6, 0xc4,
    0xff, 0x4f, 0x9b, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0xe8, 0xf6, 0xff, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x77, 0xff, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0xf2, 0xec, 0xff, 0xd1,
    0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xd4,
    0xfa, 0x37, 0x86, 0xff, 0xa9, 0x1, 0x0, 0x0,
    0x0, 0x0, 0xa7, 0xff, 0x6d, 0x0, 0x3, 0xb9,
    0xff, 0x79, 0x0, 0x0, 0x34, 0x98, 0xff, 0xe0,
    0x3c, 0x0, 0x9, 0x68, 0xfd, 0xfd, 0x80, 0x1d,
    0x98, 0xff, 0xff, 0xff, 0xbc, 0x0, 0x20, 0xff,
    0xff, 0xff, 0xff, 0x5c,

    /* U+0079 "y" */
    0xc4, 0xff, 0xff, 0xff, 0xac, 0x0, 0x0, 0xe8,
    0xff, 0xff, 0xff, 0x74, 0x3b, 0xb2, 0xff, 0xb2,
    0x35, 0x0, 0x0, 0x4a, 0xcb, 0xff, 0x87, 0x21,
    0x0, 0x2d, 0xff, 0xcc, 0x0, 0x0, 0x0, 0x4,
    0xea, 0xeb, 0x5, 0x0, 0x0, 0x0, 0xc3, 0xff,
    0x2f, 0x0, 0x0, 0x4c, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x59, 0xff, 0x93, 0x0, 0x0, 0xa9,
    0xff, 0x2d, 0x0, 0x0, 0x0, 0x0, 0x6, 0xea,
    0xed, 0x8, 0xf, 0xf7, 0xcc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x86, 0xff, 0x58, 0x64, 0xff,
    0x6a, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfd, 0xb6, 0xc1, 0xf8, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb3, 0xfd, 0xff, 0xa7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xff, 0xff, 0x44, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf8, 0xe2, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x50, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd0, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa7, 0xd4,
    0xff, 0x96, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf3, 0xf4, 0x98, 0x7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x4, 0xff, 0xd3, 0x84, 0x84, 0x84,
    0xca, 0xff, 0xd9, 0x0, 0x4, 0xff, 0x8c, 0x0,
    0x0, 0x23, 0xf4, 0xfa, 0x33, 0x0, 0x2, 0x60,
    0x2c, 0x0, 0x4, 0xc8, 0xff, 0x77, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x81, 0xff, 0xbe, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xfd, 0xed,
    0x1c, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xdf,
    0xff, 0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa4, 0xff, 0x9c, 0x0, 0x0, 0x8, 0x60, 0x24,
    0x0, 0x59, 0xff, 0xda, 0xc, 0x0, 0x0, 0x2e,
    0xff, 0x60, 0x11, 0xf0, 0xff, 0xae, 0x80, 0x80,
    0x80, 0xa4, 0xff, 0x60, 0x24, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x8, 0x0,
    0x0, 0x0, 0x1, 0x6f, 0xec, 0x63, 0x0, 0x0,
    0x0, 0x7b, 0xff, 0xa1, 0xe, 0x0, 0x0, 0xe,
    0xf6, 0xe9, 0x6, 0x0, 0x0, 0x0, 0x46, 0xff,
    0xa9, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xff, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0xff, 0x93, 0x0, 0x0,
    0x0, 0x0, 0x81, 0xff, 0x74, 0x0, 0x0, 0xf,
    0x53, 0xee, 0xf2, 0x1a, 0x0, 0x0, 0x64, 0xff,
    0xff, 0x4c, 0x0, 0x0, 0x0, 0x22, 0x83, 0xfb,
    0xdd, 0xd, 0x0, 0x0, 0x0, 0x0, 0x95, 0xff,
    0x67, 0x0, 0x0, 0x0, 0x0, 0x60, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x19, 0xfd, 0xda, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x9b, 0xff, 0x78, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xa0, 0xff, 0x65, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x26, 0x14,

    /* U+007C "|" */
    0x88, 0xff, 0x88, 0xff, 0x88, 0xff, 0x88, 0xff,
    0x88, 0xff, 0x88, 0xff, 0x88, 0xff, 0x88, 0xff,
    0x88, 0xff, 0x88, 0xff, 0x88, 0xff, 0x88, 0xff,
    0x88, 0xff, 0x88, 0xff, 0x88, 0xff, 0x88, 0xff,
    0x88, 0xff,

    /* U+007D "}" */
    0xd, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x93,
    0xde, 0x4e, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xc4,
    0xfe, 0x4b, 0x0, 0x0, 0x0, 0x0, 0x22, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd9, 0xff,
    0x12, 0x0, 0x0, 0x0, 0x0, 0xc5, 0xff, 0x27,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0xff, 0x28, 0x0,
    0x0, 0x0, 0x0, 0xc3, 0xff, 0x29, 0x0, 0x0,
    0x0, 0x0, 0xa4, 0xff, 0x4e, 0x0, 0x0, 0x0,
    0x0, 0x3c, 0xfd, 0xd7, 0x42, 0x8, 0x0, 0x0,
    0x0, 0x68, 0xff, 0xff, 0x34, 0x0, 0x0, 0x26,
    0xf0, 0xee, 0x73, 0x11, 0x0, 0x0, 0x97, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xc1, 0xff, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0xc4, 0xff, 0x28, 0x0,
    0x0, 0x0, 0x0, 0xc4, 0xff, 0x27, 0x0, 0x0,
    0x0, 0x0, 0xd2, 0xff, 0x1a, 0x0, 0x0, 0x0,
    0x12, 0xfa, 0xe1, 0x0, 0x0, 0x0, 0x6, 0xa6,
    0xff, 0x6a, 0x0, 0x0, 0x0, 0x95, 0xf9, 0x7c,
    0x0, 0x0, 0x0, 0x0, 0x22, 0x18, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x72, 0xe9, 0xf8, 0xbd, 0x39, 0x0, 0x0,
    0x0, 0x13, 0x78, 0x1b, 0x59, 0xff, 0xd6, 0xb8,
    0xfb, 0xfd, 0x75, 0x1, 0x0, 0x6d, 0xff, 0x2c,
    0xba, 0xd1, 0x6, 0x0, 0x2c, 0xda, 0xff, 0xd6,
    0xb6, 0xfc, 0xc3, 0x0, 0x43, 0x3e, 0x0, 0x0,
    0x0, 0xc, 0x8d, 0xe9, 0xfb, 0xb3, 0x17, 0x0,

    /* U+00B0 "°" */
    0x0, 0x0, 0x6, 0x1, 0x0, 0x0, 0x9, 0xab,
    0xfe, 0xe2, 0x3b, 0x0, 0x87, 0xe1, 0x4b, 0xa0,
    0xe7, 0x7, 0xc3, 0x84, 0x0, 0x20, 0xff, 0x2b,
    0x9e, 0xcc, 0x1f, 0x78, 0xf7, 0xf, 0x1c, 0xd9,
    0xff, 0xfb, 0x60, 0x0, 0x0, 0x5, 0x34, 0x19,
    0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 80, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 75, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 45, .adv_w = 120, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 81, .adv_w = 195, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 261, .adv_w = 173, .box_w = 10, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 451, .adv_w = 228, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 661, .adv_w = 201, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 856, .adv_w = 72, .box_w = 3, .box_h = 6, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 874, .adv_w = 103, .box_w = 6, .box_h = 22, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 1006, .adv_w = 102, .box_w = 6, .box_h = 22, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 1138, .adv_w = 150, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 1219, .adv_w = 179, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1340, .adv_w = 63, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1364, .adv_w = 125, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 1376, .adv_w = 77, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1385, .adv_w = 129, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1513, .adv_w = 183, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1663, .adv_w = 132, .box_w = 7, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1768, .adv_w = 177, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1933, .adv_w = 173, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2083, .adv_w = 185, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2248, .adv_w = 169, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2398, .adv_w = 179, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2548, .adv_w = 176, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2713, .adv_w = 177, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2878, .adv_w = 180, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3043, .adv_w = 65, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3076, .adv_w = 66, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3132, .adv_w = 159, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 3222, .adv_w = 176, .box_w = 9, .box_h = 6, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 3276, .adv_w = 166, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3366, .adv_w = 149, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3501, .adv_w = 285, .box_w = 17, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3824, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4049, .adv_w = 207, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4244, .adv_w = 202, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4424, .adv_w = 212, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4619, .adv_w = 204, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4799, .adv_w = 196, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4979, .adv_w = 210, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5159, .adv_w = 247, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5384, .adv_w = 107, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5474, .adv_w = 183, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5639, .adv_w = 235, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5849, .adv_w = 189, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6029, .adv_w = 310, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6314, .adv_w = 249, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6539, .adv_w = 211, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6734, .adv_w = 203, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6929, .adv_w = 215, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7181, .adv_w = 216, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7376, .adv_w = 188, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7526, .adv_w = 220, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7721, .adv_w = 238, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7946, .adv_w = 236, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8171, .adv_w = 333, .box_w = 21, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8486, .adv_w = 239, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8711, .adv_w = 228, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8921, .adv_w = 187, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9086, .adv_w = 90, .box_w = 5, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9186, .adv_w = 131, .box_w = 9, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9330, .adv_w = 86, .box_w = 4, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9410, .adv_w = 135, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 9474, .adv_w = 183, .box_w = 10, .box_h = 2, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9494, .adv_w = 78, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 13},
    {.bitmap_index = 9509, .adv_w = 177, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9630, .adv_w = 179, .box_w = 12, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9822, .adv_w = 167, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9932, .adv_w = 190, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10124, .adv_w = 165, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10234, .adv_w = 113, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10362, .adv_w = 181, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10542, .adv_w = 205, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10750, .adv_w = 102, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10846, .adv_w = 82, .box_w = 5, .box_h = 20, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 10946, .adv_w = 201, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11154, .adv_w = 102, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11250, .adv_w = 309, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11459, .adv_w = 210, .box_w = 13, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11602, .adv_w = 176, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11723, .adv_w = 187, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11888, .adv_w = 178, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12068, .adv_w = 133, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12156, .adv_w = 160, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12255, .adv_w = 114, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12353, .adv_w = 195, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12485, .adv_w = 188, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12617, .adv_w = 285, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12815, .adv_w = 188, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12947, .adv_w = 186, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13127, .adv_w = 161, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13237, .adv_w = 110, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13384, .adv_w = 70, .box_w = 2, .box_h = 17, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13418, .adv_w = 110, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13565, .adv_w = 217, .box_w = 12, .box_h = 4, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 13613, .adv_w = 118, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = 9}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 1, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 0, 0, 0, 3, 0, 3,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 5, 6, 7, 8, 9, 10,
    11, 12, 12, 13, 14, 15, 12, 12,
    8, 16, 17, 18, 0, 19, 13, 20,
    21, 22, 23, 24, 25, 26, 0, 0,
    0, 0, 27, 28, 29, 30, 31, 32,
    33, 34, 0, 0, 35, 36, 34, 34,
    37, 28, 0, 38, 0, 39, 40, 41,
    42, 43, 41, 44, 45, 0, 0, 0,
    0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 2,
    1, 0, 3, 4, 0, 5, 6, 5,
    7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    8, 0, 9, 0, 10, 0, 0, 0,
    10, 0, 0, 11, 0, 0, 0, 0,
    10, 0, 10, 0, 12, 13, 14, 15,
    16, 17, 18, 19, 0, 20, 21, 0,
    0, 0, 22, 0, 23, 23, 24, 25,
    23, 26, 27, 0, 26, 26, 27, 27,
    24, 27, 23, 28, 29, 30, 31, 32,
    33, 34, 32, 35, 0, 0, 36, 0,
    0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    -17, 0, 0, 0, 0, 0, 0, 0,
    -19, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, -9, -10,
    0, 0, -3, 0, -12, 0, 0, 0,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 3, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -27, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -35, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -19, 0, 0, 0, 0, 0, 0, -10,
    0, -11, -3, 0, -37, -9, -43, -34,
    0, -40, 0, 0, 0, 0, -5, -5,
    0, 0, 0, 0, 0, -17, -11, -22,
    -19, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, -4, 0, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -16, 0, 0, 0, -11, 9, 0, 0,
    -4, 0, -10, -6, -9, -11, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, -3,
    -3, 0, 0, 0, 0, 0, -3, -4,
    -3, 0, 0, 0, 0, 0, 0, 0,
    -37, 0, 0, 0, -34, -8, -30, 0,
    3, 0, 0, 0, 0, 0, 0, 0,
    0, -5, -3, -14, 0, 0, 0, -4,
    0, 0, -3, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 5, 5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 3, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, -19, 0, 0,
    0, 0, -9, -8, -6, -11, 0, 0,
    0, 0, -4, -12, 0, 0, -4, 0,
    0, 0, -4, -6, -10, 0, 0, 0,
    -22, 0, 0, 0, 0, 0, 0, 0,
    3, -10, 0, 0, -43, -8, -40, -22,
    0, -37, 0, 0, 0, 3, 3, 3,
    0, 0, 0, 0, 0, 0, -7, -21,
    -14, 0, 0, 0, 0, 0, 0, 0,
    -51, 0, 0, 0, -34, 4, -23, 0,
    0, 0, -3, -4, -5, -3, -4, 0,
    0, -2, -2, -3, 0, 0, 0, 0,
    0, 2, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, -4, -3,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -12, 0, -12, 0, 0, -16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -34, -36, 0, 0,
    -37, -4, -25, -2, 0, 0, 3, 2,
    0, 3, 0, 0, 0, -18, -15, -17,
    0, 0, -12, -12, -17, 0, -15, -11,
    -9, -12, -9, 0, 0, 0, 3, 0,
    -35, -6, 0, 0, -44, -10, -33, 0,
    0, 0, -5, -3, -6, -5, 0, 0,
    3, -7, -7, -19, 0, 0, 0, -5,
    0, 0, -4, -2, 0, 0, 0, 3,
    0, 0, 2, 0, -19, -9, 0, 0,
    -34, -9, -19, 0, 2, 0, -5, -2,
    -6, -3, 0, 0, 2, -5, -5, -11,
    0, 0, 0, -3, 0, 0, -3, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, -7, 0, 0, 0, -9, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, -4, -10, 0, 0, 0, 0,
    0, 0, -3, -5, 0, 0, 0, 0,
    0, -5, 3, -8, -33, -8, 0, 0,
    -40, -12, -33, -2, 3, -15, -3, -2,
    -5, -3, 0, 0, 3, -11, -10, -22,
    -3, 0, -6, -6, -17, -3, -6, -3,
    0, -4, -5, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 2, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, -3, -4, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -35,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 6, 0, -2, 0, 0,
    0, 0, 0, -3, 0, -2, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 6, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 2, 6, 5,
    0, 0, 0, 0, 1, 0, 0, -4,
    0, 0, 0, 0, 3, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 0, -4, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -22, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, 0, 0, 0,
    0, -6, -6, -20, -16, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, -9,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -3, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 5, 5,
    0, -3, 0, 0, 0, 0, 0, -5,
    -6, -3, -2, 0, 3, 0, 0, 0,
    -19, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, -3, 2, -3, 0, 0,
    0, 5, 0, 3, 3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    2, 0, 0, 0, -17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -7, -4,
    2, -3, 0, 0, -3, 8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -19, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, -3, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 45,
    .right_class_cnt     = 36,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 8,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_lv_demo_high_res_roboto_slab_regular_20 = {
#else
lv_font_t font_lv_demo_high_res_roboto_slab_regular_20 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 22,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_USE_DEMO_HIGH_RES*/

