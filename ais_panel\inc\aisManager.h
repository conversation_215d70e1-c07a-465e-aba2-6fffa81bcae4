/**
 * @file    aisManager.h
 * @brief   AIS manager header file
 * <AUTHOR>
 * @date    2024-12-21
 * 
 * @copyright Copyright (c) 2024 Intelliantech
 */

#ifndef __AIS_MANAGER_H__
#define __AIS_MANAGER_H__

#include "devlib/devctrl.h"
#include "BamAlert.h"
#include "RegionalMgr.h"
#include "SetupMgr.h"
#include "nmea/nmea.h"
#include "nmea/decoder.h"
#include "LvglMgr.h"
#include "vk_messageq.h"
#include "sqlite/targetdb.h"
#include "sqlite/messagedb.h"

class CAisManager : public CNmea, public CDecoder/*, public CDB*/
{
public:
  // constructor / destructor
  CAisManager();
  ~CAisManager();

  // methods - public
  int32_t setup();
  void run();
  void close();

private:
  // methods - private
  bool LoadTargetData(uint32_t nMmsi, CShip* pShip);
  bool SaveTargetData(int nMsgNoId, int nSentFormat, CShip* pShip);
  bool SaveMessageData(int nMsgNoId, CMessage* pMsg);
  void ProcABM(void);
  void ProcABK(void);
  void ProcACA(void);
  void ProcACK(void);
  void ProcACS(void);
  void ProcAIR(void);
  void ProcALF(void);
  void ProcALR(void);
  void ProcBBM(void);
  void ProcGSA(void);
  void ProcGSV(void);
  void ProcLR1(void);
  void ProcLR2(void);
  void ProcLR3(void);
  void ProcLRF(void);
  void ProcLRI(void);
  void ProcSPW(void);
  void ProcSSD(void);
  void ProcTXT(void);
  void ProcVDM(void);
  void ProcVDO(void);
  void ProcVSD(void);
  void DecodeAisMsg(const char *pEncodedMsg, int nSentenceFormat);
  void ProcSentence(void);
  void OnSerialCallback(MessageType type, const char *sentence);

private:
  // members
  std::shared_ptr<CDeviceCtrl>	m_pDevice;
  std::shared_ptr<CShip>        m_pOwnShipInfo;
  std::shared_ptr<CSetupMgr>	m_pSetupMgr;
  std::shared_ptr<CDB>			m_pDB;
  std::shared_ptr<CMessageDB>	m_pMessageDB;
  std::shared_ptr<CBamAlertMgr> m_pBamAlertMgr;
  std::shared_ptr<CRegionMgr>   m_pRegionMgr;

  typedef void(CAisManager::*NmeaProcFunc)(void);
  NmeaProcFunc m_pNmeaProcFunc[CSentence::NMEA_COUNT];
  UiLvglMgr    m_uiLvglMgr;
  bool         m_threadRunning;
  mqd_t        m_msgQueue;
};

#endif /* __AIS_MANAGER_H__ */