/*******************************************************************************
 * Size: 16 px
 * Bpp: 8
 * Opts: --bpp 8 --size 16 --no-compress --font Roboto-Medium.ttf --range 32-127,176 --format lvgl -o font_lv_demo_high_res_roboto_medium_16.c
 ******************************************************************************/

#include "../../../lvgl.h"

#if LV_USE_DEMO_HIGH_RES

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xd2, 0xff, 0x28, 0xcc, 0xff, 0x23, 0xc6, 0xff,
    0x1d, 0xc0, 0xff, 0x17, 0xbb, 0xff, 0x12, 0xb5,
    0xff, 0xc, 0xaf, 0xff, 0x6, 0xa9, 0xff, 0x1,
    0x40, 0x62, 0x0, 0x4, 0xf, 0x0, 0xb8, 0xf8,
    0x26, 0xa4, 0xea, 0x1f,

    /* U+0022 "\"" */
    0x34, 0xff, 0x28, 0xdc, 0x80, 0x34, 0xff, 0x1f,
    0xdc, 0x77, 0x34, 0xff, 0x8, 0xdc, 0x62, 0x34,
    0xf1, 0x0, 0xdc, 0x4c, 0xe, 0x3f, 0x0, 0x3d,
    0x11,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x7, 0xfa, 0x49, 0x1, 0xf0,
    0x59, 0x0, 0x0, 0x0, 0x0, 0x37, 0xff, 0x15,
    0x27, 0xff, 0x25, 0x0, 0x0, 0x0, 0x0, 0x6a,
    0xe1, 0x0, 0x5a, 0xf0, 0x1, 0x0, 0x0, 0xa8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x78,
    0x0, 0x34, 0x50, 0xde, 0xa8, 0x50, 0xd3, 0xb0,
    0x50, 0x25, 0x0, 0x0, 0x1, 0xf3, 0x5b, 0x0,
    0xe5, 0x67, 0x0, 0x0, 0x3, 0xc, 0x2a, 0xff,
    0x3a, 0x1a, 0xff, 0x45, 0xa, 0x0, 0x40, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0xf, 0x3c, 0x9d, 0xdb, 0x3c, 0x91, 0xe4, 0x3c,
    0x35, 0x0, 0x0, 0x0, 0xa3, 0xac, 0x0, 0x93,
    0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x84,
    0x0, 0xbc, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf3, 0x5c, 0x0, 0xe4, 0x68, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x54, 0x28, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x68, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x6c, 0xef, 0xb3,
    0x32, 0x0, 0x0, 0x0, 0x19, 0xe0, 0xff, 0xff,
    0xff, 0xfc, 0x58, 0x0, 0x0, 0x92, 0xff, 0x92,
    0xc, 0x4c, 0xfd, 0xe7, 0x6, 0x0, 0xbe, 0xff,
    0x2a, 0x0, 0x0, 0xbe, 0xff, 0x28, 0x0, 0xa4,
    0xff, 0x6a, 0x0, 0x0, 0x1f, 0x30, 0xb, 0x0,
    0x34, 0xf9, 0xfe, 0xa9, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0xd0, 0xff, 0xff, 0xb9, 0x18,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xbb, 0xff,
    0xcb, 0x2, 0xf, 0x50, 0x38, 0x0, 0x0, 0x1,
    0xc9, 0xff, 0x2c, 0x1b, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0xa8, 0xff, 0x3d, 0x0, 0xce, 0xff, 0x8a,
    0x3a, 0x67, 0xf9, 0xf2, 0x10, 0x0, 0x2e, 0xdf,
    0xff, 0xff, 0xff, 0xf4, 0x57, 0x0, 0x0, 0x0,
    0x8, 0x49, 0xff, 0x78, 0x15, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x3c, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x55, 0xdf, 0xf0, 0x9d, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0xf7, 0x86, 0x49,
    0xf0, 0x68, 0x0, 0xf, 0x83, 0x5, 0x0, 0x0,
    0x35, 0xff, 0x1e, 0x0, 0xba, 0x95, 0x0, 0x95,
    0xb6, 0x0, 0x0, 0x0, 0x1a, 0xfe, 0x5b, 0x16,
    0xe3, 0x79, 0x35, 0xf4, 0x22, 0x0, 0x0, 0x0,
    0x0, 0x81, 0xfd, 0xff, 0xce, 0x14, 0xcd, 0x7d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x2, 0x6f, 0xda, 0x7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1a, 0xf1, 0x51, 0xa6,
    0xf2, 0xcd, 0x2d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xab, 0xa9, 0x7e, 0xe7, 0x44, 0xb2, 0xcf, 0x0,
    0x0, 0x0, 0x0, 0x48, 0xf2, 0x1a, 0xb7, 0x9b,
    0x0, 0x46, 0xff, 0xa, 0x0, 0x0, 0x9, 0xdd,
    0x71, 0x0, 0xb7, 0x9a, 0x0, 0x46, 0xff, 0xb,
    0x0, 0x0, 0x22, 0xa6, 0x3, 0x0, 0x7f, 0xe5,
    0x40, 0xab, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xa9, 0xf3, 0xcf, 0x30, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x39, 0xc8, 0xf4, 0xd6, 0x4f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0xee, 0xf6, 0xa1,
    0xe4, 0xf9, 0x27, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x7e, 0x0, 0x48, 0xff, 0x63, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x8c, 0x0, 0x7a, 0xff,
    0x43, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe5, 0xf5,
    0x9a, 0xfe, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6d, 0xff, 0xff, 0x8e, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x44, 0xf2, 0xf9, 0xff, 0xb0,
    0x1, 0x28, 0xac, 0x46, 0x0, 0x11, 0xf1, 0xf1,
    0x2d, 0xb5, 0xff, 0x81, 0x5b, 0xff, 0x4e, 0x0,
    0x4a, 0xff, 0xa6, 0x0, 0xf, 0xd9, 0xfe, 0xda,
    0xfd, 0x1f, 0x0, 0x38, 0xff, 0xc5, 0x0, 0x0,
    0x28, 0xf3, 0xff, 0xab, 0x0, 0x0, 0x2, 0xc8,
    0xff, 0xb0, 0x76, 0xb0, 0xff, 0xff, 0xdb, 0x11,
    0x0, 0x0, 0xf, 0x8e, 0xde, 0xf7, 0xdc, 0x90,
    0x87, 0xff, 0xb8, 0x3,

    /* U+0027 "'" */
    0x5c, 0xff, 0x18, 0x5c, 0xff, 0x10, 0x5c, 0xfd,
    0x1, 0x5c, 0xef, 0x0, 0x14, 0x32, 0x0,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x17, 0x0, 0x0, 0x0, 0x39,
    0xed, 0x23, 0x0, 0x1a, 0xea, 0xab, 0x2, 0x0,
    0xa3, 0xef, 0xf, 0x0, 0x1e, 0xfe, 0x84, 0x0,
    0x0, 0x77, 0xff, 0x35, 0x0, 0x0, 0xb5, 0xf3,
    0x2, 0x0, 0x0, 0xe2, 0xd8, 0x0, 0x0, 0x0,
    0xf5, 0xc0, 0x0, 0x0, 0x0, 0xfa, 0xc0, 0x0,
    0x0, 0x0, 0xeb, 0xd1, 0x0, 0x0, 0x0, 0xcc,
    0xe5, 0x0, 0x0, 0x0, 0x97, 0xff, 0x19, 0x0,
    0x0, 0x49, 0xff, 0x57, 0x0, 0x0, 0x3, 0xde,
    0xc0, 0x0, 0x0, 0x0, 0x53, 0xff, 0x49, 0x0,
    0x0, 0x0, 0x98, 0xf0, 0x1b, 0x0, 0x0, 0x3,
    0x7a, 0x8,

    /* U+0029 ")" */
    0x15, 0x1, 0x0, 0x0, 0x0, 0x8f, 0xb5, 0x6,
    0x0, 0x0, 0x22, 0xf0, 0x9a, 0x0, 0x0, 0x0,
    0x66, 0xff, 0x3e, 0x0, 0x0, 0x8, 0xec, 0xb7,
    0x0, 0x0, 0x0, 0x9f, 0xfd, 0x17, 0x0, 0x0,
    0x65, 0xff, 0x56, 0x0, 0x0, 0x41, 0xff, 0x81,
    0x0, 0x0, 0x30, 0xff, 0x9a, 0x0, 0x0, 0x28,
    0xff, 0x9f, 0x0, 0x0, 0x3a, 0xff, 0x8e, 0x0,
    0x0, 0x4e, 0xff, 0x6d, 0x0, 0x0, 0x83, 0xff,
    0x37, 0x0, 0x0, 0xc3, 0xe3, 0x2, 0x0, 0x23,
    0xfd, 0x7e, 0x0, 0x0, 0xac, 0xe3, 0xb, 0x0,
    0x6f, 0xf7, 0x38, 0x0, 0x0, 0x50, 0x37, 0x0,
    0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x18, 0xff, 0x42, 0x0, 0x0, 0x7,
    0x0, 0xe, 0xff, 0x39, 0x0, 0x2, 0x8e, 0xca,
    0x66, 0xff, 0x70, 0xad, 0xa3, 0x44, 0xa0, 0xeb,
    0xff, 0xfb, 0xbf, 0x67, 0x0, 0x4, 0xc8, 0xf5,
    0xcb, 0x5, 0x0, 0x0, 0x88, 0xf1, 0x30, 0xf0,
    0x87, 0x0, 0x0, 0x89, 0x6d, 0x0, 0x6d, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x51, 0xa8, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0x5c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0x5c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff,
    0x5c, 0x0, 0x0, 0x0, 0x78, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x54, 0x61, 0xd0, 0xd0,
    0xe7, 0xff, 0xe1, 0xd0, 0xd0, 0x44, 0x0, 0x0,
    0x0, 0x7c, 0xff, 0x5c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7c, 0xff, 0x5c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0x5c, 0x0, 0x0,
    0x0,

    /* U+002C "," */
    0x0, 0xfc, 0xba, 0x2, 0xfd, 0xb3, 0x25, 0xff,
    0x87, 0x87, 0xf7, 0x21, 0x29, 0x4a, 0x0,

    /* U+002D "-" */
    0x44, 0x9c, 0x9c, 0x9c, 0x66, 0x70, 0xff, 0xff,
    0xff, 0xa8,

    /* U+002E "." */
    0xc, 0x1b, 0x0, 0xcf, 0xfb, 0x26, 0xb0, 0xe8,
    0x1b,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0xad, 0xcf, 0x0, 0x0,
    0x0, 0xd, 0xf7, 0x77, 0x0, 0x0, 0x0, 0x5d,
    0xff, 0x20, 0x0, 0x0, 0x0, 0xb5, 0xc7, 0x0,
    0x0, 0x0, 0x12, 0xfa, 0x6f, 0x0, 0x0, 0x0,
    0x66, 0xfd, 0x19, 0x0, 0x0, 0x0, 0xbe, 0xbf,
    0x0, 0x0, 0x0, 0x19, 0xfd, 0x67, 0x0, 0x0,
    0x0, 0x6f, 0xfb, 0x14, 0x0, 0x0, 0x0, 0xc7,
    0xb7, 0x0, 0x0, 0x0, 0x20, 0xff, 0x5f, 0x0,
    0x0, 0x0, 0x78, 0xf8, 0xf, 0x0, 0x0, 0x0,
    0xd0, 0xaf, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x6b, 0xda, 0xf9, 0xe0, 0x7c, 0x1,
    0x0, 0x0, 0x62, 0xff, 0xe6, 0xa2, 0xdf, 0xff,
    0x7b, 0x0, 0x0, 0xd5, 0xfd, 0x24, 0x0, 0x15,
    0xf6, 0xeb, 0x3, 0xe, 0xff, 0xd2, 0x0, 0x0,
    0x0, 0xbc, 0xff, 0x26, 0x26, 0xff, 0xbb, 0x0,
    0x0, 0x0, 0xa5, 0xff, 0x3d, 0x2c, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0xa0, 0xff, 0x44, 0x2c, 0xff,
    0xb8, 0x0, 0x0, 0x0, 0xa0, 0xff, 0x43, 0x25,
    0xff, 0xbd, 0x0, 0x0, 0x0, 0xa5, 0xff, 0x3c,
    0xe, 0xff, 0xd5, 0x0, 0x0, 0x0, 0xbe, 0xff,
    0x23, 0x0, 0xd2, 0xff, 0x29, 0x0, 0x17, 0xf8,
    0xe8, 0x2, 0x0, 0x5d, 0xff, 0xe8, 0xa3, 0xdf,
    0xff, 0x76, 0x0, 0x0, 0x0, 0x67, 0xda, 0xfa,
    0xe1, 0x7b, 0x1, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x22, 0x83, 0xe4, 0x48, 0xbd, 0xfe,
    0xff, 0xff, 0xb0, 0xf1, 0x9d, 0xef, 0xff, 0x31,
    0x8, 0x0, 0xe4, 0xff, 0x0, 0x0, 0x0, 0xe4,
    0xff, 0x0, 0x0, 0x0, 0xe4, 0xff, 0x0, 0x0,
    0x0, 0xe4, 0xff, 0x0, 0x0, 0x0, 0xe4, 0xff,
    0x0, 0x0, 0x0, 0xe4, 0xff, 0x0, 0x0, 0x0,
    0xe4, 0xff, 0x0, 0x0, 0x0, 0xe4, 0xff, 0x0,
    0x0, 0x0, 0xe4, 0xff,

    /* U+0032 "2" */
    0x0, 0x5, 0x7f, 0xdd, 0xf7, 0xd9, 0x7a, 0x5,
    0x0, 0x0, 0xa4, 0xff, 0xd7, 0xa0, 0xe4, 0xff,
    0x8a, 0x0, 0x24, 0xff, 0xd7, 0x6, 0x0, 0x1e,
    0xfa, 0xed, 0x0, 0x3e, 0xc4, 0x72, 0x0, 0x0,
    0x0, 0xe1, 0xfe, 0x6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xfc, 0xce, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xb1, 0xff, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x83, 0xff, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x63, 0xff, 0xc9, 0xa, 0x0, 0x0,
    0x0, 0x0, 0x47, 0xfb, 0xda, 0x15, 0x0, 0x0,
    0x0, 0x0, 0x31, 0xf3, 0xe8, 0x22, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xe6, 0xff, 0xbf, 0x94, 0x94,
    0x94, 0x94, 0x4a, 0x24, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+0033 "3" */
    0x0, 0xe, 0x8f, 0xe4, 0xf6, 0xd5, 0x6f, 0x2,
    0x0, 0x0, 0xbb, 0xff, 0xce, 0x9e, 0xe1, 0xff,
    0x7a, 0x0, 0x29, 0xff, 0xcf, 0x2, 0x0, 0x19,
    0xfc, 0xdd, 0x0, 0xa, 0x28, 0x19, 0x0, 0x0,
    0x0, 0xf1, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x75, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x0,
    0xf4, 0xff, 0xff, 0xb3, 0x9, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0xa0, 0xe6, 0xfb, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0xee, 0xf1, 0x8,
    0x1f, 0x58, 0x2f, 0x0, 0x0, 0x0, 0xc2, 0xff,
    0x24, 0x42, 0xff, 0xc1, 0x1, 0x0, 0xf, 0xec,
    0xfa, 0xd, 0x3, 0xcd, 0xff, 0xcc, 0x9e, 0xdf,
    0xff, 0x90, 0x0, 0x0, 0x10, 0x8f, 0xdf, 0xf5,
    0xd3, 0x6b, 0x3, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0xea, 0xff, 0x48,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x91, 0xff, 0xff,
    0x48, 0x0, 0x0, 0x0, 0x0, 0x2a, 0xfb, 0xfd,
    0xff, 0x48, 0x0, 0x0, 0x0, 0x0, 0xbc, 0xed,
    0xb2, 0xff, 0x48, 0x0, 0x0, 0x0, 0x52, 0xff,
    0x6c, 0xa0, 0xff, 0x48, 0x0, 0x0, 0x8, 0xde,
    0xd4, 0x4, 0xa0, 0xff, 0x48, 0x0, 0x0, 0x7d,
    0xff, 0x45, 0x0, 0xa0, 0xff, 0x48, 0x0, 0x1d,
    0xf5, 0xb1, 0x0, 0x0, 0xa0, 0xff, 0x48, 0x0,
    0x8a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x53, 0x98, 0x98, 0x98, 0x98, 0xd9, 0xff,
    0xb5, 0x68, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa0,
    0xff, 0x48, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa0, 0xff, 0x48, 0x0,

    /* U+0035 "5" */
    0x12, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18,
    0x2d, 0xff, 0xdf, 0xb8, 0xb8, 0xb8, 0xb8, 0x11,
    0x48, 0xff, 0x78, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x63, 0xff, 0x5b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7e, 0xff, 0xc7, 0xee, 0xf3, 0xad, 0x18, 0x0,
    0x95, 0xff, 0xd9, 0xa7, 0xdd, 0xff, 0xc2, 0x0,
    0x3, 0x35, 0x6, 0x0, 0x9, 0xd5, 0xff, 0x37,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x81, 0xff, 0x60,
    0x5a, 0x55, 0x0, 0x0, 0x0, 0x80, 0xff, 0x61,
    0xc8, 0xfe, 0x29, 0x0, 0x4, 0xcf, 0xff, 0x33,
    0x4d, 0xff, 0xec, 0xa0, 0xcf, 0xff, 0xb3, 0x0,
    0x0, 0x4c, 0xc9, 0xf4, 0xea, 0x99, 0xe, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x32, 0xa8, 0xe5, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x5a, 0xfc, 0xff, 0xd0, 0x86,
    0x0, 0x0, 0x0, 0x23, 0xf6, 0xe9, 0x3b, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x92, 0xff, 0x53, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xda, 0xf7, 0x86,
    0xe7, 0xf6, 0xb2, 0x1a, 0x0, 0x3, 0xfe, 0xff,
    0xf2, 0xa9, 0xd8, 0xff, 0xc2, 0x0, 0x11, 0xff,
    0xf6, 0x27, 0x0, 0x8, 0xd6, 0xff, 0x35, 0x10,
    0xff, 0xd2, 0x0, 0x0, 0x0, 0x85, 0xff, 0x5f,
    0x2, 0xf5, 0xed, 0x1, 0x0, 0x0, 0x83, 0xff,
    0x64, 0x0, 0xb1, 0xff, 0x52, 0x0, 0x6, 0xd3,
    0xff, 0x30, 0x0, 0x2f, 0xf7, 0xf6, 0xaa, 0xd3,
    0xff, 0xaf, 0x0, 0x0, 0x0, 0x3a, 0xc6, 0xf8,
    0xe9, 0x90, 0x9, 0x0,

    /* U+0037 "7" */
    0x74, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x6c, 0x44, 0x98, 0x98, 0x98, 0x98, 0x98, 0xdd,
    0xff, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xee, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x69, 0xff, 0x6c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xd7, 0xf3, 0xe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x48, 0xff, 0x96, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb7, 0xff, 0x2c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x28, 0xfe, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x96, 0xff, 0x55, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0xf4, 0xe6, 0x4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x75, 0xff, 0x7f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xe0, 0xfb, 0x1a,
    0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x1, 0x6c, 0xd6, 0xf5, 0xdb, 0x7a, 0x4,
    0x0, 0x0, 0x6c, 0xff, 0xe5, 0xa0, 0xe1, 0xff,
    0x83, 0x0, 0x0, 0xcc, 0xff, 0x2a, 0x0, 0x1a,
    0xfb, 0xe3, 0x0, 0x0, 0xe1, 0xfe, 0x4, 0x0,
    0x0, 0xeb, 0xf7, 0x0, 0x0, 0x98, 0xff, 0x75,
    0xb, 0x5f, 0xff, 0xad, 0x0, 0x0, 0xc, 0xbf,
    0xff, 0xff, 0xff, 0xcc, 0x13, 0x0, 0x0, 0x52,
    0xf8, 0xe1, 0xa3, 0xdd, 0xfa, 0x5e, 0x0, 0x7,
    0xeb, 0xee, 0x12, 0x0, 0xc, 0xe3, 0xf4, 0xf,
    0x27, 0xff, 0xc1, 0x0, 0x0, 0x0, 0xaa, 0xff,
    0x3b, 0x13, 0xfe, 0xec, 0xf, 0x0, 0x5, 0xdb,
    0xff, 0x25, 0x0, 0xa4, 0xff, 0xdd, 0x9e, 0xd4,
    0xff, 0xb8, 0x0, 0x0, 0x7, 0x7a, 0xd8, 0xf6,
    0xdc, 0x86, 0xb, 0x0,

    /* U+0039 "9" */
    0x0, 0x1, 0x73, 0xdd, 0xf8, 0xcf, 0x48, 0x0,
    0x0, 0x0, 0x7e, 0xff, 0xe0, 0xa9, 0xf2, 0xfb,
    0x3a, 0x0, 0x9, 0xf3, 0xf0, 0x14, 0x0, 0x40,
    0xff, 0xbc, 0x0, 0x35, 0xff, 0xac, 0x0, 0x0,
    0x0, 0xdd, 0xfa, 0x6, 0x35, 0xff, 0xaa, 0x0,
    0x0, 0x0, 0xc2, 0xff, 0x1d, 0x10, 0xfb, 0xeb,
    0xe, 0x0, 0x1a, 0xea, 0xff, 0x21, 0x0, 0x99,
    0xff, 0xd8, 0x97, 0xe0, 0xff, 0xff, 0x13, 0x0,
    0xb, 0x9d, 0xf1, 0xef, 0x8a, 0xe5, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xc6, 0xff,
    0x42, 0x0, 0x0, 0x0, 0x68, 0xc2, 0xf5, 0xff,
    0x87, 0x0, 0x0, 0x0, 0x0, 0x9e, 0xed, 0xba,
    0x4f, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0xb9, 0xe2, 0x14, 0xdc, 0xfb, 0x21, 0xe, 0x1a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x19, 0x0, 0xdb, 0xfa, 0x20,
    0xbc, 0xe5, 0x16,

    /* U+003B ";" */
    0x9, 0xd8, 0xca, 0x1, 0x10, 0xf4, 0xeb, 0x4,
    0x0, 0x16, 0x12, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xb4, 0x0,
    0x0, 0xd9, 0xda, 0x0, 0x1, 0xf5, 0xb8, 0x0,
    0x4c, 0xff, 0x56, 0x0, 0x38, 0x88, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xc0, 0x7,
    0x0, 0x0, 0x4, 0x5c, 0xd2, 0xff, 0xff, 0x7,
    0xb, 0x6e, 0xe1, 0xff, 0xf3, 0x9a, 0x33, 0x0,
    0x7f, 0xff, 0xdf, 0x69, 0xd, 0x0, 0x0, 0x0,
    0x5d, 0xf9, 0xfe, 0xba, 0x54, 0x5, 0x0, 0x0,
    0x0, 0x1a, 0x8b, 0xf2, 0xff, 0xe9, 0x89, 0x2,
    0x0, 0x0, 0x0, 0x10, 0x7a, 0xe9, 0xff, 0x8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x69, 0x5,

    /* U+003D "=" */
    0xdc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x8c,
    0xa4, 0xa4, 0xa4, 0xa4, 0xa4, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8c, 0xa4, 0xa4,
    0xa4, 0xa4, 0xa4, 0x8f, 0xdc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0,

    /* U+003E ">" */
    0xc6, 0x53, 0x2, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xdd, 0x6d, 0xb, 0x0, 0x0, 0x26, 0x88,
    0xe6, 0xff, 0xee, 0x87, 0x1a, 0x0, 0x0, 0x3,
    0x4d, 0xc2, 0xff, 0xbf, 0x0, 0x2, 0x46, 0xaa,
    0xf9, 0xff, 0x95, 0x7e, 0xe0, 0xff, 0xf9, 0xa0,
    0x2d, 0x0, 0xff, 0xee, 0x87, 0x1a, 0x0, 0x0,
    0x0, 0x6e, 0xc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x36, 0xbe, 0xf1, 0xe7, 0xa2, 0x17, 0x0,
    0x1d, 0xf1, 0xfe, 0xbb, 0xd1, 0xff, 0xc0, 0x0,
    0x6b, 0xff, 0x8a, 0x0, 0x2, 0xda, 0xff, 0x15,
    0xe, 0x1c, 0xa, 0x0, 0x0, 0xc3, 0xff, 0x22,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xfd, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xf0, 0xf5, 0x33, 0x0,
    0x0, 0x0, 0x3, 0xda, 0xfc, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xad, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0x70, 0x3d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x40, 0xfe, 0x97, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x36, 0xf1, 0x85, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x32, 0xa3, 0xdb, 0xf7,
    0xe1, 0xb1, 0x45, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7e, 0xfd, 0xaf, 0x5c, 0x3f, 0x50, 0x92,
    0xf7, 0x8d, 0x1, 0x0, 0x0, 0x0, 0x77, 0xf4,
    0x4a, 0x0, 0x0, 0x1, 0x0, 0x0, 0x2b, 0xea,
    0x68, 0x0, 0x0, 0x26, 0xf8, 0x65, 0x0, 0xc,
    0xa6, 0xf8, 0xe8, 0x83, 0x1, 0x5a, 0xeb, 0x7,
    0x0, 0x8d, 0xd7, 0x1, 0x0, 0xab, 0xe7, 0x55,
    0x9c, 0xf9, 0x0, 0x4, 0xed, 0x43, 0x0, 0xe2,
    0x80, 0x0, 0x33, 0xff, 0x54, 0x0, 0x7e, 0xe6,
    0x0, 0x0, 0xba, 0x7e, 0xd, 0xff, 0x4e, 0x0,
    0x88, 0xf6, 0x6, 0x0, 0x91, 0xd2, 0x0, 0x0,
    0xa0, 0x8e, 0x2c, 0xff, 0x32, 0x0, 0xba, 0xd1,
    0x0, 0x0, 0xa4, 0xbe, 0x0, 0x0, 0x9f, 0x95,
    0x27, 0xff, 0x33, 0x0, 0xcd, 0xb8, 0x0, 0x0,
    0xb8, 0xaa, 0x0, 0x0, 0xba, 0x74, 0x17, 0xff,
    0x46, 0x0, 0xc1, 0xc4, 0x0, 0x3, 0xe4, 0xa1,
    0x0, 0xd, 0xf4, 0x3c, 0x0, 0xe0, 0x81, 0x0,
    0x7e, 0xf9, 0x4e, 0x8d, 0xf4, 0xdd, 0x3a, 0xad,
    0xc6, 0x0, 0x0, 0x92, 0xe4, 0xb, 0x11, 0xc1,
    0xf6, 0x9f, 0x2d, 0xd5, 0xf8, 0xb8, 0x1c, 0x0,
    0x0, 0x17, 0xef, 0xac, 0x7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xed, 0xdd, 0x79, 0x4f, 0x4a, 0x6b, 0x76,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0x86, 0xd3, 0xf1, 0xf0, 0xcb, 0x6e, 0x0, 0x0,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0xbe, 0xff, 0x65, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xfd,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x76, 0xff, 0xe8, 0xfe, 0x1e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd1, 0xf2, 0x5c, 0xff,
    0x78, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xa3, 0x9, 0xf3, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x89, 0xff, 0x4b, 0x0, 0xa7, 0xff, 0x2f,
    0x0, 0x0, 0x0, 0x1, 0xe2, 0xee, 0x5, 0x0,
    0x50, 0xff, 0x8b, 0x0, 0x0, 0x0, 0x40, 0xff,
    0xe9, 0xac, 0xac, 0xb3, 0xff, 0xe4, 0x2, 0x0,
    0x0, 0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x42, 0x0, 0x7, 0xef, 0xf2, 0x6, 0x0,
    0x0, 0x0, 0x51, 0xff, 0x9e, 0x0, 0x53, 0xff,
    0xa9, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0xf1,
    0x8, 0xae, 0xff, 0x5b, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb3, 0xff, 0x56,

    /* U+0042 "B" */
    0xd8, 0xff, 0xff, 0xff, 0xf2, 0xd0, 0x6c, 0x3,
    0x0, 0xd8, 0xff, 0xb7, 0xac, 0xb8, 0xf5, 0xff,
    0x8b, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x32,
    0xff, 0xea, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0,
    0x6, 0xfe, 0xf4, 0x0, 0xd8, 0xff, 0x24, 0x0,
    0xe, 0x85, 0xff, 0xa4, 0x0, 0xd8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc5, 0xc, 0x0, 0xd8, 0xff,
    0x9c, 0x8c, 0x91, 0xcf, 0xff, 0x8a, 0x0, 0xd8,
    0xff, 0x24, 0x0, 0x0, 0x5, 0xe1, 0xfd, 0x1d,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0xba, 0xff,
    0x40, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x15, 0xec,
    0xff, 0x25, 0xd8, 0xff, 0xb4, 0xa8, 0xb0, 0xeb,
    0xff, 0xb7, 0x0, 0xd8, 0xff, 0xff, 0xff, 0xf8,
    0xd8, 0x80, 0xb, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0xe, 0x89, 0xde, 0xf9, 0xe3, 0x94,
    0x11, 0x0, 0x0, 0xc, 0xd1, 0xff, 0xe4, 0xb7,
    0xde, 0xff, 0xcd, 0x6, 0x0, 0x8a, 0xff, 0xac,
    0x5, 0x0, 0x3, 0xac, 0xff, 0x70, 0x1, 0xe7,
    0xfd, 0x1e, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xb6,
    0x1c, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x3, 0x30, 0xff, 0xca, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x32, 0xff, 0xcb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xe8, 0xfc, 0x1a, 0x0, 0x0, 0x0, 0x3e,
    0xfc, 0xb5, 0x0, 0x8c, 0xff, 0xa0, 0x2, 0x0,
    0x3, 0xac, 0xff, 0x72, 0x0, 0xe, 0xd7, 0xff,
    0xdc, 0xb2, 0xdd, 0xff, 0xce, 0x6, 0x0, 0x0,
    0x12, 0x92, 0xe2, 0xfa, 0xe2, 0x92, 0x11, 0x0,

    /* U+0044 "D" */
    0xd8, 0xff, 0xff, 0xfc, 0xdf, 0x93, 0x1b, 0x0,
    0x0, 0xd8, 0xff, 0xb7, 0xae, 0xd3, 0xff, 0xe8,
    0x2e, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x66,
    0xff, 0xd3, 0x3, 0xd8, 0xff, 0x24, 0x0, 0x0,
    0x0, 0xc5, 0xff, 0x44, 0xd8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0x7c, 0xff, 0x84, 0xd8, 0xff, 0x24,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x9d, 0xd8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x9d, 0xd8,
    0xff, 0x24, 0x0, 0x0, 0x0, 0x7e, 0xff, 0x85,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0xc8, 0xff,
    0x47, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x6c, 0xff,
    0xd6, 0x4, 0xd8, 0xff, 0xb4, 0xab, 0xd4, 0xff,
    0xe9, 0x2f, 0x0, 0xd8, 0xff, 0xff, 0xfb, 0xde,
    0x92, 0x1b, 0x0, 0x0,

    /* U+0045 "E" */
    0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8c,
    0xd8, 0xff, 0xb7, 0xac, 0xac, 0xac, 0xac, 0x5e,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0xd8, 0xff, 0xad, 0xa0, 0xa0, 0xa0, 0x82, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0xb4, 0xa8, 0xa8, 0xa8, 0xa8, 0x63,
    0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x98,

    /* U+0046 "F" */
    0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x64,
    0xd8, 0xff, 0xb7, 0xac, 0xac, 0xac, 0xac, 0x43,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0xb4, 0xa8, 0xa8, 0xa8, 0x78, 0x0,
    0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb8, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x13, 0x93, 0xe2, 0xf8, 0xda, 0x94,
    0x15, 0x0, 0x0, 0xf, 0xd8, 0xff, 0xe4, 0xb4,
    0xd9, 0xff, 0xd9, 0xd, 0x0, 0x8c, 0xff, 0xb5,
    0x7, 0x0, 0x2, 0x9e, 0xff, 0x7f, 0x0, 0xe8,
    0xff, 0x28, 0x0, 0x0, 0x0, 0x25, 0xd0, 0x9e,
    0x18, 0xff, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x29, 0xff, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x29, 0xff, 0xd5, 0x0,
    0x0, 0x70, 0xff, 0xff, 0xff, 0xe0, 0x11, 0xff,
    0xf1, 0x0, 0x0, 0x3f, 0x90, 0x9a, 0xff, 0xe0,
    0x0, 0xdc, 0xff, 0x36, 0x0, 0x0, 0x0, 0x18,
    0xff, 0xe0, 0x0, 0x79, 0xff, 0xc2, 0xb, 0x0,
    0x0, 0x2b, 0xff, 0xe0, 0x0, 0x6, 0xc4, 0xff,
    0xe7, 0xac, 0xbc, 0xf5, 0xff, 0xa3, 0x0, 0x0,
    0x7, 0x7c, 0xd7, 0xf7, 0xf0, 0xc2, 0x65, 0x3,

    /* U+0048 "H" */
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0xc8,
    0xff, 0x30, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0x30, 0xd8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0x0, 0xc8, 0xff, 0x30, 0xd8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x30,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0xc8,
    0xff, 0x30, 0xd8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xd8, 0xff, 0xb4, 0xa8,
    0xa8, 0xa8, 0xa8, 0xec, 0xff, 0x30, 0xd8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x30,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0xc8,
    0xff, 0x30, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0x30, 0xd8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0x0, 0xc8, 0xff, 0x30, 0xd8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x0, 0xc8, 0xff, 0x30,

    /* U+0049 "I" */
    0xb8, 0xff, 0x40, 0xb8, 0xff, 0x40, 0xb8, 0xff,
    0x40, 0xb8, 0xff, 0x40, 0xb8, 0xff, 0x40, 0xb8,
    0xff, 0x40, 0xb8, 0xff, 0x40, 0xb8, 0xff, 0x40,
    0xb8, 0xff, 0x40, 0xb8, 0xff, 0x40, 0xb8, 0xff,
    0x40, 0xb8, 0xff, 0x40,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0xff, 0xc8,
    0x51, 0x88, 0x31, 0x0, 0x0, 0x38, 0xff, 0xc2,
    0x78, 0xff, 0x93, 0x0, 0x0, 0x82, 0xff, 0x9b,
    0x1b, 0xf0, 0xff, 0xc0, 0xbb, 0xff, 0xf7, 0x2c,
    0x0, 0x30, 0xb7, 0xeb, 0xed, 0xb9, 0x2f, 0x0,

    /* U+004B "K" */
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x8, 0xcd, 0xff,
    0x8d, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x9f,
    0xff, 0xbd, 0x3, 0x0, 0xd8, 0xff, 0x24, 0x0,
    0x6b, 0xff, 0xe1, 0x14, 0x0, 0x0, 0xd8, 0xff,
    0x24, 0x3b, 0xfa, 0xf7, 0x32, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x3b, 0xe6, 0xff, 0x5d, 0x0, 0x0,
    0x0, 0x0, 0xd8, 0xff, 0xd9, 0xff, 0xe4, 0x5,
    0x0, 0x0, 0x0, 0x0, 0xd8, 0xff, 0xff, 0xfa,
    0xff, 0x82, 0x0, 0x0, 0x0, 0x0, 0xd8, 0xff,
    0xc8, 0x34, 0xf8, 0xfb, 0x32, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x2b, 0x0, 0x79, 0xff, 0xd2, 0x6,
    0x0, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x3, 0xcb,
    0xff, 0x83, 0x0, 0x0, 0xd8, 0xff, 0x24, 0x0,
    0x0, 0x2c, 0xf9, 0xfb, 0x33, 0x0, 0xd8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x7d, 0xff, 0xd3, 0x6,

    /* U+004C "L" */
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0xff, 0xb4, 0xa8, 0xa8, 0xa8, 0xa8, 0x31,
    0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4c,

    /* U+004D "M" */
    0xd8, 0xff, 0xe3, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xe3, 0xff, 0xd4, 0xd8, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x41, 0xff, 0xff, 0xd4,
    0xd8, 0xfd, 0xff, 0x9b, 0x0, 0x0, 0x0, 0x0,
    0x9c, 0xff, 0xfd, 0xd4, 0xd8, 0xf6, 0xcc, 0xef,
    0x7, 0x0, 0x0, 0x7, 0xef, 0xc9, 0xf8, 0xd4,
    0xd8, 0xfe, 0x70, 0xff, 0x52, 0x0, 0x0, 0x53,
    0xff, 0x6e, 0xff, 0xd4, 0xd8, 0xff, 0x1f, 0xfb,
    0xad, 0x0, 0x0, 0xae, 0xfa, 0x20, 0xff, 0xd4,
    0xd8, 0xff, 0x11, 0xb6, 0xf8, 0x10, 0x10, 0xf8,
    0xb2, 0x15, 0xff, 0xd4, 0xd8, 0xff, 0x1a, 0x59,
    0xff, 0x64, 0x65, 0xff, 0x55, 0x1f, 0xff, 0xd4,
    0xd8, 0xff, 0x1f, 0x9, 0xf2, 0xbf, 0xc0, 0xf0,
    0x8, 0x24, 0xff, 0xd4, 0xd8, 0xff, 0x20, 0x0,
    0xa0, 0xfe, 0xfe, 0x9b, 0x0, 0x24, 0xff, 0xd4,
    0xd8, 0xff, 0x20, 0x0, 0x43, 0xff, 0xff, 0x3e,
    0x0, 0x24, 0xff, 0xd4, 0xd8, 0xff, 0x20, 0x0,
    0x2, 0xe4, 0xe0, 0x1, 0x0, 0x24, 0xff, 0xd4,

    /* U+004E "N" */
    0xd8, 0xff, 0x6f, 0x0, 0x0, 0x0, 0x0, 0xc8,
    0xff, 0x30, 0xd8, 0xff, 0xf0, 0x17, 0x0, 0x0,
    0x0, 0xc8, 0xff, 0x30, 0xd8, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xc8, 0xff, 0x30, 0xd8, 0xff,
    0xeb, 0xfe, 0x39, 0x0, 0x0, 0xc8, 0xff, 0x30,
    0xd8, 0xff, 0x66, 0xff, 0xcd, 0x2, 0x0, 0xc8,
    0xff, 0x30, 0xd8, 0xff, 0x24, 0xaa, 0xff, 0x68,
    0x0, 0xc8, 0xff, 0x30, 0xd8, 0xff, 0x24, 0x1c,
    0xf4, 0xed, 0x13, 0xc8, 0xff, 0x30, 0xd8, 0xff,
    0x24, 0x0, 0x79, 0xff, 0x98, 0xc8, 0xff, 0x30,
    0xd8, 0xff, 0x24, 0x0, 0x6, 0xda, 0xfd, 0xeb,
    0xff, 0x30, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x48,
    0xff, 0xff, 0xff, 0x30, 0xd8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0xb1, 0xff, 0xff, 0x30, 0xd8, 0xff,
    0x24, 0x0, 0x0, 0x0, 0x21, 0xf7, 0xff, 0x30,

    /* U+004F "O" */
    0x0, 0x0, 0x9, 0x80, 0xdb, 0xf7, 0xdd, 0x87,
    0xc, 0x0, 0x0, 0x0, 0x6, 0xc4, 0xff, 0xe7,
    0xbb, 0xe4, 0xff, 0xce, 0xa, 0x0, 0x0, 0x79,
    0xff, 0xb9, 0x9, 0x0, 0x6, 0xad, 0xff, 0x84,
    0x0, 0x0, 0xdc, 0xff, 0x2e, 0x0, 0x0, 0x0,
    0x22, 0xfd, 0xe5, 0x1, 0x17, 0xff, 0xe9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdc, 0xff, 0x20, 0x2e,
    0xff, 0xcd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc2,
    0xff, 0x36, 0x2f, 0xff, 0xce, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc2, 0xff, 0x36, 0x17, 0xff, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdc, 0xff, 0x1f,
    0x0, 0xdf, 0xff, 0x2c, 0x0, 0x0, 0x0, 0x20,
    0xfd, 0xe6, 0x1, 0x0, 0x7c, 0xff, 0xb6, 0x7,
    0x0, 0x4, 0xa7, 0xff, 0x86, 0x0, 0x0, 0x7,
    0xc6, 0xff, 0xe5, 0xb6, 0xe0, 0xff, 0xcf, 0xb,
    0x0, 0x0, 0x0, 0x9, 0x81, 0xdb, 0xf7, 0xdf,
    0x8a, 0xd, 0x0, 0x0,

    /* U+0050 "P" */
    0xd8, 0xff, 0xff, 0xff, 0xfd, 0xe5, 0x98, 0x13,
    0x0, 0xd8, 0xff, 0xb7, 0xac, 0xb0, 0xe2, 0xff,
    0xd6, 0xb, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x8,
    0xc2, 0xff, 0x6b, 0xd8, 0xff, 0x24, 0x0, 0x0,
    0x0, 0x64, 0xff, 0x9f, 0xd8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0x98, 0xd8, 0xff, 0x24,
    0x0, 0x4, 0x36, 0xe0, 0xff, 0x56, 0xd8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa6, 0x2, 0xd8,
    0xff, 0xb4, 0xa8, 0xa3, 0x84, 0x41, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0xc, 0x86, 0xdd, 0xf7, 0xdb, 0x81,
    0x9, 0x0, 0x0, 0x0, 0xa, 0xcd, 0xff, 0xe4,
    0xbb, 0xe7, 0xff, 0xc7, 0x7, 0x0, 0x0, 0x85,
    0xff, 0xaf, 0x6, 0x0, 0x8, 0xb4, 0xff, 0x7a,
    0x0, 0x1, 0xe7, 0xfd, 0x24, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xdd, 0x0, 0x23, 0xff, 0xdd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x16, 0x3a,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0xff, 0x2e, 0x3b, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xce, 0xff, 0x2e, 0x23, 0xff, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x19,
    0x2, 0xe9, 0xfe, 0x23, 0x0, 0x0, 0x0, 0x2a,
    0xff, 0xe2, 0x0, 0x0, 0x88, 0xff, 0xad, 0x5,
    0x0, 0x6, 0xb1, 0xff, 0x80, 0x0, 0x0, 0xb,
    0xce, 0xff, 0xe2, 0xb6, 0xe3, 0xff, 0xc8, 0xa,
    0x0, 0x0, 0x0, 0xd, 0x87, 0xdd, 0xf9, 0xf5,
    0xff, 0xb4, 0xd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xb8, 0xff, 0xd3, 0x9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8d,
    0x5c, 0x0,

    /* U+0052 "R" */
    0xd8, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0x6b, 0x4,
    0x0, 0xd8, 0xff, 0xb7, 0xac, 0xb8, 0xf3, 0xff,
    0x9b, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x27,
    0xf7, 0xfe, 0x16, 0xd8, 0xff, 0x24, 0x0, 0x0,
    0x0, 0xc2, 0xff, 0x3c, 0xd8, 0xff, 0x24, 0x0,
    0x0, 0x0, 0xd2, 0xff, 0x2d, 0xd8, 0xff, 0x24,
    0x0, 0xe, 0x76, 0xff, 0xdd, 0x2, 0xd8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe8, 0x35, 0x0, 0xd8,
    0xff, 0xb4, 0xa8, 0xd9, 0xff, 0x63, 0x0, 0x0,
    0xd8, 0xff, 0x24, 0x0, 0x37, 0xff, 0xdd, 0x5,
    0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0xb0, 0xff,
    0x69, 0x0, 0xd8, 0xff, 0x24, 0x0, 0x0, 0x2c,
    0xfd, 0xe6, 0xa, 0xd8, 0xff, 0x24, 0x0, 0x0,
    0x0, 0xa3, 0xff, 0x78,

    /* U+0053 "S" */
    0x0, 0x0, 0x51, 0xc4, 0xee, 0xf2, 0xbe, 0x46,
    0x0, 0x0, 0x0, 0x78, 0xff, 0xf4, 0xb2, 0xb8,
    0xfa, 0xff, 0x56, 0x0, 0x2, 0xf1, 0xfe, 0x27,
    0x0, 0x0, 0x3e, 0xff, 0xde, 0x0, 0x7, 0xff,
    0xf6, 0x6, 0x0, 0x0, 0x0, 0x8b, 0x93, 0x5,
    0x0, 0xbd, 0xff, 0xb5, 0x2b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xcf, 0xff, 0xff, 0xcd,
    0x6d, 0x8, 0x0, 0x0, 0x0, 0x0, 0x3, 0x58,
    0xb8, 0xfc, 0xff, 0xdd, 0x26, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x19, 0xa2, 0xff, 0xc7, 0x0,
    0x43, 0xb4, 0x70, 0x0, 0x0, 0x0, 0x2, 0xf3,
    0xff, 0xa, 0x2e, 0xff, 0xe4, 0x15, 0x0, 0x0,
    0x22, 0xfd, 0xf7, 0x4, 0x0, 0x95, 0xff, 0xf0,
    0xb1, 0xae, 0xf1, 0xff, 0x8c, 0x0, 0x0, 0x0,
    0x5c, 0xc4, 0xf3, 0xf0, 0xcc, 0x62, 0x2, 0x0,

    /* U+0054 "T" */
    0xa4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x6e, 0xac, 0xac, 0xb6, 0xff, 0xf1,
    0xac, 0xac, 0xac, 0x40, 0x0, 0x0, 0x0, 0x20,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0xd4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0xd4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xff, 0x7c, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0xff, 0x7c, 0x4, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0x7c, 0x4, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0x7c,
    0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xff, 0x7c, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0xff, 0x7c, 0x4, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0x7c, 0x3, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x7c, 0xff, 0x7b,
    0x0, 0xf7, 0xfe, 0x9, 0x0, 0x0, 0x0, 0x91,
    0xff, 0x6e, 0x0, 0xbf, 0xff, 0x6e, 0x0, 0x0,
    0x12, 0xe3, 0xff, 0x34, 0x0, 0x3c, 0xfa, 0xff,
    0xc5, 0xb0, 0xea, 0xff, 0xa4, 0x0, 0x0, 0x0,
    0x37, 0xb6, 0xf0, 0xfa, 0xd7, 0x75, 0x3, 0x0,

    /* U+0056 "V" */
    0xb1, 0xff, 0x76, 0x0, 0x0, 0x0, 0x0, 0x19,
    0xfe, 0xfb, 0x14, 0x5a, 0xff, 0xc5, 0x0, 0x0,
    0x0, 0x0, 0x68, 0xff, 0xb8, 0x0, 0xc, 0xf6,
    0xfd, 0x16, 0x0, 0x0, 0x0, 0xb7, 0xff, 0x61,
    0x0, 0x0, 0xac, 0xff, 0x63, 0x0, 0x0, 0xd,
    0xf9, 0xf9, 0x10, 0x0, 0x0, 0x55, 0xff, 0xb1,
    0x0, 0x0, 0x56, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x9, 0xf4, 0xf6, 0xa, 0x0, 0xa6, 0xff, 0x5b,
    0x0, 0x0, 0x0, 0x0, 0xa7, 0xff, 0x4f, 0x5,
    0xf0, 0xf7, 0xc, 0x0, 0x0, 0x0, 0x0, 0x50,
    0xff, 0x9e, 0x45, 0xff, 0xac, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf1, 0xea, 0x97, 0xff, 0x55,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa2, 0xff,
    0xf9, 0xf4, 0x9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4b, 0xff, 0xff, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xee, 0xff, 0x4f,
    0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x85, 0xff, 0x70, 0x0, 0x0, 0x0, 0xd7, 0xfd,
    0x10, 0x0, 0x0, 0x46, 0xff, 0xaf, 0x4d, 0xff,
    0xa3, 0x0, 0x0, 0x15, 0xff, 0xff, 0x4b, 0x0,
    0x0, 0x78, 0xff, 0x77, 0x15, 0xff, 0xd5, 0x0,
    0x0, 0x53, 0xff, 0xff, 0x88, 0x0, 0x0, 0xaa,
    0xff, 0x3f, 0x0, 0xdd, 0xfc, 0xa, 0x0, 0x90,
    0xfc, 0xde, 0xc5, 0x0, 0x0, 0xdc, 0xfc, 0xb,
    0x0, 0xa5, 0xff, 0x39, 0x0, 0xce, 0xca, 0x94,
    0xf9, 0x9, 0xe, 0xfe, 0xcf, 0x0, 0x0, 0x6d,
    0xff, 0x6b, 0xe, 0xfc, 0x8a, 0x55, 0xff, 0x40,
    0x40, 0xff, 0x97, 0x0, 0x0, 0x35, 0xff, 0x9d,
    0x49, 0xff, 0x49, 0x16, 0xff, 0x7d, 0x72, 0xff,
    0x5f, 0x0, 0x0, 0x5, 0xf7, 0xcf, 0x87, 0xfb,
    0xd, 0x0, 0xd6, 0xba, 0xa4, 0xff, 0x27, 0x0,
    0x0, 0x0, 0xc5, 0xfa, 0xcc, 0xc9, 0x0, 0x0,
    0x96, 0xf3, 0xd9, 0xee, 0x1, 0x0, 0x0, 0x0,
    0x8d, 0xff, 0xff, 0x88, 0x0, 0x0, 0x57, 0xff,
    0xff, 0xb7, 0x0, 0x0, 0x0, 0x0, 0x55, 0xff,
    0xff, 0x48, 0x0, 0x0, 0x17, 0xff, 0xff, 0x7f,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xfb, 0xc,
    0x0, 0x0, 0x0, 0xd8, 0xff, 0x47, 0x0, 0x0,

    /* U+0058 "X" */
    0x4f, 0xff, 0xe9, 0xd, 0x0, 0x0, 0x1, 0xcd,
    0xff, 0x75, 0x0, 0xba, 0xff, 0x85, 0x0, 0x0,
    0x5e, 0xff, 0xdb, 0x6, 0x0, 0x2b, 0xfb, 0xf6,
    0x1d, 0x9, 0xe2, 0xff, 0x4e, 0x0, 0x0, 0x0,
    0x93, 0xff, 0xa2, 0x7b, 0xff, 0xba, 0x0, 0x0,
    0x0, 0x0, 0x11, 0xec, 0xfe, 0xf5, 0xfb, 0x2a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xff, 0xff,
    0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x79,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1a, 0xf3, 0xfa, 0xed, 0xfe, 0x37, 0x0, 0x0,
    0x0, 0x0, 0xa3, 0xff, 0x92, 0x6c, 0xff, 0xc9,
    0x1, 0x0, 0x0, 0x39, 0xfe, 0xf0, 0x13, 0x4,
    0xd9, 0xff, 0x5f, 0x0, 0x1, 0xcb, 0xff, 0x75,
    0x0, 0x0, 0x51, 0xff, 0xe7, 0xd, 0x62, 0xff,
    0xdf, 0x7, 0x0, 0x0, 0x0, 0xc2, 0xff, 0x89,

    /* U+0059 "Y" */
    0xb0, 0xff, 0x7b, 0x0, 0x0, 0x0, 0x0, 0xc0,
    0xff, 0x6b, 0x30, 0xfe, 0xe9, 0x8, 0x0, 0x0,
    0x35, 0xff, 0xe3, 0x7, 0x0, 0xae, 0xff, 0x69,
    0x0, 0x0, 0xac, 0xff, 0x69, 0x0, 0x0, 0x2e,
    0xfe, 0xdd, 0x3, 0x25, 0xfd, 0xe2, 0x6, 0x0,
    0x0, 0x0, 0xad, 0xff, 0x57, 0x99, 0xff, 0x68,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfe, 0xd7, 0xf8,
    0xe1, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xab,
    0xff, 0xff, 0x66, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x35, 0xff, 0xeb, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x20, 0xff, 0xdc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0xff, 0xdc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0xff, 0xdc, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x54, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe8, 0x0, 0x38, 0xac, 0xac, 0xac, 0xac, 0xac,
    0xdd, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x18, 0xee, 0xf8, 0x29, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xae, 0xff, 0x7a, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x57, 0xff, 0xcf, 0x4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0xea, 0xfb,
    0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa7,
    0xff, 0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xd7, 0x7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0xe5, 0xfd, 0x3a, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0xff, 0xff, 0xb3,
    0xa8, 0xa8, 0xa8, 0xa8, 0xa8, 0xf, 0x60, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18,

    /* U+005B "[" */
    0x87, 0x8c, 0x8c, 0x1e, 0xf8, 0xff, 0xff, 0x38,
    0xf8, 0xf0, 0x0, 0x0, 0xf8, 0xf0, 0x0, 0x0,
    0xf8, 0xf0, 0x0, 0x0, 0xf8, 0xf0, 0x0, 0x0,
    0xf8, 0xf0, 0x0, 0x0, 0xf8, 0xf0, 0x0, 0x0,
    0xf8, 0xf0, 0x0, 0x0, 0xf8, 0xf0, 0x0, 0x0,
    0xf8, 0xf0, 0x0, 0x0, 0xf8, 0xf0, 0x0, 0x0,
    0xf8, 0xf0, 0x0, 0x0, 0xf8, 0xf0, 0x0, 0x0,
    0xf8, 0xf0, 0x0, 0x0, 0xf8, 0xf8, 0x88, 0x1d,
    0xf8, 0xff, 0xff, 0x38,

    /* U+005C "\\" */
    0xaa, 0xff, 0x36, 0x0, 0x0, 0x0, 0x0, 0x4c,
    0xff, 0x94, 0x0, 0x0, 0x0, 0x0, 0x4, 0xea,
    0xec, 0x5, 0x0, 0x0, 0x0, 0x0, 0x91, 0xff,
    0x4f, 0x0, 0x0, 0x0, 0x0, 0x33, 0xff, 0xac,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd5, 0xf8, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x78, 0xff, 0x67, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xfd, 0xc5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbd, 0xff, 0x23, 0x0, 0x0,
    0x0, 0x0, 0x60, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf5, 0xdd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa5, 0xff, 0x3b, 0x0, 0x0, 0x0, 0x0,
    0x47, 0xff, 0x99,

    /* U+005D "]" */
    0x7e, 0x8c, 0x8c, 0x29, 0xe8, 0xff, 0xff, 0x4c,
    0x0, 0x98, 0xff, 0x4c, 0x0, 0x98, 0xff, 0x4c,
    0x0, 0x98, 0xff, 0x4c, 0x0, 0x98, 0xff, 0x4c,
    0x0, 0x98, 0xff, 0x4c, 0x0, 0x98, 0xff, 0x4c,
    0x0, 0x98, 0xff, 0x4c, 0x0, 0x98, 0xff, 0x4c,
    0x0, 0x98, 0xff, 0x4c, 0x0, 0x98, 0xff, 0x4c,
    0x0, 0x98, 0xff, 0x4c, 0x0, 0x98, 0xff, 0x4c,
    0x0, 0x98, 0xff, 0x4c, 0x7b, 0xcf, 0xff, 0x4c,
    0xe8, 0xff, 0xff, 0x4c,

    /* U+005E "^" */
    0x0, 0x0, 0x72, 0xff, 0x49, 0x0, 0x0, 0x0,
    0x0, 0xd5, 0xff, 0xac, 0x0, 0x0, 0x0, 0x39,
    0xff, 0xc8, 0xfa, 0x15, 0x0, 0x0, 0x9c, 0xed,
    0x25, 0xfe, 0x73, 0x0, 0xb, 0xf3, 0x95, 0x0,
    0xbe, 0xd6, 0x0, 0x62, 0xff, 0x37, 0x0, 0x5f,
    0xff, 0x3a,

    /* U+005F "_" */
    0x8b, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x1b,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,

    /* U+0060 "`" */
    0x33, 0x80, 0x59, 0x0, 0x0, 0x5, 0xbf, 0xfb,
    0x2d, 0x0, 0x0, 0x12, 0xda, 0xc4, 0x1,

    /* U+0061 "a" */
    0x0, 0x18, 0x9d, 0xe8, 0xf6, 0xd0, 0x51, 0x0,
    0x1, 0xd3, 0xfe, 0xb1, 0x8d, 0xdf, 0xfd, 0x39,
    0x7, 0x5c, 0x4c, 0x0, 0x1, 0x4d, 0xff, 0x8b,
    0x0, 0x18, 0x98, 0xe1, 0xfc, 0xff, 0xff, 0xa3,
    0x3, 0xcc, 0xfe, 0x7f, 0x3c, 0x67, 0xff, 0xa4,
    0x2d, 0xff, 0xb4, 0x0, 0x0, 0x40, 0xff, 0xa4,
    0x3b, 0xff, 0xbb, 0x0, 0x0, 0x88, 0xff, 0xa4,
    0xa, 0xe4, 0xff, 0xb3, 0xb5, 0xff, 0xff, 0xab,
    0x0, 0x30, 0xc3, 0xf4, 0xce, 0x5a, 0xff, 0xd3,

    /* U+0062 "b" */
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe1, 0x84, 0xeb,
    0xf3, 0xa4, 0xe, 0x0, 0x8, 0xff, 0xff, 0xe8,
    0xaa, 0xdf, 0xff, 0xa3, 0x0, 0x8, 0xff, 0xf3,
    0x18, 0x0, 0xe, 0xe5, 0xfc, 0x1b, 0x8, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x99, 0xff, 0x46, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x83, 0xff, 0x5d,
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x98, 0xff,
    0x47, 0x8, 0xff, 0xf4, 0x19, 0x0, 0xb, 0xe3,
    0xfd, 0x1c, 0x8, 0xff, 0xfe, 0xe7, 0xa5, 0xdc,
    0xff, 0xa7, 0x0, 0x8, 0xff, 0xbc, 0x87, 0xec,
    0xf4, 0xa6, 0x10, 0x0,

    /* U+0063 "c" */
    0x0, 0x2, 0x7a, 0xdf, 0xf7, 0xd2, 0x5a, 0x0,
    0x0, 0x8d, 0xff, 0xde, 0xa4, 0xe9, 0xff, 0x55,
    0x16, 0xfa, 0xe7, 0xe, 0x0, 0x23, 0xfd, 0xc2,
    0x4d, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x41, 0x40,
    0x5d, 0xff, 0x87, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0xff, 0x9a, 0x0, 0x0, 0x0, 0x7, 0x7,
    0x16, 0xfb, 0xe4, 0xb, 0x0, 0x16, 0xf8, 0xd1,
    0x0, 0x90, 0xff, 0xda, 0x9f, 0xe1, 0xff, 0x5d,
    0x0, 0x2, 0x7c, 0xe0, 0xf9, 0xd4, 0x5d, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe0, 0xff,
    0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe0,
    0xff, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe0, 0xff, 0x8, 0x0, 0x9, 0x9b, 0xf1, 0xe3,
    0x7d, 0xe2, 0xff, 0x8, 0x0, 0x98, 0xff, 0xe5,
    0xaa, 0xe7, 0xff, 0xff, 0x8, 0x16, 0xf9, 0xef,
    0x16, 0x0, 0x17, 0xf2, 0xff, 0x8, 0x43, 0xff,
    0xa4, 0x0, 0x0, 0x0, 0xe0, 0xff, 0x8, 0x58,
    0xff, 0x8a, 0x0, 0x0, 0x0, 0xe0, 0xff, 0x8,
    0x40, 0xff, 0x99, 0x0, 0x0, 0x0, 0xe0, 0xff,
    0x8, 0x14, 0xf8, 0xe1, 0x6, 0x0, 0xa, 0xee,
    0xff, 0x8, 0x0, 0x97, 0xff, 0xc6, 0x7e, 0xcc,
    0xff, 0xff, 0x8, 0x0, 0x9, 0x9b, 0xf2, 0xe7,
    0x90, 0xc5, 0xff, 0x8,

    /* U+0065 "e" */
    0x0, 0x0, 0x5e, 0xd4, 0xf8, 0xda, 0x62, 0x0,
    0x0, 0x0, 0x6a, 0xff, 0xe4, 0xa5, 0xe7, 0xff,
    0x4c, 0x0, 0x7, 0xef, 0xf4, 0x1a, 0x0, 0x23,
    0xfb, 0xc6, 0x0, 0x3d, 0xff, 0xbb, 0x20, 0x20,
    0x20, 0xda, 0xf7, 0x1, 0x54, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x14, 0x47, 0xff, 0xc2,
    0x50, 0x50, 0x50, 0x50, 0x50, 0x7, 0x11, 0xf7,
    0xeb, 0x1a, 0x0, 0x0, 0x3c, 0x24, 0x0, 0x0,
    0x7c, 0xff, 0xec, 0xa2, 0xb8, 0xfb, 0xb4, 0x0,
    0x0, 0x0, 0x65, 0xd4, 0xf9, 0xe7, 0x95, 0x10,
    0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x5a, 0xd5, 0xf7, 0x98, 0x0, 0x28,
    0xfd, 0xf9, 0xb1, 0x6c, 0x0, 0x58, 0xff, 0x95,
    0x0, 0x0, 0xa4, 0xff, 0xff, 0xff, 0xff, 0x44,
    0x4f, 0xab, 0xff, 0xc4, 0x7c, 0x20, 0x0, 0x5c,
    0xff, 0x8c, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x8c,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0x8c, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0x8c, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x8c,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0x8c, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x7, 0x95, 0xee, 0xf1, 0x9a, 0xb0, 0xff,
    0x18, 0x0, 0x93, 0xff, 0xe8, 0xaf, 0xe5, 0xff,
    0xff, 0x18, 0x13, 0xf8, 0xf1, 0x19, 0x0, 0x12,
    0xe6, 0xff, 0x18, 0x3e, 0xff, 0xa7, 0x0, 0x0,
    0x0, 0xcc, 0xff, 0x18, 0x54, 0xff, 0x8e, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0x18, 0x3b, 0xff, 0xa3,
    0x0, 0x0, 0x0, 0xcc, 0xff, 0x18, 0x11, 0xf6,
    0xee, 0x15, 0x0, 0x12, 0xe6, 0xff, 0x18, 0x0,
    0x8e, 0xff, 0xe5, 0xab, 0xe3, 0xff, 0xff, 0x18,
    0x0, 0x6, 0x93, 0xef, 0xe8, 0x8e, 0xd6, 0xff,
    0x13, 0x0, 0x18, 0x29, 0x0, 0x0, 0x19, 0xf5,
    0xeb, 0x1, 0x0, 0xa9, 0xf7, 0xb3, 0xa0, 0xe9,
    0xff, 0x75, 0x0, 0x0, 0x14, 0x95, 0xe6, 0xf4,
    0xcd, 0x5c, 0x0, 0x0,

    /* U+0068 "h" */
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd8, 0x71, 0xe4, 0xf0, 0xad, 0x13,
    0xc, 0xff, 0xfe, 0xe6, 0xa8, 0xe2, 0xff, 0x96,
    0xc, 0xff, 0xee, 0x15, 0x0, 0x1d, 0xff, 0xd7,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf5, 0xef,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,

    /* U+0069 "i" */
    0x0, 0xc9, 0xda, 0x8, 0x0, 0xdc, 0xea, 0xb,
    0x0, 0x5, 0x7, 0x0, 0x0, 0xe8, 0xff, 0x0,
    0x0, 0xe8, 0xff, 0x0, 0x0, 0xe8, 0xff, 0x0,
    0x0, 0xe8, 0xff, 0x0, 0x0, 0xe8, 0xff, 0x0,
    0x0, 0xe8, 0xff, 0x0, 0x0, 0xe8, 0xff, 0x0,
    0x0, 0xe8, 0xff, 0x0, 0x0, 0xe8, 0xff, 0x0,

    /* U+006A "j" */
    0x0, 0x6, 0xd8, 0xcd, 0x0, 0x0, 0x9, 0xe9,
    0xe0, 0x2, 0x0, 0x0, 0x7, 0x6, 0x0, 0x0,
    0x0, 0xf0, 0xf4, 0x0, 0x0, 0x0, 0xf0, 0xf4,
    0x0, 0x0, 0x0, 0xf0, 0xf4, 0x0, 0x0, 0x0,
    0xf0, 0xf4, 0x0, 0x0, 0x0, 0xf0, 0xf4, 0x0,
    0x0, 0x0, 0xf0, 0xf4, 0x0, 0x0, 0x0, 0xf0,
    0xf4, 0x0, 0x0, 0x0, 0xf0, 0xf4, 0x0, 0x0,
    0x0, 0xf0, 0xf4, 0x0, 0x0, 0x3, 0xf7, 0xeb,
    0x0, 0x65, 0xc1, 0xff, 0xba, 0x0, 0x89, 0xf6,
    0xc6, 0x28, 0x0,

    /* U+006B "k" */
    0x4, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x0,
    0x9a, 0xff, 0xab, 0x1, 0x4, 0xff, 0xe0, 0x0,
    0x70, 0xff, 0xcc, 0xa, 0x0, 0x4, 0xff, 0xe0,
    0x48, 0xfc, 0xe5, 0x1a, 0x0, 0x0, 0x4, 0xff,
    0xef, 0xee, 0xff, 0x36, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x72, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x7e, 0xf1, 0xf6, 0x25, 0x0,
    0x0, 0x4, 0xff, 0xe0, 0x0, 0x67, 0xff, 0xc2,
    0x1, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x1, 0xbe,
    0xff, 0x6d, 0x0, 0x4, 0xff, 0xe0, 0x0, 0x0,
    0x22, 0xf5, 0xf4, 0x22,

    /* U+006C "l" */
    0xe8, 0xff, 0xe8, 0xff, 0xe8, 0xff, 0xe8, 0xff,
    0xe8, 0xff, 0xe8, 0xff, 0xe8, 0xff, 0xe8, 0xff,
    0xe8, 0xff, 0xe8, 0xff, 0xe8, 0xff, 0xe8, 0xff,

    /* U+006D "m" */
    0x8, 0xff, 0xc9, 0x7c, 0xe5, 0xf6, 0xb5, 0x16,
    0x82, 0xe8, 0xf0, 0xad, 0x13, 0x8, 0xff, 0xfe,
    0xe5, 0xac, 0xe6, 0xff, 0xe7, 0xea, 0xa9, 0xdf,
    0xff, 0x9a, 0x8, 0xff, 0xf0, 0x16, 0x0, 0x27,
    0xff, 0xfc, 0x2e, 0x0, 0x1a, 0xff, 0xdc, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0xfc, 0xe8, 0x0,
    0x0, 0x0, 0xf5, 0xf3, 0x8, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0xfc, 0xe8, 0x0, 0x0, 0x0, 0xf4,
    0xf4, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xfc,
    0xe8, 0x0, 0x0, 0x0, 0xf4, 0xf4, 0x8, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xfc, 0xe8, 0x0, 0x0,
    0x0, 0xf4, 0xf4, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0xfc, 0xe8, 0x0, 0x0, 0x0, 0xf4, 0xf4,
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xfc, 0xe8,
    0x0, 0x0, 0x0, 0xf4, 0xf4,

    /* U+006E "n" */
    0xc, 0xff, 0xc2, 0x70, 0xe3, 0xf2, 0xb0, 0x15,
    0xc, 0xff, 0xfb, 0xe7, 0xa8, 0xe0, 0xff, 0x98,
    0xc, 0xff, 0xf0, 0x17, 0x0, 0x1c, 0xff, 0xd8,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf5, 0xef,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,
    0xc, 0xff, 0xd8, 0x0, 0x0, 0x0, 0xf4, 0xf0,

    /* U+006F "o" */
    0x0, 0x0, 0x63, 0xd4, 0xf8, 0xdf, 0x7b, 0x2,
    0x0, 0x0, 0x76, 0xff, 0xe6, 0xa6, 0xdf, 0xff,
    0x8b, 0x0, 0xd, 0xf6, 0xf1, 0x17, 0x0, 0xf,
    0xe2, 0xfc, 0x1f, 0x47, 0xff, 0xa4, 0x0, 0x0,
    0x0, 0x87, 0xff, 0x58, 0x5b, 0xff, 0x8a, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x77, 0x49, 0xff, 0x9f,
    0x0, 0x0, 0x0, 0x88, 0xff, 0x63, 0x11, 0xf7,
    0xed, 0x14, 0x0, 0xb, 0xde, 0xfe, 0x21, 0x0,
    0x7f, 0xff, 0xe2, 0xa1, 0xda, 0xff, 0x95, 0x0,
    0x0, 0x0, 0x6e, 0xda, 0xf9, 0xde, 0x7a, 0x2,
    0x0,

    /* U+0070 "p" */
    0x8, 0xff, 0xca, 0x90, 0xe5, 0xf3, 0xa4, 0xe,
    0x0, 0x8, 0xff, 0xff, 0xcf, 0x87, 0xcc, 0xff,
    0xa3, 0x0, 0x8, 0xff, 0xec, 0xc, 0x0, 0x9,
    0xe5, 0xfb, 0x19, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x9c, 0xff, 0x42, 0x8, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x8b, 0xff, 0x59, 0x8, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0xa4, 0xff, 0x43, 0x8, 0xff,
    0xf0, 0x10, 0x0, 0x13, 0xee, 0xfb, 0x18, 0x8,
    0xff, 0xff, 0xde, 0x9e, 0xdf, 0xff, 0x9f, 0x0,
    0x8, 0xff, 0xe3, 0x80, 0xe3, 0xf3, 0xa2, 0xd,
    0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0xa, 0x9b, 0xf0, 0xef, 0x90, 0xbe, 0xff,
    0x4, 0x0, 0x9a, 0xff, 0xe1, 0xa7, 0xe3, 0xff,
    0xff, 0x4, 0x17, 0xfa, 0xed, 0x12, 0x0, 0x14,
    0xef, 0xff, 0x4, 0x43, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0xe0, 0xff, 0x4, 0x58, 0xff, 0x8a, 0x0,
    0x0, 0x0, 0xe0, 0xff, 0x4, 0x40, 0xff, 0x9f,
    0x0, 0x0, 0x0, 0xe0, 0xff, 0x4, 0x15, 0xf9,
    0xeb, 0x11, 0x0, 0x15, 0xef, 0xff, 0x4, 0x0,
    0x99, 0xff, 0xdf, 0xa3, 0xe2, 0xff, 0xff, 0x4,
    0x0, 0xa, 0x9b, 0xf1, 0xe7, 0x88, 0xe4, 0xff,
    0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe0,
    0xff, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe0, 0xff, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe0, 0xff, 0x4,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8, 0xff,
    0xd1, 0x93, 0xf0, 0x68, 0x8, 0xff, 0xff, 0xf6,
    0xc7, 0x52, 0x8, 0xff, 0xf6, 0x24, 0x0, 0x0,
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xe0, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x16, 0x9f, 0xe7, 0xf1, 0xc1, 0x3c, 0x0,
    0x0, 0xc5, 0xff, 0xa1, 0x8e, 0xf2, 0xf6, 0x27,
    0x12, 0xff, 0xc7, 0x0, 0x0, 0x5f, 0xc8, 0x56,
    0x4, 0xe2, 0xfc, 0x8e, 0x3c, 0x4, 0x0, 0x0,
    0x0, 0x29, 0xbe, 0xff, 0xff, 0xee, 0x7d, 0x1,
    0x0, 0x0, 0x0, 0x18, 0x5d, 0xd4, 0xff, 0x5f,
    0x4d, 0xe8, 0x84, 0x0, 0x0, 0x57, 0xff, 0x86,
    0xc, 0xe2, 0xfa, 0x9d, 0x86, 0xe0, 0xfd, 0x3e,
    0x0, 0x21, 0xab, 0xef, 0xf3, 0xc9, 0x4e, 0x0,

    /* U+0074 "t" */
    0x0, 0x8c, 0xff, 0x5c, 0x0, 0x0, 0x8c, 0xff,
    0x5c, 0x0, 0xf0, 0xff, 0xff, 0xff, 0xd8, 0x74,
    0xc4, 0xff, 0xab, 0x68, 0x0, 0x8c, 0xff, 0x5c,
    0x0, 0x0, 0x8c, 0xff, 0x5c, 0x0, 0x0, 0x8c,
    0xff, 0x5c, 0x0, 0x0, 0x8c, 0xff, 0x5c, 0x0,
    0x0, 0x87, 0xff, 0x61, 0x0, 0x0, 0x63, 0xff,
    0xe0, 0x99, 0x0, 0x9, 0xb0, 0xf5, 0xc8,

    /* U+0075 "u" */
    0x10, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xf8, 0xf0,
    0x10, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xf8, 0xf0,
    0x10, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xf8, 0xf0,
    0x10, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xf8, 0xf0,
    0x10, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xf8, 0xf0,
    0xe, 0xff, 0xd5, 0x0, 0x0, 0x0, 0xf8, 0xf0,
    0x0, 0xf6, 0xf0, 0x8, 0x0, 0x20, 0xfc, 0xf0,
    0x0, 0xb3, 0xff, 0xcf, 0xa6, 0xed, 0xff, 0xf0,
    0x0, 0x1d, 0xb8, 0xf4, 0xe6, 0x78, 0xe2, 0xf0,

    /* U+0076 "v" */
    0xab, 0xff, 0x48, 0x0, 0x0, 0x65, 0xff, 0x89,
    0x57, 0xff, 0x91, 0x0, 0x0, 0xad, 0xff, 0x36,
    0xb, 0xf6, 0xda, 0x0, 0x4, 0xf1, 0xe2, 0x1,
    0x0, 0xaf, 0xff, 0x24, 0x3f, 0xff, 0x90, 0x0,
    0x0, 0x5b, 0xff, 0x6d, 0x87, 0xff, 0x3d, 0x0,
    0x0, 0xe, 0xf8, 0xb6, 0xd0, 0xe8, 0x2, 0x0,
    0x0, 0x0, 0xb3, 0xf8, 0xff, 0x97, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0x44, 0x0, 0x0,
    0x0, 0x0, 0x10, 0xf9, 0xed, 0x4, 0x0, 0x0,

    /* U+0077 "w" */
    0x9c, 0xff, 0x38, 0x0, 0x1, 0xea, 0xc8, 0x0,
    0x0, 0x5d, 0xff, 0x76, 0x5a, 0xff, 0x71, 0x0,
    0x35, 0xff, 0xfd, 0x14, 0x0, 0x95, 0xff, 0x35,
    0x18, 0xff, 0xaa, 0x0, 0x7d, 0xfd, 0xff, 0x5c,
    0x0, 0xcd, 0xf0, 0x3, 0x0, 0xd6, 0xe4, 0x0,
    0xc5, 0xb9, 0xe0, 0xa6, 0xa, 0xfb, 0xb2, 0x0,
    0x0, 0x94, 0xff, 0x2e, 0xfc, 0x6f, 0x95, 0xed,
    0x41, 0xff, 0x71, 0x0, 0x0, 0x53, 0xff, 0xac,
    0xff, 0x25, 0x4a, 0xff, 0xb0, 0xff, 0x30, 0x0,
    0x0, 0x12, 0xfe, 0xfe, 0xdc, 0x0, 0x9, 0xf6,
    0xff, 0xed, 0x1, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x92, 0x0, 0x0, 0xb4, 0xff, 0xad, 0x0, 0x0,
    0x0, 0x0, 0x8d, 0xff, 0x48, 0x0, 0x0, 0x69,
    0xff, 0x6c, 0x0, 0x0,

    /* U+0078 "x" */
    0x5e, 0xff, 0xb4, 0x0, 0x0, 0xa7, 0xff, 0x70,
    0x1, 0xc8, 0xff, 0x3f, 0x35, 0xfe, 0xd6, 0x4,
    0x0, 0x36, 0xfe, 0xc8, 0xc0, 0xff, 0x45, 0x0,
    0x0, 0x0, 0xa0, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3a, 0xff, 0xff, 0x49, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x49, 0xff, 0xb7, 0xa7, 0xff, 0x59, 0x0,
    0x5, 0xd9, 0xfd, 0x2e, 0x21, 0xf9, 0xe3, 0xb,
    0x75, 0xff, 0xa1, 0x0, 0x0, 0x8e, 0xff, 0x85,

    /* U+0079 "y" */
    0xbc, 0xff, 0x49, 0x0, 0x0, 0x7d, 0xff, 0x7f,
    0x62, 0xff, 0x96, 0x0, 0x0, 0xc6, 0xff, 0x28,
    0xf, 0xf8, 0xe2, 0x0, 0x12, 0xfc, 0xd1, 0x0,
    0x0, 0xae, 0xff, 0x2f, 0x59, 0xff, 0x7a, 0x0,
    0x0, 0x55, 0xff, 0x7c, 0xa2, 0xff, 0x23, 0x0,
    0x0, 0x8, 0xf2, 0xcb, 0xe9, 0xcb, 0x0, 0x0,
    0x0, 0x0, 0xa1, 0xff, 0xff, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x47, 0xff, 0xfe, 0x1e, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfd, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0x6e, 0x0, 0x0, 0x0,
    0x24, 0xb4, 0xf8, 0xee, 0xf, 0x0, 0x0, 0x0,
    0x31, 0xf4, 0xd5, 0x3e, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x48, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x58,
    0x2b, 0x9c, 0x9c, 0x9c, 0xb0, 0xff, 0xfa, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0xff, 0x78, 0x0,
    0x0, 0x0, 0x0, 0x66, 0xff, 0xc1, 0x2, 0x0,
    0x0, 0x0, 0x25, 0xf4, 0xf0, 0x1f, 0x0, 0x0,
    0x0, 0x4, 0xc9, 0xff, 0x5a, 0x0, 0x0, 0x0,
    0x0, 0x83, 0xff, 0xa6, 0x0, 0x0, 0x0, 0x0,
    0x32, 0xfc, 0xff, 0xa4, 0x94, 0x94, 0x94, 0x4a,
    0x5c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x1, 0x35, 0x0, 0x0, 0x0,
    0x14, 0xc6, 0xf4, 0x9, 0x0, 0x0, 0xa1, 0xff,
    0x3a, 0x0, 0x0, 0x0, 0xef, 0xd4, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xbc, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xbc, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xad,
    0x0, 0x0, 0x1b, 0x9d, 0xff, 0x62, 0x0, 0x0,
    0x90, 0xff, 0xb8, 0x1, 0x0, 0x0, 0x34, 0xbd,
    0xff, 0x49, 0x0, 0x0, 0x0, 0x26, 0xff, 0xa6,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xbb, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xbc, 0x0, 0x0, 0x0, 0x0,
    0xf4, 0xcc, 0x0, 0x0, 0x0, 0x0, 0xb2, 0xfc,
    0x23, 0x0, 0x0, 0x0, 0x24, 0xe2, 0xe4, 0x9,
    0x0, 0x0, 0x0, 0x10, 0x5d, 0x0,

    /* U+007C "|" */
    0xa4, 0xac, 0xa4, 0xac, 0xa4, 0xac, 0xa4, 0xac,
    0xa4, 0xac, 0xa4, 0xac, 0xa4, 0xac, 0xa4, 0xac,
    0xa4, 0xac, 0xa4, 0xac, 0xa4, 0xac, 0xa4, 0xac,
    0xa4, 0xac, 0xa4, 0xac, 0xf, 0x10,

    /* U+007D "}" */
    0x27, 0xe, 0x0, 0x0, 0x0, 0xa1, 0xf0, 0x43,
    0x0, 0x0, 0x9, 0xd4, 0xec, 0xc, 0x0, 0x0,
    0x7a, 0xff, 0x4a, 0x0, 0x0, 0x61, 0xff, 0x65,
    0x0, 0x0, 0x60, 0xff, 0x68, 0x0, 0x0, 0x52,
    0xff, 0x77, 0x0, 0x0, 0x12, 0xf0, 0xdb, 0x38,
    0x0, 0x0, 0x52, 0xff, 0xec, 0x0, 0xa, 0xe1,
    0xec, 0x61, 0x0, 0x4c, 0xff, 0x81, 0x0, 0x0,
    0x5f, 0xff, 0x68, 0x0, 0x0, 0x60, 0xff, 0x67,
    0x0, 0x0, 0x72, 0xff, 0x4f, 0x0, 0x1, 0xc2,
    0xf7, 0x17, 0x0, 0x91, 0xfd, 0x67, 0x0, 0x0,
    0x41, 0x2e, 0x0, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x5e, 0x9c, 0x66, 0x2, 0x0, 0x0,
    0x83, 0x71, 0x0, 0x74, 0xff, 0xff, 0xff, 0xaf,
    0x7, 0xb, 0xf0, 0x90, 0x0, 0xe6, 0xc0, 0x11,
    0x77, 0xff, 0xd9, 0xd3, 0xff, 0x33, 0x5, 0xaf,
    0x50, 0x0, 0x0, 0x58, 0xda, 0xef, 0x72, 0x0,

    /* U+00B0 "°" */
    0x3c, 0xd6, 0xdf, 0x4c, 0x0, 0xdc, 0x6e, 0x5a,
    0xf0, 0x3, 0xec, 0x45, 0x2c, 0xfb, 0x8, 0x66,
    0xfb, 0xfe, 0x7c, 0x0, 0x0, 0x11, 0x16, 0x0,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 64, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 69, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 36, .adv_w = 83, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 61, .adv_w = 156, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 181, .adv_w = 146, .box_w = 9, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 325, .adv_w = 188, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 469, .adv_w = 164, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 601, .adv_w = 43, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 616, .adv_w = 89, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 706, .adv_w = 90, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 796, .adv_w = 113, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 852, .adv_w = 143, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 933, .adv_w = 56, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 948, .adv_w = 84, .box_w = 5, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 958, .adv_w = 72, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 967, .adv_w = 101, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1045, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1153, .adv_w = 146, .box_w = 5, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1213, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1321, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1429, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1537, .adv_w = 146, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1633, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1741, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1849, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1957, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2065, .adv_w = 68, .box_w = 3, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2092, .adv_w = 61, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2140, .adv_w = 130, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2204, .adv_w = 143, .box_w = 7, .box_h = 5, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2239, .adv_w = 133, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 2295, .adv_w = 125, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2391, .adv_w = 229, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2601, .adv_w = 170, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2733, .adv_w = 162, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2841, .adv_w = 167, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2961, .adv_w = 167, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3069, .adv_w = 145, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3165, .adv_w = 141, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3261, .adv_w = 174, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3381, .adv_w = 182, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3501, .adv_w = 72, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3537, .adv_w = 142, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3633, .adv_w = 161, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3753, .adv_w = 139, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3849, .adv_w = 224, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3993, .adv_w = 182, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4113, .adv_w = 177, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4245, .adv_w = 164, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4353, .adv_w = 177, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4507, .adv_w = 160, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4615, .adv_w = 155, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4735, .adv_w = 155, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4855, .adv_w = 167, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4975, .adv_w = 166, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5107, .adv_w = 225, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5275, .adv_w = 162, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5395, .adv_w = 156, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5515, .adv_w = 154, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5635, .adv_w = 70, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 5703, .adv_w = 107, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5794, .adv_w = 70, .box_w = 4, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5862, .adv_w = 109, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 5904, .adv_w = 116, .box_w = 8, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5920, .adv_w = 83, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 5935, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6007, .adv_w = 144, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6115, .adv_w = 134, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6187, .adv_w = 145, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6295, .adv_w = 137, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6376, .adv_w = 91, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6448, .adv_w = 145, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6556, .adv_w = 142, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6652, .adv_w = 65, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6700, .adv_w = 64, .box_w = 5, .box_h = 15, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 6775, .adv_w = 134, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6883, .adv_w = 65, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6907, .adv_w = 223, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7024, .adv_w = 142, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7096, .adv_w = 146, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7177, .adv_w = 144, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7285, .adv_w = 145, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7393, .adv_w = 90, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7453, .adv_w = 132, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7525, .adv_w = 85, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7580, .adv_w = 142, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7652, .adv_w = 127, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7724, .adv_w = 190, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7832, .adv_w = 129, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7904, .adv_w = 125, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8000, .adv_w = 129, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8072, .adv_w = 86, .box_w = 6, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8174, .adv_w = 64, .box_w = 2, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8204, .adv_w = 86, .box_w = 5, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8289, .adv_w = 170, .box_w = 10, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 8329, .adv_w = 97, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 7}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 1, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    1, 53,
    3, 3,
    3, 8,
    3, 34,
    3, 66,
    3, 68,
    3, 69,
    3, 70,
    3, 72,
    3, 78,
    3, 79,
    3, 80,
    3, 81,
    3, 82,
    3, 84,
    3, 88,
    8, 3,
    8, 8,
    8, 34,
    8, 66,
    8, 68,
    8, 69,
    8, 70,
    8, 72,
    8, 78,
    8, 79,
    8, 80,
    8, 81,
    8, 82,
    8, 84,
    8, 88,
    9, 55,
    9, 56,
    9, 58,
    13, 3,
    13, 8,
    15, 3,
    15, 8,
    16, 16,
    34, 3,
    34, 8,
    34, 32,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 78,
    34, 79,
    34, 80,
    34, 81,
    34, 85,
    34, 86,
    34, 87,
    34, 88,
    34, 90,
    34, 91,
    35, 53,
    35, 55,
    35, 58,
    36, 10,
    36, 53,
    36, 62,
    36, 94,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 57,
    37, 58,
    37, 59,
    38, 53,
    38, 68,
    38, 69,
    38, 70,
    38, 71,
    38, 72,
    38, 80,
    38, 82,
    38, 86,
    38, 87,
    38, 88,
    38, 90,
    39, 13,
    39, 15,
    39, 34,
    39, 43,
    39, 53,
    39, 66,
    39, 68,
    39, 69,
    39, 70,
    39, 72,
    39, 80,
    39, 82,
    39, 83,
    39, 86,
    39, 87,
    39, 90,
    41, 34,
    41, 53,
    41, 57,
    41, 58,
    42, 34,
    42, 53,
    42, 57,
    42, 58,
    43, 34,
    44, 14,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 80,
    44, 82,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 86,
    45, 87,
    45, 88,
    45, 90,
    46, 34,
    46, 53,
    46, 57,
    46, 58,
    47, 34,
    47, 53,
    47, 57,
    47, 58,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 57,
    48, 58,
    48, 59,
    49, 13,
    49, 15,
    49, 34,
    49, 43,
    49, 57,
    49, 59,
    49, 66,
    49, 68,
    49, 69,
    49, 70,
    49, 72,
    49, 80,
    49, 82,
    49, 85,
    49, 87,
    49, 90,
    50, 53,
    50, 55,
    50, 56,
    50, 58,
    51, 53,
    51, 55,
    51, 58,
    53, 1,
    53, 13,
    53, 14,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 43,
    53, 48,
    53, 50,
    53, 52,
    53, 53,
    53, 55,
    53, 56,
    53, 58,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    55, 10,
    55, 13,
    55, 14,
    55, 15,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 62,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 80,
    55, 82,
    55, 83,
    55, 86,
    55, 87,
    55, 90,
    55, 94,
    56, 10,
    56, 13,
    56, 14,
    56, 15,
    56, 34,
    56, 53,
    56, 62,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 83,
    56, 86,
    56, 94,
    57, 14,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 55,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 80,
    57, 82,
    57, 86,
    57, 87,
    57, 90,
    58, 7,
    58, 10,
    58, 11,
    58, 13,
    58, 14,
    58, 15,
    58, 34,
    58, 36,
    58, 40,
    58, 43,
    58, 48,
    58, 50,
    58, 52,
    58, 53,
    58, 54,
    58, 55,
    58, 56,
    58, 57,
    58, 58,
    58, 62,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    58, 94,
    59, 34,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    59, 68,
    59, 69,
    59, 70,
    59, 72,
    59, 80,
    59, 82,
    59, 86,
    59, 87,
    59, 88,
    59, 90,
    60, 43,
    60, 54,
    66, 3,
    66, 8,
    66, 87,
    66, 90,
    67, 3,
    67, 8,
    67, 87,
    67, 89,
    67, 90,
    67, 91,
    68, 3,
    68, 8,
    70, 3,
    70, 8,
    70, 87,
    70, 90,
    71, 3,
    71, 8,
    71, 10,
    71, 62,
    71, 68,
    71, 69,
    71, 70,
    71, 72,
    71, 82,
    71, 94,
    73, 3,
    73, 8,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 82,
    78, 3,
    78, 8,
    79, 3,
    79, 8,
    80, 3,
    80, 8,
    80, 87,
    80, 89,
    80, 90,
    80, 91,
    81, 3,
    81, 8,
    81, 87,
    81, 89,
    81, 90,
    81, 91,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 72,
    83, 80,
    83, 82,
    83, 85,
    83, 87,
    83, 88,
    83, 90,
    85, 80,
    87, 3,
    87, 8,
    87, 13,
    87, 15,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 71,
    87, 72,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 80,
    89, 82,
    90, 3,
    90, 8,
    90, 13,
    90, 15,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 71,
    90, 72,
    90, 80,
    90, 82,
    91, 68,
    91, 69,
    91, 70,
    91, 72,
    91, 80,
    91, 82,
    92, 43,
    92, 54
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -7, -5, -5, -15, -6, -7, -7, -7,
    -7, -2, -2, -11, -2, -7, -11, 1,
    -5, -5, -15, -6, -7, -7, -7, -7,
    -2, -2, -11, -2, -7, -11, 1, 3,
    5, 3, -36, -36, -36, -36, -31, -15,
    -15, -10, -3, -3, -3, -3, -15, -2,
    -10, -5, -19, -6, -6, -1, -6, -2,
    -1, -6, -4, -6, 2, -3, -3, -7,
    -3, -4, -1, -2, -15, -15, -3, -11,
    -3, -3, -5, -3, 3, -2, -2, -2,
    -2, -2, -2, -2, -2, -3, -3, -3,
    -34, -34, -24, -26, 3, -4, -3, -3,
    -3, -3, -3, -3, -3, -3, -3, -3,
    2, -4, 2, -3, 2, -4, 2, -3,
    -3, -20, -4, -4, -4, -4, -3, -3,
    -3, -3, -3, -3, -3, -5, -8, -5,
    -36, -36, 2, -8, -8, -8, -8, -26,
    -3, -26, -12, -35, -2, -15, -6, -15,
    2, -4, 2, -3, 2, -4, 2, -3,
    -15, -15, -3, -11, -3, -3, -5, -3,
    -50, -50, -22, -23, -6, -4, -1, -2,
    -2, -2, -2, -2, -2, 2, 2, 2,
    -4, -3, -2, -4, -6, -2, -6, -7,
    -32, -34, -32, -15, -3, -3, -27, -3,
    -3, -2, 2, 2, 2, 2, -21, -11,
    -11, -11, -11, -11, -11, -26, -11, -11,
    -8, -9, -8, -10, -6, -10, -10, -7,
    -3, 3, -27, -20, -27, -9, -2, -2,
    -2, -2, 2, -6, -5, -5, -5, -5,
    -6, -5, -4, -3, -1, -1, 2, 2,
    -18, -7, -18, -5, 2, 2, -4, -4,
    -4, -4, -4, -4, -4, -3, -2, 2,
    -19, -3, -3, -3, -3, 2, -3, -3,
    -3, -3, -3, -3, -3, -4, -4, -4,
    3, -6, -29, -19, -29, -19, -4, -4,
    -12, -4, -4, -2, 2, -12, 2, 2,
    2, 2, 2, -8, -8, -8, -8, -3,
    -8, -5, -5, -8, -5, -8, -5, -7,
    -3, -5, -2, -3, -2, -4, 2, 2,
    -3, -3, -3, -3, -3, -3, -3, -3,
    -3, -3, -2, -3, -3, -3, -2, -2,
    -2, -2, -2, -2, -4, -4, -1, -2,
    -1, -2, -1, -1, -2, -2, -2, -2,
    2, 2, 3, 2, -3, -3, -3, -3,
    -3, 2, -10, -10, -2, -2, -2, -2,
    -2, -10, -10, -10, -10, -11, -11, -2,
    -3, -2, -2, -4, -4, -1, -2, -1,
    -2, 2, 2, -22, -22, -4, -2, -2,
    -2, 3, -2, -4, -2, 6, 2, 2,
    2, -4, 2, 2, -21, -21, -2, -2,
    -2, -2, 2, -2, -2, -2, -15, -15,
    -2, -2, -2, -2, -5, -2, 2, 2,
    -21, -21, -2, -2, -2, -2, 2, -2,
    -2, -2, -2, -2, -2, -2, -2, -2,
    -2, -2
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 434,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 8,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_lv_demo_high_res_roboto_medium_16 = {
#else
lv_font_t font_lv_demo_high_res_roboto_medium_16 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 18,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_USE_DEMO_HIGH_RES*/

