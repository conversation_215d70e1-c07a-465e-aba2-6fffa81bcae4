#include "../lv_demo_smartwatch.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

// data generated from: volume_down_g.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t img_volume_down_g_icon_data[] = {
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x50,0x6E,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x10,0x66,0x11,0x66,0xF1,0x65,0x31,0x66,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF1,0x65,0x11,0x66,0x11,0x66,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,
0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0x11,0x66,0xF1,0x65,0x11,0x66,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0x56,0xF1,0x65,0xF1,0x5D,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF1,0x65,0xF0,0x65,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0x11,0x5E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,
0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0xF2,0x5D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xF2,0x55,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xF3,0x65,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0xD3,0x55,0x35,0x56,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,
0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x55,0xD4,0x4D,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xB4,0x4D,0xD4,0x4D,0x00,0x00,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xD4,0x4D,0xB3,0x4D,0x00,0x00,
0x00,0x00,0xD4,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xD5,0x45,0x00,0x00,0x00,0x00,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xB5,0x4D,0xD5,0x4D,0x00,0x00,0xB4,0x4D,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x55,0x55,0x00,0x00,0x00,0x00,
0x00,0x00,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x96,0x4D,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x95,0x45,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xB6,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0xB5,0x45,0x96,0x45,
0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x97,0x3D,0x96,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x96,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x97,0x3D,0x97,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,0x97,0x3D,
0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x97,0x35,0x97,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x97,0x35,0x97,0x35,0x77,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF7,0x45,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x97,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x35,0x78,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x98,0x35,0x78,0x35,0x98,0x35,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF7,0x44,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x35,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x35,0x78,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,0x78,0x2D,
0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x79,0x2D,0x79,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x59,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x79,0x2D,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x7A,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,
0x5A,0x2D,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x55,0x05,0x00,0x00,0x00,0x00,0x00,0x00,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x25,0x5A,0x2D,0x00,0x00,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5A,0x1D,0x00,0x00,
0x00,0x00,0x5A,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x5B,0x1D,0x3A,0x1D,0x00,0x00,0x00,0x00,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x5B,0x1D,0x00,0x00,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x00,0x00,
0x00,0x00,0x00,0x00,0x3C,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3C,0x15,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x3B,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFB,0x24,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,
0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0xBB,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFD,0x14,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x3C,0x15,0x1C,0x15,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1D,0x15,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x3D,0x15,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x0C,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,
0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0x1D,0x0D,0xFD,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x9F,0x04,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x1E,0x0D,0x9F,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x04,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0x1E,0x05,0xFF,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,
0xFF,0x04,0xFF,0x04,0xFF,0x04,0xFF,0x04,0x1F,0x05,0xBF,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x59,0x9B,0xCA,0xEA,0xFA,0xFA,0xEA,0xCA,0x9A,0x58,0x0D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x26,0x9D,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9C,0x25,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x8C,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x8A,0x08,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x25,0xD3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x19,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x25,0xEA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE3,0x18,0x00,0x00,0x00,
0x00,0x00,0x08,0xD0,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xCC,0x09,0x00,0x00,0x00,0x00,0x8C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x99,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x81,0x00,0x00,0x00,0x26,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDD,0x2F,0x00,0xB7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x18,0x00,
0x00,0x9D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xB7,0x12,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x96,0x00,0x0E,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x88,0x03,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0D,0x59,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x50,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x57,
0x9B,0xFF,0xFF,0xFF,0xFF,0xFF,0xDD,0xCC,0xCC,0xE1,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x99,0xCA,0xFF,0xFF,0xFF,0xFF,0xBB,0x00,0x00,0x00,0x6B,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC8,0xEB,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x6B,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xF0,0xEE,0xEE,0xEE,0xEE,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xF9,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x6B,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0x70,0x00,0x00,0x00,0x00,0x00,0x08,0xCC,0xFF,0xFF,0xFF,0xFF,0xF8,0xF9,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x6B,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0x71,0x00,0x00,0x00,0x00,0x00,0x08,0xCD,0xFF,0xFF,0xFF,0xFF,0xF8,0xEB,0xFF,0xFF,0xFF,0xFF,0x9F,0x00,0x00,0x00,0x6B,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xF0,0xEE,0xEE,0xEE,0xEE,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xE9,
0xC9,0xFF,0xFF,0xFF,0xFF,0xBB,0x00,0x00,0x00,0x6B,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC7,0x9A,0xFF,0xFF,0xFF,0xFF,0xFF,0xDD,0xCC,0xCC,0xE1,0xC5,0x00,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x98,0x58,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF3,0x50,0x00,0x00,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x55,
0x0C,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x88,0x03,0x00,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x0C,0x00,0x9C,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xB7,0x12,0x00,0x00,0xB4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x9A,0x00,0x00,0x25,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDD,0x2F,0x00,0xB7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x22,0x00,
0x00,0x00,0x8A,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x9A,0xF5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x87,0x00,0x00,0x00,0x00,0x08,0xC7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x07,0x00,0x00,0x00,0x00,0x00,0x18,0xE2,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0x1B,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x2E,0xE3,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE2,0x2D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x15,0xC1,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xC6,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x81,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0x86,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x96,0xF4,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF4,0x99,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x57,0x98,0xC8,0xE9,0xF9,0xF9,0xE9,0xC8,0x97,0x56,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

}; // LVGL_9 compatible
const lv_image_dsc_t img_volume_down_g_icon = {
   .header.magic = LV_IMAGE_HEADER_MAGIC,
   .header.w = 32,
   .header.h = 32,
   .data_size = sizeof(img_volume_down_g_icon_data),
   .header.cf = LV_COLOR_FORMAT_RGB565A8,
   .data = img_volume_down_g_icon_data};

