/**
 * @file lv_example_monkey.h
 *
 */

#ifndef LV_EXAMPLE_MONKEY_H
#define LV_EXAMPLE_MONKEY_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_monkey_1(void);
void lv_example_monkey_2(void);
void lv_example_monkey_3(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_MONKEY_H*/
