/*******************************************************************************
 * Size: 24 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 24 --font TrumpGothicPro.ttf -r 0x20-0x7F --font ../../../scripts/built_in_font/DejaVuSans.ttf --range 0xFEA9,0xFE8D,0xFEAA,0xFECB,0xFEF9,0xFE95,0xFE8E,0xFEF3,0xFE8B,0xFEBC,0xFEA3,0xFE87,0xFEF4 --font ../../../scripts/built_in_font/SimSun.woff --symbols 语語言標題月日电池今日距离天的速度时间设置蓝牙灯亮度音量最大限度光照强统计三平均高时速简体中文。 --format lvgl -o font_ebike_trump_24.c --force-fast-kern-format
 ******************************************************************************/

#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "../../../lvgl.h"
#endif

#ifndef FONT_EBIKE_TRUMP_24
    #define FONT_EBIKE_TRUMP_24 1
#endif

#if FONT_EBIKE_TRUMP_24

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x90, 0xf9, 0xf, 0x80, 0xf8, 0xe, 0x80,
    0xe7, 0xe, 0x70, 0xd7, 0xd, 0x60, 0xd6, 0xc,
    0x50, 0xc5, 0xc, 0x50, 0xb4, 0xb, 0x40, 0x10,
    0x0, 0x1, 0xe9, 0x1e, 0x80,

    /* U+0022 "\"" */
    0x6f, 0x2f, 0x35, 0xe1, 0xf2, 0x3d, 0xf, 0x12,
    0xc0, 0xe0, 0x1b, 0xc, 0x0, 0x10, 0x10,

    /* U+0023 "#" */
    0x0, 0x0, 0x3, 0x10, 0x13, 0x0, 0x0, 0x1,
    0xf4, 0x8, 0xe0, 0x0, 0x0, 0x5f, 0x0, 0xca,
    0x0, 0x0, 0x9, 0xb0, 0xf, 0x50, 0x0, 0x0,
    0xd7, 0x4, 0xf1, 0x0, 0x1, 0x2f, 0x51, 0x8e,
    0x10, 0xa, 0xff, 0xff, 0xff, 0xff, 0x80, 0x34,
    0xcc, 0x45, 0xf7, 0x41, 0x0, 0xe, 0x70, 0x4f,
    0x10, 0x0, 0x14, 0xf4, 0x19, 0xd1, 0x10, 0x5f,
    0xff, 0xff, 0xff, 0xfd, 0x2, 0x4c, 0xb4, 0x5f,
    0x74, 0x20, 0x0, 0xe6, 0x5, 0xf1, 0x0, 0x0,
    0x3f, 0x20, 0x9c, 0x0, 0x0, 0x7, 0xe0, 0xd,
    0x80, 0x0, 0x0, 0xba, 0x1, 0xf4, 0x0, 0x0,
    0x3, 0x20, 0x14, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x6, 0x0, 0x0, 0x1, 0xd2, 0x0, 0x5,
    0xff, 0xf8, 0x0, 0xeb, 0xd9, 0xf2, 0x2f, 0x5c,
    0x2f, 0x63, 0xf4, 0xc1, 0xf6, 0x3f, 0x4c, 0x1f,
    0x73, 0xf4, 0xc1, 0xf7, 0x1f, 0x7c, 0x8, 0x30,
    0xce, 0xc0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x7,
    0xfa, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0xcc,
    0xf1, 0x17, 0x2c, 0x4f, 0x53, 0xf4, 0xc1, 0xf6,
    0x3f, 0x4c, 0x1f, 0x62, 0xf4, 0xc1, 0xf6, 0x1f,
    0x5c, 0x2f, 0x50, 0xdd, 0xdc, 0xf1, 0x4, 0xef,
    0xe5, 0x0, 0x0, 0xc0, 0x0, 0x0, 0x6, 0x0,
    0x0,

    /* U+0025 "%" */
    0x8, 0xed, 0x40, 0x0, 0x2f, 0x10, 0x0, 0x2f,
    0x49, 0xc0, 0x0, 0x6d, 0x0, 0x0, 0x5f, 0x5,
    0xf0, 0x0, 0xa9, 0x0, 0x0, 0x5f, 0x5, 0xf0,
    0x0, 0xe4, 0x0, 0x0, 0x6f, 0x5, 0xf0, 0x2,
    0xf0, 0x0, 0x0, 0x6f, 0x5, 0xf0, 0x6, 0xc0,
    0x0, 0x0, 0x6f, 0x5, 0xf0, 0xb, 0x80, 0x0,
    0x0, 0x6f, 0x5, 0xf0, 0xe, 0x41, 0xcf, 0xb0,
    0x5f, 0x5, 0xf0, 0x3f, 0x9, 0xd4, 0xf5, 0x5f,
    0x5, 0xf0, 0x7b, 0xb, 0x80, 0xc8, 0x3f, 0x39,
    0xd0, 0xb7, 0xc, 0x80, 0xb9, 0xb, 0xff, 0x60,
    0xf3, 0xc, 0x80, 0xb9, 0x0, 0x21, 0x4, 0xf0,
    0xc, 0x80, 0xb9, 0x0, 0x0, 0x8, 0xb0, 0xc,
    0x80, 0xb9, 0x0, 0x0, 0xc, 0x70, 0xc, 0x80,
    0xb9, 0x0, 0x0, 0xf, 0x30, 0xc, 0x80, 0xb9,
    0x0, 0x0, 0x4e, 0x0, 0xb, 0x80, 0xc8, 0x0,
    0x0, 0x8a, 0x0, 0x9, 0xd4, 0xf5, 0x0, 0x0,
    0xc6, 0x0, 0x2, 0xcf, 0xb0,

    /* U+0026 "&" */
    0x1, 0xbf, 0xe7, 0x0, 0xa, 0xf7, 0xaf, 0x30,
    0xe, 0x90, 0xe, 0x80, 0xf, 0x70, 0xd, 0xa0,
    0xf, 0x70, 0x1, 0x10, 0xf, 0x70, 0x0, 0x0,
    0xe, 0x80, 0x6, 0x40, 0x9, 0xe5, 0x2d, 0xb0,
    0x0, 0x8f, 0xff, 0xf6, 0x5, 0xf9, 0x5e, 0xc2,
    0xd, 0x90, 0xd, 0xa0, 0xf, 0x70, 0xd, 0xa0,
    0xf, 0x70, 0xd, 0xa0, 0xf, 0x70, 0xd, 0xa0,
    0xf, 0x70, 0xd, 0xa0, 0xf, 0x70, 0xd, 0x90,
    0xe, 0x90, 0xf, 0x70, 0x8, 0xf7, 0xaf, 0x20,
    0x0, 0xae, 0xd6, 0x0,

    /* U+0027 "'" */
    0x6f, 0x5, 0xe0, 0x3d, 0x2, 0xc0, 0x1b, 0x0,
    0x10,

    /* U+0028 "(" */
    0x0, 0xbb, 0x0, 0xf6, 0x5, 0xf1, 0x9, 0xd0,
    0xc, 0xa0, 0xf, 0x70, 0x2f, 0x50, 0x4f, 0x30,
    0x5f, 0x10, 0x6f, 0x0, 0x7f, 0x0, 0x7f, 0x0,
    0x7f, 0x0, 0x6f, 0x0, 0x5f, 0x10, 0x4f, 0x30,
    0x2f, 0x50, 0xf, 0x70, 0xc, 0xa0, 0x9, 0xd0,
    0x5, 0xf1, 0x0, 0xf6, 0x0, 0xbb,

    /* U+0029 ")" */
    0xbc, 0x0, 0x6f, 0x0, 0x1f, 0x50, 0xd, 0x90,
    0xa, 0xc0, 0x7, 0xf0, 0x5, 0xf1, 0x3, 0xf3,
    0x1, 0xf5, 0x0, 0xf6, 0x0, 0xf7, 0x0, 0xf7,
    0x0, 0xf7, 0x0, 0xf6, 0x1, 0xf5, 0x3, 0xf3,
    0x5, 0xf1, 0x7, 0xf0, 0xa, 0xc0, 0xd, 0x90,
    0x1f, 0x50, 0x6f, 0x10, 0xbc, 0x0,

    /* U+002A "*" */
    0x0, 0x10, 0x20, 0x0, 0x8a, 0x3f, 0x10, 0x1,
    0xec, 0x60, 0x8, 0xff, 0xff, 0xf0, 0x12, 0xde,
    0x62, 0x0, 0x7c, 0x5e, 0x0, 0x2, 0x30, 0x50,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0, 0x9b,
    0x0, 0x0, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x1,
    0x11, 0xac, 0x11, 0x10, 0xf, 0xff, 0xff, 0xff,
    0xf2, 0x4, 0x44, 0xbc, 0x44, 0x40, 0x0, 0x0,
    0x9b, 0x0, 0x0, 0x0, 0x0, 0x9b, 0x0, 0x0,
    0x0, 0x0, 0x9b, 0x0, 0x0,

    /* U+002C "," */
    0x1, 0x84, 0x7, 0xf2, 0xc, 0xa0, 0x1f, 0x20,
    0x6a, 0x0, 0xb2, 0x0,

    /* U+002D "-" */
    0x1, 0x11, 0x3f, 0xfe, 0x4, 0x43,

    /* U+002E "." */
    0x0, 0x1, 0xe9, 0x1e, 0x80,

    /* U+002F "/" */
    0x0, 0x0, 0x1f, 0x40, 0x0, 0x5, 0xf0, 0x0,
    0x0, 0x9c, 0x0, 0x0, 0xd, 0x80, 0x0, 0x1,
    0xf4, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x9, 0xc0,
    0x0, 0x0, 0xc8, 0x0, 0x0, 0x1f, 0x40, 0x0,
    0x4, 0xf1, 0x0, 0x0, 0x8c, 0x0, 0x0, 0xc,
    0x80, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x4f, 0x10,
    0x0, 0x8, 0xd0, 0x0, 0x0, 0xc9, 0x0, 0x0,
    0xf, 0x50, 0x0, 0x4, 0xf1, 0x0, 0x0, 0x8d,
    0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x1a, 0xfe, 0x70, 0x9f, 0x7a, 0xf4, 0xe9, 0x0,
    0xe9, 0xf8, 0x0, 0xda, 0xf8, 0x0, 0xda, 0xf8,
    0x0, 0xda, 0xf8, 0x0, 0xda, 0xf8, 0x0, 0xda,
    0xf8, 0x0, 0xda, 0xf8, 0x0, 0xda, 0xf8, 0x0,
    0xda, 0xf8, 0x0, 0xda, 0xf8, 0x0, 0xda, 0xf8,
    0x0, 0xda, 0xf8, 0x0, 0xda, 0xf8, 0x0, 0xda,
    0xd9, 0x0, 0xe8, 0x9f, 0x7a, 0xf4, 0x1a, 0xfe,
    0x70,

    /* U+0031 "1" */
    0x7, 0xed, 0xcf, 0xfd, 0x72, 0xad, 0x0, 0xad,
    0x0, 0xad, 0x0, 0xad, 0x0, 0xad, 0x0, 0xad,
    0x0, 0xad, 0x0, 0xad, 0x0, 0xad, 0x0, 0xad,
    0x0, 0xad, 0x0, 0xad, 0x0, 0xad, 0x0, 0xad,
    0x0, 0xad, 0x0, 0xad, 0x0, 0xad,

    /* U+0032 "2" */
    0x3, 0xcf, 0xc3, 0x0, 0xdd, 0x6c, 0xe0, 0x2f,
    0x50, 0x3f, 0x44, 0xf3, 0x1, 0xf5, 0x4f, 0x30,
    0x1f, 0x64, 0xf3, 0x1, 0xf6, 0x27, 0x10, 0x2f,
    0x50, 0x0, 0x6, 0xf2, 0x0, 0x0, 0xdd, 0x0,
    0x0, 0x4f, 0x60, 0x0, 0xc, 0xe0, 0x0, 0x5,
    0xf7, 0x0, 0x0, 0xde, 0x0, 0x0, 0x4f, 0x70,
    0x0, 0xb, 0xf1, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0x3f, 0x60, 0x0, 0x4, 0xfa, 0x88, 0x70, 0x4f,
    0xff, 0xff, 0x0,

    /* U+0033 "3" */
    0x3, 0xcf, 0xd4, 0x0, 0xdd, 0x6c, 0xf1, 0x2f,
    0x50, 0x2f, 0x44, 0xf3, 0x1, 0xf6, 0x4f, 0x30,
    0x1f, 0x63, 0xd3, 0x1, 0xf6, 0x0, 0x0, 0x1f,
    0x40, 0x0, 0x2a, 0xd0, 0x0, 0x9f, 0xd1, 0x0,
    0x2, 0x5d, 0xc0, 0x0, 0x0, 0x3f, 0x30, 0x0,
    0x1, 0xf5, 0x29, 0x20, 0x1f, 0x64, 0xf3, 0x1,
    0xf6, 0x4f, 0x30, 0x1f, 0x63, 0xf3, 0x1, 0xf5,
    0x2f, 0x50, 0x3f, 0x40, 0xde, 0x7c, 0xe0, 0x2,
    0xbf, 0xd3, 0x0,

    /* U+0034 "4" */
    0x0, 0xc, 0xf6, 0x0, 0x0, 0xff, 0x60, 0x0,
    0x4d, 0xf6, 0x0, 0x8, 0xaf, 0x60, 0x0, 0xc6,
    0xf6, 0x0, 0xf, 0x3f, 0x60, 0x4, 0xf1, 0xf6,
    0x0, 0x8c, 0x1f, 0x60, 0xc, 0x81, 0xf6, 0x0,
    0xf5, 0x1f, 0x60, 0x4f, 0x21, 0xf6, 0x8, 0xf0,
    0x1f, 0x60, 0xce, 0x88, 0xfb, 0x6c, 0xff, 0xff,
    0xfd, 0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f, 0x60, 0x0,
    0x1, 0xf6, 0x0,

    /* U+0035 "5" */
    0x1f, 0xff, 0xff, 0x1, 0xfb, 0x88, 0x80, 0x1f,
    0x60, 0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f, 0x60, 0x0,
    0x1, 0xf8, 0xdf, 0xa0, 0x1f, 0xe5, 0x8f, 0x51,
    0xf8, 0x0, 0xf8, 0x3, 0x10, 0xe, 0x90, 0x0,
    0x0, 0xe9, 0x0, 0x0, 0xe, 0x90, 0x21, 0x0,
    0xe9, 0x1f, 0x60, 0xe, 0x90, 0xf6, 0x0, 0xe9,
    0xf, 0x80, 0xf, 0x80, 0xbf, 0x7b, 0xf3, 0x1,
    0xbf, 0xe6, 0x0,

    /* U+0036 "6" */
    0xa, 0xee, 0x70, 0x9f, 0x7a, 0xf5, 0xd9, 0x0,
    0xe9, 0xf8, 0x0, 0xda, 0xf8, 0x0, 0xda, 0xf8,
    0x0, 0x21, 0xf8, 0x0, 0x0, 0xf9, 0xcf, 0xb0,
    0xfe, 0x67, 0xf6, 0xfa, 0x0, 0xe9, 0xf8, 0x0,
    0xca, 0xf8, 0x0, 0xcb, 0xf8, 0x0, 0xcb, 0xf8,
    0x0, 0xcb, 0xf8, 0x0, 0xcb, 0xf8, 0x0, 0xca,
    0xea, 0x0, 0xe9, 0x9f, 0x8a, 0xf4, 0xa, 0xee,
    0x70,

    /* U+0037 "7" */
    0x8f, 0xff, 0xff, 0xe4, 0x88, 0x88, 0xfa, 0x0,
    0x0, 0x2f, 0x50, 0x0, 0x6, 0xf1, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0xf, 0x80, 0x0, 0x4, 0xf4,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0xd, 0xb0, 0x0,
    0x1, 0xf7, 0x0, 0x0, 0x6f, 0x20, 0x0, 0xa,
    0xe0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x3f, 0x50,
    0x0, 0x8, 0xf1, 0x0, 0x0, 0xcd, 0x0, 0x0,
    0x1f, 0x80, 0x0, 0x5, 0xf4, 0x0, 0x0, 0xaf,
    0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x1, 0xaf, 0xd6, 0x0, 0xaf, 0x7a, 0xf2, 0xf,
    0x80, 0xf, 0x70, 0xf6, 0x0, 0xe9, 0x1f, 0x60,
    0xe, 0x90, 0xf6, 0x0, 0xe9, 0xe, 0x70, 0xe,
    0x70, 0x8c, 0x14, 0xf2, 0x0, 0xdf, 0xf6, 0x0,
    0x8d, 0x48, 0xe1, 0xe, 0x70, 0xe, 0x60, 0xf6,
    0x0, 0xe9, 0x1f, 0x60, 0xe, 0x91, 0xf6, 0x0,
    0xe9, 0x1f, 0x60, 0xe, 0x90, 0xf6, 0x0, 0xe9,
    0xe, 0x80, 0xf, 0x70, 0xaf, 0x7b, 0xf2, 0x1,
    0xaf, 0xd6, 0x0,

    /* U+0039 "9" */
    0x1, 0xbf, 0xd5, 0x0, 0xbf, 0x7b, 0xf2, 0xf,
    0x80, 0x1f, 0x71, 0xf6, 0x0, 0xf8, 0x1f, 0x60,
    0xf, 0x81, 0xf6, 0x0, 0xf8, 0x1f, 0x60, 0xf,
    0x81, 0xf6, 0x0, 0xf8, 0x1f, 0x60, 0xf, 0x80,
    0xf7, 0x0, 0xf8, 0xe, 0xc2, 0x7f, 0x80, 0x5f,
    0xfa, 0xe8, 0x0, 0x13, 0xe, 0x80, 0x0, 0x0,
    0xe8, 0x1f, 0x60, 0xe, 0x81, 0xf6, 0x0, 0xe8,
    0xf, 0x80, 0xf, 0x70, 0xce, 0x7b, 0xf3, 0x2,
    0xbf, 0xd6, 0x0,

    /* U+003A ":" */
    0x1e, 0x81, 0xe9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xe9, 0x1e,
    0x80,

    /* U+003B ";" */
    0x0, 0x0, 0x0, 0x8, 0xf1, 0x0, 0x8e, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xa0, 0x1, 0xf7, 0x0, 0x6e, 0x0, 0xb, 0x70,
    0x1, 0xe0, 0x0, 0x24, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x4, 0xf7,
    0x0, 0x0, 0x2f, 0xb0, 0x0, 0x1, 0xec, 0x0,
    0x0, 0xc, 0xe1, 0x0, 0x0, 0xaf, 0x20, 0x0,
    0x7, 0xf4, 0x0, 0x0, 0x5f, 0x60, 0x0, 0x0,
    0x9f, 0x20, 0x0, 0x0, 0xb, 0xe1, 0x0, 0x0,
    0x1, 0xdc, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0,
    0x0, 0x4, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0x60,
    0x0, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0xa3,

    /* U+003D "=" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xf2, 0x3, 0x33, 0x33, 0x33, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11,
    0x10, 0xf, 0xff, 0xff, 0xff, 0xf2, 0x4, 0x44,
    0x44, 0x44, 0x40,

    /* U+003E ">" */
    0x5, 0x0, 0x0, 0x0, 0x5f, 0x60, 0x0, 0x0,
    0x9, 0xf4, 0x0, 0x0, 0x0, 0xbe, 0x20, 0x0,
    0x0, 0xd, 0xd1, 0x0, 0x0, 0x1, 0xeb, 0x0,
    0x0, 0x0, 0x3f, 0x90, 0x0, 0x0, 0x4, 0xf6,
    0x0, 0x0, 0x1, 0xea, 0x0, 0x0, 0xd, 0xd0,
    0x0, 0x0, 0xbe, 0x10, 0x0, 0x9, 0xf3, 0x0,
    0x0, 0x7f, 0x50, 0x0, 0x5, 0xf8, 0x0, 0x0,
    0x3f, 0xa0, 0x0, 0x0, 0x2b, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x5, 0xdf, 0xb1, 0x2, 0xfb, 0x7e, 0xb0, 0x6f,
    0x10, 0x7f, 0x8, 0xf0, 0x6, 0xf1, 0x8f, 0x0,
    0x6f, 0x28, 0xf0, 0x6, 0xf1, 0x12, 0x0, 0x6f,
    0x10, 0x0, 0xa, 0xe0, 0x0, 0x9d, 0xf7, 0x0,
    0xd, 0xc4, 0x0, 0x0, 0xc8, 0x0, 0x0, 0xc,
    0x80, 0x0, 0x0, 0xb7, 0x0, 0x0, 0xb, 0x60,
    0x0, 0x0, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0,
    0xdb, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x9e, 0xff, 0xd7, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe7, 0x55, 0x9f, 0xd1, 0x0,
    0x0, 0x4, 0xf9, 0x0, 0x0, 0x2, 0xec, 0x0,
    0x0, 0x1e, 0xa0, 0x0, 0x0, 0x0, 0x4f, 0x60,
    0x0, 0x9e, 0x0, 0x0, 0x0, 0x10, 0xb, 0xc0,
    0x1, 0xf6, 0x0, 0xd, 0xf8, 0xf4, 0x5, 0xf1,
    0x7, 0xf0, 0x0, 0x5f, 0x7c, 0xf1, 0x1, 0xf4,
    0xc, 0xa0, 0x0, 0x9d, 0x8, 0xe0, 0x0, 0xf5,
    0xf, 0x50, 0x0, 0xd9, 0xa, 0xb0, 0x0, 0xf6,
    0x3f, 0x20, 0x0, 0xf6, 0xd, 0x80, 0x0, 0xf5,
    0x5f, 0x0, 0x3, 0xf3, 0xf, 0x50, 0x2, 0xf4,
    0x6e, 0x0, 0x7, 0xf0, 0x4f, 0x20, 0x5, 0xf1,
    0x7d, 0x0, 0xa, 0xc0, 0x7f, 0x0, 0xa, 0xd0,
    0x7d, 0x0, 0xd, 0x90, 0xac, 0x0, 0xe, 0x70,
    0x6e, 0x0, 0xf, 0x70, 0xe9, 0x0, 0x7e, 0x0,
    0x4f, 0x10, 0xf, 0xb9, 0xda, 0x5, 0xf5, 0x0,
    0x1f, 0x40, 0xb, 0xe6, 0x2d, 0xfc, 0x40, 0x0,
    0xc, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xce, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xf8, 0x31, 0x26, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x8e, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x23, 0x30, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x7f, 0x50, 0x0, 0x9, 0xf7, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0xd, 0xec, 0x0, 0x0, 0xfa,
    0xe0, 0x0, 0x1f, 0x6f, 0x0, 0x3, 0xf3, 0xf2,
    0x0, 0x5f, 0xf, 0x40, 0x7, 0xd0, 0xe6, 0x0,
    0xab, 0xc, 0x80, 0xc, 0x90, 0xba, 0x0, 0xe8,
    0x9, 0xc0, 0xf, 0x71, 0x8f, 0x2, 0xff, 0xff,
    0xf1, 0x4f, 0x54, 0x6f, 0x36, 0xf0, 0x1, 0xf5,
    0x8e, 0x0, 0xf, 0x7a, 0xc0, 0x0, 0xd9, 0xca,
    0x0, 0xb, 0xb0,

    /* U+0042 "B" */
    0x1f, 0xff, 0xe6, 0x1, 0xfb, 0x8c, 0xf2, 0x1f,
    0x60, 0x1f, 0x61, 0xf6, 0x0, 0xf7, 0x1f, 0x60,
    0xf, 0x71, 0xf6, 0x0, 0xf7, 0x1f, 0x60, 0xf,
    0x71, 0xf6, 0x2, 0xf5, 0x1f, 0xb8, 0xcc, 0x1,
    0xff, 0xff, 0x60, 0x1f, 0x60, 0x4f, 0x41, 0xf6,
    0x0, 0xd9, 0x1f, 0x60, 0xc, 0xb1, 0xf6, 0x0,
    0xcb, 0x1f, 0x60, 0xc, 0xb1, 0xf6, 0x0, 0xcb,
    0x1f, 0x60, 0xe, 0x91, 0xfb, 0x8b, 0xf5, 0x1f,
    0xff, 0xe8, 0x0,

    /* U+0043 "C" */
    0x2, 0xbf, 0xd5, 0x0, 0xbe, 0x7b, 0xf2, 0xf,
    0x70, 0x1f, 0x61, 0xf5, 0x0, 0xf8, 0x2f, 0x50,
    0xf, 0x82, 0xf5, 0x0, 0xf8, 0x2f, 0x50, 0xa,
    0x52, 0xf5, 0x0, 0x0, 0x2f, 0x50, 0x0, 0x2,
    0xf5, 0x0, 0x0, 0x2f, 0x50, 0x0, 0x2, 0xf5,
    0x0, 0x0, 0x2f, 0x50, 0x7, 0x32, 0xf5, 0x0,
    0xf8, 0x2f, 0x50, 0xf, 0x81, 0xf5, 0x0, 0xf8,
    0xf, 0x70, 0x1f, 0x60, 0xbe, 0x7b, 0xf2, 0x2,
    0xbf, 0xd5, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xd6, 0x1, 0xfb, 0x8c, 0xf2, 0x1f,
    0x60, 0x2f, 0x61, 0xf6, 0x0, 0xf7, 0x1f, 0x60,
    0xf, 0x71, 0xf6, 0x0, 0xf7, 0x1f, 0x60, 0xf,
    0x71, 0xf6, 0x0, 0xf7, 0x1f, 0x60, 0xf, 0x71,
    0xf6, 0x0, 0xf7, 0x1f, 0x60, 0xf, 0x71, 0xf6,
    0x0, 0xf7, 0x1f, 0x60, 0xf, 0x71, 0xf6, 0x0,
    0xf7, 0x1f, 0x60, 0xf, 0x71, 0xf6, 0x0, 0xf7,
    0x1f, 0x60, 0x2f, 0x61, 0xfb, 0x8c, 0xf2, 0x1f,
    0xff, 0xe6, 0x0,

    /* U+0045 "E" */
    0x1f, 0xff, 0xfd, 0x1f, 0xb8, 0x86, 0x1f, 0x60,
    0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f,
    0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0,
    0x1f, 0x60, 0x0, 0x1f, 0xff, 0xfa, 0x1f, 0xb8,
    0x85, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f,
    0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0,
    0x1f, 0x60, 0x0, 0x1f, 0xb8, 0x86, 0x1f, 0xff,
    0xfd,

    /* U+0046 "F" */
    0x1f, 0xff, 0xfd, 0x1f, 0xb8, 0x86, 0x1f, 0x60,
    0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f,
    0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0,
    0x1f, 0x60, 0x0, 0x1f, 0xff, 0xfa, 0x1f, 0xb8,
    0x85, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f,
    0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0,
    0x1f, 0x60, 0x0, 0x1f, 0x60, 0x0, 0x1f, 0x60,
    0x0,

    /* U+0047 "G" */
    0x1, 0xbf, 0xe8, 0x0, 0xbe, 0x79, 0xf5, 0xf,
    0x70, 0xd, 0xa1, 0xf5, 0x0, 0xbc, 0x2f, 0x50,
    0xb, 0xc2, 0xf5, 0x0, 0xbc, 0x2f, 0x50, 0x9,
    0xa2, 0xf5, 0x0, 0x0, 0x2f, 0x50, 0x0, 0x2,
    0xf5, 0x2, 0x21, 0x2f, 0x55, 0xff, 0xc2, 0xf5,
    0x15, 0xcc, 0x2f, 0x50, 0xb, 0xc2, 0xf5, 0x0,
    0xbc, 0x2f, 0x50, 0xb, 0xc1, 0xf5, 0x0, 0xbc,
    0xf, 0x70, 0xd, 0xa0, 0xbe, 0x79, 0xf6, 0x1,
    0xbf, 0xe8, 0x0,

    /* U+0048 "H" */
    0x1f, 0x60, 0xe, 0x91, 0xf6, 0x0, 0xe9, 0x1f,
    0x60, 0xe, 0x91, 0xf6, 0x0, 0xe9, 0x1f, 0x60,
    0xe, 0x91, 0xf6, 0x0, 0xe9, 0x1f, 0x60, 0xe,
    0x91, 0xf6, 0x0, 0xe9, 0x1f, 0xb8, 0x8f, 0x91,
    0xff, 0xff, 0xf9, 0x1f, 0x60, 0xe, 0x91, 0xf6,
    0x0, 0xe9, 0x1f, 0x60, 0xe, 0x91, 0xf6, 0x0,
    0xe9, 0x1f, 0x60, 0xe, 0x91, 0xf6, 0x0, 0xe9,
    0x1f, 0x60, 0xe, 0x91, 0xf6, 0x0, 0xe9, 0x1f,
    0x60, 0xe, 0x90,

    /* U+0049 "I" */
    0x1f, 0x61, 0xf6, 0x1f, 0x61, 0xf6, 0x1f, 0x61,
    0xf6, 0x1f, 0x61, 0xf6, 0x1f, 0x61, 0xf6, 0x1f,
    0x61, 0xf6, 0x1f, 0x61, 0xf6, 0x1f, 0x61, 0xf6,
    0x1f, 0x61, 0xf6, 0x1f, 0x60,

    /* U+004A "J" */
    0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6,
    0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6,
    0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6,
    0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6,
    0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6, 0x1, 0xf6,
    0x1, 0xf6, 0x3, 0xf4, 0x3c, 0xf1, 0x7d, 0x40,

    /* U+004B "K" */
    0x1f, 0x60, 0xc, 0xb0, 0x1f, 0x60, 0x1f, 0x60,
    0x1f, 0x60, 0x6f, 0x20, 0x1f, 0x60, 0xbd, 0x0,
    0x1f, 0x60, 0xf8, 0x0, 0x1f, 0x65, 0xf3, 0x0,
    0x1f, 0x6a, 0xe0, 0x0, 0x1f, 0x6e, 0xa0, 0x0,
    0x1f, 0xaf, 0x50, 0x0, 0x1f, 0xef, 0x20, 0x0,
    0x1f, 0x9f, 0x70, 0x0, 0x1f, 0x6d, 0xc0, 0x0,
    0x1f, 0x67, 0xf1, 0x0, 0x1f, 0x62, 0xf7, 0x0,
    0x1f, 0x60, 0xcc, 0x0, 0x1f, 0x60, 0x6f, 0x20,
    0x1f, 0x60, 0x1f, 0x70, 0x1f, 0x60, 0xb, 0xd0,
    0x1f, 0x60, 0x5, 0xf2,

    /* U+004C "L" */
    0x1f, 0x60, 0x1, 0xf6, 0x0, 0x1f, 0x60, 0x1,
    0xf6, 0x0, 0x1f, 0x60, 0x1, 0xf6, 0x0, 0x1f,
    0x60, 0x1, 0xf6, 0x0, 0x1f, 0x60, 0x1, 0xf6,
    0x0, 0x1f, 0x60, 0x1, 0xf6, 0x0, 0x1f, 0x60,
    0x1, 0xf6, 0x0, 0x1f, 0x60, 0x1, 0xf6, 0x0,
    0x1f, 0x60, 0x1, 0xfb, 0x87, 0x1f, 0xff, 0xe0,

    /* U+004D "M" */
    0x1f, 0xf4, 0x0, 0xc, 0xf9, 0x1f, 0xf7, 0x0,
    0xe, 0xf9, 0x1f, 0xf9, 0x0, 0x1f, 0xf9, 0x1f,
    0xfb, 0x0, 0x3f, 0xf9, 0x1f, 0xde, 0x0, 0x6f,
    0xd9, 0x1f, 0xbf, 0x0, 0x8d, 0xd9, 0x1f, 0x9f,
    0x30, 0xaa, 0xd9, 0x1f, 0x6f, 0x50, 0xd8, 0xd9,
    0x1f, 0x5e, 0x70, 0xf5, 0xd9, 0x1f, 0x5c, 0xa1,
    0xf2, 0xd9, 0x1f, 0x5a, 0xc4, 0xf0, 0xd9, 0x1f,
    0x67, 0xf6, 0xd0, 0xd9, 0x1f, 0x65, 0xfa, 0xa0,
    0xd9, 0x1f, 0x63, 0xff, 0x80, 0xe9, 0x1f, 0x60,
    0xff, 0x50, 0xe9, 0x1f, 0x60, 0xef, 0x20, 0xe9,
    0x1f, 0x60, 0xbf, 0x0, 0xe9, 0x1f, 0x60, 0x9d,
    0x0, 0xe9, 0x1f, 0x60, 0x7a, 0x0, 0xe9,

    /* U+004E "N" */
    0x1f, 0x70, 0x5, 0xf2, 0x1f, 0xb0, 0x5, 0xf2,
    0x1f, 0xf0, 0x5, 0xf2, 0x1f, 0xf3, 0x5, 0xf2,
    0x1f, 0xf7, 0x5, 0xf2, 0x1f, 0xfb, 0x5, 0xf2,
    0x1f, 0xcf, 0x5, 0xf2, 0x1f, 0x9f, 0x35, 0xf2,
    0x1f, 0x6f, 0x75, 0xf2, 0x1f, 0x6b, 0xb5, 0xf2,
    0x1f, 0x67, 0xf5, 0xf2, 0x1f, 0x63, 0xf8, 0xf2,
    0x1f, 0x60, 0xfc, 0xf2, 0x1f, 0x60, 0xbf, 0xf2,
    0x1f, 0x60, 0x7f, 0xf2, 0x1f, 0x60, 0x4f, 0xf2,
    0x1f, 0x60, 0xf, 0xf2, 0x1f, 0x60, 0xc, 0xf2,
    0x1f, 0x60, 0x8, 0xf2,

    /* U+004F "O" */
    0x2, 0xbf, 0xd5, 0x0, 0xbe, 0x7b, 0xf2, 0xf,
    0x70, 0x1f, 0x61, 0xf5, 0x0, 0xf8, 0x2f, 0x50,
    0xf, 0x82, 0xf5, 0x0, 0xf8, 0x2f, 0x50, 0xf,
    0x82, 0xf5, 0x0, 0xf8, 0x2f, 0x50, 0xf, 0x82,
    0xf5, 0x0, 0xf8, 0x2f, 0x50, 0xf, 0x82, 0xf5,
    0x0, 0xf8, 0x2f, 0x50, 0xf, 0x82, 0xf5, 0x0,
    0xf8, 0x2f, 0x50, 0xf, 0x81, 0xf5, 0x0, 0xf8,
    0xf, 0x70, 0x1f, 0x60, 0xbe, 0x7b, 0xf2, 0x2,
    0xbf, 0xd5, 0x0,

    /* U+0050 "P" */
    0x1f, 0xff, 0xd6, 0x1, 0xfb, 0x8c, 0xf2, 0x1f,
    0x60, 0x2f, 0x61, 0xf6, 0x0, 0xf7, 0x1f, 0x60,
    0xf, 0x71, 0xf6, 0x0, 0xf7, 0x1f, 0x60, 0xf,
    0x71, 0xf6, 0x0, 0xf7, 0x1f, 0x60, 0x2f, 0x61,
    0xfb, 0x8c, 0xf1, 0x1f, 0xff, 0xd6, 0x1, 0xf6,
    0x0, 0x0, 0x1f, 0x60, 0x0, 0x1, 0xf6, 0x0,
    0x0, 0x1f, 0x60, 0x0, 0x1, 0xf6, 0x0, 0x0,
    0x1f, 0x60, 0x0, 0x1, 0xf6, 0x0, 0x0, 0x1f,
    0x60, 0x0, 0x0,

    /* U+0051 "Q" */
    0x2, 0xbf, 0xd5, 0x0, 0xbe, 0x7b, 0xf2, 0xf,
    0x70, 0x1f, 0x61, 0xf5, 0x0, 0xf8, 0x2f, 0x50,
    0xf, 0x82, 0xf5, 0x0, 0xf8, 0x2f, 0x50, 0xf,
    0x82, 0xf5, 0x0, 0xf8, 0x2f, 0x50, 0xf, 0x82,
    0xf5, 0x0, 0xf8, 0x2f, 0x50, 0xf, 0x82, 0xf5,
    0x0, 0xf8, 0x2f, 0x50, 0xf, 0x82, 0xf5, 0x0,
    0xf8, 0x2f, 0x50, 0xf, 0x81, 0xf5, 0x0, 0xf8,
    0xf, 0x70, 0x1f, 0x60, 0xbe, 0x7b, 0xf2, 0x2,
    0xbf, 0xf8, 0x0, 0x0, 0xa, 0xa0, 0x0, 0x0,
    0x3f, 0x20, 0x0, 0x0, 0x93,

    /* U+0052 "R" */
    0x1f, 0xff, 0xe6, 0x1, 0xfb, 0x8d, 0xf2, 0x1f,
    0x60, 0x2f, 0x61, 0xf6, 0x0, 0xf7, 0x1f, 0x60,
    0xf, 0x71, 0xf6, 0x0, 0xf7, 0x1f, 0x60, 0xf,
    0x71, 0xf6, 0x0, 0xf7, 0x1f, 0x60, 0x2f, 0x51,
    0xfb, 0x8c, 0xf1, 0x1f, 0xff, 0xe5, 0x1, 0xf6,
    0x8e, 0x0, 0x1f, 0x64, 0xf2, 0x1, 0xf6, 0xf,
    0x70, 0x1f, 0x60, 0xbb, 0x1, 0xf6, 0x7, 0xf0,
    0x1f, 0x60, 0x3f, 0x41, 0xf6, 0x0, 0xe8, 0x1f,
    0x60, 0xa, 0xd0,

    /* U+0053 "S" */
    0x3, 0xcf, 0xc3, 0x0, 0xed, 0x6c, 0xe0, 0x3f,
    0x40, 0x3f, 0x34, 0xf3, 0x2, 0xf5, 0x5f, 0x30,
    0x2f, 0x54, 0xf3, 0x2, 0xf5, 0x3f, 0x40, 0x1b,
    0x40, 0xeb, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0,
    0xa, 0xf7, 0x0, 0x0, 0xb, 0xf5, 0x0, 0x0,
    0xd, 0xe0, 0x27, 0x10, 0x5f, 0x45, 0xf3, 0x2,
    0xf5, 0x4f, 0x30, 0x2f, 0x54, 0xf3, 0x2, 0xf5,
    0x2f, 0x40, 0x3f, 0x30, 0xed, 0x7d, 0xe0, 0x3,
    0xcf, 0xd3, 0x0,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0x36, 0x8c, 0xf8, 0x81, 0x0,
    0x8f, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0, 0x8f,
    0x0, 0x0, 0x8, 0xf0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x8, 0xf0, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x8, 0xf0, 0x0, 0x0, 0x8f, 0x0, 0x0, 0x8,
    0xf0, 0x0, 0x0, 0x8f, 0x0, 0x0, 0x8, 0xf0,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x8, 0xf0, 0x0,
    0x0, 0x8f, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0,
    0x8f, 0x0, 0x0,

    /* U+0055 "U" */
    0x1f, 0x60, 0xe, 0x91, 0xf6, 0x0, 0xe9, 0x1f,
    0x60, 0xe, 0x91, 0xf6, 0x0, 0xe9, 0x1f, 0x60,
    0xe, 0x91, 0xf6, 0x0, 0xe9, 0x1f, 0x60, 0xe,
    0x91, 0xf6, 0x0, 0xe9, 0x1f, 0x60, 0xe, 0x91,
    0xf6, 0x0, 0xe9, 0x1f, 0x60, 0xe, 0x91, 0xf6,
    0x0, 0xe9, 0x1f, 0x60, 0xe, 0x91, 0xf6, 0x0,
    0xe9, 0x1f, 0x60, 0xe, 0x90, 0xf6, 0x0, 0xe8,
    0xf, 0x80, 0xf, 0x70, 0xaf, 0x7b, 0xf3, 0x1,
    0xbf, 0xe6, 0x0,

    /* U+0056 "V" */
    0xbb, 0x0, 0x2f, 0x59, 0xd0, 0x3, 0xf3, 0x7f,
    0x0, 0x5f, 0x15, 0xf0, 0x6, 0xf0, 0x4f, 0x20,
    0x8d, 0x2, 0xf3, 0xa, 0xb0, 0xf, 0x50, 0xba,
    0x0, 0xe6, 0xd, 0x80, 0xc, 0x80, 0xe6, 0x0,
    0xa9, 0xf, 0x40, 0x8, 0xb1, 0xf2, 0x0, 0x6c,
    0x3f, 0x0, 0x4, 0xe4, 0xe0, 0x0, 0x2f, 0x6c,
    0x0, 0x1, 0xf9, 0xa0, 0x0, 0xf, 0xc9, 0x0,
    0x0, 0xdf, 0x70, 0x0, 0xb, 0xf5, 0x0, 0x0,
    0x9f, 0x30, 0x0,

    /* U+0057 "W" */
    0xac, 0x0, 0x5f, 0x40, 0xd, 0x99, 0xe0, 0x7,
    0xf6, 0x0, 0xf8, 0x7f, 0x0, 0x8f, 0x70, 0xf,
    0x65, 0xf1, 0xa, 0xf9, 0x1, 0xf4, 0x4f, 0x20,
    0xcf, 0xb0, 0x3f, 0x32, 0xf3, 0xd, 0xdc, 0x4,
    0xf1, 0xf, 0x50, 0xf9, 0xe0, 0x5f, 0x0, 0xf6,
    0xf, 0x6f, 0x7, 0xe0, 0xd, 0x72, 0xf3, 0xf1,
    0x8c, 0x0, 0xb9, 0x4f, 0xf, 0x39, 0xa0, 0x9,
    0xa5, 0xd0, 0xe5, 0xb9, 0x0, 0x8c, 0x7c, 0xc,
    0x6c, 0x70, 0x6, 0xd8, 0xa0, 0xb8, 0xd5, 0x0,
    0x4e, 0xa8, 0x9, 0xaf, 0x40, 0x3, 0xfc, 0x70,
    0x7c, 0xf2, 0x0, 0x1f, 0xf5, 0x5, 0xff, 0x0,
    0x0, 0xff, 0x30, 0x4f, 0xf0, 0x0, 0xe, 0xf1,
    0x2, 0xfd, 0x0, 0x0, 0xcf, 0x0, 0xf, 0xb0,
    0x0,

    /* U+0058 "X" */
    0xc, 0xb0, 0x0, 0x8f, 0x0, 0x7f, 0x0, 0xd,
    0xa0, 0x2, 0xf4, 0x1, 0xf6, 0x0, 0xd, 0x90,
    0x6f, 0x10, 0x0, 0x8e, 0xa, 0xd0, 0x0, 0x3,
    0xf3, 0xe8, 0x0, 0x0, 0xd, 0xbf, 0x40, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x3, 0xfb, 0x0,
    0x0, 0x0, 0x1f, 0x90, 0x0, 0x0, 0x6, 0xfd,
    0x0, 0x0, 0x0, 0xbe, 0xf1, 0x0, 0x0, 0xf,
    0x6f, 0x50, 0x0, 0x5, 0xf0, 0xca, 0x0, 0x0,
    0xab, 0x8, 0xe0, 0x0, 0xf, 0x60, 0x4f, 0x30,
    0x4, 0xf2, 0x0, 0xf7, 0x0, 0x9d, 0x0, 0xb,
    0xb0, 0xe, 0x80, 0x0, 0x7f, 0x0,

    /* U+0059 "Y" */
    0xad, 0x0, 0xb, 0xc7, 0xf0, 0x0, 0xe8, 0x3f,
    0x30, 0x1f, 0x50, 0xf6, 0x5, 0xf1, 0xc, 0x90,
    0x8e, 0x0, 0x9c, 0xb, 0xa0, 0x5, 0xf0, 0xe7,
    0x0, 0x2f, 0x5f, 0x30, 0x0, 0xeb, 0xf0, 0x0,
    0xb, 0xfc, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x3,
    0xf5, 0x0, 0x0, 0x2f, 0x40, 0x0, 0x2, 0xf4,
    0x0, 0x0, 0x2f, 0x40, 0x0, 0x2, 0xf4, 0x0,
    0x0, 0x2f, 0x40, 0x0, 0x2, 0xf4, 0x0, 0x0,
    0x2f, 0x40, 0x0,

    /* U+005A "Z" */
    0x7f, 0xff, 0xff, 0x38, 0x88, 0xfb, 0x0, 0x1,
    0xf7, 0x0, 0x4, 0xf3, 0x0, 0x8, 0xf0, 0x0,
    0xc, 0xc0, 0x0, 0xf, 0x80, 0x0, 0x4f, 0x40,
    0x0, 0x8f, 0x0, 0x0, 0xcc, 0x0, 0x0, 0xf8,
    0x0, 0x3, 0xf4, 0x0, 0x7, 0xf1, 0x0, 0xb,
    0xd0, 0x0, 0xf, 0x90, 0x0, 0x3f, 0x50, 0x0,
    0x7f, 0x10, 0x0, 0xaf, 0x88, 0x88, 0xbf, 0xff,
    0xff,

    /* U+005B "[" */
    0xef, 0xf5, 0xe9, 0x41, 0xe7, 0x0, 0xe7, 0x0,
    0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0,
    0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0,
    0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0,
    0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0, 0xe7, 0x0,
    0xe7, 0x0, 0xe8, 0x10, 0xef, 0xf5, 0x34, 0x41,

    /* U+005C "\\" */
    0x8d, 0x0, 0x0, 0x4, 0xf1, 0x0, 0x0, 0xf,
    0x50, 0x0, 0x0, 0xc9, 0x0, 0x0, 0x8, 0xd0,
    0x0, 0x0, 0x4f, 0x10, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0xc, 0x80, 0x0, 0x0, 0x8c, 0x0, 0x0,
    0x4, 0xf1, 0x0, 0x0, 0x1f, 0x40, 0x0, 0x0,
    0xc8, 0x0, 0x0, 0x9, 0xc0, 0x0, 0x0, 0x5f,
    0x0, 0x0, 0x1, 0xf4, 0x0, 0x0, 0xd, 0x80,
    0x0, 0x0, 0x9c, 0x0, 0x0, 0x5, 0xf0, 0x0,
    0x0, 0x1f, 0x40,

    /* U+005D "]" */
    0xbf, 0xf9, 0x34, 0xe9, 0x0, 0xd9, 0x0, 0xd9,
    0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9,
    0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9,
    0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9,
    0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9, 0x0, 0xd9,
    0x1, 0xd9, 0xbf, 0xf9, 0x24, 0x42,

    /* U+005E "^" */
    0x0, 0xe, 0xf0, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0x0, 0x9d, 0xcb, 0x0, 0x0, 0xe8, 0x6f, 0x10,
    0x4, 0xf2, 0x1f, 0x60, 0x9, 0xd0, 0xc, 0xb0,
    0xf, 0x80, 0x6, 0xf1, 0x5f, 0x20, 0x1, 0xf6,

    /* U+005F "_" */
    0x3f, 0xff, 0xff, 0x10, 0x22, 0x22, 0x20,

    /* U+0060 "`" */
    0x7, 0x40, 0xa, 0xe0, 0x1, 0xe5,

    /* U+0061 "a" */
    0x8, 0xee, 0x60, 0x2f, 0x9a, 0xf1, 0x5f, 0x23,
    0xf3, 0x5f, 0x12, 0xf4, 0x13, 0x2, 0xf4, 0x0,
    0x2, 0xf4, 0xc, 0xf9, 0xf4, 0x2f, 0x9a, 0xf4,
    0x4f, 0x23, 0xf4, 0x5f, 0x12, 0xf4, 0x5f, 0x12,
    0xf4, 0x4f, 0x24, 0xf4, 0x2f, 0xab, 0xf4, 0xa,
    0xe5, 0xf4,

    /* U+0062 "b" */
    0x2f, 0x40, 0x0, 0x2f, 0x40, 0x0, 0x2f, 0x40,
    0x0, 0x2f, 0x40, 0x0, 0x2f, 0x40, 0x0, 0x2f,
    0x7e, 0xb0, 0x2f, 0xb8, 0xf4, 0x2f, 0x50, 0xf6,
    0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40,
    0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f,
    0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6,
    0x2f, 0x50, 0xf6, 0x2f, 0xb7, 0xf4, 0x2f, 0x5e,
    0xb0,

    /* U+0063 "c" */
    0x6, 0xee, 0x80, 0x1f, 0xa9, 0xf2, 0x3f, 0x31,
    0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f,
    0x30, 0x31, 0x4f, 0x30, 0x0, 0x4f, 0x30, 0x0,
    0x4f, 0x30, 0x83, 0x4f, 0x31, 0xf5, 0x4f, 0x31,
    0xf5, 0x3f, 0x31, 0xf5, 0x1f, 0xa9, 0xf2, 0x6,
    0xee, 0x70,

    /* U+0064 "d" */
    0x0, 0x1, 0xf5, 0x0, 0x1, 0xf5, 0x0, 0x1,
    0xf5, 0x0, 0x1, 0xf5, 0x0, 0x1, 0xf5, 0xa,
    0xf7, 0xf5, 0x1f, 0x9a, 0xf5, 0x3f, 0x32, 0xf5,
    0x4f, 0x21, 0xf5, 0x4f, 0x21, 0xf5, 0x4f, 0x21,
    0xf5, 0x4f, 0x21, 0xf5, 0x4f, 0x21, 0xf5, 0x4f,
    0x21, 0xf5, 0x4f, 0x21, 0xf5, 0x4f, 0x21, 0xf5,
    0x3f, 0x32, 0xf5, 0x1f, 0x99, 0xf5, 0xa, 0xf6,
    0xe5,

    /* U+0065 "e" */
    0x6, 0xee, 0x80, 0x1f, 0xa9, 0xf2, 0x3f, 0x31,
    0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f,
    0x31, 0xf5, 0x4f, 0xff, 0xf5, 0x4f, 0x63, 0x31,
    0x4f, 0x30, 0x0, 0x4f, 0x31, 0xe5, 0x4f, 0x31,
    0xf5, 0x3f, 0x31, 0xf5, 0x1f, 0xa9, 0xf2, 0x6,
    0xee, 0x70,

    /* U+0066 "f" */
    0x0, 0xbf, 0x50, 0x6f, 0x62, 0x9, 0xd0, 0x0,
    0x9d, 0x0, 0xa, 0xd0, 0xf, 0xff, 0xf6, 0x5c,
    0xe5, 0x20, 0xad, 0x0, 0xa, 0xd0, 0x0, 0xad,
    0x0, 0xa, 0xd0, 0x0, 0xad, 0x0, 0xa, 0xd0,
    0x0, 0xad, 0x0, 0xa, 0xd0, 0x0, 0xad, 0x0,
    0xa, 0xd0, 0x0, 0xad, 0x0, 0xa, 0xd0, 0x0,

    /* U+0067 "g" */
    0x7, 0xef, 0xff, 0x1f, 0x98, 0xf3, 0x3f, 0x32,
    0xf3, 0x4f, 0x21, 0xf5, 0x4f, 0x21, 0xf5, 0x4f,
    0x21, 0xf5, 0x4f, 0x22, 0xf4, 0x1f, 0x66, 0xf2,
    0xa, 0xff, 0xa0, 0xc, 0x52, 0x0, 0x1f, 0x50,
    0x0, 0xe, 0xff, 0x90, 0x3f, 0x68, 0xf2, 0x7f,
    0x2, 0xf4, 0x8e, 0x1, 0xf5, 0x8e, 0x1, 0xf5,
    0x7f, 0x2, 0xf4, 0x4f, 0x8a, 0xf1, 0x9, 0xee,
    0x60,

    /* U+0068 "h" */
    0x3f, 0x30, 0x0, 0x3f, 0x30, 0x0, 0x3f, 0x30,
    0x0, 0x3f, 0x30, 0x0, 0x3f, 0x30, 0x0, 0x3f,
    0x7e, 0xb0, 0x3f, 0xb8, 0xf3, 0x3f, 0x41, 0xf5,
    0x3f, 0x30, 0xf6, 0x3f, 0x30, 0xf6, 0x3f, 0x30,
    0xf6, 0x3f, 0x30, 0xf6, 0x3f, 0x30, 0xf6, 0x3f,
    0x30, 0xf6, 0x3f, 0x30, 0xf6, 0x3f, 0x30, 0xf6,
    0x3f, 0x30, 0xf6, 0x3f, 0x30, 0xf6, 0x3f, 0x30,
    0xf6,

    /* U+0069 "i" */
    0x5f, 0x56, 0xf7, 0x1, 0x0, 0x0, 0x3f, 0x33,
    0xf3, 0x3f, 0x33, 0xf3, 0x3f, 0x33, 0xf3, 0x3f,
    0x33, 0xf3, 0x3f, 0x33, 0xf3, 0x3f, 0x33, 0xf3,
    0x3f, 0x33, 0xf3,

    /* U+006A "j" */
    0x5, 0xf5, 0x6, 0xf7, 0x0, 0x10, 0x0, 0x0,
    0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3,
    0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3,
    0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3,
    0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3, 0x3, 0xf3,
    0x3, 0xf2, 0x4b, 0xf0, 0xbe, 0x70,

    /* U+006B "k" */
    0x3f, 0x30, 0x0, 0x3, 0xf3, 0x0, 0x0, 0x3f,
    0x30, 0x0, 0x3, 0xf3, 0x0, 0x0, 0x3f, 0x30,
    0x0, 0x3, 0xf3, 0x7, 0xf0, 0x3f, 0x30, 0xda,
    0x3, 0xf3, 0x3f, 0x50, 0x3f, 0x38, 0xf0, 0x3,
    0xf3, 0xea, 0x0, 0x3f, 0x7f, 0x40, 0x3, 0xfd,
    0xe0, 0x0, 0x3f, 0xbf, 0x10, 0x3, 0xf6, 0xf6,
    0x0, 0x3f, 0x3d, 0xb0, 0x3, 0xf3, 0x7f, 0x10,
    0x3f, 0x32, 0xf5, 0x3, 0xf3, 0xc, 0xb0, 0x3f,
    0x30, 0x7f, 0x0,

    /* U+006C "l" */
    0x2f, 0x42, 0xf4, 0x2f, 0x42, 0xf4, 0x2f, 0x42,
    0xf4, 0x2f, 0x42, 0xf4, 0x2f, 0x42, 0xf4, 0x2f,
    0x42, 0xf4, 0x2f, 0x42, 0xf4, 0x2f, 0x42, 0xf4,
    0x2f, 0x42, 0xf4, 0x2f, 0x40,

    /* U+006D "m" */
    0x2f, 0x6e, 0xc3, 0xed, 0x12, 0xfa, 0x5f, 0xb3,
    0xf6, 0x2f, 0x50, 0xf7, 0xd, 0x82, 0xf4, 0xf,
    0x60, 0xd9, 0x2f, 0x40, 0xf6, 0xd, 0x92, 0xf4,
    0xf, 0x60, 0xd9, 0x2f, 0x40, 0xf6, 0xd, 0x92,
    0xf4, 0xf, 0x60, 0xd9, 0x2f, 0x40, 0xf6, 0xd,
    0x92, 0xf4, 0xf, 0x60, 0xd9, 0x2f, 0x40, 0xf6,
    0xd, 0x92, 0xf4, 0xf, 0x60, 0xd9, 0x2f, 0x40,
    0xf6, 0xd, 0x92, 0xf4, 0xf, 0x60, 0xd9,

    /* U+006E "n" */
    0x2f, 0x6e, 0xb0, 0x2f, 0xa5, 0xf4, 0x2f, 0x50,
    0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f,
    0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6,
    0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40,
    0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f,
    0x40, 0xf6,

    /* U+006F "o" */
    0x6, 0xee, 0x80, 0x1f, 0xa9, 0xf2, 0x3f, 0x31,
    0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f,
    0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5,
    0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31,
    0xf5, 0x3f, 0x31, 0xf5, 0x1f, 0xa9, 0xf2, 0x6,
    0xee, 0x70,

    /* U+0070 "p" */
    0x2f, 0x6e, 0xb0, 0x2f, 0xa5, 0xf4, 0x2f, 0x50,
    0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f,
    0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6,
    0x2f, 0x40, 0xf6, 0x2f, 0x40, 0xf6, 0x2f, 0x40,
    0xf6, 0x2f, 0x50, 0xf6, 0x2f, 0xb8, 0xf4, 0x2f,
    0x9e, 0xb0, 0x2f, 0x40, 0x0, 0x2f, 0x40, 0x0,
    0x2f, 0x40, 0x0, 0x2f, 0x40, 0x0, 0x2f, 0x40,
    0x0,

    /* U+0071 "q" */
    0x9, 0xf6, 0xd5, 0x1f, 0xa9, 0xf5, 0x3f, 0x32,
    0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f,
    0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5,
    0x4f, 0x31, 0xf5, 0x4f, 0x31, 0xf5, 0x4f, 0x31,
    0xf5, 0x3f, 0x32, 0xf5, 0x1f, 0xaa, 0xf5, 0x9,
    0xf8, 0xf5, 0x0, 0x1, 0xf5, 0x0, 0x1, 0xf5,
    0x0, 0x1, 0xf5, 0x0, 0x1, 0xf5, 0x0, 0x1,
    0xf5,

    /* U+0072 "r" */
    0x2f, 0x6f, 0x72, 0xfc, 0x81, 0x2f, 0x50, 0x2,
    0xf4, 0x0, 0x2f, 0x40, 0x2, 0xf4, 0x0, 0x2f,
    0x40, 0x2, 0xf4, 0x0, 0x2f, 0x40, 0x2, 0xf4,
    0x0, 0x2f, 0x40, 0x2, 0xf4, 0x0, 0x2f, 0x40,
    0x2, 0xf4, 0x0,

    /* U+0073 "s" */
    0x9, 0xfe, 0x70, 0x4f, 0x89, 0xf2, 0x6f, 0x1,
    0xf5, 0x6f, 0x1, 0xf5, 0x5f, 0x20, 0xc4, 0x1f,
    0x90, 0x0, 0x8, 0xf6, 0x0, 0x0, 0xaf, 0x60,
    0x0, 0xc, 0xf0, 0x5a, 0x4, 0xf3, 0x7f, 0x1,
    0xf5, 0x6f, 0x1, 0xf5, 0x4f, 0x7a, 0xf2, 0x9,
    0xee, 0x70,

    /* U+0074 "t" */
    0x6, 0x40, 0x0, 0xd9, 0x0, 0xd, 0x90, 0x0,
    0xd9, 0x0, 0xef, 0xff, 0x24, 0xeb, 0x50, 0xd,
    0x90, 0x0, 0xd9, 0x0, 0xd, 0x90, 0x0, 0xd9,
    0x0, 0xd, 0x90, 0x0, 0xd9, 0x0, 0xd, 0x90,
    0x0, 0xd9, 0x0, 0xd, 0x90, 0x0, 0xca, 0x0,
    0xa, 0xe5, 0x10, 0x3d, 0xf2,

    /* U+0075 "u" */
    0x3f, 0x31, 0xf6, 0x3f, 0x31, 0xf6, 0x3f, 0x31,
    0xf6, 0x3f, 0x31, 0xf6, 0x3f, 0x31, 0xf6, 0x3f,
    0x31, 0xf6, 0x3f, 0x31, 0xf6, 0x3f, 0x31, 0xf6,
    0x3f, 0x31, 0xf6, 0x3f, 0x31, 0xf6, 0x3f, 0x31,
    0xf6, 0x2f, 0x32, 0xf6, 0x1f, 0xa9, 0xf6, 0x9,
    0xf7, 0xd6,

    /* U+0076 "v" */
    0xcc, 0x2, 0xf6, 0x9d, 0x3, 0xf4, 0x7f, 0x5,
    0xf2, 0x5f, 0x6, 0xf0, 0x3f, 0x28, 0xd0, 0x1f,
    0x39, 0xb0, 0xf, 0x5b, 0x90, 0xd, 0x6c, 0x70,
    0xb, 0x8e, 0x50, 0x8, 0x9f, 0x30, 0x6, 0xcf,
    0x10, 0x4, 0xff, 0x0, 0x2, 0xfd, 0x0, 0x0,
    0xfb, 0x0,

    /* U+0077 "w" */
    0xcc, 0x2, 0xf9, 0x5, 0xf2, 0x9d, 0x3, 0xfa,
    0x7, 0xf0, 0x7f, 0x5, 0xfc, 0x8, 0xe0, 0x5f,
    0x16, 0xfd, 0xa, 0xc0, 0x3f, 0x28, 0xdf, 0xb,
    0xa0, 0x1f, 0x4a, 0x9f, 0x1d, 0x80, 0xf, 0x5b,
    0x6f, 0x2e, 0x60, 0xd, 0x7d, 0x4e, 0x4f, 0x30,
    0xb, 0x8e, 0x3c, 0x7f, 0x10, 0x8, 0xbf, 0x1a,
    0xbf, 0x0, 0x6, 0xef, 0x9, 0xed, 0x0, 0x4,
    0xfe, 0x7, 0xfb, 0x0, 0x2, 0xfc, 0x5, 0xf9,
    0x0, 0x0, 0xfa, 0x3, 0xf7, 0x0,

    /* U+0078 "x" */
    0xbb, 0x0, 0xf7, 0x6f, 0x3, 0xf2, 0x2f, 0x48,
    0xd0, 0xd, 0x8c, 0x80, 0x8, 0xdf, 0x30, 0x4,
    0xfe, 0x0, 0x0, 0xfa, 0x0, 0x0, 0xfb, 0x0,
    0x5, 0xff, 0x0, 0x9, 0xaf, 0x50, 0xe, 0x6b,
    0x90, 0x2f, 0x27, 0xe0, 0x7e, 0x2, 0xf3, 0xba,
    0x0, 0xe8,

    /* U+0079 "y" */
    0xad, 0x0, 0xdb, 0x8f, 0x0, 0xf8, 0x6f, 0x10,
    0xf6, 0x4f, 0x22, 0xf4, 0x2f, 0x44, 0xf1, 0xf,
    0x55, 0xf0, 0xe, 0x77, 0xd0, 0xc, 0x99, 0xa0,
    0xa, 0xaa, 0x80, 0x8, 0xcc, 0x50, 0x5, 0xde,
    0x30, 0x3, 0xff, 0x10, 0x1, 0xfe, 0x0, 0x0,
    0xfc, 0x0, 0x0, 0xda, 0x0, 0x0, 0xe7, 0x0,
    0x1, 0xf5, 0x0, 0x4, 0xf3, 0x0, 0x7, 0xf0,
    0x0,

    /* U+007A "z" */
    0x5f, 0xff, 0xf5, 0x15, 0x5a, 0xf2, 0x0, 0xb,
    0xd0, 0x0, 0xf, 0x90, 0x0, 0x5f, 0x40, 0x0,
    0x9f, 0x0, 0x0, 0xeb, 0x0, 0x3, 0xf6, 0x0,
    0x8, 0xf1, 0x0, 0xd, 0xd0, 0x0, 0x1f, 0x80,
    0x0, 0x6f, 0x30, 0x0, 0xbf, 0x55, 0x50, 0xdf,
    0xff, 0xf0,

    /* U+007B "{" */
    0x0, 0x3e, 0xf3, 0x0, 0xbc, 0x40, 0x0, 0xe8,
    0x0, 0x0, 0xe7, 0x0, 0x0, 0xe7, 0x0, 0x0,
    0xe7, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xe7, 0x0,
    0x0, 0xf7, 0x0, 0x2, 0xf5, 0x0, 0x1b, 0xe0,
    0x0, 0xef, 0x50, 0x0, 0x6e, 0xd0, 0x0, 0x3,
    0xf4, 0x0, 0x0, 0xf6, 0x0, 0x0, 0xe7, 0x0,
    0x0, 0xe7, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xe7,
    0x0, 0x0, 0xe7, 0x0, 0x0, 0xe7, 0x0, 0x0,
    0xcb, 0x10, 0x0, 0x6f, 0xf3, 0x0, 0x2, 0x40,

    /* U+007C "|" */
    0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8,
    0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8,
    0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8, 0xf8,
    0x74,

    /* U+007D "}" */
    0xbf, 0xa0, 0x0, 0x37, 0xf4, 0x0, 0x0, 0xf6,
    0x0, 0x0, 0xf7, 0x0, 0x0, 0xf7, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0xf7, 0x0, 0x0, 0xf7, 0x0,
    0x0, 0xe7, 0x0, 0x0, 0xca, 0x0, 0x0, 0x7f,
    0x50, 0x0, 0xc, 0xf7, 0x0, 0x5f, 0xa2, 0x0,
    0xbb, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xf7, 0x0,
    0x0, 0xf7, 0x0, 0x0, 0xf7, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0xf7, 0x0, 0x0, 0xf6, 0x0, 0x3,
    0xf5, 0x0, 0xbf, 0xd0, 0x0, 0x23, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfc,
    0x9b, 0xe1, 0x1c, 0x75, 0x8c, 0xfd, 0x50,

    /* U+3002 "。" */
    0x0, 0x0, 0x0, 0x9, 0xbc, 0x80, 0x78, 0x0,
    0xa4, 0xa2, 0x0, 0x57, 0x67, 0x0, 0xa4, 0x9,
    0xbc, 0x80, 0x0, 0x0, 0x0,

    /* U+4E09 "三" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x0, 0x9, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4a, 0x99,
    0x99, 0x99, 0x99, 0x99, 0xbf, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0x0, 0x7, 0xa9, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x9a, 0xee, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+4E2D "中" */
    0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x10, 0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0xc,
    0x70, 0xec, 0x99, 0x99, 0x99, 0xfb, 0x99, 0x99,
    0x99, 0xfb, 0xd, 0x70, 0x0, 0x0, 0xf, 0x60,
    0x0, 0x0, 0xe, 0x60, 0xd7, 0x0, 0x0, 0x0,
    0xf6, 0x0, 0x0, 0x0, 0xe6, 0xd, 0x70, 0x0,
    0x0, 0xf, 0x60, 0x0, 0x0, 0xe, 0x60, 0xd7,
    0x0, 0x0, 0x0, 0xf6, 0x0, 0x0, 0x0, 0xe6,
    0xd, 0x70, 0x0, 0x0, 0xf, 0x60, 0x0, 0x0,
    0xe, 0x60, 0xdc, 0x99, 0x99, 0x99, 0xfb, 0x99,
    0x99, 0x99, 0xf6, 0xd, 0x70, 0x0, 0x0, 0xf,
    0x60, 0x0, 0x0, 0xe, 0x70, 0x91, 0x0, 0x0,
    0x0, 0xf6, 0x0, 0x0, 0x0, 0x71, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+4EAE "亮" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x65, 0x0, 0x0, 0x0, 0xad, 0x10, 0x0, 0xaa,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x96, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdc, 0x99, 0x99, 0x99, 0x99, 0xec, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0x0,
    0xd, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd7,
    0x0, 0x0, 0x0, 0x0, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xc9, 0x99, 0x99, 0x99, 0x9e,
    0x60, 0x0, 0x0, 0x0, 0x1, 0x0, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x1,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x30, 0x0, 0x6d, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x89, 0xfe, 0x10, 0x2f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89,
    0x10, 0xa, 0xf2, 0x0, 0x7, 0x30, 0x0, 0x0,
    0x84, 0x0, 0x16, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0x88, 0x88, 0x8f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0xe6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc9,
    0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x40, 0x0,
    0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x9, 0x0, 0x0, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0xe, 0x60, 0x0, 0x0, 0xc0, 0x0, 0x0,
    0x6, 0xe3, 0x0, 0x0, 0x0, 0xea, 0x10, 0x0,
    0x3f, 0x80, 0x1, 0x6a, 0x81, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xd4, 0x4, 0x62, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+4ECA "今" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xed, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x36, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0x80, 0xb, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xc0, 0x0,
    0x2d, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xe2, 0x0, 0x0, 0x5e, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf3, 0x10, 0x0, 0x0,
    0x7f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf4,
    0x1, 0xc5, 0x0, 0x0, 0x7f, 0xc3, 0x0, 0x0,
    0x0, 0x6, 0xe3, 0x0, 0x2, 0xfa, 0x0, 0x0,
    0x4e, 0xfc, 0x62, 0x0, 0x8, 0xa1, 0x0, 0x0,
    0x6, 0xf5, 0x0, 0x0, 0x1a, 0xfe, 0x80, 0x1a,
    0x50, 0x0, 0x0, 0x0, 0xc, 0x20, 0x0, 0x0,
    0x4, 0x20, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xad, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+4F53 "体" */
    0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xd1,
    0x0, 0x0, 0xf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xf7, 0x0, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x10, 0x0,
    0x0, 0xf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x80, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf1, 0x0, 0x0, 0x0,
    0xf, 0x60, 0x0, 0x1c, 0x80, 0x0, 0x0, 0xb9,
    0x0, 0xaa, 0x99, 0xaf, 0xfc, 0xa9, 0x99, 0x99,
    0x20, 0x0, 0x2f, 0xe5, 0x0, 0x0, 0x8, 0xef,
    0x67, 0x0, 0x0, 0x0, 0x0, 0xa, 0x9f, 0x30,
    0x0, 0x0, 0xe8, 0xf6, 0x90, 0x0, 0x0, 0x0,
    0x3, 0xb2, 0xf1, 0x0, 0x0, 0x5f, 0x1f, 0x65,
    0x60, 0x0, 0x0, 0x0, 0xb1, 0x2f, 0x10, 0x0,
    0xd, 0x80, 0xf6, 0xc, 0x0, 0x0, 0x0, 0x72,
    0x2, 0xf1, 0x0, 0x6, 0xe0, 0xf, 0x60, 0x87,
    0x0, 0x0, 0x3, 0x0, 0x2f, 0x10, 0x0, 0xe5,
    0x0, 0xf6, 0x1, 0xf2, 0x0, 0x0, 0x0, 0x2,
    0xf1, 0x0, 0x8a, 0x0, 0xf, 0x60, 0x9, 0xd1,
    0x0, 0x0, 0x0, 0x2f, 0x10, 0x3c, 0x0, 0x0,
    0xf6, 0x0, 0x1f, 0xd1, 0x0, 0x0, 0x2, 0xf1,
    0x1b, 0x10, 0x0, 0xf, 0x60, 0x18, 0x6f, 0xe5,
    0x0, 0x0, 0x2f, 0x29, 0x12, 0xaa, 0x99, 0xfb,
    0x9c, 0xe8, 0x9a, 0x40, 0x0, 0x2, 0xf4, 0x0,
    0x1, 0x0, 0xf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x10, 0x0, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xf1, 0x0, 0x0,
    0x0, 0xf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x10, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0x0,
    0xe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5149 "光" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xda, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x70, 0x0, 0x0, 0xd7, 0x0,
    0x0, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6c, 0x10,
    0x0, 0xd7, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xd1, 0x0, 0xd7, 0x0, 0xc, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xfa, 0x0, 0xd7,
    0x0, 0x3f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0xd7, 0x0, 0xb8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x46, 0x0, 0xd7, 0x4, 0xb0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd7, 0xa, 0x0, 0x0, 0x4f, 0x60, 0x4c, 0x99,
    0x99, 0x9d, 0xd9, 0x99, 0xeb, 0x99, 0x99, 0x99,
    0x81, 0x0, 0x0, 0x0, 0xb, 0xa0, 0x0, 0xd6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x80, 0x0, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x60, 0x0, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x30, 0x0,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0xd6, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0xab, 0x0, 0x0, 0xd6, 0x0,
    0x0, 0x0, 0x80, 0x0, 0x0, 0x1, 0xf4, 0x0,
    0x0, 0xd6, 0x0, 0x0, 0x0, 0x90, 0x0, 0x0,
    0xa, 0xc0, 0x0, 0x0, 0xd6, 0x0, 0x0, 0x1,
    0xa0, 0x0, 0x0, 0x7d, 0x10, 0x0, 0x0, 0xd6,
    0x0, 0x0, 0x3, 0xe0, 0x0, 0x8, 0xb1, 0x0,
    0x0, 0x0, 0xac, 0x32, 0x22, 0x29, 0xfa, 0x3,
    0xb5, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xff, 0xc2, 0x75, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5747 "均" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x2, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0x0, 0x6, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf6, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0, 0x2, 0x40, 0x0,
    0x0, 0xf6, 0x0, 0x0, 0x6f, 0x99, 0x99, 0x99,
    0x9d, 0xf2, 0x0, 0x0, 0xf6, 0x3, 0x0, 0xd6,
    0x0, 0x0, 0x0, 0xb, 0xa0, 0x49, 0x99, 0xfb,
    0xbf, 0x64, 0xc0, 0x0, 0x0, 0x0, 0xb, 0x90,
    0x1, 0x0, 0xf6, 0x0, 0xc, 0x20, 0x91, 0x0,
    0x0, 0xb, 0x90, 0x0, 0x0, 0xf6, 0x0, 0x65,
    0x0, 0x5e, 0x40, 0x0, 0xc, 0x90, 0x0, 0x0,
    0xf6, 0x0, 0x50, 0x0, 0xc, 0xf2, 0x0, 0xc,
    0x80, 0x0, 0x0, 0xf6, 0x0, 0x0, 0x0, 0x4,
    0xf6, 0x0, 0xd, 0x80, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0xa1, 0x0, 0xe, 0x70, 0x0,
    0x0, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1, 0x84,
    0xe, 0x70, 0x0, 0x0, 0xf6, 0x1, 0x74, 0x0,
    0x0, 0x7a, 0x10, 0xf, 0x60, 0x0, 0x0, 0xfa,
    0xba, 0x20, 0x0, 0x7d, 0x60, 0x0, 0xf, 0x50,
    0x1, 0x6b, 0xfa, 0x20, 0x2, 0x9f, 0xb1, 0x0,
    0x0, 0x2f, 0x40, 0xaf, 0xfa, 0x20, 0x0, 0x3f,
    0xf6, 0x0, 0x0, 0x0, 0x4f, 0x30, 0x1d, 0x40,
    0x0, 0x0, 0x7, 0x40, 0x0, 0x0, 0x0, 0x8f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x53, 0x23, 0xeb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbc,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5927 "大" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0, 0x0,
    0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf6,
    0x0, 0x0, 0x0, 0x3e, 0xc1, 0x1a, 0x99, 0x99,
    0x99, 0x99, 0xfb, 0x99, 0x99, 0x99, 0x99, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xf8, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xd2, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x90, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x40, 0x4a, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0x0,
    0xc, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe7, 0x0, 0x3, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x8c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x30,
    0x0, 0x0, 0xc, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xe4, 0x0, 0x0, 0x0, 0x1, 0xde, 0x60,
    0x0, 0x0, 0x0, 0x7d, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0xfd, 0x61, 0x0, 0x2b, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xd6, 0x28,
    0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5929 "天" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3a, 0x0, 0x0, 0x0, 0x18, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0xff, 0xb0, 0x0, 0x0, 0x2,
    0x10, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xd0, 0x0, 0x0, 0x0, 0xc8, 0x0,
    0x29, 0x99, 0x99, 0x99, 0x9d, 0xe9, 0x99, 0x99,
    0x9c, 0xff, 0x80, 0x3, 0x10, 0x0, 0x0, 0xd,
    0x95, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0x51, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x10, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbb, 0x0, 0x67, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf4, 0x0, 0xd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xb0, 0x0,
    0x6, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0xc9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xe5, 0x0, 0x0, 0x0, 0x3f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0x50, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x40, 0x0, 0x0, 0x3,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfc,
    0x62, 0x0, 0x7a, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xdf, 0xb4, 0x38, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5E73 "平" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xc1, 0x0, 0x0,
    0x9, 0xa9, 0x99, 0x99, 0x9b, 0xa9, 0x99, 0x99,
    0xcd, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd9, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x24, 0x0, 0x0, 0xd, 0x90, 0x0, 0x9, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0x0, 0x0, 0xd9,
    0x0, 0x0, 0xed, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xfa, 0x0, 0xd, 0x90, 0x0, 0x8d, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0xd9, 0x0,
    0x2d, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x70, 0xd, 0x90, 0x9, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x91, 0x0, 0xd9, 0x4, 0x20,
    0x0, 0x8, 0x30, 0x7, 0x88, 0x88, 0x88, 0x88,
    0x8e, 0xc8, 0x88, 0x88, 0x8a, 0xff, 0x40, 0x22,
    0x11, 0x11, 0x11, 0x11, 0xd9, 0x11, 0x11, 0x11,
    0x11, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xda, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x10, 0x0, 0x0, 0x0, 0xad, 0x0, 0x0,
    0x0, 0x91, 0x0, 0x0, 0x4e, 0x99, 0x99, 0x99,
    0x9b, 0xc9, 0x99, 0x99, 0xcf, 0xd1, 0x0, 0x3,
    0xf2, 0x0, 0x3, 0x30, 0x0, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0x20, 0x0, 0x5f, 0x40,
    0x0, 0xf, 0xb0, 0x0, 0x0, 0x0, 0x3, 0xf2,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0xe5, 0x1, 0x70,
    0x0, 0x0, 0x3f, 0x59, 0x99, 0xbf, 0x99, 0x99,
    0x9f, 0xb9, 0xdf, 0x90, 0x0, 0x3, 0xf2, 0x20,
    0x4, 0xe0, 0x0, 0x0, 0xe5, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x10, 0x0, 0x5e, 0x0, 0x0, 0xe,
    0x50, 0x0, 0x0, 0x0, 0x5, 0xf0, 0x0, 0x5,
    0xe0, 0x0, 0x0, 0xe5, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0x5f, 0x99, 0x99, 0x9f, 0x50,
    0x0, 0x0, 0x0, 0x7, 0xd0, 0x0, 0x2, 0x20,
    0x0, 0x0, 0x22, 0x0, 0x0, 0x0, 0x0, 0x9b,
    0x0, 0x59, 0x99, 0x99, 0x99, 0x99, 0xfb, 0x0,
    0x0, 0x0, 0xa, 0x80, 0x0, 0x0, 0x70, 0x0,
    0x0, 0x9f, 0x40, 0x0, 0x0, 0x0, 0xc5, 0x0,
    0x0, 0x4, 0x70, 0x0, 0x5f, 0x50, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x8, 0x60, 0x3f,
    0x80, 0x0, 0x0, 0x0, 0x3, 0xb0, 0x0, 0x0,
    0x0, 0xb, 0x9f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x85, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x0, 0x0, 0x0, 0x4,
    0xcc, 0x5a, 0xfb, 0x51, 0x0, 0x0, 0x5, 0x50,
    0x0, 0x0, 0x5c, 0xb4, 0x0, 0x3, 0xbf, 0xfd,
    0xb9, 0x40, 0x70, 0x2, 0x69, 0x96, 0x10, 0x0,
    0x0, 0x0, 0x27, 0xcf, 0x40, 0x1, 0x0, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5F3A "强" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0x0,
    0x82, 0x11, 0x11, 0x13, 0xc1, 0x0, 0x49, 0x99,
    0x99, 0xfa, 0x0, 0xfa, 0x77, 0x77, 0x79, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xe5, 0x0, 0xe6, 0x0,
    0x0, 0x3, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xe5,
    0x0, 0xe6, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xe5, 0x0, 0xe8, 0x33, 0x33, 0x35,
    0xf0, 0x0, 0x1, 0x0, 0x0, 0xe5, 0x0, 0xe9,
    0x55, 0xc6, 0x57, 0xf1, 0x0, 0x5, 0xd9, 0x99,
    0xf5, 0x0, 0x71, 0x0, 0xfa, 0x0, 0x10, 0x0,
    0x6, 0xd0, 0x0, 0xc3, 0x0, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x4,
    0x0, 0x0, 0xf4, 0x0, 0x6, 0x0, 0x9, 0xa0,
    0x0, 0x0, 0xd, 0xb9, 0x99, 0xfa, 0x99, 0xaf,
    0x60, 0xc, 0x80, 0x0, 0x40, 0xd, 0x60, 0x0,
    0xf4, 0x0, 0x2f, 0x10, 0x1f, 0xc9, 0x99, 0xf8,
    0xd, 0x60, 0x0, 0xf4, 0x0, 0x2f, 0x10, 0x5,
    0x10, 0x2, 0xf2, 0xd, 0x60, 0x0, 0xf4, 0x0,
    0x2f, 0x10, 0x0, 0x0, 0x4, 0xf0, 0xd, 0xb9,
    0x99, 0xfa, 0x99, 0xaf, 0x10, 0x0, 0x0, 0x6,
    0xe0, 0xe, 0x50, 0x0, 0xf4, 0x0, 0x29, 0x0,
    0x0, 0x0, 0x8, 0xb0, 0x3, 0x0, 0x0, 0xf4,
    0x1, 0x40, 0x0, 0x0, 0x0, 0xb, 0x80, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x88, 0x0, 0x0, 0x0,
    0xf, 0x50, 0x0, 0x0, 0x0, 0xf4, 0x0, 0xd,
    0x90, 0x4, 0x52, 0x8f, 0x10, 0x1, 0x34, 0x57,
    0xfb, 0xbb, 0xbb, 0xf4, 0x0, 0x7f, 0xf7, 0x6,
    0xff, 0xfd, 0xa8, 0x63, 0x10, 0x0, 0xe6, 0x0,
    0xa, 0x60, 0x0, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x50,

    /* U+6587 "文" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xda, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0x0, 0x0, 0x0, 0x1c, 0x30, 0x8,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0xdf, 0xf3, 0x2, 0x10, 0x0, 0x80, 0x0, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x54, 0x0, 0x0, 0x0, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x19, 0x0, 0x0, 0x3, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0x0,
    0x0, 0x8, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x50, 0x0, 0xd, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x3f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0x0, 0xac, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4d, 0x2, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x8b,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0x36, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xb1, 0x0, 0x5e, 0xf8, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd5, 0x0, 0x0, 0x1,
    0xaf, 0xfc, 0x85, 0x20, 0x0, 0x6, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xb3, 0x47,
    0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x67, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+65E5 "日" */
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xe,
    0xb9, 0x99, 0x99, 0x99, 0x99, 0x9b, 0xf5, 0xe7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xe, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf0, 0xe7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xd, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xf0, 0xd7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xd, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xf0, 0xd7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xd, 0xc9, 0x99, 0x99, 0x99,
    0x99, 0x9b, 0xf0, 0xd7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xd, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf0, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xd, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf0, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xd, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf0,
    0xe7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xe,
    0xc9, 0x99, 0x99, 0x99, 0x99, 0x9b, 0xf0, 0xe7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0,

    /* U+65F6 "时" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x40, 0x0, 0x7,
    0x10, 0x0, 0x94, 0x0, 0x0, 0x0, 0x2, 0xf3,
    0x0, 0x0, 0xec, 0x99, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0x2f, 0x30, 0x0, 0xe, 0x70, 0x0, 0xe6,
    0x0, 0x0, 0x0, 0x2, 0xf3, 0x5, 0x20, 0xd7,
    0x0, 0xe, 0x78, 0x99, 0x99, 0x99, 0x9f, 0xaa,
    0xfe, 0x2d, 0x70, 0x0, 0xe6, 0x21, 0x0, 0x0,
    0x2, 0xf3, 0x0, 0x0, 0xd7, 0x0, 0xe, 0x60,
    0x0, 0x0, 0x0, 0x2f, 0x30, 0x0, 0xd, 0x70,
    0x0, 0xe6, 0x3, 0x20, 0x0, 0x2, 0xf3, 0x0,
    0x0, 0xdc, 0x99, 0x9f, 0x60, 0xd, 0x40, 0x0,
    0x2f, 0x30, 0x0, 0xd, 0x70, 0x0, 0xe6, 0x0,
    0x5f, 0x50, 0x2, 0xf3, 0x0, 0x0, 0xd7, 0x0,
    0xe, 0x60, 0x0, 0xdf, 0x0, 0x2f, 0x30, 0x0,
    0xd, 0x70, 0x0, 0xe6, 0x0, 0x8, 0xd0, 0x2,
    0xf3, 0x0, 0x0, 0xd7, 0x0, 0xe, 0x60, 0x0,
    0x12, 0x0, 0x2f, 0x30, 0x0, 0xd, 0x70, 0x0,
    0xe6, 0x0, 0x0, 0x0, 0x2, 0xf3, 0x0, 0x0,
    0xdc, 0x99, 0x9f, 0x70, 0x0, 0x0, 0x0, 0x2f,
    0x30, 0x0, 0xe, 0x70, 0x0, 0xe7, 0x0, 0x0,
    0x0, 0x2, 0xf3, 0x0, 0x0, 0xe7, 0x0, 0x9,
    0x20, 0x0, 0x0, 0x0, 0x2f, 0x30, 0x0, 0x6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x75, 0x48, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5d, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0x20, 0x0, 0x0,

    /* U+6700 "最" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x14, 0x0, 0x0, 0x0, 0x0, 0x2e, 0x99, 0x99,
    0x99, 0x99, 0x99, 0xcf, 0x40, 0x0, 0x0, 0x0,
    0x1f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xa9,
    0x99, 0x99, 0x99, 0x99, 0xbe, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0x98, 0x88, 0x88,
    0x88, 0x88, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x7c, 0x2, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x1e, 0x80, 0x3b, 0x9a, 0xc9, 0x99, 0x9c,
    0xa9, 0x99, 0x99, 0x99, 0xac, 0xc3, 0x0, 0x6,
    0xe0, 0x0, 0xe, 0x50, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x6, 0xe0, 0x0, 0xe, 0x55, 0x55,
    0x55, 0x55, 0xac, 0x0, 0x0, 0x6, 0xf8, 0x88,
    0x8f, 0x54, 0x59, 0x44, 0x44, 0xda, 0x0, 0x0,
    0x6, 0xe0, 0x0, 0xe, 0x50, 0xa, 0x0, 0x3,
    0xf1, 0x0, 0x0, 0x6, 0xf9, 0x99, 0x9f, 0x50,
    0x7, 0x50, 0xa, 0x90, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0xe, 0x50, 0x1, 0xd0, 0x4f, 0x10, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0xe, 0x64, 0x62, 0x8b,
    0xd7, 0x0, 0x0, 0x0, 0x6, 0xe1, 0x47, 0xaf,
    0xa4, 0x0, 0xe, 0xe0, 0x0, 0x0, 0x16, 0x8c,
    0xff, 0xb6, 0x2e, 0x50, 0x0, 0x8d, 0xea, 0x0,
    0x0, 0x1e, 0xe8, 0x30, 0x0, 0xe, 0x50, 0x9,
    0xa0, 0x2e, 0xd4, 0x0, 0x3, 0x0, 0x0, 0x0,
    0xe, 0x51, 0xa6, 0x0, 0x1, 0xcf, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x78, 0x10, 0x0, 0x0,
    0x7, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6708 "月" */
    0x0, 0x0, 0x5, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x93, 0x0, 0x0, 0x0, 0x7f, 0x99, 0x99, 0x99,
    0x99, 0x9f, 0xd0, 0x0, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0,
    0x7, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf5, 0x0,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x50, 0x0, 0x0, 0x6, 0xf9, 0x99, 0x99, 0x99,
    0x99, 0xf5, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0,
    0x7e, 0x0, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0x7, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xf5,
    0x0, 0x0, 0x0, 0x8e, 0x99, 0x99, 0x99, 0x99,
    0x9f, 0x50, 0x0, 0x0, 0xa, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0xca, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0xf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x4, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xf, 0x50,
    0x0, 0x0, 0xaa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0x3f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x50, 0x0, 0xc, 0x40, 0x0, 0x0,
    0x1, 0x53, 0x14, 0xf5, 0x0, 0xa, 0x40, 0x0,
    0x0, 0x0, 0x4, 0xaf, 0xff, 0x20, 0x8, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0x40, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6A19 "標" */
    0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0x0,
    0x0, 0x0, 0x2f, 0x0, 0x1b, 0xa9, 0x9f, 0x99,
    0xae, 0x99, 0x99, 0x20, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0x0, 0xe, 0x10, 0x3d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0xe, 0x10,
    0x3d, 0x0, 0x20, 0x0, 0x0, 0x0, 0x2f, 0x6,
    0xb1, 0xe8, 0x8f, 0x98, 0x9e, 0x88, 0xf5, 0x0,
    0x8, 0x88, 0xbf, 0x88, 0x84, 0xf3, 0x1e, 0x21,
    0x3d, 0x14, 0xf1, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0xf2, 0xe, 0x10, 0x3d, 0x3, 0xe0, 0x0,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0xf2, 0xe, 0x10,
    0x3d, 0x3, 0xf0, 0x0, 0x0, 0x2, 0xff, 0x8c,
    0x10, 0xfa, 0x9d, 0x99, 0x9d, 0x9a, 0xf0, 0x0,
    0x0, 0x7, 0xdf, 0xe, 0xa0, 0xe1, 0x0, 0x0,
    0x0, 0x1, 0x40, 0x0, 0x0, 0xd, 0x5f, 0x7,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x10, 0x0,
    0x0, 0x4a, 0x2f, 0x0, 0x10, 0x9a, 0x99, 0x99,
    0x99, 0x99, 0x60, 0x0, 0x0, 0xa2, 0x2f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0,
    0x2, 0x70, 0x2f, 0x0, 0x49, 0x99, 0x99, 0x99,
    0x99, 0x99, 0xaf, 0xa0, 0x7, 0x0, 0x2f, 0x0,
    0x3, 0x1, 0x0, 0x2f, 0x40, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x2f, 0x0, 0x0, 0x1e, 0x60, 0x2f,
    0x44, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0xbc, 0x20, 0x2f, 0x40, 0x6d, 0x40, 0x0,
    0x0, 0x0, 0x2f, 0x0, 0x9, 0xb0, 0x0, 0x2f,
    0x40, 0x6, 0xf6, 0x0, 0x0, 0x0, 0x2f, 0x0,
    0x89, 0x0, 0x0, 0x2f, 0x40, 0x0, 0xaf, 0x0,
    0x0, 0x0, 0x3f, 0x17, 0x40, 0x0, 0x78, 0xbf,
    0x30, 0x0, 0x1b, 0x0, 0x0, 0x0, 0x3e, 0x11,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x0,

    /* U+6C60 "池" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa7, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xeb, 0x0, 0x0, 0x0, 0x0, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf3, 0x0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x2, 0x4, 0x70, 0x0, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x70, 0x4f, 0x30,
    0xf, 0x40, 0x0, 0x30, 0x0, 0x8, 0x20, 0x0,
    0x7, 0x4, 0xf0, 0x0, 0xf4, 0x1, 0x5f, 0xb0,
    0x0, 0x3e, 0x50, 0x4, 0x40, 0x4f, 0x0, 0xf,
    0x97, 0x73, 0xe6, 0x0, 0x0, 0x8f, 0x30, 0x90,
    0x4, 0xf6, 0x77, 0xf4, 0x0, 0xe, 0x50, 0x0,
    0x1, 0xf4, 0x9, 0x68, 0x9f, 0x0, 0xf, 0x40,
    0x0, 0xe4, 0x0, 0x0, 0x3, 0x5, 0x50, 0x4,
    0xf0, 0x0, 0xf4, 0x0, 0xf, 0x40, 0x0, 0x0,
    0x0, 0xb1, 0x0, 0x4f, 0x0, 0xf, 0x40, 0x0,
    0xf3, 0x0, 0x0, 0x0, 0x1b, 0x0, 0x4, 0xf0,
    0x0, 0xf4, 0x0, 0xf, 0x30, 0x0, 0x0, 0x8,
    0x70, 0x0, 0x4f, 0x0, 0xf, 0x40, 0x1, 0xf2,
    0x0, 0x0, 0x1, 0xe2, 0x0, 0x4, 0xf0, 0x0,
    0xf5, 0x8c, 0xde, 0x0, 0x0, 0x7b, 0xed, 0x0,
    0x0, 0x4f, 0x0, 0xf, 0x40, 0x3e, 0x40, 0x40,
    0x0, 0x1e, 0xa0, 0x0, 0x4, 0xf0, 0x0, 0xf5,
    0x0, 0x0, 0x8, 0x0, 0x0, 0xc9, 0x0, 0x0,
    0x4f, 0x0, 0x8, 0x10, 0x0, 0x0, 0xb0, 0x0,
    0xe, 0x80, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x20, 0x1, 0xf9, 0x0, 0x0, 0x2f,
    0x63, 0x33, 0x33, 0x33, 0x39, 0xfa, 0x0, 0x1f,
    0x90, 0x0, 0x0, 0x9e, 0xff, 0xff, 0xff, 0xff,
    0xeb, 0x10, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+706F "灯" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x84, 0x0, 0x0, 0x0, 0xc8, 0x0, 0x19,
    0x99, 0x99, 0x99, 0x99, 0xaf, 0xe3, 0x0, 0x0,
    0xc, 0x80, 0x1, 0x20, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc8, 0x8, 0xd2, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x12, 0xc,
    0x82, 0xf8, 0x0, 0x0, 0x7, 0xe0, 0x0, 0x0,
    0x0, 0x7, 0x40, 0xb9, 0xc4, 0x0, 0x0, 0x0,
    0x7e, 0x0, 0x0, 0x0, 0x0, 0xd3, 0xb, 0xc1,
    0x0, 0x0, 0x0, 0x7, 0xe0, 0x0, 0x0, 0x0,
    0x7f, 0x10, 0xb8, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0xe, 0x90, 0xc, 0x70, 0x0,
    0x0, 0x0, 0x7, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x7e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0,
    0x0, 0x7, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x91, 0x0, 0x0, 0x0,
    0x7, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xd2,
    0xd4, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x98, 0x5, 0xf4, 0x0, 0x0, 0x7,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x10, 0xc,
    0xb0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x80, 0x0, 0x38, 0x0, 0x0, 0x7, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0xb0, 0x0, 0x0, 0x0,
    0x22, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x0, 0xa1,
    0x0, 0x0, 0x0, 0x2, 0x8f, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0,

    /* U+7167 "照" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x9, 0x1,
    0x44, 0x44, 0x44, 0x44, 0xa9, 0x0, 0x2, 0xf9,
    0x99, 0xaf, 0x70, 0x75, 0x7f, 0x64, 0x44, 0xcc,
    0x10, 0x2, 0xf2, 0x0, 0x3f, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0xd8, 0x0, 0x1, 0xf2, 0x0, 0x3f,
    0x0, 0x0, 0xb9, 0x0, 0x0, 0xf5, 0x0, 0x1,
    0xf2, 0x0, 0x3f, 0x0, 0x2, 0xf1, 0x0, 0x4,
    0xf1, 0x0, 0x1, 0xf2, 0x0, 0x3f, 0x0, 0xc,
    0x80, 0x29, 0xcf, 0xc0, 0x0, 0x1, 0xf5, 0x44,
    0x7f, 0x0, 0x7a, 0x0, 0x0, 0x9c, 0x10, 0x0,
    0x1, 0xf5, 0x44, 0x7f, 0x6, 0x93, 0x0, 0x0,
    0x0, 0x61, 0x0, 0x1, 0xf2, 0x0, 0x3f, 0x24,
    0xe, 0xb9, 0x99, 0x99, 0xfb, 0x0, 0x1, 0xf2,
    0x0, 0x3f, 0x0, 0xd, 0x70, 0x0, 0x0, 0xf5,
    0x0, 0x1, 0xf2, 0x0, 0x3f, 0x0, 0xd, 0x70,
    0x0, 0x0, 0xf5, 0x0, 0x1, 0xf2, 0x0, 0x3f,
    0x0, 0xd, 0x70, 0x0, 0x0, 0xf5, 0x0, 0x1,
    0xf9, 0x99, 0xaf, 0x0, 0xd, 0xc9, 0x99, 0x99,
    0xf6, 0x0, 0x2, 0xf2, 0x0, 0x3f, 0x10, 0xc,
    0x40, 0x0, 0x0, 0xa3, 0x0, 0x2, 0xb0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x0, 0x24, 0x0, 0x0, 0x70, 0x0,
    0x7, 0x10, 0x0, 0x0, 0x2a, 0x0, 0xd, 0x30,
    0x0, 0x8a, 0x0, 0x2, 0xe4, 0x0, 0x0, 0xaa,
    0x0, 0x7, 0xe1, 0x0, 0x1f, 0x80, 0x0, 0x8f,
    0x50, 0x5, 0xf7, 0x0, 0x3, 0xf6, 0x0, 0xc,
    0xe0, 0x0, 0x1f, 0xe0, 0x2f, 0xf2, 0x0, 0x0,
    0xf3, 0x0, 0x7, 0xa0, 0x0, 0xa, 0xd0, 0x5,
    0x20, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7259 "牙" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x8a,
    0x99, 0x99, 0x99, 0x99, 0x9b, 0x99, 0x99, 0xdd,
    0x80, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x70,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x30, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xda, 0x0, 0x0, 0x0,
    0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf2,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0xa, 0x50,
    0x0, 0x2f, 0xe9, 0x99, 0x99, 0x99, 0xbf, 0x99,
    0x99, 0xcf, 0xf5, 0x0, 0x6, 0x40, 0x0, 0x0,
    0x1e, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x8e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf5, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xa0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc1, 0x0,
    0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xad, 0x10, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xb1, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xc7, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c,
    0x30, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x60, 0x0, 0x0, 0x37, 0x65, 0xcd,
    0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x2, 0xaf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7535 "电" */
    0x0, 0x0, 0x0, 0x2, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x0, 0x0, 0x3, 0xf1, 0x0, 0x0, 0x4,
    0x0, 0x0, 0x3f, 0x99, 0x99, 0x9a, 0xf9, 0x99,
    0x99, 0x9f, 0xc0, 0x0, 0x3f, 0x20, 0x0, 0x3,
    0xf1, 0x0, 0x0, 0x1f, 0x50, 0x0, 0x2f, 0x20,
    0x0, 0x3, 0xf1, 0x0, 0x0, 0x1f, 0x30, 0x0,
    0x2f, 0x20, 0x0, 0x3, 0xf1, 0x0, 0x0, 0x1f,
    0x30, 0x0, 0x2f, 0x20, 0x0, 0x3, 0xf1, 0x0,
    0x0, 0x1f, 0x30, 0x0, 0x2f, 0xa9, 0x99, 0x9a,
    0xf9, 0x99, 0x99, 0x9f, 0x30, 0x0, 0x2f, 0x20,
    0x0, 0x3, 0xf1, 0x0, 0x0, 0x1f, 0x30, 0x0,
    0x2f, 0x20, 0x0, 0x3, 0xf1, 0x0, 0x0, 0x1f,
    0x30, 0x0, 0x3f, 0x20, 0x0, 0x3, 0xf1, 0x0,
    0x0, 0x1f, 0x30, 0x0, 0x3f, 0x20, 0x0, 0x3,
    0xf1, 0x0, 0x0, 0x1f, 0x40, 0x0, 0x3f, 0x98,
    0x88, 0x8a, 0xf9, 0x88, 0x88, 0x9f, 0x40, 0x0,
    0x3f, 0x20, 0x0, 0x3, 0xf1, 0x0, 0x0, 0x17,
    0x0, 0x50, 0x23, 0x0, 0x0, 0x3, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x80, 0x0, 0x0, 0x0, 0x3,
    0xf1, 0x0, 0x0, 0x0, 0x1, 0xa0, 0x0, 0x0,
    0x0, 0x3, 0xf1, 0x0, 0x0, 0x0, 0x2, 0xe0,
    0x0, 0x0, 0x0, 0x2, 0xf6, 0x22, 0x22, 0x22,
    0x29, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xc2,

    /* U+7684 "的" */
    0x0, 0x0, 0x61, 0x0, 0x0, 0x0, 0x14, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe1, 0x0, 0x0,
    0x6, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf4,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x4a, 0x0, 0x0, 0x0, 0xe, 0x90, 0x0,
    0x0, 0x0, 0x5, 0x8, 0x20, 0x6, 0x20, 0x4,
    0xf1, 0x0, 0x0, 0x40, 0x1, 0xfa, 0xa9, 0x99,
    0xfe, 0x0, 0xae, 0x99, 0x99, 0x9f, 0xc0, 0x1f,
    0x30, 0x0, 0xe, 0x50, 0x2f, 0x20, 0x0, 0x2,
    0xf6, 0x0, 0xf3, 0x0, 0x0, 0xe4, 0x9, 0x60,
    0x0, 0x0, 0x2f, 0x40, 0xf, 0x30, 0x0, 0xe,
    0x42, 0xb0, 0x0, 0x0, 0x2, 0xf3, 0x0, 0xf3,
    0x0, 0x0, 0xe4, 0x91, 0x10, 0x0, 0x0, 0x3f,
    0x30, 0xf, 0x30, 0x0, 0xe, 0x72, 0x9, 0x40,
    0x0, 0x3, 0xf3, 0x0, 0xf3, 0x0, 0x0, 0xe4,
    0x0, 0x1f, 0x40, 0x0, 0x3f, 0x20, 0xf, 0xa9,
    0x99, 0x9f, 0x40, 0x0, 0xae, 0x0, 0x4, 0xf2,
    0x0, 0xf3, 0x0, 0x0, 0xe4, 0x0, 0x5, 0xf2,
    0x0, 0x4f, 0x10, 0xf, 0x30, 0x0, 0xe, 0x50,
    0x0, 0x1a, 0x0, 0x5, 0xf1, 0x0, 0xf3, 0x0,
    0x0, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x0,
    0xf, 0x30, 0x0, 0xe, 0x50, 0x0, 0x0, 0x0,
    0x6, 0xf0, 0x0, 0xf3, 0x0, 0x0, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0xf, 0x30, 0x0,
    0xe, 0x50, 0x0, 0x0, 0x0, 0x9, 0xd0, 0x1,
    0xfa, 0x99, 0x99, 0xf5, 0x0, 0x1, 0x75, 0x34,
    0xf9, 0x0, 0x1f, 0x30, 0x0, 0xe, 0x50, 0x0,
    0x1, 0x8f, 0xff, 0x30, 0x1, 0x90, 0x0, 0x0,
    0x30, 0x0, 0x0, 0x0, 0x7d, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+79BB "离" */
    0x0, 0x0, 0x0, 0x0, 0x28, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x2c,
    0x10, 0x6b, 0x99, 0x99, 0x99, 0x99, 0x9a, 0x99,
    0x99, 0x99, 0xbc, 0xa0, 0x0, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x25, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xeb, 0x5, 0x84, 0x2, 0xdd, 0x10, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xe5, 0x0, 0x7, 0xde,
    0x70, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe5,
    0x0, 0x6, 0xdc, 0xe6, 0x0, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xe5, 0x2, 0xa6, 0x0, 0x7f, 0x70,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xd5, 0x55, 0x0,
    0x0, 0x7, 0x80, 0xf5, 0x0, 0x0, 0x0, 0x2,
    0xfb, 0x99, 0x99, 0x99, 0x99, 0x99, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x62, 0x0, 0x0, 0xc8, 0x0,
    0x0, 0xc3, 0x0, 0x0, 0x0, 0x50, 0x0, 0x0,
    0x5, 0xf5, 0x0, 0x0, 0x0, 0x71, 0x0, 0x0,
    0xeb, 0x99, 0x99, 0x9f, 0xb9, 0x99, 0x99, 0x99,
    0xfb, 0x0, 0x0, 0xd6, 0x0, 0x0, 0x8a, 0x0,
    0x0, 0x0, 0x0, 0xe4, 0x0, 0x0, 0xd6, 0x0,
    0x4, 0xc0, 0x0, 0x97, 0x0, 0x0, 0xe4, 0x0,
    0x0, 0xd6, 0x0, 0x5b, 0x0, 0x0, 0xb, 0xd3,
    0x0, 0xe4, 0x0, 0x0, 0xd6, 0x6, 0xfc, 0xbc,
    0xba, 0x98, 0xed, 0x0, 0xe4, 0x0, 0x0, 0xd6,
    0x0, 0xc6, 0x30, 0x0, 0x0, 0x3d, 0x0, 0xe4,
    0x0, 0x0, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xe4, 0x0, 0x0, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x39, 0xdd, 0xf2, 0x0, 0x0,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0x90, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+7B80 "简" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdd, 0x10, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf4, 0x0, 0x7, 0x20, 0x9d, 0x10, 0x0, 0x8,
    0x10, 0x0, 0xe, 0xc9, 0xa9, 0x9c, 0xb3, 0xfa,
    0xa9, 0x99, 0x9c, 0x90, 0x0, 0x98, 0x0, 0xb7,
    0x0, 0xb, 0x50, 0xb, 0x40, 0x0, 0x0, 0x5,
    0x90, 0x0, 0x5f, 0x0, 0x74, 0x0, 0x5, 0xe0,
    0x0, 0x0, 0x37, 0x0, 0x86, 0x6, 0x0, 0x30,
    0x0, 0x0, 0x40, 0x30, 0x0, 0x0, 0x0, 0xd,
    0xa0, 0x1a, 0x99, 0x99, 0x99, 0x99, 0xfc, 0x0,
    0x0, 0x5a, 0x26, 0xf1, 0x1, 0x0, 0x0, 0x0,
    0x0, 0xe6, 0x0, 0x0, 0x5f, 0x20, 0x81, 0x0,
    0x0, 0x2, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x5e,
    0x0, 0x4, 0xd9, 0x99, 0x9f, 0x70, 0x0, 0xe4,
    0x0, 0x0, 0x4e, 0x0, 0x4, 0xf0, 0x0, 0x2f,
    0x30, 0x0, 0xe4, 0x0, 0x0, 0x4e, 0x0, 0x3,
    0xf0, 0x0, 0x2f, 0x0, 0x0, 0xe4, 0x0, 0x0,
    0x4e, 0x0, 0x3, 0xf0, 0x0, 0x2f, 0x0, 0x0,
    0xe4, 0x0, 0x0, 0x4e, 0x0, 0x3, 0xf9, 0x99,
    0xaf, 0x0, 0x0, 0xe4, 0x0, 0x0, 0x5e, 0x0,
    0x4, 0xf0, 0x0, 0x2f, 0x10, 0x0, 0xe4, 0x0,
    0x0, 0x5e, 0x0, 0x4, 0xf0, 0x0, 0x2f, 0x10,
    0x0, 0xe4, 0x0, 0x0, 0x5f, 0x0, 0x4, 0xf9,
    0x99, 0xaf, 0x10, 0x0, 0xe4, 0x0, 0x0, 0x5f,
    0x0, 0x4, 0xf0, 0x0, 0x2a, 0x10, 0x0, 0xe4,
    0x0, 0x0, 0x5f, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x10, 0x0, 0xf4, 0x0, 0x0, 0x5f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0xde, 0xf3, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xa0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x0, 0x5, 0x0, 0x0, 0x0, 0x14, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0,
    0x0, 0xb, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xae, 0x10, 0x0, 0x0, 0x2, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0x0, 0xa, 0x50, 0x0, 0x9, 0xa0, 0x0,
    0x2b, 0xa9, 0x9a, 0x99, 0x99, 0xad, 0xc1, 0x0,
    0x2d, 0x10, 0xa, 0x50, 0x0, 0xc, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0xc3, 0x0, 0x2f, 0xc0, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x0, 0xa, 0x70, 0x12,
    0xcc, 0x0, 0x0, 0xd6, 0x0, 0x30, 0x0, 0x0,
    0x5f, 0xfc, 0x9b, 0xe1, 0x0, 0x8, 0x70, 0x0,
    0x6b, 0x20, 0x0, 0x8, 0x20, 0x2e, 0x30, 0x0,
    0x5a, 0x0, 0x0, 0x7, 0xf8, 0x0, 0x0, 0x0,
    0xd4, 0x0, 0x7, 0xe3, 0x34, 0x56, 0x77, 0xef,
    0x70, 0x0, 0xb, 0x70, 0x0, 0xa, 0xfe, 0xfd,
    0x68, 0xf1, 0xd, 0x80, 0x0, 0xa8, 0x0, 0x1,
    0x33, 0x40, 0xf6, 0x4, 0xf0, 0x3, 0x20, 0xd,
    0xfa, 0xbc, 0xa6, 0x20, 0x0, 0xf6, 0x4, 0xf0,
    0x0, 0x0, 0x9, 0xf9, 0x30, 0x0, 0x0, 0x0,
    0xf4, 0x4, 0xf0, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0, 0x0, 0x2, 0xf2, 0x4, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x30, 0x5, 0xf0, 0x4,
    0xf0, 0x0, 0x0, 0x0, 0x1, 0x48, 0xa8, 0x40,
    0xa, 0xb0, 0x4, 0xf0, 0x0, 0x60, 0x3b, 0xef,
    0xa5, 0x0, 0x0, 0x1f, 0x50, 0x4, 0xf0, 0x0,
    0x90, 0xe, 0xa1, 0x0, 0x0, 0x0, 0xcb, 0x0,
    0x4, 0xf0, 0x0, 0xc0, 0x2, 0x0, 0x0, 0x0,
    0x1c, 0x90, 0x0, 0x3, 0xf6, 0x35, 0xf8, 0x0,
    0x0, 0x0, 0x6, 0xa3, 0x0, 0x0, 0x0, 0xae,
    0xee, 0xc3, 0x0, 0x0, 0x0, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7F6E "置" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0x1, 0xfa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xbf, 0x80, 0x0,
    0x0, 0x1, 0xf4, 0x0, 0x1f, 0x30, 0x2, 0xf1,
    0x0, 0x2f, 0x40, 0x0, 0x0, 0x1, 0xf4, 0x0,
    0x1f, 0x30, 0x2, 0xf1, 0x0, 0x2f, 0x40, 0x0,
    0x0, 0x1, 0xf4, 0x0, 0x1f, 0x30, 0x2, 0xf1,
    0x0, 0x2f, 0x40, 0x0, 0x0, 0x1, 0xfb, 0xaa,
    0xaf, 0xba, 0xab, 0xfa, 0xaa, 0xbf, 0x40, 0x0,
    0x0, 0x2, 0xb1, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0x18, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x50, 0x0, 0x0, 0x4d, 0x20, 0x0,
    0x0, 0x1c, 0xca, 0xaa, 0xaa, 0xbf, 0xaa, 0xaa,
    0xaa, 0xaa, 0x80, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x5c, 0x0, 0x0, 0x6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xea, 0xaa, 0xdd, 0xaa, 0xaa,
    0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfb,
    0xaa, 0xaa, 0xaa, 0xaa, 0xcf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfb,
    0xaa, 0xaa, 0xaa, 0xaa, 0xcf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfb, 0xaa, 0xaa, 0xaa, 0xaa,
    0xcf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x5d, 0x0, 0x9, 0xaa, 0xac, 0xfb,
    0xaa, 0xaa, 0xaa, 0xaa, 0xcf, 0xaa, 0xff, 0xb0,
    0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x0, 0x34, 0x0, 0x0, 0x4, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x50,
    0x0, 0xa, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0x0, 0x0, 0x9, 0xa0, 0x0, 0x2c,
    0x20, 0x29, 0x99, 0x99, 0xbf, 0x99, 0x99, 0x9c,
    0xd9, 0x99, 0xef, 0xd2, 0x2, 0x0, 0x0, 0x4e,
    0x0, 0x0, 0x9, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0x0, 0x0, 0x46, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x0, 0x2,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x50,
    0x2f, 0x80, 0x7, 0xf2, 0x0, 0x9, 0xd1, 0x0,
    0x0, 0xf, 0x70, 0x2f, 0x30, 0xc, 0xc9, 0x99,
    0x9a, 0xa8, 0x0, 0x0, 0xf, 0x40, 0x1f, 0x30,
    0x2f, 0x14, 0x10, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x40, 0x1f, 0x30, 0x97, 0x1, 0xcb, 0x40, 0x0,
    0x0, 0x0, 0xf, 0x40, 0x2f, 0x32, 0xa0, 0x0,
    0x9, 0xf9, 0x0, 0x0, 0x0, 0xf, 0x40, 0x2f,
    0x47, 0x0, 0x0, 0x0, 0xae, 0x0, 0x0, 0x0,
    0x2, 0x10, 0x26, 0x0, 0x0, 0x0, 0x0, 0x45,
    0x0, 0x0, 0x0, 0x0, 0xfa, 0x99, 0x99, 0x99,
    0x99, 0x99, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xf7,
    0x0, 0xe6, 0x0, 0xf5, 0x0, 0xe7, 0x0, 0x0,
    0x0, 0x0, 0xf7, 0x0, 0xe6, 0x0, 0xf5, 0x0,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0xf7, 0x0, 0xe6,
    0x0, 0xf5, 0x0, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0xf7, 0x0, 0xe6, 0x0, 0xf5, 0x0, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0xf7, 0x0, 0xe6, 0x0, 0xf5,
    0x0, 0xe6, 0x7, 0x70, 0x69, 0x99, 0xfc, 0x99,
    0xfb, 0x99, 0xfb, 0x99, 0xfb, 0xaf, 0xf5, 0x12,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0x0,
    0x0, 0x0, 0x29, 0x0, 0x6, 0x99, 0x99, 0x99,
    0x99, 0x9a, 0x99, 0x99, 0x99, 0xef, 0xb0, 0x1,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x5b, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x98, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x53, 0x0,
    0x0, 0x0, 0x0, 0x69, 0x99, 0x99, 0x99, 0x99,
    0x99, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xa8, 0x88, 0x88, 0x88, 0x89, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0x50, 0x0, 0x0, 0x0, 0x2,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x50, 0x0,
    0x0, 0x0, 0x2, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x50, 0x0, 0x0, 0x0, 0x2, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xa8, 0x88, 0x88, 0x88,
    0x89, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x51,
    0x11, 0x11, 0x11, 0x12, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0x30, 0x0, 0x0, 0x0, 0x1, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8A9E "語" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xc3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x40, 0x0,
    0x0, 0x0, 0x7f, 0x10, 0x0, 0x89, 0x99, 0xa9,
    0x99, 0x9e, 0xe2, 0x0, 0x0, 0x0, 0x2f, 0x30,
    0x50, 0x10, 0x3, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xa9, 0x9d, 0x9a, 0xe9, 0x0, 0x4, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x16, 0xe1, 0x11, 0x97, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x8, 0x9c, 0xd7,
    0x77, 0xeb, 0x0, 0x0, 0x0, 0x68, 0x88, 0x9f,
    0x40, 0x0, 0xa, 0xa0, 0x0, 0xd7, 0x0, 0x0,
    0x0, 0x22, 0x11, 0x11, 0x0, 0x0, 0xb, 0x90,
    0x0, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x70, 0x0, 0xf6, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x7, 0x1, 0x11, 0x1f, 0x61,
    0x12, 0xf6, 0x5f, 0x40, 0x0, 0x9a, 0x99, 0xac,
    0x43, 0xa8, 0x77, 0x77, 0x77, 0x77, 0x77, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x3,
    0x0, 0x6, 0x62, 0x22, 0x22, 0x2a, 0x90, 0x0,
    0x0, 0xda, 0x99, 0x9f, 0xa0, 0x7, 0xf7, 0x77,
    0x77, 0x7d, 0xc0, 0x0, 0x0, 0xd6, 0x0, 0xe,
    0x50, 0x6, 0xe0, 0x0, 0x0, 0xa, 0xa0, 0x0,
    0x0, 0xd6, 0x0, 0xe, 0x50, 0x6, 0xe0, 0x0,
    0x0, 0xa, 0xa0, 0x0, 0x0, 0xd6, 0x0, 0xe,
    0x50, 0x6, 0xe0, 0x0, 0x0, 0xa, 0xa0, 0x0,
    0x0, 0xd6, 0x0, 0xe, 0x50, 0x6, 0xe0, 0x0,
    0x0, 0xa, 0xa0, 0x0, 0x0, 0xdb, 0x88, 0x8f,
    0x50, 0x6, 0xf9, 0x99, 0x99, 0x9d, 0xa0, 0x0,
    0x0, 0xd6, 0x0, 0xe, 0x60, 0x7, 0xe0, 0x0,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0x92, 0x0, 0x5,
    0x10, 0x5, 0x70, 0x0, 0x0, 0x5, 0x30, 0x0,

    /* U+8BA1 "计" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x8a, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x60, 0x0, 0x0, 0x0, 0x8f,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf4,
    0x0, 0x0, 0x0, 0x8e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf4, 0x0, 0x0, 0x0, 0x8e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x1b, 0x60, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x5d, 0x10, 0x7, 0x98, 0x8f, 0xa0,
    0x6b, 0x99, 0x99, 0xcf, 0x99, 0x99, 0x99, 0x70,
    0x0, 0x0, 0xf, 0x30, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x30,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x30, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x30,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x30, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x30,
    0x3, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x30, 0x66, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x38,
    0xa0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xcd, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3,
    0x0, 0x0, 0x0, 0x8f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0x0, 0x8f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+8BBE "设" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x30, 0x0, 0x0,
    0x71, 0x0, 0x2, 0xa1, 0x0, 0x0, 0x0, 0x6,
    0xf3, 0x0, 0x0, 0xed, 0x99, 0x9b, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0xfb, 0x0, 0x0, 0xd9, 0x0,
    0x5, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xa7, 0x0,
    0x0, 0xe7, 0x0, 0x5, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x5, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf1,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0xa, 0xa0, 0x0, 0x4, 0xfb, 0x9b, 0xb2,
    0x4a, 0x99, 0xfd, 0x10, 0x5d, 0x10, 0x0, 0x0,
    0x68, 0x98, 0x83, 0x1, 0x0, 0xe7, 0x5, 0xb1,
    0x0, 0x0, 0x0, 0x6, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x66, 0x2a, 0xa9, 0x99, 0x99, 0x9f, 0xc0,
    0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x44, 0x0,
    0x0, 0x5f, 0x40, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x19, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,
    0x0, 0xe6, 0x0, 0x0, 0xb, 0x10, 0x2, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x30, 0x5,
    0x80, 0xa, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xe6,
    0x5, 0x70, 0x0, 0xd2, 0x3f, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xe6, 0x6a, 0x0, 0x0, 0x5d, 0xda,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xed, 0xd0, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x30, 0x0, 0x2, 0xeb, 0xaf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xb7, 0x0, 0x0, 0x7e, 0x60,
    0x7, 0xfe, 0x94, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0x81, 0x0, 0x0, 0x3c, 0xff, 0xd5, 0x0,
    0x0, 0x4, 0x8a, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x49, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BED "语" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x54, 0x0, 0x0, 0x4, 0xd2, 0x0, 0x4a,
    0x99, 0x9a, 0x99, 0x99, 0x9e, 0xe2, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x20, 0x5, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x10, 0x0, 0x0,
    0x7f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x50, 0x0, 0x0, 0x9, 0xc0, 0x0, 0x1a, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xa9, 0xed,
    0x99, 0x9a, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x70, 0x0, 0x4f, 0x10, 0x0,
    0x0, 0x0, 0x4c, 0x10, 0x0, 0x1, 0xf4, 0x0,
    0x4, 0xf1, 0x0, 0x0, 0x5b, 0x9b, 0xf2, 0x0,
    0x0, 0x4f, 0x10, 0x0, 0x4f, 0x10, 0x20, 0x0,
    0x0, 0x6d, 0x4, 0x99, 0x9b, 0xf9, 0x99, 0x9a,
    0xf9, 0xaf, 0x90, 0x0, 0x6, 0xd0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd0, 0x0, 0x8, 0x10,
    0x0, 0x0, 0x0, 0xa2, 0x0, 0x0, 0x0, 0x6d,
    0x0, 0x0, 0xec, 0x99, 0x99, 0x99, 0x9f, 0xa0,
    0x0, 0x0, 0x6, 0xd0, 0x3, 0xd, 0x70, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x6d, 0x7,
    0x60, 0xd7, 0x0, 0x0, 0x0, 0xf, 0x40, 0x0,
    0x0, 0x6, 0xeb, 0x80, 0xd, 0x70, 0x0, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0x0, 0xaf, 0xa0, 0x0,
    0xd7, 0x0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0,
    0x4, 0xc0, 0x0, 0xd, 0xb8, 0x88, 0x88, 0x88,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8,
    0x11, 0x11, 0x11, 0x1f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x20, 0x0, 0x0, 0x0, 0x50,
    0x0, 0x0,

    /* U+8DDD "距" */
    0x0, 0x20, 0x0, 0x0, 0x30, 0x2, 0x0, 0x0,
    0x0, 0x2, 0x30, 0x0, 0xe, 0x99, 0x99, 0xaf,
    0x50, 0xe9, 0x99, 0x99, 0x99, 0xee, 0x20, 0x0,
    0xe4, 0x0, 0x3, 0xe0, 0xe, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x40, 0x0, 0x3e, 0x0,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe4,
    0x0, 0x3, 0xe0, 0xe, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x3f, 0x0, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe4, 0x0,
    0x3, 0xf0, 0xe, 0x96, 0x66, 0x66, 0x7e, 0x30,
    0x0, 0xf, 0xa9, 0xe9, 0xaf, 0x0, 0xe7, 0x33,
    0x33, 0x37, 0xf2, 0x0, 0x0, 0xa1, 0xf, 0x21,
    0x10, 0xe, 0x50, 0x0, 0x0, 0x5e, 0x0, 0x0,
    0x0, 0x0, 0xf2, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0x5, 0xe0, 0x0, 0x5, 0xa2, 0xf, 0x20, 0x60,
    0xe, 0x50, 0x0, 0x0, 0x5e, 0x0, 0x0, 0x4e,
    0x10, 0xfa, 0xac, 0x40, 0xe5, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0x4, 0xe0, 0xf, 0x20, 0x0, 0xe,
    0xb9, 0x99, 0x99, 0xbe, 0x0, 0x0, 0x4e, 0x0,
    0xf2, 0x0, 0x0, 0xe5, 0x0, 0x0, 0x5, 0xe0,
    0x0, 0x4, 0xe0, 0xf, 0x20, 0x0, 0xe, 0x50,
    0x0, 0x0, 0x21, 0x0, 0x0, 0x4e, 0x0, 0xf2,
    0x0, 0x10, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xe0, 0xf, 0x47, 0x96, 0xe, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0x49, 0xfa, 0x30,
    0x0, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0xfe, 0x71, 0x0, 0x0, 0x1e, 0x50, 0x0, 0x0,
    0x1, 0xd5, 0x6, 0xe6, 0x0, 0x0, 0x0, 0x5,
    0xfb, 0x99, 0x99, 0x99, 0x99, 0x80, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+901F "速" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0x10, 0x0, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xe3, 0x0,
    0x0, 0x0, 0x2, 0xf1, 0x0, 0x2, 0xc2, 0x0,
    0x0, 0x1, 0xfc, 0x8, 0xb9, 0x99, 0x99, 0xf9,
    0x99, 0x99, 0x97, 0x0, 0x0, 0x0, 0xa7, 0x0,
    0x0, 0x0, 0x2, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x2, 0xf1,
    0x0, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xa9, 0x99, 0xf9, 0x99, 0xbf, 0x50, 0x0,
    0x0, 0x0, 0x4, 0x0, 0xe, 0x40, 0x2, 0xf1,
    0x0, 0x4e, 0x0, 0x0, 0x7, 0x99, 0xaf, 0x70,
    0xe, 0x40, 0x2, 0xf1, 0x0, 0x4e, 0x0, 0x0,
    0x1, 0x10, 0x3f, 0x0, 0xe, 0x40, 0x2, 0xf1,
    0x0, 0x4f, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x0,
    0xe, 0xb9, 0x9f, 0xf9, 0x99, 0xbf, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0x0, 0xa, 0x20, 0x4f, 0xf1,
    0x0, 0x25, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x0,
    0x0, 0x1, 0xe9, 0xf8, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0x0, 0x0, 0xc, 0x82, 0xf2,
    0x8e, 0x81, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x0,
    0x0, 0xb8, 0x2, 0xf1, 0x3, 0xef, 0x50, 0x0,
    0x0, 0x0, 0x3f, 0x0, 0xa, 0x60, 0x2, 0xf1,
    0x0, 0x1d, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0x11,
    0x93, 0x0, 0x3, 0xf2, 0x0, 0x2, 0xb0, 0x0,
    0x0, 0x2b, 0xa4, 0xb5, 0x0, 0x0, 0x3, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfa, 0x0, 0x1c,
    0x60, 0x0, 0x4, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xd0, 0x0, 0x0, 0x9e, 0xb8, 0x66, 0x56,
    0x67, 0x79, 0xab, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6b, 0xdf, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb9,
    0x99, 0x99, 0x99, 0x99, 0x9f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x9, 0xd9,
    0x99, 0x99, 0x99, 0x99, 0x9f, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x9, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xd9, 0x99, 0x99, 0x99, 0x99,
    0x9f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x19, 0x0,
    0x8, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0xcd, 0x80, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x9d, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x60,
    0x0, 0xe, 0x50, 0x0, 0xa, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x60, 0x0, 0xe, 0x50, 0x0,
    0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0xd, 0xb9,
    0x99, 0x9f, 0xb9, 0x99, 0x9d, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x60, 0x0, 0xe, 0x50, 0x0,
    0xa, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe, 0xb9,
    0x99, 0x9f, 0xb9, 0x99, 0x9d, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xc, 0x30, 0x0, 0xe, 0x50, 0x0,
    0x6, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x50, 0x0, 0x0, 0xb7, 0x0, 0x0,
    0x0, 0x3, 0xda, 0x99, 0x99, 0x9f, 0xb9, 0x99,
    0x99, 0x99, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x50, 0x0,
    0x0, 0x0, 0x6b, 0x0, 0x3, 0x99, 0x99, 0x99,
    0x99, 0x9f, 0xb9, 0x99, 0x99, 0x99, 0xff, 0x60,
    0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+95F4 "间" */
    0x0, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xb0, 0x1, 0x0,
    0xfa, 0x7, 0xa9, 0x88, 0x88, 0x88, 0x8b, 0xf5,
    0x2f, 0x80, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xe0, 0x2f, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xe0, 0x1f, 0x30, 0x0, 0x60,
    0x0, 0x0, 0x74, 0x0, 0x6, 0xe0, 0x1f, 0x30,
    0x0, 0xeb, 0x99, 0x99, 0xfd, 0x0, 0x6, 0xe0,
    0x1f, 0x30, 0x0, 0xe6, 0x0, 0x0, 0xe4, 0x0,
    0x6, 0xe0, 0x1f, 0x30, 0x0, 0xe6, 0x0, 0x0,
    0xe4, 0x0, 0x6, 0xe0, 0x1f, 0x30, 0x0, 0xd6,
    0x0, 0x0, 0xe4, 0x0, 0x6, 0xe0, 0x1f, 0x30,
    0x0, 0xdb, 0x99, 0x99, 0xf4, 0x0, 0x6, 0xe0,
    0x1f, 0x30, 0x0, 0xd6, 0x0, 0x0, 0xe5, 0x0,
    0x6, 0xe0, 0x1f, 0x30, 0x0, 0xd6, 0x0, 0x0,
    0xe5, 0x0, 0x6, 0xe0, 0x1f, 0x30, 0x0, 0xe6,
    0x0, 0x0, 0xe5, 0x0, 0x6, 0xe0, 0x1f, 0x30,
    0x0, 0xe6, 0x0, 0x0, 0xe5, 0x0, 0x6, 0xe0,
    0x1f, 0x30, 0x0, 0xeb, 0x99, 0x99, 0xf5, 0x0,
    0x6, 0xe0, 0x1f, 0x30, 0x0, 0xe4, 0x0, 0x0,
    0xb2, 0x0, 0x6, 0xe0, 0x1f, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x1f, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x7, 0xe0,
    0x2f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5d,
    0xef, 0xc0, 0x2e, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcd, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+9650 "限" */
    0x33, 0x0, 0x3, 0x50, 0x32, 0x0, 0x0, 0x0,
    0x18, 0x0, 0x5, 0xf9, 0x99, 0xee, 0x15, 0xf9,
    0x99, 0x99, 0x9b, 0xf6, 0x0, 0x5f, 0x0, 0xf,
    0x40, 0x5e, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x4,
    0xf0, 0x3, 0xd0, 0x5, 0xe0, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x4f, 0x0, 0x87, 0x0, 0x5e, 0x0,
    0x0, 0x0, 0x6e, 0x0, 0x4, 0xf0, 0xb, 0x10,
    0x5, 0xf9, 0x99, 0x99, 0x9b, 0xe0, 0x0, 0x4f,
    0x0, 0x90, 0x0, 0x5e, 0x0, 0x0, 0x0, 0x6f,
    0x0, 0x4, 0xf0, 0x25, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x6, 0xf0, 0x0, 0x4f, 0x0, 0x82, 0x0,
    0x5e, 0x0, 0x0, 0x0, 0x6f, 0x0, 0x4, 0xf0,
    0x1, 0xc0, 0x4, 0xf9, 0x99, 0x99, 0x9b, 0xf0,
    0x0, 0x4f, 0x0, 0x9, 0x60, 0x4e, 0x1, 0x80,
    0x0, 0x46, 0x10, 0x4, 0xf0, 0x0, 0x5d, 0x4,
    0xe0, 0xb, 0x0, 0x0, 0x7e, 0x10, 0x4f, 0x0,
    0x3, 0xf0, 0x4e, 0x0, 0x93, 0x0, 0x6f, 0xa4,
    0x4, 0xf0, 0x0, 0x5f, 0x4, 0xe0, 0x4, 0xb0,
    0x8b, 0x20, 0x0, 0x4f, 0x5a, 0xae, 0xd0, 0x4e,
    0x0, 0xd, 0xb5, 0x0, 0x0, 0x4, 0xf0, 0x3f,
    0xe4, 0x4, 0xe0, 0x0, 0x5d, 0x10, 0x0, 0x0,
    0x4f, 0x0, 0x41, 0x0, 0x4e, 0x0, 0x0, 0xbd,
    0x10, 0x0, 0x4, 0xf0, 0x0, 0x0, 0x4, 0xe0,
    0x38, 0x41, 0xde, 0x50, 0x0, 0x5f, 0x0, 0x0,
    0x0, 0x5f, 0xbb, 0x10, 0x1, 0xdf, 0xd7, 0x35,
    0xf0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x1,
    0xbf, 0xa2, 0x5f, 0x0, 0x0, 0x0, 0xa, 0x0,
    0x0, 0x0, 0x0, 0x40, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+97F3 "音" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xae, 0x0, 0x0, 0x8, 0x30,
    0x0, 0x0, 0x28, 0x88, 0x88, 0x88, 0xbc, 0x88,
    0x88, 0xaf, 0xe2, 0x0, 0x0, 0x3, 0x10, 0x20,
    0x0, 0x0, 0x0, 0x93, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4b, 0x0, 0x0, 0x0, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0,
    0x5, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf4, 0x0, 0xa, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xe1, 0x0, 0x1c, 0x0,
    0x0, 0x1c, 0x40, 0x59, 0x99, 0x99, 0x99, 0x99,
    0x99, 0xbb, 0x99, 0x99, 0xdf, 0xf3, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x70, 0x0, 0x0, 0x0, 0x0, 0xe, 0xb9,
    0x99, 0x99, 0x99, 0x9a, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x0, 0x0, 0x2, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x70, 0x0, 0x0,
    0x0, 0x2, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x70, 0x0, 0x0, 0x0, 0x2, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xc9, 0x99, 0x99, 0x99, 0x99,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x70, 0x0, 0x0, 0x0, 0x2, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xc9, 0x99, 0x99, 0x99,
    0x99, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xe, 0x70,
    0x0, 0x0, 0x0, 0x2, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x30, 0x0, 0x0, 0x0, 0x1, 0x91,
    0x0, 0x0,

    /* U+984C "題" */
    0x0, 0x7, 0x10, 0x0, 0xa, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x86, 0x0, 0x0, 0xcb, 0x99, 0x99,
    0xf7, 0x4c, 0x99, 0x99, 0x99, 0x9c, 0xb2, 0x0,
    0xb, 0x50, 0x0, 0xf, 0x20, 0x0, 0x5, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xb6, 0x0, 0x0, 0xf2,
    0x0, 0x0, 0x75, 0x0, 0x20, 0x0, 0x0, 0xb,
    0xb8, 0x88, 0x8f, 0x20, 0x2d, 0x9c, 0x99, 0x9f,
    0xa0, 0x0, 0x0, 0xb5, 0x0, 0x0, 0xf2, 0x1,
    0xf1, 0x0, 0x0, 0xe4, 0x0, 0x0, 0xb, 0x61,
    0x11, 0x1f, 0x20, 0x1f, 0x10, 0x0, 0xe, 0x30,
    0x0, 0x0, 0xca, 0x77, 0x77, 0xf2, 0x1, 0xf9,
    0x99, 0x99, 0xf3, 0x0, 0x0, 0x5, 0x0, 0x0,
    0x2, 0x0, 0x1f, 0x10, 0x0, 0xe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x51, 0xf4, 0x33,
    0x33, 0xe3, 0x0, 0x1, 0xba, 0x99, 0xea, 0x99,
    0x98, 0x1f, 0x66, 0x66, 0x6f, 0x30, 0x0, 0x0,
    0x10, 0xd, 0x40, 0x0, 0x1, 0xf1, 0x0, 0x0,
    0xe4, 0x0, 0x0, 0x1f, 0x50, 0xd4, 0x0, 0x0,
    0x1f, 0x99, 0x99, 0x9f, 0x40, 0x0, 0x2, 0xf1,
    0xd, 0x40, 0x3d, 0x12, 0xb1, 0x0, 0x0, 0x80,
    0x0, 0x0, 0x3e, 0x0, 0xda, 0x88, 0x85, 0x0,
    0xaa, 0x1, 0x91, 0x0, 0x0, 0x5, 0xf3, 0xd,
    0x40, 0x0, 0x0, 0x5e, 0x30, 0x3, 0xe5, 0x0,
    0x0, 0x78, 0x90, 0xd4, 0x0, 0x0, 0x3c, 0x10,
    0x0, 0x7, 0xf2, 0x0, 0xb, 0x41, 0x9d, 0x40,
    0x0, 0x38, 0x0, 0x0, 0x0, 0xc, 0x10, 0x0,
    0xd0, 0x2, 0xe8, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x47, 0x0, 0x2, 0xce, 0xa6,
    0x44, 0x44, 0x44, 0x56, 0x8a, 0xc5, 0x9, 0x0,
    0x0, 0x0, 0x49, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xe5, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9AD8 "高" */
    0x0, 0x0, 0x0, 0x0, 0x29, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd4, 0x0, 0x0, 0x0, 0x4,
    0x30, 0x39, 0x99, 0x99, 0x99, 0x99, 0xa9, 0x99,
    0x99, 0x99, 0x9f, 0xe2, 0x3, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0x10, 0x0, 0x0, 0x0, 0xa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xc9, 0x99, 0x99,
    0x99, 0x9f, 0x40, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x70, 0x0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0x0, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xc9, 0x99,
    0x99, 0x99, 0x9f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x30, 0x0, 0x0, 0x0, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x86, 0x0, 0x0, 0xeb, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0xfd, 0x0, 0x0,
    0xe6, 0x0, 0x31, 0x0, 0x0, 0x1, 0x60, 0x0,
    0xe6, 0x0, 0x0, 0xd6, 0x0, 0x9e, 0x99, 0x99,
    0x9b, 0xf3, 0x0, 0xe6, 0x0, 0x0, 0xd6, 0x0,
    0x8c, 0x0, 0x0, 0x5, 0xd0, 0x0, 0xe6, 0x0,
    0x0, 0xd6, 0x0, 0x8c, 0x0, 0x0, 0x5, 0xd0,
    0x0, 0xe6, 0x0, 0x0, 0xd6, 0x0, 0x8c, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0xe6, 0x0, 0x0, 0xd6,
    0x0, 0x9e, 0x88, 0x88, 0x8b, 0xe0, 0x0, 0xe6,
    0x0, 0x0, 0xe6, 0x0, 0x86, 0x0, 0x0, 0x4,
    0x70, 0x0, 0xe6, 0x0, 0x0, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0x76, 0xf5, 0x0, 0x0,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xf2, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x40, 0x0,

    /* U+FE87 "ﺇ" */
    0xb, 0xf6, 0x0, 0xbf, 0x60, 0xb, 0xf6, 0x0,
    0xbf, 0x60, 0xb, 0xf6, 0x0, 0xbf, 0x60, 0xb,
    0xf6, 0x0, 0xbf, 0x60, 0xb, 0xf6, 0x0, 0xbf,
    0x60, 0xb, 0xf6, 0x0, 0xbf, 0x60, 0xb, 0xf6,
    0x0, 0xbf, 0x60, 0xb, 0xf6, 0x0, 0xbf, 0x60,
    0xb, 0xf6, 0x0, 0xbf, 0x60, 0x0, 0x0, 0x1,
    0xbf, 0xd0, 0x9b, 0x35, 0xa, 0x80, 0x0, 0x4f,
    0xcc, 0x4b, 0xea, 0x61, 0x0, 0x0, 0x0,

    /* U+FE8B "ﺋ" */
    0x0, 0xa, 0xed, 0x20, 0x6, 0xd3, 0x41, 0x0,
    0x7a, 0x0, 0x0, 0x2, 0xfb, 0xb6, 0x0, 0x8f,
    0xc8, 0x20, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x12, 0x10, 0x0, 0x9, 0xf9, 0x0,
    0x0, 0x9f, 0x90, 0x0, 0x9, 0xf9, 0x0, 0x0,
    0xcf, 0x70, 0x3, 0x7f, 0xf4, 0x3, 0xff, 0xfc,
    0x0, 0x3f, 0xfa, 0x10, 0x0,

    /* U+FE8D "ﺍ" */
    0xbf, 0x6b, 0xf6, 0xbf, 0x6b, 0xf6, 0xbf, 0x6b,
    0xf6, 0xbf, 0x6b, 0xf6, 0xbf, 0x6b, 0xf6, 0xbf,
    0x6b, 0xf6, 0xbf, 0x6b, 0xf6, 0xbf, 0x6b, 0xf6,
    0xbf, 0x6b, 0xf6,

    /* U+FE8E "ﺎ" */
    0xbf, 0x60, 0x0, 0xbf, 0x60, 0x0, 0xbf, 0x60,
    0x0, 0xbf, 0x60, 0x0, 0xbf, 0x60, 0x0, 0xbf,
    0x60, 0x0, 0xbf, 0x60, 0x0, 0xbf, 0x60, 0x0,
    0xbf, 0x60, 0x0, 0xbf, 0x60, 0x0, 0xbf, 0x60,
    0x0, 0xbf, 0x60, 0x0, 0xbf, 0x60, 0x0, 0xbf,
    0x60, 0x0, 0xaf, 0x90, 0x0, 0x7f, 0xf6, 0x21,
    0x1e, 0xff, 0xf8, 0x2, 0xbf, 0xf8,

    /* U+FE95 "ﺕ" */
    0x0, 0x0, 0x0, 0x9, 0xb0, 0xaa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf0, 0xde,
    0x0, 0x0, 0x1, 0x32, 0x7, 0x82, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x2f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfb,
    0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfa, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf5, 0x3f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0xff, 0xc0, 0xb, 0xff,
    0xb6, 0x21, 0x12, 0x47, 0x9d, 0xff, 0xfb, 0x10,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x50, 0x0, 0x0, 0x2, 0x8c, 0xef, 0xfe, 0xdb,
    0x85, 0x10, 0x0, 0x0,

    /* U+FEA3 "ﺣ" */
    0x0, 0x2, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xfc, 0x84, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xa4, 0x0, 0x0,
    0x0, 0x12, 0x46, 0xad, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xff, 0x80, 0x0, 0x0, 0x22, 0x36, 0xbf,
    0xfe, 0x30, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xfb,
    0x10, 0x0, 0x0, 0x3, 0xff, 0xdb, 0x72, 0x0,
    0x0, 0x0, 0x0,

    /* U+FEA9 "ﺩ" */
    0x0, 0x17, 0x84, 0x0, 0x0, 0x0, 0x5f, 0xf4,
    0x0, 0x0, 0x0, 0x7f, 0xe1, 0x0, 0x0, 0x0,
    0xcf, 0x80, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0x0, 0xff, 0x40,
    0x0, 0x0, 0x5f, 0xf1, 0x45, 0x23, 0x9f, 0xfb,
    0x8, 0xff, 0xff, 0xfd, 0x10, 0x5d, 0xff, 0xc7,
    0x0, 0x0,

    /* U+FEAA "ﺪ" */
    0x0, 0x17, 0x83, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x5, 0xff, 0xb0, 0x0,
    0x12, 0x23, 0x9f, 0xff, 0xf9, 0x32, 0x8f, 0xff,
    0xff, 0xd5, 0xff, 0xfd, 0x8f, 0xff, 0xc7, 0x0,
    0x4d, 0xfd,

    /* U+FEBC "ﺼ" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xfe,
    0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x46, 0x20, 0x3, 0xef, 0xe7, 0x21, 0x5f,
    0xf5, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x3, 0xff,
    0xc1, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0, 0x0,
    0xdf, 0x91, 0xef, 0xb0, 0x0, 0x0, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xdf, 0xc0, 0x0,
    0x0, 0x1b, 0xff, 0x50, 0x0, 0x2, 0x4d, 0xff,
    0xff, 0xf4, 0x22, 0x36, 0xaf, 0xff, 0xfb, 0x42,
    0x3, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xf0, 0x3f, 0xfc, 0x50, 0x5c,
    0xff, 0xff, 0xfe, 0xb7, 0x10, 0x6d, 0xff, 0x0,

    /* U+FECB "ﻋ" */
    0x0, 0x0, 0x0, 0x5b, 0xdf, 0x30, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xaf,
    0xe6, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfb, 0x0, 0x0, 0x0, 0x60, 0x0, 0x3f,
    0xf5, 0x0, 0x17, 0xee, 0x0, 0x0, 0xaf, 0xfc,
    0xbf, 0xff, 0xc0, 0x0, 0x0, 0x9f, 0xff, 0xfc,
    0x50, 0x2, 0x36, 0xbf, 0xff, 0xc4, 0x0, 0x3,
    0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x3f, 0xeb,
    0x71, 0x0, 0x0, 0x0, 0x0,

    /* U+FEF3 "ﻳ" */
    0x0, 0x9, 0xf9, 0x0, 0x0, 0x9f, 0x90, 0x0,
    0x9, 0xf9, 0x0, 0x0, 0xbf, 0x70, 0x3, 0x7f,
    0xf4, 0x3, 0xff, 0xfc, 0x0, 0x3f, 0xfa, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xe0, 0xed, 0x0, 0xab, 0xa, 0xa0,

    /* U+FEF4 "ﻴ" */
    0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0x90,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0xbf,
    0xb0, 0x0, 0x3, 0x7f, 0xff, 0x73, 0x13, 0xff,
    0xff, 0xff, 0xf7, 0x3f, 0xfa, 0x3a, 0xef, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xe0, 0xed, 0x0, 0x0, 0xab, 0xa,
    0xa0, 0x0,

    /* U+FEF9 "ﻹ" */
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x5, 0x50,
    0x0, 0x0, 0xe, 0xf3, 0xa, 0xf7, 0x0, 0x0,
    0xe, 0xf3, 0x2, 0xfe, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0xbf, 0x60, 0x0, 0xe, 0xf3, 0x0, 0x3f,
    0xd0, 0x0, 0xe, 0xf3, 0x0, 0xc, 0xf4, 0x0,
    0xe, 0xf3, 0x0, 0x5, 0xfc, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0xdf, 0x30, 0xf, 0xf3, 0x0, 0x0,
    0x6f, 0xa0, 0xf, 0xf2, 0x0, 0x0, 0xe, 0xf2,
    0x2f, 0xf0, 0x0, 0x0, 0x7, 0xf9, 0x6f, 0xc0,
    0x0, 0x0, 0x1, 0xff, 0xef, 0x70, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf5, 0x0, 0x2, 0x52, 0x4c, 0xff, 0x70, 0x0,
    0x5, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x3, 0xdf,
    0xe9, 0x10, 0x0, 0x0, 0x1b, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x9b, 0x35, 0x0, 0x0, 0x0, 0x0,
    0xa8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xcc,
    0x40, 0x0, 0x0, 0x0, 0xbe, 0xa6, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 47, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 57, .box_w = 3, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 29, .adv_w = 77, .box_w = 5, .box_h = 6, .ofs_x = 0, .ofs_y = 13},
    {.bitmap_index = 44, .adv_w = 178, .box_w = 11, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 138, .adv_w = 116, .box_w = 7, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 219, .adv_w = 227, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 352, .adv_w = 125, .box_w = 8, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 428, .adv_w = 41, .box_w = 3, .box_h = 6, .ofs_x = 0, .ofs_y = 13},
    {.bitmap_index = 437, .adv_w = 64, .box_w = 4, .box_h = 23, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 483, .adv_w = 64, .box_w = 4, .box_h = 23, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 529, .adv_w = 103, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 12},
    {.bitmap_index = 554, .adv_w = 162, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 599, .adv_w = 73, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 611, .adv_w = 74, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 617, .adv_w = 57, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 622, .adv_w = 108, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 689, .adv_w = 123, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 746, .adv_w = 83, .box_w = 4, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 784, .adv_w = 111, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 851, .adv_w = 118, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 918, .adv_w = 115, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 985, .adv_w = 117, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1052, .adv_w = 121, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1109, .adv_w = 111, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1176, .adv_w = 121, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1243, .adv_w = 121, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1310, .adv_w = 57, .box_w = 3, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1327, .adv_w = 66, .box_w = 5, .box_h = 15, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 1365, .adv_w = 162, .box_w = 8, .box_h = 16, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 1429, .adv_w = 162, .box_w = 10, .box_h = 7, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 1464, .adv_w = 162, .box_w = 8, .box_h = 16, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 1528, .adv_w = 106, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1595, .adv_w = 258, .box_w = 16, .box_h = 24, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 1787, .adv_w = 110, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1854, .adv_w = 118, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1921, .adv_w = 115, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1988, .adv_w = 117, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2055, .adv_w = 102, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2112, .adv_w = 101, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2169, .adv_w = 121, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2236, .adv_w = 121, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2303, .adv_w = 54, .box_w = 3, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2332, .adv_w = 54, .box_w = 4, .box_h = 24, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 2380, .adv_w = 114, .box_w = 8, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2456, .adv_w = 81, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2504, .adv_w = 168, .box_w = 10, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2599, .adv_w = 129, .box_w = 8, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2675, .adv_w = 118, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2742, .adv_w = 112, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2809, .adv_w = 118, .box_w = 7, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2886, .adv_w = 114, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2953, .adv_w = 113, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3020, .adv_w = 103, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3087, .adv_w = 120, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3154, .adv_w = 106, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3221, .adv_w = 175, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3326, .adv_w = 115, .box_w = 9, .box_h = 19, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3412, .adv_w = 114, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3479, .adv_w = 101, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3536, .adv_w = 74, .box_w = 4, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 3584, .adv_w = 108, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3651, .adv_w = 74, .box_w = 4, .box_h = 23, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3697, .adv_w = 162, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 3729, .adv_w = 110, .box_w = 7, .box_h = 2, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3736, .adv_w = 38, .box_w = 4, .box_h = 3, .ofs_x = -1, .ofs_y = 16},
    {.bitmap_index = 3742, .adv_w = 96, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3784, .adv_w = 98, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3841, .adv_w = 95, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3883, .adv_w = 98, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3940, .adv_w = 96, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3982, .adv_w = 70, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4030, .adv_w = 97, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 4087, .adv_w = 99, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4144, .adv_w = 48, .box_w = 3, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4171, .adv_w = 48, .box_w = 4, .box_h = 23, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 4217, .adv_w = 99, .box_w = 7, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4284, .adv_w = 49, .box_w = 3, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4313, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4376, .adv_w = 99, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4418, .adv_w = 98, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4460, .adv_w = 99, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 4517, .adv_w = 99, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 4574, .adv_w = 73, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4609, .adv_w = 94, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4651, .adv_w = 68, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4696, .adv_w = 99, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4738, .adv_w = 91, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4780, .adv_w = 151, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4850, .adv_w = 92, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4892, .adv_w = 94, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 4949, .adv_w = 89, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4991, .adv_w = 88, .box_w = 6, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5063, .adv_w = 58, .box_w = 2, .box_h = 25, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 5088, .adv_w = 88, .box_w = 6, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5160, .adv_w = 162, .box_w = 10, .box_h = 3, .ofs_x = 0, .ofs_y = 12},
    {.bitmap_index = 5175, .adv_w = 384, .box_w = 6, .box_h = 7, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 5196, .adv_w = 384, .box_w = 24, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5424, .adv_w = 384, .box_w = 19, .box_h = 23, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 5643, .adv_w = 384, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5908, .adv_w = 384, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6173, .adv_w = 384, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6438, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6691, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6944, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7197, .adv_w = 384, .box_w = 22, .box_h = 22, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7439, .adv_w = 384, .box_w = 23, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7692, .adv_w = 384, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7957, .adv_w = 384, .box_w = 22, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 8199, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8452, .adv_w = 384, .box_w = 15, .box_h = 21, .ofs_x = 5, .ofs_y = -2},
    {.bitmap_index = 8610, .adv_w = 384, .box_w = 21, .box_h = 22, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8841, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9094, .adv_w = 384, .box_w = 19, .box_h = 22, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9303, .adv_w = 384, .box_w = 24, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9579, .adv_w = 384, .box_w = 23, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9832, .adv_w = 384, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10097, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 10350, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 10603, .adv_w = 384, .box_w = 20, .box_h = 22, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 10823, .adv_w = 384, .box_w = 21, .box_h = 23, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 11065, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11318, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11571, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11824, .adv_w = 384, .box_w = 24, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12100, .adv_w = 384, .box_w = 22, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12342, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 12595, .adv_w = 384, .box_w = 24, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12859, .adv_w = 384, .box_w = 24, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13135, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13388, .adv_w = 384, .box_w = 23, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13630, .adv_w = 384, .box_w = 23, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13872, .adv_w = 384, .box_w = 24, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14148, .adv_w = 384, .box_w = 24, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14424, .adv_w = 384, .box_w = 20, .box_h = 23, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 14654, .adv_w = 384, .box_w = 21, .box_h = 22, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 14885, .adv_w = 384, .box_w = 22, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 15127, .adv_w = 384, .box_w = 23, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15380, .adv_w = 384, .box_w = 22, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15633, .adv_w = 107, .box_w = 5, .box_h = 25, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 15696, .adv_w = 107, .box_w = 7, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 15749, .adv_w = 107, .box_w = 3, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 15776, .adv_w = 117, .box_w = 6, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 15830, .adv_w = 362, .box_w = 20, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15930, .adv_w = 237, .box_w = 15, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16005, .adv_w = 171, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16055, .adv_w = 202, .box_w = 12, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 16121, .adv_w = 333, .box_w = 23, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16225, .adv_w = 229, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16310, .adv_w = 107, .box_w = 7, .box_h = 11, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 16349, .adv_w = 116, .box_w = 9, .box_h = 11, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 16399, .adv_w = 219, .box_w = 12, .box_h = 24, .ofs_x = 0, .ofs_y = -6}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1e07, 0x1e2b, 0x1eac, 0x1ec8, 0x1f51, 0x2147, 0x2745,
    0x2925, 0x2927, 0x2e71, 0x2ea4, 0x2f38, 0x3585, 0x35e3, 0x35f4,
    0x36fe, 0x3706, 0x3a17, 0x3c5e, 0x406d, 0x4165, 0x4257, 0x4533,
    0x4682, 0x49b9, 0x4b7e, 0x4edd, 0x4f6c, 0x54db, 0x59fe, 0x5a9c,
    0x5b9f, 0x5bbc, 0x5beb, 0x5ddb, 0x601d, 0x61cd, 0x65f2, 0x664e,
    0x67f1, 0x684a, 0x6ad6, 0xce85, 0xce89, 0xce8b, 0xce8c, 0xce93,
    0xcea1, 0xcea7, 0xcea8, 0xceba, 0xcec9, 0xcef1, 0xcef2, 0xcef7
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 12290, .range_length = 52984, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 56, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 1, 0, 2, 0, 0, 0, 3,
    2, 4, 5, 6, 0, 7, 8, 7,
    9, 10, 11, 12, 13, 14, 15, 16,
    17, 16, 18, 19, 19, 0, 0, 0,
    0, 20, 21, 22, 23, 24, 25, 26,
    27, 0, 0, 0, 28, 29, 0, 0,
    30, 31, 30, 32, 33, 34, 35, 36,
    37, 38, 39, 40, 41, 42, 0, 0,
    0, 0, 43, 44, 45, 46, 47, 48,
    49, 50, 0, 0, 51, 0, 50, 50,
    52, 44, 53, 54, 55, 56, 57, 58,
    59, 60, 61, 62, 63, 0, 64, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 1, 0, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 8,
    10, 11, 12, 13, 14, 15, 16, 11,
    17, 18, 18, 19, 19, 0, 0, 0,
    20, 21, 22, 23, 24, 23, 23, 23,
    24, 23, 23, 25, 23, 23, 23, 23,
    24, 23, 24, 23, 26, 27, 28, 29,
    30, 31, 32, 33, 0, 34, 35, 0,
    0, 0, 36, 0, 37, 38, 37, 39,
    40, 0, 0, 41, 0, 0, 42, 42,
    37, 42, 38, 42, 43, 44, 45, 46,
    47, 48, 49, 50, 51, 0, 52, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 0, 0,
    0, 0, -5, 0, -5, -5, 0, -7,
    0, 0, 0, 0, 0, 0, -4, 0,
    0, 0, 0, -3, 0, -5, -5, 0,
    -5, 0, 0, 0, -4, 0, 0, 0,
    0, 0, 0, -21, -16, -15, 0, 0,
    0, 0, -7, 0, 0, 0, -5, 0,
    -15, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -3, -4, 0, -3, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 12, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, -6,
    -6, -6, 0, 0, 16, -5, -6, 0,
    -5, -5, -5, 0, 0, 0, -6, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -5, -6, 0, -6, 0, -3, -5, 0,
    -3, 0, 0, 0, 0, -3, 0, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, -4, -10, 0, -3, -6, -4,
    0, -4, 0, 0, 0, 0, 0, -5,
    0, -3, -17, -5, -16, -14, 0, -22,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, -3, 0, -7, -7, 0,
    -6, 0, 0, 0, 0, -16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    -6, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -9, 0,
    -4, -3, -5, -13, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -55, 0, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, -8, -8, 0, -9,
    0, -7, -8, 0, -6, -3, -3, -3,
    0, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, -4, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -3, 0, 0, -7, 0, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -11, 0, 0, 4, 0, -21,
    -12, -18, 0, 2, 0, 0, -9, 0,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, 0, 4, 0, 3, 3, 6, 4,
    0, 2, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -8, 0, -3, -3, 0, -10,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -13, 0,
    -4, -3, -7, -12, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, -10, 0, 0, 0, 0, -8, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    2, 0, 0, -4, 0, 0, 0, -1,
    0, -1, -13, 0, -6, -5, 0, -12,
    0, -9, -7, -1, 0, 0, -2, 0,
    0, 0, 0, -2, 0, -3, -3, 0,
    -2, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, -3, 0,
    -3, -2, -2, -5, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -1, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    -1, 0, -2, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, -28, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, -7, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -3, -3, -1, -3, 0, -3, -3, 0,
    -2, -1, -1, -5, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, -2, -2, -1, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 5, 0, 0,
    0, 0, -5, 0, 7, 0, 0, 0,
    0, 0, 0, -2, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    -3, -3, -3, 0, 0, 0, -1, -3,
    -1, -6, -6, 0, -5, 0, 0, 0,
    -5, -15, 0, 0, 0, 0, -13, 0,
    -10, 0, 0, -8, 0, 0, -10, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, -16, -1, -12, -11, 0, -18,
    0, -11, -8, 0, 0, 0, -5, 0,
    0, 0, 0, -5, -1, -10, -11, 0,
    -10, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    -1, -1, -2, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, -28,
    -5, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, -8, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -2, 0, 0, -1, -1, -1, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    -2, -1, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -3,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, -17, -9, -10, 0, 2,
    0, 0, -9, 0, 0, 0, -8, 0,
    -11, -13, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -16,
    -13, -13, -10, -13, 0, -13, -15, -7,
    -13, -16, -15, -16, -15, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, -16, -4, -8, 0, 0,
    0, 0, -3, 0, 0, 0, -3, 0,
    -7, -6, 0, -2, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -6, -6, 0, -6, 0, -4, -5, 0,
    -4, 0, 0, -1, 0, -3, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, -14,
    -3, -7, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -5, -5, 0, -2,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, -5, -5, 0, -5,
    0, -3, -4, 0, -3, 0, 0, -1,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, -5, 0, 0, 0,
    0, 0, -3, 0, 3, 0, 0, 0,
    0, 0, 0, -2, 0, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -3, -3, -3, 0, 0, -1, -3, -2,
    -3, -5, -5, 0, -4, 0, 0, 0,
    -7, 0, 0, -3, 0, 0, 0, -22,
    -13, -14, -4, 0, 0, 0, -11, 0,
    0, 0, -10, 0, -15, -12, 0, -4,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -15, -14, -14, -2, -15,
    0, -13, -15, -1, -12, -4, -4, -6,
    -4, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 0, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    -1, -1, -2, 0, 0, -1, 0, -1,
    -1, -3, -3, 0, -3, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, -8, -8, 0, 0,
    14, -7, -7, 0, -7, -4, -4, -4,
    0, -6, -7, 0, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -10, 0,
    -8, -7, 3, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, -4, 0, -3, 0, 0, 0,
    0, -3, 0, 0, 0, -5, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 0, -7, -5, 0, -14,
    0, -8, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, -4, 0, 0,
    0, -6, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -13, 0,
    -6, -5, -3, -14, -1, -8, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -8,
    0, -3, 0, 0, 0, -6, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -17, 0, -5, -4, -2, -14,
    0, -8, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -1, -1,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, -6, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -15, 0, -5, -4, -3, -15,
    0, -8, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -1, -2,
    0, 0, 0, -7, -5, 0, 0, 0,
    0, 7, 0, -5, -3, -4, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    -4, -3, 0, 0, 0, 0, 3, 0,
    3, 2, 5, 3, 0, 4, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -15, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, -5, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -13, 0,
    -7, -5, 0, -14, 0, -8, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -16, 0, -1, 0, 0, -6,
    0, -4, -4, -1, -2, -1, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, -3, 0, 0,
    0, -6, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, -13, 0,
    -6, -5, -3, -14, -1, -8, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -1, -2, 0, 0, 0, -7,
    0, 0, 0, 0, 0, -5, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 0, -4, -3, 0, -12,
    0, -6, -7, 0, 0, 0, 0, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, -6, 0, 0, 0,
    0, -4, 0, -10, -8, -9, 0, 0,
    0, 0, 0, 0, 0, 0, -7, 0,
    -8, -8, -1, 0, -1, 0, -19, 0,
    0, 0, -6, -2, -4, 0, -3, -2,
    -2, -2, 0, -3, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, -3, 0, 0, 0, -5, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -15, 0, -5, -4, -2, -14,
    0, -7, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -1, -1,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -12, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 0, -4, -3, 0, -12,
    0, -6, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, -5, 0, 0, 0,
    0, -5, 0, -7, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, -16, 0,
    0, 0, -5, -4, -3, -3, -4, -2,
    -2, 0, 0, -2, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -5, 0, 0, 0, 0, -5, 0, -7,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -15, 0, 0, 0, -5, -5,
    -3, 0, -4, -2, -1, 0, 0, -2,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -16, -1,
    -1, 0, 0, -5, 0, -3, -4, -1,
    -2, -1, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -5, 0, 0, 0, 0, -4, 0, -8,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, 0, -16, 0, 0, 0, -5, -3,
    -3, 0, -3, -2, -2, 0, 0, -2,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -18, 0,
    0, 0, 0, -5, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, -7, -8, 0, 0,
    14, -7, -7, 0, -6, -4, -4, -4,
    0, -5, -8, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 64,
    .right_class_cnt     = 52,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
    /*Store all the custom data of the font*/
    static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_ebike_trump_24 = {
#else
lv_font_t font_ebike_trump_24 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 28,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_EBIKE_TRUMP_24*/

