/*******************************************************************************
 * Size: 18 px
 * Bpp: 8
 * Opts: --bpp 8 --size 18 --no-compress --font Roboto-Medium.ttf --range 32-127,176 --format lvgl -o font_lv_demo_high_res_roboto_medium_18.c
 ******************************************************************************/

#include "../../../lvgl.h"

#if LV_USE_DEMO_HIGH_RES

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xae, 0xff, 0x8c, 0xa8, 0xff, 0x87, 0xa2, 0xff,
    0x81, 0x9d, 0xff, 0x7c, 0x97, 0xff, 0x76, 0x92,
    0xff, 0x71, 0x8c, 0xff, 0x6b, 0x86, 0xff, 0x65,
    0x81, 0xff, 0x60, 0x27, 0x50, 0x1c, 0x10, 0x50,
    0xc, 0xa8, 0xff, 0x97, 0x79, 0xf5, 0x68,

    /* U+0022 "\"" */
    0x1c, 0xff, 0x6c, 0x78, 0xff, 0x10, 0x1c, 0xff,
    0x65, 0x78, 0xff, 0x9, 0x1c, 0xff, 0x4e, 0x78,
    0xf2, 0x0, 0x1c, 0xff, 0x38, 0x78, 0xdc, 0x0,
    0x15, 0xc0, 0x1b, 0x5a, 0x96, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x0, 0x86, 0xf0, 0x2, 0x12,
    0xfe, 0x66, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc0,
    0xb7, 0x0, 0x4c, 0xff, 0x2b, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf5, 0x7d, 0x0, 0x87, 0xef, 0x1,
    0x0, 0x0, 0x7c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa8, 0x0, 0x36, 0x70, 0xa9,
    0xff, 0x7b, 0x70, 0xf8, 0xba, 0x70, 0x49, 0x0,
    0x0, 0x0, 0x84, 0xf5, 0x1, 0x11, 0xff, 0x66,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa9, 0xd0, 0x0,
    0x38, 0xff, 0x40, 0x0, 0x0, 0x5, 0x20, 0x20,
    0xd2, 0xb6, 0x20, 0x70, 0xff, 0x38, 0x20, 0x0,
    0x28, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xc, 0x50, 0x71, 0xff, 0x81,
    0x50, 0xd4, 0xce, 0x50, 0x50, 0x0, 0x0, 0x0,
    0x58, 0xff, 0x21, 0x0, 0xe6, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x86, 0xf2, 0x1, 0x13, 0xff,
    0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb5, 0xc6,
    0x0, 0x41, 0xff, 0x36, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x54, 0xff, 0x14, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x54, 0xff,
    0x14, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0xae,
    0xf2, 0xff, 0xdc, 0x72, 0x0, 0x0, 0x0, 0x11,
    0xe9, 0xff, 0xeb, 0xd3, 0xfd, 0xff, 0x7c, 0x0,
    0x0, 0x72, 0xff, 0xcd, 0x9, 0x0, 0x5f, 0xff,
    0xf2, 0xa, 0x0, 0x96, 0xff, 0x8a, 0x0, 0x0,
    0x1, 0xf1, 0xff, 0x2d, 0x0, 0x79, 0xff, 0xca,
    0x6, 0x0, 0x0, 0x22, 0x28, 0xa, 0x0, 0x19,
    0xec, 0xff, 0xd5, 0x5d, 0x7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd2, 0xff, 0xff, 0xea, 0x69,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x4b, 0xb4,
    0xfe, 0xff, 0x8b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfe, 0xfd, 0x1c, 0xd, 0xe0,
    0xe0, 0x11, 0x0, 0x0, 0x0, 0xd9, 0xff, 0x47,
    0x0, 0xe8, 0xff, 0x62, 0x0, 0x0, 0x16, 0xf4,
    0xff, 0x31, 0x0, 0x73, 0xff, 0xfc, 0xab, 0x98,
    0xe4, 0xff, 0xcf, 0x3, 0x0, 0x2, 0x7b, 0xf2,
    0xff, 0xff, 0xff, 0xbd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x99, 0xea, 0x16, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x84, 0xe4, 0x0, 0x0,
    0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x36, 0xc9, 0xf5, 0xd6, 0x4e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xe7, 0xce,
    0x5e, 0xb5, 0xf8, 0x1a, 0x0, 0x7, 0x6b, 0xb,
    0x0, 0x0, 0x1c, 0xff, 0x60, 0x0, 0x34, 0xff,
    0x48, 0x0, 0x83, 0xdc, 0x9, 0x0, 0x0, 0x7,
    0xf9, 0xa4, 0x19, 0x7f, 0xff, 0x2d, 0x2c, 0xf9,
    0x45, 0x0, 0x0, 0x0, 0x0, 0x68, 0xf8, 0xff,
    0xfe, 0x8e, 0x2, 0xc7, 0xa4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x36, 0x1a, 0x0, 0x6e,
    0xed, 0x16, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf3, 0x62, 0x0, 0x5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb5, 0xc0, 0x1e, 0xbf, 0xfc, 0xec, 0x66, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x59, 0xf9, 0x28, 0xb6,
    0xe4, 0x52, 0x9b, 0xfd, 0x2f, 0x0, 0x0, 0x0,
    0x12, 0xe9, 0x80, 0x0, 0xf3, 0x8b, 0x0, 0x13,
    0xff, 0x69, 0x0, 0x0, 0x0, 0xa0, 0xd8, 0x7,
    0x0, 0xf0, 0x8e, 0x0, 0x15, 0xff, 0x69, 0x0,
    0x0, 0x1, 0x86, 0x3f, 0x0, 0x0, 0xaf, 0xec,
    0x64, 0xa4, 0xfc, 0x2b, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xb1, 0xf2, 0xdd, 0x5b,
    0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x11, 0x99, 0xe4, 0xf1, 0xb8, 0x2a,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb5, 0xff,
    0xeb, 0xd7, 0xff, 0xe6, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x12, 0xff, 0xf1, 0x13, 0x0, 0xa6, 0xff,
    0x4e, 0x0, 0x0, 0x0, 0x0, 0x23, 0xff, 0xe7,
    0x0, 0x0, 0xa7, 0xff, 0x3f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd3, 0xff, 0x6d, 0x8b, 0xff, 0xbb,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xfc,
    0xff, 0xff, 0xa0, 0x9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x80, 0xfe, 0xff, 0xf2, 0x21, 0x0,
    0x13, 0x4c, 0x2e, 0x0, 0x0, 0x88, 0xff, 0xd2,
    0xa5, 0xff, 0xd3, 0xd, 0x51, 0xff, 0x93, 0x0,
    0x18, 0xfc, 0xfd, 0x21, 0x5, 0xc1, 0xff, 0xb4,
    0x8f, 0xff, 0x6d, 0x0, 0x39, 0xff, 0xee, 0x0,
    0x0, 0x14, 0xdf, 0xff, 0xfd, 0xfe, 0x23, 0x0,
    0x14, 0xfc, 0xff, 0x3e, 0x0, 0x0, 0x43, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x88, 0xff, 0xf7,
    0xa9, 0xab, 0xf1, 0xff, 0xff, 0xf6, 0x34, 0x0,
    0x0, 0x0, 0x58, 0xc8, 0xf0, 0xee, 0xca, 0x6f,
    0x84, 0xff, 0xe4, 0x19,

    /* U+0027 "'" */
    0x48, 0xff, 0x58, 0x48, 0xff, 0x51, 0x48, 0xff,
    0x42, 0x48, 0xff, 0x32, 0x31, 0xb0, 0x19,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x22, 0x69, 0x0, 0x0, 0x23,
    0xeb, 0xc3, 0x0, 0x5, 0xd4, 0xe6, 0x1d, 0x0,
    0x70, 0xff, 0x65, 0x0, 0x2, 0xe2, 0xe6, 0x4,
    0x0, 0x42, 0xff, 0x9d, 0x0, 0x0, 0x82, 0xff,
    0x5a, 0x0, 0x0, 0xb5, 0xff, 0x39, 0x0, 0x0,
    0xcd, 0xff, 0x20, 0x0, 0x0, 0xdb, 0xff, 0x11,
    0x0, 0x0, 0xd3, 0xff, 0x1c, 0x0, 0x0, 0xc1,
    0xff, 0x2f, 0x0, 0x0, 0x99, 0xff, 0x49, 0x0,
    0x0, 0x63, 0xff, 0x84, 0x0, 0x0, 0x14, 0xfa,
    0xc5, 0x0, 0x0, 0x0, 0xa6, 0xfe, 0x27, 0x0,
    0x0, 0x20, 0xf7, 0xaf, 0x0, 0x0, 0x0, 0x66,
    0xff, 0x75, 0x0, 0x0, 0x0, 0x75, 0xb8, 0x0,
    0x0, 0x0, 0x0, 0x9,

    /* U+0029 ")" */
    0x44, 0x4a, 0x0, 0x0, 0x0, 0x0, 0x74, 0xfe,
    0x57, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xf8, 0x28,
    0x0, 0x0, 0x0, 0x22, 0xfd, 0xbf, 0x0, 0x0,
    0x0, 0x0, 0xb1, 0xff, 0x35, 0x0, 0x0, 0x0,
    0x5b, 0xff, 0x96, 0x0, 0x0, 0x0, 0x20, 0xff,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0xf2, 0xff, 0xb,
    0x0, 0x0, 0x0, 0xe0, 0xff, 0x25, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x35, 0x0, 0x0, 0x0, 0xd9,
    0xff, 0x2b, 0x0, 0x0, 0x0, 0xec, 0xff, 0x18,
    0x0, 0x0, 0x8, 0xfc, 0xeb, 0x0, 0x0, 0x0,
    0x40, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x80, 0xff,
    0x5b, 0x0, 0x0, 0x2, 0xdc, 0xe9, 0x9, 0x0,
    0x0, 0x65, 0xff, 0x62, 0x0, 0x0, 0x36, 0xf3,
    0xad, 0x1, 0x0, 0x0, 0x76, 0xaf, 0x9, 0x0,
    0x0, 0x0, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x0, 0xbb, 0xca, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb1, 0xc0, 0x0, 0x0, 0x0,
    0x65, 0xa3, 0x3d, 0xa7, 0xb6, 0x27, 0x8d, 0x5b,
    0x7d, 0xf2, 0xff, 0xfc, 0xf7, 0xff, 0xfc, 0x8a,
    0x0, 0x6, 0x73, 0xff, 0xff, 0x78, 0x12, 0x0,
    0x0, 0xc, 0xd9, 0xca, 0xdd, 0xc4, 0x3, 0x0,
    0x0, 0x9e, 0xfc, 0x30, 0x4a, 0xff, 0x80, 0x0,
    0x0, 0x2e, 0x6e, 0x0, 0x0, 0x8b, 0x2a, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x7, 0x9c, 0x9c, 0x4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0,
    0x68, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x68, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x1, 0x4, 0x4, 0xf,
    0xff, 0xff, 0xb, 0x4, 0x4, 0x1, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0x8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0x8, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x0, 0xdc, 0xff, 0x10, 0x0, 0xdd, 0xff, 0xc,
    0x3, 0xf9, 0xea, 0x0, 0x4a, 0xff, 0x8e, 0x0,
    0x6d, 0xd6, 0x11, 0x0, 0x0, 0x2, 0x0, 0x0,

    /* U+002D "-" */
    0x49, 0xc4, 0xc4, 0xc4, 0xc4, 0x2d, 0x60, 0xff,
    0xff, 0xff, 0xff, 0x3c,

    /* U+002E "." */
    0x1c, 0x60, 0x10, 0xba, 0xff, 0x96, 0x85, 0xf5,
    0x64,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x21, 0xff, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x79, 0xff, 0x36, 0x0, 0x0,
    0x0, 0x0, 0xd2, 0xdd, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0x84, 0x0, 0x0, 0x0, 0x0, 0x84,
    0xff, 0x2c, 0x0, 0x0, 0x0, 0x0, 0xdc, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x35, 0xff, 0x7a, 0x0,
    0x0, 0x0, 0x0, 0x8e, 0xff, 0x22, 0x0, 0x0,
    0x0, 0x2, 0xe5, 0xc8, 0x0, 0x0, 0x0, 0x0,
    0x40, 0xff, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x99,
    0xfd, 0x19, 0x0, 0x0, 0x0, 0x5, 0xec, 0xbe,
    0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0x65, 0x0,
    0x0, 0x0, 0x0, 0xa3, 0xfa, 0x12, 0x0, 0x0,
    0x0, 0x0, 0x73, 0x65, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x33, 0xb8, 0xf1, 0xf6, 0xcb, 0x51,
    0x0, 0x0, 0x0, 0x27, 0xf3, 0xff, 0xe6, 0xdb,
    0xff, 0xfe, 0x51, 0x0, 0x0, 0xa0, 0xff, 0xac,
    0x2, 0x0, 0x7e, 0xff, 0xd6, 0x0, 0x0, 0xe6,
    0xff, 0x3b, 0x0, 0x0, 0xe, 0xfb, 0xff, 0x1c,
    0x8, 0xff, 0xff, 0x18, 0x0, 0x0, 0x0, 0xe6,
    0xff, 0x3e, 0x12, 0xff, 0xff, 0xc, 0x0, 0x0,
    0x0, 0xd8, 0xff, 0x4b, 0x14, 0xff, 0xff, 0xc,
    0x0, 0x0, 0x0, 0xd8, 0xff, 0x4c, 0x13, 0xff,
    0xff, 0xc, 0x0, 0x0, 0x0, 0xd8, 0xff, 0x4a,
    0x6, 0xff, 0xff, 0x1a, 0x0, 0x0, 0x0, 0xe5,
    0xff, 0x3d, 0x0, 0xe2, 0xff, 0x40, 0x0, 0x0,
    0xc, 0xfb, 0xff, 0x1a, 0x0, 0x9a, 0xff, 0xb2,
    0x3, 0x0, 0x7b, 0xff, 0xd3, 0x0, 0x0, 0x21,
    0xf1, 0xff, 0xe8, 0xd9, 0xff, 0xfe, 0x4e, 0x0,
    0x0, 0x0, 0x2e, 0xb5, 0xf2, 0xf7, 0xcb, 0x4f,
    0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x2, 0x43, 0xa3, 0xb1, 0x1f, 0x84,
    0xe2, 0xff, 0xff, 0xbc, 0x84, 0xff, 0xf2, 0xd1,
    0xff, 0xbc, 0x4e, 0x55, 0x9, 0x64, 0xff, 0xbc,
    0x0, 0x0, 0x0, 0x64, 0xff, 0xbc, 0x0, 0x0,
    0x0, 0x64, 0xff, 0xbc, 0x0, 0x0, 0x0, 0x64,
    0xff, 0xbc, 0x0, 0x0, 0x0, 0x64, 0xff, 0xbc,
    0x0, 0x0, 0x0, 0x64, 0xff, 0xbc, 0x0, 0x0,
    0x0, 0x64, 0xff, 0xbc, 0x0, 0x0, 0x0, 0x64,
    0xff, 0xbc, 0x0, 0x0, 0x0, 0x64, 0xff, 0xbc,
    0x0, 0x0, 0x0, 0x64, 0xff, 0xbc,

    /* U+0032 "2" */
    0x0, 0x0, 0x49, 0xbf, 0xf0, 0xef, 0xc6, 0x54,
    0x0, 0x0, 0x0, 0x60, 0xff, 0xff, 0xdc, 0xdc,
    0xff, 0xff, 0x71, 0x0, 0x7, 0xec, 0xff, 0x6c,
    0x0, 0x0, 0x89, 0xff, 0xea, 0x0, 0x36, 0xff,
    0xee, 0x1, 0x0, 0x0, 0x21, 0xff, 0xff, 0x8,
    0x7, 0x1c, 0x17, 0x0, 0x0, 0x0, 0x37, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb2, 0xff, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x78, 0xff, 0xd7, 0x8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xfe, 0xee, 0x25, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4d, 0xfb, 0xf4, 0x39, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xf7, 0xf9, 0x45,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x31, 0xf1, 0xfc,
    0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe9,
    0xff, 0xf0, 0xc4, 0xc4, 0xc4, 0xc4, 0xc4, 0x6e,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90,

    /* U+0033 "3" */
    0x0, 0x0, 0x5e, 0xca, 0xf5, 0xea, 0xc0, 0x4a,
    0x0, 0x0, 0x0, 0x81, 0xff, 0xff, 0xd4, 0xdb,
    0xff, 0xff, 0x65, 0x0, 0xd, 0xf8, 0xff, 0x52,
    0x0, 0x0, 0x7d, 0xff, 0xdd, 0x0, 0x10, 0x64,
    0x62, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x83, 0xff,
    0xb8, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xbd, 0xdb,
    0xff, 0xcf, 0x1c, 0x0, 0x0, 0x0, 0x0, 0x94,
    0xff, 0xff, 0xff, 0xac, 0x12, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x21, 0xad, 0xff, 0xbb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0xfe,
    0xff, 0x19, 0x31, 0xb0, 0x9c, 0x0, 0x0, 0x0,
    0x4, 0xfb, 0xff, 0x27, 0x1e, 0xfe, 0xfe, 0x4d,
    0x0, 0x0, 0x6d, 0xff, 0xed, 0x4, 0x0, 0x93,
    0xff, 0xff, 0xd5, 0xdb, 0xff, 0xff, 0x63, 0x0,
    0x0, 0x1, 0x5b, 0xc9, 0xf1, 0xf5, 0xc2, 0x4c,
    0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x56, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0xe8,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9d, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x41, 0xff, 0xac, 0xf4, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x8, 0xdb, 0xf1, 0x1a, 0xf4, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x88, 0xff, 0x6b, 0x0,
    0xf4, 0xff, 0x30, 0x0, 0x0, 0x2f, 0xfb, 0xc8,
    0x2, 0x0, 0xf4, 0xff, 0x30, 0x0, 0x3, 0xcb,
    0xfb, 0x2e, 0x0, 0x0, 0xf4, 0xff, 0x30, 0x0,
    0x68, 0xff, 0xf2, 0xc4, 0xc4, 0xc4, 0xfd, 0xff,
    0xcf, 0x99, 0x82, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf4, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0xff,
    0x30, 0x0,

    /* U+0035 "5" */
    0x0, 0xd6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x18, 0x0, 0xf2, 0xff, 0xe8, 0xe8, 0xe8, 0xe8,
    0xe8, 0x15, 0xd, 0xff, 0xec, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x29, 0xff, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x45, 0xff, 0xca, 0x95,
    0xbf, 0xae, 0x57, 0x0, 0x0, 0x61, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x84, 0x0, 0x2e, 0x96,
    0x87, 0x18, 0x13, 0x88, 0xff, 0xfd, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xd5, 0xff, 0x67,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa9, 0xff,
    0x7e, 0x94, 0xb8, 0x37, 0x0, 0x0, 0x0, 0xc2,
    0xff, 0x6e, 0x98, 0xff, 0xb4, 0x7, 0x0, 0x41,
    0xfe, 0xff, 0x2d, 0x1d, 0xe9, 0xff, 0xe9, 0xd2,
    0xfc, 0xff, 0x9e, 0x0, 0x0, 0x20, 0xa5, 0xec,
    0xfa, 0xd9, 0x77, 0x3, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x5, 0x69, 0xc1, 0xec, 0x9a, 0x0,
    0x0, 0x0, 0x15, 0xcb, 0xff, 0xff, 0xf0, 0x83,
    0x0, 0x0, 0x1, 0xbb, 0xff, 0xb8, 0x2c, 0x0,
    0x0, 0x0, 0x0, 0x47, 0xff, 0xcc, 0x7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa0, 0xff, 0x92, 0xbc,
    0xf7, 0xeb, 0x92, 0x8, 0x0, 0xd6, 0xff, 0xfe,
    0xef, 0xd8, 0xfe, 0xff, 0x9d, 0x0, 0xed, 0xff,
    0xb4, 0xb, 0x0, 0x53, 0xff, 0xfc, 0x24, 0xf7,
    0xff, 0x32, 0x0, 0x0, 0x0, 0xcd, 0xff, 0x5a,
    0xed, 0xff, 0x3a, 0x0, 0x0, 0x0, 0xac, 0xff,
    0x75, 0xc6, 0xff, 0x6a, 0x0, 0x0, 0x0, 0xca,
    0xff, 0x5f, 0x6f, 0xff, 0xdd, 0x18, 0x0, 0x4a,
    0xff, 0xfa, 0x1b, 0x6, 0xca, 0xff, 0xf6, 0xd7,
    0xfd, 0xff, 0x80, 0x0, 0x0, 0xf, 0x92, 0xe7,
    0xf9, 0xd5, 0x66, 0x0, 0x0,

    /* U+0037 "7" */
    0x64, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x78, 0x4c, 0xc4, 0xc4, 0xc4, 0xc4, 0xc4,
    0xc4, 0xf6, 0xff, 0x56, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x21, 0xfc, 0xe4, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x92, 0xff, 0x7a, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0xf4, 0xf7,
    0x14, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7a,
    0xff, 0x9c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xe6, 0xff, 0x2e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x62, 0xff, 0xbe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xd4, 0xff, 0x4f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xde,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbe,
    0xff, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x32, 0xff, 0xf3, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa6, 0xff, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x37, 0xb7, 0xe9, 0xf0, 0xc8, 0x54,
    0x0, 0x0, 0x0, 0x3e, 0xfb, 0xff, 0xe2, 0xd7,
    0xff, 0xff, 0x6e, 0x0, 0x0, 0xad, 0xff, 0xaa,
    0x2, 0x0, 0x7a, 0xff, 0xe3, 0x0, 0x0, 0xc4,
    0xff, 0x62, 0x0, 0x0, 0x27, 0xff, 0xfa, 0x0,
    0x0, 0x8b, 0xff, 0xab, 0x3, 0x0, 0x75, 0xff,
    0xc1, 0x0, 0x0, 0xe, 0xc2, 0xff, 0xe5, 0xd9,
    0xff, 0xde, 0x25, 0x0, 0x0, 0x6, 0x9b, 0xff,
    0xff, 0xff, 0xff, 0xbd, 0x17, 0x0, 0x0, 0x92,
    0xff, 0xbc, 0x1f, 0x13, 0x96, 0xff, 0xc4, 0x0,
    0x1, 0xf5, 0xff, 0x2a, 0x0, 0x0, 0x5, 0xf2,
    0xff, 0x2e, 0xd, 0xff, 0xff, 0x1b, 0x0, 0x0,
    0x0, 0xe7, 0xff, 0x44, 0x0, 0xde, 0xff, 0x88,
    0x0, 0x0, 0x52, 0xff, 0xfd, 0x19, 0x0, 0x58,
    0xff, 0xff, 0xe0, 0xd4, 0xfe, 0xff, 0x8f, 0x0,
    0x0, 0x0, 0x4a, 0xc1, 0xf4, 0xf9, 0xd1, 0x6a,
    0x1, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x39, 0xba, 0xf1, 0xf1, 0xaf, 0x25,
    0x0, 0x0, 0x0, 0x3c, 0xf9, 0xff, 0xe2, 0xea,
    0xff, 0xe9, 0x1b, 0x0, 0x0, 0xca, 0xff, 0x91,
    0x0, 0x5, 0xb3, 0xff, 0x9d, 0x0, 0x14, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x2e, 0xff, 0xf0, 0x1,
    0x27, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4, 0xfd,
    0xff, 0x1a, 0xa, 0xfe, 0xff, 0x1e, 0x0, 0x0,
    0x8, 0xfa, 0xff, 0x27, 0x0, 0xc1, 0xff, 0xb6,
    0x1a, 0x1b, 0xaa, 0xff, 0xff, 0x1e, 0x0, 0x33,
    0xf5, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xff, 0xc,
    0x0, 0x0, 0x28, 0x93, 0xb2, 0x8a, 0x49, 0xff,
    0xdb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x95, 0xff, 0x8b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x85, 0xff, 0xf1, 0x1c, 0x0, 0x0, 0x0,
    0x45, 0xe5, 0xfe, 0xff, 0xf5, 0x48, 0x0, 0x0,
    0x0, 0x0, 0x53, 0xf5, 0xd7, 0x91, 0x1f, 0x0,
    0x0, 0x0,

    /* U+003A ":" */
    0x8c, 0xf3, 0x55, 0xc8, 0xff, 0x8c, 0x22, 0x61,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x5f, 0xc,
    0xc6, 0xff, 0x8a, 0x90, 0xf5, 0x5a,

    /* U+003B ";" */
    0x0, 0xb9, 0xec, 0x33, 0x3, 0xf5, 0xff, 0x60,
    0x0, 0x34, 0x5a, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x14, 0x4,
    0x0, 0xb4, 0xff, 0x38, 0x0, 0xb6, 0xff, 0x33,
    0x0, 0xd6, 0xfc, 0x11, 0x2a, 0xfe, 0xac, 0x0,
    0x44, 0xd0, 0x1e, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x5a, 0xb7,
    0x0, 0x0, 0x0, 0xc, 0x6f, 0xe0, 0xff, 0xe8,
    0x0, 0x17, 0x84, 0xee, 0xff, 0xff, 0xc4, 0x59,
    0x54, 0xf8, 0xff, 0xee, 0x92, 0x2b, 0x0, 0x0,
    0x74, 0xff, 0xf7, 0x72, 0xf, 0x0, 0x0, 0x0,
    0x1a, 0x91, 0xf4, 0xff, 0xf5, 0xa1, 0x3c, 0x0,
    0x0, 0x0, 0x12, 0x7d, 0xe9, 0xff, 0xff, 0xc7,
    0x0, 0x0, 0x0, 0x0, 0x8, 0x69, 0xdb, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x47,

    /* U+003D "=" */
    0x98, 0xd4, 0xd4, 0xd4, 0xd4, 0xd4, 0xd4, 0xb2,
    0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x95, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xaf,
    0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8,

    /* U+003E ">" */
    0xb4, 0x63, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe0, 0xff, 0xe9, 0x7f, 0x16, 0x0, 0x0, 0x0,
    0x4b, 0xb2, 0xfb, 0xff, 0xf7, 0x9c, 0x2b, 0x0,
    0x0, 0x0, 0x17, 0x74, 0xd5, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x8, 0x5a, 0xe1, 0xff, 0xb8,
    0x0, 0x33, 0x95, 0xef, 0xff, 0xfc, 0xab, 0x34,
    0xbc, 0xff, 0xff, 0xf1, 0x8f, 0x20, 0x0, 0x0,
    0xe0, 0xdf, 0x72, 0xe, 0x0, 0x0, 0x0, 0x0,
    0x44, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x16, 0x97, 0xdf, 0xf5, 0xd5, 0x71, 0x4,
    0x0, 0x9, 0xda, 0xff, 0xf8, 0xe2, 0xfe, 0xff,
    0x86, 0x0, 0x57, 0xff, 0xe6, 0x1a, 0x0, 0x5c,
    0xff, 0xf0, 0x0, 0x2b, 0x60, 0x42, 0x0, 0x0,
    0x15, 0xff, 0xff, 0xe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3b, 0xf3, 0xfd, 0x47, 0x0, 0x0, 0x0,
    0x0, 0x2a, 0xf1, 0xfd, 0x63, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x83, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe1, 0xff, 0x29, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0x18, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0x46, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf5, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xc7,
    0xe8, 0x24, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x57, 0xb6, 0xeb,
    0xf9, 0xe0, 0xb9, 0x57, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0xbd, 0xff, 0xbe, 0x7e,
    0x63, 0x74, 0xad, 0xfa, 0xc6, 0x14, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xd4, 0xeb, 0x3e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xdb, 0xce, 0x3, 0x0,
    0x0, 0x0, 0x96, 0xf7, 0x2a, 0x0, 0x0, 0x0,
    0x10, 0x2, 0x0, 0x0, 0x22, 0xf6, 0x6c, 0x0,
    0x0, 0x26, 0xfe, 0x7e, 0x0, 0x0, 0x30, 0xcf,
    0xff, 0xf7, 0xa0, 0xf, 0x0, 0x95, 0xcf, 0x0,
    0x0, 0x82, 0xfb, 0x14, 0x0, 0x1c, 0xee, 0xdc,
    0x64, 0x9e, 0xff, 0x39, 0x0, 0x47, 0xfe, 0x17,
    0x0, 0xcf, 0xbe, 0x0, 0x0, 0x9f, 0xfd, 0x25,
    0x0, 0x6f, 0xff, 0x22, 0x0, 0x1e, 0xff, 0x37,
    0x0, 0xf3, 0x94, 0x0, 0x7, 0xf4, 0xbb, 0x0,
    0x0, 0x85, 0xff, 0xb, 0x0, 0x13, 0xff, 0x43,
    0x14, 0xff, 0x76, 0x0, 0x31, 0xff, 0x8a, 0x0,
    0x0, 0x9b, 0xf4, 0x0, 0x0, 0x1b, 0xff, 0x3d,
    0x10, 0xff, 0x77, 0x0, 0x45, 0xff, 0x6f, 0x0,
    0x0, 0xb0, 0xdd, 0x0, 0x0, 0x43, 0xff, 0x15,
    0x2, 0xff, 0x83, 0x0, 0x3a, 0xff, 0x7f, 0x0,
    0x8, 0xe7, 0xd5, 0x0, 0x0, 0xa0, 0xce, 0x0,
    0x0, 0xdc, 0xb7, 0x0, 0x5, 0xec, 0xe6, 0x5b,
    0xb0, 0xe5, 0xfc, 0x66, 0x88, 0xfe, 0x4c, 0x0,
    0x0, 0x93, 0xf3, 0x11, 0x0, 0x4c, 0xe2, 0xf2,
    0x8d, 0x18, 0xbe, 0xf8, 0xdc, 0x60, 0x0, 0x0,
    0x0, 0x32, 0xfd, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x93, 0xff, 0x7f, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x97, 0xff, 0xe1, 0x8d, 0x6c,
    0x72, 0x9c, 0xdd, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3b, 0xa2, 0xd8, 0xf4,
    0xed, 0xcb, 0x7f, 0x6, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0x2b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x8a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xe9, 0xfa, 0xfd, 0xe6,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xb4, 0xbd, 0xff, 0x49, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xad, 0xff, 0x5a, 0x63, 0xff,
    0xa8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0xf9,
    0xf4, 0xb, 0x10, 0xf8, 0xf7, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xa6, 0x0, 0x0, 0xaf,
    0xff, 0x67, 0x0, 0x0, 0x0, 0x0, 0xcc, 0xff,
    0x4c, 0x0, 0x0, 0x55, 0xff, 0xc7, 0x0, 0x0,
    0x0, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x26, 0x0, 0x0, 0x8a, 0xff, 0xea,
    0xd8, 0xd8, 0xd8, 0xd8, 0xec, 0xff, 0x85, 0x0,
    0x3, 0xe6, 0xff, 0x48, 0x0, 0x0, 0x0, 0x0,
    0x50, 0xff, 0xe2, 0x2, 0x49, 0xff, 0xef, 0x5,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf3, 0xff, 0x44,
    0xa8, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa6, 0xff, 0xa4,

    /* U+0042 "B" */
    0xb4, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xc9, 0x69,
    0x2, 0x0, 0xb4, 0xff, 0xed, 0xd8, 0xd9, 0xef,
    0xff, 0xff, 0x8d, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x2, 0x7f, 0xff, 0xf7, 0x7, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x24, 0xff, 0xff, 0x17,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x73, 0xff,
    0xdc, 0x1, 0xb4, 0xff, 0xdc, 0xb4, 0xb5, 0xd3,
    0xff, 0xe2, 0x34, 0x0, 0xb4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd5, 0x3c, 0x0, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0xd, 0x72, 0xff, 0xf1, 0x12,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x5a, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0xd9, 0xff, 0x67, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xff, 0x37, 0xb4, 0xff,
    0xeb, 0xd4, 0xd4, 0xe7, 0xff, 0xff, 0xb2, 0x0,
    0xb4, 0xff, 0xff, 0xff, 0xff, 0xee, 0xcc, 0x6b,
    0x5, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x47, 0xb8, 0xea, 0xf8, 0xd7,
    0x82, 0xa, 0x0, 0x0, 0x0, 0x0, 0x7b, 0xff,
    0xff, 0xf0, 0xe8, 0xff, 0xff, 0xcf, 0xd, 0x0,
    0x0, 0x3e, 0xfe, 0xfc, 0x60, 0x2, 0x0, 0x29,
    0xdf, 0xff, 0x89, 0x0, 0x0, 0xaf, 0xff, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x61, 0xff, 0xe4, 0x0,
    0x0, 0xf4, 0xff, 0x4b, 0x0, 0x0, 0x0, 0x0,
    0x14, 0x64, 0x63, 0x2, 0x10, 0xff, 0xff, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x13, 0xff, 0xff, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf5, 0xff, 0x49, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x54, 0x54, 0x2, 0x0, 0xb4, 0xff, 0x9d,
    0x0, 0x0, 0x0, 0x0, 0x60, 0xff, 0xe7, 0x0,
    0x0, 0x47, 0xff, 0xf9, 0x55, 0x0, 0x0, 0x2a,
    0xdf, 0xff, 0x8c, 0x0, 0x0, 0x0, 0x89, 0xff,
    0xff, 0xec, 0xe9, 0xff, 0xff, 0xcf, 0xe, 0x0,
    0x0, 0x0, 0x0, 0x52, 0xc0, 0xef, 0xf8, 0xd5,
    0x80, 0xa, 0x0, 0x0,

    /* U+0044 "D" */
    0xb4, 0xff, 0xff, 0xff, 0xf2, 0xd0, 0x79, 0xe,
    0x0, 0x0, 0xb4, 0xff, 0xed, 0xd8, 0xe1, 0xfe,
    0xff, 0xda, 0x24, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x1f, 0xbc, 0xff, 0xcd, 0x5, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x13, 0xf3, 0xff, 0x52,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x9d,
    0xff, 0xa4, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x79, 0xff, 0xc5, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x64, 0xff, 0xd7, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x79, 0xff, 0xc5,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xa4, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x17, 0xf4, 0xff, 0x51, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x22, 0xc2, 0xff, 0xcb, 0x4, 0xb4, 0xff,
    0xeb, 0xd4, 0xdf, 0xff, 0xff, 0xd8, 0x22, 0x0,
    0xb4, 0xff, 0xff, 0xff, 0xf1, 0xce, 0x77, 0xd,
    0x0, 0x0,

    /* U+0045 "E" */
    0xb4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x9c, 0xb4, 0xff, 0xed, 0xd8, 0xd8, 0xd8, 0xd8,
    0xd8, 0x83, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xeb,
    0xd4, 0xd4, 0xd4, 0xd4, 0xa5, 0x0, 0xb4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x0, 0xb4,
    0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0xeb, 0xd4, 0xd4,
    0xd4, 0xd4, 0xd4, 0x8e, 0xb4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xac,

    /* U+0046 "F" */
    0xb4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0xb4, 0xff, 0xed, 0xd8, 0xd8, 0xd8, 0xd8,
    0xd8, 0x5e, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xed,
    0xd8, 0xd8, 0xd8, 0xd8, 0x91, 0x0, 0xb4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xac, 0x0, 0xb4,
    0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x54, 0xbf, 0xf0, 0xf9, 0xde,
    0x95, 0x19, 0x0, 0x0, 0x0, 0x0, 0x8a, 0xff,
    0xff, 0xef, 0xe2, 0xfd, 0xff, 0xe7, 0x21, 0x0,
    0x0, 0x42, 0xff, 0xfe, 0x68, 0x2, 0x0, 0x1d,
    0xc9, 0xff, 0xaf, 0x0, 0x0, 0xaf, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x37, 0xec, 0xe5, 0x5,
    0x0, 0xeb, 0xff, 0x58, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x39,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x2c, 0x0, 0x0, 0xbc, 0xff,
    0xff, 0xff, 0xff, 0x1c, 0x5, 0xfe, 0xff, 0x3d,
    0x0, 0x0, 0x8d, 0xc0, 0xc7, 0xff, 0xff, 0x1c,
    0x0, 0xe7, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0x1c, 0x0, 0xa0, 0xff, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x1c,
    0x0, 0x31, 0xfb, 0xff, 0x86, 0x8, 0x0, 0x2,
    0x6e, 0xff, 0xff, 0x1c, 0x0, 0x0, 0x68, 0xfc,
    0xff, 0xf5, 0xdf, 0xf9, 0xff, 0xff, 0xae, 0x4,
    0x0, 0x0, 0x0, 0x39, 0xab, 0xe5, 0xfa, 0xe8,
    0xb5, 0x57, 0x1, 0x0,

    /* U+0048 "H" */
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0x78, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc0, 0xff, 0x78, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc0, 0xff,
    0x78, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc0, 0xff, 0x78, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc0, 0xff, 0x78, 0xb4,
    0xff, 0xed, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xf6,
    0xff, 0x78, 0xb4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x78, 0xb4, 0xff, 0x88,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc0, 0xff, 0x78,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0x78, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc0, 0xff, 0x78, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc0, 0xff,
    0x78, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc0, 0xff, 0x78, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc0, 0xff, 0x78,

    /* U+0049 "I" */
    0x90, 0xff, 0xa4, 0x90, 0xff, 0xa4, 0x90, 0xff,
    0xa4, 0x90, 0xff, 0xa4, 0x90, 0xff, 0xa4, 0x90,
    0xff, 0xa4, 0x90, 0xff, 0xa4, 0x90, 0xff, 0xa4,
    0x90, 0xff, 0xa4, 0x90, 0xff, 0xa4, 0x90, 0xff,
    0xa4, 0x90, 0xff, 0xa4, 0x90, 0xff, 0xa4,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x74, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x74,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x74, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x74, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x74, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x74, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x74, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x74, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x74, 0xff,
    0xbf, 0x80, 0xe0, 0x96, 0x0, 0x0, 0x0, 0x8a,
    0xff, 0xb4, 0x66, 0xff, 0xef, 0x25, 0x0, 0x20,
    0xe7, 0xff, 0x7b, 0xb, 0xda, 0xff, 0xfc, 0xe2,
    0xfb, 0xff, 0xde, 0x10, 0x0, 0x17, 0x9b, 0xe5,
    0xfb, 0xe8, 0x9c, 0x17, 0x0,

    /* U+004B "K" */
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x10, 0xd9,
    0xff, 0xc1, 0x5, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x3, 0xb8, 0xff, 0xdf, 0x14, 0x0, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x8e, 0xff, 0xf3, 0x2d, 0x0,
    0x0, 0xb4, 0xff, 0x88, 0x0, 0x60, 0xff, 0xfe,
    0x4f, 0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x39,
    0xf8, 0xff, 0x7a, 0x0, 0x0, 0x0, 0x0, 0xb4,
    0xff, 0xa0, 0xe6, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0xf, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0xff,
    0xcd, 0xe2, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0xff, 0xdb, 0x16, 0x44, 0xfe, 0xff, 0x56,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x93, 0xff, 0xed, 0x1a, 0x0, 0x0, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x9, 0xd9, 0xff, 0xb9, 0x1,
    0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x37,
    0xfc, 0xff, 0x6c, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x84, 0xff, 0xf6, 0x28,

    /* U+004C "L" */
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0x88,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb4,
    0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb4, 0xff, 0xeb, 0xd4, 0xd4,
    0xd4, 0xd4, 0xd4, 0x45, 0xb4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x54,

    /* U+004D "M" */
    0xb4, 0xff, 0xff, 0x5e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa0, 0xff, 0xff, 0x70, 0xb4, 0xff,
    0xff, 0xbd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf3, 0xff, 0xff, 0x70, 0xb4, 0xff, 0xff, 0xfe,
    0x1e, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0xff,
    0xff, 0x70, 0xb4, 0xff, 0xd3, 0xff, 0x7b, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0xff, 0xd3, 0xff, 0x70,
    0xb4, 0xff, 0x7d, 0xfe, 0xd9, 0x0, 0x0, 0x0,
    0x1c, 0xfd, 0xdc, 0x9f, 0xff, 0x70, 0xb4, 0xff,
    0x64, 0xc2, 0xff, 0x38, 0x0, 0x0, 0x79, 0xff,
    0x7c, 0xa9, 0xff, 0x70, 0xb4, 0xff, 0x6e, 0x62,
    0xff, 0x97, 0x0, 0x0, 0xd7, 0xfd, 0x1e, 0xb3,
    0xff, 0x70, 0xb4, 0xff, 0x78, 0xc, 0xf4, 0xee,
    0x7, 0x36, 0xff, 0xbb, 0x0, 0xbd, 0xff, 0x70,
    0xb4, 0xff, 0x81, 0x0, 0xa1, 0xff, 0x54, 0x95,
    0xff, 0x5a, 0x0, 0xc5, 0xff, 0x70, 0xb4, 0xff,
    0x84, 0x0, 0x41, 0xff, 0xb9, 0xed, 0xf0, 0x9,
    0x0, 0xc8, 0xff, 0x70, 0xb4, 0xff, 0x84, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0x99, 0x0, 0x0, 0xc8,
    0xff, 0x70, 0xb4, 0xff, 0x84, 0x0, 0x0, 0x81,
    0xff, 0xff, 0x38, 0x0, 0x0, 0xc8, 0xff, 0x70,
    0xb4, 0xff, 0x84, 0x0, 0x0, 0x21, 0xfe, 0xd7,
    0x0, 0x0, 0x0, 0xc8, 0xff, 0x70,

    /* U+004E "N" */
    0xb4, 0xff, 0xd1, 0x4, 0x0, 0x0, 0x0, 0x0,
    0xc0, 0xff, 0x74, 0xb4, 0xff, 0xff, 0x75, 0x0,
    0x0, 0x0, 0x0, 0xc0, 0xff, 0x74, 0xb4, 0xff,
    0xff, 0xf4, 0x1e, 0x0, 0x0, 0x0, 0xc0, 0xff,
    0x74, 0xb4, 0xff, 0xfc, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0xc0, 0xff, 0x74, 0xb4, 0xff, 0x9f, 0xef,
    0xff, 0x4f, 0x0, 0x0, 0xc0, 0xff, 0x74, 0xb4,
    0xff, 0x88, 0x68, 0xff, 0xe1, 0xb, 0x0, 0xc0,
    0xff, 0x74, 0xb4, 0xff, 0x88, 0x1, 0xc7, 0xff,
    0x8b, 0x0, 0xc0, 0xff, 0x74, 0xb4, 0xff, 0x88,
    0x0, 0x2f, 0xfb, 0xfb, 0x2d, 0xc0, 0xff, 0x74,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x8d, 0xff, 0xc5,
    0xc1, 0xff, 0x74, 0xb4, 0xff, 0x88, 0x0, 0x0,
    0xc, 0xe2, 0xff, 0xfa, 0xff, 0x74, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x50, 0xff, 0xff, 0xff,
    0x74, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0,
    0xb2, 0xff, 0xff, 0x74, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf4, 0xff, 0x74,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x40, 0xb3, 0xe8, 0xf4, 0xd1,
    0x73, 0x7, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xfe,
    0xff, 0xfa, 0xf1, 0xff, 0xff, 0xc6, 0xe, 0x0,
    0x0, 0x32, 0xfc, 0xff, 0x7d, 0x9, 0x0, 0x37,
    0xe1, 0xff, 0x97, 0x0, 0x0, 0xa4, 0xff, 0xb8,
    0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xfa, 0x10,
    0x0, 0xed, 0xff, 0x56, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xe8, 0xff, 0x54, 0xb, 0xff, 0xff, 0x32,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc7, 0xff, 0x73,
    0x1b, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0xff, 0x83, 0xc, 0xff, 0xff, 0x33,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbd, 0xff, 0x73,
    0x0, 0xec, 0xff, 0x55, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe2, 0xff, 0x56, 0x0, 0xa2, 0xff, 0xb6,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xff, 0xfb, 0x11,
    0x0, 0x30, 0xfb, 0xff, 0x79, 0x7, 0x0, 0x2f,
    0xdb, 0xff, 0x99, 0x0, 0x0, 0x0, 0x69, 0xfd,
    0xff, 0xf7, 0xec, 0xff, 0xff, 0xc8, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0xb1, 0xe9, 0xf5, 0xd3,
    0x78, 0x9, 0x0, 0x0,

    /* U+0050 "P" */
    0xb4, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xdc, 0x8d,
    0x11, 0x0, 0xb4, 0xff, 0xef, 0xdc, 0xdc, 0xe7,
    0xff, 0xff, 0xdb, 0x13, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x2f, 0xe9, 0xff, 0x8d, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x7b, 0xff, 0xce,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x73,
    0xff, 0xd1, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x21, 0xdd, 0xff, 0x99, 0xb4, 0xff, 0xed, 0xd8,
    0xd8, 0xe1, 0xfd, 0xff, 0xe9, 0x1f, 0xb4, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xe0, 0x99, 0x1c, 0x0,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x46, 0xb8, 0xea, 0xf3, 0xcf,
    0x71, 0x6, 0x0, 0x0, 0x0, 0x0, 0x79, 0xff,
    0xff, 0xf8, 0xf2, 0xff, 0xff, 0xc1, 0xa, 0x0,
    0x0, 0x3c, 0xfe, 0xfe, 0x72, 0x6, 0x0, 0x3d,
    0xe6, 0xff, 0x8d, 0x0, 0x0, 0xb0, 0xff, 0xa8,
    0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xf5, 0xb,
    0x2, 0xf7, 0xff, 0x45, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xf0, 0xff, 0x4b, 0x18, 0xff, 0xff, 0x21,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0xff, 0x68,
    0x27, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc4, 0xff, 0x77, 0x18, 0xff, 0xff, 0x23,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcd, 0xff, 0x68,
    0x2, 0xf6, 0xff, 0x47, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0x4a, 0x0, 0xae, 0xff, 0xa9,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xff, 0xf7, 0xb,
    0x0, 0x3a, 0xfd, 0xfe, 0x70, 0x5, 0x0, 0x37,
    0xe3, 0xff, 0x89, 0x0, 0x0, 0x0, 0x75, 0xfe,
    0xff, 0xf6, 0xed, 0xff, 0xff, 0xbb, 0x9, 0x0,
    0x0, 0x0, 0x0, 0x45, 0xb7, 0xeb, 0xf5, 0xf4,
    0xff, 0xcd, 0x1e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xce, 0xff, 0xea, 0x2e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xa8, 0xa6, 0x5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+0052 "R" */
    0xb4, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xcc, 0x70,
    0x4, 0x0, 0xb4, 0xff, 0xef, 0xdc, 0xdc, 0xf1,
    0xff, 0xff, 0xaa, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0x6a, 0xff, 0xff, 0x34, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x63,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x1, 0xe7,
    0xff, 0x55, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x19,
    0x95, 0xff, 0xf4, 0x13, 0xb4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x50, 0x0, 0xb4, 0xff,
    0xed, 0xd8, 0xd8, 0xfe, 0xff, 0x47, 0x0, 0x0,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0xad, 0xff, 0xad,
    0x0, 0x0, 0xb4, 0xff, 0x88, 0x0, 0x0, 0x2e,
    0xfe, 0xfe, 0x2f, 0x0, 0xb4, 0xff, 0x88, 0x0,
    0x0, 0x0, 0xae, 0xff, 0xb0, 0x0, 0xb4, 0xff,
    0x88, 0x0, 0x0, 0x0, 0x30, 0xfe, 0xff, 0x32,
    0xb4, 0xff, 0x88, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0xff, 0xb3,

    /* U+0053 "S" */
    0x0, 0x0, 0x22, 0x9d, 0xda, 0xf7, 0xe7, 0xa7,
    0x2e, 0x0, 0x0, 0x0, 0x3b, 0xf6, 0xff, 0xf6,
    0xdc, 0xf5, 0xff, 0xfa, 0x47, 0x0, 0x0, 0xc6,
    0xff, 0xb3, 0xd, 0x0, 0x8, 0x9c, 0xff, 0xe0,
    0x3, 0x0, 0xec, 0xff, 0x52, 0x0, 0x0, 0x0,
    0x16, 0xe8, 0xe8, 0x20, 0x0, 0xbf, 0xff, 0xb6,
    0xd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x30, 0xf0, 0xff, 0xec, 0x91, 0x39, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x21, 0xa6, 0xfa, 0xff,
    0xff, 0xda, 0x56, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x66, 0xc3, 0xff, 0xff, 0x79, 0x0,
    0x2, 0x8, 0x7, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0xff, 0xf7, 0xf, 0x45, 0xff, 0xf6, 0x4, 0x0,
    0x0, 0x0, 0x11, 0xff, 0xff, 0x30, 0xd, 0xed,
    0xff, 0x91, 0x9, 0x0, 0x2, 0x78, 0xff, 0xfc,
    0x13, 0x0, 0x4e, 0xf9, 0xff, 0xf6, 0xdb, 0xee,
    0xff, 0xff, 0x87, 0x0, 0x0, 0x0, 0x27, 0x9c,
    0xe0, 0xf9, 0xe6, 0xbc, 0x4f, 0x0, 0x0,

    /* U+0054 "T" */
    0x9c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8c, 0x83, 0xd8, 0xd8, 0xd8, 0xf2,
    0xff, 0xee, 0xd8, 0xd8, 0xd8, 0x76, 0x0, 0x0,
    0x0, 0x0, 0xa8, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa8, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa8, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa8, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa8, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa8,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa8, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa8, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa8, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0xe8, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x8c,
    0xff, 0xa8, 0xe8, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x8c, 0xff, 0xa8, 0xe8, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0xff, 0xa8, 0xe8, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xa8,
    0xe8, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x8c,
    0xff, 0xa8, 0xe8, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x8c, 0xff, 0xa8, 0xe8, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x8c, 0xff, 0xa8, 0xe8, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x8c, 0xff, 0xa8,
    0xe6, 0xff, 0x53, 0x0, 0x0, 0x0, 0x0, 0x91,
    0xff, 0xa5, 0xd0, 0xff, 0x79, 0x0, 0x0, 0x0,
    0x0, 0xb8, 0xff, 0x8e, 0x85, 0xff, 0xe8, 0x30,
    0x0, 0x0, 0x58, 0xfe, 0xff, 0x41, 0xf, 0xd3,
    0xff, 0xff, 0xe6, 0xef, 0xff, 0xff, 0x9b, 0x0,
    0x0, 0xd, 0x85, 0xd8, 0xf9, 0xf4, 0xc7, 0x60,
    0x0, 0x0,

    /* U+0056 "V" */
    0xab, 0xff, 0xbf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1a, 0xfe, 0xff, 0x52, 0x51, 0xff, 0xfc, 0x13,
    0x0, 0x0, 0x0, 0x0, 0x69, 0xff, 0xf0, 0x7,
    0x7, 0xef, 0xff, 0x5f, 0x0, 0x0, 0x0, 0x0,
    0xba, 0xff, 0x9d, 0x0, 0x0, 0x9c, 0xff, 0xaf,
    0x0, 0x0, 0x0, 0xf, 0xfa, 0xff, 0x43, 0x0,
    0x0, 0x41, 0xff, 0xf5, 0x9, 0x0, 0x0, 0x5b,
    0xff, 0xe5, 0x2, 0x0, 0x0, 0x2, 0xe4, 0xff,
    0x4e, 0x0, 0x0, 0xab, 0xff, 0x8e, 0x0, 0x0,
    0x0, 0x0, 0x8c, 0xff, 0x9e, 0x0, 0x8, 0xf3,
    0xff, 0x33, 0x0, 0x0, 0x0, 0x0, 0x32, 0xff,
    0xeb, 0x3, 0x4c, 0xff, 0xd9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd7, 0xff, 0x3e, 0x9d, 0xff,
    0x7e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7d,
    0xff, 0x90, 0xea, 0xff, 0x24, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x23, 0xff, 0xf7, 0xff, 0xc9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc8, 0xff, 0xff, 0x6f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xfc, 0x18,
    0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x77, 0xff, 0xb9, 0x0, 0x0, 0x0, 0x12, 0xfe,
    0xfc, 0xe, 0x0, 0x0, 0x0, 0xca, 0xff, 0x66,
    0x3d, 0xff, 0xed, 0x0, 0x0, 0x0, 0x51, 0xff,
    0xff, 0x49, 0x0, 0x0, 0x4, 0xf7, 0xff, 0x2c,
    0x8, 0xfa, 0xff, 0x22, 0x0, 0x0, 0x91, 0xff,
    0xff, 0x87, 0x0, 0x0, 0x2f, 0xff, 0xf0, 0x2,
    0x0, 0xc9, 0xff, 0x56, 0x0, 0x0, 0xd1, 0xf8,
    0xf8, 0xc6, 0x0, 0x0, 0x62, 0xff, 0xb8, 0x0,
    0x0, 0x8f, 0xff, 0x8a, 0x0, 0x13, 0xfe, 0xbd,
    0xc0, 0xfa, 0xa, 0x0, 0x94, 0xff, 0x7e, 0x0,
    0x0, 0x55, 0xff, 0xbe, 0x0, 0x52, 0xff, 0x7a,
    0x7e, 0xff, 0x42, 0x0, 0xc7, 0xff, 0x44, 0x0,
    0x0, 0x1a, 0xff, 0xf0, 0x1, 0x92, 0xff, 0x37,
    0x3c, 0xff, 0x81, 0x3, 0xf6, 0xfc, 0xd, 0x0,
    0x0, 0x0, 0xe0, 0xff, 0x26, 0xd3, 0xf1, 0x3,
    0x5, 0xf4, 0xbf, 0x2d, 0xff, 0xcf, 0x0, 0x0,
    0x0, 0x0, 0xa6, 0xff, 0x6e, 0xfe, 0xb1, 0x0,
    0x0, 0xb8, 0xf7, 0x66, 0xff, 0x95, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0xff, 0xdd, 0xff, 0x6e, 0x0,
    0x0, 0x77, 0xff, 0xce, 0xff, 0x5b, 0x0, 0x0,
    0x0, 0x0, 0x32, 0xff, 0xff, 0xff, 0x2b, 0x0,
    0x0, 0x35, 0xff, 0xff, 0xff, 0x21, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0xff, 0xe7, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xff, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbd, 0xff, 0xa5, 0x0, 0x0,
    0x0, 0x0, 0xb1, 0xff, 0xad, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0x40, 0xff, 0xff, 0x4b, 0x0, 0x0, 0x0, 0x6,
    0xdd, 0xff, 0xa6, 0x0, 0x0, 0xa6, 0xff, 0xd7,
    0x4, 0x0, 0x0, 0x75, 0xff, 0xf3, 0x1a, 0x0,
    0x0, 0x19, 0xf2, 0xff, 0x6c, 0x0, 0x14, 0xf0,
    0xff, 0x74, 0x0, 0x0, 0x0, 0x0, 0x72, 0xff,
    0xec, 0x10, 0x96, 0xff, 0xd6, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xd4, 0xff, 0xaf, 0xfb, 0xff,
    0x43, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd2, 0xff, 0xff, 0x3e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xb9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xe0, 0xff, 0xad, 0xfb, 0xff,
    0x54, 0x0, 0x0, 0x0, 0x0, 0x0, 0x85, 0xff,
    0xe9, 0xe, 0x91, 0xff, 0xe2, 0xb, 0x0, 0x0,
    0x0, 0x26, 0xf9, 0xff, 0x63, 0x0, 0x11, 0xec,
    0xff, 0x89, 0x0, 0x0, 0x0, 0xba, 0xff, 0xcc,
    0x2, 0x0, 0x0, 0x6a, 0xff, 0xfa, 0x29, 0x0,
    0x56, 0xff, 0xff, 0x3b, 0x0, 0x0, 0x0, 0x3,
    0xd2, 0xff, 0xbd, 0x0,

    /* U+0059 "Y" */
    0xae, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd0, 0xff, 0x9c, 0x2a, 0xfd, 0xff, 0x3c, 0x0,
    0x0, 0x0, 0x49, 0xff, 0xf9, 0x1e, 0x0, 0xa2,
    0xff, 0xb4, 0x0, 0x0, 0x0, 0xc2, 0xff, 0x92,
    0x0, 0x0, 0x21, 0xfa, 0xfe, 0x2d, 0x0, 0x3b,
    0xff, 0xf5, 0x17, 0x0, 0x0, 0x0, 0x96, 0xff,
    0xa5, 0x0, 0xb4, 0xff, 0x87, 0x0, 0x0, 0x0,
    0x0, 0x19, 0xf6, 0xfc, 0x4f, 0xff, 0xf0, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0xff, 0xf8,
    0xff, 0x7c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x13, 0xf1, 0xff, 0xeb, 0xc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa7, 0xff, 0x9b, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa4,
    0xff, 0x98, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa4, 0xff, 0x98, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa4, 0xff, 0x98,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa4, 0xff, 0x98, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x40, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4, 0x36, 0xd8, 0xd8, 0xd8, 0xd8,
    0xd8, 0xd8, 0xf5, 0xff, 0xe7, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xf6, 0xff, 0x4d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xca,
    0xff, 0x9e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7e, 0xff, 0xe1, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x32, 0xfa, 0xfe, 0x43, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xd4, 0xff,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0xff, 0xda, 0xa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xfd, 0xfc, 0x39, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xdd, 0xff, 0x87,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9a,
    0xff, 0xd2, 0x6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x35, 0xff, 0xff, 0xe7, 0xd4, 0xd4, 0xd4,
    0xd4, 0xd4, 0xd4, 0x31, 0x4c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3c,

    /* U+005B "[" */
    0xd8, 0xff, 0xff, 0xc0, 0xd8, 0xff, 0xcd, 0x8a,
    0xd8, 0xff, 0x4c, 0x0, 0xd8, 0xff, 0x4c, 0x0,
    0xd8, 0xff, 0x4c, 0x0, 0xd8, 0xff, 0x4c, 0x0,
    0xd8, 0xff, 0x4c, 0x0, 0xd8, 0xff, 0x4c, 0x0,
    0xd8, 0xff, 0x4c, 0x0, 0xd8, 0xff, 0x4c, 0x0,
    0xd8, 0xff, 0x4c, 0x0, 0xd8, 0xff, 0x4c, 0x0,
    0xd8, 0xff, 0x4c, 0x0, 0xd8, 0xff, 0x4c, 0x0,
    0xd8, 0xff, 0x4c, 0x0, 0xd8, 0xff, 0x4c, 0x0,
    0xd8, 0xff, 0xcd, 0x8a, 0xd8, 0xff, 0xff, 0xc0,

    /* U+005C "\\" */
    0xa5, 0xff, 0x76, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x47, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xe5, 0xff, 0x33, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8a, 0xff, 0x92, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2b, 0xff, 0xeb, 0x5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcd, 0xff, 0x4e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xad, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x15, 0xfb, 0xf9, 0x12, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb2, 0xff, 0x69, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x54, 0xff, 0xc8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xee, 0xff, 0x26, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x97, 0xff, 0x84, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xff, 0xe1, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xda, 0xff, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0x80, 0x44,

    /* U+005D "]" */
    0xe4, 0xff, 0xff, 0xb4, 0xa3, 0xd6, 0xff, 0xb4,
    0x0, 0x6c, 0xff, 0xb4, 0x0, 0x6c, 0xff, 0xb4,
    0x0, 0x6c, 0xff, 0xb4, 0x0, 0x6c, 0xff, 0xb4,
    0x0, 0x6c, 0xff, 0xb4, 0x0, 0x6c, 0xff, 0xb4,
    0x0, 0x6c, 0xff, 0xb4, 0x0, 0x6c, 0xff, 0xb4,
    0x0, 0x6c, 0xff, 0xb4, 0x0, 0x6c, 0xff, 0xb4,
    0x0, 0x6c, 0xff, 0xb4, 0x0, 0x6c, 0xff, 0xb4,
    0x0, 0x6c, 0xff, 0xb4, 0x0, 0x6c, 0xff, 0xb4,
    0xa3, 0xd6, 0xff, 0xb4, 0xe4, 0xff, 0xff, 0xb4,

    /* U+005E "^" */
    0x0, 0x0, 0x3, 0x7d, 0x58, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x50, 0xff, 0xf2, 0xc, 0x0, 0x0,
    0x0, 0x0, 0xb7, 0xfc, 0xff, 0x66, 0x0, 0x0,
    0x0, 0x20, 0xfd, 0x98, 0xe3, 0xcd, 0x0, 0x0,
    0x0, 0x86, 0xff, 0x36, 0x85, 0xff, 0x35, 0x0,
    0x5, 0xe7, 0xd6, 0x0, 0x25, 0xff, 0x9c, 0x0,
    0x54, 0xff, 0x77, 0x0, 0x0, 0xc4, 0xf4, 0xf,

    /* U+005F "_" */
    0xb6, 0xbc, 0xbc, 0xbc, 0xbc, 0xbc, 0xbc, 0xbc,
    0x11, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x18,

    /* U+0060 "`" */
    0x34, 0xf7, 0xfb, 0x29, 0x0, 0x0, 0x62, 0xff,
    0xb7, 0x0, 0x0, 0x0, 0x99, 0xff, 0x4a,

    /* U+0061 "a" */
    0x0, 0x0, 0x5b, 0xc9, 0xf5, 0xe9, 0xaf, 0x28,
    0x0, 0x0, 0x71, 0xff, 0xf9, 0xbc, 0xd6, 0xff,
    0xe9, 0x16, 0x0, 0xe2, 0xf8, 0x4b, 0x0, 0x1,
    0xc2, 0xff, 0x6d, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x17, 0x98, 0xff, 0x94, 0x0, 0xf, 0x87, 0xe5,
    0xfe, 0xff, 0xff, 0xff, 0x98, 0x0, 0xbf, 0xff,
    0xc4, 0x60, 0x49, 0xac, 0xff, 0x98, 0x20, 0xff,
    0xfa, 0xb, 0x0, 0x0, 0x8c, 0xff, 0x98, 0x1f,
    0xff, 0xfd, 0x24, 0x0, 0x18, 0xd4, 0xff, 0x98,
    0x0, 0xc5, 0xff, 0xf5, 0xd0, 0xf7, 0xff, 0xff,
    0xa3, 0x0, 0x16, 0xa2, 0xea, 0xea, 0xa6, 0x76,
    0xff, 0xcf,

    /* U+0062 "b" */
    0xe8, 0xff, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0xff, 0x38, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x69, 0xc3,
    0xf8, 0xe5, 0x81, 0x2, 0x0, 0xe8, 0xff, 0xfd,
    0xeb, 0xe3, 0xff, 0xff, 0x88, 0x0, 0xe8, 0xff,
    0x9b, 0x3, 0x0, 0x69, 0xff, 0xf9, 0x12, 0xe8,
    0xff, 0x38, 0x0, 0x0, 0x1, 0xe3, 0xff, 0x4d,
    0xe8, 0xff, 0x38, 0x0, 0x0, 0x0, 0xbe, 0xff,
    0x68, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x0, 0xbd,
    0xff, 0x62, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x1,
    0xe2, 0xff, 0x43, 0xe8, 0xff, 0x9d, 0x3, 0x0,
    0x65, 0xff, 0xf1, 0xf, 0xe8, 0xff, 0xf8, 0xe9,
    0xdd, 0xff, 0xff, 0x82, 0x0, 0xe8, 0xff, 0x42,
    0xc3, 0xf8, 0xe6, 0x82, 0x3, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x3d, 0xbd, 0xf3, 0xf5, 0xbc, 0x37,
    0x0, 0x0, 0x46, 0xfb, 0xff, 0xdf, 0xde, 0xff,
    0xf6, 0x28, 0x1, 0xda, 0xff, 0x85, 0x0, 0x1,
    0x91, 0xff, 0xa7, 0x2b, 0xff, 0xf9, 0xa, 0x0,
    0x0, 0x1d, 0xa8, 0x8c, 0x4a, 0xff, 0xdd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xff, 0xdd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xff,
    0xf7, 0x9, 0x0, 0x0, 0xf, 0x64, 0x57, 0x1,
    0xda, 0xff, 0x7f, 0x0, 0x0, 0x81, 0xff, 0xb4,
    0x0, 0x46, 0xfb, 0xff, 0xdd, 0xdc, 0xff, 0xf5,
    0x31, 0x0, 0x0, 0x3d, 0xbd, 0xf4, 0xf2, 0xb5,
    0x2f, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0x8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x8,
    0x0, 0x0, 0x60, 0xd9, 0xf9, 0xd1, 0x61, 0xff,
    0xff, 0x8, 0x0, 0x5c, 0xff, 0xff, 0xec, 0xe4,
    0xfe, 0xff, 0xff, 0x8, 0x2, 0xe3, 0xff, 0x98,
    0x1, 0x0, 0x7e, 0xff, 0xff, 0x8, 0x2d, 0xff,
    0xfc, 0x14, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x8,
    0x49, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0x8, 0x48, 0xff, 0xdc, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0x8, 0x28, 0xff, 0xf7, 0x7,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0x8, 0x1, 0xdf,
    0xff, 0x6f, 0x0, 0x0, 0x59, 0xff, 0xff, 0x8,
    0x0, 0x58, 0xff, 0xfe, 0xb8, 0xb3, 0xfb, 0xff,
    0xff, 0x8, 0x0, 0x0, 0x5e, 0xda, 0xfa, 0xd8,
    0x5d, 0xf5, 0xff, 0x8,

    /* U+0065 "e" */
    0x0, 0x0, 0x26, 0xab, 0xec, 0xf5, 0xc0, 0x3c,
    0x0, 0x0, 0x0, 0x28, 0xee, 0xff, 0xe0, 0xda,
    0xff, 0xf9, 0x36, 0x0, 0x0, 0xb9, 0xff, 0x90,
    0x1, 0x0, 0x7f, 0xff, 0xb8, 0x0, 0x14, 0xfd,
    0xfc, 0x10, 0x0, 0x0, 0x16, 0xff, 0xf7, 0x3,
    0x37, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x13, 0x3e, 0xff, 0xf9, 0xa0, 0xa0, 0xa0,
    0xa0, 0xa0, 0xa0, 0xf, 0x1f, 0xff, 0xff, 0x1b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcd,
    0xff, 0xb2, 0xb, 0x0, 0xc, 0xa3, 0x50, 0x0,
    0x0, 0x34, 0xf3, 0xff, 0xef, 0xcc, 0xf2, 0xff,
    0x9f, 0x0, 0x0, 0x0, 0x2a, 0xab, 0xed, 0xf8,
    0xd3, 0x6e, 0x2, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x14, 0x9e, 0xe5, 0xf3, 0x50, 0x0,
    0x0, 0xb7, 0xff, 0xf9, 0xd7, 0x4b, 0x0, 0xd,
    0xff, 0xff, 0x34, 0x0, 0x0, 0x0, 0x27, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xec, 0x0, 0x61, 0xaf, 0xff, 0xfe, 0xa0,
    0x93, 0x0, 0x0, 0x28, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x28, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x28, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x28, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xff, 0xfc, 0x0,
    0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x58, 0xd5, 0xf9, 0xd9, 0x59, 0xdc,
    0xff, 0x1c, 0x0, 0x54, 0xff, 0xff, 0xef, 0xe0,
    0xfe, 0xfd, 0xff, 0x1c, 0x0, 0xdc, 0xff, 0xa0,
    0x3, 0x0, 0x60, 0xff, 0xff, 0x1c, 0x24, 0xff,
    0xfd, 0x16, 0x0, 0x0, 0x8, 0xff, 0xff, 0x1c,
    0x41, 0xff, 0xe7, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x1c, 0x3f, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0x1c, 0x20, 0xff, 0xfc, 0x12,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x1c, 0x0, 0xd6,
    0xff, 0x9a, 0x1, 0x0, 0x61, 0xff, 0xff, 0x1c,
    0x0, 0x4d, 0xfe, 0xff, 0xea, 0xdd, 0xff, 0xff,
    0xff, 0x1c, 0x0, 0x0, 0x53, 0xd5, 0xf9, 0xd8,
    0x60, 0xff, 0xff, 0x1b, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x27, 0xff, 0xfe, 0xb, 0x0, 0x31,
    0xc1, 0x15, 0x0, 0x4, 0xa4, 0xff, 0xcb, 0x0,
    0x0, 0x88, 0xff, 0xf5, 0xc5, 0xe5, 0xff, 0xfa,
    0x40, 0x0, 0x0, 0x3, 0x68, 0xcc, 0xf7, 0xf1,
    0xb7, 0x38, 0x0, 0x0,

    /* U+0068 "h" */
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xff, 0x56, 0xb4, 0xf5, 0xe3, 0x8a, 0x6,
    0xf0, 0xff, 0xf8, 0xea, 0xe0, 0xff, 0xff, 0x74,
    0xf0, 0xff, 0x96, 0x3, 0x0, 0x86, 0xff, 0xca,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x3b, 0xff, 0xea,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,

    /* U+0069 "i" */
    0x9d, 0xf1, 0x43, 0xd1, 0xff, 0x6d, 0x1c, 0x4a,
    0x4, 0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4,
    0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c,
    0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff,
    0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c,

    /* U+006A "j" */
    0x0, 0x0, 0xb4, 0xec, 0x2f, 0x0, 0x0, 0xe9,
    0xff, 0x54, 0x0, 0x0, 0x24, 0x45, 0x1, 0x0,
    0x0, 0xd0, 0xff, 0x54, 0x0, 0x0, 0xd0, 0xff,
    0x54, 0x0, 0x0, 0xd0, 0xff, 0x54, 0x0, 0x0,
    0xd0, 0xff, 0x54, 0x0, 0x0, 0xd0, 0xff, 0x54,
    0x0, 0x0, 0xd0, 0xff, 0x54, 0x0, 0x0, 0xd0,
    0xff, 0x54, 0x0, 0x0, 0xd0, 0xff, 0x54, 0x0,
    0x0, 0xd0, 0xff, 0x54, 0x0, 0x0, 0xd0, 0xff,
    0x54, 0x0, 0x0, 0xd0, 0xff, 0x53, 0x0, 0x8,
    0xea, 0xff, 0x3d, 0x8f, 0xe7, 0xff, 0xea, 0xc,
    0x95, 0xf6, 0xce, 0x3e, 0x0,

    /* U+006B "k" */
    0xe8, 0xff, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe8, 0xff, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe8, 0xff, 0x3c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0xff, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x3c, 0x0,
    0xe, 0xd5, 0xff, 0xaa, 0x1, 0xe8, 0xff, 0x3c,
    0x3, 0xb8, 0xff, 0xc9, 0x9, 0x0, 0xe8, 0xff,
    0x3c, 0x94, 0xff, 0xe1, 0x18, 0x0, 0x0, 0xe8,
    0xff, 0x9f, 0xff, 0xf3, 0x2e, 0x0, 0x0, 0x0,
    0xe8, 0xff, 0xff, 0xff, 0xcb, 0x2, 0x0, 0x0,
    0x0, 0xe8, 0xff, 0xff, 0xf2, 0xff, 0x73, 0x0,
    0x0, 0x0, 0xe8, 0xff, 0x7d, 0x4f, 0xff, 0xf7,
    0x27, 0x0, 0x0, 0xe8, 0xff, 0x3c, 0x0, 0xa6,
    0xff, 0xc7, 0x3, 0x0, 0xe8, 0xff, 0x3c, 0x0,
    0x13, 0xe9, 0xff, 0x76, 0x0, 0xe8, 0xff, 0x3c,
    0x0, 0x0, 0x54, 0xff, 0xf8, 0x29,

    /* U+006C "l" */
    0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff,
    0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4,
    0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c,
    0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff,
    0x5c, 0xc4, 0xff, 0x5c, 0xc4, 0xff, 0x5c, 0xc4,
    0xff, 0x5c,

    /* U+006D "m" */
    0xe8, 0xff, 0x4b, 0xb8, 0xf5, 0xed, 0x92, 0x5,
    0x6d, 0xdb, 0xf6, 0xd0, 0x4a, 0x0, 0xe8, 0xff,
    0xf9, 0xe6, 0xe3, 0xff, 0xff, 0xcc, 0xff, 0xdb,
    0xf0, 0xff, 0xf8, 0x1e, 0xe8, 0xff, 0x88, 0x1,
    0x0, 0x92, 0xff, 0xff, 0x56, 0x0, 0xa, 0xd5,
    0xff, 0x6c, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x42,
    0xff, 0xe7, 0x0, 0x0, 0x0, 0x99, 0xff, 0x8a,
    0xe8, 0xff, 0x38, 0x0, 0x0, 0x3c, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x94, 0xff, 0x90, 0xe8, 0xff,
    0x38, 0x0, 0x0, 0x3c, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x94, 0xff, 0x90, 0xe8, 0xff, 0x38, 0x0,
    0x0, 0x3c, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x94,
    0xff, 0x90, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x3c,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x94, 0xff, 0x90,
    0xe8, 0xff, 0x38, 0x0, 0x0, 0x3c, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x94, 0xff, 0x90, 0xe8, 0xff,
    0x38, 0x0, 0x0, 0x3c, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x94, 0xff, 0x90,

    /* U+006E "n" */
    0xf0, 0xff, 0x3b, 0xb2, 0xf4, 0xe5, 0x8f, 0x7,
    0xf0, 0xff, 0xf2, 0xeb, 0xdf, 0xff, 0xff, 0x78,
    0xf0, 0xff, 0x99, 0x3, 0x0, 0x83, 0xff, 0xcb,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x3a, 0xff, 0xeb,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,
    0xf0, 0xff, 0x34, 0x0, 0x0, 0x34, 0xff, 0xf0,

    /* U+006F "o" */
    0x0, 0x0, 0x2b, 0xac, 0xeb, 0xf6, 0xc7, 0x4e,
    0x0, 0x0, 0x0, 0x32, 0xf3, 0xff, 0xe7, 0xdb,
    0xff, 0xff, 0x68, 0x0, 0x0, 0xc9, 0xff, 0x9c,
    0x2, 0x0, 0x67, 0xff, 0xf5, 0x14, 0x21, 0xff,
    0xfc, 0x14, 0x0, 0x0, 0x0, 0xd4, 0xff, 0x5e,
    0x45, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0xa5,
    0xff, 0x82, 0x47, 0xff, 0xdf, 0x0, 0x0, 0x0,
    0x0, 0xa8, 0xff, 0x7d, 0x24, 0xff, 0xfb, 0x11,
    0x0, 0x0, 0x0, 0xd5, 0xff, 0x59, 0x0, 0xd0,
    0xff, 0x98, 0x1, 0x0, 0x61, 0xff, 0xf3, 0xf,
    0x0, 0x39, 0xf6, 0xff, 0xe6, 0xd9, 0xff, 0xfe,
    0x62, 0x0, 0x0, 0x0, 0x32, 0xb5, 0xf1, 0xf3,
    0xc3, 0x4a, 0x0, 0x0,

    /* U+0070 "p" */
    0xe8, 0xff, 0x58, 0xc6, 0xf3, 0xe4, 0x7f, 0x2,
    0x0, 0xe8, 0xff, 0xfd, 0xbb, 0xb7, 0xfb, 0xff,
    0x84, 0x0, 0xe8, 0xff, 0x6b, 0x0, 0x0, 0x59,
    0xff, 0xf7, 0xf, 0xe8, 0xff, 0x38, 0x0, 0x0,
    0x1, 0xe1, 0xff, 0x49, 0xe8, 0xff, 0x38, 0x0,
    0x0, 0x0, 0xbd, 0xff, 0x64, 0xe8, 0xff, 0x38,
    0x0, 0x0, 0x0, 0xc5, 0xff, 0x5e, 0xe8, 0xff,
    0x38, 0x0, 0x0, 0x4, 0xeb, 0xff, 0x3f, 0xe8,
    0xff, 0x8a, 0x0, 0x0, 0x73, 0xff, 0xee, 0xc,
    0xe8, 0xff, 0xff, 0xdf, 0xda, 0xff, 0xff, 0x7a,
    0x0, 0xe8, 0xff, 0x6f, 0xc6, 0xf9, 0xe5, 0x7d,
    0x1, 0x0, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0xff, 0x38, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x38,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x63, 0xd9, 0xf9, 0xd4, 0x4d, 0xee,
    0xff, 0x4, 0x0, 0x62, 0xff, 0xff, 0xe2, 0xd7,
    0xfc, 0xff, 0xff, 0x4, 0x3, 0xe5, 0xff, 0x91,
    0x0, 0x0, 0x68, 0xff, 0xff, 0x4, 0x2e, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x20, 0xff, 0xff, 0x4,
    0x49, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x20, 0xff,
    0xff, 0x4, 0x48, 0xff, 0xdf, 0x0, 0x0, 0x0,
    0x20, 0xff, 0xff, 0x4, 0x29, 0xff, 0xfa, 0xf,
    0x0, 0x0, 0x20, 0xff, 0xff, 0x4, 0x1, 0xe1,
    0xff, 0x8f, 0x0, 0x0, 0x6c, 0xff, 0xff, 0x4,
    0x0, 0x5b, 0xff, 0xff, 0xe1, 0xd8, 0xff, 0xff,
    0xff, 0x4, 0x0, 0x0, 0x60, 0xda, 0xfa, 0xd4,
    0x6d, 0xff, 0xff, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x20, 0xff, 0xff, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0xff, 0xff, 0x4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0xff,
    0xff, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0xff, 0xff, 0x4,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x1, 0xe8, 0xff,
    0x6b, 0xd9, 0xfe, 0x14, 0xe8, 0xff, 0xfe, 0xfe,
    0xf7, 0x14, 0xe8, 0xff, 0xad, 0x10, 0x0, 0x0,
    0xe8, 0xff, 0x39, 0x0, 0x0, 0x0, 0xe8, 0xff,
    0x38, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x38, 0x0,
    0x0, 0x0, 0xe8, 0xff, 0x38, 0x0, 0x0, 0x0,
    0xe8, 0xff, 0x38, 0x0, 0x0, 0x0, 0xe8, 0xff,
    0x38, 0x0, 0x0, 0x0, 0xe8, 0xff, 0x38, 0x0,
    0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x3, 0x6c, 0xd3, 0xf5, 0xe1, 0x9d, 0x1c,
    0x0, 0x0, 0x92, 0xff, 0xee, 0xb1, 0xdc, 0xff,
    0xe3, 0x10, 0x1, 0xf7, 0xff, 0x30, 0x0, 0x6,
    0xda, 0xff, 0x5f, 0x0, 0xec, 0xff, 0x71, 0x6,
    0x0, 0xd, 0x14, 0x8, 0x0, 0x5c, 0xfa, 0xff,
    0xf4, 0xb6, 0x65, 0x8, 0x0, 0x0, 0x0, 0x24,
    0x85, 0xcb, 0xfd, 0xff, 0xd3, 0x10, 0x13, 0x3c,
    0x2d, 0x0, 0x0, 0x1c, 0xd6, 0xff, 0x6e, 0x35,
    0xff, 0xec, 0x13, 0x0, 0x0, 0xb2, 0xff, 0x79,
    0x0, 0xb5, 0xff, 0xeb, 0xb1, 0xca, 0xff, 0xf3,
    0x23, 0x0, 0x8, 0x80, 0xda, 0xf8, 0xe5, 0xaa,
    0x2b, 0x0,

    /* U+0074 "t" */
    0x0, 0x2e, 0x80, 0x62, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xc4, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xc4,
    0x0, 0x0, 0xec, 0xff, 0xff, 0xff, 0xff, 0x74,
    0x93, 0xc2, 0xff, 0xe9, 0xa0, 0x48, 0x0, 0x5c,
    0xff, 0xc4, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xc4,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xc4, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xc4, 0x0, 0x0, 0x0, 0x51, 0xff, 0xd7,
    0x0, 0x0, 0x0, 0x23, 0xfd, 0xff, 0xe2, 0x71,
    0x0, 0x0, 0x6f, 0xe7, 0xf0, 0x6a,

    /* U+0075 "u" */
    0xf4, 0xff, 0x30, 0x0, 0x0, 0x38, 0xff, 0xec,
    0xf4, 0xff, 0x30, 0x0, 0x0, 0x38, 0xff, 0xec,
    0xf4, 0xff, 0x30, 0x0, 0x0, 0x38, 0xff, 0xec,
    0xf4, 0xff, 0x30, 0x0, 0x0, 0x38, 0xff, 0xec,
    0xf4, 0xff, 0x30, 0x0, 0x0, 0x38, 0xff, 0xec,
    0xf4, 0xff, 0x30, 0x0, 0x0, 0x38, 0xff, 0xec,
    0xed, 0xff, 0x36, 0x0, 0x0, 0x38, 0xff, 0xec,
    0xce, 0xff, 0x7b, 0x0, 0x2, 0x93, 0xff, 0xec,
    0x74, 0xff, 0xff, 0xd7, 0xea, 0xfd, 0xff, 0xec,
    0x5, 0x86, 0xe2, 0xf8, 0xc5, 0x56, 0xff, 0xec,

    /* U+0076 "v" */
    0xa6, 0xff, 0x8c, 0x0, 0x0, 0x0, 0xb1, 0xff,
    0x81, 0x50, 0xff, 0xd7, 0x0, 0x0, 0x6, 0xf3,
    0xff, 0x2d, 0x8, 0xf2, 0xff, 0x22, 0x0, 0x42,
    0xff, 0xd8, 0x0, 0x0, 0xa6, 0xff, 0x6d, 0x0,
    0x8b, 0xff, 0x84, 0x0, 0x0, 0x51, 0xff, 0xb7,
    0x0, 0xd4, 0xff, 0x2f, 0x0, 0x0, 0x8, 0xf3,
    0xf7, 0x28, 0xff, 0xdb, 0x0, 0x0, 0x0, 0x0,
    0xa6, 0xff, 0xb3, 0xff, 0x87, 0x0, 0x0, 0x0,
    0x0, 0x51, 0xff, 0xff, 0xff, 0x32, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf3, 0xff, 0xde, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa7, 0xff, 0x8a, 0x0,
    0x0, 0x0,

    /* U+0077 "w" */
    0x93, 0xff, 0x7c, 0x0, 0x0, 0x49, 0xff, 0x9d,
    0x0, 0x0, 0x29, 0xff, 0xe9, 0x1, 0x51, 0xff,
    0xb5, 0x0, 0x0, 0x92, 0xff, 0xe7, 0x1, 0x0,
    0x61, 0xff, 0xa8, 0x0, 0x11, 0xfd, 0xed, 0x1,
    0x0, 0xdb, 0xff, 0xff, 0x33, 0x0, 0x9a, 0xff,
    0x65, 0x0, 0x0, 0xcc, 0xff, 0x28, 0x24, 0xff,
    0xc8, 0xff, 0x7e, 0x0, 0xd3, 0xff, 0x23, 0x0,
    0x0, 0x8a, 0xff, 0x62, 0x6e, 0xff, 0x45, 0xed,
    0xc9, 0xd, 0xfd, 0xe0, 0x0, 0x0, 0x0, 0x47,
    0xff, 0x9b, 0xb7, 0xf2, 0x5, 0xa6, 0xfe, 0x5b,
    0xff, 0x9e, 0x0, 0x0, 0x0, 0xa, 0xf9, 0xdc,
    0xf6, 0xac, 0x0, 0x5a, 0xff, 0xd9, 0xff, 0x5c,
    0x0, 0x0, 0x0, 0x0, 0xc2, 0xff, 0xff, 0x62,
    0x0, 0x11, 0xfc, 0xff, 0xff, 0x1a, 0x0, 0x0,
    0x0, 0x0, 0x80, 0xff, 0xfe, 0x19, 0x0, 0x0,
    0xc2, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0xff, 0xcd, 0x0, 0x0, 0x0, 0x76, 0xff,
    0x95, 0x0, 0x0, 0x0,

    /* U+0078 "x" */
    0x55, 0xff, 0xef, 0x12, 0x0, 0xd, 0xe9, 0xff,
    0x63, 0x0, 0xbd, 0xff, 0x90, 0x0, 0x86, 0xff,
    0xcb, 0x2, 0x0, 0x2a, 0xfb, 0xfa, 0x42, 0xf7,
    0xfe, 0x37, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf9,
    0xff, 0x9e, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe9,
    0xff, 0xf1, 0x17, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xf4, 0xff, 0xf9, 0x25, 0x0, 0x0, 0x0, 0x0,
    0xa8, 0xff, 0xe6, 0xff, 0xb7, 0x0, 0x0, 0x0,
    0x40, 0xff, 0xf0, 0x21, 0xe8, 0xff, 0x4f, 0x0,
    0x4, 0xd3, 0xff, 0x77, 0x0, 0x66, 0xff, 0xde,
    0x8, 0x70, 0xff, 0xe2, 0x8, 0x0, 0x3, 0xd4,
    0xff, 0x7f,

    /* U+0079 "y" */
    0xb9, 0xff, 0x8e, 0x0, 0x0, 0x0, 0xca, 0xff,
    0x75, 0x63, 0xff, 0xdb, 0x0, 0x0, 0x16, 0xfd,
    0xff, 0x21, 0x12, 0xfa, 0xff, 0x29, 0x0, 0x5f,
    0xff, 0xcd, 0x0, 0x0, 0xb7, 0xff, 0x77, 0x0,
    0xa9, 0xff, 0x79, 0x0, 0x0, 0x61, 0xff, 0xc4,
    0x4, 0xef, 0xff, 0x25, 0x0, 0x0, 0x10, 0xf9,
    0xfd, 0x53, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0xb5, 0xff, 0xdf, 0xff, 0x7d, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0x29, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf8, 0xff, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb2, 0xff, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc0, 0xff, 0x2d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x47, 0xff, 0xd4,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xe3, 0xff, 0xff,
    0x58, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xf0, 0xe6,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x30, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x25, 0xc8, 0xc8, 0xc8, 0xc8, 0xe5, 0xff,
    0xfa, 0x25, 0x0, 0x0, 0x0, 0x0, 0x12, 0xe4,
    0xff, 0x77, 0x0, 0x0, 0x0, 0x0, 0x0, 0xad,
    0xff, 0xbe, 0x2, 0x0, 0x0, 0x0, 0x0, 0x65,
    0xff, 0xee, 0x1c, 0x0, 0x0, 0x0, 0x0, 0x26,
    0xf4, 0xff, 0x54, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xcc, 0xff, 0x9c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x89, 0xff, 0xda, 0xb, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xfd, 0xff, 0xdf, 0xc4, 0xc4, 0xc4, 0xc4,
    0x55, 0x48, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70,

    /* U+007B "{" */
    0x0, 0x0, 0x0, 0x1, 0x4f, 0x4b, 0x0, 0x0,
    0x2, 0xa5, 0xff, 0x8f, 0x0, 0x0, 0x5c, 0xff,
    0xb1, 0x0, 0x0, 0x0, 0xad, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xcc, 0xff, 0x35, 0x0, 0x0, 0x0,
    0xd0, 0xff, 0x34, 0x0, 0x0, 0x0, 0xd7, 0xff,
    0x2d, 0x0, 0x0, 0x19, 0xf8, 0xf6, 0xb, 0x0,
    0x55, 0xe4, 0xff, 0x74, 0x0, 0x0, 0x80, 0xff,
    0xed, 0x33, 0x0, 0x0, 0x5, 0x5e, 0xff, 0xdd,
    0x1, 0x0, 0x0, 0x0, 0xe1, 0xff, 0x24, 0x0,
    0x0, 0x0, 0xd0, 0xff, 0x33, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x34, 0x0, 0x0, 0x0, 0xbc, 0xff,
    0x41, 0x0, 0x0, 0x0, 0x85, 0xff, 0x7f, 0x0,
    0x0, 0x0, 0x15, 0xe7, 0xf6, 0x54, 0x0, 0x0,
    0x0, 0x20, 0xb1, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1,

    /* U+007C "|" */
    0x78, 0xff, 0x78, 0xff, 0x78, 0xff, 0x78, 0xff,
    0x78, 0xff, 0x78, 0xff, 0x78, 0xff, 0x78, 0xff,
    0x78, 0xff, 0x78, 0xff, 0x78, 0xff, 0x78, 0xff,
    0x78, 0xff, 0x78, 0xff, 0x78, 0xff, 0x4e, 0xa8,

    /* U+007D "}" */
    0x4a, 0x53, 0x2, 0x0, 0x0, 0x0, 0x8c, 0xff,
    0xac, 0x4, 0x0, 0x0, 0x0, 0xae, 0xff, 0x62,
    0x0, 0x0, 0x0, 0x4d, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x32, 0xff, 0xcf, 0x0, 0x0, 0x0, 0x30,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x29, 0xff, 0xda,
    0x0, 0x0, 0x0, 0x9, 0xf4, 0xf9, 0x1c, 0x0,
    0x0, 0x0, 0x6d, 0xfe, 0xe6, 0x5a, 0x0, 0x0,
    0x2b, 0xe3, 0xff, 0x88, 0x0, 0x0, 0xd7, 0xff,
    0x62, 0x6, 0x0, 0x21, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x30,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xbd,
    0x0, 0x0, 0x0, 0x7d, 0xff, 0x88, 0x0, 0x0,
    0x52, 0xf6, 0xe8, 0x18, 0x0, 0x0, 0x85, 0xb2,
    0x22, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x5d, 0xbc, 0xbe, 0x5c, 0x0, 0x0, 0x0,
    0x6d, 0x80, 0x59, 0xff, 0xff, 0xff, 0xff, 0x9c,
    0x7, 0x19, 0xf1, 0xc6, 0xca, 0xf5, 0x2c, 0x29,
    0xcc, 0xff, 0xf0, 0xf4, 0xff, 0x60, 0x9a, 0x7f,
    0x0, 0x0, 0x9, 0x92, 0xe7, 0xe5, 0x7a, 0x1,

    /* U+00B0 "°" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x15, 0xbc, 0xf8,
    0xa7, 0x7, 0x9f, 0xc8, 0x49, 0xde, 0x74, 0xd8,
    0x62, 0x0, 0x86, 0xac, 0xb4, 0xad, 0x1c, 0xc9,
    0x89, 0x2b, 0xe6, 0xff, 0xd5, 0x15, 0x0, 0x8,
    0x2a, 0x5, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 72, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 77, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 39, .adv_w = 93, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 69, .adv_w = 176, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 212, .adv_w = 164, .box_w = 10, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 382, .adv_w = 212, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 551, .adv_w = 184, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 707, .adv_w = 49, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 722, .adv_w = 100, .box_w = 5, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 822, .adv_w = 102, .box_w = 6, .box_h = 20, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 942, .adv_w = 127, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1006, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1106, .adv_w = 63, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1130, .adv_w = 95, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1142, .adv_w = 80, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1151, .adv_w = 114, .box_w = 7, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1256, .adv_w = 164, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1386, .adv_w = 164, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1464, .adv_w = 164, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1594, .adv_w = 164, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1724, .adv_w = 164, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1854, .adv_w = 164, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1971, .adv_w = 164, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2088, .adv_w = 164, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2218, .adv_w = 164, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2348, .adv_w = 164, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2478, .adv_w = 76, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2508, .adv_w = 68, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2564, .adv_w = 146, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2636, .adv_w = 161, .box_w = 8, .box_h = 6, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2684, .adv_w = 150, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 2756, .adv_w = 140, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2873, .adv_w = 258, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3145, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3301, .adv_w = 182, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3431, .adv_w = 188, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3587, .adv_w = 188, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3717, .adv_w = 163, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3834, .adv_w = 158, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3951, .adv_w = 196, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4107, .adv_w = 205, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4250, .adv_w = 81, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4289, .adv_w = 160, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4406, .adv_w = 182, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4549, .adv_w = 156, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4666, .adv_w = 252, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4848, .adv_w = 204, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4991, .adv_w = 199, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5147, .adv_w = 184, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5277, .adv_w = 199, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5469, .adv_w = 180, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5599, .adv_w = 174, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5742, .adv_w = 175, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5885, .adv_w = 188, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6015, .adv_w = 186, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6171, .adv_w = 253, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6379, .adv_w = 182, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6535, .adv_w = 176, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6678, .adv_w = 173, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6821, .adv_w = 79, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6893, .adv_w = 120, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7013, .adv_w = 79, .box_w = 4, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7085, .adv_w = 123, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 7141, .adv_w = 130, .box_w = 9, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7159, .adv_w = 93, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 11},
    {.bitmap_index = 7174, .adv_w = 156, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7264, .adv_w = 162, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7390, .adv_w = 151, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7480, .adv_w = 163, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7620, .adv_w = 155, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7720, .adv_w = 102, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7818, .adv_w = 163, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7958, .adv_w = 160, .box_w = 8, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8070, .adv_w = 74, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8109, .adv_w = 72, .box_w = 5, .box_h = 17, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 8194, .adv_w = 150, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8320, .adv_w = 74, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8362, .adv_w = 251, .box_w = 14, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8502, .adv_w = 160, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8582, .adv_w = 164, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8682, .adv_w = 162, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 8808, .adv_w = 164, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8948, .adv_w = 101, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9014, .adv_w = 149, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9104, .adv_w = 96, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9182, .adv_w = 160, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9262, .adv_w = 142, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9352, .adv_w = 214, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9492, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9582, .adv_w = 140, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9708, .adv_w = 145, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9798, .adv_w = 97, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 9912, .adv_w = 72, .box_w = 2, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9944, .adv_w = 97, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 10058, .adv_w = 191, .box_w = 10, .box_h = 4, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 10098, .adv_w = 109, .box_w = 5, .box_h = 7, .ofs_x = 1, .ofs_y = 8}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 1, .glyph_id_start = 96,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    1, 53,
    3, 3,
    3, 8,
    3, 34,
    3, 66,
    3, 68,
    3, 69,
    3, 70,
    3, 72,
    3, 78,
    3, 79,
    3, 80,
    3, 81,
    3, 82,
    3, 84,
    3, 88,
    8, 3,
    8, 8,
    8, 34,
    8, 66,
    8, 68,
    8, 69,
    8, 70,
    8, 72,
    8, 78,
    8, 79,
    8, 80,
    8, 81,
    8, 82,
    8, 84,
    8, 88,
    9, 55,
    9, 56,
    9, 58,
    13, 3,
    13, 8,
    15, 3,
    15, 8,
    16, 16,
    34, 3,
    34, 8,
    34, 32,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 78,
    34, 79,
    34, 80,
    34, 81,
    34, 85,
    34, 86,
    34, 87,
    34, 88,
    34, 90,
    34, 91,
    35, 53,
    35, 55,
    35, 58,
    36, 10,
    36, 53,
    36, 62,
    36, 94,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 57,
    37, 58,
    37, 59,
    38, 53,
    38, 68,
    38, 69,
    38, 70,
    38, 71,
    38, 72,
    38, 80,
    38, 82,
    38, 86,
    38, 87,
    38, 88,
    38, 90,
    39, 13,
    39, 15,
    39, 34,
    39, 43,
    39, 53,
    39, 66,
    39, 68,
    39, 69,
    39, 70,
    39, 72,
    39, 80,
    39, 82,
    39, 83,
    39, 86,
    39, 87,
    39, 90,
    41, 34,
    41, 53,
    41, 57,
    41, 58,
    42, 34,
    42, 53,
    42, 57,
    42, 58,
    43, 34,
    44, 14,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 80,
    44, 82,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 86,
    45, 87,
    45, 88,
    45, 90,
    46, 34,
    46, 53,
    46, 57,
    46, 58,
    47, 34,
    47, 53,
    47, 57,
    47, 58,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 57,
    48, 58,
    48, 59,
    49, 13,
    49, 15,
    49, 34,
    49, 43,
    49, 57,
    49, 59,
    49, 66,
    49, 68,
    49, 69,
    49, 70,
    49, 72,
    49, 80,
    49, 82,
    49, 85,
    49, 87,
    49, 90,
    50, 53,
    50, 55,
    50, 56,
    50, 58,
    51, 53,
    51, 55,
    51, 58,
    53, 1,
    53, 13,
    53, 14,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 43,
    53, 48,
    53, 50,
    53, 52,
    53, 53,
    53, 55,
    53, 56,
    53, 58,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    55, 10,
    55, 13,
    55, 14,
    55, 15,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 62,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 80,
    55, 82,
    55, 83,
    55, 86,
    55, 87,
    55, 90,
    55, 94,
    56, 10,
    56, 13,
    56, 14,
    56, 15,
    56, 34,
    56, 53,
    56, 62,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 83,
    56, 86,
    56, 94,
    57, 14,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 55,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 80,
    57, 82,
    57, 86,
    57, 87,
    57, 90,
    58, 7,
    58, 10,
    58, 11,
    58, 13,
    58, 14,
    58, 15,
    58, 34,
    58, 36,
    58, 40,
    58, 43,
    58, 48,
    58, 50,
    58, 52,
    58, 53,
    58, 54,
    58, 55,
    58, 56,
    58, 57,
    58, 58,
    58, 62,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    58, 94,
    59, 34,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    59, 68,
    59, 69,
    59, 70,
    59, 72,
    59, 80,
    59, 82,
    59, 86,
    59, 87,
    59, 88,
    59, 90,
    60, 43,
    60, 54,
    66, 3,
    66, 8,
    66, 87,
    66, 90,
    67, 3,
    67, 8,
    67, 87,
    67, 89,
    67, 90,
    67, 91,
    68, 3,
    68, 8,
    70, 3,
    70, 8,
    70, 87,
    70, 90,
    71, 3,
    71, 8,
    71, 10,
    71, 62,
    71, 68,
    71, 69,
    71, 70,
    71, 72,
    71, 82,
    71, 94,
    73, 3,
    73, 8,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 82,
    78, 3,
    78, 8,
    79, 3,
    79, 8,
    80, 3,
    80, 8,
    80, 87,
    80, 89,
    80, 90,
    80, 91,
    81, 3,
    81, 8,
    81, 87,
    81, 89,
    81, 90,
    81, 91,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 72,
    83, 80,
    83, 82,
    83, 85,
    83, 87,
    83, 88,
    83, 90,
    85, 80,
    87, 3,
    87, 8,
    87, 13,
    87, 15,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 71,
    87, 72,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 80,
    89, 82,
    90, 3,
    90, 8,
    90, 13,
    90, 15,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 71,
    90, 72,
    90, 80,
    90, 82,
    91, 68,
    91, 69,
    91, 70,
    91, 72,
    91, 80,
    91, 82,
    92, 43,
    92, 54
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -8, -5, -5, -17, -7, -8, -8, -8,
    -8, -3, -3, -13, -3, -8, -13, 2,
    -5, -5, -17, -7, -8, -8, -8, -8,
    -3, -3, -13, -3, -8, -13, 2, 3,
    5, 3, -40, -40, -40, -40, -35, -17,
    -17, -11, -3, -3, -3, -3, -17, -2,
    -11, -5, -21, -7, -7, -2, -7, -2,
    -2, -7, -5, -7, 2, -4, -3, -8,
    -4, -4, -2, -2, -17, -17, -3, -12,
    -3, -3, -6, -3, 3, -3, -3, -3,
    -3, -3, -3, -3, -2, -4, -3, -4,
    -39, -39, -27, -29, 3, -5, -3, -3,
    -3, -3, -3, -3, -4, -3, -3, -3,
    3, -4, 2, -4, 3, -4, 2, -4,
    -3, -23, -4, -4, -4, -4, -4, -4,
    -4, -4, -4, -4, -3, -6, -9, -6,
    -40, -40, 3, -9, -9, -9, -9, -29,
    -3, -29, -13, -39, -2, -17, -7, -17,
    3, -4, 2, -4, 3, -4, 2, -4,
    -17, -17, -3, -12, -3, -3, -6, -3,
    -57, -57, -25, -26, -7, -5, -2, -2,
    -2, -2, -2, -2, -2, 2, 2, 2,
    -5, -4, -3, -5, -7, -3, -7, -8,
    -36, -38, -36, -17, -4, -4, -30, -4,
    -4, -2, 2, 2, 2, 2, -24, -13,
    -13, -13, -13, -13, -13, -29, -13, -13,
    -9, -11, -9, -12, -7, -11, -12, -8,
    -3, 3, -30, -22, -30, -11, -2, -2,
    -2, -2, 2, -6, -6, -6, -6, -6,
    -6, -6, -4, -4, -2, -2, 3, 2,
    -20, -8, -20, -6, 2, 2, -5, -4,
    -4, -4, -4, -4, -4, -3, -3, 2,
    -22, -4, -4, -4, -4, 2, -4, -4,
    -4, -4, -3, -4, -3, -4, -4, -4,
    3, -7, -32, -21, -32, -21, -4, -4,
    -13, -4, -4, -2, 2, -13, 3, 2,
    2, 3, 3, -9, -9, -9, -9, -3,
    -9, -6, -6, -9, -6, -9, -6, -8,
    -3, -5, -3, -3, -3, -4, 3, 2,
    -4, -4, -4, -4, -3, -3, -3, -3,
    -3, -3, -3, -4, -4, -4, -3, -3,
    -2, -2, -2, -2, -4, -4, -2, -2,
    -2, -2, -2, -2, -2, -2, -2, -2,
    2, 2, 3, 3, -3, -3, -3, -3,
    -3, 3, -11, -11, -3, -3, -3, -3,
    -3, -11, -11, -11, -11, -12, -12, -2,
    -3, -2, -2, -4, -4, -2, -2, -2,
    -2, 2, 2, -24, -24, -4, -3, -3,
    -3, 3, -3, -5, -3, 7, 3, 2,
    3, -4, 2, 2, -23, -23, -2, -2,
    -2, -2, 2, -2, -2, -2, -17, -17,
    -3, -3, -3, -3, -6, -3, 2, 2,
    -23, -23, -2, -2, -2, -2, 2, -2,
    -2, -2, -2, -2, -2, -2, -2, -2,
    -3, -3
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 434,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 8,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_lv_demo_high_res_roboto_medium_18 = {
#else
lv_font_t font_lv_demo_high_res_roboto_medium_18 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 20,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_USE_DEMO_HIGH_RES*/

