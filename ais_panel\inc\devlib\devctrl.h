/**
 * @file    devctrl.h
 * @brief   devctrl implementation file
 * <AUTHOR>
 * @version 0.1
 * @date    2024-12-21
 * 
 * @copyright Copyright (c) 2024
 */

#ifndef __CDeviceCtrl__
#define __CDeviceCtrl__

#include <cstring>
#include <thread>
#include <mutex>
#include <csignal>
#include <queue>
#include <map>
#include <condition_variable>
#include <functional>
#include "lvgl.h"
#include "serialdrv.h"

#define MAX_Q_SIZE              50

#define DEFAULT_LCD_BRIGHTNESS  (24)
#define MAX_LCD_BRIGHTNESS      (100)
#define MAX_PWM_PERIOD          (2000)

enum MessageType { SENSOR1, SENSOR2, SENSOR3, LR, EXT, PILOT, MODEM, ETH450 };

enum dw791x_playmode {
  HAPTIC_STOP=0,
  HAPTIC_TICK,
  HAPTIC_SHORT_PRESS,	
  HAPTIC_LONG_PRESS,
  HAP<PERSON>C_RELEASE,
  HAPTIC_RTP_TEST,
  HAPTIC_WAVE_PLAY,
  HAPTIC_TEST
};

enum dw791x_type {
  TYPE_SCREEN,
  TYPE_BUTTON
};

struct fwdMsg {
  MessageType type;
  std::string message;
};

class CDeviceCtrl : public CSerialDrv
{
private:
  typedef std::function<void(MessageType, const char *)> callback;
  typedef std::vector<callback> callback_list;

  callback_list m_callbackList;

public:
  CDeviceCtrl();
  ~CDeviceCtrl();

  static std::shared_ptr<CDeviceCtrl> getInst() { 
    static std::shared_ptr<CDeviceCtrl> pInst = std::make_shared<CDeviceCtrl>();
    return pInst;
  }

  std::string getConfigJson(const std::string& conf_name);
  bool OpenSerialPort(void);
  bool CloseSerialPort(void);
  void PlayBeep(int sec);
  void PlayHaptic(int mode = HAPTIC_TICK);
  void SetLcdBrightness(uint32_t level);

  /*!
   * @brief enqueue message to receive queue
   * @param type message type
   * @param message message
   */
  void enRcvQ(MessageType type, const std::string &message)
  {
    std::lock_guard<std::mutex> lock(r_mutex);
    if (rcvQ.size() > MAX_Q_SIZE)
    {
      rcvQ.pop();
    }
    rcvQ.push({type, message});
    rcv_cv.notify_one();
  }

  /*!
   * @brief wait for message in receive queue
   * @return true if the queue is not empty, false otherwise
   */
  bool waitRevQ(void)
  {
    std::unique_lock<std::mutex> lock(r_mutex);
    rcv_cv.wait(lock, [&] { return !rcvQ.empty() || !m_rcv_running; });

    if (!m_rcv_running && rcvQ.empty()) {
      return false;
    }

    return true;
  }

  /*!
   * @brief enqueue message to forward queue
   * @param str enqueue message
   */
  void enFwdWQ(const std::string &str)
  {
    std::lock_guard<std::mutex> lock(f_mutex);
    fwdQ.emplace_back(str);
    fwd_cv.notify_one();
  }

  /*!
   * @brief add callback to callback list
   * @param cb callback
   */
  void addCallback(callback cb) {
    m_callbackList.push_back(cb);
  }

  /*!
   * @brief remove all callback from callback list
   */
  void removeAllCallback() {
    m_callbackList.clear();
  }

  void OnSimulationModeCb(std::shared_ptr<void> payload);

private:
  /*!
   * @brief do callback
   * @param str callback message
   */
  void do_callback(MessageType type, const char *sentence) {
    for (auto &cb : m_callbackList) {
      cb(type, sentence);
    }
  }

  int32_t threadTpMgr(void);
  int32_t threadSimulMgr(void);

public:
  std::queue<fwdMsg> rcvQ;
  std::vector<std::string> fwdQ;

private:
  static CDeviceCtrl *instance;
  std::string m_ser_name;
  CSerialDrv  m_ser_ctrl;

  uint16_t m_baudrate;

  std::thread m_thread_tp;
  bool m_thread_tp_run;

  std::thread m_thread_simul;
  bool m_thread_simul_run;

  uint32_t m_brightness;

  std::mutex f_mutex;
  std::mutex r_mutex;

  std::condition_variable rcv_cv;
  std::condition_variable fwd_cv;

  bool m_ser_enabled;
  bool m_rcv_running;
  bool m_fwd_running;
};
#endif /* __CDeviceCtrl__ */
