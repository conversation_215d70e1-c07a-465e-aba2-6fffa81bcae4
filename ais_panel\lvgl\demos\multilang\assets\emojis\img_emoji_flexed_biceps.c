#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_FLEXED_BICEPS
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_FLEXED_BICEPS
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_FLEXED_BICEPS uint8_t
img_emoji_flexed_biceps_map[] = {
    0xfa, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xfe, 0xff, 0xff, 0xfb, 0xfb, 0xff, 0xe9, 0xfe, 0xf6, 0xff, 0xef, 0xfd, 0xfb, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xde, 0xfd, 0xff, 0xff, 0xc7, 0xf9, 0xff, 0xff, 0xc8, 0xde, 0xff, 0xff, 0xe4, 0xef, 0xff, 0xff, 0xfa, 0xfa, 0xff, 0xff, 0xf9, 0xfc, 0xff, 0xff, 0xe8, 0xf6, 0xf0, 0xff, 0xee, 0xff, 0xfb, 0xff, 0xf3, 0xff, 0xfe, 0xff, 0xf0, 0xf8, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xfe, 0xff, 0xda, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfd, 0xe7, 0xff, 0xff, 0x71, 0xd6, 0xef, 0xff, 0x3a, 0xc4, 0xe1, 0xff, 0x1e, 0xc8, 0xea, 0xff, 0x58, 0xd0, 0xed, 0xff, 0xbb, 0xeb, 0xfd, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0xfb, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xf4, 0xf6, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xf9, 0xff, 0xf1, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xf4, 0xfd, 0xff, 0xff, 0xab, 0xe9, 0xfb, 0xff, 0x54, 0xd0, 0xee, 0xff, 0x1b, 0xc6, 0xf2, 0xff, 0x44, 0xb9, 0xfc, 0xff, 0x04, 0xc4, 0xff, 0xff, 0x29, 0xbc, 0xea, 0xff, 0x83, 0xd7, 0xf0, 0xff, 0xe1, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xf5, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xb5, 0xe9, 0xfa, 0xff, 0x3d, 0xd3, 0xeb, 0xff, 0x00, 0xc4, 0xe9, 0xff, 0x03, 0xc8, 0xff, 0xff, 0x45, 0xc0, 0xff, 0xff, 0x0c, 0xc1, 0xff, 0xff, 0x2b, 0xba, 0xf4, 0xff, 0x7a, 0xd2, 0xf0, 0xff, 0xce, 0xf9, 0xfc, 0xff, 0xee, 0xff, 0xf5, 0xff, 0xf0, 0xfd, 0xef, 0xff, 0xf7, 0xff, 0xfe, 0xff, 0xf8, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xc4, 0xe5, 0xf8, 0xff, 0x76, 0xd3, 0xf4, 0xff, 0x3d, 0xc7, 0xf5, 0xff, 0x1f, 0xc0, 0xf7, 0xff, 0x16, 0xc3, 0xff, 0xff, 0x00, 0xaf, 0xef, 0xff, 0x00, 0xb5, 0xf3, 0xff, 0x20, 0xbd, 0xef, 0xff, 0x9c, 0xde, 0xfb, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xff, 0xfc, 0xf7, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xf1, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xf9, 0xff, 0xff, 0xf9, 0xff, 0xff, 0x90, 0xd9, 0xf9, 0xff, 0x37, 0xcb, 0xf9, 0xff, 0x0f, 0xc3, 0xf7, 0xff, 0x1f, 0xbd, 0xf3, 0xff, 0x66, 0xcc, 0xff, 0xff, 0x3d, 0xc2, 0xe8, 0xff, 0x52, 0xc1, 0xef, 0xff, 0x89, 0xd2, 0xf8, 0xff, 0xd9, 0xf1, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfe, 0xfb, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xf1, 0xf8, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xf7, 0xff, 0xeb, 0xf2, 0xff, 0xff, 0x4f, 0xce, 0xf5, 0xff, 0x17, 0xc8, 0xff, 0xff, 0x00, 0xc2, 0xfd, 0xff, 0x13, 0xbf, 0xe9, 0xff, 0xc4, 0xe6, 0xff, 0xff, 0xfd, 0xf6, 0xff, 0xff, 0xfb, 0xed, 0xff, 0xff, 0xfc, 0xf0, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xe2, 0xfe, 0xf8, 0xff, 0xe5, 0xff, 0xfb, 0xff, 0xf6, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xfb, 0xff, 0xa8, 0xe9, 0xf8, 0xff, 0x00, 0xbf, 0xe3, 0xff, 0x21, 0xc5, 0xff, 0xff, 0x59, 0xc5, 0xff, 0xff, 0x5f, 0xc4, 0xf1, 0xff, 0xeb, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xe4, 0xff, 0xfb, 0xff, 0xf1, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfa, 0xfd, 0xff, 0xff, 0xfa, 0xfb, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xfc, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0x3e, 0xc4, 0xf4, 0xff, 0x2d, 0xd5, 0xff, 0xff, 0x2c, 0xc5, 0xfc, 0xff, 0x0f, 0xcc, 0xff, 0xff, 0x4f, 0xc8, 0xe2, 0xff, 0xfd, 0xff, 0xf9, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xec, 0xfc, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xe5, 0xef, 0xff, 0xff, 0x2e, 0xc0, 0xf0, 0xff, 0x26, 0xd0, 0xff, 0xff, 0x31, 0xc2, 0xfa, 0xff, 0x1f, 0xc9, 0xff, 0xff, 0x5d, 0xc8, 0xe9, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xfa, 0xf7, 0xff, 0xff, 0xc5, 0xee, 0xff, 0xff, 0x79, 0xdc, 0xf2, 0xff, 0x41, 0xce, 0xe3, 0xff, 0x3b, 0xcc, 0xe1, 0xff, 0x6c, 0xd7, 0xec, 0xff, 0xb3, 0xe8, 0xfd, 0xff, 0xe6, 0xf4, 0xff, 0xff, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfe, 0xff,
    0xb8, 0xec, 0xfd, 0xff, 0x20, 0xc2, 0xf6, 0xff, 0x21, 0xd0, 0xff, 0xff, 0x38, 0xc2, 0xfd, 0xff, 0x30, 0xc6, 0xff, 0xff, 0x62, 0xc8, 0xf2, 0xff, 0xe4, 0xfa, 0xff, 0xff, 0xe6, 0xf2, 0xff, 0xff, 0x65, 0xcd, 0xfc, 0xff, 0x52, 0xce, 0xfe, 0xff, 0x35, 0xcf, 0xff, 0xff, 0x1e, 0xcf, 0xff, 0xff, 0x1a, 0xcd, 0xff, 0xff, 0x28, 0xca, 0xfa, 0xff, 0x3f, 0xc6, 0xf4, 0xff, 0x4e, 0xc3, 0xf0, 0xff, 0xba, 0xf1, 0xf4, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0x8d, 0xe2, 0xf8, 0xff, 0x1a, 0xc3, 0xfc, 0xff, 0x1c, 0xce, 0xff, 0xff, 0x32, 0xc2, 0xfc, 0xff, 0x2e, 0xbf, 0xff, 0xff, 0x46, 0xc3, 0xf0, 0xff, 0x99, 0xe7, 0xf8, 0xff, 0x93, 0xd9, 0xff, 0xff, 0x18, 0xc8, 0xfd, 0xff, 0x24, 0xc6, 0xff, 0xff, 0x34, 0xc3, 0xff, 0xff, 0x3e, 0xc2, 0xff, 0xff, 0x3b, 0xc1, 0xff, 0xff, 0x2e, 0xc3, 0xff, 0xff, 0x1c, 0xc6, 0xff, 0xff, 0x10, 0xc8, 0xfc, 0xff, 0x7e, 0xd3, 0xe9, 0xff, 0xdf, 0xe5, 0xfc, 0xff,
    0x69, 0xd0, 0xf0, 0xff, 0x15, 0xbd, 0xfd, 0xff, 0x0e, 0xc8, 0xff, 0xff, 0x22, 0xbe, 0xf9, 0xff, 0x1c, 0xb6, 0xff, 0xff, 0x1a, 0xbb, 0xed, 0xff, 0x3d, 0xd1, 0xe9, 0xff, 0x34, 0xbe, 0xf3, 0xff, 0x33, 0xc8, 0xff, 0xff, 0x2b, 0xc6, 0xfe, 0xff, 0x1d, 0xc2, 0xfb, 0xff, 0x16, 0xc0, 0xfa, 0xff, 0x15, 0xc1, 0xfb, 0xff, 0x1e, 0xc5, 0xfe, 0xff, 0x2b, 0xca, 0xff, 0xff, 0x35, 0xce, 0xff, 0xff, 0x27, 0xc3, 0xf1, 0xff, 0x8e, 0xd0, 0xff, 0xff,
    0x52, 0xc4, 0xec, 0xff, 0x17, 0xbb, 0xff, 0xff, 0x07, 0xc6, 0xff, 0xff, 0x14, 0xc1, 0xf9, 0xff, 0x15, 0xb4, 0xff, 0xff, 0x07, 0xbe, 0xf6, 0xff, 0x08, 0xcb, 0xed, 0xff, 0x06, 0xb4, 0xfa, 0xff, 0x1e, 0xbc, 0xf2, 0xff, 0x16, 0xc2, 0xf6, 0xff, 0x0b, 0xca, 0xfb, 0xff, 0x04, 0xd0, 0xff, 0xff, 0x04, 0xd0, 0xff, 0xff, 0x0b, 0xca, 0xfb, 0xff, 0x16, 0xc2, 0xf6, 0xff, 0x1e, 0xbc, 0xf2, 0xff, 0x00, 0xc5, 0xfd, 0xff, 0x53, 0xcc, 0xff, 0xff,
    0x41, 0xc0, 0xed, 0xff, 0x1a, 0xbd, 0xff, 0xff, 0x00, 0xc5, 0xff, 0xff, 0x0a, 0xbf, 0xf6, 0xff, 0x17, 0xb1, 0xff, 0xff, 0x0b, 0xbf, 0xff, 0xff, 0x01, 0xc8, 0xf6, 0xff, 0x0a, 0xb1, 0xff, 0xff, 0x01, 0xc4, 0xff, 0xff, 0x05, 0xc2, 0xff, 0xff, 0x0b, 0xbe, 0xfc, 0xff, 0x0e, 0xbb, 0xf9, 0xff, 0x0e, 0xbb, 0xf9, 0xff, 0x0a, 0xbd, 0xfb, 0xff, 0x05, 0xc0, 0xfd, 0xff, 0x00, 0xc1, 0xff, 0xff, 0x00, 0xc4, 0xfb, 0xff, 0x67, 0xcf, 0xff, 0xff,
    0x33, 0xbd, 0xec, 0xff, 0x16, 0xba, 0xff, 0xff, 0x00, 0xbe, 0xfd, 0xff, 0x00, 0xb7, 0xef, 0xff, 0x15, 0xa6, 0xfc, 0xff, 0x12, 0xb8, 0xff, 0xff, 0x05, 0xbe, 0xf6, 0xff, 0x18, 0xa8, 0xff, 0xff, 0x00, 0xae, 0xfd, 0xff, 0x06, 0xb1, 0xff, 0xff, 0x13, 0xb5, 0xff, 0xff, 0x1c, 0xb7, 0xff, 0xff, 0x1e, 0xb7, 0xff, 0xff, 0x15, 0xb5, 0xff, 0xff, 0x09, 0xb1, 0xff, 0xff, 0x01, 0xae, 0xfe, 0xff, 0x21, 0xba, 0xed, 0xff, 0xa1, 0xcf, 0xff, 0xff,
    0x83, 0xd8, 0xf8, 0xff, 0x42, 0xc4, 0xef, 0xff, 0x01, 0xb3, 0xf4, 0xff, 0x00, 0xaf, 0xff, 0xff, 0x00, 0xad, 0xff, 0xff, 0x0d, 0xa9, 0xff, 0xff, 0x0d, 0xb1, 0xff, 0xff, 0x06, 0xbd, 0xff, 0xff, 0x00, 0xb2, 0xf5, 0xff, 0x04, 0xb1, 0xf9, 0xff, 0x04, 0xae, 0xfc, 0xff, 0x00, 0xab, 0xfb, 0xff, 0x00, 0xab, 0xf7, 0xff, 0x00, 0xae, 0xf5, 0xff, 0x14, 0xb3, 0xf7, 0xff, 0x28, 0xb8, 0xf9, 0xff, 0xa4, 0xd3, 0xee, 0xff, 0xce, 0xed, 0xff, 0xff,
    0xef, 0xf9, 0xff, 0xff, 0xa9, 0xe2, 0xfb, 0xff, 0x57, 0xcd, 0xf0, 0xff, 0x27, 0xc2, 0xf0, 0xff, 0x12, 0xb7, 0xf0, 0xff, 0x00, 0xaf, 0xe6, 0xff, 0x00, 0xb5, 0xe1, 0xff, 0x00, 0xc1, 0xe1, 0xff, 0x00, 0xb5, 0xf6, 0xff, 0x00, 0xb4, 0xfa, 0xff, 0x00, 0xb1, 0xfc, 0xff, 0x00, 0xb0, 0xfc, 0xff, 0x00, 0xb1, 0xf9, 0xff, 0x03, 0xb7, 0xf8, 0xff, 0x1d, 0xbf, 0xfa, 0xff, 0x34, 0xc5, 0xfd, 0xff, 0xcc, 0xeb, 0xff, 0xff, 0xe3, 0xfa, 0xff, 0xff,
    0xff, 0xfe, 0xfb, 0xff, 0xf8, 0xff, 0xfe, 0xff, 0xe1, 0xfe, 0xff, 0xff, 0xe2, 0xfd, 0xff, 0xff, 0xe9, 0xf2, 0xff, 0xff, 0xe1, 0xe3, 0xff, 0xff, 0xcc, 0xdc, 0xff, 0xff, 0xbf, 0xde, 0xff, 0xff, 0xa7, 0xde, 0xf9, 0xff, 0xab, 0xdd, 0xfb, 0xff, 0xae, 0xdb, 0xfc, 0xff, 0xac, 0xdb, 0xfa, 0xff, 0xae, 0xe0, 0xf7, 0xff, 0xc2, 0xe9, 0xf8, 0xff, 0xe2, 0xf3, 0xfc, 0xff, 0xfb, 0xfc, 0xff, 0xff, 0xf3, 0xfc, 0xff, 0xff, 0xf6, 0xfe, 0xff, 0xff,
};

const lv_image_dsc_t img_emoji_flexed_biceps = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_flexed_biceps_map,
    .data_size = sizeof(img_emoji_flexed_biceps_map),
};

#endif
